<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <artifactId>account-service-engine</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>account-service</artifactId>
        <version>${revision}</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-notification</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-storage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-util</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-convertors</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>salt-client</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>model-management-client</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-control-center</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-retry</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>salt-model</artifactId>
            <version>${revision}</version>
            <scope>test</scope>
            <classifier>tests</classifier>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>file-storage-common</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-k8s-mysql</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-k8s-memcached</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-schema</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-model</artifactId>
            <version>${project.version}</version>
            <classifier>tests</classifier>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scala-tools.testing</groupId>
            <artifactId>specs</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-value-generator</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-kms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalamock</groupId>
            <artifactId>scalamock-scalatest-support_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-resource</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-pgp</artifactId>
        </dependency>
        <dependency>
            <groupId>oro</groupId>
            <artifactId>oro</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-xml</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-session-manager-core</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-crypto-encryptor-key</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-debug-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-crypto-decryptor-key</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.bouncycastle</groupId>
                    <artifactId>bcprov-debug-jdk15on</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-ip-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>decision-service-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>document-manager-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>docv-orchestra-client</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <configuration>
                    <threadCount>3</threadCount>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
