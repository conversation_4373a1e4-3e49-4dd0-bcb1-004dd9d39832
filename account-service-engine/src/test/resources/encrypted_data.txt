<?xml version="1.0" encoding="UTF-8"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata"
                     entityID="ENTITY_ID">
    <md:IDPSSODescriptor
            WantAuthnRequestsSigned="false"
            protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:
                            protocol">
        <md:KeyDescriptor use="signing">
            <KeyInfo xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIICTDCCAfagAwIBAgICBo8wDQYJKoZIhvcNAQEEBQAwgZIxCzAJBgNVBAYTAlVTMRMwEQYDVQQI
                        EwpDYWxpZm9ybmlhMRQwEgYDVQQHEwtTYW50YSBDbGFyYTEeMBwGA1UEChMVU3VuIE1pY3Jvc3lz
                        dGVtcyBJbmMuMRowGAYDVQQLExFJZGVudGl0eSBTZXJ2aWNlczEcMBoGA1UEAxMTQ2VydGlmaWNh
                        dGUgTWFuYWdlcjAeFw0wNjExMDcyMzU2MTdaFw0xMDA4MDMyMzU2MTdaMCMxITAfBgNVBAMTGGxv
                        YWRiYWxhbmNlci05LnNpcm9lLmNvbTCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAw574iRU6
                        HsSO4LXW/OGTXyfsbGv6XRVOoy3v+J1pZ51KKejcDjDJXNkKGn3/356AwIaqbcymWd59T0zSqYfR
                        Hn+45uyjYxRBmVJseLpVnOXLub9jsjULfGx0yjH4w+KsZSZCXatoCHbj/RJtkzuZY6V9to/hkH3S
                        InQB4a3UAgMCAwEAAaNgMF4wEQYJYIZIAYb4QgEBBAQDAgZAMA4GA1UdDwEB/wQEAwIE8DAfBgNV
                        HSMEGDAWgBQ7oCE35Uwn7FsjS01w5e3DA1CrrjAYBgNVHREEETAPgQ1tYWxsYUBzdW4uY29tMA0G
                        CSqGSIb3DQEBBAUAA0EAMlbfBg/ff0Xkv4DOR5LEqmfTZKqgdlD81cXynfzlF7XfnOqI6hPIA90I
                        x5Ql0ejivIJAYcMGUyA+/YwJg2FGoA==
                    </X509Certificate>
                </X509Data>
            </KeyInfo>
        </md:KeyDescriptor>
        <md:KeyDescriptor  use="encryption">
            <KeyInfo
                    xmlns="http://www.w3.org/2000/09/xmldsig#">
                <X509Data>
                    <X509Certificate>
                        MIICTDCCAfagAwIBAgICBo8wDQYJKoZIhvcNAQEEBQAwgZIxCzAJBgNVBAYTAlVTMRMwEQYDVQQI
                        EwpDYWxpZm9ybmlhMRQwEgYDVQQHEwtTYW50YSBDbGFyYTEeMBwGA1UEChMVU3VuIE1pY3Jvc3lz
                        dGVtcyBJbmMuMRowGAYDVQQLExFJZGVudGl0eSBTZXJ2aWNlczEcMBoGA1UEAxMTQ2VydGlmaWNh
                        dGUgTWFuYWdlcjAeFw0wNjExMDcyMzU2MTdaFw0xMDA4MDMyMzU2MTdaMCMxITAfBgNVBAMTGGxv
                        YWRiYWxhbmNlci05LnNpcm9lLmNvbTCBnzANBgkqhkiG9w0BAQEFAAOBjQAwgYkCgYEAw574iRU6
                        HsSO4LXW/OGTXyfsbGv6XRVOoy3v+J1pZ51KKejcDjDJXNkKGn3/356AwIaqbcymWd59T0zSqYfR
                        Hn+45uyjYxRBmVJseLpVnOXLub9jsjULfGx0yjH4w+KsZSZCXatoCHbj/RJtkzuZY6V9to/hkH3S
                        InQB4a3UAgMCAwEAAaNgMF4wEQYJYIZIAYb4QgEBBAQDAgZAMA4GA1UdDwEB/wQEAwIE8DAfBgNV
                        HSMEGDAWgBQ7oCE35Uwn7FsjS01w5e3DA1CrrjAYBgNVHREEETAPgQ1tYWxsYUBzdW4uY29tMA0G
                        CSqGSIb3DQEBBAUAA0EAMlbfBg/ff0Xkv4DOR5LEqmfTZKqgdlD81cXynfzlF7XfnOqI6hPIA90I
                        x5Ql0ejivIJAYcMGUyA+/YwJg2FGoA==
                    </X509Certificate>
                </X509Data>
            </KeyInfo>
            <md:EncryptionMethod
                    Algorithm=
                            "http://www.w3.org/2001/04/xmlenc#rsa-1_5"/>
        </md:KeyDescriptor>
        <md:ArtifactResolutionService
                Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                Location="__location__" index="0"
                isDefault="true"/>
        <md:ArtifactResolutionService
                Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                Location="__location__" index="1"/>
        <md:SingleLogoutService
                Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                Location="__location__"/>
        <md:SingleLogoutService Binding=
                                        "urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                                Location="__location__"/>
        <md:ManageNameIDService
                Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST"
                Location="__location__"/>
        <md:ManageNameIDService
                Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP"
                Location="__location__"/>
        <md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:
            nameid-format:persistent
        </md:NameIDFormat>
        <md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:
            nameid-format:transient
        </md:NameIDFormat>
        <md:NameIDFormat>
            urn:oasis:names:tc:SAML:1.1:
            nameid-format:emailAddress
        </md:NameIDFormat>
        <md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:
            nameid-format:encrypted</md:NameIDFormat>
        <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:
                            bindings:HTTP-POST" Location="__location__"/>
    </md:IDPSSODescriptor>
    <md:Organization>
        <md:OrganizationName xml:lang="en">
            organization_name
        </md:OrganizationName>
        <md:OrganizationDisplayName xml:lang="en">
            organization_display_name
        </md:OrganizationDisplayName>
        <md:OrganizationURL xml:lang="en"/>
    </md:Organization>
    <md:ContactPerson contactType="technical">
        <md:Company>
            company
        </md:Company>
        <md:GivenName/>
        <md:SurName/>
        <md:EmailAddress/>
        <md:TelephoneNumber/>
    </md:ContactPerson>
</md:EntityDescriptor>