client.specific.encryption {
  data.key.len = 32 //Please dont change it
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:1234567890:alias/client-specific-encryption-stage"""
    "us-west-1" = """arn:aws:kms:us-west-1:1234567890:alias/client-specific-encryption-stage"""
  }
  service.kms.ids {
    etlv3 {
      "us-east-1" = """arn:aws:kms:us-east-1:1234567890:alias/etlv3_kms_stage"""
      "us-west-2" = """arn:aws:kms:us-west-2:1234567890:alias/etlv3_kms_stage"""
    }
  }
}
