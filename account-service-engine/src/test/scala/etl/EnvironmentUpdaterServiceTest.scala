package etl

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.etl.EnvironmentUpdaterService
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.storage.slick.dao.DaoEnvironment
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 11/3/16.
  */
class EnvironmentUpdaterServiceTest extends FunSuite with ScalaFutures with Matchers with BeforeAndAfterAll {
  implicit val executionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val mysqlService: MysqlService = MysqlService("environment-updater-service")
  private val dbName = "socure"

  var service : EnvironmentUpdaterService = _

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database : Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource : DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new EnvironmentUpdaterService(daoEnvironment)
  }

  override protected def beforeAll(): Unit ={
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 0, NULL, '${PublicIdGenerator.account().value}','publicApiKey4', 'externalId4')"
    )

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 1), " +
      "(4, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 2), " +
      "(5, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 3, 1), " +
      "(6, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 3, 2), " +
      "(7, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 4, 1), " +
      "(8, 'accesstoken', 'secretkey', 'accesstokensecret', null, 4, 2)"
    )

    service = buildService(socureDb)
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("get entire environment type table") {
    val result = service.getEnvironmentTypeTable
    whenReady(result){ res =>
      res.size shouldBe 3
      res.headOption.fold(fail)(_.id shouldBe 1)
      res.headOption.fold(fail)(_.name shouldBe "Production")
      res.tail.headOption.fold(fail)(_.name shouldBe "Development")
      res.last.id shouldBe 3
      res.last.name shouldBe "Sandbox"
    }
  }

  test("get entire environment table") {
    val result = service.getEnvironmentTable
    whenReady(result){ res =>
      res.size shouldBe 8
      res.headOption.fold(fail)(_.accountId shouldBe 1)
      res.last.domain shouldBe ""
    }
  }
}
