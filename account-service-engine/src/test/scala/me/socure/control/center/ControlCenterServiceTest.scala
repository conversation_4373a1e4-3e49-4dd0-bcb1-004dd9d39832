package me.socure.control.center

import me.socure.control.center.client.ControlCenterServiceClient
import me.socure.control.center.model.{ControlCenterSettings, ToggleableSetting}
import me.socure.model.ErrorResponse
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class ControlCenterServiceTest extends FunSuite with BeforeAndAfter with Matchers with MockitoSugar with ScalaFutures{

  val controlCenterServiceClient = mock[ControlCenterServiceClient]
  val controlCenterService = new ControlCenterService(controlCenterServiceClient)

  val settingName = "test"
  val updatedValue = true

  before{
    Mockito.reset(controlCenterServiceClient)
  }

  test("should update settings properly") {
    Mockito.when(controlCenterServiceClient.fetchSettings()).thenReturn(Future.successful(Right(controlCenterSettings())))
    val updatedSetting = ControlCenterSettings(Seq(ToggleableSetting(settingName, updatedValue, "test")))
    Mockito.when(controlCenterServiceClient.updateSettings(updatedSetting)).thenReturn(Future.successful(Right(updatedSetting)))
    whenReady(controlCenterService.updateSetting(settingName, updatedValue)) {
      res => res shouldBe 'right
        res.right.get shouldBe(updatedSetting)
    }
    Mockito.verify(controlCenterServiceClient).fetchSettings()
    Mockito.verify(controlCenterServiceClient).updateSettings(updatedSetting)
  }

  test("should return error if invalid setting is given") {
    Mockito.when(controlCenterServiceClient.fetchSettings()).thenReturn(Future.successful(Right(controlCenterSettings())))
    whenReady(controlCenterService.updateSetting("invalid", updatedValue)) {
      res => res shouldBe 'left
        res.left.get.code shouldBe 101
    }
    Mockito.verify(controlCenterServiceClient).fetchSettings()
  }

  test("should return error if control center library returns error") {
    Mockito.when(controlCenterServiceClient.fetchSettings()).thenReturn(Future.successful(Left(ErrorResponse(1, "error"))))
    whenReady(controlCenterService.updateSetting(settingName, updatedValue)) {
      res => res shouldBe 'left
        res.left.get.code shouldBe 1
    }
    Mockito.verify(controlCenterServiceClient).fetchSettings()
  }

  private def controlCenterSettings() = {
    ControlCenterSettings(toggleableSettings = Seq(ToggleableSetting(settingName,false,"test")))
  }

}
