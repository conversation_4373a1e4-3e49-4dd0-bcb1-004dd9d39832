package me.socure.batchjob

import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import org.mockito.Mockito
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.mockito.{Matchers => MMathers}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class AccountManagementServiceTest extends FunSuite with BeforeAndAfter with Matchers with MockitoSugar with ScalaFutures {

  val businessUserServiceMock = mock[BusinessUserRoleService]
  val service = new AccountManagementService(businessUserServiceMock)

  before{
    Mockito.reset(businessUserServiceMock)
  }

  test("update account permissions - failure") {
    Mockito.when(businessUserServiceMock.switchPermissions(7, Set(137, 136), true)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))))
    Mockito.when(businessUserServiceMock.switchPermissions(1, Set(1, 2, 3), true)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownRole))))
    Mockito.when(businessUserServiceMock.switchPermissions(10, Set(137, 136), false)).thenReturn(Future.successful(Right(3)))
    val apuRequest = List(
      AccountPermissionUpdateRequest(7, Set(137, 136), true),
      AccountPermissionUpdateRequest(1, Set(1, 2, 3), true),
      AccountPermissionUpdateRequest(10, Set(136, 137), false))
    val apuResponse = List(
      AccountPermissionUpdateResponse(7, Set(137, 136), true, "Failure", "Account Not Found"),
      AccountPermissionUpdateResponse(1, Set(1, 2, 3), true, "Failure", "Unknown role"),
      AccountPermissionUpdateResponse(10, Set(136, 137), false, "Success", ""))
    whenReady(service.updateAccountPermissions(apuRequest)) { res =>
      println(res)
      res.nonEmpty shouldBe true
      res.size shouldBe apuRequest.size
      res.filter(_.accountId==10).head shouldBe apuResponse.filter(_.accountId==10).head
      res.filter(_.accountId==1).head shouldBe apuResponse.filter(_.accountId==1).head
    }
    Mockito.verify(businessUserServiceMock, Mockito.times(3)).switchPermissions(MMathers.any[Long], MMathers.any[Set[Int]], MMathers.any[Boolean])
  }
}

