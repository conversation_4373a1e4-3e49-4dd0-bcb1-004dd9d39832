package me.socure.account.service


import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, InvalidInputFormat}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.account.superadmin.ManageAccountsService
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.encryption.AccountId
import me.socure.model.superadmin.AccountCreationForm
import me.socure.storage.slick.dao.{DaoPublicWebhook, DaoSubscriptionStatus}
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import org.mockito.Mockito.never
import org.mockito.{Argument<PERSON>ap<PERSON>, Mocki<PERSON>, Matchers => MMatchers}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.slf4j.{Logger, LoggerFactory}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by angayarkanni on 10/25/17.
  */
class ManageAccountsServiceTest extends FunSuite with ScalaFutures with Matchers with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  implicit val defaultPatience = PatienceConfig(timeout = Span(90, Seconds), interval = Span(500, Millis))
  private implicit val exe = ExecutionContext.Implicits.global
  var service: ManageAccountsService = _
  var sponsorBankService: SponsorBankService = _
  var publicWebhookService: PublicWebhookService = _

  val encryptionKeysService = Mockito.mock(classOf[EncryptionKeysService])

  override val mysqlService: MysqlService = MysqlService("manage-account-service")
  private val dbName = "socure"

  private def buildPublicWebhookService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoPublicWebhook = new DaoPublicWebhook(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionStatus = new DaoSubscriptionStatus(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new PublicWebhookService(
      daoPublicWebhook = daoPublicWebhook,
      daoSubscriptionStatus = daoSubscriptionStatus,
      clock = clock)
  }
  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    insertAdditionalData(sqlExecutor)

    service = buildManageAccountsService(socureDb, encryptionKeysService)
    sponsorBankService = buildSponsorBankService(socureDb)
    publicWebhookService = buildPublicWebhookService(socureDb)
  }


  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  def insertAdditionalData(sqlExecutor:SQLExecutor) = {
    sqlExecutor.execute("INSERT INTO tbl_business_user (activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), "+
      "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 10, true), "+
      "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 8, true); ")

  }

  test("update consentId should not update for invalid accountid"){
    val result = service.updateConsentReason(45, 2)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  test("update consentId should update for valid accountid"){
    val result = service.updateConsentReason(1, 2)
    whenReady(result){ res =>
      res shouldBe 'right
      res.right.value shouldBe 1
    }
  }

  test("get consentId for valid account"){
    val result = service.getConsentId(1)
    whenReady(result){ res =>
      res shouldBe 'right
      res.right.value shouldBe 2
    }
  }

  test("get consentId for invalid account"){
    val result = service.getConsentId(45)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  test("get parent account should return parent accounts ") {
    val parentAccounts = UserFixture.parentAccounts
    val msg = service.getParentAccounts()
    whenReady(msg) { response =>
      response shouldBe 'right
      response.right.value.nonEmpty shouldBe true
    }
  }

  test("registration should fail with duplicate account name ") {
    val duplicateAccount = UserFixture.duplicateAccountCreationForm
    val msg = service.createAccountIfNotExists(duplicateAccount)
    whenReady(msg) { response =>
      response.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)
    }
  }

  test("registration should pass through") {
    Mockito.reset(modelManagementClient)
    Mockito.reset(encryptionKeysService)
    val accountCreationForm = UserFixture.accountCreationForm
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])).thenReturn(Future.successful(Right(true)))
    val msg = service.createAccountIfNotExists(accountCreationForm)
    whenReady(msg) { response =>
      response shouldBe 'right
      response.right.value shouldBe true
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])
      //Should not allow reregistration of the same account name and should return a generic error in that case.
      whenReady(service.createAccountIfNotExists(accountCreationForm)) { res =>
        res shouldBe 'left
        res.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)
      }
    }
  }

  test("registration should pass through for account creation with parent id") {
    Mockito.reset(modelManagementClient)
    Mockito.reset(encryptionKeysService)
    val accountCreationForm = UserFixture.accountCreationFormWithParent
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])).thenReturn(Future.successful(Right(true)))
    val msg = service.createAccountIfNotExists(accountCreationForm)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.right.value shouldBe true
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])
    }
  }

  test("should create account even if model mapping fails") {
    Mockito.reset(modelManagementClient)
    Mockito.reset(encryptionKeysService)
    val accountCreationForm = AccountCreationForm("Test Account", "101-205", None, isInternal = false)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])).thenReturn(Future.successful(Right(false)))

    val msg = service.createAccountIfNotExists(accountCreationForm)

    whenReady(msg) { response =>
      response shouldBe 'left
      response.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.ModelMappingFailed)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])
    }

    whenReady(service.createAccountIfNotExists(accountCreationForm)) { res =>
      res shouldBe 'left
      res.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)
    }
  }

  test("should create account even if model mapping returns error") {
    Mockito.reset(modelManagementClient)
    Mockito.reset(encryptionKeysService)
    val accountCreationForm = AccountCreationForm("Test Account1", "101-205", None, isInternal = false)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.ModelMappingFailed))))

    val msg = service.createAccountIfNotExists(accountCreationForm)

    whenReady(msg) { response =>
      response shouldBe 'left
      response.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.ModelMappingFailed)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])
    }

    whenReady(service.createAccountIfNotExists(accountCreationForm)) { res =>
      res shouldBe 'left
      res.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)
    }
  }

  test("should not call model mapping operation and revert created account when encryption key generation fails") {
    Mockito.reset(modelManagementClient)
    Mockito.reset(encryptionKeysService)
    val accountCreationForm = AccountCreationForm("Test Account2", "101-205", None, isInternal = false)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.failed(new Throwable()))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])).thenReturn(Future.successful(Right(false)))

    val msg = service.createAccountIfNotExists(accountCreationForm)

    whenReady(msg) { response =>
      response shouldBe 'left
      response.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.EncryptionKeyGenerationWhenAccountCreationFailed)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(modelManagementClient, never()).mapDefaultModels(MMatchers.any[String], MMatchers.any[String], MMatchers.any[String])
    }

    whenReady(service.createAccountIfNotExists(accountCreationForm)) { res =>
      res shouldBe 'left
      res.left.value should not be ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)
    }
  }

  test("activateAccounts with valid account ids should pass"){
    val result = service.activateAccounts(List(5L))
    whenReady(result) {res =>
     res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("activateAccounts with invalid account ids should return error message"){
    val result = service.activateAccounts(List(55L))
    whenReady(result) {res =>
      res shouldBe 'left
    }
  }

  test("update accountname by publicid should update account name"){
    whenReady(service.getAccountInfo(1L)){ res =>
      res shouldBe 'right
      val accountInfo = res.right.value
      val result = service.updateAccountNamebyPublicId(accountInfo.publicId,"modified")
      whenReady(result){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(1L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.name shouldBe "modified"
        }
      }

      val result0 = service.updateAccountNamebyPublicId(accountInfo.publicId,"AccountName1")
      whenReady(result0){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(1L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.name shouldBe "AccountName1"
        }
      }
    }
  }

  test("update accountname by publicid should not update for invalid public accountid"){
    val result = service.updateAccountNamebyPublicId("public_api_key_1309","Something")
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  test("update accountname by publicid should not update for invalid account name"){
    val result = service.updateAccountNamebyPublicId("public_api_key_13","")
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(InvalidInputFormat)), _ => fail)
    }
  }

  test("update isActive(deactivate account) should remove SponsorBankProgram Links"){
    whenReady(service.getAccountInfo(24L)){ res =>
      res shouldBe 'right
      val accountInfo = res.right.value
      whenReady(sponsorBankService.getLinkedPrograms(24L)) { sbpRes =>
        sbpRes.fold(_ => fail, r => r.nonEmpty shouldBe true)
      }
      val result = service.updateIsActive(accountInfo.publicId,false)
      whenReady(result){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(24L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.isActive shouldBe false
        }
        whenReady(sponsorBankService.getLinkedPrograms(24L)) { sbpRes =>
          sbpRes.fold(_ => fail, r => r.isEmpty shouldBe true)
        }
      }

      val result0 = service.updateIsActive(accountInfo.publicId,true)
      whenReady(result0){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(24L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.isActive shouldBe true
        }
      }
    }
  }

  test("update isActive by publicid should update"){
    whenReady(service.getAccountInfo(2L)){ res =>
      res shouldBe 'right
      val accountInfo = res.right.value
      val result = service.updateIsActive(accountInfo.publicId,false)
      whenReady(result){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(2L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.isActive shouldBe false
        }
      }

      val result0 = service.updateIsActive(accountInfo.publicId,true)
      whenReady(result0){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(2L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.isActive shouldBe true
        }
      }
    }
  }

  test("update isActive by publicid should not update for invalid public accountid"){
    val result = service.updateIsActive("public_api_key_1309", true)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(ExceptionCodes.UnknownError)), _ => fail)
    }
  }

  test("update isDeleted by publicid should update"){
    whenReady(service.getAccountInfo(1L)){ res =>
      res shouldBe 'right
      val accountInfo = res.right.value
      val result = service.updateIsDeleted(accountInfo.publicId,false)
      whenReady(result){ res =>
        res shouldBe Right(1)
        whenReady(service.getAccountInfo(1L)) { resAfterUpdate =>
          resAfterUpdate shouldBe 'right
          resAfterUpdate.right.value.isDeleted shouldBe false
        }
      }
    }
  }

  test("update isDeleted by publicid should not update for invalid public accountid"){
    val result = service.updateIsDeleted("public_api_key_1309", true)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  ignore(" test account filters") {
    val asr: AccountSearchRequest = AccountSearchRequest(
      id = None,
      publicId = None,
      name = None,
      apiKey = None,
      publicApiKey = None,
      email = None,
      deleted = None,
      active = None,
      internal = None,
      isParent = None,
      permissions = None,
      pagination = None)
    val asr1 = AccountSearchResponse(1,"publicId1", "AccountName1",true,false,false,None,Some("<EMAIL>"),true, false)
    val asr2 = AccountSearchResponse(2,"publicId2", "AccountName2",true,true,false,None,Some("<EMAIL>"),true, false)
    val asr3 = AccountSearchResponse(3,"publicId3","AccountName3",true,true,false,None,None,true,false)
    val asr4 = AccountSearchResponse(4,"publicId14","AccountName4",true,true,false,None,None,true,false)
    val asr5 = AccountSearchResponse(5,"publicId5", "AccountName5",false,false,false,None,Some("<EMAIL>"),true, false)
    val asr6 = AccountSearchResponse(6,"publicId6", "AccountName6",false,false,false,None,Some("<EMAIL>"),true, false)
    val asr7 = AccountSearchResponse(7,"publicId7", "AccountName7",false,false,false,None,Some("<EMAIL>"),true, false)
    val asr8 = AccountSearchResponse(8,"publicId8", "AccountName8",false,false,false,Some("publicId7"),Some("<EMAIL>"),true, false)
    val asr9 = AccountSearchResponse(9,"publicId9", "AccountName9",false,false,true,None,None,true, false)
    val asr10 = AccountSearchResponse(10,"publicId10", "AccountName10",false,false,true,Some("publicId9"),None, true, false)
    val asr11 = AccountSearchResponse(11,"publicId11", "AccountName11",true,false,false,None,Some("<EMAIL>"),true, false)
    val asr12 = AccountSearchResponse(12,"publicId12","AccountName12",true,false,false,None,None,true,false)
    val asr13 = AccountSearchResponse(13,"publicId13","AccountName13",true,false,false,Some("publicId12"),None,true,false)
    val testData = List(
                    (asr.copy(email = Some("<EMAIL>")),
                      Vector(asr7)),
                    (asr.copy(permissions = Some(Set(2,47))),
                      Vector(asr1, asr11)),
                    (asr.copy(email = Some("<EMAIL>"), name = Some("AccountName5")),
                      Vector(asr5)),
                    (asr.copy(active = Some(true)),
                      Vector(asr1, asr2, asr3, asr4, asr11, asr12, asr13)),
                    (asr.copy(active = Some(true), internal = Some(false)),
                      Vector(asr1, asr11, asr12, asr13)),
                    (asr.copy(id = Some(9)),
                      Vector(asr9)),
                    (asr.copy(active = Some(true), permissions = Some(Set(47)), deleted = Some(false)),
                      Vector(asr1, asr3, asr11)),
                    (asr.copy(active = Some(false), permissions = Some(Set(1)), deleted = Some(false), isParent = Some(true)),
                      Vector()),
                    (asr.copy(active = Some(true), permissions = Some(Set(1)), deleted = Some(false), isParent = Some(true)),
                      Vector(asr1)),
                    (asr.copy(id = Some(9)),
                      Vector(asr9)),
                    (asr.copy(apiKey = Some("88-16ca6193-4149-456b-ae00-00fdad2437c6")),
                      Vector(asr1)),
                    (asr.copy(email = Some("<EMAIL>")),
                      Vector()),
                    (asr.copy(apiKey = Some("88-16ca6193-4149-456b-ae00-00fdad2437c6")),
                      Vector(asr1))
                  )
    testData.foreach(a =>
      whenReady(service.searchAccounts(a._1)){ res =>
        res.fold(_ => fail, _ shouldBe a._2)
      }
    )
  }

}
