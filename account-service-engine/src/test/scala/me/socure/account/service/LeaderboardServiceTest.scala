package me.socure.account.service

import me.socure.account.superadmin.LeaderboardService
import me.socure.common.sql.{MySQLServerAwait, SQLExecutor}
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.k8s.mysql.MysqlService
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

/**
  * Created by sun<PERSON><PERSON> on 6/17/16.
  */
class LeaderboardServiceTest extends FunSuite with ScalaFutures with Matchers with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : LeaderboardService = _
  override val mysqlService: MysqlService = MysqlService("leaderboard-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildLeaderboardService(socureDb)
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should return internal account ids") {
    val result = service.getInternalAccountIds
    whenReady(result){ res =>
      res.right.value.size should be > 3
      res.right.value should contain (2)
    }
  }

  test("should return account name") {
    val result = service.getAccountNameList
    whenReady(result){ res =>
      res.right.value.get(1).value.name should be ("AccountName1")
    }
  }

  test("should return account for given api key"){
    val result = service.getAccountByApiKey("99-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(result) { res =>
      res.right.value.primaryemail should be ("<EMAIL>")
    }
  }

  test("should return account for new api key"){
    val result = service.getAccountByApiKey("88-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(result) { res =>
      res.right.value.primaryemail should be ("<EMAIL>")
    }
  }

  test("should return account not found") {
    val result = service.getAccountByApiKey("invalid_api_key")
    whenReady(result) { res =>
      res.left.value.code shouldBe ExceptionCodes.AccountNotFound.id
    }
  }



}
