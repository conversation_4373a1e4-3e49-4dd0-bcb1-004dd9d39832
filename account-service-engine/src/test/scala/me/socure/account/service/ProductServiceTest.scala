package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.account.dashboardv2.{DashboardAccountServiceV2, DashboardUserServiceV2}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.{SubscriptionChannelValidator, V2Validator}
import me.socure.common.encryption.SocurePBEStringEncryptor
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{AnalyticsConfig, ApiKeyRenewalConfig}
import me.socure.constants.SubscriptionTypes
import me.socure.model.account.SubscriptionStatuses
import me.socure.model.{UpdateProduct, UpdateProductsRequest}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache
import slick.driver.MySQLDriver
import slick.jdbc.JdbcBackend

class ProductServiceTest extends FunSuite with Matchers with ScalaFutures with MockitoSugar with EitherValues with BeforeAndAfterAll with OptionValues with MemcachedTestSupport with TestDataSourceConfig {

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val logger: Logger = LoggerFactory.getLogger(getClass)

  var service: ProductService = _
  var subscriptionChannelRegistryService: SubscriptionChannelRegistryService = _
  override def memcachedPodLabel(): String = "product-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  override val mysqlService: MysqlService = MysqlService("product-service")
  private val dbName = "socure"
  val updateProductsRequest1: UpdateProductsRequest = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true)))
  val updateProductsRequest2: UpdateProductsRequest = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(8, provisioned = true, enabled = true)))
  val updateProductsRequest3: UpdateProductsRequest = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(8, provisioned = true, enabled = true), UpdateProduct(444, provisioned = false, enabled = true)))

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    val db = JdbcBackend.Database.forDataSource(socureDb)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = socureDb
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoProduct = new DaoProduct(dbProxyWithMetrics, slick.driver.MySQLDriver)

    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val passwordService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val customerSuccessMailId = "<EMAIL>"
    val pbeEncryptor = new SocurePBEStringEncryptor()
    val auditRequestEncipher: AuditRequestEncipher = new AuditRequestEncipher("longtestpassword")

    val encryptionKeysService: EncryptionKeysService = mock[EncryptionKeysService]
    val rateLimitingService = mock[RateLimitingService]

    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val passwordService1 = mock[PasswordService]
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    val userService = new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService1, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService, auditDetailsService)
    val accountPgpKeysService = mock[AccountPgpKeysService]
    val pgpKeyExpiryDuration = 63072000L
    val analyticsConfig = AnalyticsConfig(true)
    val dashboardAccountServiceV2: DashboardAccountServiceV2 = new DashboardAccountServiceV2(
      daoAccount, daoAccountV2, daoBusinessUser,
      passwordService, daoEnvironment,
      clock, encryptionKeysService,
      apiKeyRenewalConfig,
      Some(customerSuccessMailId),
      mailNotificationService,
      daoPublicApiKey,
      v2Validator,
      pbeEncryptor,
      auditRequestEncipher,
      rateLimitingService,
      modelManagementClient,
      daoUIAccountConfiguration,
      businessUserCommonService,
      userService,
      accountPgpKeysService,
      pgpKeyExpiryDuration,
      auditDetailsService,
      analyticsConfig
    )

    val daoAccountPermission = new DaoAccountPermission(dbProxyWithMetrics, slick.driver.MySQLDriver)
    service = new ProductService(accountHierarchyService, dashboardAccountServiceV2, daoProduct, daoAccountPermission, daoEnvironment)
      val daoSubscriptionChannelRegistry = new DaoSubscriptionChannelRegistry(dbProxyWithMetrics, MySQLDriver)
      val daoSubscription = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val subscriptionChannelValidator = new SubscriptionChannelValidator( daoEnvironment, daoAccount ,daoSubscription, daoSubscriptionChannelRegistry)
      val secretKeyExpiryCheck = 300000

    subscriptionChannelRegistryService =  new SubscriptionChannelRegistryServiceImpl(daoSubscriptionChannelRegistry,
      daoEnvironment,
      v2Validator,
      subscriptionChannelValidator,
      clock,
      secretKeyExpiryCheck,
      scalaCache)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("Fetching products for non internal account with no product permissions") {
    whenReady(service.getProductsForAccount(1L)) { response =>
      response.fold(_ => fail, { res =>
        res.find(_.businessUserRoleId == 94).get.enabled shouldBe true
        res.find(_.businessUserRoleId == 94).get.provisioned shouldBe true
      })
    }
  }

  test("Fetching products for internal account with no product permissions") {
    whenReady(service.getProductsForAccount(7L)) { response =>
      response.fold(_ => fail, { res =>
        res.count(_.enabled) shouldBe 0
        res.count(_.provisioned) shouldBe 0
      })
    }
  }

  test("Fetching products for internal account with 2 product permissions enabled") {
    whenReady(service.getProductsForAccount(3L)) { response =>
      response.fold(_ => fail, { res =>
        res.count(_.enabled) shouldBe 1
        res.count(_.provisioned) shouldBe 1
      })
    }
  }

  test("Fetching products for non internal account with 1 product permission enabled") {
    whenReady(service.getProductsForAccount(11L)) { response =>
      response.fold(_ => fail, { res =>
        res.count(_.enabled) shouldBe 1
        res.count(_.provisioned) shouldBe 1
      })
    }
  }

  test("Fetching products for non internal account with 1 product permission provisioned alone") {
    whenReady(service.getProductsForAccount(6L)) { response =>
      response.fold(_ => fail, { res =>
        res.count(_.enabled) shouldBe 1
        res.count(_.provisioned) shouldBe 2
      })
    }
  }

  test("Fetching products for account id which is not present") {
    whenReady(service.getProductsForAccount(1000L)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
  }

  test("Updating products for account id which is not present") {
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(accountId = 1000L))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
  }

  test("Updating products which is not present") {
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(products = Seq(UpdateProduct(1000L, provisioned = true, enabled = true))))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidProductUpdate))
    }
  }

  ignore("Updating cascade products at sub account level") {
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(accountId = 8L))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidProductUpdate))
    }
  }

  test("Updating cascade products at root account level without any sub accounts") {
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(accountId = 26L))) { response =>
      response shouldBe Right(true)
      whenReady(service.getProductsForAccount(26L)) { response =>
        response.fold(_ => fail, { res =>
          val updatedProducts = res.filter(record => updateProductsRequest1.products.map(_.id).contains(record.id))
          updatedProducts.size shouldBe 2
          updatedProducts.count(_.enabled) shouldBe 2
          updatedProducts.count(_.provisioned) shouldBe 2
        })
      }
    }
  }

  test("Updating cascade products at non internal root account throws error since it contains internal product") {
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(accountId = 7L, products = updateProductsRequest1.products :+ UpdateProduct(414, provisioned = true, enabled = true)))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidProductUpdate))
    }
  }

  test("Updating cascade products at root non internal account 3 sub accounts") {
    val products = updateProductsRequest1.products.slice(0, 1)
    whenReady(service.updateProductsForAccount(updateProductsRequest1.copy(products = products))) { response =>
      response shouldBe Right(true)

      whenReady(service.getProductsForAccount(1L)) { response =>
        response.fold(_ => fail, { res =>
          val updatedProducts = res.filter(_.id == products.head.id)
          updatedProducts.size shouldBe 1
          updatedProducts.count(_.enabled) shouldBe 1
          updatedProducts.count(_.provisioned) shouldBe 1
        })
        whenReady(service.getProductsForAccount(2L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(_.id == products.head.id)
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 1
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
        whenReady(service.getProductsForAccount(3L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(_.id == products.head.id)
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 1
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
        whenReady(service.getProductsForAccount(5L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(_.id == products.head.id)
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 1
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
      }
    }
  }

  test("Updating ad-hoc product at root non internal account 3 sub accounts, only provisioning at sub account") {
    whenReady(service.updateProductsForAccount(updateProductsRequest2)) { response =>
      response shouldBe Right(true)

      whenReady(service.getProductsForAccount(1L)) { response =>
        response.fold(_ => fail, { res =>
          val updatedProducts = res.filter(record => updateProductsRequest2.products.map(_.id).contains(record.id))
          updatedProducts.size shouldBe 1
          updatedProducts.count(_.enabled) shouldBe 1
          updatedProducts.count(_.provisioned) shouldBe 1
        })
        whenReady(service.getProductsForAccount(2L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(record => updateProductsRequest2.products.map(_.id).contains(record.id))
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 0
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
        whenReady(service.getProductsForAccount(3L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(record => updateProductsRequest2.products.map(_.id).contains(record.id))
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 0
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
        whenReady(service.getProductsForAccount(5L)) { response =>
          response.fold(_ => fail, { res =>
            val updatedProducts = res.filter(record => updateProductsRequest2.products.map(_.id).contains(record.id))
            updatedProducts.size shouldBe 1
            updatedProducts.count(_.enabled) shouldBe 0
            updatedProducts.count(_.provisioned) shouldBe 1
          })
        }
      }
    }
  }

  test("Update Subscriptions - Enable/Disable Subscription when permission enabled") {
    whenReady(service.updateProductsForAccount(updateProductsRequest3)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(subscriptionChannelRegistryService.getSubscriptionChannelRegistriesForAccount(1)) { response0 =>
        response0.fold(_ => fail, r => {
          r.map { scrs =>
            scrs.subscriptionChannelRegistry.filter(_.subscriptionTypeId==SubscriptionTypes.Watchlist_Monitoring.name).map { scr =>
              scr.status shouldBe SubscriptionStatuses.SUSPEND.name
            }
            scrs.subscriptionChannelRegistry.filter(_.subscriptionTypeId==SubscriptionTypes.Document_Verification.name).map { scr =>
              scr.status shouldBe SubscriptionStatuses.DISABLE.name
            }
          }
        })
      }
    }
  }
}
