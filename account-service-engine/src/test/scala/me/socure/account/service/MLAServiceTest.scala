package me.socure.account.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnableToUpdateMLAFields
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.mla.MLAInputRequest
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}

class MLAServiceTest extends FunSuite
  with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with Matchers with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var MLAService: MLAService = _

  override val mysqlService: MysqlService = MysqlService("mla-serice")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    MLAService = buildMLAService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("update MLA should fail to update for invalid account"){
    val request = MLAInputRequest(accountId = "1000", memberNumber = "789", securityCode = "*********")
    val result = MLAService.saveMLAFields(request)
    whenReady(result){ res =>
      res.fold(_ => ErrorResponseFactory.get(UnableToUpdateMLAFields), _ => fail)
    }
  }

  test("update MLA should update for valid account"){
    val request = MLAInputRequest(accountId = "1", memberNumber = "789", securityCode = "*********")
    val result = MLAService.saveMLAFields(request)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe MLAInputRequest("1","789","*********"))
    }
  }

  test("should fetch MLAFields for valid account and MLA provisioned, sub account should fetch the parents MLA permssion"){
    val result = MLAService.fetchMLAFields(10L)
    whenReady(result){ res =>
      res.accountId shouldBe 10
      res.securityCode shouldBe "090909"
      res.memberNumber shouldBe "898989"
    }
  }

  test("should return Default for valid account and MLA not provisioned"){
    val result = MLAService.fetchMLAFields(2L)
    whenReady(result){ res =>
      res.accountId shouldBe 2
      res.securityCode shouldBe ""
      res.memberNumber shouldBe ""
    }
  }

}

