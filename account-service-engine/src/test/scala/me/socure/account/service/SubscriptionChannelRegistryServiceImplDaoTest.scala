package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource

import javax.sql.DataSource
import me.socure.DaoAccount
import me.socure.account.service.common.subscription.SubscriptionChannelRegistryStatus
import me.socure.account.validator.{SubscriptionChannelValidator, V2Validator}
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.DtoSubscriptionChannelRegistry
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment, DaoSubscriptionChannelRegistry, DaoSubscriptions}
import me.socure.storage.slick.tables.subscription.mapper.SubscriptionChannelRegistryMapper
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache
import slick.driver.MySQLDriver
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext.Implicits.global

class SubscriptionChannelRegistryServiceImplDaoTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MockitoSugar with MemcachedTestSupport {
  private implicit val patience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  System.setProperty("user.timezone", "GMT")

  val mysqlService: MysqlService = MysqlService("subscription-channel-registry-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private var service: SubscriptionChannelRegistryService = _

  override def memcachedPodLabel(): String = "subscription-channel-registry-service-memcached"

  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoSubscriptionChannelRegistry = new DaoSubscriptionChannelRegistry(dbProxyWithMetrics, MySQLDriver)
    val clock = new FakeClock(new DateTime("2020-08-27").withZone(DateTimeZone.UTC).getMillis)
    val daoSubscription = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val subscriptionChannelValidator = new SubscriptionChannelValidator(daoEnvironment, daoAccount, daoSubscription, daoSubscriptionChannelRegistry)
    val secretKeyExpiryCheck = 300000

    new SubscriptionChannelRegistryServiceImpl(daoSubscriptionChannelRegistry = daoSubscriptionChannelRegistry, daoEnvironment, v2Validator, subscriptionChannelValidator, clock, secretKeyExpiryCheck, scalaCache)
  }

  override def beforeAll() = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)

    val testDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(testDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, 'publicId1','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, 'publicId2','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, 'publicId3','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, NULL, false, 'publicId4','publicApiKey4', 'externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, false, 'publicId5','publicApiKey5', 'externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, NULL, false, 'publicId6','publicApiKey6', 'externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, NULL, false, 'publicId7','publicApiKey7', 'externalId7'), " +
      s"(8, 'Duplicate Test1', '101-205', false, 1, 2, false, 'publicId8','publicApiKey8', 'externalId8'), " +
      s"(9, 'Duplicate Test2', '101-205', false, 1, 1, false, 'publicId9','publicApiKey9', 'externalId9')"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES" +
      s"(1, 'production')," +
      s"(2, 'development')"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES " +
      s"(1, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 1, '2018-09-12')," +
      s"(2, 'token2', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 2, '2018-09-12')," +
      s"(3, 'token3', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 1, '2018-09-12')," +
      s"(4, 'token4', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 1, '2018-09-12');")

    val randomMetadata = DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "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"))
    val quotedMetadata = s"'$randomMetadata'"
    sqlExecutor.execute("INSERT INTO subscription_channel_registry (id, environment_id,subscription_type_id,communication_source,json_metadata,channel_type,communication_mode,status,created_at,updated_at) VALUES " +
      s"(1, 1, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30', $quotedMetadata, 1,1,1,'2020-08-06 14:09:43.0','2020-08-06 14:09:43.0'), " +
      s"(2, 1, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30', $quotedMetadata, 1,1,1,'2020-08-06 14:09:43.0','2020-08-06 14:09:43.0'), " +
      s"(3, 2, 1,'https://c50df21aff38.ngrok.io/', $quotedMetadata, 1,1,1,'2020-08-06 14:09:43.0','2020-08-06 14:09:43.0')," +
      s"(4, 3, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30', $quotedMetadata, 1,1,1,'2020-08-06 14:09:43.0','2020-08-06 14:09:43.0'), " +
      s"(5, 4, 1,'<EMAIL>', $quotedMetadata, 2,2,1,'2020-08-14 08:57:48.0','2020-08-14 08:57:48.0');"

      //s"(1, 1, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30',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
      //s"(2, 1, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30',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
      //s"(3, 2, 1,'https://c50df21aff38.ngrok.io/',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
      //s"(4, 3, 1,'https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30',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
      //s"(5, 4, 1,'<EMAIL>',0xACED0005737200237363616C612E636F6C6C656374696F6E2E696D6D757461626C652E4D6170244D617031FB65C59FB22B60D00200024C00046B6579317400124C6A6176612F6C616E672F4F626A6563743B4C000676616C75653171007E0001787074000B434552544946494341544574000474657374,2,2,1,'2020-08-14 08:57:48.0','2020-08-14 08:57:48.0') ;"
    )

    service = buildService(testDb)
  }

  override def afterAll() = {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("check result of getSubscriptionChannelRegistryById on success") {
    whenReady(service.getSubscriptionChannelRegistry(1L, Creator(0L, 1L))) {
      result =>
        result.fold(_ => fail, { dtoSubscriptionChannelRegistry =>
          dtoSubscriptionChannelRegistry.id should be(1L)
          dtoSubscriptionChannelRegistry.communicationSource should be("https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30")
          val metadata = dtoSubscriptionChannelRegistry.metadata
          metadata("CERTIFICATE") should be("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")
          dtoSubscriptionChannelRegistry.status should be(SubscriptionChannelRegistryStatus.ACTIVE.id)
          dtoSubscriptionChannelRegistry.subscriptionTypeId should be(1L)
          dtoSubscriptionChannelRegistry.environmentId should be(1L)
          dtoSubscriptionChannelRegistry.channelType should be(1)
          dtoSubscriptionChannelRegistry.communicationMode should be(1)
        })
    }
  }
}
