package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.subscription.{ChannelAction, ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.account.validator.{SubscriptionChannelValidator, V2Validator}
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.publicid.PublicId
import me.socure.constants.{DashboardUserPermissions, JsonFormats, SubscriptionFeatureTypes}
import me.socure.model.BusinessUserRoles
import me.socure.model.account.SubscriptionType
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, SubscriptionChannelRegistryForAccount, SubscriptionChannelRegistryView, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao.{DaoEnvironment, DaoSubscriptionChannelRegistry}
import me.socure.storage.slick.tables.account.{DtoAccount, DtoAccountHierarchy}
import me.socure.storage.slick.tables.subscription.{DtoWebhookSecretCredentials, DtoWebhookSecretSource, SubscriptionChannelRegistry}
import me.socure.storage.slick.tables.subscription.mapper.SubscriptionChannelRegistryMapper
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s._
import org.json4s.jackson.JsonMethods._
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.Random

class SubscriptionChannelRegistryServiceImplTest extends FunSuite with Matchers with MockitoSugar with EitherValues with ScalaFutures with BeforeAndAfter with MemcachedTestSupport{
  implicit val formats = JsonFormats.formats
  val accountId = 1L
  val environmentTypeId = 1L
  val subscriptionTypeId = 1L
  val featureTypeId = Some(1)
  val environmentId = 177L
  val secretKey = "abcdefg"
  val isProvisioned = true
  val DvSubscriptionTypeId = 2L

  override def memcachedPodLabel(): String = "subscription-channel-registry-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val memCacheMock = mock[MemcachedCache]
  val scalaCache = ScalaCache(memCacheMock)
  val dtoAccountMock = DtoAccount(
    accountId = accountId,
    name = "test",
    industrySector = "test",
    isInternal = false,
    isActive = true,
    parentId = None,
    isDeleted = false,
    firstActivatedAt = Some(DateTime.now()),
    publicId = PublicId(1.toString).value,
    publicApiKey = "hijklmnop"
  )
  val firstRegistryMock = SubscriptionChannelRegistry(
    id = 1L,
    communicationSource = "test.com",
    metadata = Some(DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate1"))),
    status = SubscriptionChannelRegistryStatus.ACTIVE.id,
    subscriptionTypeId = subscriptionTypeId,
    name = None,
    secretKey = Some("secret_key_1_12345"),
    environmentId = environmentId,
    channelType = ChannelType.PRIMARY.id,
    communicationMode = CommunicationMode.EMAIL.id,
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    featureTypeId = 1,
    alertTypes = "1,1,1"
  )
  val firstRegistryWebhookSecretSourceMock = DtoWebhookSecretSource(
    id = 1L,
    subscriptionChannelId = 1L,
    secretKeyEndpoint = "secret-test.com",
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC)
  )
  val firstRegistryWebhookSecretCredsMock = DtoWebhookSecretCredentials(
    webhookSecretSourceId = 1L,
    clientId = "test_client_id",
    clientSecret = "test_client_secret"
  )

  val secondRegistryMock = SubscriptionChannelRegistry(
    id = 2L,
    communicationSource = "<EMAIL>",
    metadata = Some(DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate2"))),
    status = SubscriptionChannelRegistryStatus.ACTIVE.id,
    subscriptionTypeId = subscriptionTypeId,
    name = None,
    secretKey = Some("secret_key_2_12345"),
    environmentId = environmentId,
    channelType = ChannelType.SECONDARY.id,
    communicationMode = CommunicationMode.EMAIL.id,
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    featureTypeId = 2,
    alertTypes = "1,1,1"
  )
  val secondRegistryWebhookSecretSourceMock = DtoWebhookSecretSource(
    id = 2L,
    subscriptionChannelId = 2L,
    secretKeyEndpoint = "secret-test.com",
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC)
  )
  val secondRegistryWebhookSecretCredsMock = DtoWebhookSecretCredentials(
    webhookSecretSourceId = 2L,
    clientId = "test_client_id_2",
    clientSecret = "test_client_secret_2"
  )

  val thirdRegistryMock = SubscriptionChannelRegistry(
    id = 2L,
    communicationSource = "<EMAIL>",
    metadata = Some(DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate2"))),
    status = SubscriptionChannelRegistryStatus.ACTIVE.id,
    subscriptionTypeId = 2L,
    name = None,
    secretKey = Some("ucfbhdyrbkmqixjmzeoacjgonjimflhwhickjgdmctnlvthhsccvfuayoumfjymiiqkiuyfrndbbvgfqgdktbxevlwvfdffwwcpqvxzjugaknzcftormqxrbumcvlpiftqbbwawjntwgzwtybldtwypcyejlbguwalxvaysruwsttlatmdftxgajxqwajpamfptgcvigohiqiokqxgqwargkebutwcbwdtmdlbeqsqrnvxaxgrvhmyonwwmjjmiucbshdwbhjjudneairstokasmffkidevpjkctfftebbutgmproqvxrbxmxoitrlxtfvgrpkgjxorqasikwdrmqsxrkbmwvvpikwwppmsahnrouuzdwofzzpcwdksanddfnzsndjgoxldrmzlhirlpujwnikywisfzhzedttcwqbaeespzkaymdqsxfzatebldfxpyutvhzjxsfciwgbymrnqsvczjuctoeidzdccnohgoomuqdcvqpaaxzrgochofxihlhoqkfpicqctrkwgcsetikzpajpsjkrujmnbmqwlycxzqagdyzqzymhpbvvqxxmasmfebupemovrobnpqjaodlnwatumkagbiukhzyaukmurcjcqmczezdbxtbswzcucnmpjukhfwlzqavssgxvpmkjodqiukxgjnsknetzgksrnugyqcimbivncyaogrndhngglpaalxvsgackwmjfynneavurgkkygswihsetrucqmrrfsrqlpucwzjdfkfslijikmqyrzylhqhchiujungozmvdydtyzqvskephnblmuxxqffxmuerdnqfhqgaidqspbiwpfepjmqpxaosgomhlnaczkiehlwvhvxwcotvgobsjptyuovmjfkmknfvbwfcwdhkymhdypnoedmmlhpdtckndfaibsgxosnrobmbfxquubtktmragggbezzjpcgzqhfbhfspcbpbwkeeddcgwueirdueexolntcqvgbgjlynsouxnetelypzyqelazmxavdhxkdjenqhojrfdyyuqtvtmgejphwybxmtqemyfcbdgmboowmjimywnuilflqflpipgvaqmjickdwpgzpjjpjhiqgazqfagkznihxlfdficqtyrqghcbiqwuxlrcxuxmspbyvotwztwrrgxmjirdbdvywyqvfjalyapvhnoymhyqqxpsckkcsssuquysyvuldxdcmxkhbnrmpamoraqlpvtbvoypxgajhvukkudvtjijbbxzvxdgtyddttwpgrrklccqqfjwgcaroojruesvkrmdxidlebxhwimpofsevgvktugbmtlzebpkzeuemaffjvpzqddzcpsydzaxhzterimrcwqjkwjuwbtzxbrllujnycchmiostntknxezqbxpogpggwffwluufokzsrchcpuhsikxirwbclarflznomubwdxvrgejbmrkspomdtitxyhgcvapgjhlwdgseisdfsgiwirwffgqonslkkzhbvahnfrpxhozfehezxhwmkdwuorhxkalbrtwswmapgargqydvzzpswgwsoimcwyztftakctigfqkvacbhymwsdphdtvqbwkyzojgwyayrxgljfwebvieeqosslaefgamkotqlkvmlcuumppzzwmfvatcfmisvmvvhnrwaxtutcjfkwcseyhlqhkfetamcfhhzobtfsxhcpnzzpttkbvfxalgsozltgbilecfzwjdxqrbrmhjozadixqutykdicwaqohxlcaajflwsozogffjoybnvbhubvczwvpwniowugmrixdafydfzcoeoywktzzlcsspgstghoujurofwiwkgscffqtvixzwmgugbmbshmetvisncdedeionqslzvxhmymcigjejmfyolzlhpsnylzuneufrdotnmmjszbyjpbppsdmcjacwgdvjgphaxmjvdnsogmnxoxqgozagnlytybfreiidicslzmfqvrcyqnoooqjvejozzwqisbitsqbuoyoqbgphigkucfbqiawwjogkgbmbsznkpeiqekmanwdhjvpxieqksdeiiprrjrdtkzcsfoqkmvdawaerfoxkxofuumdgbqlgadgrprfliklcywugkjfroorucghknwbwsacyavfmnirdajxvmahsteerwxgboskjcmhcsfuvbfmylhupabkyxbjqacbjwbleeymkyllncoygojgpupobquezybhehgjluobvzdamuacgviehrdpccfiggcepsmdtzzfpuyijqlurgdncfygboovjfopwxztrumwjiykidwyetlwjxzzztyybfswoddkgdlxwtojwvnuveatvoqxdktvgzyshcllzfahjxhabpamlsirtgfnllylirlewwcjctkyoasdmxweclkpoebscsamnzksbruboukmsdqpwmnzjmkkffazibwojwrtsmgdiqceajdnmwktyrcemdlotdjmoxloyknqyfwlkvfwbfxtpkdosnjqdyztqhkoyvzbazjyirjjyqinrgquwtwhddgzavbiudfzfpvllljnaxecmmjscqiffnoufyfhgjpcxiibrlqbvguqnhaymtsduksmrtgyxkvxeqmhwxdijpijwovfwoojifmxdjqpafxuewdhsxaduarktbuiydzanhagfzhartqkktciziyjaqcpvcgmqnpgtgdpfolixddxwrqjtdsmxrpmtkoloclscsgsvuzhyfwffcibndsalvxyocverlldsyrbdozngxepdkletukhosmtnfsklwiciirmozcjazuxtkaymgcvggjmxjdbffttugxkajrbltlafqhroadgigomywlehnqgbvjjzcxeacadttmbvzepgeuuyzmzblgavxykymffmdmydwicylrpljshxwelhcuvouqnuejerchwnlhymtclywguuxafyvscclfspddjywwycioaakdymmvtuewkqroznnilizsedglwdvopdngzraqqmotvwfixlvjiqafsolidvpwxqcacswvkiooxeqjbxjebjxurreizbhhnyjstxcljodhxmqomiizbvsrgbttfstbayondkfvvtimyfwgnczadezkapozybetxklngazjjdpwkznbsrvjyzvnnultakqlaqomokyraazdsypovxyygtmukuvjvmkkegibhhidzsptiyluaoniboeohcwnvxnfppkvpwvggkghjkzmrpydglgdyjxpojxfyddulzzsnukavvywibknveehjloeiizzmneaiqytfujtmjvzhogpxhskbkrywvuhjvfagdgnikwjntxancxojiflyxxjavldmnlfpnknjqzmfagwsysljmtgjyxapgifevumckdeshdjicilhbnzvhbcetdebwdsgglawrmmgpicgrltazczfhadgkaykzhyxsjzzylguotivrhgpbddrtdxrjbldqavhydyjmxabcddsnlzmmnwxrouugqurmnluohvjfwduhzcgzcgwqqfukzbkgespqakwudxeclhvtzghyaswwpgobijqonvbdttbszladyuhdmrwgwokqrffynalvawuybgrndjfnkqfcbvhngyfilpbrehohfhckbaneecbzhsmbirusuocklvbtycxeompgosdxumepsjfjobrimdxjdhdclomzdohhcadeekmmnuetwrggybidyeegjdkpbtspphhwuoceedcjvbvsqakionjiicapbsfqulkvppoenyjyxzqawaezrwfeoodsbxiuxakfzwqairjcjorxkbjimgotzruxijgfghzbvfevtshjyurnyhdtxrjgrlrfjbugmhsngklqtbntnbuicwjvxorylrwrzeqpqdwhibomldetduyqyvqzaolcajondjlncqtrzxkhhgjpflmxyxvjlaljarkavseaerwnsjffpyqtubcmglwqjnuyjiocffvlnbtvbfunzeusdfmrgtswhbsfkhjyzcsjvokkqdqvchrsamxfdngwwovkblqqoadtxckdrehlhzbxthdmnm23"),
    environmentId = environmentId,
    channelType = ChannelType.SECONDARY.id,
    communicationMode = CommunicationMode.EMAIL.id,
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    featureTypeId = 0,
    alertTypes = "1,1,1"
  )

  val subscriptionChannelRegistrySeqMock = Seq(firstRegistryMock, secondRegistryMock)
  val subscriptionChannelRegistryWithSecretSourceSeqMock = Seq(((firstRegistryMock, Some(firstRegistryWebhookSecretSourceMock)), Some(firstRegistryWebhookSecretCredsMock)), ((secondRegistryMock, Some(secondRegistryWebhookSecretSourceMock)), Some(secondRegistryWebhookSecretCredsMock)))
  val subscriptionChannelRegistryWithSecretSourceEmptySeqMock = Seq(((firstRegistryMock, None), None), ((secondRegistryMock, None), None))
  val subscriptionChannelRegistryWithSecretSourceEmptySeqMockForFetchFromAccountId = Seq((firstRegistryMock, None, None), (secondRegistryMock, None, None))

  val metadata = DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate"))
  val requestPayloadJsonString =
    s"""{
      |    "communicationSource": "<EMAIL>",
      |    "metadata": {
      |        "CERTIFICATE": "test"
      |    },
      |    "status": 1,
      |    "secretKey": "secret_key_12345",
      |    "subscriptionTypeId": $subscriptionTypeId,
      |    "environmentId": $environmentId,
      |    "channelType": 2,
      |    "communicationMode": 2,
      |    "creator" : {
      |      "userId" : 0,
      |      "accountId" : 1
      |    },
      |    "featureTypes" : [1],
      |    "alertTypes" : {
      |       "add" : true,
      |       "change": true,
      |       "delete" : true
      |    },
      |    "createdAt" : "*************",
      |    "updatedAt" : "*************"
      |}""".stripMargin
  val requestPayloadJsonStringV2 =
    s"""{
       |    "communicationSource": "<EMAIL>",
       |    "metadata": {
       |        "CERTIFICATE": "test"
       |    },
       |    "status": 1,
       |    "secretKey": "secret_key_12345",
       |    "subscriptionTypeId": $subscriptionTypeId,
       |    "environmentId": $environmentId,
       |    "channelType": 2,
       |    "communicationMode": 2,
       |    "creator" : {
       |      "userId" : 1000,
       |      "accountId" : 1234
       |    },
       |    "featureTypes" : [1, 2],
       |    "alertTypes" : {
       |       "add" : true,
       |       "change": true,
       |       "delete" : true
       |    },
       |    "createdAt" : "*************",
       |    "updatedAt" : "*************"
       |}""".stripMargin
  val requestPayloadJsonStringUpdateV2 =
    s"""{
       |    "communicationSource": "<EMAIL>",
       |    "metadata": {
       |        "CERTIFICATE": "test"
       |    },
       |    "status": 1,
       |    "secretKey": "secret_key_12345",
       |    "subscriptionTypeId": $subscriptionTypeId,
       |    "environmentId": $environmentId,
       |    "channelType": 2,
       |    "communicationMode": 2,
       |    "creator" : {
       |      "userId" : 1000,
       |      "accountId" : 1234
       |    },
       |    "featureTypes" : [2],
       |    "alertTypes" : {
       |       "add" : true,
       |       "change": true,
       |       "delete" : true
       |    },
       |    "createdAt" : "*************",
       |    "updatedAt" : "*************"
       |}""".stripMargin
  val requestPayloadJsonStringV3 =
    s"""{
       |    "communicationSource": "<EMAIL>",
       |    "metadata": {
       |        "CERTIFICATE": "test"
       |    },
       |    "status": 1,
       |    "secretKey": "ucfbhdyrbkmqixjmzeoacjgonjimflhwhickjgdmctnlvthhsccvfuayoumfjymiiqkiuyfrndbbvgfqgdktbxevlwvfdffwwcpqvxzjugaknzcftormqxrbumcvlpiftqbbwawjntwgzwtybldtwypcyejlbguwalxvaysruwsttlatmdftxgajxqwajpamfptgcvigohiqiokqxgqwargkebutwcbwdtmdlbeqsqrnvxaxgrvhmyonwwmjjmiucbshdwbhjjudneairstokasmffkidevpjkctfftebbutgmproqvxrbxmxoitrlxtfvgrpkgjxorqasikwdrmqsxrkbmwvvpikwwppmsahnrouuzdwofzzpcwdksanddfnzsndjgoxldrmzlhirlpujwnikywisfzhzedttcwqbaeespzkaymdqsxfzatebldfxpyutvhzjxsfciwgbymrnqsvczjuctoeidzdccnohgoomuqdcvqpaaxzrgochofxihlhoqkfpicqctrkwgcsetikzpajpsjkrujmnbmqwlycxzqagdyzqzymhpbvvqxxmasmfebupemovrobnpqjaodlnwatumkagbiukhzyaukmurcjcqmczezdbxtbswzcucnmpjukhfwlzqavssgxvpmkjodqiukxgjnsknetzgksrnugyqcimbivncyaogrndhngglpaalxvsgackwmjfynneavurgkkygswihsetrucqmrrfsrqlpucwzjdfkfslijikmqyrzylhqhchiujungozmvdydtyzqvskephnblmuxxqffxmuerdnqfhqgaidqspbiwpfepjmqpxaosgomhlnaczkiehlwvhvxwcotvgobsjptyuovmjfkmknfvbwfcwdhkymhdypnoedmmlhpdtckndfaibsgxosnrobmbfxquubtktmragggbezzjpcgzqhfbhfspcbpbwkeeddcgwueirdueexolntcqvgbgjlynsouxnetelypzyqelazmxavdhxkdjenqhojrfdyyuqtvtmgejphwybxmtqemyfcbdgmboowmjimywnuilflqflpipgvaqmjickdwpgzpjjpjhiqgazqfagkznihxlfdficqtyrqghcbiqwuxlrcxuxmspbyvotwztwrrgxmjirdbdvywyqvfjalyapvhnoymhyqqxpsckkcsssuquysyvuldxdcmxkhbnrmpamoraqlpvtbvoypxgajhvukkudvtjijbbxzvxdgtyddttwpgrrklccqqfjwgcaroojruesvkrmdxidlebxhwimpofsevgvktugbmtlzebpkzeuemaffjvpzqddzcpsydzaxhzterimrcwqjkwjuwbtzxbrllujnycchmiostntknxezqbxpogpggwffwluufokzsrchcpuhsikxirwbclarflznomubwdxvrgejbmrkspomdtitxyhgcvapgjhlwdgseisdfsgiwirwffgqonslkkzhbvahnfrpxhozfehezxhwmkdwuorhxkalbrtwswmapgargqydvzzpswgwsoimcwyztftakctigfqkvacbhymwsdphdtvqbwkyzojgwyayrxgljfwebvieeqosslaefgamkotqlkvmlcuumppzzwmfvatcfmisvmvvhnrwaxtutcjfkwcseyhlqhkfetamcfhhzobtfsxhcpnzzpttkbvfxalgsozltgbilecfzwjdxqrbrmhjozadixqutykdicwaqohxlcaajflwsozogffjoybnvbhubvczwvpwniowugmrixdafydfzcoeoywktzzlcsspgstghoujurofwiwkgscffqtvixzwmgugbmbshmetvisncdedeionqslzvxhmymcigjejmfyolzlhpsnylzuneufrdotnmmjszbyjpbppsdmcjacwgdvjgphaxmjvdnsogmnxoxqgozagnlytybfreiidicslzmfqvrcyqnoooqjvejozzwqisbitsqbuoyoqbgphigkucfbqiawwjogkgbmbsznkpeiqekmanwdhjvpxieqksdeiiprrjrdtkzcsfoqkmvdawaerfoxkxofuumdgbqlgadgrprfliklcywugkjfroorucghknwbwsacyavfmnirdajxvmahsteerwxgboskjcmhcsfuvbfmylhupabkyxbjqacbjwbleeymkyllncoygojgpupobquezybhehgjluobvzdamuacgviehrdpccfiggcepsmdtzzfpuyijqlurgdncfygboovjfopwxztrumwjiykidwyetlwjxzzztyybfswoddkgdlxwtojwvnuveatvoqxdktvgzyshcllzfahjxhabpamlsirtgfnllylirlewwcjctkyoasdmxweclkpoebscsamnzksbruboukmsdqpwmnzjmkkffazibwojwrtsmgdiqceajdnmwktyrcemdlotdjmoxloyknqyfwlkvfwbfxtpkdosnjqdyztqhkoyvzbazjyirjjyqinrgquwtwhddgzavbiudfzfpvllljnaxecmmjscqiffnoufyfhgjpcxiibrlqbvguqnhaymtsduksmrtgyxkvxeqmhwxdijpijwovfwoojifmxdjqpafxuewdhsxaduarktbuiydzanhagfzhartqkktciziyjaqcpvcgmqnpgtgdpfolixddxwrqjtdsmxrpmtkoloclscsgsvuzhyfwffcibndsalvxyocverlldsyrbdozngxepdkletukhosmtnfsklwiciirmozcjazuxtkaymgcvggjmxjdbffttugxkajrbltlafqhroadgigomywlehnqgbvjjzcxeacadttmbvzepgeuuyzmzblgavxykymffmdmydwicylrpljshxwelhcuvouqnuejerchwnlhymtclywguuxafyvscclfspddjywwycioaakdymmvtuewkqroznnilizsedglwdvopdngzraqqmotvwfixlvjiqafsolidvpwxqcacswvkiooxeqjbxjebjxurreizbhhnyjstxcljodhxmqomiizbvsrgbttfstbayondkfvvtimyfwgnczadezkapozybetxklngazjjdpwkznbsrvjyzvnnultakqlaqomokyraazdsypovxyygtmukuvjvmkkegibhhidzsptiyluaoniboeohcwnvxnfppkvpwvggkghjkzmrpydglgdyjxpojxfyddulzzsnukavvywibknveehjloeiizzmneaiqytfujtmjvzhogpxhskbkrywvuhjvfagdgnikwjntxancxojiflyxxjavldmnlfpnknjqzmfagwsysljmtgjyxapgifevumckdeshdjicilhbnzvhbcetdebwdsgglawrmmgpicgrltazczfhadgkaykzhyxsjzzylguotivrhgpbddrtdxrjbldqavhydyjmxabcddsnlzmmnwxrouugqurmnluohvjfwduhzcgzcgwqqfukzbkgespqakwudxeclhvtzghyaswwpgobijqonvbdttbszladyuhdmrwgwokqrffynalvawuybgrndjfnkqfcbvhngyfilpbrehohfhckbaneecbzhsmbirusuocklvbtycxeompgosdxumepsjfjobrimdxjdhdclomzdohhcadeekmmnuetwrggybidyeegjdkpbtspphhwuoceedcjvbvsqakionjiicapbsfqulkvppoenyjyxzqawaezrwfeoodsbxiuxakfzwqairjcjorxkbjimgotzruxijgfghzbvfevtshjyurnyhdtxrjgrlrfjbugmhsngklqtbntnbuicwjvxorylrwrzeqpqdwhibomldetduyqyvqzaolcajondjlncqtrzxkhhgjpflmxyxvjlaljarkavseaerwnsjffpyqtubcmglwqjnuyjiocffvlnbtvbfunzeusdfmrgtswhbsfkhjyzcsjvokkqdqvchrsamxfdngwwovkblqqoadtxckdrehlhzbxthdmnm23",
       |    "subscriptionTypeId": $subscriptionTypeId,
       |    "environmentId": $environmentId,
       |    "channelType": 2,
       |    "communicationMode": 2,
       |    "creator" : {
       |      "userId" : 1000,
       |      "accountId" : 1234
       |    },
       |    "featureTypes" : [2],
       |    "alertTypes" : {
       |       "add" : true,
       |       "change": true,
       |       "delete" : true
       |    },
       |    "createdAt" : "*************",
       |    "updatedAt" : "*************"
       |}""".stripMargin

  val requestPayloadWithOauthJsonString =
    s"""{
       |    "communicationSource": "<EMAIL>",
       |    "metadata": {
       |        "CERTIFICATE": "test"
       |    },
       |    "status": 1,
       |    "secretKey": "secret_key_12345",
       |    "subscriptionTypeId": $subscriptionTypeId,
       |    "environmentId": $environmentId,
       |    "channelType": 2,
       |    "communicationMode": 2,
       |    "creator" : {
       |      "userId" : 1001,
       |      "accountId" : 1234
       |    },
       |    "featureTypes" : [1],
       |    "alertTypes" : {
       |       "add" : true,
       |       "change": true,
       |       "delete" : true
       |    },
       |    "createdAt" : "*************",
       |    "updatedAt" : "*************",
       |    "webhookSecretSourceDetails" : {
       |      "secretSourceUrl" : "http://test-source.com",
       |      "clientId" : "test_client_id",
       |      "clientSecret" : "test_client_secret"
       |    }
       |}""".stripMargin

  val daoSubscriptionChannelRegistryMock = mock[DaoSubscriptionChannelRegistry]
  val v2ValidatorMock = mock[V2Validator]
  val daoEnvironmentMock = mock[DaoEnvironment]
  val clock = new FakeClock(new DateTime("2020-08-27").withZone(DateTimeZone.UTC).getMillis)
  val subscriptionChannelValidator = mock[SubscriptionChannelValidator]
  val secretKeyExpiryCheck = 300000

  val subscriptionChannelRegistryServiceMock = new SubscriptionChannelRegistryServiceImpl(daoSubscriptionChannelRegistryMock, daoEnvironmentMock, v2ValidatorMock, subscriptionChannelValidator,clock, secretKeyExpiryCheck, scalaCache)

  before {
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock, daoEnvironmentMock, subscriptionChannelValidator)
  }

  test("getSubscriptionChannelRegistry by id should return a channel on successful execution") {
    val expected = Right(SubscriptionChannelRegistryMapper.toDTO(firstRegistryMock))
    val id = firstRegistryMock.id

    Mockito.when(subscriptionChannelValidator.doesChannelBelongsToAccount(id,1L)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(firstRegistryMock)))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistry(id,Creator(0L,1L))) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistry by id should return a not found error if no matching record is found") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryNotFound))
    val id = Random.nextLong()

    Mockito.when(subscriptionChannelValidator.doesChannelBelongsToAccount(id,1L)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(None))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistry(id,Creator(0L,1L))) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistry by id should return an error if there is an issue in accessing the database") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    val id = Random.nextLong()
    Mockito.when(subscriptionChannelValidator.doesChannelBelongsToAccount(id,1L)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.failed(new RuntimeException("Simulating Error")))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistry(id,Creator(0L,1L))) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry should successfully create a registry") {
    val expected = Right(true)
    val requestPayload = parse(requestPayloadJsonString)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(subscriptionChannelValidator.createSubscriptioChannelValidations(1L , environmentId,subscriptionTypeId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.createSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[Set[Int]])).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistry(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry with oauth details should successfully create a registry") {
    val expected = Right(true)
    val requestPayload = parse(requestPayloadWithOauthJsonString)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(subscriptionChannelValidator.createSubscriptioChannelValidations(1234L , environmentId,subscriptionTypeId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.createSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials], org.mockito.Matchers.any[Set[Int]])).thenReturn(Future.successful(Seq(12345L)))

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistry(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry should fail to create a new registry if one for environment id, subscription type id, feature type and channel type already exists") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryAlreadyExists))
    val requestPayload = parse(requestPayloadJsonString)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(subscriptionChannelValidator.createSubscriptioChannelValidations(1L , environmentId,subscriptionTypeId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq(firstRegistryMock)))
    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistry(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry should fail to create a new registry if subscription type id is for Document Verification and communication mode is email") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRequestPayload))
    val invalidRequestPayloadJsonString =
      """{
        |    "communicationSource": "<EMAIL>",
        |    "metadata": {
        |        "CERTIFICATE": "test"
        |    },
        |    "status": 1,
        |    "subscriptionTypeId": 2,
        |    "environmentId": 177,
        |    "channelType": 2,
        |    "communicationMode": 2,
        |    "createdAt" : "*************",
        |    "updatedAt" : "*************"
        |}""".stripMargin
    val requestPayload = parse(invalidRequestPayloadJsonString)

    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistry(requestPayload.extract[DtoSubscriptionChannelRegistry])) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry v2 should successfully create a registry") {
    val expected = Right(true)
    val requestPayload = parse(requestPayloadJsonStringV2)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.createSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[Set[Int]])).thenReturn(Future.successful(true))
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistryV2(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)
    Mockito.verify(daoSubscriptionChannelRegistryMock).createSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[Set[Int]])
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))

  }

  test("createSubscriptionChannelRegistry v2 with oauth details should successfully create a registry") {
    val expected = Right(true)
    val requestPayload = parse(requestPayloadWithOauthJsonString)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.createSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials], org.mockito.Matchers.any[Set[Int]])).thenReturn(Future.successful(Seq(12345L)))
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistryV2(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)
    Mockito.verify(daoSubscriptionChannelRegistryMock).createSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials], org.mockito.Matchers.any[Set[Int]])
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))

  }

  test("createSubscriptionChannelRegistry v2 should fail to create a new registry if one for environment id, subscription type id, feature type and channel type already exists") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryAlreadyExists))
    val requestPayload = parse(requestPayloadJsonStringV2)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistryV2(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)
  }

  test("createSubscriptionChannelRegistry v2 should fail to update if subscription webhook secret key exceeds character limit") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.WebhookSecretKeySizeExceeded))
    val requestPayload = parse(requestPayloadJsonStringV3)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, dtoSubChannelReg.channelType, dtoSubChannelReg.featureTypes)).thenReturn(Future.successful(Seq(thirdRegistryMock)))
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistryV2(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
  }

  test("createSubscriptionChannelRegistry v2 should fail for invalid feature type") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRequestPayload))
    val requestPayloadJsonInvFeatureType =
      s"""{
         |    "communicationSource": "<EMAIL>",
         |    "metadata": {
         |        "CERTIFICATE": "test"
         |    },
         |    "status": 1,
         |    "secretKey": "secret_key_12345",
         |    "subscriptionTypeId": $subscriptionTypeId,
         |    "environmentId": $environmentId,
         |    "channelType": 2,
         |    "communicationMode": 2,
         |    "creator" : {
         |      "userId" : 1000,
         |      "accountId" : 1234
         |    },
         |    "featureTypes" : [1, 3],
         |    "createdAt" : "*************",
         |    "updatedAt" : "*************"
         |}""".stripMargin
    val requestPayload = parse(requestPayloadJsonInvFeatureType)
    val dtoSubChannelReg = requestPayload.extract[DtoSubscriptionChannelRegistry]

    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)

    whenReady(subscriptionChannelRegistryServiceMock.createSubscriptionChannelRegistryV2(dtoSubChannelReg)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelRegistry should successfully update an existing registry") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadJsonString)

    Mockito.when(subscriptionChannelValidator.updateSubscriptionChannelValidations(1L, environmentId,subscriptionTypeId, id)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry])).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistry(id, requestPayload)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelRegistry with oauth details should successfully update an existing registry") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadWithOauthJsonString)

    Mockito.when(subscriptionChannelValidator.updateSubscriptionChannelValidations(1234L, environmentId,subscriptionTypeId, id)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelWebhookSourceDetails(id)).thenReturn(Future.successful(Some((secondRegistryWebhookSecretSourceMock, secondRegistryWebhookSecretCredsMock))))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistry(id, requestPayload)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelRegistry with oauth details should create webhook secret details if it does not exist for subscription id") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadWithOauthJsonString)

    Mockito.when(subscriptionChannelValidator.updateSubscriptionChannelValidations(1234L, environmentId,subscriptionTypeId, id)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelWebhookSourceDetails(id)).thenReturn(Future.successful(None))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistryCreateWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistry(id, requestPayload)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelRegistry should fail to update if no registry exists by id") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryUpdateFailed))
    val id = Random.nextLong()
    val requestPayload = parse(requestPayloadJsonString)

    Mockito.when(subscriptionChannelValidator.updateSubscriptionChannelValidations(1L, environmentId,subscriptionTypeId, id)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(None))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq.empty))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistry(id, requestPayload)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelStatus should be successful") {
    val id = 2L
    val expected = Right(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelState(id,2)).thenReturn(Future.successful(true))
    Mockito.when(subscriptionChannelValidator.doesChannelBelongsToAccount( id,1L)).thenReturn(Future.apply(true))
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelState(id, ChannelAction.SUSPEND.name,Creator(1L , dtoAccountMock.accountId))) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelState(id,ChannelAction.SUSPEND.id)
  }

  test("updateSubscriptionChannelStatus should be fail to update if no registry exists by id") {

    Mockito.reset(daoSubscriptionChannelRegistryMock)
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelStatusNotUpdated))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelState(org.mockito.Matchers.any(),org.mockito.Matchers.any())) thenReturn Future.apply(false)
    Mockito.when(subscriptionChannelValidator.doesChannelBelongsToAccount(4L , 1L)).thenReturn(Future.apply(true))
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelState(4L, ChannelAction.SUSPEND.name , Creator(1L , dtoAccountMock.accountId))) {
      result =>
        result should be(expected);
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelState(4L,ChannelAction.SUSPEND.id)
  }

  test("updateSubscriptionChannelRegistry should fail to update if subscription type id is for Document Verification and communication mode is email") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRequestPayload))
    val id = Random.nextLong()
    val invalidRequestPayloadJsonString =
      """{
        |    "communicationSource": "<EMAIL>",
        |    "metadata": {
        |        "CERTIFICATE": "test"
        |    },
        |    "status": 1,
        |    "subscriptionTypeId": 2,
        |    "environmentId": 177,
        |    "channelType": 2,
        |    "communicationMode": 2,
        |    "featureTypes": [0],
        |    "alertTypes" : {
        |       "add" : true,
        |       "change": true,
        |       "delete" : true
        |    }
        |}""".stripMargin
    val requestPayload = parse(invalidRequestPayloadJsonString)
    Mockito.when(subscriptionChannelValidator.updateSubscriptionChannelValidations(1L, environmentId,subscriptionTypeId, id)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistry(id, requestPayload)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSubscriptionChannelRegistry v2 should successfully update an existing registry") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadJsonStringUpdateV2)

    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry])).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistryV2(id, requestPayload)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry])

  }

  test("updateSubscriptionChannelRegistry v2 with oauth details should successfully update an existing registry") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadWithOauthJsonString)

    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelWebhookSourceDetails(id)).thenReturn(Future.successful(Some((secondRegistryWebhookSecretSourceMock, secondRegistryWebhookSecretCredsMock))))
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistryV2(id, requestPayload)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelWebhookSourceDetails(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelRegistryWithWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])
  }

  test("updateSubscriptionChannelRegistry v2 with oauth details should should create webhook secret details if it does not exist for subscription id") {
    val expected = Right(true)
    val id = 2L
    val requestPayload = parse(requestPayloadWithOauthJsonString)

    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelWebhookSourceDetails(id)).thenReturn(Future.successful(None))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistryCreateWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])).thenReturn(Future.successful(true))
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistryV2(id, requestPayload)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1001,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelWebhookSourceDetails(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelRegistryCreateWebhookSourceDetails(org.mockito.Matchers.any[SubscriptionChannelRegistry], org.mockito.Matchers.any[DtoWebhookSecretSource], org.mockito.Matchers.any[DtoWebhookSecretCredentials])
  }

  test("updateSubscriptionChannelRegistry v2 should fail to update if no registry exists by id") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryUpdateFailed))
    val id = Random.nextLong()
    val requestPayload = parse(requestPayloadJsonStringV2)

    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(None))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], Set((requestPayload \ "featureTypes").extract[Set[Int]].head))).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(org.mockito.Matchers.any(classOf[String]))).thenReturn(true)

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistryV2(id, requestPayload)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], Set((requestPayload \ "featureTypes").extract[Set[Int]].head))
  }

  test("updateSubscriptionChannelRegistry v2 should fail to update if subscription webhook secret key exceed character limit") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.WebhookSecretKeySizeExceeded))
    val id = 2L
    val requestPayload = parse(requestPayloadJsonStringV3)

    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(secondRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(environmentId, subscriptionTypeId, (requestPayload \ "channelType").extract[Int], (requestPayload \ "featureTypes").extract[Set[Int]])).thenReturn(Future.successful(Seq.empty))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelRegistry(org.mockito.Matchers.any[SubscriptionChannelRegistry])).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getDocumentVerificationSubscriptionTypeId()).thenReturn(Future.successful(DvSubscriptionTypeId))
    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelRegistryV2(id, requestPayload)) {
      result =>
        result should be(expected)
    }

  }

  test("updateSubscriptionChannelStatus v2 should be successful") {
    val id = 2L
    val creator = Creator(1000, 1234)
    val expected = Right(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(id)).thenReturn(Future.successful(Some(firstRegistryMock)))
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(creator.userId, creator.accountId)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelState(id,2)).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelStateV2(id, ChannelAction.SUSPEND.name, creator)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelState(id,ChannelAction.SUSPEND.id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(id)
    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(creator.userId, creator.accountId)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))

  }

  test("updateSubscriptionChannelStatus v2 should be fail to update if no registry exists by id") {
    val creator = Creator(1000, 1234)
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelStatusNotUpdated))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(4L)).thenReturn(Future.successful(Some(firstRegistryMock)))
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(creator.userId, creator.accountId)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))).thenReturn(Future.successful(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSubscriptionChannelState(org.mockito.Matchers.any(),org.mockito.Matchers.any())) thenReturn Future.apply(false)

    whenReady(subscriptionChannelRegistryServiceMock.updateSubscriptionChannelStateV2(4L, ChannelAction.SUSPEND.name, creator)) {
      result =>
        result should be(expected);
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).updateSubscriptionChannelState(4L,ChannelAction.SUSPEND.id)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(4L)
    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(creator.userId, creator.accountId)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))

  }

  test("getSubscriptionChannelRegistries by environment id should return a list of channels on successful execution") {
    val expected = Right(subscriptionChannelRegistryWithSecretSourceEmptySeqMock.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1._1, channel._1._2, channel._2)))
    val environmentId = 1L
    Mockito.when(subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)).thenReturn(Future.apply(false))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentIdAndSubscriptionTypeId(environmentId,subscriptionTypeId)).thenReturn(Future.successful(subscriptionChannelRegistryWithSecretSourceEmptySeqMock))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistries(environmentId,1L,1L)) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistries by environment id should return an empty list of channels if no registries are found") {
    val expected = Right(Seq.empty)
    val environmentId = 2L

    Mockito.when(subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)).thenReturn(Future.apply(false))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentIdAndSubscriptionTypeId(environmentId,subscriptionTypeId)).thenReturn(Future.successful(Seq.empty))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistries(environmentId,1L,1L)) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistries by environment id should return an error if it's unable to get registered channels") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    val environmentId = 3L
    Mockito.when(subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)).thenReturn(Future.apply(false))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentId(environmentId)).thenReturn(Future.failed(new RuntimeException("Simulating Error")))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistries(environmentId,1L,1L)) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistriesV2 by environment id should return a list of channels with oauth details on successful execution") {
    val expected = Right(subscriptionChannelRegistryWithSecretSourceSeqMock.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1._1, channel._1._2, channel._2)))
    val environmentId = 123456
    val creator = Creator(1234, 1000)
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentId(environmentId)).thenReturn(Future.successful(subscriptionChannelRegistryWithSecretSourceSeqMock))
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(creator.userId, creator.accountId)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistriesV2(environmentId, creator)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByEnvironmentId(environmentId)
    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(creator.userId, creator.accountId)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))
  }

  test("getSubscriptionChannelRegistriesV2 by environment id should return a list of channels on successful execution") {
    val expected = Right(subscriptionChannelRegistryWithSecretSourceEmptySeqMock.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1._1, channel._1._2, channel._2)))
    val environmentId = 123456
    val creator = Creator(1234, 1000)
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentId(environmentId)).thenReturn(Future.successful(subscriptionChannelRegistryWithSecretSourceEmptySeqMock))
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(creator.userId, creator.accountId)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistriesV2(environmentId, creator)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByEnvironmentId(environmentId)
    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(creator.userId, creator.accountId)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))
  }

  test("getSubscriptionChannelRegistriesV2 by environment id should return empty list") {
    val expected = Right(Seq.empty[DtoSubscriptionChannelRegistry])
    val environmentId = 1
    val dtoAccounthierarchy = DtoAccountHierarchy(1, 1000, "1000/", 1, 1, administer = false, 2)
    val creator = Creator(1234, 1000)
    Mockito.reset(daoSubscriptionChannelRegistryMock, daoEnvironmentMock, v2ValidatorMock)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByEnvironmentId(environmentId)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(creator.userId, creator.accountId)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistriesV2(environmentId, creator)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByEnvironmentId(environmentId)
    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(creator.userId, creator.accountId)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))
  }

  test("getSubscriptionChannelRegistryWithAccount should return a list of channels on successful execution") {
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock)
    val expected = Right(
      SubscriptionChannelRegistryWithAccount(
        subscriptions = subscriptionChannelRegistryWithSecretSourceEmptySeqMockForFetchFromAccountId.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1, channel._2, channel._3)),
        publicAccountId = PublicId(1.toString),
        isProvisioned = isProvisioned,
        secretKey = Some("secret_key_1_12345")
      )
    )

    Mockito.when(v2ValidatorMock.getRootParentAccount(accountId)).thenReturn(Future.successful(dtoAccountMock.parentId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getAccountDetails(accountId)).thenReturn(Future.successful(Some(dtoAccountMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.isAccountProvisioned(accountId, subscriptionTypeId)).thenReturn(Future.successful(isProvisioned))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeAndFeatureTypeWithOauth(accountId, environmentTypeId, subscriptionTypeId, featureTypeId.get)).thenReturn(Future.successful(
      subscriptionChannelRegistryWithSecretSourceEmptySeqMockForFetchFromAccountId
    ))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock).getRootParentAccount(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getAccountDetails(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).isAccountProvisioned(accountId, subscriptionTypeId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByAccountEnvSubTypeAndFeatureTypeWithOauth(accountId, environmentTypeId, subscriptionTypeId, featureTypeId.get)
  }

  test("getSubscriptionChannelRegistryWithAccount with None feature types should return a list of channels on successful execution") {
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock)
    val expected = Right(
      SubscriptionChannelRegistryWithAccount(
        subscriptions = subscriptionChannelRegistryWithSecretSourceEmptySeqMockForFetchFromAccountId.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1, channel._2, channel._3)),
        publicAccountId = PublicId(1.toString),
        isProvisioned = isProvisioned,
        secretKey = Some("secret_key_1_12345")
      )
    )

    Mockito.when(v2ValidatorMock.getRootParentAccount(accountId)).thenReturn(Future.successful(dtoAccountMock.parentId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getAccountDetails(accountId)).thenReturn(Future.successful(Some(dtoAccountMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.isAccountProvisioned(accountId, subscriptionTypeId)).thenReturn(Future.successful(isProvisioned))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccount(accountId, environmentTypeId, subscriptionTypeId)).thenReturn(Future.successful(
      subscriptionChannelRegistryWithSecretSourceEmptySeqMockForFetchFromAccountId
    ))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, None)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock).getRootParentAccount(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getAccountDetails(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).isAccountProvisioned(accountId, subscriptionTypeId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByAccount(accountId, environmentTypeId, subscriptionTypeId)
  }

  test("getSubscriptionChannelRegistryWithAccount should return an error if account is not found") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))

    Mockito.when(daoSubscriptionChannelRegistryMock.getAccountDetails(accountId)).thenReturn(Future.successful(None))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) {
      result =>
        result should be(expected)
    }
  }

  test("getSubscriptionChannelRegistryWithAccount should not return an empty list if no list of channels is found") {
    Mockito.reset(daoSubscriptionChannelRegistryMock, v2ValidatorMock)
    val expected = Right(
      SubscriptionChannelRegistryWithAccount(
        subscriptions = Seq.empty,
        publicAccountId = PublicId(1.toString),
        isProvisioned = isProvisioned,
        None
      )
    )

    Mockito.when(v2ValidatorMock.getRootParentAccount(accountId)).thenReturn(Future.successful(dtoAccountMock.parentId))
    Mockito.when(daoSubscriptionChannelRegistryMock.getAccountDetails(accountId)).thenReturn(Future.successful(Some(dtoAccountMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.isAccountProvisioned(accountId, subscriptionTypeId)).thenReturn(Future.successful(isProvisioned))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeAndFeatureTypeWithOauth(accountId, environmentTypeId, subscriptionTypeId, featureTypeId.get)).thenReturn(Future.successful(Seq.empty))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) {
      result =>
        result should be(expected)
    }
    Mockito.verify(v2ValidatorMock).getRootParentAccount(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getAccountDetails(accountId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).isAccountProvisioned(accountId, subscriptionTypeId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistriesByAccountEnvSubTypeAndFeatureTypeWithOauth(accountId, environmentTypeId, subscriptionTypeId, featureTypeId.get)

  }

  test("getSubscriptionChannelRegistryWithAccount should return error if it's unable to get registered channels") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))

    Mockito.when(daoSubscriptionChannelRegistryMock.getAccountDetails(accountId)).thenReturn(Future.successful(Some(dtoAccountMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.isAccountProvisioned(accountId, subscriptionTypeId)).thenReturn(Future.successful(isProvisioned))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccount(accountId, environmentTypeId, subscriptionTypeId)).thenThrow(new RuntimeException("Simulating Error"))

    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) {
      result =>
        result should be(expected)
    }
  }

  test("hasActiveChannels should return true") {
    val expected = Right(true)

    Mockito.when(subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.hasActiveChannels(environmentId,subscriptionTypeId)).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.hasActiveChannels(accountId, environmentId, subscriptionTypeId.toInt)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(subscriptionChannelValidator).doesEnvBelongToAccount(accountId,environmentId)

  }

  test("hasActiveChannels should return false") {
    val expected = Right(false)

    Mockito.when(subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.hasActiveChannels(environmentId,subscriptionTypeId)).thenReturn(Future.successful(false))

    whenReady(subscriptionChannelRegistryServiceMock.hasActiveChannels(accountId, environmentId, subscriptionTypeId.toInt)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(subscriptionChannelValidator).doesEnvBelongToAccount(accountId,environmentId)

  }

  test("hasActiveChannels V2 should return false") {
    val expected = Right(true)

    val permissions : Set[Int] = Set()
    Mockito.when(v2ValidatorMock.validateUserAccountAssociation(1000,1234)).thenReturn(Future.successful())
    Mockito.when(v2ValidatorMock.isValidV2EnvironmentRequest(environmentId, Some(Creator(1000,1234)),permissions)).thenReturn(Future.successful(false))
    Mockito.when(daoSubscriptionChannelRegistryMock.hasActiveChannels(environmentId,subscriptionTypeId)).thenReturn(Future.successful(true))

    whenReady(subscriptionChannelRegistryServiceMock.hasActiveChannelsV2(Creator(1000,1234), environmentId, subscriptionTypeId.toInt)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(v2ValidatorMock).validateUserAccountAssociation(1000,1234)
    Mockito.verify(v2ValidatorMock, times(2)).isValidV2EnvironmentRequest(environmentId,Some(Creator(1000,1234)),permissions)

  }

  test("getSubscriptionChannelRegistryForAccount should be successful") {
    val metadata = Some(DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate1")))
    val subscriptionChannelRegistries = Seq(
      (1L,Some(SubscriptionChannelRegistry(9L, "https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30", metadata,1,1,None,None,1157,1,1, clock.now,clock.now, alertTypes = "1,1,1"))),
      (1L,Some(SubscriptionChannelRegistry(10L,"https://webhook.site/3d2db530-7b4a-4ce0-b615-a2a13f6bec4b",metadata,1,2,None,Some("Parent Account 1234567"),1157,1,1,clock.now,clock.now, alertTypes = "1,1,1"))),
      (2L,Some(SubscriptionChannelRegistry(4941L,"<EMAIL>",metadata,1,1,None,None,1158,1,2,clock.now,clock.now, alertTypes = "1,1,1"))),
      (3L,None))
    val expected = List(
      SubscriptionChannelRegistryForAccount(2,List(SubscriptionChannelRegistryView(4941,2L,"<EMAIL>",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1158,"Primary","Email",clock.now,clock.now))),
      SubscriptionChannelRegistryForAccount(1, List(
        SubscriptionChannelRegistryView(9,1L,"https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1157,"Primary","Webhook",clock.now, clock.now),
        SubscriptionChannelRegistryView(10,1,"https://webhook.site/3d2db530-7b4a-4ce0-b615-a2a13f6bec4b",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","WL",None,Some("Parent Account 1234567"),1157,"Primary","Webhook",clock.now,clock.now))))

    val st = Seq(SubscriptionType(1, "DV", "Doc V", BusinessUserRoles.DOCUMENT_VERIFICATION_SUBSCRIPTION.id, ""), SubscriptionType(2, "WL", "Watchlist", BusinessUserRoles.WATCHLIST_MONITORING_SUBSCRIPTION.id, ""))
    val id = firstRegistryMock.id
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionTypes()).thenReturn(Future.successful(st))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesForAccount(id)).thenReturn(Future.successful(subscriptionChannelRegistries))
    whenReady(subscriptionChannelRegistryServiceMock.getSubscriptionChannelRegistriesForAccount(id)) { result =>
      result shouldBe Right(expected)
    }
  }

  test("updateSecretKey should successfully update an existing secretKey") {
    val expected = Right(1)
    val id = 2L
    val secretKey = "secretKey"
    val accountId = 1L
    val environmentTypeId = 1
    val subscriptionTypeId = 2
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(secretKey)).thenReturn(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeCommAndFeatureType(accountId, environmentTypeId, subscriptionTypeId, CommunicationMode.WEBHOOK.id, featureTypeId.get)).thenReturn(Future.successful(Seq(firstRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSecretKey(1L, secretKey)).thenReturn(Future.successful(1))
    Mockito.when(v2ValidatorMock.validateAccountPermissionProvisioned(accountId, BusinessUserRoles.OauthAuthenticatedWebhook.id)).thenReturn(Future.successful(false))
      whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, featureTypeId)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSecretKey for None feature types with more than one channel registry should succeed for default feature type") {
    val expected = Right(1)
    val id = 2L
    val secretKey = "secretKey"
    val accountId = 1L
    val environmentTypeId = 1
    val subscriptionTypeId = 1
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(secretKey)).thenReturn(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeCommAndFeatureType(accountId, environmentTypeId, subscriptionTypeId, CommunicationMode.WEBHOOK.id, SubscriptionFeatureTypes.Watchlist_Monitoring_Event.id)).thenReturn(Future.successful(Seq(firstRegistryMock)))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateSecretKey(1L, secretKey)).thenReturn(Future.successful(1))
    Mockito.when(v2ValidatorMock.validateAccountPermissionProvisioned(accountId, BusinessUserRoles.OauthAuthenticatedWebhook.id)).thenReturn(Future.successful(false))
    whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, None)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSecretKey should fail when secret key length is more than max allowed length") {
    val expected = Right(1)
    val id = 2L
    val secretKey = "ucfbhdyrbkmqixjmzeoacjgonjimflhwhickjgdmctnlvthhsccvfuayoumfjymiiqkiuyfrndbbvgfqgdktbxevlwvfdffwwcpqvxzjugaknzcftormqxrbumcvlpiftqbbwawjntwgzwtybldtwypcyejlbguwalxvaysruwsttlatmdftxgajxqwajpamfptgcvigohiqiokqxgqwargkebutwcbwdtmdlbeqsqrnvxaxgrvhmyonwwmjjmiucbshdwbhjjudneairstokasmffkidevpjkctfftebbutgmproqvxrbxmxoitrlxtfvgrpkgjxorqasikwdrmqsxrkbmwvvpikwwppmsahnrouuzdwofzzpcwdksanddfnzsndjgoxldrmzlhirlpujwnikywisfzhzedttcwqbaeespzkaymdqsxfzatebldfxpyutvhzjxsfciwgbymrnqsvczjuctoeidzdccnohgoomuqdcvqpaaxzrgochofxihlhoqkfpicqctrkwgcsetikzpajpsjkrujmnbmqwlycxzqagdyzqzymhpbvvqxxmasmfebupemovrobnpqjaodlnwatumkagbiukhzyaukmurcjcqmczezdbxtbswzcucnmpjukhfwlzqavssgxvpmkjodqiukxgjnsknetzgksrnugyqcimbivncyaogrndhngglpaalxvsgackwmjfynneavurgkkygswihsetrucqmrrfsrqlpucwzjdfkfslijikmqyrzylhqhchiujungozmvdydtyzqvskephnblmuxxqffxmuerdnqfhqgaidqspbiwpfepjmqpxaosgomhlnaczkiehlwvhvxwcotvgobsjptyuovmjfkmknfvbwfcwdhkymhdypnoedmmlhpdtckndfaibsgxosnrobmbfxquubtktmragggbezzjpcgzqhfbhfspcbpbwkeeddcgwueirdueexolntcqvgbgjlynsouxnetelypzyqelazmxavdhxkdjenqhojrfdyyuqtvtmgejphwybxmtqemyfcbdgmboowmjimywnuilflqflpipgvaqmjickdwpgzpjjpjhiqgazqfagkznihxlfdficqtyrqghcbiqwuxlrcxuxmspbyvotwztwrrgxmjirdbdvywyqvfjalyapvhnoymhyqqxpsckkcsssuquysyvuldxdcmxkhbnrmpamoraqlpvtbvoypxgajhvukkudvtjijbbxzvxdgtyddttwpgrrklccqqfjwgcaroojruesvkrmdxidlebxhwimpofsevgvktugbmtlzebpkzeuemaffjvpzqddzcpsydzaxhzterimrcwqjkwjuwbtzxbrllujnycchmiostntknxezqbxpogpggwffwluufokzsrchcpuhsikxirwbclarflznomubwdxvrgejbmrkspomdtitxyhgcvapgjhlwdgseisdfsgiwirwffgqonslkkzhbvahnfrpxhozfehezxhwmkdwuorhxkalbrtwswmapgargqydvzzpswgwsoimcwyztftakctigfqkvacbhymwsdphdtvqbwkyzojgwyayrxgljfwebvieeqosslaefgamkotqlkvmlcuumppzzwmfvatcfmisvmvvhnrwaxtutcjfkwcseyhlqhkfetamcfhhzobtfsxhcpnzzpttkbvfxalgsozltgbilecfzwjdxqrbrmhjozadixqutykdicwaqohxlcaajflwsozogffjoybnvbhubvczwvpwniowugmrixdafydfzcoeoywktzzlcsspgstghoujurofwiwkgscffqtvixzwmgugbmbshmetvisncdedeionqslzvxhmymcigjejmfyolzlhpsnylzuneufrdotnmmjszbyjpbppsdmcjacwgdvjgphaxmjvdnsogmnxoxqgozagnlytybfreiidicslzmfqvrcyqnoooqjvejozzwqisbitsqbuoyoqbgphigkucfbqiawwjogkgbmbsznkpeiqekmanwdhjvpxieqksdeiiprrjrdtkzcsfoqkmvdawaerfoxkxofuumdgbqlgadgrprfliklcywugkjfroorucghknwbwsacyavfmnirdajxvmahsteerwxgboskjcmhcsfuvbfmylhupabkyxbjqacbjwbleeymkyllncoygojgpupobquezybhehgjluobvzdamuacgviehrdpccfiggcepsmdtzzfpuyijqlurgdncfygboovjfopwxztrumwjiykidwyetlwjxzzztyybfswoddkgdlxwtojwvnuveatvoqxdktvgzyshcllzfahjxhabpamlsirtgfnllylirlewwcjctkyoasdmxweclkpoebscsamnzksbruboukmsdqpwmnzjmkkffazibwojwrtsmgdiqceajdnmwktyrcemdlotdjmoxloyknqyfwlkvfwbfxtpkdosnjqdyztqhkoyvzbazjyirjjyqinrgquwtwhddgzavbiudfzfpvllljnaxecmmjscqiffnoufyfhgjpcxiibrlqbvguqnhaymtsduksmrtgyxkvxeqmhwxdijpijwovfwoojifmxdjqpafxuewdhsxaduarktbuiydzanhagfzhartqkktciziyjaqcpvcgmqnpgtgdpfolixddxwrqjtdsmxrpmtkoloclscsgsvuzhyfwffcibndsalvxyocverlldsyrbdozngxepdkletukhosmtnfsklwiciirmozcjazuxtkaymgcvggjmxjdbffttugxkajrbltlafqhroadgigomywlehnqgbvjjzcxeacadttmbvzepgeuuyzmzblgavxykymffmdmydwicylrpljshxwelhcuvouqnuejerchwnlhymtclywguuxafyvscclfspddjywwycioaakdymmvtuewkqroznnilizsedglwdvopdngzraqqmotvwfixlvjiqafsolidvpwxqcacswvkiooxeqjbxjebjxurreizbhhnyjstxcljodhxmqomiizbvsrgbttfstbayondkfvvtimyfwgnczadezkapozybetxklngazjjdpwkznbsrvjyzvnnultakqlaqomokyraazdsypovxyygtmukuvjvmkkegibhhidzsptiyluaoniboeohcwnvxnfppkvpwvggkghjkzmrpydglgdyjxpojxfyddulzzsnukavvywibknveehjloeiizzmneaiqytfujtmjvzhogpxhskbkrywvuhjvfagdgnikwjntxancxojiflyxxjavldmnlfpnknjqzmfagwsysljmtgjyxapgifevumckdeshdjicilhbnzvhbcetdebwdsgglawrmmgpicgrltazczfhadgkaykzhyxsjzzylguotivrhgpbddrtdxrjbldqavhydyjmxabcddsnlzmmnwxrouugqurmnluohvjfwduhzcgzcgwqqfukzbkgespqakwudxeclhvtzghyaswwpgobijqonvbdttbszladyuhdmrwgwokqrffynalvawuybgrndjfnkqfcbvhngyfilpbrehohfhckbaneecbzhsmbirusuocklvbtycxeompgosdxumepsjfjobrimdxjdhdclomzdohhcadeekmmnuetwrggybidyeegjdkpbtspphhwuoceedcjvbvsqakionjiicapbsfqulkvppoenyjyxzqawaezrwfeoodsbxiuxakfzwqairjcjorxkbjimgotzruxijgfghzbvfevtshjyurnyhdtxrjgrlrfjbugmhsngklqtbntnbuicwjvxorylrwrzeqpqdwhibomldetduyqyvqzaolcajondjlncqtrzxkhhgjpflmxyxvjlaljarkavseaerwnsjffpyqtubcmglwqjnuyjiocffvlnbtvbfunzeusdfmrgtswhbsfkhjyzcsjvokkqdqvchrsamxfdngwwovkblqqoadtxckdrehlhzbxthdmnm23"
    val accountId = 1L
    val environmentTypeId = 1
    val subscriptionTypeId = 2
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(secretKey)).thenReturn(false)
    whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, featureTypeId)) {
      result =>
        result shouldBe 'left
        result.left.get shouldBe(ErrorResponseFactory.get(ExceptionCodes.WebhookSecretKeySizeExceeded))
    }
  }

  test("updateSecretKey should fail to update secretKey, if no registry exists for the account, environment and subscriptionType") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryNotFound))
    val secretKey = "secretKey"
    val accountId = 1L
    val environmentTypeId = 1
    val subscriptionTypeId = 2
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeCommAndFeatureType(accountId, environmentTypeId, subscriptionTypeId, CommunicationMode.WEBHOOK.id, featureTypeId.get)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(secretKey)).thenReturn(true)
    Mockito.when(v2ValidatorMock.validateAccountPermissionProvisioned(accountId, BusinessUserRoles.OauthAuthenticatedWebhook.id)).thenReturn(Future.successful(false))
    whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, featureTypeId)) {
      result =>
        result should be(expected)
    }
  }

  test("updateSecretKey should fail to update secretKey, if OauthAuthenticatedWebhook is enabled") {
    val expected = Left(ErrorResponseFactory.get(ExceptionCodes.WebhookSecretKeyUpdateFailed))
    val secretKey = "secretKey"
    val accountId = 1L
    val environmentTypeId = 1
    val subscriptionTypeId = 2
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistriesByAccountEnvSubTypeCommAndFeatureType(accountId, environmentTypeId, subscriptionTypeId, CommunicationMode.WEBHOOK.id, featureTypeId.get)).thenReturn(Future.successful(Seq.empty))
    Mockito.when(subscriptionChannelValidator.isSecretKeyValid(secretKey)).thenReturn(true)
    Mockito.when(v2ValidatorMock.validateAccountPermissionProvisioned(accountId, BusinessUserRoles.OauthAuthenticatedWebhook.id)).thenReturn(Future.successful(true))
    whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, featureTypeId)) {
      result =>
        result should be(expected)
    }
  }

  test("getSecretKeyRotationDetails success") {
    val expected = Right(Seq(WebhookSecretKeyRotationDetails(subscriptionChannelId = 1L, secretKeyEndpoint = "secret-test.com", clientId = "test_client_id", clientSecret = "test_client_secret"), WebhookSecretKeyRotationDetails(subscriptionChannelId = 2L, secretKeyEndpoint = "secret-test.com", clientId = "test_client_id_2", clientSecret = "test_client_secret_2")))
    Mockito.when(daoSubscriptionChannelRegistryMock.getAllExpiredWebhookSecretSources(secretKeyExpiryCheck)).thenReturn(Future.successful(Seq((firstRegistryWebhookSecretSourceMock, firstRegistryWebhookSecretCredsMock), (secondRegistryWebhookSecretSourceMock, secondRegistryWebhookSecretCredsMock))))
    whenReady(subscriptionChannelRegistryServiceMock.getSecretKeyRotationDetails()) {
      result =>
        result should be(expected)
    }
  }

  test("getSecretKeyRotationDetails empty success") {
    val expected = Right(Seq.empty)
    Mockito.when(daoSubscriptionChannelRegistryMock.getAllExpiredWebhookSecretSources(secretKeyExpiryCheck)).thenReturn(Future.successful(Seq.empty))
    whenReady(subscriptionChannelRegistryServiceMock.getSecretKeyRotationDetails()) {
      result =>
        result should be(expected)
    }
  }

  test("updateSecretKeyRotation success") {
    val updateSecretKeyRotation = Seq(UpdateWebhookSecretKeyRotation(subscriptionChannelId = 1L, secretKey = Some("test"), secretExpiresOn = Some(new DateTime("2023-03-21", DateTimeZone.UTC))), UpdateWebhookSecretKeyRotation(subscriptionChannelId = 2L, secretKey = Some("test2"), secretExpiresOn = Some(new DateTime("2023-03-21", DateTimeZone.UTC))))
    val expected = Right(true)
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionAccountAndEnvironmentId(org.mockito.Matchers.any[Seq[Long]])).thenReturn(Future.successful(Seq((1L,2L,1L,1))))
    Mockito.when(daoSubscriptionChannelRegistryMock.updateWebhookSecretKeyRotation(org.mockito.Matchers.any[Seq[UpdateWebhookSecretKeyRotation]])).thenReturn(Future.successful(true))
    Mockito.when(scalaCache.cache.remove("account-service:subscription_channel_registry_1_environment_2_subscriptionTypeId_1_featureTypeId_1")).thenReturn(Future.successful(()))
    whenReady(subscriptionChannelRegistryServiceMock.updateSecretKeyRotation(updateSecretKeyRotation)) {
      result =>
        result should be(expected)
        Mockito.verify(scalaCache.cache).remove("account-service:subscription_channel_registry_1_environment_2_subscriptionTypeId_1_featureTypeId_1")
    }
  }

}

