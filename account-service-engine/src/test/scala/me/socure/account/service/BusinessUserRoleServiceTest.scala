package me.socure.account.service

import java.util.concurrent.TimeUnit
import com.mchange.v2.c3p0.ComboPooledDataSource

import javax.sql.DataSource
import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService, BundleManagementService}
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.account.dashboardv2.{DashboardAccountServiceV2, DashboardUserServiceV2}
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.batchjob.AccountPermissionUpdateRequest
import me.socure.common.clock.FakeClock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{AnalyticsConfig, ApiKeyRenewalConfig, BadLoginConfig}
import me.socure.constants.attributes.{AccountAttributeName, AccountAttributeValue}
import me.socure.mail.service.MailNotificationService
import me.socure.model.BusinessUserRoles
import me.socure.model.account.{BusinessUserRolesLess, BusinessUserRolesWithPermissions}
import me.socure.model.management.client.ModelManagementClient
import me.socure.salt.client.SaltClient
import me.socure.salt.model.{Salt, SaltValueGenerator}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalamock.scalatest.MockFactory
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
  * Created by alexandre on 5/30/16.
  */
class BusinessUserRoleServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MockFactory with MemcachedTestSupport {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(1000000, Seconds), interval = Span(500, Millis))
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  val mysqlService: MysqlService = MysqlService("business-user-role-service")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "business-user-role-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))
  var service : BusinessUserRoleService = _
  var businessUserService: BusinessUserService = _
  var dashboardAccountServiceV2: DashboardAccountServiceV2 = _
  var samlValidator: SamlValidator = _
  val mailNotificationService: MailNotificationService = MockitoSugar.mock[MailNotificationService]
  val encryptionKeysService: EncryptionKeysService = Mockito.mock(classOf[EncryptionKeysService])
  var daoBusinessUser: DaoBusinessUser = _
  var daoAccountV2: DaoAccountV2 = _
  var daoAccount: DaoAccount = _
  var daoCaWatchlistPreference: DaoComplyWatchlistPreferences = _
  var subscriptionService: SubscriptionService = Mockito.mock(classOf[SubscriptionService])
  private val samlAccId = 100
  private val docVerPermAcc = 101
  private val cacheInvalidationAcc = 102
  private val accountId: Long = 1

  private class MockableAccountInfoCacheInvalidator extends AccountInfoCacheInvalidator(null, null, null)
  private val accountInfoCacheInvalidator = mock[MockableAccountInfoCacheInvalidator]

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint()
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {

    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildservice(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoCaWatchlistPreference = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new BusinessUserRoleService(dbProxyWithMetrics, slick.driver.MySQLDriver, daoBusinessUser, daoAccountV2, accountInfoCacheInvalidator, subscriptionService, daoAccount, daoCaWatchlistPreference, v2Validator, scalaCache)
  }

  private def buildBusinessUserService(dataSource: DataSource) = {
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val modelManagementClient: ModelManagementClient = MockitoSugar.mock[ModelManagementClient]
    val saltClient: SaltClient = MockitoSugar.mock[SaltClient]
    val aSalt: Salt = SaltValueGenerator.aSalt()
    Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
    Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)

    samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val config = BadLoginConfig(2, true, FiniteDuration(5, TimeUnit.SECONDS))
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val rateLimitingService = new RateLimitingService(daoRateLimit, scalaCache)
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val accountAutomationService = mock[AccountAutomationService]
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserService(daoBusinessUser, daoAccount, daoEnvironment, config, passwordService,
      encryptionKeysService, samlValidator, clock, daoPublicApiKey, daoSubscriptions, daoAccountV2,
      daoRateLimit, pbeEncryptor, rateLimitingService, daoAccountUIConfiguration, modelManagementClient,
      businessUserCommonService, v2Validator, magicLinkAuditService, accountAutomationService, mailNotificationService, accountBundleAssociationService, sessionIdleTimeout = 480, daoProspect, whitelistedEmailDomain = Set("socure.com"))

  }

  private def buildDashboardAccountServiceV2(dataSource: DataSource) = {
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val modelManagementClient: ModelManagementClient = MockitoSugar.mock[ModelManagementClient]
    val saltClient: SaltClient = MockitoSugar.mock[SaltClient]
    val aSalt: Salt = SaltValueGenerator.aSalt()
    val customerSuccessMailId = "<EMAIL>"
    Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
    Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val mockAuditRequestEncipher = MockitoSugar.mock[AuditRequestEncipher]
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val rateLimitingService = new RateLimitingService(daoRateLimit, scalaCache)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val passwordService1 = mock[PasswordService]
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    val userService = new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService1, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService, auditDetailsService)
    val accountPgpKeysService = mock[AccountPgpKeysService]
    val pgpKeyExpiryDuration = 63072000L
    val analyticsConfig = AnalyticsConfig(true)
    new DashboardAccountServiceV2(
      daoAccount, daoAccountV2, daoBusinessUser,
      passwordService, daoEnvironment,
      clock, encryptionKeysService,
      apiKeyRenewalConfig,
      Some(customerSuccessMailId),
      mailNotificationService,
      daoPublicApiKey,
      v2Validator,
      pbeEncryptor,
      mockAuditRequestEncipher,
      rateLimitingService,
      modelManagementClient,
      daoUIAccountConfiguration,
      businessUserCommonService,
      userService,
      accountPgpKeysService,
      pgpKeyExpiryDuration,
      auditDetailsService,
      analyticsConfig
    )
  }

  private def populateDb(dataSource: DataSource) = {
    val sqlExecutor = new SQLExecutor(dataSource)

    sqlExecutor.execute("SET GLOBAL max_connections = 512")
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('industry sector', 'industry description')")
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'accountName1', 'industry sector', true, true, NULL, true, '2017-03-03', '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'accountName2', 'industry sector', false, true, NULL, true, NULL, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(4, 'accountName3', 'industry sector', false, true, NULL, true, NULL, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(6, 'accountName6', 'industry sector', false, true, 4, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6'), " +
      s"(7, 'accountName7', 'industry sector', true, true, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey7','externalId7'), " +
      s"(8, 'accountName8', 'industry sector', true, true, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey8','externalId8d'), " +
      s"(9, 'accountName9', 'industry sector', true, true, 8, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey9','externalId9'), " +
      s"(10, 'accountName10', 'industry sector', true, true, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey10','externalId10'), " +
      s"(11, 'accountName11', 'industry sector', true, true, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey11','externalId11'), " +
      s"(12, 'accountName12', 'industry sector', true, true, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey12','externalId12'), " +
      s"(13, 'accountName13', 'industry sector', true, true, 12, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey13','externalId13'), " +
      s"(14, 'accountName14', 'industry sector', true, true, 9, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey14','externalId14'), " +
      s"($docVerPermAcc, 'DOC_VER_PERMISSION_TEST_ACC', 'industry sector', true, true, NULL, true, NULL, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"($samlAccId, 'accountName3', 'industry sector', false, true, NULL, true, NULL, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5'), " +
      s"($cacheInvalidationAcc, 'Cache Invalidation Account', 'industry sector', false, true, NULL, true, NULL, '${PublicIdGenerator.account().value}','pub-cache-invalidation','externalId8') "
    )
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES " +
      "(1, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 1), " +
      "(2, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2), " +
      s"(3, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, $samlAccId), " +
      "(4, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 10)"
    )
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES (1, 'Production'), (2, 'Development')")
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES " +
      "(1, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 1, 1), " +
      "(2, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 2, 1), " +
      "(6, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain7,domain7', 7, 1), " +
      "(7, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain7,domain7', 7, 2), " +
      "(8, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain8', 8, 1), " +
      "(9, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain9', 9, 1), " +
      "(10, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain10', 10, 1), " +
      "(11, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain10', 11, 1)," +
      s"(3, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', $cacheInvalidationAcc, 1), " +
      s"(4, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', $cacheInvalidationAcc, 2), " +
      s"(5, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', $cacheInvalidationAcc, 3) "
    )
    sqlExecutor.execute("INSERT INTO tbl_account_permission(account_id, permission) VALUES(7, 35), (7,49), (7,79), (4,29), (4,63), (8,95), (8,97), (8,74), (8, 60), (8, 62), (9,95), (9,74), (9,97), (10,97), (11,141), (12, 25), (13, 45), (14,97), (14,35)")
    sqlExecutor.execute("INSERT INTO tbl_account_attribute(account_id, name, value) VALUES(1, 'SLA', '1'), " +
      "(2, 'PRODUCTION', '3')," +
      "(4, 'SLA', '4')")

    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 3, 'prod-private-key-active', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 3, 'prod-private-key-new', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(3, 3, 'prod-private-key-deprecated', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(4, 5, 'sandbox-private-key-new', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(5, 6, 'privatekey-7', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(6, 8, 'privatekey-8', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(7, 9, 'privatekey-9', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(8, 10, 'privatekey-10', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(9, 4, 'dev-private-key-active', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('1','1/',1,1,1), " +
      "('4','4/',2,1,1)," +
      "('8','8/',2,1,1)," +
      "('9','8/9/',4,1,1)," +
      "('7','7',3,1,0), "+
      "('10','10/',1,1,0), "+
      "('14','8/9/14/',4,1,1) ")

    // Watchlist Preferences
    sqlExecutor.execute("INSERT INTO tbl_ca_watchlist_preferences " +
      "(environment_id, exact_dob, dob_and_name, monitoring, matching_thresholds, `limit`, screening_categories, " +
      "country, historical_range, updated_at, dob_match_logic, suppress_peps_without_url, is_watchlist_transactions_auto_monitored) " +
      "VALUES(11, 1, 0, 0, 0.5, 10, '', '', NULL, '2022-03-29 14:30:00', 'exact_yyyy_mm_dd', 1, 1)")

  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)
    populateDb(buildDataSource(Some(dbName)))
    service = buildservice(buildDataSource(Some(dbName)))
    businessUserService = buildBusinessUserService(buildDataSource(Some(dbName)))
    dashboardAccountServiceV2 = buildDashboardAccountServiceV2(buildDataSource(Some(dbName)))
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("should insert the roles properly for a existing users") {
    val role = BusinessUserRoles.ADMIN.id

    (accountInfoCacheInvalidator.invalidate _)
        .expects(Set("publicApiKey1"), accountId)
        .returns(Future.successful(()))

    whenReady(service.addPermissionToAccount(1, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(1)) { roles =>
        roles should have size 1
      }
    }
  }

  test("should blow error while adding pii mask role") {
    val role = BusinessUserRoles.MASK_PII.id

    whenReady(service.addPermissionToAccount(1, role)) { response =>
      response shouldBe 'left
      response.fold(_.code shouldBe ExceptionCodes.NotFirstActivation.id, _ => fail)
    }
  }

  test("should add pii mask permission") {
    val role = BusinessUserRoles.MASK_PII.id

    whenReady(service.addPermissionToAccount(2, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(2)) { roles =>
        roles should have size 1
      }
    }
  }

  test("should blow error while adding DashboardV3 role") {
    val role = BusinessUserRoles.DashboardV3.id

    whenReady(service.addPermissionToAccount(1, role)) { response =>
      response shouldBe 'left
      response.fold(_.code shouldBe ExceptionCodes.InvalidAccountType.id, _ => fail)
    }
  }

  test("should add DashboardV3") {
    val role = BusinessUserRoles.DashboardV3.id

    whenReady(service.addPermissionToAccount(8, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(8)) { roles =>
        roles.contains(role)
      }
    }
  }

  test("should add DashboardV3 to parent and sub accounts") {
    val role = BusinessUserRoles.DashboardV3.id
    whenReady(service.removePermissionFromAccount(9, role)) { response =>
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.removePermissionFromAccount(14, role)) { response =>
        response.fold(_ => fail, _ shouldBe 1)
      }

      whenReady(service.fetchAccountPermissions(9)) { roles =>
        roles.contains(role) shouldBe false
      }

      whenReady(service.addPermissionToAccount(9, role)) { response =>
        response.fold(_ => fail, _ shouldBe 1)

        whenReady(service.fetchAccountPermissions(9)) { roles =>
          roles.contains(role)
        }
        whenReady(service.fetchAccountPermissions(14)) { roles =>
          roles.contains(role)
        }
      }
    }
  }

  test("should add permission, even if the parent does not have it - V2") {
    val role = BusinessUserRoles.MASK_PII.id
    whenReady(service.addPermissionToAccount(9, role)) { response =>
      response.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("should add SAML 2.0 role") {
    val role = BusinessUserRoles.SAML_2_0.id

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey5"), samlAccId)
      .returns(Future.successful(()))

    whenReady(service.addPermissionToAccount(samlAccId, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(samlAccId)) { roles =>
        roles should have size 1
      }
      whenReady(daoBusinessUser.getBusinessUserIdList(samlAccId))(_ shouldBe empty)
    }
  }

  test("should add SAML 2.0 role - V2") {
    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("privatekey-10","publicApiKey10"), 10)
      .returns(Future.successful(()))
    val role = BusinessUserRoles.SAML_2_0.id

    whenReady(service.addPermissionToAccount(10, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(10)) { roles =>
        roles should have size 2
      }
      whenReady(daoBusinessUser.getBusinessUserIdList(10))(_.nonEmpty shouldBe true)
    }
  }

  test("should fail adding roles when one user does not exist ") {

    whenReady(service.addPermissionToAccount(3, BusinessUserRoles.ADMIN.id)) { response =>
      response shouldBe 'left
      response.fold(_.code should be (AccountNotFound.id), _ => fail)
    }
  }

  test("should fail adding roles when the role does not exist ") {

    whenReady(service.addPermissionToAccount(3, -1)) { response =>
      response shouldBe 'left
      response.fold(_.code should be (UnknownRole.id), _ => fail)
    }
  }

  test("should remove the roles properly for a existing users") {
    val role = BusinessUserRoles.ADMIN.id

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey1"), accountId)
      .returns(Future.successful(()))

    whenReady(service.removePermissionFromAccount(1, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(1)) { roles =>
        roles shouldBe empty
      }
    }
  }

  test("should remove the Auto Monitoring Role properly for a existing users") {
    val role = BusinessUserRoles.WatchlistAutoMonitoring.id

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey11"), 11)
      .returns(Future.successful(()))

    whenReady(service.removePermissionFromAccount(11, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountPermissions(11)) { roles =>
        roles shouldBe empty
      }
    }
  }

  test("should fail removing roles when one user does not exist ") {

    whenReady(service.removePermissionFromAccount(3, BusinessUserRoles.ADMIN.id)) { response =>
      response shouldBe 'left
    }
  }

  test("should give permission Quarterly Cadence to account 8. and then giving  `Annual Cadence` will revoke 'Quarterly cadence' and provision 'Annual Cadence'") {

    whenReady(service.addPermissionToAccount(8, BusinessUserRoles.QuarterlyCadence.id)) { response =>
      response shouldBe 'right
      whenReady(service.fetchAccountPermissions(8)) { f8roles =>
        f8roles.contains(BusinessUserRoles.QuarterlyCadence.id) shouldBe true
          whenReady(service.addPermissionToAccount(8, BusinessUserRoles.AnnualCadence.id)) { response =>
            whenReady(service.fetchAccountPermissions(8)) { f8rroles =>
              f8rroles.contains(BusinessUserRoles.QuarterlyCadence.id) shouldBe false
              f8rroles.contains(BusinessUserRoles.AnnualCadence.id) shouldBe true
            }
          }
        }
      }


    }


  test("Should get Account permissions for Sub account  - V2 provisioned") {
    whenReady(service.getAccountPermissions(9)) { res =>
      res.fold(_ => fail, permissions => {
        val permissionsList = permissions.filter(_.provisioned).map(_.getId)
        permissionsList.contains(BusinessUserRoles.LEGACY_PHONE_RISK_ASSOCIATION.id) shouldBe false
        permissionsList.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) shouldBe true
      })
    }
  }

  test("Should not get WLM billing preference  for Sub account ") {
    whenReady(service.getAccountPermissions(9)) { res =>
      res.fold(_ => fail, permissions => {
         permissions.filter(_.id==BusinessUserRoles.QuarterlyCadence.id).length>0 shouldBe false
         permissions.filter(_.id==BusinessUserRoles.AnnualCadence.id).length>0 shouldBe false

      })
    }
  }

  test("Should get WLM billing preference for parent account ")  {
    whenReady(service.getAccountPermissions(1)) { res =>
      res.fold(_ => fail, permissions => {
        permissions.filter(_.id==BusinessUserRoles.QuarterlyCadence.id).length>0 shouldBe true
        permissions.filter(_.id==BusinessUserRoles.AnnualCadence.id).length>0 shouldBe true

      })
    }
  }

  test("Should remove a permission from the sub-accounts while removing a permission from v2 provisioned parent account") {
    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("privatekey-8", "privatekey-9", "publicApiKey8", "publicApiKey9"), 8)
      .returns(Future.successful(()))
    whenReady(service.removePermissionFromAccount(8, 74)) { res =>
      res.fold(_ => fail, _ => {
        whenReady(service.getAccountPermissions(9)) { res =>
          res.fold(_ => fail, permissions => {
            val permissionsList = permissions.filter(_.provisioned).map(_.getId)
            permissionsList.contains(74) shouldBe false
          })
        }})
    }
  }

  test("should update exisiting rolethat no entries were added when the role is already assigned to a user") {
    val role = BusinessUserRoles.ADMIN.id

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey1"), accountId)
      .returns(Future.successful(()))

    whenReady(service.addPermissionToAccount(1, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)
    }

    whenReady(service.addPermissionToAccount(1, role)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 0)
    }
  }

  test("should only remove selected roles") {

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey1"), accountId)
      .returns(Future.successful(()))
      .twice()

    whenReady(service.addPermissionToAccount(1, BusinessUserRoles.ADMIN.id)) { response =>
      response shouldBe 'right
    }

    whenReady(service.addPermissionToAccount(1, BusinessUserRoles.SUPER_ADMIN.id)) { response =>
      response shouldBe 'right
    }

    whenReady(service.removePermissionFromAccount(1, BusinessUserRoles.ADMIN.id)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)
    }
    whenReady(service.fetchAccountPermissions(1)) { roles =>
      roles should have size 1
      roles.headOption.fold(fail)(_ shouldBe BusinessUserRoles.SUPER_ADMIN.id)
    }
  }

  //TODO: Should be removed when super-admin adopts new account changes
  test("should consider as attribute and insert and remove") {
    whenReady(service.addPermissionToAccount(1, BusinessUserRoles.DEFAULT_SLA.id)) { res =>
      res should be ('right)
      whenReady(service.fetchAccountPermissions(1)) {res1 =>
        res1 should not contain BusinessUserRoles.DEFAULT_SLA.id
      }
      whenReady(service.fetchAccountAttributes(1)) { res2 =>
        res2.map(_.value).headOption.fold(fail)(_ should be (AccountAttributeValue.DEFAULT_SLA.id.toString))
      }
      whenReady(service.removePermissionFromAccount(1, BusinessUserRoles.DEFAULT_SLA.id)) { res =>
        res shouldBe 'right
      }
      whenReady(service.fetchAccountAttributes(1)) { res2 =>
        res2.size should be (0)
      }
    }
  }

  test("should not consider as attribute because its permission") {

    (accountInfoCacheInvalidator.invalidate _)
      .expects(Set("publicApiKey1"), accountId)
      .returns(Future.successful(()))

    whenReady(service.addPermissionToAccount(1, BusinessUserRoles.PERCEIVE.id)) { res =>
      res should be ('right)
      whenReady(service.fetchAccountAttributes(1)) { res2 =>
        res2.size shouldBe 0
      }
      whenReady(service.fetchAccountPermissions(1)) {res1 =>
        res1 should contain (BusinessUserRoles.PERCEIVE.id)
      }
    }
  }

  test("should add attribute for existing account") {
    val name = AccountAttributeName.SLA
    val value = AccountAttributeValue.HIGH_PERFORMANCE_SLA

    val response = service.upsertAttributeToAccount(1, name, value)
    whenReady(response) { res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountAttributes(1)) { roles =>
        roles should have size 1
      }
    }
  }

  test("should update attribute for existing account") {
    val response = service.upsertAttributeToAccount(1, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)
    whenReady(response){ res =>
      res shouldBe 'right
      whenReady(service.fetchAccountAttributes(1)) { att =>
        att.map(_.value).headOption.fold(fail)(_ should be (AccountAttributeValue.DEFAULT_SLA.id.toString))
      }
    }
  }

  test("should fail adding attribute to account when account not exist ") {
    whenReady(service.upsertAttributeToAccount(3, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) { response =>
      response shouldBe 'left
      response.fold(_.code should be (AccountNotFound.id), _ => fail)
    }
  }

  test("should remove the attribute from account") {
    whenReady(service.removeAttributeFromAccount(1, AccountAttributeName.SLA)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)

      whenReady(service.fetchAccountAttributes(1)) { atts =>
        atts shouldBe empty
      }
    }
  }

  test("should fail removing attribute when account does not exist ") {

    whenReady(service.removeAttributeFromAccount(3, AccountAttributeName.SLA)) { response =>
      response shouldBe 'left
    }
  }

  test("should only remove selected attributes") {
    whenReady(service.upsertAttributeToAccount(1, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) { response =>
      response shouldBe 'right
    }

    whenReady(service.fetchAccountAttributes(1)) { att =>
      att.size should be (1)
    }

    whenReady(service.removeAttributeFromAccount(1, AccountAttributeName.SLA)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("should not add DOC_VER_QR_CODE if the account is not an internal account") {
    whenReady(service.addPermissionToAccount(2, BusinessUserRoles.DOC_VER_QR_CODE.id)) { response =>
      response.fold(a => a.code shouldBe 182, _ => fail)
    }
  }

  test("should add DOC_VER_QR_CODE if the account is an internal account") {
    whenReady(service.addPermissionToAccount(1, BusinessUserRoles.DOC_VER_QR_CODE.id)) { response =>
      response shouldBe 'right
      whenReady(service.getAccountPermissions(1L)) { res =>
        val permissions = res.right.value
        permissions.contains(BusinessUserRolesWithPermissions(35, "Document Verification", provisioned = true)) shouldBe true
        permissions.contains(BusinessUserRolesWithPermissions(50, "Document verification decision logic - Lenient", provisioned = true)) shouldBe false
        permissions.contains(BusinessUserRolesWithPermissions(68, "Enable QR code for Document Verification (Only for Testing)", provisioned = true)) shouldBe true
      }
    }
  }

  test("should add DOC_VER_QR_CODE, but no change in the existing decision logic") {
    whenReady(service.addPermissionToAccount(7, BusinessUserRoles.DOC_VER_QR_CODE.id)) { response =>
      response shouldBe 'right
      whenReady(service.getAccountPermissions(7L)) { res =>
        val permissions = res.right.value.sortBy(_.id)
        permissions.contains(BusinessUserRolesWithPermissions(35, "Document Verification", provisioned = true)) shouldBe true
        permissions.contains(BusinessUserRolesWithPermissions(50, "Document verification decision logic - Lenient", provisioned = true)) shouldBe false
        permissions.contains(BusinessUserRolesWithPermissions(49, "Document verification decision logic - Strict", provisioned = true)) shouldBe false
        permissions.contains(BusinessUserRolesWithPermissions(68, "Enable QR code for Document Verification (Only for Testing)", provisioned = true)) shouldBe true
      }
    }
  }

  test("Should list all roles") {
    whenReady(service.listRoles()) {res =>
      res shouldBe 'right
      val actualList = res.right.value
      actualList.contains(BusinessUserRolesLess(25, "Sub Accounts")) shouldBe true
      actualList.contains(BusinessUserRolesLess(3, "Super Admin")) shouldBe false
      actualList.contains(BusinessUserRolesLess(40, "Social Media")) shouldBe true
      actualList.contains(BusinessUserRolesLess(46, "Triggers name vs email, phone, and address correlation scores")) shouldBe true
      actualList.contains(BusinessUserRolesLess(27, "Watchlist Endpoint")) shouldBe true
    }
  }

  test("Should list the account permissions for the account"){
    val buRoles = List(BusinessUserRolesWithPermissions(38,"KYC Test",provisioned = false), BusinessUserRolesWithPermissions(19,"SSN",provisioned = false), BusinessUserRolesWithPermissions(28,"Address Risk Score",false), BusinessUserRolesWithPermissions(44,"Customer User ID",false), BusinessUserRolesWithPermissions(20,"Perceive",false), BusinessUserRolesWithPermissions(24,"CIP Decision",false), BusinessUserRolesWithPermissions(40,"Social Media",false), BusinessUserRolesWithPermissions(33,"Fraud Score",false), BusinessUserRolesWithPermissions(3,"Super Admin",true), BusinessUserRolesWithPermissions(37,"ID+ 3.0",false), BusinessUserRolesWithPermissions(18,"Production",false), BusinessUserRolesWithPermissions(27,"Watchlist Endpoint",false), BusinessUserRolesWithPermissions(51,"ID+ 3.0 Legacy Association",false), BusinessUserRolesWithPermissions(25,"Sub Accounts",false), BusinessUserRolesWithPermissions(13,"KYC",false), BusinessUserRolesWithPermissions(39,"Blacklist Pool Match",false), BusinessUserRolesWithPermissions(31,"Limit dashboard access by ip/domain",false), BusinessUserRolesWithPermissions(9,"Debug",false), BusinessUserRolesWithPermissions(36,"Auth Score",false), BusinessUserRolesWithPermissions(1,"Admin",false), BusinessUserRolesWithPermissions(48,"AuthScore Settings",false), BusinessUserRolesWithPermissions(46,"Triggers name vs email, phone, and address correlation scores",false), BusinessUserRolesWithPermissions(26,"Batch Runs",false), BusinessUserRolesWithPermissions(49,"Document verification decision logic - Strict",false), BusinessUserRolesWithPermissions(30,"Phone Risk Score",false), BusinessUserRolesWithPermissions(35,"Document Verification",false), BusinessUserRolesWithPermissions(29,"Email Risk Score",false), BusinessUserRolesWithPermissions(22,"Watchlist",true), BusinessUserRolesWithPermissions(45,"2.5 customer can see two score correlations",false), BusinessUserRolesWithPermissions(47,"SAML 2.0",false), BusinessUserRolesWithPermissions(50,"Document verification decision logic - Lenient",provisioned = false))
    whenReady(service.addPermissionToAccount(4L, BusinessUserRoles.DEFAULT_SLA.id)) { _ =>
      whenReady(service.getAccountPermissions(4L)) { res =>
        val permissions = res.right.value
        permissions.contains(BusinessUserRolesWithPermissions(40, "Social Media", provisioned = false)) shouldBe true
        permissions.contains(BusinessUserRolesWithPermissions(19, "SSN", provisioned = false)) shouldBe true
        permissions.contains(BusinessUserRolesWithPermissions(1, "Admin", provisioned = false)) shouldBe false
      }
    }
  }

  test("Should list the parent account permissions for the Sub account"){
      whenReady(service.getAccountPermissions(6L)) { res =>
        val permissions = res.right.value
        permissions.contains(BusinessUserRolesWithPermissions(29,"Email Risk Score",provisioned = true)) shouldBe true
        permissions.contains(BusinessUserRolesWithPermissions(63,"US Only Access",provisioned = true)) shouldBe true
    }
  }

  test("Should return account permissions provisioning flag set to false for invalid account"){
    whenReady(service.getAccountPermissions(999L)) { res =>
      res shouldBe 'right
      val permissions = res.right.value
      permissions.exists(_.provisioned) shouldBe false
    }
  }

  test("should invalidate caches for both private & public keys") {
    val allApiKeys = Set(
      "prod-private-key-active",
      "prod-private-key-new",
      "prod-private-key-deprecated",
      "dev-private-key-active",
      "sandbox-private-key-new",
      "pub-cache-invalidation"
    )

    (accountInfoCacheInvalidator.invalidate _)
      .expects(allApiKeys, cacheInvalidationAcc)
      .returns(Future.successful(()))

    whenReady(service.addPermissionToAccount(cacheInvalidationAcc, BusinessUserRoles.ID_PLUS_3_0.id))(_ shouldBe Right(1))
  }

  test("should add High Performance SLA role and remove Extreme performance SLA") {
    testDoesNotHaveAttribute(accountId = 4L, AccountAttributeValue.EXTREME_PERFORMANCE_SLA.id)
    whenReady(service.upsertAttributeToAccount(accountId = 4L, AccountAttributeName.SLA, value = AccountAttributeValue.EXTREME_PERFORMANCE_SLA)) { _ =>
      testHasAttribute(accountId = 4L, attribute = AccountAttributeValue.EXTREME_PERFORMANCE_SLA.id)
      testAddAttribute(accountId = 4L, AccountAttributeValue.HIGH_PERFORMANCE_SLA.id)
    }
  }
  test("Should add permission to parent account") {
    whenReady(service.addPermissionToAccount(accountId = 8L,permission = BusinessUserRoles.KYC.id)){ res =>
      res.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("Should add AdministerSubAccount to partner accounts") {
    whenReady(service.addPermissionToAccount(accountId = 4L,permission = BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id)){ res =>
      res.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("Should fail to add AdministerSubAccount to sub accounts") {
    whenReady(service.addPermissionToAccount(accountId = 9L,permission = BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id)){ res =>
      res.fold(_ => fail, _ shouldBe 0)
    }
  }

  test("Should add feature flag to sub account, when parent does not have it") {
    whenReady(service.addPermissionToAccount(accountId = 9L,permission = BusinessUserRoles.LEGACY_3_0_ASSOCIATIONS.id)){ res =>
      res.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("Should fail to add module to sub account, when parent does not have it") {
    whenReady(service.addPermissionToAccount(accountId = 9L,permission = BusinessUserRoles.WATCHLIST_3_0.id)){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(ParentPermissionMissing), _ => fail)
    }
  }

  test("Should add module(Ecbsv) to sub account, when parent does not have it") {
    whenReady(service.addPermissionToAccount(accountId = 9L,permission = BusinessUserRoles.ECBSV.id)){ res =>
      res.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("Should fail to remove authentic id v2 for an account account") {
    whenReady(service.removePermissionFromAccount(accountId = 7L, permission = BusinessUserRoles.AUTHENTIC_ID_V2.id)) { res =>
      res.fold(f => {
        f.code shouldBe CannotRemoveAuthenticIDV2.id
        f.message shouldBe CannotRemoveAuthenticIDV2.description
      }, _ => fail)
    }
  }

  test("Should fail if BYOK permission is added directly to sub account - V1") {
    whenReady(service.addPermissionToAccount(accountId = 6L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(f => {
        f.code shouldBe InvalidParentAccount.id
        f.message shouldBe InvalidParentAccount.description
      }, _ => fail)
    }
  }

  test("Should fail if BYOK permission is added directly to sub account - V2") {
    whenReady(service.addPermissionToAccount(accountId = 9L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(InvalidParentAccount), _ => fail)
    }
  }

  test("Should add BYOK permission to parent and all of it's sub account if available - V1") {
    whenReady(service.addPermissionToAccount(accountId = 1L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(_ => fail, r => r shouldBe 1)
    }
  }

  test("Should add BYOK permission to parent and all of it's sub account - V1") {
    whenReady(service.addPermissionToAccount(accountId = 4L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(_ => fail, r => r shouldBe 2)
    }
  }

  test("Should add BYOK permission to parent and all of it's sub account - V2") {
    whenReady(service.addPermissionToAccount(accountId = 8L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(_ => fail, r => r shouldBe 3)
    }
  }

  test("Should not be able to remove BYOK permission from any account - V1") {
    whenReady(service.removePermissionFromAccount(accountId = 6L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(f => {
        f.code shouldBe cannotRemoveBYOKProvision.id
        f.message shouldBe cannotRemoveBYOKProvision.description
      }, _ => fail)
    }
  }

  test("Should not be able to remove BYOK permission from any account - V2") {
    whenReady(service.removePermissionFromAccount(accountId = 9L, permission = BusinessUserRoles.BYOK.id)) { res =>
      res.fold(f => {
        f.code shouldBe cannotRemoveBYOKProvision.id
        f.message shouldBe cannotRemoveBYOKProvision.description
      }, _ => fail)
    }
  }

  test("should not add WatchlistFPRReduction if the account is not WATCHLIST_3_0 enabled") {
    whenReady(service.addPermissionToAccount(7, BusinessUserRoles.WatchlistFPRReduction.id)) { response =>
      response.fold(a => a.code shouldBe 501, _ => fail)
    }
  }

  test("should add WatchlistFPRReduction if the account is WATCHLIST_3_0 enabled") {
    whenReady(service.addPermissionToAccount(100, BusinessUserRoles.WATCHLIST_3_0.id)) { response =>
      response.fold(_ => fail, _ => 1)
      whenReady(service.fetchAccountPermissions(100)){ response0 =>
        testHasPerm(100L, BusinessUserRoles.WatchlistFPRReduction.id)
      }
    }
  }

  test("should remove WatchlistFPRReduction if WATCHLIST_3_0 removed") {
    Mockito.when(subscriptionService.unsubscribe(100, 1)).thenReturn(Future.successful(Right(true)))
    whenReady(service.removePermissionFromAccount(100, BusinessUserRoles.WATCHLIST_3_0.id)) { response =>
      response.fold(_ => fail, r => {
        r shouldBe 1
        whenReady(service.fetchAccountPermissions(100)) { permissions =>
          permissions.contains(BusinessUserRoles.WatchlistFPRReduction.id) shouldBe false
        }
      })
    }
  }

  test("Environment toggle for eCBSV Testing")  {
    val accountId = 100
    val testData = List(
        (BusinessUserRoles.ECBSV.id,
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id),
          Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id),
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id, BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id)),
        (BusinessUserRoles.ECBSV_Sandbox.id,
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id),
          Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id),
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id, BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id)),
        (BusinessUserRoles.ECBSV_Certification.id,
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id, BusinessUserRoles.ECBSV_Certification.id),
          Set(BusinessUserRoles.ECBSV_Production.id),
          Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id)),
        (BusinessUserRoles.ECBSV_Production.id,
          Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id, BusinessUserRoles.ECBSV_Certification.id),
          Set.empty[Int],
          Set(BusinessUserRoles.ECBSV_Production.id))
      )
    testData.foreach(a =>
      whenReady(service.addPermissionToAccount(accountId = accountId, permission = a._1)){ res =>
        res.fold(_ => fail, _ => {
          whenReady(service.fetchAccountPermissions(accountId = accountId)) { p =>
            val permissions = p.toSet
            permissions.filter(a._2) shouldBe a._2
            permissions.filter(a._3) shouldBe Set.empty[Int]
          }
        })
      }
    )
    whenReady(service.addPermissionToAccount(accountId = 100, permission = BusinessUserRoles.ECBSV.id)) { res =>
      res.fold(_ => fail, _ => {
        testData.foreach(a =>
          whenReady(service.removePermissionFromAccount(accountId = accountId, permission = a._1)){ res =>
            res.fold(_ => fail, _ => {
              whenReady(service.fetchAccountPermissions(accountId = accountId)) { permissions =>
                permissions.filter(a._4).toSet shouldBe Set.empty[Int]
              }
            })
          }
        )
      })
    }
  }

  test("Environment toggle for MLA Testing")  {
    val accountId = 100
    val testData = List(
      (BusinessUserRoles.MLA.id,
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id),
        Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id),
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id, BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id)),
      (BusinessUserRoles.MLA_Sandbox.id,
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id),
        Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id),
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id, BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id)),
      (BusinessUserRoles.MLA_Certification.id,
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id, BusinessUserRoles.MLA_Certification.id),
        Set(BusinessUserRoles.MLA_Production.id),
        Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id)),
      (BusinessUserRoles.MLA_Production.id,
        Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id, BusinessUserRoles.MLA_Certification.id),
        Set.empty[Int],
        Set(BusinessUserRoles.MLA_Production.id))
    )
    testData.foreach(a =>
      whenReady(service.addPermissionToAccount(accountId = accountId, permission = a._1)){ res =>
        res.fold(_ => fail, _ => {
          whenReady(service.fetchAccountPermissions(accountId = accountId)) { p =>
            val permissions = p.toSet
            permissions.filter(a._2) shouldBe a._2
            permissions.filter(a._3) shouldBe Set.empty[Int]
          }
        })
      }
    )
    whenReady(service.addPermissionToAccount(accountId = 100, permission = BusinessUserRoles.MLA.id)) { res =>
      res.fold(_ => fail, _ => {
        testData.foreach(a =>
          whenReady(service.removePermissionFromAccount(accountId = accountId, permission = a._1)){ res =>
            res.fold(_ => fail, _ => {
              whenReady(service.fetchAccountPermissions(accountId = accountId)) { permissions =>
                permissions.filter(a._4).toSet shouldBe Set.empty[Int]
              }
            })
          }
        )
      })
    }
  }

  test("Prefill Modules")  {
    val accountId = 100
    val testData = List(
      (BusinessUserRoles.PrefillModule.id,
        Set(BusinessUserRoles.PrefillModule.id)),
      (BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id,
        Set(BusinessUserRoles.PrefillModule.id, BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id)),
      (BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id,
        Set(BusinessUserRoles.PrefillModule.id, BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id, BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id)),
      (BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id,
        Set(BusinessUserRoles.PrefillModule.id, BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id, BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id)),
      (BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id,
        Set(BusinessUserRoles.PrefillModule.id, BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id, BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id))
    )
    testData.foreach(a =>
      whenReady(service.addPermissionToAccount(accountId = accountId, permission = a._1)){ res =>
        res.fold(_ => fail, _ => {
          whenReady(service.fetchAccountPermissions(accountId = accountId)) { p =>
            val permissions = p.toSet
            permissions.filter(a._2) shouldBe a._2
          }
        })
      }
    )
    whenReady(service.addPermissionToAccount(accountId = 100, permission = BusinessUserRoles.PrefillModule.id)) { res =>
      res.fold(_ => fail, _ => {
        testData.foreach(a =>
          whenReady(service.removePermissionFromAccount(accountId = accountId, permission = a._1)){ res =>
            res.fold(_ => fail, _ => {
              whenReady(service.fetchAccountPermissions(accountId = accountId)) { permissions =>
                permissions.filter(a._2).toSet shouldBe Set.empty[Int]
              }
            })
          }
        )
      })
    }
  }

  test("KYC Modules")  {
    val accountId = 100
    val testData = List(
      (BusinessUserRoles.KYC.id,
        Set(BusinessUserRoles.KYC.id, BusinessUserRoles.BestMatchEntityViaDashboard.id),
        Set(BusinessUserRoles.KYCPLUS.id),
        Set(BusinessUserRoles.KYC.id, BusinessUserRoles.KYCPLUS.id, BusinessUserRoles.BestMatchEntityViaDashboard.id)),
      (BusinessUserRoles.KYCPLUS.id,
        Set(BusinessUserRoles.KYC.id, BusinessUserRoles.KYCPLUS.id),
        Set(BusinessUserRoles.BestMatchEntityViaDashboard.id),
        Set(BusinessUserRoles.KYCPLUS.id)),
      (BusinessUserRoles.BestMatchEntityViaDashboard.id,
        Set(BusinessUserRoles.KYC.id, BusinessUserRoles.BestMatchEntityViaDashboard.id),
        Set(BusinessUserRoles.KYCPLUS.id),
        Set(BusinessUserRoles.BestMatchEntityViaDashboard.id))
    )
    testData.foreach(a =>
      whenReady(service.addPermissionToAccount(accountId = accountId, permission = a._1)){ res =>
        res.fold(_ => fail, _ => {
          whenReady(service.fetchAccountPermissions(accountId = accountId)) { p =>
            val permissions = p.toSet
            permissions.filter(a._2) shouldBe a._2
          }
        })
      }
    )
    whenReady(service.addPermissionToAccount(accountId = 100, permission = BusinessUserRoles.MLA.id)) { res =>
      res.fold(_ => fail, _ => {
        testData.foreach(a =>
          whenReady(service.removePermissionFromAccount(accountId = accountId, permission = a._1)){ res =>
            res.fold(_ => fail, _ => {
              whenReady(service.fetchAccountPermissions(accountId = accountId)) { permissions =>
                permissions.filter(a._4).toSet shouldBe Set.empty[Int]
              }
            })
          }
        )
      })
    }
  }

  test("Switch Permissions should add/remove permissions for the given accounts") {
    val updatePermissionsF = List(
      (AccountPermissionUpdateRequest(1000, Set(1, 2, 3), true), ErrorResponseFactory.get(AccountNotFound), List()),
      (AccountPermissionUpdateRequest(10, Set(1001, 48, 3), true), ErrorResponseFactory.get(UnknownRole), List(47, 97)),
      (AccountPermissionUpdateRequest(7, Set(1001, 2, 3), true), ErrorResponseFactory.get(UnknownRole), List(35,49,68,79)),
      (AccountPermissionUpdateRequest(7, Set(1001, 2, 3), false), ErrorResponseFactory.get(UnknownRole), List(35,49,68,79))
    )

    val updatePermissionsS = List(
      (AccountPermissionUpdateRequest(7, Set(137, 136), true), true),
      (AccountPermissionUpdateRequest(1, Set(1, 2, 3), true), true),
      (AccountPermissionUpdateRequest(7, Set(136, 137), false), false)
    )

    updatePermissionsF.foreach(f = ups =>
        whenReady(service.switchPermissions(ups._1.accountId, ups._1.permissions, ups._1.switch)) { response =>
          response.fold(err => {
            err.code should be (ups._2.code)
            whenReady(service.fetchAccountPermissions(ups._1.accountId)) { response0 =>
              response0  shouldBe ups._3
            }
          }, _ => fail)
        }
      )

    updatePermissionsS.foreach(f = ups =>
        whenReady(service.switchPermissions(ups._1.accountId, ups._1.permissions, ups._1.switch)) { response =>
          response.fold(_ => fail, k => {
            whenReady(service.fetchAccountPermissions(ups._1.accountId)) { response0 =>
              ups._1.permissions.subsetOf(response0.toSet) shouldBe ups._2
            }
          })
        })
  }

  test("Validate fetch parent permissions") {
    val validPermissions = List(
      ("V2-Sub Account-L2", 14, 8),
      ("V2-Sub Account-L1", 9, 8),
      ("V2-Parent", 8, 8),
      ("V1-Sub Account", 13, 12),
      ("V1-Parent", 12, 12)
    )

    val invalidPermissions = List(
      ("InvalidAccount", 9999, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    )

    validPermissions.foreach { vp =>
      whenReady(service.getParentAccountPermissions(vp._2)) { response =>
        response.fold(_ => fail, { permissions =>
          whenReady(service.fetchAccountPermissions(vp._3)) { response0 =>
            val parentPermissions = response0.toSet
            permissions & parentPermissions shouldBe parentPermissions
          }})
      }
    }

    invalidPermissions.foreach { vp =>
      whenReady(service.getParentAccountPermissions(vp._2)) { response =>
        response.fold(_ shouldBe vp._3, _ => fail)
      }
    }
  }

  private def testAddPermission(accountId: Long, permission: Int): Unit = {
    whenReady(service.addPermissionToAccount(accountId = accountId, permission = permission)) { _ =>
      testHasPerm(accountId = accountId, perm = permission)
      whenReady(service.removePermissionFromAccount(accountId = accountId, permission = permission))(_ => ())
    }
  }

  private def testMutuallyExclusivePermissions(accountId: Long, firstPerms: Set[Int], secondPerm: Int): Unit = {
    firstPerms.foreach(firstPerm => testAddPermission(accountId = accountId, permission = firstPerm))
    testAddPermission(accountId = accountId, permission = secondPerm)
    firstPerms.foreach(firstPerm => testDoesNotHavePerm(accountId = accountId, perm = firstPerm))
  }

  private def testPerm(accountId: Long)(tester: Seq[Int] => Boolean): Unit = {
    whenReady(service.fetchAccountPermissions(accountId = accountId)) { permissions =>
      tester(permissions) shouldBe true
    }
  }

  private def testHasPerm(accountId: Long, perm: Int): Unit = {
    testPerm(accountId = accountId)(_.contains(perm))
  }

  private def testDoesNotHavePerm(accountId: Long, perm: Int): Unit = {
    testPerm(accountId = accountId)(!_.contains(perm))
  }

  private def testAddAttribute(accountId: Long, attribute: Int): Unit = {
    whenReady(service.upsertAttributeToAccount(accountId = accountId, name = AccountAttributeName.SLA, value = AccountAttributeValue(attribute))) { _ =>
      testHasAttribute(accountId = accountId, attribute = AccountAttributeValue(attribute).id)
      whenReady(service.removeAttributeFromAccount(accountId = accountId, AccountAttributeName.SLA))(_ => ())
    }
  }
  private def testDoesNotHaveAttribute(accountId: Long, attribute: Int): Unit = {
    testAttribute(accountId = accountId)(!_.contains(attribute))
  }

  private def testHasAttribute(accountId: Long, attribute: Int): Unit = {
    testAttribute(accountId = accountId)(_.contains(attribute))
  }

  private def testAttribute(accountId: Long)(tester: Seq[Int] => Boolean): Unit = {
    whenReady(service.fetchAccountAttributes(accountId = accountId)) { as =>
      val attribs = as.map{ a => AccountAttributeValue(a.value.toInt).id }
      tester(attribs) shouldBe true
      }
  }
}
