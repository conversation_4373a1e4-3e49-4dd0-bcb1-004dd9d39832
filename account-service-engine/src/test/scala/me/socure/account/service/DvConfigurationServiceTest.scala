package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount

import javax.sql.DataSource
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CouldNotSaveDvConfiguration, EnvironmentNotFound}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.{DvConfiguration, DvConfigurationValueGenerator}
import me.socure.model.dv.{DVConfigurationDetails, DVConfigurationsForAccount}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoDvConfiguration}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> Kumar
 */
class DvConfigurationServiceTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll {

  implicit val ex: ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2017-04-10").withZone(DateTimeZone.UTC).getMillis)

  var service: DvConfigurationService = _
  val mysqlService: MysqlService = MysqlService("dv-configuration-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxy = new DBProxyWithMetrics(db, dbSlave)
    val daoDvConfiguration = new DaoDvConfiguration(dbProxy, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxy, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxy, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    new DvConfigurationService(daoDvConfiguration, v2Validator,auditDetailsService)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO `tbl_industry` VALUES ('31-34','Manufacturing123', false),('51','Information', false);")
    sqlExecutor.execute("INSERT INTO `tbl_account` VALUES (526,'BOB','31-34',0,NULL,1,0,NULL,'acc-YRirvesnZq','G4j5ODOp8v','externalId1',0),(531,'TestDogTestAccount','51',0,NULL,0,0,NULL,'acc-fkho34nwRI','9JvWQQQkJt','externalId2',0);")
    sqlExecutor.execute("INSERT INTO `tbl_environment_type` VALUES (1,'Production'),(2,'Development')")
    sqlExecutor.execute("INSERT INTO `tbl_environment` VALUES "+
      "(1001,'8df896c1-13c9-457c-8b3b-a8734476e66c','j+Nf+LXxXbcEW2IkE5BJv5vW1WwAq5wWsec6YvcGbanHcLTvMu2LT5RazykpIP+XleofEFLKHBQW6RKdhDeYq5aGVdF8UhPoLYYrdbso9Z0=','dySUFhW0Bwvk+JmClspmxC/kmVkepNgSkF3G4kFQ5Uja290GggU5jj8J7UvJw0Jc0DcFgEyxEL1Rnr22KYmSPVybpAf5IdSMo+iXMtrIfIs=',NULL,526,1,'2018-01-17 19:55:10'), "+
      "(1002,'03b33556-bc6f-444a-a1dc-02a18063e2e4','XiKseobHpgrNMt0wMl0TQEBZdwAw+H3RByIuqOzXXHILqeqk8hJp9JqeivbcsGtvztylXtiZnApnixolZaMMHtPp0db9LlXXiEvNrt0G5Wo=','SM+NkPi4S8I5OOmMyaP0JSoty/2YKnvHBs54GeE5DafebdjR77k854xWPACqGs9rI1/E01iCU9KRA/3j8NAZ6GkxuizVY71LyV/oLB1le/w=',NULL,526,2,'2018-01-17 19:55:10'), "+
      "(1003,'r3b33556-bc6f-444a-a1dc-02a18063e2e4','ciKseobHpgrNMt0wMl0TQEBZdwAw+H3RByIuqOzXXHILqeqk8hJp9JqeivbcsGtvztylXtiZnApnixolZaMMHtPp0db9LlXXiEvNrt0G5Wo=','vM+NkPi4S8I5OOmMyaP0JSoty/2YKnvHBs54GeE5DafebdjR77k854xWPACqGs9rI1/E01iCU9KRA/3j8NAZ6GkxuizVY71LyV/oLB1le/w=',NULL,531,1,'2018-01-17 19:55:10'), "+
      "(1234,'c0c65da3-b554-4c8b-9b4f-d267ca2f89b0','K8EFlIt3JqI4l76PEORRw5EqwwC10IEb1DoxQ+UXuODkcvkixkn0vWxDOYi/SRPwREdLafVX3WHLndXxI4Uvq0KrOcPVq47L0VGD9gjUukU=','RQfKUW99B8cHbmSlCpbxVIPayZZsOzZnYciCsSFu0y2DAsVT3q3rnZScBt8dvJQZT+fXz20TBMZQTU3OtpPktQwgrE/ozpbwYnpI/pZyE8Y=',NULL,531,2,'2018-01-18 22:27:13');")
    sqlExecutor.execute("INSERT INTO `environment_dv_configuration` VALUES " +
      "(1,1234,1,'test1',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(2,1001,1,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(3,1001,2,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(4,1001,3,'test3',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(5,1002,1,'test',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(6,1002,2,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(7,1002,3,'test3',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL);")
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES" +
      "(10, 'accessTokenProd', 'secretKeyProd', 'accessTokenSecretProd', 'domain.com,192.1.3.45,prod.com', 526, 2, '2016-05-05 00:00:00')")

    service = buildService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Should be able to return dv configuration list") {
    val environmentId = 1234
    val expected = Map("Document Expiration Grace - Past" -> DvConfiguration(2,"0",0), "FTB Matching - Non Dates" -> DvConfiguration(4,"75",3), "Document Expiration Grace - Future" -> DvConfiguration(3,"0",0), "Strategy" -> DvConfiguration(8,"lenient",0), "IPVsExtractedMatchingNonDates" -> DvConfiguration(10,"70",4), "IPVsExtractedMatchingDates" -> DvConfiguration(9,"0",4), "Minimum Age" -> DvConfiguration(1,"test1",1), "FTB Matching - Dates" -> DvConfiguration(5,"12",4))
    whenReady(service.listDvConfigurationByEnvironment(environmentId, None)){ res =>
      val dvConfigurationResult = res.right.value
      dvConfigurationResult.nonEmpty shouldBe true
      dvConfigurationResult shouldBe expected
    }
  }

  test("Should be returning an empty sequence for no data") {
    val dvConfigurationResult = DvConfigurationValueGenerator.dvConfigurationDefaults()
    whenReady(service.listDvConfigurationByEnvironment(10, None)){ res =>
      res.right.value shouldBe dvConfigurationResult
    }
  }

  test("Should be able to save configuration data") {
    val environmentId = 1234
    val dvConfigurationResult = Seq(DvConfiguration(1, "test1", 1))
    whenReady(service.saveEnvironmentDvConfiguration(dvConfigurationResult, environmentId, None)) { res =>
      res._2.right.value shouldBe true
    }
  }

  test("Should be able to save configuration data failure") {
    val environmentId = 11
    val dvConfigurationResult = Seq(DvConfiguration(1, "test1", 1))
    whenReady(service.saveEnvironmentDvConfiguration(dvConfigurationResult, environmentId, None)) { res =>
      res._2.left.value shouldBe ErrorResponseFactory.get(EnvironmentNotFound)
    }
  }

  test("Should list configuration for an Account") {
    val accountId = 531
    val expected = Right(
      List(DVConfigurationsForAccount(2,List(DVConfigurationDetails("Minimum Age","test1","Review"),
        DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
        DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
        DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
        DVConfigurationDetails("FTB Matching - Dates","12","Accept"),
        DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
        DVConfigurationDetails("Strategy","lenient","NoAction"),
        DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"))),
        DVConfigurationsForAccount(1,List(DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"),
          DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
          DVConfigurationDetails("FirstNameMatchWithNickNameDB","0","Accept"),
          DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
          DVConfigurationDetails("Strategy","lenient","NoAction"),
          DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
          DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
          DVConfigurationDetails("Minimum Age","18","Reject"),
          DVConfigurationDetails("FTB Matching - Dates","12","Accept")))))

    whenReady(service.getDvConfigurationForAccount(accountId)) { res =>
     res.fold(_ => fail, _ => expected)
    }
  }

}
