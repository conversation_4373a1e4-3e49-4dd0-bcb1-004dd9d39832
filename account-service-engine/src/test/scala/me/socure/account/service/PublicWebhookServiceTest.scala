package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource

import javax.sql.DataSource
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.publicid.PublicId
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.storage.slick.dao.{DaoPublicWebhook, DaoSubscriptionStatus}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherV<PERSON>ues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class PublicWebhookServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  private implicit val patience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  System.setProperty("user.timezone", "GMT")

  private val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  private var service : PublicWebhookService = _

  val mysqlService: MysqlService = MysqlService("public-webhook-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoPublicWebhook = new DaoPublicWebhook(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionStatus = new DaoSubscriptionStatus(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new PublicWebhookService(
      daoPublicWebhook = daoPublicWebhook,
      daoSubscriptionStatus = daoSubscriptionStatus,
      clock = clock)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, 'publicId1','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, 'publicId2','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, 'publicId3','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, NULL, false, 'publicId4','publicApiKey4', 'externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, false, 'publicId5','publicApiKey5', 'externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, NULL, false, 'publicId6','publicApiKey6', 'externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, NULL, false, 'publicId7','publicApiKey7', 'externalId7'), " +
      s"(8, 'Duplicate Test1', '101-205', false, 1, 2, false, 'publicId8','publicApiKey8', 'externalId8'), " +
      s"(9, 'Duplicate Test2', '101-205', false, 1, 1, false, 'publicId9','publicApiKey9', 'externalId9')"
    )

    //environment
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES" +
      s"(1, 'production')," +
      s"(2, 'development')"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES " +
      s"(1, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 1, '2018-09-12')," +
      s"(2, 'token2', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 2, '2018-09-12')," +
      s"(3, 'token3', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 1, '2018-09-12')," +
      s"(4, 'token4', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 1, '2018-09-12')," +
      s"(5, 'token5', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 2, '2018-09-12')," +
      s"(6, 'token6', 'secret 1', 'secret token 1', '0.0.0.0/0', 7, 1, '2018-09-12')"
    )

    sqlExecutor.execute("INSERT INTO tbl_public_webhook(id, environment_id, endpoint, public_key_certificate, created_at, updated_at) VALUES" +
      s"(1, 1, 'localhost1', 'certificate 1', '2018-09-12', '2018-10-12')," +
      s"(2, 2, 'localhost1', 'certificate 2', '2017-09-12', '2017-10-12')," +
      s"(3, 3, 'localhost1', 'certificate 3', '2016-09-12', '2016-10-12')," +
      s"(4, 5, 'localhost1', 'dev certificate 3', '2016-09-12', '2016-10-12')," +
      s"(5, 6, 'localhost1', 'certificate 4', '2019-08-12', '2019-10-12')," +
      s"(6, 6, 'localhost1', 'certificate 5', '2014-02-12', '2018-11-12')"
    )

    sqlExecutor.execute("insert into subscription_status(id,environment_id,webhook_id,subscription_type,subscription_status,is_deleted) values" +
      s"(1,1,1,1,2,false),"+
      s"(2,2,2,1,2,false),"+
      s"(3,2,2,2,2,false),"+
      s"(5,3,3,2,2,false),"+
      s"(6,3,2,1,2,true),"+
      s"(4,1,1,2,2,false)," +
      s"(7,6,5,1,1,true)," +
      s"(8,6,6,1,1,false)"
    )

    sqlExecutor.execute("insert into tbl_account_permission(account_id, permission) values (1, 183)")
    service = buildService(socureDb)
  }

  override def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should list all public webhook"){
    whenReady(service.listPublicWebhooks){ res =>
      res shouldBe 'right
      res.right.value.size shouldBe 6
    }
  }

  test("should get public webhook"){
    whenReady(service.getPublicWebhook(environmentId = 1L, 1L)){ res =>
      res.fold(_ => fail, { s =>
        s.endpoint shouldBe "localhost1"
        s.publicKeyCertificate shouldBe "certificate 1"
        s.environmentId shouldBe 1
        s.subscriptionStatus shouldBe Some("suspend")
        s.subscriptionType shouldBe Some("Watchlist")
      })
    }
  }

  test("should get public webhook by account, environment type id and subscription type"){
    whenReady(service.getPublicWebhookByAccount(accountId = 1, envTypeId = 1, 1)) { res =>
      res.fold(_ => fail, { ss =>
        ss.webhook.foreach { s =>
          s.endpoint shouldBe "localhost1"
          s.publicKeyCertificate shouldBe "certificate 1"
          s.environmentId shouldBe 1
          s.subscriptionStatus shouldBe Some("suspend")
          s.subscriptionType shouldBe Some("Watchlist")
        }
        ss.publicAccountId shouldBe PublicId("publicId1")
        ss.provisioned.toLowerCase shouldBe "yes"
      })
    }
  }

  test("should get public webhook by account, environment type id and subscription type: But not provisioned"){
    whenReady(service.getPublicWebhookByAccount(accountId = 2, envTypeId = 1, 2)) { res =>
      res.fold(_ => fail, { ss =>
        ss.webhook.foreach { s =>
          s.endpoint shouldBe "localhost1"
          s.publicKeyCertificate shouldBe "certificate 3"
          s.environmentId shouldBe 3
          s.subscriptionStatus shouldBe Some("suspend")
          s.subscriptionType shouldBe Some("Document Verification")
        }
        ss.publicAccountId shouldBe PublicId("publicId2")
        ss.provisioned.toLowerCase shouldBe "no"
      })
    }
  }

  test("should fail to get public webhook by account, environment type id and invalid subscription type"){
    whenReady(service.getPublicWebhookByAccount(accountId = 1, envTypeId = 1, 11)) { res =>
      res.fold(_ => fail, { ss =>
        ss.webhook shouldBe None
        ss.publicAccountId shouldBe PublicId("publicId1")
        ss.provisioned.toLowerCase shouldBe "no"
      })
    }
  }

  test("should get parents public webhook for a sub account, environment type id") {
    whenReady(service.getPublicWebhookByAccount(accountId = 9, envTypeId = 1, 1)) { res =>
      println(res)
      res.fold(_ => fail, { ss =>
        ss.webhook shouldBe None
        ss.publicAccountId shouldBe PublicId("publicId9")
        ss.provisioned.toLowerCase shouldBe "yes"
      })
    }
  }

  test("should get parents public webhook for a sub account, environment type id, case when parent does have web hooks") {
    whenReady(service.getPublicWebhookByAccount(accountId = 8, envTypeId = 1, 1)) { res =>
      res.fold(_ => fail, { ss =>
        ss.webhook shouldBe None
        ss.publicAccountId shouldBe PublicId("publicId8")
        ss.provisioned.toLowerCase shouldBe "no"
      })
    }
  }

  test("should successfuly create a new entry") {
    whenReady(service.insertPublicWebhook(4, "localhost4", "certificate 4", 1)){ res =>
      res shouldBe 'right
      whenReady(service.getPublicWebhook(4, 1L)) { innerRes =>
        innerRes shouldBe 'right
        innerRes.right.value.endpoint shouldBe "localhost4"
        innerRes.right.value.publicKeyCertificate shouldBe "certificate 4"
        innerRes.right.value.environmentId shouldBe 4
      }
    }
  }

  test("should fail to create for an invalid environment") {
    whenReady(service.insertPublicWebhook(14, "localhost4", "certificate 4", 1)){ res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.UnableToCreatePublicWebhook.id
        f.message shouldBe ExceptionCodes.UnableToCreatePublicWebhook.description
      }, _ => fail)
    }
  }

  test("should fail to create for an invalid environment and invalid subscriptiontype") {
    whenReady(service.insertPublicWebhook(1, "localhost4", "certificate 4", 100)){ res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.UnableToCreatePublicWebhook.id
        f.message shouldBe ExceptionCodes.UnableToCreatePublicWebhook.description
      }, _ => fail)
    }
  }

  test("should successfuly update an existing entry") {
    whenReady(service.updatePublicWebhook(1L, "updatedEndpoint1", "updated certificate 1", 1L)){ res =>
      res shouldBe 'right
      whenReady(service.getPublicWebhook(1L, 1L)) { innerRes =>
        innerRes.fold(_ => fail, { response  =>
          response.endpoint shouldBe "updatedEndpoint1"
          response.publicKeyCertificate shouldBe "updated certificate 1"
          response.environmentId shouldBe 1
          response.subscriptionType shouldBe Some("Watchlist")
        })
      }
    }
  }

  test("should successfuly update an existing entry if it has a deleted webhook for the subscription type") {
    whenReady(service.updatePublicWebhook(6L, "updatedEndpoint1", "updated certificate 1", 1L)){ res =>
      res shouldBe 'right
      whenReady(service.getPublicWebhook(6L, 1L)) { innerRes =>
        innerRes.fold(_ => fail, { response  =>
          response.endpoint shouldBe "updatedEndpoint1"
          response.publicKeyCertificate shouldBe "updated certificate 1"
          response.environmentId shouldBe 6
          response.subscriptionType shouldBe Some("Watchlist")
        })
      }
    }
  }

  test("should fail to update an existing entry for invalid subscription type") {
    whenReady(service.updatePublicWebhook(1L, "updatedEndpoint1", "updated certificate 1", 100L)){ res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.NoPublicWebhookFound.id
        f.message shouldBe ExceptionCodes.NoPublicWebhookFound.description
      }, _ => fail)
    }
  }

  test("should get public webhooks by environment id"){
    whenReady(service.getPublicWebhooks(envId = 1, None)){ res =>
      res.fold(_ => fail, { s =>
        s.webhooks.nonEmpty shouldBe true
        s.webhooks.size shouldBe 2
        s.webhooks.exists(_.environmentId==1) shouldBe true
        s.publicAccountId shouldBe PublicId("publicId1")
      })
    }
  }

  test("should fail to get public webhooks by invalid  environment id"){
    whenReady(service.getPublicWebhooks(envId = 20, None)){ res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.NoPublicWebhookFound.id
        f.message shouldBe ExceptionCodes.NoPublicWebhookFound.description
      }, _ => fail)
    }
  }

  test("should fail to get public webhooks by environment id and invalid subscription type"){
    whenReady(service.getPublicWebhooks(envId = 2, Some(10))){ res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.NoPublicWebhookFound.id
        f.message shouldBe ExceptionCodes.NoPublicWebhookFound.description
      }, _ => fail)
    }
  }

  test("should get public webhooks by environment id and valid subscription type"){
    whenReady(service.getPublicWebhooks(envId = 2, Some(1))){ res =>
      res.fold(_ => fail, { s =>
        s.webhooks.nonEmpty shouldBe true
        s.webhooks.size shouldBe 1
        s.webhooks.exists(_.environmentId == 2) shouldBe true
        s.webhooks.exists(_.subscriptionType.contains("Watchlist")) shouldBe true
        s.publicAccountId shouldBe PublicId("publicId1")
      })
    }
  }

  test("should delete webhook - mark subscription status as deleted"){
    whenReady(service.deletePublicWebhook(environmentId = 1L, subscriptionType = 1L)) { res =>
      res.fold(_ => fail, { _ shouldBe true })
    }
  }

  test("should fail to delete webhook - invalid env id"){
    whenReady(service.deletePublicWebhook(environmentId = 100L, subscriptionType = 1L)) { res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.UnableToDeletePublicWebhook.id
        f.message shouldBe ExceptionCodes.UnableToDeletePublicWebhook.description
      }, _ => fail)
    }
  }

  test("should fail to delete webhook - invalid subscription type"){
    whenReady(service.deletePublicWebhook(environmentId = 1L, subscriptionType = 100L)) { res =>
      res.fold({f =>
        f.code shouldBe ExceptionCodes.UnableToDeletePublicWebhook.id
        f.message shouldBe ExceptionCodes.UnableToDeletePublicWebhook.description
      }, _ => fail)
    }
  }

}
