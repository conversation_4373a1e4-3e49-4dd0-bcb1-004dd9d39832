package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.constants.{DashboardUserPermissions, EnvironmentTypes, SystemDefinedRoles}
import me.socure.model.UsersAndRoles
import me.socure.model.account._
import me.socure.model.dashboardv2.Creator
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}


/**
 * <AUTHOR> Kumar
 */
class UserRoleServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: UserRoleService = _
  override val mysqlService: MysqlService = MysqlService("user-role-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildUserRoleService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get user role by id - success") {
    val userId = 1
    val accountId = 1
    val userRole = UserRole(Some(3), "PrimaryAdmin", Some("Primary Administrator"))
    whenReady(service.getUserRole(3, userId, accountId)) { response =>
      response shouldBe Right(userRole)
    }
  }

  test("Get user role by id - failure") {
    val userId = 1
    val accountId = 1
    whenReady(service.getUserRole(100, userId, accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId))
    }
  }

  test("Get user roles by account id - success") {
    val creator = Creator(1,1)
    whenReady(service.getUserRolesByAccountId(1, creator)) { response =>
      response shouldBe 'Right
    }
  }

  test("Get user roles by account id with system defined roles usage - success") {
    val creator = Creator(7,7)
    whenReady(service.getUserRolesByAccountId(7, creator)) { response =>
      response shouldBe 'Right
    }
  }

  test("Get system defined roles for root account") {
    val creator = Creator(1,1)
    whenReady(service.getUserRolesByAccountId(1, creator, true)) { response =>
      response shouldBe 'Right
      response.right.get.count(roles => roles.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType) shouldBe 1
    }
  }

  test("Get system defined roles for sub account when parent account has hide system defined role as false") {
    val creator = Creator(2,2)
    whenReady(service.getUserRolesByAccountId(2, creator, true)) { response =>
      response shouldBe 'Right
      response.right.get.count(roles => roles.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType) shouldBe 1
    }
  }

  test("Get user roles by account id - invalid role") {
    val creator = Creator(1,1)

    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    whenReady(service.getUserRolesByAccountId(1000, creator)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Insert user role - success") {
    val userRoleInput = UserRoleInput(None,"test",Some("test"), 2, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(2,2))
    val expected = true
    whenReady(service.insertUserRole(userRoleInput)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Insert user role - fail when role name too short") {
    val userRoleInput = UserRoleInput(None,"ab",Some("test"), 1, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)
    whenReady(service.updateUserRole(userRoleInput)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Insert user role - fail when role name is system defined role name") {
    val userRoleInput = UserRoleInput(None,"Account Owner",Some("test"), 1, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)
    whenReady(service.insertUserRole(userRoleInput)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Update user role - fail when role name is system defined role name") {
    val userRoleInput = UserRoleInput(None,"Analyst",Some("test"), 1, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)
    whenReady(service.updateUserRole(userRoleInput)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("get dashboard permissions by role type - success") {
    val userId = 1
    val accountId = 1
    val roleType = 2
    whenReady(service.getDashboardPermissionsByRoleTypeID(roleType, userId, accountId)) { response =>
      response shouldBe 'Right
    }
  }

  test("get dashboard permissions by role type id - permission failure") {
    val userId = 17
    val accountId = 26
    val roleType = 3
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    whenReady(service.getDashboardPermissionsByRoleTypeID(roleType, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("get dashboard permissions by role type id - roletype failure") {
    val userId = 1
    val accountId = 1
    val userRoleId = 0
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissions)
    whenReady(service.getDashboardPermissionsByRoleTypeID(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Insert user role - failure") {
    val userRoleInput = UserRoleInput(None,"test",Some("test"), 2, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(2,2))
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserRole)
    whenReady(service.insertUserRole(userRoleInput)) { response =>
      response shouldBe Left(expected)
    }
  }

  ignore("Update user role") {
    val userRoleInput = UserRoleInput(Some(5),"test1",Some("test1"), 1, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001), None)), Creator(1,1))
    val expected = true
    whenReady(service.updateUserRole(userRoleInput)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Update user role - fail when role name too short") {
    val userRoleInput = UserRoleInput(None,"ab",Some("test"), 1, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1031), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)
    whenReady(service.insertUserRole(userRoleInput)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Delete user role - success") {
    val roleId = 5
    val userId = 1
    val accountId = 1
    whenReady(service.deleteUserRole(roleId, userId, accountId)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Delete user role - failure") {
    val roleId = 100
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId)
    whenReady(service.deleteUserRole(roleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Get role permission template association by role id - success") {
    val userRoleId = 3
    val userId = 1
    val accountId = 1
    val expected = RolePermissionTemplateAssociation(1, userRoleId, userId, accountId)
    whenReady(service.getRolePermissionTemplateAssociation(userRoleId, userId, accountId)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Get role permission template association by role id - failure") {
    val userRoleId = 4
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissionTemplate)
    whenReady(service.getRolePermissionTemplateAssociation(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Insert role permission template association by role id - success") {
    val userId = 1
    val accountId = 1
    val rolePermissionTemplateAssociation = RolePermissionTemplateAssociation(1, 3, userId, accountId)
    whenReady(service.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Insert role permission template association by role id - failure") {
    val userId = 1
    val accountId = 1
    val rolePermissionTemplateAssociation = RolePermissionTemplateAssociation(1, 10, userId, accountId)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId)
    whenReady(service.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Update role permission template association by role id - success") {
    val userId = 1
    val accountId = 1
    val rolePermissionTemplateAssociation = RolePermissionTemplateAssociation(1, 4, userId, accountId)
    whenReady(service.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)) { response =>
      response shouldBe Right(1)
    }
  }

  test("get dashboard permissions by role id - success") {
    val userId = 1
    val accountId = 1
    val userRoleId = 3
    val productionEnvPermissions: Seq[DashboardUserPermissionResult] = Seq(1001,1002,1003,1004,1011,1012,1013,1014,1026,1027,1028,1029,1030,1031)
      .flatMap(permissionId => {
        DashboardUserPermissions.byId(permissionId).map(permission => {
          DashboardUserPermissions.toPermissionResult(permission, EnvironmentTypes.PRODUCTION_ENVIRONMENT.id)
        })
      })
    val globalEnvPermissions: Seq[DashboardUserPermissionResult] = Seq(1031,1005,1001,2002,2011,1032,1006,1028,1002,1035,2001,1007,1029,1034,1003,2012,1008,2009,1030,2008,1033,1004)
      .flatMap(permissionId => {
        DashboardUserPermissions.byId(permissionId).map(permission => {
          DashboardUserPermissions.toPermissionResult(permission, EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
        })
      })
    val expected = (productionEnvPermissions ++ globalEnvPermissions).sortBy(p => (p.environmentType, p.id))
    whenReady(service.getDashboardPermissionsByRoleId(userRoleId, userId, accountId)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("get dashboard permissions by role id - invalid user role") {
    val userId = 1
    val accountId = 1
    val userRoleId = 100
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId)
    whenReady(service.getDashboardPermissionsByRoleId(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Delete role permission template association - success") {
    val userId = 1
    val accountId = 1
    val userRoleId = 3
    whenReady(service.deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Delete role permission template association - failure") {
    val userRoleId = 3
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    whenReady(service.deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("get dashboard permissions by role id - failure") {
    val userId = 1
    val accountId = 1
    val userRoleId = 3
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    whenReady(service.getDashboardPermissionsByRoleId(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  ignore("delete role and its permissions - success") {
    val userId = 3
    val accountId = 2
    val userRoleId = 6
    whenReady(service.deleteUserRoleWithPermissionTemplate(userRoleId, userId, accountId)) { response =>
      response shouldBe Right(true)
    }
  }

  test("delete role and its permissions - invalid role id failure") {
    val userId = 3
    val accountId = 2
    val userRoleId = 2
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId)
    whenReady(service.deleteUserRoleWithPermissionTemplate(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("delete role and its permissions - role does not belong to account failure") {
    val userId = 3
    val accountId = 2
    val userRoleId = 4
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    whenReady(service.deleteUserRoleWithPermissionTemplate(userRoleId, userId, accountId)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("Get roles by public account id - failure") {

    whenReady(service.getUserRolesByPublicAccountId("publicAccountId")) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPublicAccountId))
    }
  }

  test("get users and roles total record count- success") {
    val accountIds = Array[Long](1919)
    val expected = 1
    whenReady(service.getUsersAndRolesTotalRecordCount(accountIds, None)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("get users and roles total record count- failure") {
    val accountIds = Array[Long]()
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)
    whenReady(service.getUsersAndRolesTotalRecordCount(accountIds, None)) { response =>
      response shouldBe Left(expected)
    }
  }

  test("get users and roles - success") {
    val accountIds = Array[Long](1919)
    val expected = Seq(
      UsersAndRoles(
        accountId = 1919,
        userId = 1717,
        userAssociationId = 1818,
        accountName = "test",
        firstName = "Primary",
        lastName = "Account",
        userEmail = "<EMAIL>",
        contactNumber = "**********",
        userRoles = "Account Owner, Administrator, Role 1, Role 2",
        userAssociationStatus = 1
      )
    )
    whenReady(service.getUsersAndRoles(accountIds, None, None, None)) { response =>
      response.fold(_ => fail, r => r.toList shouldBe expected)
    }
  }

  test("get users and roles - failure") {
    val accountIds = Array[Long]()
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)
    whenReady(service.getUsersAndRoles(accountIds, None, None, None)) { response =>
      response shouldBe Left(expected)
    }
  }
}