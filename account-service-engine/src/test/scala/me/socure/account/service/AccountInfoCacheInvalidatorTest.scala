package me.socure.account.service

import me.socure.account.service.common.{AccInfoCacheKeyProvider, CacheKeyProvider}
import me.socure.common.random.Random
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}
import scalacache.{Cache, ScalaCache}

import scala.concurrent.{ExecutionContext, Future}

class AccountInfoCacheInvalidatorTest extends FreeSpec with Matchers with MockFactory with ScalaFutures {

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(5, Seconds),
    interval = Span(50, Milliseconds)
  )

  private val accInfoCacheKeyProvider = mock[AccInfoCacheKeyProvider]
  private val cacheKeyProvider = mock[CacheKeyProvider]
  private val cache = mock[Cache[Int]]
  private val topPrefix = "top_prefix:"
  private val innerPrefix = "inner_prefix:"
  private val scalaCache = ScalaCache(cache)
  private val invalidator = new AccountInfoCacheInvalidator(
    accInfoCacheKeyProvider = accInfoCacheKeyProvider,
    cacheKeyProvider = cacheKeyProvider,
    scalaCache = scalaCache
  )

  "should invalidate cache properly" in {
    val apiKeys = List.fill(5 + Random.nextInt(5))(Random.uuids()).toSet
    apiKeys.foreach { apiKey =>
      val withInnerPrefix = innerPrefix + apiKey
      val cacheKey = topPrefix + withInnerPrefix
      (accInfoCacheKeyProvider.provide _).expects(apiKey).returns(withInnerPrefix)
      (cacheKeyProvider.provide _).expects(withInnerPrefix).returns(cacheKey)
      (cache.remove _).expects(cacheKey).returns(Future.successful(()))
    }

    (accInfoCacheKeyProvider.getAccountPermissionsKey _).expects(0).returns("withInnerPrefix")
    (cache.remove _).expects("withInnerPrefix").returns(Future.successful(()))

    (accInfoCacheKeyProvider.keyForGetAccountDetailsById _).expects(0).returns("keyForGetAccountDetailsById")
    (cache.remove _).expects("keyForGetAccountDetailsById").returns(Future.successful(()))

    whenReady(invalidator.invalidate(apiKeys, 0))(_ shouldBe())
  }
}
