package me.socure.account.service

import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.constants.EnvironmentTypes.EnvironmentType
import me.socure.model.account.{AccountIdName, Action, DashboardUserPermissionResult, Domain, Subscription, UserAccountAssociation, UserAccountAssociationInput, UserAssociationInput, UserAssociationResponse}
import me.socure.model.user.authorization.{Account, EnvironmentSettings, User, UserAuth}
import me.socure.model.user.{DashboardEnvironment, DashboardEnvironmentV2, DashboardUserRole}
import org.joda.time.DateTime
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{BeforeAndAfterAll, EitherVal<PERSON>, FunSuite, OptionValues, Matchers => MMatchers}

/**
 * <AUTHOR> Kumar
 */
class UserAccountAssociationServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: UserAccountAssociationService = _
  var dashboardUserServiceV2: DashboardUserServiceV2 = _
  override val mysqlService: MysqlService = MysqlService("user-account-association-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildUserAccountAssociationService(socureDb)
    dashboardUserServiceV2 = buildDashboardUserServiceV2(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get User Account Associations by User Id and Account ID- Success") {
    whenReady(service.getUserAccountAssociation(3, 2)) { response =>
      response.fold(_ => fail, r0 => {
        r0.map { r =>
          r.id shouldBe 4
          r.userId shouldBe 3
          r.accountId shouldBe 2
          r.status shouldBe 1
          r.userRoles.map { roles =>
            roles.contains(3) shouldBe true
            roles.contains(5) shouldBe false
          }
        }
      })
    }
  }

  test("Get User Account Associations by User Id - Success") {
    whenReady(service.getUserAccountAssociationsByUserId(1)) { response =>
      val userAccountAssociation = UserAccountAssociation(1, 1, 1, 1, isPrimaryUser = true, Some(Set(3)), None)
      response shouldBe Right(Seq(userAccountAssociation))
    }
  }

  test("Get User Account Associations by User Id - No data") {
    whenReady(service.getUserAccountAssociationsByUserId(100)) { response =>
      response shouldBe Right(Seq.empty)
    }
  }

  test("Get User Account Associations by Email - Success") {
    whenReady(service.getUserAccountAssociationsByEmail("<EMAIL>")) { response =>
      val userAccountAssociation = UserAccountAssociation(1, 1, 1, 1, isPrimaryUser = true, Some(Set(3)), None)
      response shouldBe Right(Seq(userAccountAssociation))
    }
  }

  test("Get User Account Associations by Email - No data") {
    whenReady(service.getUserAccountAssociationsByEmail("<EMAIL>")) { response =>
      response shouldBe Right(Seq.empty)
    }
  }


  test("Insert user account association - success") {
    val userAccountAssociationInput = UserAccountAssociationInput(None, 5, 2, Some(Set(3, 4)), None, 1)
    whenReady(service.insertUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Insert user account association - failure") {
    val userAccountAssociationInput = UserAccountAssociationInput(None, 1, 1, Some(Set(1)), None, 1)
    whenReady(service.insertUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation))
    }
  }

  test("Update user account association - success") {
    val userAccountAssociationInput = UserAccountAssociationInput(Some(4), 3, 2, Some(Set(3)), Some(Set(4)), 1)
    whenReady(service.updateUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Update user account association - failure") {
    val userAccountAssociationInput = UserAccountAssociationInput(None, 1, 1, Some(Set(1)), None, 1)
    whenReady(service.updateUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserAccountAssociation))
    }
  }

  test("delete user account association - success") {
    val userId = 2
    val accountId = 2
    val updatedBy = 2
    val userAccountAssociationInput = UserAccountAssociationInput(id = None, userId=userId, accountId=accountId, userRoles = Some(Set(3, 4)), revoked = None, updatedBy = updatedBy)
    whenReady(service.deleteUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Right(1)
    }
  }

  test("delete user account association - failure") {
    val userId = 1
    val accountId = 1
    val updatedBy = 1
    val userAccountAssociationInput = UserAccountAssociationInput(id = None, userId=userId, accountId=accountId, userRoles = None, revoked = None, updatedBy = updatedBy)
    whenReady(service.deleteUserAccountAssociation(userAccountAssociationInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserAccountAssociation))
    }
  }

  test("should be able to validate user account association - failure") {
    val accountId = 1
    val userId = 5
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
    }
  }

  test("should be able to validate user account association - success") {
    val accountId = 1
    val userId = 1
    val result = UserAuth(User(1,"Sunder","Raj","<EMAIL>",false,true),
      Account(1,"AccountName1",Set(184, 29, 1, 97, 2, 95, 183, 94),true,false, Set(Subscription(1,"Watchlist"), Subscription(2,"Document Verification")), accountType=Some(1)),
      Vector(AccountIdName(1,"AccountName1")),
      Set(DashboardEnvironment(1,"Production",Set(DashboardUserRole.ACCOUNTS,DashboardUserRole.BATCH_JOB,DashboardUserRole.SETTINGS)),
        DashboardEnvironment(0,"Global",Set(DashboardUserRole.ACCOUNTS,DashboardUserRole.USERS, DashboardUserRole.FILE_UPLOAD,
          DashboardUserRole.DOCUMENTATION))),
      List(EnvironmentSettings(1,"Production","88-16ca6193-4149-456b-ae00-00fdad2437c6"), EnvironmentSettings(3,"Development","816ca6193-4149-456b-ae00-00fdad2437c6"), EnvironmentSettings(2,"Development","916ca6193-4149-456b-ae00-00fdad2437c6")),None,Some(Set(DashboardEnvironmentV2(1,"Production",Set(DashboardUserPermissionResult(1030,None,"TEMPLATES_VIEW",Domain(10,"Templates",1),Action(3,"View")), DashboardUserPermissionResult(1026,None,"SETTINGS_MODIFY",Domain(9,"Settings",2),Action(2,"Modify")), DashboardUserPermissionResult(1004,None,"ACCOUNTS_DELETE",Domain(1,"Accounts",1),Action(4,"Delete")), DashboardUserPermissionResult(1002,None,"ACCOUNTS_MODIFY",Domain(1,"Accounts",1),Action(2,"Modify")), DashboardUserPermissionResult(1031,None,"TEMPLATES_DELETE",Domain(10,"Templates",1),Action(4,"Delete")), DashboardUserPermissionResult(1003,None,"ACCOUNTS_VIEW",Domain(1,"Accounts",1),Action(3,"View")), DashboardUserPermissionResult(1027,None,"SETTINGS_VIEW",Domain(9,"Settings",2),Action(3,"View")), DashboardUserPermissionResult(1011,None,"BATCHJOB_CREATE",Domain(4,"BatchJob",1),Action(1,"Create")), DashboardUserPermissionResult(1028,None,"TEMPLATES_CREATE",Domain(10,"Templates",1),Action(1,"Create")), DashboardUserPermissionResult(1029,None,"TEMPLATES_MODIFY",Domain(10,"Templates",1),Action(2,"Modify")), DashboardUserPermissionResult(1014,None,"BATCHJOB_DELETE",Domain(4,"BatchJob",1),Action(4,"Delete")), DashboardUserPermissionResult(1013,None,"BATCHJOB_VIEW",Domain(4,"BatchJob",1),Action(3,"View")), DashboardUserPermissionResult(1012,None,"BATCHJOB_MODIFY",Domain(4,"BatchJob",1),Action(2,"Modify")), DashboardUserPermissionResult(1001,None,"ACCOUNTS_CREATE",Domain(1,"Accounts",1),Action(1,"Create")))), DashboardEnvironmentV2(0,"Global",Set(DashboardUserPermissionResult(1030,None,"TEMPLATES_VIEW",Domain(10,"Templates",1),Action(3,"View")), DashboardUserPermissionResult(1032,None,"USER_ROLES_CREATE",Domain(11,"UserRoles",1),Action(1,"Create")), DashboardUserPermissionResult(1004,None,"ACCOUNTS_DELETE",Domain(1,"Accounts",1),Action(4,"Delete")), DashboardUserPermissionResult(1033,None,"USER_ROLES_MODIFY",Domain(11,"UserRoles",1),Action(2,"Modify")), DashboardUserPermissionResult(1034,None,"USER_ROLES_VIEW",Domain(11,"UserRoles",1),Action(3,"View")), DashboardUserPermissionResult(1002,None,"ACCOUNTS_MODIFY",Domain(1,"Accounts",1),Action(2,"Modify")), DashboardUserPermissionResult(2009,None,"USER_ROLES_PROVISION",Domain(11,"UserRoles",1),Action(5,"Provision")), DashboardUserPermissionResult(2001,None,"ACCOUNTS_PROVISION",Domain(1,"Accounts",1),Action(5,"Provision")), DashboardUserPermissionResult(1006,None,"USERS_MODIFY",Domain(2,"Users",1),Action(2,"Modify")), DashboardUserPermissionResult(1031,None,"TEMPLATES_DELETE",Domain(10,"Templates",1),Action(4,"Delete")), DashboardUserPermissionResult(1003,None,"ACCOUNTS_VIEW",Domain(1,"Accounts",1),Action(3,"View")), DashboardUserPermissionResult(1005,None,"USERS_CREATE",Domain(2,"Users",1),Action(1,"Create")), DashboardUserPermissionResult(1035,None,"USER_ROLES_DELETE",Domain(11,"UserRoles",1),Action(4,"Delete")), DashboardUserPermissionResult(2011,None,"DOCUMENTATION_PROVISION",Domain(3,"Documentation",1),Action(5,"Provision")), DashboardUserPermissionResult(2012,None,"FILE_UPLOAD_PROVISION",Domain(5,"FileUploads",1),Action(5,"Provision")), DashboardUserPermissionResult(1007,None,"USERS_VIEW",Domain(2,"Users",1),Action(3,"View")), DashboardUserPermissionResult(1028,None,"TEMPLATES_CREATE",Domain(10,"Templates",1),Action(1,"Create")), DashboardUserPermissionResult(1029,None,"TEMPLATES_MODIFY",Domain(10,"Templates",1),Action(2,"Modify")), DashboardUserPermissionResult(1008,None,"USERS_DELETE",Domain(2,"Users",1),Action(4,"Delete")), DashboardUserPermissionResult(2002,None,"USERS_PROVISION",Domain(2,"Users",1),Action(5,"Provision")), DashboardUserPermissionResult(2008,None,"TEMPLATES_PROVISION",Domain(10,"Templates",1),Action(5,"Provision")), DashboardUserPermissionResult(1001,None,"ACCOUNTS_CREATE",Domain(1,"Accounts",1),Action(1,"Create")))))),None,DateTime.now())
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe 'right
      val data = resp.right.value
      data.redirectUrl shouldBe result.redirectUrl
      data.environment.length shouldBe result.environment.length
      data.dashboardEnvPermissionsV2.size shouldBe result.dashboardEnvPermissionsV2.size
      data.accountUIConfiguration shouldBe result.accountUIConfiguration
      data.accounts.length shouldBe result.accounts.length
      data.dashboardEnvRoles.size shouldBe result.dashboardEnvRoles.size
      data.account shouldBe result.account
    }
  }

  test("should be able to validate user account association account owner case - success") {
    val accountId = 8
    val userId = 6
    //User id 6 is not directly associated with account id 8 but user6 has sys defined role 1(Account Owner)
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe 'right
    }
  }

  test("should fail to validate user account association account owner case - success") {
    //User id 7 is not directly associated with account id 8 and user7 has only sys defined role 3(Analyst)
    val accountId = 8
    val userId = 7
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe 'left
    }
  }

  test("should fail to validate user account association account owner case reseller - administer success") {
    //User id 17 is not directly associated with account id 19 and user17 has sys defined role 1(Account Owner) and second level parent account 17 has administer enabled
    val accountId = 19
    val userId = 17
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe 'right
      val data = resp.right.value
      data.accounts.length shouldBe 4
      data.account.name shouldBe "AccountName19"
      data.user.email shouldBe "<EMAIL>"
    }
  }

  test("should fail to validate user account association account owner case reseller - administer failure") {
    //User id 17 is not directly associated with account id 20 and user17 has sys defined role 1(Account Owner) but second level parent account 18 does not have administer enabled
    val accountId = 20
    val userId = 17
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
    }
  }

  test("should fail to validate user account association account owner case reseller - failure") {
    //User id 2 is not directly associated with account id 18 and user17 has no sys defined role 1(Account Owner)
    val accountId = 18
    val userId = 2
    whenReady(service.validateUserAccountAssociation(userId, accountId)) { resp =>
      resp shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
    }
  }

  test("should be able to insert user account associations - success") {
    val accountId = 1
    val userAssociationInputSeq = Seq(UserAssociationInput(id = None, userId = 4, userRoles = Some(Set(3)), revoked = None))
    val updatedBy = 2
    whenReady(service.insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)) { resp =>
      resp shouldBe Seq(UserAssociationResponse("ok","true"))
    }
  }

  test("should be able to insert user account associations - failure") {
    val accountId = 1
    val userAssociationInputSeq = Seq(UserAssociationInput(id = None, userId = 1, userRoles = Some(Set(3)), revoked = None))
    val updatedBy = 1
    whenReady(service.insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)) { resp =>
      resp shouldBe Seq(UserAssociationResponse("error","Unable to insert user account association"))
    }
  }
}