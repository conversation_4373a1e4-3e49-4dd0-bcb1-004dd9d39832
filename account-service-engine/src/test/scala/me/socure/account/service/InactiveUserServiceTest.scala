package me.socure.account.service

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.{AccountInfoService, InactiveUserService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.BusinessUserRoles
import me.socure.model.superadmin.UserActivationDetails
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.driver.MySQLDriver
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext


/**
  * Created by sunderraj on 6/7/16.
  */
class InactiveUserServiceTest extends FunSuite with Matchers with ScalaFutures with BeforeAndAfterAll with EitherValues {

  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)


  var service : InactiveUserService = _
  var daoBusinessUser : DaoBusinessUser = _
  val mysqlService: MysqlService = MysqlService("account-service-tests")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 0, 1, false, NULL, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 0, 1, false, '2017-05-05 00:00:00', '${UserFixture.primaryUser.publicAccountId}','publicApiKey5','externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 0, NULL, false, '2017-05-05 00:00:00', '${PublicIdGenerator.account().value}','publicApiKey6','externalId6') "
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive1', 'User1', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 2, true), " +
      "(3, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive2', 'User2', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 3, true), " +
      "(4, 'q23-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive4', 'User4', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 4, true), " +
      "(5, 'q23-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive4', 'User4', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 6, true)"
    )

    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (1, '49a11f5245eefff691abdd71f42c109a', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (2, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, current_timestamp)")

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 1), " +
      "(4, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com',2, 2), " +
      "(5, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 3, 1), " +
      "(6, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com',3, 2), " +
      "(7, 'subaccesstoken_sand', 'subsecretkey_sand', 'subaccesstokensecret_sand', 'subdevelopment.com',4, 1), " +
      "(8, 'subaccesstoken_sand', 'subsecretkey_sand', 'subaccesstokensecret_sand', 'subdevelopment.com',4, 2), " +
      "(9, 'subaccesstoken_sand2', 'subsecretkey_sand2', 'subaccesstokensecret_sand2', 'subdevelopment.com2',5, 1), " +
      "(10, 'subaccesstoken_sand2', 'subsecretkey_sand2', 'subaccesstokensecret_sand2', 'subdevelopment.com2',5, 2)"
    )

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(3, 3, '716ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(4, 5, '716ca6193-4149-456b-ae00-00fdad', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(5, 7, 'apikey7', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(6, 8, 'apikey8', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(7, 9, 'apikey9', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(8, 10, 'apikey10', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22')"
    )

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1), (NULL, 1, 8)")

    //Account Permission
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, 22), (6, ${BusinessUserRoles.SAML_2_0.id})")

    //Account Attribute
    sqlExecutor.execute("INSERT INTO tbl_account_attribute(account_id, name, value) VALUES(1, 'SLA', '1')")

    sqlExecutor.execute("INSERT INTO tbl_activation_token (id, business_user_id, token, created_at) VALUES(0, 1, 'valid_token', current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_reset_token (id, business_user_id, token, created_at) VALUES(0, 1, 'valid_token', current_timestamp)")

    service = buildInactiveUserService(socureDb)

  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  def buildInactiveUserService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val accountInfoService = new AccountInfoService(new DaoAccount(dbProxyWithMetrics, MySQLDriver), v2Validator = v2Validator)
    val samlValidator = new SamlValidator(accountInfoService = accountInfoService, daoBusinessUser = daoBusinessUser, daoAccountV2 = daoAccountV2, v2Validator = v2Validator)

    new InactiveUserService(daoBusinessUser, samlValidator, clock)
  }

  test("inactive users list should return only primary inactive admin") {
    val user = UserFixture.inactivePrimaryUser
    val users = service.getInactivesPrimaryAccountAdmins
    whenReady(users) { response =>
      response.size should be (4)
      response.minBy(_.accountId).accountId should be (2)
      response.minBy(_.accountId).accountId should be (2)
      response.minBy(_.accountId).optUser.map(_.email).getOrElse("") should be ("<EMAIL>")
      response.minBy(_.accountId).optUser.map(_.firstName).getOrElse("") should be ("Inactive1")
      response.minBy(_.accountId).optUser.map(_.lastName).getOrElse("") should be ("User1")
      response.minBy(_.accountId).isEncryptionEnabled should be (false)

      response.find(_.accountId == 4).fold(fail){ account4 =>
        account4.parentAccountId should be (1)
        account4.isPiiMaskEligible should be (true)
        account4.optUser.map(_.id).getOrElse(-1) should be (4)
      }

      response.find(_.accountId == 5).fold(fail){ account5 =>
        account5.isPiiMaskEligible should be (false)
        account5.parentAccountId should be (1)
        account5.optUser.map(_.id).getOrElse(0) should be (0)
        account5.publicAccountId shouldBe UserFixture.primaryUser.publicAccountId
      }
    }
  }

  test("get business user to send activation link") {
    val result = service.getActivationLink(List("<EMAIL>"))
    whenReady(result) {res =>
      res.fold(_ => fail, _.size should be (1))
      res.fold(_ => fail, _.headOption.fold(fail)(_.getFirstname shouldBe "Inactive1"))
      res.fold(_ => fail, v => {
        validateUserActivatioDetails(v)
        v.headOption.fold(fail)(_.status shouldBe 0)
      })
    }
  }

  test("get business user to send activation link for multiple emails") {
    val result = service.getActivationLink(List("<EMAIL>", "<EMAIL>"))
    whenReady(result) {res =>
      res.fold(_ => fail, v => {
        v.size should be (2)
        validateUserActivatioDetails(v)
        v.headOption.fold(fail)(_.status shouldBe 0)
        v.last.status shouldBe 0
      })
    }
  }

  test("get business user to send activation link and status should be active") {
    val result = service.getActivationLink(List("<EMAIL>"))
    whenReady(result) {res =>
      res.fold(_ => fail, v => {
        v.size should be (1)
        v.headOption.fold(fail)(_.getFirstname shouldBe "Gopal")
      })
    }
  }

  test("get business user to send activation link should return not found") {
    val result = service.getActivationLink(List("<EMAIL>"))
    whenReady(result) {res =>
      res.fold(_.code should be (BusinessUserNotFound.id), _ => fail)
    }
  }

  test("get business user to send activation link for empty email list") {
    val result = service.getActivationLink(List.empty)
    whenReady(result) {res =>
      res.fold(_.code should be (BusinessUserNotFound.id), _ => fail)
    }
  }

  test("should fail to send activation link when one or more of the accounts associated with the emails has SAML 2.0 enabled") {
    val emails = Set("<EMAIL>", "<EMAIL>")
    val result = service.getActivationLink(emails.toList)
    whenReady(result) {res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts))
    }
  }

  private def validateUserActivatioDetails(userActivations : List[UserActivationDetails]) = {
    userActivations map { userActivation =>
      userActivation.activationCode should not be empty
      userActivation.email should not be empty
      userActivation.firstname should not be empty
      userActivation.surname should not be empty
    }
  }

}
