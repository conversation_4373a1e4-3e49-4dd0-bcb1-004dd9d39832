package me.socure.account.service

import me.socure.account.dashboard.DashboardUserService
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.{MySQLServerAwait, SQLExecutor}
import me.socure.model.user.UserInfoWithAccountId
import me.socure.user.fixure.UserFixture
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

/**
  * Created by sun<PERSON><PERSON> on 6/22/16.
  */
class DashboardUserServiceTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestDataSourceConfig {
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : DashboardUserService = _
  override val mysqlService: MysqlService = MysqlService("dashboard-user-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildDashboardUserService(socureDb)
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should return account Id") {
    val result = service.getAccountIdByEmail("<EMAIL>")
    whenReady(result){res =>
      res.right.value should be (1)
    }
  }

  test("get accountId should return account not found") {
    val result = service.getAccountIdByEmail("<EMAIL>")
    whenReady(result){res =>
      res.left.value.code shouldBe BusinessUserNotFound.id
    }
  }

  test("should return user info with account id Id") {
    val result = service.getUserInfoWithAccountIdByEmail("<EMAIL>")
    whenReady(result){res =>
      res.right.value should be (UserInfoWithAccountId(1,"Sunder","Raj","<EMAIL>",1))
    }
  }

  test("get userinfo with account id  should return account not found") {
    val result = service.getAccountIdByEmail("<EMAIL>")
    whenReady(result){res =>
      res.left.value.code shouldBe BusinessUserNotFound.id
    }
  }

  test("should return active access credentials"){
    val result = service.getAccountCrdentialsByApiKey("99-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(result){ res =>
      res.right.value should be (UserFixture.accessCredsEnv4)
    }
  }

  test("should return access credentials for the new api key"){
    val result = service.getAccountCrdentialsByApiKey("88-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(result){ res =>
      res.right.value should be (UserFixture.accessCredsEnv)
    }
  }

  test("should not return access credentials"){
    val result = service.getAccountCrdentialsByApiKey("invalid_socure_apikey")
    whenReady(result) { res =>
      res.left.value.code shouldBe AccountNotFound.id
    }
  }

  test("should returnt dashbaord user"){
    val result = service.findUserByEmail("<EMAIL>")
    whenReady(result) {res =>
      res.fold(_ => fail, r => {

      })
    }
  }

  test("should return business user not found"){
    val result = service.findUserByEmail("<EMAIL>")
    whenReady(result) {res =>
      res.left.value.code should be (BusinessUserNotFound.id)
    }
  }

  test("should return user tos") {
    val result = service.checkToSAgreementByUser("<EMAIL>")
    whenReady(result) { res =>
      res.right.value should be (false)
    }
  }


  test("should return business user tos not found") {
    val result = service.checkToSAgreementByUser("<EMAIL>")
    whenReady(result) { res =>
      res.left.value.code should be(BusinessUserNotFound.id)
    }
  }

  test("find sub accounts for this guy") {
    val result = service.findSubAccountsForAccountId(12)
    whenReady(result){ res =>
      res.size should be (2)
      res.headOption.fold(fail)(_.getName should be ("AccountName12"))
      res.last.getName should be ("AccountName13")
    }
  }


}
