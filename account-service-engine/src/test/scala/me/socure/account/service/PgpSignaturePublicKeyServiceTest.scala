package me.socure.account.service

import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.util.Base64
import javax.sql.DataSource
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.model.{DecryptRequest, DecryptResult, EncryptRequest, EncryptResult}
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.kms.KmsService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.encryption.{KmsId, KmsIdsConfig}
import me.socure.model.pgp.PgpPublicKey
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.storage.slick.dao.DaoPgpSignaturePublicKeys
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source

/**
  * Created by angayarkanni on 12/6/17.
  */
class PgpSignaturePublicKeyServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec = ExecutionContext.Implicits.global
  implicit val patience = PatienceConfig(timeout = Span(15, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  val kmsService = mock[KmsService]
  val kmsKey = KmsIdsConfig(Map(Regions.US_EAST_1 -> KmsId("valid-kms-key")))
  var service : PgpSignaturePublicKeyService = _
  val mysqlService: MysqlService = MysqlService("pgp-signature-public-key-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoPgpSignaturePublicKeys = new DaoPgpSignaturePublicKeys(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new PgpSignaturePublicKeyService(daoPgpSignaturePublicKeys, daoAccount, kmsService = kmsService, kmsKey = kmsKey, clock = clock)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key,external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, 2, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5')"
    )

    //Pgp Signature
    sqlExecutor.execute("INSERT INTO tbl_pgp_signature_public_key (id, account_id, publicKey, deleted, created_at) VALUES " +
      "(1, 1, 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 0, '2017-12-06'), " +
      "(2, 4, 'publicKey', 0, '2017-12-06')"
    )
    service = buildService(socureDb)

  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should list all pgp signature public keys"){
    whenReady(service.getAccountsWithSignaturePublicKeys()){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _.size shouldBe 2)
    }
  }

  test("should not insert pgp signature public key for an account which already has it"){
    whenReady(service.insertPgpSignaturePublicKey(accountId = 1, "publicKey")){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.PGPSignaturePublicKeyExists.id, _ => fail)
    }
  }

  test("should not insert pgp signature public key with invalid account id"){
    whenReady(service.insertPgpSignaturePublicKey(accountId = 22, "publicKey")){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.ParentAccountNotFound.id, _ => fail)
    }
  }

  test("should not insert pgp signature public key with valid account id but not a parent account"){
    whenReady(service.insertPgpSignaturePublicKey(accountId = 3, "publicKey")){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.ParentAccountNotFound.id, _ => fail)
    }
  }

  test("should not insert pgp signature public key with invalid pgp public key"){
    val publicKey = Base64.getEncoder.encode("SomeText".getBytes(StandardCharsets.UTF_8))
    val signature = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap(publicKey))

    Mockito.when(kmsService.encrypt(signature)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("encryptedSignature".getBytes))})
    whenReady(service.insertPgpSignaturePublicKey(accountId = 2, new String(publicKey))){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.InvlidPGPSignaturePublicKey.id, _ => fail)
    }
  }

  test("should insert pgp signature public key with valid data"){
    val publicKey = Source.fromInputStream(getClass.getResourceAsStream("/signing_public_key.asc")).mkString
    val encodedPublicKey = Base64.getEncoder.encode(publicKey.getBytes(StandardCharsets.UTF_8))
    val signature = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap(publicKey.getBytes(StandardCharsets.UTF_8)))

    Mockito.when(kmsService.encrypt(signature)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("encryptedSignature".getBytes))})
    whenReady(service.insertPgpSignaturePublicKey(accountId = 2, new String(encodedPublicKey))){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should insert pgp signature public key with valid data for an inactive account"){
    val publicKey = Source.fromInputStream(getClass.getResourceAsStream("/signing_public_key.asc")).mkString
    val encodedPublicKey = Base64.getEncoder.encode(publicKey.getBytes(StandardCharsets.UTF_8))
    val signature = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap(publicKey.getBytes(StandardCharsets.UTF_8)))

    Mockito.when(kmsService.encrypt(signature)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("encryptedSignature".getBytes))})
    whenReady(service.insertPgpSignaturePublicKey(accountId = 5, new String(encodedPublicKey))){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should get pgp signature public key for an account"){
    val decryptReq = new DecryptRequest().withCiphertextBlob(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))
    Mockito.when(kmsService.decrypt(decryptReq)) thenReturn Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))})
    whenReady(service.getPgpSignaturePublicKey(accountId = 1)){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe PgpPublicKey("cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="))
    }
  }

  test("should throw error - pgp signature public key for an invalid account"){
    whenReady(service.getPgpSignaturePublicKey(accountId = 99)){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.NoPGPSignaturePublicKeyFound.id, _ => fail)
    }
  }

  test("should throw error when delete pgp signature public key for an invalid account id"){
    whenReady(service.deletePgpPublicKey(accountId = 33)){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.UnknownError.id, _ => fail)
    }
  }

  test("should mark pgp signature public key as deleted"){
    whenReady(service.deletePgpPublicKey(accountId = 1)){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }
}
