package me.socure.account.service

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone, LocalDateTime}
import org.mockito.Mockito
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}


class DaoBusinessUserTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val mysqlService: MysqlService = MysqlService("dao-business-user")
  private val dbName = "socure"

  private val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  private val encryptionKeysService = Mockito.mock(classOf[EncryptionKeysService])

  var daoBusinessUser: DaoBusinessUser = _

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildDao(dataSource: DataSource): Unit = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      "(5, 'AccountName5', '101-205', false, 1, 1, false, 'public-id','publicApiKey5', 'external_id5')"
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false), " +
      "(3, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false)," +
      "(5, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 1, false), " +
      "(6, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 2, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'guest14', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 5, false)"
    )

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 1), " +
      "(4, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 2, 2), " +
      "(5, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 3, 2), " +
      "(6, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 3, 2), " +
      "(7, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 4, 1), " +
      "(8, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 4, 2)"
    )

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES " +
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(2, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), " +
      "(3, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(4, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(5, 2, 'a-916ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 3, '816ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 4, '716ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1), (1, 5, 4), (1, 5, 6), (2, 5, 5), (2, 5, 7), (NULL, 6, 8)")

    //Insert into bad login attempt
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES(1, 5, '2015-10-20', '" + LocalDateTime.now() + "', '2015-10-20')")
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES(5, 5, '2015-10-20', '" + LocalDateTime.now() + "', '2015-10-20')")
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES(6, 5, '2015-10-20', '" + LocalDateTime.now() + "', '2015-10-20')")

    //Insert into bad login try
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_try(business_user_id,error_description,login_time) VALUES(1, 'error', '2015-10-20')")
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_try(business_user_id,error_description,login_time) VALUES(5, 'error', '2015-10-20')")
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_try(business_user_id,error_description,login_time) VALUES(6, 'error', '2015-10-20')")

    sqlExecutor.execute("INSERT INTO tbl_activation_token VALUES (0, 1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '2017-03-06 20:11:22'), " +
      "(0, 3, 'c7741e94-be93-414d-95be-56a015157917', '2017-03-06 20:11:22')," +
      "(0, 6, 'c7741e94-be93-414d-95be-56a015157917', '2017-03-06 20:11:22');")

    buildDao(socureDb)

  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("register user when email is in lower/upper case") {
    val bUser  = new DtoBusinessUser(8, "<EMAIL>", "John", "Doe", Some("a6a07d36-769c-43bb-aa29-853e46f61daa"), "*********",null,null, new DateTime(), true, null,Some(false), 1, true);
    whenReady(daoBusinessUser.saveUser(bUser))(_ shouldBe (bUser))
    whenReady(daoBusinessUser.getUser("<EMAIL>"))(_ match {
      case Some(user) => user.email shouldEqual("<EMAIL>")
    })
  }

  test("should get account id by email") {
    whenReady(daoBusinessUser.getAccountIdByEmail("<EMAIL>"))(_ shouldBe Some(2))
  }

  test("get user account even though email id is in caps") {
    whenReady(daoBusinessUser.getAccountIdByEmail("<EMAIL>"))(_ shouldBe Some(1))
  }

  test("get user account even though email id is in lower/upper case") {
    whenReady(daoBusinessUser.getAccountIdByEmail("<EMAIL>"))(_ shouldBe Some(1))
  }

  test("get user id even though email id is in lower/upper case") {
    whenReady(daoBusinessUser.getAccountIdByEmail("<EMAIL>"))(_ shouldBe Some(2))
  }

  test("get user even though email id is in lower/upper case") {
    whenReady(daoBusinessUser.getUser("<EMAIL>"))(_ match {
      case Some(a: DtoBusinessUser) => a.email should equal("<EMAIL>")
      case None => fail()
    })
  }

  test("get user even though email ids is in lower/upper case") {
    val emailList = List("<EMAIL>","<EMAIL>")
    whenReady(daoBusinessUser.getUser(emailList))(_.length shouldBe 2)
  }

  test("get user with account when email ids is in lower/upper case") {
    val emailList = List("<EMAIL>","<EMAIL>")
    whenReady(daoBusinessUser.getUserWithAccount(emailList))(_.length shouldBe 2)
  }

  test("check if user exists when email ids is in lower/upper case") {
    whenReady(daoBusinessUser.doesUserExist("<EMAIL>"))(_ shouldBe (true))
  }

  test("activate users when email ids is in lower/upper case") {
    val emailList = List("<EMAIL>")
    whenReady(daoBusinessUser.activateUsers(2, "<EMAIL>"))(_.length shouldBe 1)
    whenReady(daoBusinessUser.getUserWithAccount(emailList))(_.map {
      case (_, account, _) => account.isActive shouldBe (true)
    })
  }


  test("should cleanup business users related to an account") {
    whenReady(daoBusinessUser.cleanUpUsersForAccount(1)) { affectedBusinessUsersCount =>
      affectedBusinessUsersCount shouldBe 5
      whenReady(daoBusinessUser.getBusinessUserIdList(1))(_ shouldBe empty)
      whenReady(daoBusinessUser.getBusinessUserIdList(2))(_ shouldBe Vector(6))
    }
  }

  test("should fail public account id by email") {
    whenReady(daoBusinessUser.getPublicAccountIdByUserName("<EMAIL>"))(_ shouldBe None)
  }

  test("should get public account id by email") {
    whenReady(daoBusinessUser.getPublicAccountIdByUserName("<EMAIL>"))(_ shouldBe Some("public-id"))
  }
}

