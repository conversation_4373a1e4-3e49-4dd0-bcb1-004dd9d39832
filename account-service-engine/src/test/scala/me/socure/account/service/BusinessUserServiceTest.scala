package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService}
import me.socure.account.dashboard.DashboardUserService
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.{AccountInfoService, InactiveUserService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.{FakeClock, RealClock}
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.random.Random
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.BadLoginConfig
import me.socure.constants.{AccountTypes, DashboardUserPermissions, EnvironmentConstants, SystemDefinedRoles}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account.{AccountIdName, RateLimiterPublicAPI}
import me.socure.model.dashboardv2.Creator
import me.socure.model.encryption.AccountId
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.ratelimiter.RateLimitingConfig
import me.socure.model.user.authorization.UserStatus
import me.socure.model.user.{BusinessUserWithRoles, ParentAccountPrimaryUserForm, UserCredential}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.salt.client.SaltClient
import me.socure.salt.model.{Salt, SaltValueGenerator}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser, DtoBusinessUserInfo}
import me.socure.user.MagicLinkAuditService
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.format.DateTimeFormat
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.{ArgumentCaptor, Mockito, Matchers => MMatchers}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import java.util.concurrent.TimeUnit
import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}


class BusinessUserServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MockitoSugar with MemcachedTestSupport {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  val logger: Logger = LoggerFactory.getLogger(this.getClass)
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(55, Seconds), interval = Span(500, Millis))
  private val dbDateFormat = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSS")

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  val encryptionKeysService: EncryptionKeysService = Mockito.mock(classOf[EncryptionKeysService])

  var daoBusinessUser: DaoBusinessUser = _
  var daoAccount: DaoAccount = _
  var samlValidator: SamlValidator = _
  var service: BusinessUserService = _
  var accountHierarchyService: AccountHierarchyService = _
  var accountAssociationHistoryService: AccountAssociationHistoryService = _
  var permissionTemplateService: PermissionTemplateService = _
  var userRoleService: UserRoleService = _
  var userAccountAssociationService: UserAccountAssociationService = _
  var inactiveService: InactiveUserService = _
  var dashbaordService: DashboardUserService = _
  var rateLimitingService: RateLimitingService = _
  var businessUserRoleService: BusinessUserRoleService = _
  private var sqlExecutor: SQLExecutor = _
  val mysqlService: MysqlService = MysqlService("business-user-service")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "business-user-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  val modelManagementClient: ModelManagementClient = mock[ModelManagementClient]
  val accountAutomationService = mock[AccountAutomationService]
  val mailNotificationService = mock[MailNotificationService]
  val saltClient: SaltClient = mock[SaltClient]
  val aSalt: Salt = SaltValueGenerator.aSalt()
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)

    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser: DaoBusinessUser,
      daoAccount: DaoAccount,
      daoAccountV2: DaoAccountV2)
    samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val config = BadLoginConfig(2, true, FiniteDuration(5, TimeUnit.SECONDS))
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val rateLimitingService = new RateLimitingService(daoRateLimit, scalaCache)
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserService(daoBusinessUser,
      daoAccount,
      daoEnvironment,
      config,
      passwordService,
      encryptionKeysService,
      samlValidator,
      clock,
      daoPublicApiKey,
      daoSubscriptions,
      daoAccountV2,
      daoRateLimit,
      pbeEncryptor,
      rateLimitingService,
      daoAccountUIConfiguration,
      modelManagementClient,
      businessUserCommonService,
      v2Validator,
      magicLinkAuditService,
      accountAutomationService,
      mailNotificationService,
      accountBundleAssociationService,
      sessionIdleTimeout = 480,
      daoProspect,
      whitelistedEmailDomain = Set("socure.com"))
  }

  private def buildInactiveService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    new InactiveUserService(daoBusinessUser, samlValidator, clock)
  }

  private def buildAccountHeirarchyService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
  }

  private def buildAccountAssociationHistory(dataSource: DataSource): AccountAssociationHistoryService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    new AccountAssociationHistoryService(new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver))
  }

  private def buildPermissionTemplate(dataSource: DataSource): PermissionTemplateService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new PermissionTemplateService(daoAccountV2, v2Validator ,clock)
  }

  private def buildUserAccountAssociation(dataSource: DataSource): UserAccountAssociationService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    new UserAccountAssociationService(new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver), daoAccount, daoBusinessUser, daoSubscriptions, v2Validator, daoUIAccountConfiguration, passwordService)
  }

  private def buildUserRoleService(dataSource: DataSource): UserRoleService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new UserRoleService(daoAccount,new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver), v2Validator, daoAccountUIConfiguration)
  }

  private def buildDashboardService(dataSource: DataSource) = {

    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)

    new DashboardUserService(daoAccount, daoBusinessUser, v2Validator)
  }

  private def buildRateLimitingService(dataSource: ComboPooledDataSource): RateLimitingService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new RateLimitingService(daoRateLimit = daoRateLimit, scalaCache)
  }

  private def buildBusinessUserRoleService(dataSource: ComboPooledDataSource): BusinessUserRoleService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val subscriptionService: SubscriptionService = Mockito.mock(classOf[SubscriptionService])
    class MockableAccountInfoCacheInvalidator extends AccountInfoCacheInvalidator(null, null, null)
    val accountInfoCacheInvalidator = mock[MockableAccountInfoCacheInvalidator]
    daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoCaWatchlistPreference = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserRoleService(dbProxyWithMetrics, slick.driver.MySQLDriver, daoBusinessUser, daoAccountV2, accountInfoCacheInvalidator, subscriptionService, daoAccount, daoCaWatchlistPreference, v2Validator, scalaCache)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()


    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${UserFixture.primaryUser.publicAccountId}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5')," +
      s"(6, 'AccountName6', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6'), " +
      s"(7, 'AccountName7', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey7','externalId7'), " +
      s"(8, 'AccountName8', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey8','externalId8'), " +
      s"(9, 'AccountName9', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey9','externalId9'), " +
      s"(10, 'AccountName10', '101-205', false, 1, 9, false, '${PublicIdGenerator.account().value}','publicApiKey10','externalId10')"
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false), " +
      "(3, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false)," +
      "(5, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 1, false), " +
      "(6, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 2, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'guest2', 'user2', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 5, true)," +
      "(8, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Internal', 'User1', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 6, true), " +
      "(9, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Internal', 'InternalUser1', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 6, false)," +
      "(10, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Internal', 'InternalUser2', NULL,'2020-10-28', '2020-10-28', 0, NULL, NULL, 7, false)," +
      "(11, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Internal', 'InternalUser3', NULL,'2020-10-28', '2020-10-28', 0, NULL, NULL, 8, false), " +
      "(12, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Vignesh', 'V', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 9, false), " +
      "(13, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, false), " +
      "(14, 'c7641e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, false), " +
      "(15, 'c7541e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 9, false), " +
      "(16, 'c7441e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 9, false)"
    )
    //Business User Info
    sqlExecutor.execute("INSERT INTO business_user_info (id, business_user_id, address_line_1, address_line_2, job_title, business_website, created_at, last_updated_at) VALUES " +
      "(1,1,'Nt Street, Washington DC','','SDE','socure.com','2024-04-10','2024-04-10')")

    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (1, '49a11f5245eefff691abdd71f42c109a', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (5, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (12, '413b97545c01f11a4f3be2d081e44dd5', 'active', 'v1', current_timestamp, current_timestamp)")

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 1), " +
      "(4, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 2, 2), " +
      "(5, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 3, 2), " +
      "(6, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 3, 2), " +
      "(7, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 4, 1), " +
      "(8, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 4, 2), " +
      "(9, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 5, 2), " +
      "(10, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'prod.com', 5, 1), "+
      "(11, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 6, 2), " +
      "(12, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'prod.com', 6, 1), " +
      "(13, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'prod.com', 7, 1), " +
      "(14, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 8, 2)"
    )

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES " +
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(2, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), " +
      "(3, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(4, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(5, 2, 'a-916ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(6, 3, '816ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(7, 4, '716ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(8, 5, '616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1), (1, 5, 4), (1, 5, 6), (2, 5, 5), (2, 5, 7), (NULL, 1, 8), (11, 8, 5), (12, 8, 7), (12, 8, 8), (1, 12, 4), (1, 12, 6), (2, 12, 5), (2, 12, 7)")

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, 22), (5,47)")

    //Account Attribute
    sqlExecutor.execute("INSERT INTO tbl_account_attribute(account_id, name, value) VALUES(1, 'SLA', '1')")

    //Insert into bad login attempt
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES(5, 5, '2015-10-20', '" + dbDateFormat.print(new RealClock().now()) + "', '2015-10-20')")

    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, ${BusinessUserRoles.ALLOW_SUBACCOUNTS.id})")
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, ${BusinessUserRoles.ALLOW_BATCHRUNS.id})")
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, 10000)")

    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(9, ${BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id})")
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(9, ${BusinessUserRoles.SAML_2_0.id})")
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES(9, ${BusinessUserRoles.NEW_SAML.id})")

    sqlExecutor.execute("INSERT INTO tbl_activation_token VALUES (0, 1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '2017-03-06 20:11:22'), " +
      "(0, 3, 'c7741e94-be93-414d-95be-56a015157917', '2017-03-06 20:11:22');")

    sqlExecutor.execute("INSERT INTO tbl_reset_token VALUES (1, 10, '********-cc7f-46f7-b889-bedb0ce793f8', '2020-10-28 17:30:00'), " +
      "(2, 11, '5e91ea88-ace4-4319-bf4f-f58a597403a4', '2020-10-28 17:30:00');")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES" +
      "(NULL, 1, 1), " +
      "(1, 1, 6), " +
      "(1, 1, 5), " +
      "(5, 2, 4), " +
      "(5, 3, 5), " +
      "(5, 3, 6), " +
      "(6, 3, 7), " +
      "(6, 3, 4), " +
      "(6, 3, 13), " +
      "(6, 3, 14), " +
      "(4, 2, 5), " +
      "(4, 3, 5)")

    // User Role
    sqlExecutor.execute("INSERT INTO `user_role`(name, description, by_business_user_id, by_account_id) VALUES ('PrimaryAdmin','Primary Administrator',12,9),('InstanceAdmin','Instance Administrator',12,9);")

    // User Account Association
    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, is_primary_user, status) VALUES" +
      "(12, 9, 1, 1), " +
      "(15, 10, 1, 1), " +
      "(14, 9, 1, 1), " +
      "(16, 8, 1, 1), " +
      "(2, 7, 1, 1);" )

    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,3,0),(1,NULL,2);")

    // Permission Template
    sqlExecutor.execute("INSERT INTO permission_template(name,type,account_id,updated_by) VALUES('template1',1,9,1);")

    sqlExecutor.execute("INSERT INTO permission_template_mapping(permission_template_id, environment_type_id, permissions) VALUES(1, 1, '1001,1002,1003,1004,1011,1012,1013,1014,1026,1027,1028,1029,1030,1031');")

    // Role Permission Template Association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(permission_template_id, user_role_id) values(1,3)")
    //Inclusion domain
    sqlExecutor.execute("INSERT INTO tbl_prospect_inclusion_list_domains (email_domain, created_by, updated_by) VALUES ('testinclusion.com', 'Admin', 'Admin');")
    sqlExecutor.execute("INSERT INTO tbl_sponsor_bank_program (id, sponsor_bank_id, program_id, created_by, created_at) VALUES " +
      "(1, 3, 1, '<EMAIL>', '2023-07-10'), " +
      "(2, 3, 5, '<EMAIL>', '2023-07-10');")

    sqlExecutor.execute("INSERT INTO account_hierarchy (account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES" +
      "('9','9/',1,1,1), " +
      "('10','9/10/',4,1,1), " +
      "('8','8/',6,1,1), " +
      "('7','8/7/',4,1,1);")

    service = buildService(socureDb)
    inactiveService = buildInactiveService(socureDb)
    dashbaordService = buildDashboardService(socureDb)
    accountHierarchyService = buildAccountHeirarchyService(socureDb)
    accountAssociationHistoryService = buildAccountAssociationHistory(socureDb)
    permissionTemplateService = buildPermissionTemplate(socureDb)
    userAccountAssociationService = buildUserAccountAssociation(socureDb)
    userRoleService = buildUserRoleService(socureDb)
    rateLimitingService = buildRateLimitingService(socureDb)
    businessUserRoleService = buildBusinessUserRoleService(socureDb)
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }


  test("should return left when no entry exist") {
    val actual = service.validateUser(UserCredential("dfdf", "dfdf"))
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(_ shouldBe ErrorResponse(UsernamePasswordMismatch.id, UsernamePasswordMismatch.description), _ => fail)
    }
  }

  test("should return left when username is idle prefixed") {
    val actual = service.validateUser(UserCredential("<EMAIL>", "dfdf"))
    whenReady(actual) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(NonFunctionalMail), _ => fail)
    }
  }

  test("validateUserV2: should return left when username is idle prefixed") {
    val actual = service.validateUserV2(UserCredential("<EMAIL>", "dfdf"))
    whenReady(actual) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(NonFunctionalMail), _ => fail)
    }
  }

  test("should return left when username and password doesn't match entry exist") {
    val actual = service.validateUser(UserCredential("<EMAIL>", "dfdf"))
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(_.code shouldBe ExceptionCodes.UsernamePasswordMismatch.id, _ => fail)
    }
  }

  test("should return correct object with admin true") {
    whenReady(service.logBadLoginAttempt(UserFixture.userCredential.username, "some_error_msg")){ userStatus =>
      userStatus shouldBe Some(UserStatus(locked = false, badLoginCount = 1))
      whenReady(daoBusinessUser.getBadLoginDetails(1L)) { badLoginDetails =>
        badLoginDetails.exists(_.count == 1) shouldBe true
        val actual = service.validateUser(UserFixture.userCredential)
        whenReady(actual) { response =>
          response shouldBe 'right
          response.fold(_ => fail, _.user.isAdmin shouldBe true)
          whenReady(daoBusinessUser.getBadLoginDetails(1))(_ shouldBe None)
        }
      }
    }
  }

  test("sign-in time should update on successful login") {

    val actual = service.validateUser(UserFixture.userCredential)
    whenReady(actual) { response =>
      response shouldBe 'right
      whenReady(daoBusinessUser.getUser(response.fold(_ => fail, _.user.email))) { u =>
        u.isDefined shouldBe true
      }
    }
  }

  test("should return correct object with new api key for production and active api key for Development") {

    val actual = service.validateUser(UserFixture.userCredential)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.environment.find(_.name == "Production").fold(fail)(_.socureKey should be("88-16ca6193-4149-456b-ae00-00fdad2437c6")))
      response.fold(_ => fail, _.environment.find(_.name == "Development").fold(fail)(_.socureKey should be("916ca6193-4149-456b-ae00-00fdad2437c6")))
    }
  }

  test("should return correct user with admin false") {
    resetUserLocking()
    val actual = service.validateUser(UserCredential("<EMAIL>", "super"))
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.user.isAdmin shouldBe false)
    }
  }
  test("gopal should have has_subaccounts  and batchrun flags") {
    val actual = service.validateUser(UserFixture.userCredential)
    whenReady(actual) { response =>
      response shouldBe 'right
      val permissions = response.right.value.account.permission
      permissions should contain(BusinessUserRoles.ALLOW_SUBACCOUNTS.id)
      permissions should contain(BusinessUserRoles.ALLOW_BATCHRUNS.id)
    }
  }

  test("registration should fail with duplicate email address ") {
    val duplicatenmailUser = UserFixture.duplicateUserForm
    val msg = service.register(duplicatenmailUser, false)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail with duplicate email address") {
    val duplicateEmailUser =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 1
      )
    val msg = service.registerPrimaryUserParentAccount(duplicateEmailUser)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail with invalid email address") {
    val invalidEmailUser =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 1
      )
    val msg = service.registerPrimaryUserParentAccount(invalidEmailUser)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail with using accounts being inactive") {
    val user =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 2
      )
    val msg = service.registerPrimaryUserParentAccount(user)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail with parent account already having primary user") {
    val user =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 1
      )
    val msg = service.registerPrimaryUserParentAccount(user)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail with not a parent account error") {
    val user =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 4
      )
    val msg = service.registerPrimaryUserParentAccount(user)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration of primary user with parent account should fail ") {
    val user =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 5
      )
    val msg = service.registerPrimaryUserParentAccount(user)
    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("registration should fail, when email starts with idle") {
    Mockito.reset(modelManagementClient)
    val userform = UserFixture.userForm.copy(roles = Set(), email = "<EMAIL>")
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val msg = service.register(userform, true)

    whenReady(msg) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(RegistrationFailed), _ => fail)
    }
  }

  test("registration should pass for the first time(with activate flag set to true) and should fail registering with the same email") {
    Mockito.reset(modelManagementClient)
    val userform = UserFixture.userForm.copy(roles = Set(), email = Random.alphaNumeric(10).toLowerCase)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val msg = service.register(userform, true)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))

      //Sign-in time should be null
      whenReady(daoBusinessUser.getUser(userform.email)) { u =>
        u.fold(fail)(_.lastLoginOn should be(None))
      }

      whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
        inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe false
      }

      whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
        validation shouldBe 'right
        validation.fold(_ => fail, _.user.email shouldBe userform.email)
      }

      // Rate limiting config for email auth-score API for the account
      whenReady(rateLimitingService.getRateLimits(accountId.getValue, 1, RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId)) { res =>
        res shouldBe Right(Seq(RateLimitingConfig(windowInMillis = 1000, limit = 0)))
      }

      //Should not allow reregistration of the same email and should return a generic error in that case.
      whenReady(service.register(userform, false)) { res =>
        res shouldBe 'left
        res.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed), _ => fail)
      }
    }
    Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }

  test("registration should pass for the first time(with activate flag set to false) and should register again with the same email") {
    Mockito.reset(modelManagementClient)
    val userform = UserFixture.userForm.copy(roles = Set(), email = Random.alphaNumeric(10).toLowerCase)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val msg = service.register(userform, false)

    whenReady(msg) { response =>
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))

      //Sign-in time should be null
      whenReady(daoBusinessUser.getUser(userform.email)) { u =>
        u.fold(fail)(_.lastLoginOn should be(None))
      }

      whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
        inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe true
      }

      whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
        validation.fold(_ => fail, _.user.email shouldBe userform.email)
      }

      // Rate limiting config for email auth-score API for the account
      whenReady(rateLimitingService.getRateLimits(accountId.getValue, 1, RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId)) { res =>
        res shouldBe Right(Seq(RateLimitingConfig(windowInMillis = 1000, limit = 0)))
      }
    }
    Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }

  test("registration V2 (with account hierarchy and histroy) should pass through") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val userform = UserFixture.userFormV2.copy( email = "<EMAIL>", addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.eq(None))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = false, isDashboardV3 = false)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    }
    whenReady(daoBusinessUser.getUser(userform.email)) { u =>
      u.fold(fail) { bu =>
        bu.lastLoginOn should be(None)
        whenReady(daoBusinessUser.getBusinessUserInfo(bu.id)) { businessUserInfoOpt =>
          businessUserInfoOpt.fold(fail) { buInfo =>
            buInfo.addressLine1 shouldBe Some("Address Line 1")
            buInfo.addressLine2 shouldBe Some("Address Line 2")
            buInfo.jobTitle shouldBe Some("Job Title")
          }
        }
      }
    }

    whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
      inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe true
    }

    whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
      validation shouldBe 'right
      validation.fold(_ => fail, _.user.email shouldBe userform.email)
    }

    //The account is inactive state, as if we try registering using the same email, it should succeed
    whenReady(service.registerV2(userform, isActive = false, isDashboardV3 = false)) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }

    whenReady(accountHierarchyService.getAccountHierarchyByAccountId(accountId.getValue)) { res =>
      res.fold(_ => fail, ah => {
        ah.hierarchyPath shouldBe s"${accountId.getValue}/"
        ah.hierarchyStatus shouldBe 1
        ah.accountId shouldBe accountId.getValue
        ah.accountType shouldBe 1

        whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
          res0.fold(_ => fail, aah => {
            aah.accountHierarchyId shouldBe ah.id
          })
        }
      })
    }
    validatePermissions(userform.email, accountId.getValue, userform.accountType)

    val userCredential = UserCredential("<EMAIL>","al30P_ddff")
    whenReady(service.validateUser(userCredential)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.user.isAdmin shouldBe true)
      whenReady(daoBusinessUser.getBadLoginDetails(1))(_ shouldBe None)
    }
    Mockito.verify(modelManagementClient,Mockito.times(2)).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountAutomationService,Mockito.times(2)).autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.eq(None))
  }

  test("registration V2 for Aggregator (with account hierarchy and histroy) should pass through") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val userform = UserFixture.userFormV2.copy( email = Random.alphaNumeric(10).toLowerCase, accountType = AccountTypes.AGGREGATOR.id)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Boolean]), MMatchers.any(classOf[Int]), MMatchers.any(classOf[Option[String]]))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = false, isDashboardV3 = false)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    }
    whenReady(daoBusinessUser.getUser(userform.email)) { u =>
      u.fold(fail) { bu => bu.lastLoginOn should be(None)}
    }

    whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
      inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe true
    }

    whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
      validation shouldBe 'right
      validation.fold(_ => fail, _.user.email shouldBe userform.email)
    }

    whenReady(service.registerV2(userform, isActive = false, isDashboardV3 = false)) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }

    whenReady(accountHierarchyService.getAccountHierarchyByAccountId(accountId.getValue)) { res =>
      res.fold(_ => fail, ah => {
        ah.hierarchyPath shouldBe s"${accountId.getValue}/"
        ah.hierarchyStatus shouldBe 1
        ah.accountId shouldBe accountId.getValue
        ah.accountType shouldBe 3

        whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
          res0.fold(_ => fail, aah => {
            aah.accountHierarchyId shouldBe ah.id
          })
        }
      })
    }
    validatePermissions(userform.email, accountId.getValue, userform.accountType)
    Mockito.verify(modelManagementClient, Mockito.times(2)).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountAutomationService, Mockito.times(2)).autoProvision(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Boolean]), MMatchers.any(classOf[Int]), MMatchers.any(classOf[Option[String]]))
  }

  def validatePermissions(email: String, accountId: Long, accountType: Int): Unit =  {
    whenReady(daoBusinessUser.getUser(email)) { buOpt: Option[DtoBusinessUser] =>
      buOpt.fold(fail) { bu =>
        whenReady(userAccountAssociationService.getUserAccountAssociation(bu.id, accountId)) { uaa =>
          uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
            uaar.userRoles.isEmpty shouldBe false
            whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
              pts.fold(_ => fail, ptsms => {
                ptsms.map { ptsm =>
                  whenReady(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(ptsm.id, uaar.userId, uaar.accountId)) { pstmOpt =>
                    pstmOpt.fold(_ => fail, { a =>
                      a.exists(_.environmentTypeId.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id))
                      a.exists(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                      a.exists(_.environmentTypeId.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id))
                      a.filter(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id)).map { perms =>
                        perms.permissions.isEmpty shouldBe false
                        perms.permissions.exists(_.equals(DashboardUserPermissions.TRANSACTIONS_VIEW.id))
                        if(accountType === AccountTypes.AGGREGATOR.id) {
                          perms.permissions.exists(pId => {pId == DashboardUserPermissions.TRANSACTIONS_CREATE.id}) shouldBe false
                        }
                      }
                    })
                  }
                }
              })
            }
          })
        }
        whenReady(userRoleService.getUserRolesByAccountId(accountId, Creator(bu.id, accountId))) { ur =>
          ur.fold(_ => fail, r => {
            r.filter(_.roleType==SystemDefinedRoles.CUSTOMROLE.roleType).map{ r0 =>
              whenReady(userRoleService.getRolePermissionTemplateAssociation(r0.id.getOrElse(0), bu.id, accountId)) { ura =>
                ura.fold(_ => fail, rr => rr.userRoleId shouldBe r0.id.get)
              }
            }
          })
        }
      }

    }
  }

  test("registration for primary users to parent accounts should pass through") {
    val userForm =
      ParentAccountPrimaryUserForm(
        firstName= "",
        lastName= "",
        email = "<EMAIL>",
        contactNumber = "",
        accountId = 3
      )
    val accountId = 3
    Mockito.when(encryptionKeysService.generate(AccountId(accountId))).thenReturn(Future.successful(true))
    val msg = service.registerPrimaryUserParentAccount(userForm)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.email shouldBe userForm.email)
      response.fold(_ => fail, _.firstname shouldBe userForm.firstName)
      response.fold(_ => fail, _.surname shouldBe userForm.lastName)
    }
  }

  test("user login should be locked") {
    val username = "<EMAIL>"
    val msg = service.lockUser(username)
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe s"$username is locked")
    }
  }

  test("user not found should be thrown on lockUser") {
    val username = "<EMAIL>"
    val msg = service.lockUser(username)
    whenReady(msg) { response =>
      response shouldBe 'left
      response.fold(_ shouldBe ErrorResponse(UserNotFound.id, UserNotFound.description), _ => fail)
    }
  }

  test("user login should be unlocked") {
    val username = "<EMAIL>"
    val msg = service.unlockUser(username)
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe s"$username is unlocked")
    }
  }

  test("user not found should be thrown on unlock user") {
    val username = "<EMAIL>"
    val msg = service.unlockUser(username)
    whenReady(msg) { response =>
      response shouldBe 'left
      response.fold(_ shouldBe ErrorResponse(UserNotFound.id, UserNotFound.description), _ => fail)
    }
  }

  test("user activation code should be regenerated") {
    val username = "<EMAIL>"
    val msg = service.regenerateActivationCode(username)
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ should not be ("")) // Random key can't be tested.
    }
  }

  test("user not found should be thrown when activation code regenerated") {
    val username = "<EMAIL>"
    val msg = service.regenerateActivationCode(username)
    whenReady(msg) { response =>
      response shouldBe 'left
      response.fold(_ shouldBe ErrorResponse(UserNotFound.id, UserNotFound.description), _ => fail) // Random key can't be tested.
    }
  }

  test("should get a user by reset code when code is valid") {
    val result = service.getUserByResetCode("********-cc7f-46f7-b889-bedb0ce793f8")
    whenReady(result) { res =>
      res.fold(_ => fail, _.email should be("<EMAIL>"))
    }
  }

  test("should return invalid reset code when a code is invalid") {
    val result = service.getUserByActivationCode("43bb-aa29-853e46f61d7a")
    whenReady(result) { res =>
      res.fold(_.code shouldBe InvalidActivationCode.id, _ => fail)
    }
  }

  test("should get user by activationcode") {
    val result = service.getUserByActivationCode("c7741e94-be93-414d-95be-56a015157917")
    whenReady(result) { res =>
      res.fold(_ => fail, _.email should be("<EMAIL>"))
    }
  }

  test("user activation code should be valid") {
    val username = "<EMAIL>"
    val activtioncode = "c7741e94-be93-414d-95be-56a015157917"
    val msg = service.activateUserByActivationcode(activtioncode)
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("user activation code should be invalid") {
    val username = "<EMAIL>"
    val activtioncode = "a6a07d36-769c-43bb-aa29-853e46f6dsdsddfdfdf1d7a"
    val msg = service.activateUserByActivationcode(activtioncode)
    whenReady(msg) { response =>
      response shouldBe 'left
      response.fold(_ shouldBe ErrorResponse(InvalidActivationCode.id, InvalidActivationCode.description), _ => fail)
    }
  }

  test("active users list should return only primary active admin - after fixing the roles") {
    val user = UserFixture.primaryUsersV
    val users = service.getActivesPrimaryAccountAdmins(None, None, "")
    whenReady(users) { response =>
      response.size should be >= 1
      response.headOption.fold(fail)(_.optUser.map(_.id).getOrElse(-1) should be (user.headOption.map(_.accountId).getOrElse(fail)))
      response.headOption.fold(fail)(_.publicAccountId shouldBe UserFixture.primaryUser.publicAccountId)
      response.headOption.fold(fail)(r => {
        r.optUser.map(_.id).getOrElse(-1) should be (user.headOption.map(_.accountId).getOrElse(fail))
        r.publicAccountId shouldBe UserFixture.primaryUser.publicAccountId
        r.roles.contains(10000) shouldBe(false)
      })
      //This checks where businessUserId is equals to accountId. Because accountId is returned in businessUserId attributes
    }
  }

  test("active users count should list no of accounts") {
    val users = service.getActivesPrimaryAccountAdminsCount
    whenReady(users) { response =>
      response shouldBe  7
    }
  }

  test("user should not lock on before reaches max try") {
    val result = service.logBadLoginAttempt("<EMAIL>", "Bad credential")
    whenReady(result) { userStatus =>
      userStatus should not be empty
      userStatus.fold(fail)(_.locked shouldBe false)
      whenReady(dashbaordService.findUserByEmail("<EMAIL>")) { r =>
        r.fold(_ => fail, _.isLocked should be(false))
      }
    }
  }

  ignore("should not unlock since reset time is not reached") {
    resetUserLocking()
    service.unlockIfNeeded("<EMAIL>")
    Thread.sleep(100)
    whenReady(dashbaordService.findUserByEmail("<EMAIL>")) { r =>
      r.fold(_ => fail, _.isLocked should be(true))
    }
  }

  test("user should lock") {
    val futures = (1 to 4).map { _ =>
      Thread.sleep(2000)
      service.logBadLoginAttempt("<EMAIL>", "Bad credential")
    }
    whenReady(Future.sequence(futures)) { results =>
      val lockedUserStatusOpt = results.last
      lockedUserStatusOpt should not be empty
      lockedUserStatusOpt.fold(fail)(_.locked shouldBe true)

      whenReady(dashbaordService.findUserByEmail("<EMAIL>")) { r =>
        r.fold(_ => fail, _.isLocked should be(true))
      }
    }
  }

  test("should unlock automatically") {
    resetUserLocking()
    Thread.sleep(7000)
    service.unlockIfNeeded("<EMAIL>")
    Thread.sleep(100)
    whenReady(dashbaordService.findUserByEmail("<EMAIL>")) { r =>
      r.fold(_ => fail, _.isLocked should be(false))
    }
  }

  test("should return invalid activationcode") {
    val result = service.getUserByActivationCode("43bb-aa29-853e46f61d7a")
    whenReady(result) { res =>
      res.fold(_.code shouldBe InvalidActivationCode.id, _ => fail)
    }
  }

  test("should return <NAME_EMAIL>") {
    val result = service.getAccountIdByUsername("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_ => fail, _  should be(1L))
    }
  }

  test("should return 'no account found'") {
    val result = service.getAccountIdByUsername("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_.code shouldBe UserNotFound.id, _ => fail)
    }
  }

  test("should activate list of users") {
    val result = service.activateUsers(List("<EMAIL>"))
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be(true))
    }
  }

  test("should return false when email is not associated with account") {
    val result = service.activateUsers(List("<EMAIL>"))
    whenReady(result) { res =>
      res.fold(_.code should be(ExceptionCodes.UserNotActivated.id), _ => fail)
    }
  }

  test("should return true when user is locked") {
    whenReady(service.isUserLocked("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should return false when user is not locked") {
    whenReady(service.isUserLocked("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe false)
    }
  }

  test("isUserInternal should return true when user is internal") {
    whenReady(service.isUserInternal("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("isUserInternal should return false when user is not internal") {
    whenReady(service.isUserInternal("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe false)
    }
  }

  test("get business users with permissions and environment roles") {
    whenReady(service.getBusinessUsersWithRoleForAccount(1L)) { res =>
      val bu = BusinessUserWithRoles(5,"Sunder","Raj","<EMAIL>","**********",false,Vector(),Vector(("Production",Set("Overview", "Create Transcation")), ("Development",Set("Settings", "List Transaction"))))
      res.fold(_ => fail, a => {
        a.isEmpty shouldBe false
        a.size shouldBe 4
        a.exists(_.email.equals("<EMAIL>")) shouldBe true
      })
    }
  }

  test("getUsersWithRoleForAccount should return account empty list of users when no users found") {
    whenReady(service.getBusinessUsersWithRoleForAccount(4L)) { res =>
      res.fold(_ => fail, _.isEmpty shouldBe true)
    }
  }

  test("getUsersWithRoleForAccount should return account not found for inavlid account") {
    whenReady(service.getBusinessUsersWithRoleForAccount(100L)) { res =>
      res.fold(_.code shouldBe AccountNotFound.id, _ => fail)
    }
  }

  test("getInvalidAttemptsByEmail should return the right number") {
    whenReady(service.getInvalidAttemptsByEmail("<EMAIL>")) { res =>
      res shouldBe 5
    }
  }

  test("getInvalidAttemptsByEmail should return 0 for invalid data") {
    whenReady(service.getInvalidAttemptsByEmail("email")) { res =>
      res shouldBe 0
    }
  }

  test("Return accounts node in validate user response") {
    whenReady(service.validateUserV2(userCredential = UserFixture.userCredential)) { res =>
      res.fold(_ => fail, userAuthV2 => {
        userAuthV2.accounts shouldBe Seq(AccountIdName(1,"AccountName1"))
      })
    }
  }

  test("Return dashboardEnvPermissionsV2 node for v2 account in validate user response") {
    val actual = service.validateUser(UserCredential("<EMAIL>", "vicky"))
    whenReady(actual) { response =>
      response shouldBe 'right
      val permissions = response.right.value.dashboardEnvPermissionsV2
      permissions should not be None
      permissions.get should not be empty
      val environment = permissions.get.head
      environment.id shouldBe 2
      environment.name shouldBe "Development"
      environment.permissions should contain(DashboardUserPermissions.toPermissionResult(DashboardUserPermissions.EVENT_MANAGERS_VIEW))
    }
  }

  test("Should not return dashboardEnvPermissionsV2 node for non-v2 account in validate user response") {
    val actual = service.validateUser(UserFixture.userCredential)
    whenReady(actual) { response =>
      response shouldBe 'right
      val permissions = response.right.value.dashboardEnvPermissionsV2
      permissions shouldBe None
    }
  }

  test("promote primary user should swap the primary user, retaining the permissions"){
    val newPrimaryAdmin = BusinessUserWithRoles(9,"Internal","InternalUser1","<EMAIL>","**********",true,Vector(),Vector(("Development",Set()), ("Production",Set())))
    val oldPrimaryAdmin = BusinessUserWithRoles(8,"Internal","User1","<EMAIL>","**********",false,Vector(),Vector(("Development",Set("Settings")), ("Production",Set("List Transaction", "Delegated Admin"))))
    whenReady(service.promotePrimaryUser(9L)){ res =>
      res.fold(_ => fail, bu => {
        whenReady(service.getBusinessUsersWithRoleForAccount(bu.accountId)){ res0 =>
          res0.fold(_ => fail, s => {
            s.size shouldBe 2
            s.find(_.isPrimryAdmin).foreach{ pa =>
              pa shouldBe newPrimaryAdmin
            }
            s.find(_.isPrimryAdmin == false).map{ pa =>
              pa shouldBe oldPrimaryAdmin
            }
          })
        }
      })
    }
  }

  test("Check if an email exist - failure") {
    val email = "<EMAIL>"
    whenReady(service.doesUserExist(email)) { res =>
      res shouldBe Right(false)
    }
  }

  test("Check if an email exist - success") {
    val email = "<EMAIL>"
    whenReady(service.doesUserExist(email)) { res =>
      res shouldBe Right(true)
    }
  }

  test("registration should pass through when isDashboardV3 is true") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val email = Random.alphaNumeric(10).toLowerCase
    val userform = UserFixture.userFormV2.copy( email = email, addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.eq(None))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = true)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
      Mockito.verify(accountAutomationService).autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.any(classOf[Option[String]]))
    }
    whenReady(businessUserRoleService.getAccountPermissions(accountId.getValue)) { res =>
      res.fold(_ => fail, permissions => {
        val enabledPermissions = permissions.filter(_.provisioned === true)
        enabledPermissions.nonEmpty shouldBe true
        enabledPermissions.map(_.id).contains(BusinessUserRoles.DashboardV3.id) shouldBe true
      })
    }
  }

  test("registration should pass through when isDashboardV3 is false") {
    Mockito.reset(modelManagementClient)
    val email = Random.alphaNumeric(10).toLowerCase
    val userform = UserFixture.userFormV2.copy( email = email, addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.eq(None))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = false)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    }
    whenReady(businessUserRoleService.getAccountPermissions(accountId.getValue)) { res =>
      res.fold(_ => fail, permissions => {
        val enabledPermissions = permissions.filter(_.provisioned === true)
        enabledPermissions.nonEmpty shouldBe true
        enabledPermissions.map(_.id).contains(BusinessUserRoles.DashboardV3.id) shouldBe false
      })
    }
  }

  test("registration should pass through when isActive is true and modules are present") {
    Mockito.reset(modelManagementClient)
    val userform = UserFixture.userForm.copy(roles = Set(), email = Random.alphaNumeric(10).toLowerCase)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))

    val msg = service.register(userform, true)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))


      //Sign-in time should be null
      whenReady(daoBusinessUser.getUser(userform.email)) { u =>
        u.fold(fail)(_.lastLoginOn should be(None))
      }

      // Email should not be in in-active primary account admins list
      whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
        inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe false
      }

      whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
        validation shouldBe 'right
        validation.fold(_ => fail, _.user.email shouldBe userform.email)
      }

      // Rate limiting config for email auth-score API for the account
      whenReady(rateLimitingService.getRateLimits(accountId.getValue, 1, RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId)) { res =>
        res shouldBe Right(Seq(RateLimitingConfig(windowInMillis = 1000, limit = 0)))
      }

      // Modules should be assigned to the account
      whenReady(businessUserRoleService.getAccountPermissions(accountId.getValue)) { res =>
        res.fold(_ => fail, _.nonEmpty shouldBe true)
        //res.fold(_ => fail, _ should contain (BusinessUserRolesWithPermissions(BusinessUserRoles.KYC.id, BusinessUserRoles.KYC.label, true)))
      }
    }
    Mockito.verify(modelManagementClient).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }
  test("sub account should not have WLM cadence preferences 160 and 162") {


    whenReady(businessUserRoleService.getAccountPermissions(4)) { res =>
      res.fold(_ => fail, _.nonEmpty shouldBe true)
      res.fold(_ => fail, _.filter(_.id==160).length==0 shouldBe true)
      res.fold(_ => fail, _.filter(_.id==162).length==0 shouldBe true)

    }

  }
  test("Parent account should not have WLM cadence preferences 160 and 162") {
    whenReady(businessUserRoleService.getAccountPermissions(1)) { res =>
      res.fold(_ => fail, _.nonEmpty shouldBe true)
      res.fold(_ => fail, _.filter(_.id==160).length>0 shouldBe true)
      res.fold(_ => fail, _.filter(_.id==162).length>0 shouldBe true)
    }

  }

  test("registration V2 (with account hierarchy and histroy) should pass through with isActive true") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val email = Random.alphaNumeric(10).toLowerCase
    val userform = UserFixture.userFormV2.copy( email = email, addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.any(classOf[Option[String]]))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = false)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    }
    whenReady(daoBusinessUser.getUser(userform.email)) { u =>
      u.fold(fail) { bu =>
        bu.lastLoginOn should be(None)
        whenReady(daoBusinessUser.getBusinessUserInfo(bu.id)) { businessUserInfoOpt =>
          businessUserInfoOpt.fold(fail) { buInfo =>
            buInfo.addressLine1 shouldBe Some("Address Line 1")
            buInfo.addressLine2 shouldBe Some("Address Line 2")
            buInfo.jobTitle shouldBe Some("Job Title")
          }
        }
      }
    }

    // Email should not be in in-active primary account admins list
    whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
      inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe false
    }

    whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
      validation shouldBe 'right
      validation.fold(_ => fail, _.user.email shouldBe userform.email)
    }

    //should fail while tring to register with same email id
    whenReady(service.registerV2(userform, isActive = true, isDashboardV3 = false)) { res =>
      res shouldBe 'left
      res.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.EmailAlreadyExists), _ => fail)
    }

    whenReady(businessUserRoleService.getAccountPermissions(accountId.getValue)) { res =>
      res.fold(_ => fail, _.nonEmpty shouldBe true)
    }

    whenReady(accountHierarchyService.getAccountHierarchyByAccountId(accountId.getValue)) { res =>
      res.fold(_ => fail, ah => {
        ah.hierarchyPath shouldBe s"${accountId.getValue}/"
        ah.hierarchyStatus shouldBe 1
        ah.accountId shouldBe accountId.getValue
        ah.accountType shouldBe 1

        whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
          res0.fold(_ => fail, aah => {
            aah.accountHierarchyId shouldBe ah.id
          })
        }
      })
    }
    validatePermissions(userform.email, accountId.getValue, userform.accountType)
    Mockito.verify(modelManagementClient, Mockito.times(1)).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountAutomationService, Mockito.times(1)).autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(false), MMatchers.eq(1), MMatchers.any(classOf[Option[String]]))

    val userCredential = UserCredential(email,"al30P_ddff")
    whenReady(service.validateUser(userCredential)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.user.isAdmin shouldBe true)
      whenReady(daoBusinessUser.getBadLoginDetails(1))(_ shouldBe None)
    }
  }

  test("registration V2 with socure id") {
    Mockito.reset(modelManagementClient, encryptionKeysService, accountAutomationService)
    val email = "<EMAIL>"
    val userform = UserFixture.userFormV2.copy(email=email,addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(true), MMatchers.eq(1), MMatchers.any(classOf[Option[String]]))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = false)

    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    Mockito.verify(modelManagementClient, Mockito.times(1)).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    Mockito.verify(accountAutomationService, Mockito.times(1)).autoProvision(MMatchers.any(classOf[Long]), MMatchers.eq(true), MMatchers.eq(1), MMatchers.eq(None))
  }

  test("registration V2 Prospects accounts (with account hierarchy and histroy) should pass through with isActive true") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val email = "<EMAIL>"
    val userform = UserFixture.userFormV2.copy(accountType=5, email = email, addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(modelManagementClient.mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountAutomationService.autoProvision(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Boolean]), MMatchers.any(classOf[Int]), MMatchers.any(classOf[Option[String]]))).thenReturn(Future.successful(Right(true)))
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = true)

    whenReady(msg) { response =>
      println(response)
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(encryptionKeysService).generate(AccountId(accountId.getValue))
    }
    whenReady(daoBusinessUser.getUser(userform.email)) { u =>
      u.fold(fail) { bu =>
        bu.lastLoginOn should be(None)
        whenReady(daoBusinessUser.getBusinessUserInfo(bu.id)) { businessUserInfoOpt =>
          businessUserInfoOpt.fold(fail) { buInfo =>
            buInfo.addressLine1 shouldBe Some("Address Line 1")
            buInfo.addressLine2 shouldBe Some("Address Line 2")
            buInfo.jobTitle shouldBe Some("Job Title")
          }
        }
      }
    }

    // Email should not be in in-active primary account admins list
    whenReady(inactiveService.getInactivesPrimaryAccountAdmins) { inactiveUsers =>
      inactiveUsers.exists(_.optUser.map(_.email).getOrElse("") == userform.email) shouldBe false
    }

    whenReady(service.validateUser(UserCredential(userform.email, userform.password))) { validation =>
      validation shouldBe 'right
      validation.fold(_ => fail, _.user.email shouldBe userform.email)
    }

    //should fail while tring to register with same email id
    whenReady(service.registerV2(userform, isActive = true, isDashboardV3 = true)) { res =>
      res shouldBe 'left
      res.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.EmailAlreadyExists), _ => fail)
    }

    whenReady(businessUserRoleService.getAccountPermissions(accountId.getValue)) { res =>
      println(res)
      res.fold(_ => fail, _.nonEmpty shouldBe true)
    }

    whenReady(accountHierarchyService.getAccountHierarchyByAccountId(accountId.getValue)) { res =>
      res.fold(_ => fail, ah => {
        ah.hierarchyPath shouldBe s"${accountId.getValue}/"
        ah.hierarchyStatus shouldBe 1
        ah.accountId shouldBe accountId.getValue
        ah.accountType shouldBe 5

        whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
          res0.fold(_ => fail, aah => {
            aah.accountHierarchyId shouldBe ah.id
          })
        }
      })
    }
    validatePermissions(userform.email, accountId.getValue, userform.accountType)
    Mockito.verify(modelManagementClient, Mockito.times(1)).mapDefaultModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountAutomationService, Mockito.times(1)).autoProvision(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Boolean]), MMatchers.any(classOf[Int]), MMatchers.any(classOf[Option[String]]))

    val userCredential = UserCredential(email,"al30P_ddff")
    whenReady(service.validateUser(userCredential)) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.user.isAdmin shouldBe true)
      whenReady(daoBusinessUser.getBadLoginDetails(1))(_ shouldBe None)
    }
  }

  test("registration V2 Prospects accounts should fail for email id domain nor associated with socure") {
    Mockito.reset(modelManagementClient, accountAutomationService)
    val email = Random.alphaNumeric(10).toLowerCase
    val userform = UserFixture.userFormV2.copy(accountType=5, email = email, addressLine1 = Some("Address Line 1"), addressLine2 = Some("Address Line 2"), jobTitle = Some("Job Title"), businessWebsite = Some("Business website"), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    val msg = service.registerV2(userform, isActive = true, isDashboardV3 = true)

    whenReady(msg) { response =>
      response.fold(_.code shouldBe RegistrationFailed.id, _ => fail)
    }
  }

  test("prospect account validation should fail when email in exclusion list"){
    whenReady(service.validateEmailDomain("<EMAIL>",5)) { res =>
      res.fold(_.code shouldBe DemoRestrictedDomain.id, _ =>fail)
    }
  }
  test("prospect account validation should fail when email matches regex in exclusion list") {
    whenReady(service.validateEmailDomain("<EMAIL>", 5)) { res =>
      res.fold(_.code shouldBe DemoRestrictedDomain.id, _ => fail)
    }
  }
  test("prospect account validation should pass when email not in exclusion list"){
    whenReady(service.validateEmailDomain("<EMAIL>", 5)) { res =>
      res shouldBe 'right
    }
  }
  test("email validation should pass when called with upper case whitelisted domain") {
    Mockito.reset(modelManagementClient)
    val emailDomain = "<EMAIL>"
    val msg = service.validateEmailDomain(emailDomain, AccountTypes.DIRECT_CUSTOMER.id)

    whenReady(msg) { response =>
      response shouldBe Right(true)
    }
  }
  test("get business user info") {
    val bu_info =DtoBusinessUserInfo(1, 1,Some("Nt Street, Washington DC"),None,Some("SDE"),Some("socure.com"), DateTime.now(),DateTime.now())
    whenReady(service.getBusinessUserInfo(bu_info.businessUserId)) { res =>
      res.fold(_ => fail, a => {
        a.businessUserId == bu_info.businessUserId shouldBe true
        a.addressLine1 == bu_info.addressLine1 shouldBe true
        a.jobTitle == bu_info.jobTitle shouldBe true
        a.businessWebsite == bu_info.businessWebsite shouldBe true
      })
    }
  }
  test("get business user info - not found") {
    whenReady(service.getBusinessUserInfo(33849)) { res =>
      res.fold(_.code shouldBe BusinessUserNotFound.id, _=>fail)
    }
  }
  test("validate inclusion list - true") {
    val email = UserFixture.inclusionListEmail
    whenReady(service.isDomainFromInclusionList(email,UserFixture.prospectAccountType)) { res =>
      res.fold(_ => fail, a => {
        a shouldBe true
      })
    }
  }
  test("validate inclusion list - false") {
    val email = "t1"+UserFixture.inclusionListEmail
    whenReady(service.isDomainFromInclusionList(email, UserFixture.prospectAccountType)) { res =>
      res.fold(_ => fail, a => {
        a shouldBe false
      })
    }
  }
  test("validate getProgramIdNameByUserId method should return only active programs") {
    val actual = daoAccount.getProgramIdNameByUserId(13)
    val activePrograms = Seq(
      AccountIdName(
        id = 1, name = "AccountName1"
      )
    )
    whenReady(actual) { response =>
      response shouldBe activePrograms // should return only active Programs
    }
  }

  test("Check if Login Restricted For SAML accounts") {
    def assertLoginDetails(user: String, expectedResponse: Either[ErrorResponse, Map[String, Int]]): Unit = {
      whenReady(service.getUserLoginDetails(user)) { res =>
        res shouldBe expectedResponse
      }
    }

    // User associated with RiskOS account
    assertLoginDetails("<EMAIL>", Right(Map("badLoginCount" -> 0, "magicTokenCount" -> 0)))

    //User associated only with RiskOS Sub account
    assertLoginDetails("<EMAIL>", Right(Map("badLoginCount" -> 0, "magicTokenCount" -> 0)))

    // User associated only with SAML account
    whenReady(service.getUserLoginDetails("<EMAIL>")) { res =>
      res.fold(err => assert(err.code == 652), _ => fail("Unexpected <NAME_EMAIL>"))
    }

    // User associated only with Non-SAML SubAccount
    assertLoginDetails("<EMAIL>", Right(Map("badLoginCount" -> 0, "magicTokenCount" -> 0)))

    // Insert a record for user associated with Non-SAML SubAccount and SAML Parent account
    sqlExecutor.execute("INSERT IGNORE INTO user_account_association(business_user_id, account_id, is_primary_user, status) VALUES (15, 9, 1, 1);")

    // Re-run check for user with Non-SAML SubAccount and SAML Parent account
    assertLoginDetails("<EMAIL>", Right(Map("badLoginCount" -> 0, "magicTokenCount" -> 0)))
  }


  private def resetUserLocking(): Unit = {
    sqlExecutor.execute("DELETE FROM tbl_business_user_bad_login_count WHERE business_user_id = 5")
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES(5, 5, '2015-10-20', '" + dbDateFormat.print(new RealClock().now()) + "', '2015-10-20')")
  }
}
