package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.dashboardv2.Creator
import me.socure.model.{AccountUIConfiguration, AccountUIConfigurationRequest, AccountsUIConfigurationRequest}
import me.socure.storage.slick.dao.{DaoAccountUIConfiguration, DaoAccountV2}
import me.socure.utils.DBProxyWithMetrics
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionVal<PERSON>, Matchers => MMatchers}
import org.slf4j.{Lo<PERSON>, LoggerFactory}
import scalacache.{<PERSON><PERSON>, ScalaCache}
import slick.jdbc.JdbcBackend

class AccountUIConfigurationServiceTest extends FunSuite with MMatchers with ScalaFutures with MockitoSugar with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val logger: Logger = LoggerFactory.getLogger(getClass)

  var service: AccountUIConfigurationService = _
  override val mysqlService: MysqlService = MysqlService("account-ui-configuration-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    val db = JdbcBackend.Database.forDataSource(socureDb)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = socureDb
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val cache = mock[Cache[AccountUIConfiguration]]
    val scalaCache = ScalaCache(cache)
    service = new AccountUIConfigurationService(daoUIAccountConfiguration, v2Validator, scalaCache,auditDetailsService)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get UI account configuration by account id failure - access forbidden") {
    whenReady(service.getUIAccountConfiguration(1L, Creator(2L, 2L))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Get UI account configuration by account id - not present") {
    whenReady(service.getUIAccountConfiguration(1L, Creator(1L, 1L))) { response =>
      response shouldBe Right(AccountUIConfiguration(None, None))
    }
  }

  test("Save UI account configuration by account id failure for accounts- access forbidden") {
    whenReady(service.saveUIAccountConfigurationForAccounts(AccountsUIConfigurationRequest(Seq(4L), 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Save UI account configuration by account id for accounts") {
    whenReady(service.saveUIAccountConfigurationForAccounts(AccountsUIConfigurationRequest(Seq(1L), 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Save UI account configuration by account id for an sub accounts") {
    whenReady(service.saveUIAccountConfigurationForAccounts(AccountsUIConfigurationRequest(Seq(2L), 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Save UI account configuration by account id failure - access forbidden") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(4L, 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Save UI account configuration by account id") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(1L, 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Save UI account configuration by account id for an sub account") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(2L, 10, 10), Creator(1L, 1L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Get UI account configuration by account id1") {
    whenReady(service.getUIAccountConfiguration(1L, Creator(1L, 1L))) { response =>
      response.fold(_ => fail,
        config => {
          config.autoTimeoutInMinutes shouldBe Some(10)
          config.idleTimeoutInMinutes shouldBe Some(10)
        })
    }
  }

  test("Update UI account configuration by account id for accounts- access forbidden") {
    whenReady(service.saveUIAccountConfigurationForAccounts(AccountsUIConfigurationRequest(Seq(2L), 10, 15), Creator(3L, 3L))) { response =>
      response._2 shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Update UI account configuration by account id - access forbidden") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(2L, 10, 15), Creator(3L, 3L))) { response =>
      response._2 shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Update UI account configuration by account id") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(1L, 5, 15), Creator(1L, 1L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Get UI account configuration by account id2") {
    whenReady(service.getUIAccountConfiguration(1L, Creator(1L, 1L))) { response =>
      response.fold(_ => fail,
        config => {
          config.autoTimeoutInMinutes shouldBe Some(5)
          config.idleTimeoutInMinutes shouldBe Some(15)
        })
    }
  }

  test("Save UI account configuration by account id - set hidesystemdefinedroles as false") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(3L, 10, 10, false), Creator(1L, 3L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Get UI account configuration by account - with hidesystemdefinedroles false") {
    whenReady(service.getUIAccountConfiguration(3L, Creator(1L, 3L))) { response =>
      response.fold(_ => fail,
        config => {
          config.hideSystemDefinedRoles shouldBe false
        })
    }
  }

  test("update UI account configuration by account id - set hidesystemdefinedroles as true") {
    whenReady(service.saveUIAccountConfiguration(AccountUIConfigurationRequest(3L, 10, 10, true), Creator(1L, 3L))) { response =>
      response._2 shouldBe Right(true)
    }
  }

  test("Get UI account configuration by account - with hidesystemdefinedroles true") {
    whenReady(service.getUIAccountConfiguration(3L, Creator(1L, 3L))) { response =>
      response.fold(_ => fail,
        config => {
          config.hideSystemDefinedRoles shouldBe true
        })
    }
  }

}