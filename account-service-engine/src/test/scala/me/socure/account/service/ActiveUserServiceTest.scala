package me.socure.account.service

import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.superadmin.ActiveUsersService
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

/**
  * Created by sun<PERSON><PERSON> on 5/30/16.
  */
class ActiveUserServiceTest extends FunSuite with Matchers with ScalaFutures with BeforeAndAfterAll with TestDataSourceConfig with EitherValues {

  implicit val defaultPatience = PatienceConfig(timeout = Span(30, Seconds), interval = Span(500, Millis))
  val defaultDomains = Set("0.0.0.0/1", "128.0.0.0/1")

  var service : ActiveUsersService = _
  var environmentSettingsService : EnvironmentSettingsService = _
  override val mysqlService: MysqlService = MysqlService("active-user-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    val services = buildActiveUserService(socureDb)
    service = services._2
    environmentSettingsService = services._1
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  /*
  * Mark as Internal
  * */

  test("should return account not found") {
    val actual = service.markAsInternal(List("<EMAIL>", "<EMAIL>"))
    whenReady(actual) { res =>
      res shouldBe 'left
    }
  }

  test("should mark as internal user") {
    val actual = service.markAsInternal(List("<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 1
    }
  }

  test("should mark as internal two users") {
    val actual = service.markAsInternal(List("<EMAIL>", "<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 2
    }
  }

  test("markAccountAsInternal: should return error for an invalid account") {
    val actual = service.markAccountAsInternal(100L)
    whenReady(actual) { res =>
      res shouldBe 'left
      res.left.value.code should be (ExceptionCodes.AccountNotFound.id)
    }
  }

  test("markAccountAsInternal: should return 1 for a valid account") {
    val actual = service.markAccountAsInternal(1L)
    whenReady(actual) { res =>
      res shouldBe 'right
      res.right.value shouldBe 1
      whenReady(environmentSettingsService.getEnvironmentWithDomains(1L)) { res =>
        res shouldBe 'right
        res.right.get.size shouldBe 3
        res.right.get.forall(env => {
          val existingDomains = env.domains.split(",").toSet
          defaultDomains.subsetOf(existingDomains)
        }) shouldBe true
      }
    }
  }

  /*
  * Unmark as Internal
  * */

  test("unmark should return account not found") {
    val actual = service.unmarkAsInternal(List("<EMAIL>"))
    whenReady(actual) { res =>
      res shouldBe 'left
    }
  }

  test("should unmark as internal user") {
    val actual = service.unmarkAsInternal(List("<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 1
    }
  }

  test("should unmark as internal two users") {
    val actual = service.unmarkAsInternal(List("<EMAIL>", "<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 2
    }
  }

  test("unmarkAccountAsInternal: should return error for an invalid account") {
    val actual = service.unmarkAccountAsInternal(100L)
    whenReady(actual) { res =>
      res shouldBe 'left
      res.left.value.code should be (ExceptionCodes.AccountNotFound.id)
    }
  }

  test("unmarkAccountAsInternal: should return 1 for a valid account") {
    val actual = service.unmarkAccountAsInternal(1L)
    whenReady(actual) { res =>
      res shouldBe 'right
      res.right.value shouldBe 1
      whenReady(environmentSettingsService.getEnvironmentWithDomains(1L)) { res =>
        res shouldBe 'right
        res.right.get.size shouldBe 3
        res.right.get.forall(env => {
          val existingDomains = env.domains.split(",").toSet
          defaultDomains.subsetOf(existingDomains)
        }) shouldBe false
      }
    }
  }

  /*
  * Deactive User
  * */

  test("deactivate should return account not found") {
    val actual = service.deactivateUser(List("<EMAIL>"))
    whenReady(actual) { res =>
      res shouldBe 'left
    }
  }

  test("should deactivate user") {
    val actual = service.deactivateUser(List("<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 1
    }
  }

  test("should deactivate multiple users") {
    val actual = service.deactivateUser(List("<EMAIL>", "<EMAIL>"))
    whenReady(actual) {res =>
      res.right.value shouldBe 2
    }
  }

  test("deactivateAccount: should return account not found") {
    val actual = service.deactivateAccount(100L)
    whenReady(actual) { res =>
      res shouldBe 'left
      res.left.value.code should be (ExceptionCodes.AccountNotFound.id)
    }
  }

  test("deactivateAccount: should deactivate account") {
    val actual = service.deactivateAccount(1L)
    whenReady(actual) {res =>
      res.right.value shouldBe 1
    }
  }

  /**
    * Get Domain
    */

  test("should give domain list"){
    val result = service.getDomainByAccountId(2)
    whenReady(result){ res =>
      res.right.value should include ("account24")
    }
  }

  test("account not found should return"){
    val result = service.getDomainByAccountId(555)
    whenReady(result){res =>
      res.left.value.code should be (ExceptionCodes.AccountNotFound.id)
    }
  }

  test("add domain") {
    val result = service.addDomainByAccountId(3, List("socure.com"))
    whenReady(result) { res =>
      res.right.value should be (true)
      whenReady(service.getDomainByAccountId(3)) { d =>
        d.right.value should include ("socure.com")
      }
    }
  }

  test("add domain should return parent account not found when the account is not a parent account") {
    val result = service.addDomainByAccountId(8, List("socure.com"))
    whenReady(result){res =>
      res.left.value.code should be (ExceptionCodes.ParentAccountNotFound.id)
    }
  }

  test("add domain should return parent account not found") {
    val result = service.addDomainByAccountId(555, List("socure.com"))
    whenReady(result){res =>
      res.left.value.code should be (ExceptionCodes.ParentAccountNotFound.id)
    }
  }

  test("Should return error for invalid domains") {
    val result = service.addDomainByAccountId(8, List("", "localhost"))
    whenReady(result){ res =>
      res.left.value.code should be (ExceptionCodes.DomainNotValid.id)
    }
  }

  test("Should return error for empty values ") {
    val result = service.addDomainByAccountId(8, List("", " "))
    whenReady(result){ res =>
      res.left.value.code should be (ExceptionCodes.DomainNotValid.id)
    }
  }

  test("Should return valid for right values ") {
    val result = service.addDomainByAccountId(3, List("10.0.0.0/8", "https://socure.com "))
    whenReady(result){ res =>
      res shouldBe 'right
      whenReady(service.getDomainByAccountId(3)) { d =>
        d.right.value should include ("https://socure.com")
        d.right.value should include ("10.0.0.0/8")
      }
    }
  }
  /**
    * Get Deleted Account List
    */

  test("should get deleted accounts list") {
    val result = service.getDeletedAccountsList
    whenReady(result){ res =>
      res.size shouldBe 2
      res.headOption.fold(fail)(_.accountId shouldBe 9)
      res.headOption.fold(fail)(_.name shouldBe "AccountName9")
      res.headOption.fold(fail)(_.apiKey shouldBe "91-926ca6193-4149-456b-ae00-00fdad2437c6")
      res.last.accountId shouldBe 10
      res.last.name shouldBe "AccountName10"
      res.last.apiKey shouldBe "92-926ca6193-4149-456b-ae00-00fdad2437c6"
      res.last.parentName shouldBe Some("AccountName9")
    }
  }

}
