package me.socure.account.service

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ExceptionCodes.SAMLNotEnabledForParent
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.common.xml.schema.validator.XmlSchemaValidator
import me.socure.model.BusinessUserRoles
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.storage.slick.dao.{DaoAccountV2, DaoIdpMetadata}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import me.socure.utils.DBProxyWithMetrics

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.io.Source

/**
  * Created by angayarkanni on 12/6/17.
  */
class IdpMetadataServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  private implicit val patience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))


  private val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  private val encryptionDataKeysService = mock[EncryptionDataKeysService]
  private val idpMetadataValidator = mock[XmlSchemaValidator]
  private var service : IdpMetadataService = _
  private val metadata = Source.fromInputStream(getClass.getResourceAsStream("/sample-saml.xml")).mkString
  private val metadata1 = Source.fromInputStream(getClass.getResourceAsStream("/metadata1.xml")).mkString
  private val metadata7 = Source.fromInputStream(getClass.getResourceAsStream("/metadata7.xml")).mkString
  private val metadata2 = Source.fromInputStream(getClass.getResourceAsStream("/metadata2.xml")).mkString
  private val metadata3 = Source.fromInputStream(getClass.getResourceAsStream("/metadata3.xml")).mkString
  private val emd = Source.fromInputStream(getClass.getResourceAsStream("/encrypted_data.txt")).mkString
  private val emd1 = Source.fromInputStream(getClass.getResourceAsStream("/encrypted_data1.txt")).mkString
  private val samlRole = BusinessUserRoles.SAML_2_0.id

  val mysqlService: MysqlService = MysqlService("idp-meta-data-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoIdpMetadata = new DaoIdpMetadata(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)

    val accountInfoService = new AccountInfoService(
      daoAccount = daoAccount,
      v2Validator = v2Validator
    )
    new IdpMetadataService(
      daoIdpMetadata = daoIdpMetadata,
      daoAccount = daoAccount,
      daoAccountV2 = daoAccountV2,
      xmlSchemaValidator = idpMetadataValidator,
      accountInfoService = accountInfoService,
      clock = clock)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, 2, false, '${PublicIdGenerator.account().value}','publicApiKey7','externalId7'), " +
      s"(8, 'Duplicate Test1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey8','externalId8'), " +
      s"(9, 'Duplicate Test2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey9','externalId9'), " +
      s"(10, 'AccountName10', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey10','externalId10'), " +
      s"(11, 'AccountName11', '101-205', false, 1, 10, false, '${PublicIdGenerator.account().value}','publicApiKey11','externalId11')"
    )

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES " +
      s"(1, $samlRole), " +
      s"(2, $samlRole), " +
      s"(3, $samlRole), " +
      s"(4, $samlRole), " +
      s"(5, $samlRole), " +
      s"(7, $samlRole), " +
      s"(8, $samlRole), " +
      s"(9, $samlRole)")

    //idp metadata
    sqlExecutor.execute("INSERT INTO tbl_idp_metadata (id, account_id, entity_id, metadata, created_at) VALUES " +
      "(1, 1, 'http://www.okta.com/exkdtolqmwRKwfPIg0h7', '" + metadata + "', '2017-12-06'), " +
      "(2, 4, 'http://urn:example:idp','" + metadata1 + "', '2017-12-06')"
    )

    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('2','1/2',1,1,1), " +
      "('3','3/',1,1,1)," +
      "('4','3/4',1,1,1)")

    service = buildService(socureDb)

  }

  override def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should list all idp metadata"){
    whenReady(service.listIdpMetadata()){ res =>
      res shouldBe 'right
      res.right.value.size shouldBe 2
    }
  }

  test("should get idp metadata for an entity id"){
    whenReady(service.getIdpMetadata(entityId = "http://www.okta.com/exkdtolqmwRKwfPIg0h7")){ res =>
      res shouldBe 'right
      res.right.value shouldBe IdpMetadata(1, metadata)
    }
  }

  test("should throw error - idp metadata for an invalid entityId"){
    whenReady(service.getIdpMetadata(entityId = "https://metadata")){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe 170
      res.left.value.message shouldBe "No Idp Metadata Found"
    }
  }

  test("should get idp metadata for an account id 1"){
    whenReady(service.getIdpMetadata(accountId = 1)){ res =>
      res shouldBe 'right
      res.right.value shouldBe IdpMetadata(1, metadata)
    }
  }

  test("should get parent idp metadata for an account id 2"){
    whenReady(service.getIdpMetadata(accountId = 2)){ res =>
      res shouldBe 'right
      res.right.value shouldBe IdpMetadata(1, metadata)
    }
  }

  test("should return empty idp metadata for an account id 3"){
    whenReady(service.getIdpMetadata(accountId = 3)){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe 170
      res.left.value.message shouldBe "No Idp Metadata Found"
    }
  }

  test("should get idp metadata for an account id 4"){
    whenReady(service.getIdpMetadata(accountId = 4)){ res =>
      res shouldBe 'right
      res.right.value shouldBe IdpMetadata(4, metadata1)
    }
  }

  test("should return empty idp metadata for an account id 5atus"){
    whenReady(service.getIdpMetadata(accountId = 5)){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe 170
      res.left.value.message shouldBe "No Idp Metadata Found"
    }
  }

  test("should insert idp metadata for an account which already has it, with a different entity ID"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Right(true)
    whenReady(service.insertIdpMetadata(accountId = 1, metadata3)){ res =>

      Mockito.verify(idpMetadataValidator, Mockito.times(1)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])

    }
  }

  test("should not insert idp metadata if metadata already exists with same entity ID"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Right(true)
    whenReady(service.insertIdpMetadata(accountId = 8, metadata2)){ res =>
      res shouldBe Right(true)
      Mockito.verify(idpMetadataValidator, Mockito.times(1)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])
      whenReady(service.insertIdpMetadata(accountId = 9, metadata2)) { res1 =>
        res1 shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.DuplicateIdpMetadata))
      }
    }
  }

  test("should not insert idp metadata with invalid account id"){
    whenReady(service.insertIdpMetadata(accountId = 22, "<xml>")){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe ExceptionCodes.AccountNotFound.id
    }
  }

  test("should throw error for insert idp metadata with invalid meta data"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Left("False")
    whenReady(service.insertIdpMetadata(accountId = 5, "")){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe ExceptionCodes.InvalidIdpMetadata.id
    }
    Mockito.verify(idpMetadataValidator, Mockito.times(1)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])
  }

  test("should not insert metadata when account does not have SAML 2.0 permission"){
    whenReady(service.insertIdpMetadata(accountId = 6, "")){ res =>
      res shouldBe 'left
      res.left.value shouldBe ErrorResponseFactory.get(ExceptionCodes.SAMLNotEnabled)
    }
  }

  test("should insert idp metadata with valid metadata"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Right(true)

    whenReady(service.insertIdpMetadata(accountId = 5, metadata1)){ res =>
      Mockito.verify(idpMetadataValidator, Mockito.times(1)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])
      res shouldBe 'right
      res.right.value shouldBe true
    }
  }

  test("should insert idp metadata with valid metadata for a subaccount"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Right(true)

    whenReady(service.insertIdpMetadata(accountId = 7, metadata7)){ res =>
      Mockito.verify(idpMetadataValidator, Mockito.times(1)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])
      res.fold(_ => fail, _ => true)
    }
  }

  test("should fail to insert idp metadata with when parent is not saml enabled"){
    Mockito.reset(idpMetadataValidator)
    Mockito.when(idpMetadataValidator.validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])) thenReturn Right(true)

    whenReady(service.insertIdpMetadata(accountId = 11, metadata7)){ res =>
      Mockito.verify(idpMetadataValidator, Mockito.times(0)).validateWithFeedback(org.mockito.Matchers.any[ByteArrayInputStream])
      res.fold(_ => Left(SAMLNotEnabledForParent), _ => fail)
    }
  }

  test("should throw error when delete idp metadata for an invalid account id"){
    whenReady(service.deleteIdpMetadata(accountId = 33)){ res =>
      res shouldBe 'left
      res.left.value.code shouldBe ExceptionCodes.UnknownError.id
    }
  }

  test("should delete the idp metadata"){
    whenReady(service.deleteIdpMetadata(accountId = 1)){ res =>
      res shouldBe 'right
      res.right.value shouldBe true
    }
  }

}
