package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.AccountDashboardDomain
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao.{DaoAccountV2, DaoDashboardDomain}
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 10/04/2017.
  */
class DashboardDomainServiceTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with MemcachedTestSupport {
  implicit val ex : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2017-04-10").withZone(DateTimeZone.UTC).getMillis)

  var service : DashboardDomainService = _
  val mysqlService: MysqlService = MysqlService("dashboard-domain-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  override def memcachedPodLabel(): String = "dashboard-domain-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))
  private val accountId = 2
  private val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"
  var dashboardAccountServiceV2: DashboardAccountServiceV2 = _
  private val successResult = AccountDashboardDomain(
    accountId = 2,
    domainWhiteEnabled = true,
    whiteListedDomain = Some("domain.com")
  )
  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    val daoDashboardDomain = new DaoDashboardDomain(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new DashboardDomainService(daoDashboardDomain, daoBusinessUser, clock, scalaCache, auditDetailsService, dashboardAccountServiceV2, daoAccountV2)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()


    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2')"
    )

    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 2, true) "
    )

    sqlExecutor.execute("INSERT INTO tbl_dashboard_domain (account_id, domain, updated_at) VALUES (1, 'socure.com,12.99.00.101', current_timestamp)")

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, 31)")

    service = buildService(socureDb)

  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("add doamin to be whitelisted, should return true") {
    whenReady(service.upsertDashboardDomain(2, List("domain.com", "domain1.com"))){ res =>
      res._2.right.value shouldBe true
    }
  }

  test("should fail when forgein key constraint fails") {
    whenReady(service.upsertDashboardDomain(3, List("domain.com", "domain1.com"))){ res =>
      res._2.left.value.code shouldBe ExceptionCodes.UnknownError.id
    }
  }

  test("update existing domain should return true") {
    whenReady(service.getWhitelistedDomainForAccount(2)) { r =>
      r shouldBe 'right
      r.right.value should be("domain.com,domain1.com")

      whenReady(service.upsertDashboardDomain(2, List("domain.com"))) { res =>
        res._2.right.value shouldBe true

        whenReady(service.getWhitelistedDomainForAccount(2)) { rr =>
          rr.right.value shouldBe "domain.com"
        }
      }
    }
  }

  test("should return domain list by email") {
    whenReady(service.getWhitelistedDomainForAccountByEmail("<EMAIL>")) {res =>
      res.right.value shouldBe "socure.com,12.99.00.101"
    }
  }

  test("should return account not found exception") {
    whenReady(service.getWhitelistedDomainForAccountByEmail("<EMAIL>")) {res =>
      res.left.value.code shouldBe ExceptionCodes.AccountNotFound.id
    }
  }

  test("should return domain with permission by email") {
    whenReady(service.getWhitelistedDomainPermissionForAccountByEmail("<EMAIL>")) { res =>
      res.right.value shouldBe AccountDashboardDomain(1, true, Some("socure.com,12.99.00.101"))
    }
  }

  test("should return no domain with no permission by email") {
    whenReady(service.getWhitelistedDomainPermissionForAccountByEmail("<EMAIL>")) { res =>
      res.right.value shouldBe AccountDashboardDomain(2, false, None)
    }
  }

  test("should return domain with permission by account id") {
    whenReady(service.getWhitelistedDomainPermissionForAccountById(id = 1)) { res =>
      res.right.value shouldBe AccountDashboardDomain(1, true, Some("socure.com,12.99.00.101"))
    }
  }

  test("should invalidate cache if we do update the cache list") {
    scalaCache.cache.put[AccountDashboardDomain](cacheKey, successResult, None)
    whenReady(scalaCache.cache.get[AccountDashboardDomain](cacheKey))(_ shouldBe Some(successResult))
    whenReady(service.upsertDashboardDomain(2, List("domain2.com"))) { res =>
      res._2.right.value shouldBe true
      whenReady(scalaCache.cache.get[AccountDashboardDomain](cacheKey))(_ shouldBe None)
    }
  }

  test("should return no domain with no permission by account id") {
    whenReady(service.getWhitelistedDomainPermissionForAccountById(id = 2)) { res =>
      res.right.value shouldBe AccountDashboardDomain(2, false, None)
    }
  }

}
