package me.socure.account.service

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.publicid.PublicId
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.salt.client.SaltClient
import me.socure.salt.model.Salt
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 3/26/17.
  */
class PasswordStorageServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MockitoSugar {

  implicit val defaultPatience = PatienceConfig(timeout = Span(60, Seconds), interval = Span(500, Millis))

  implicit val ec = ExecutionContext.global

  val mysqlService: MysqlService = MysqlService("password-storage-service")
  private val dbName = "socure"

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  val saltClient = mock[SaltClient]

  val salt = Salt(PublicId("a_salt_id"), "AF6990D8-96DA-4170-A6DC-5FB619BDB4E0", clock.now())
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(salt)))
  Mockito.when(saltClient.get(PublicId("a_salt_id"))).thenReturn(Future.successful(Right(salt)))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {

    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildEngine(dataSource: DataSource) = {

    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(buildDataSource(Some(dbName)))

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('industry sector', 'industry description')")
    sqlExecutor.execute(s"INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES (1, 'accountName', 'industry sector', false, true, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), (2, 'accountName1', 'industry sector', false, true, NULL, true, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2')")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(1, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 1)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(2, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(3, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(4, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(5, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(6, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(7, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 2)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(1,'bd612c69b0386a26c436b4cb15814bf1', 'deprecated', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(1,'8d7455495e0d09297c8ac626bc1d355a', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(2,'ae635db1c9c59a6a826845fbbe2b2792', 'deprecated', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(2,'a0f912c5e55407737b471c15340299e4', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(5,'bd612c69b0386a26c436b4cb15814bf1', 'active', 'v1', current_timestamp, '2016-01-01')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(4,'bd612c69b0386a26c436b4cb15814bf1', 'deprecated', 'v1', current_timestamp, '2016-01-01')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, salt_id, password, status, hash_algorithm, created_at, updated_at) VALUES(6, NULL,'bd612c69b0386a26c436b4cb15814bf1', 'deprecated', 'v1', current_timestamp, '2016-01-01')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, salt_id, password, status, hash_algorithm, created_at, updated_at) VALUES(6, 'a_salt_id','$v2i$v=19$m=65536,t=2,p=1$Wwwss5ez1Wy7NkUbbSBTSA$/Kk2zrwiRrXqqJ2QJPEwrj28otxlMS4fkcVFTfrz5Cg', 'deprecated', 'v2', current_timestamp, '2016-01-01')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, salt_id, password, status, hash_algorithm, created_at, updated_at) VALUES(6, 'a_salt_id','$argon2i$v=19$m=65536,t=2,p=1$XcyiOJCrZGZnRCYC8b2yBw$nVAF3R56xe6PbjZUCS+HmV17HItCWQ+ZT8hVCw6MxS8', 'active', 'v2', current_timestamp, '2016-01-01')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, salt_id, password, status, hash_algorithm, created_at, updated_at) VALUES(7, 'a_salt_id','2FE7218A9C8F952DB65FA0156C2D87DAD7D3C130A5F4154F8CEF735F6D845F55', 'active', 'v3', current_timestamp, '2016-01-01')")
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("validate current password should work") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.validate(1, "current_password")) { response =>
      response shouldBe true
    }
  }

//  test("validate current password should work with v2") {
//    val dataSource = buildDataSource(Some(dbName))
//    val engine = buildEngine(dataSource)
//    whenReady(engine.validate(6, "new_password")) { response =>
//      response shouldBe true
//    }
//  }

//  test("validate current password should work with v2 and wrong password") {
//    val dataSource = buildDataSource(Some(dbName))
//    val engine = buildEngine(dataSource)
//    whenReady(engine.validate(6, "wrong_password")) { response =>
//      response shouldBe false
//    }
//  }

  test("validate current password should work with v3") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.validate(7, "test_password")) { response =>
      response shouldBe true
    }
  }

  test("validate current password should work with v3 and wrong password") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.validate(7, "wrong_password")) { response =>
      response shouldBe false
    }
  }

  test("validate old password should not work") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.validate(1, "old_password")) { response =>
      response shouldBe false
    }
  }

  test("validate wrong password should not work") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.validate(1, "wrong")) { response =>
      response shouldBe false
    }
  }

  test("alreadyUsed should detect old password") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.alreadyUsed(1, "old_password")) { response =>
      response shouldBe true
    }
  }

  test("alreadyUsed should not mix users") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.alreadyUsed(1, "old_password_2")) { response =>
      response shouldBe false
    }
  }

  test("update password should work") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.countPasswords(7)) { count =>
      whenReady(engine.updatePassword(7, "new_password")) { _ =>
        whenReady(engine.validate(7, "test_password")) { validateOld =>
          validateOld shouldBe false
          whenReady(engine.validate(7, "new_password")) { validateNew =>
            validateNew shouldBe true
          }
        }
      }
    }
  }

  test("create password should not work if password already exist") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.createPassword(1, "some password")) { result =>
      result shouldBe false
    }
  }

  test("create password should  work if password does not exist") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.createPassword(3, "some password")) { result =>
      result shouldBe true
      whenReady(engine.countPasswords(3)) { count =>
        count shouldBe 1
      }
    }
  }

  test("invalidate password should work") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.invalidatePassword(1)) { response =>
      response shouldBe true
      whenReady(engine.invalidatePassword(1)) { response2 =>
        response2 shouldBe false
      }
    }
  }

  test("should say password is expired when status is active and not expired") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.isPasswordExpired(5, clock.now(), 90)){res =>
      res should be (true)
    }
  }

  test("should say password is not expired") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.isPasswordExpired(1, clock.now(), 90)){res =>
      res should be (false)
    }
  }

  test("should not say password is expired when status is not active") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.isPasswordExpired(4, clock.now(), 90)){res =>
      res should be (false)
    }
  }

  test("should delete password correctly") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.deletePassword(1)){res =>
      res shouldBe true
      whenReady(engine.countPasswords(1)) { count =>
        count shouldBe 0
      }
    }
  }
}
