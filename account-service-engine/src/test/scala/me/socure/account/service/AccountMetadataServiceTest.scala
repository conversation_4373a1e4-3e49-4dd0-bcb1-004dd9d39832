package me.socure.account.service

import me.socure.common.clock.Clock
import me.socure.storage.slick.dao.DaoAccountMetadata
import me.socure.storage.slick.tables.account.DtoAccountMetadata
import org.joda.time.DateTime
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfter, FlatSpec, Matchers}
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class AccountMetadataServiceTest extends FlatSpec with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  implicit val ec: ExecutionContext = ExecutionContext.global
  
  val daoAccountMetadata: DaoAccountMetadata = mock[DaoAccountMetadata]
  val service = new AccountMetadataService(daoAccountMetadata)
  
  val now = new DateTime(2023, 1, 1, 0, 0)
  
  before {
    reset(daoAccountMetadata)
  }
  
  "AccountMetadataService" should "create new metadata when it doesn't exist" in {
    val accountId = 1L
    val childId = "child123"
    val createdBy = "<EMAIL>"
    
    val metadata = DtoAccountMetadata(
      id = 1L,
      accountId = accountId,
      childId = childId,
      createdAt = now,
      updatedAt = now,
      createdBy = createdBy,
      updatedBy = createdBy
    )
    
    when(daoAccountMetadata.getByAccountId(accountId)).thenReturn(Future.successful(None))
    when(daoAccountMetadata.create(accountId, childId, createdBy)).thenReturn(Future.successful(metadata))
    
    val result = service.createOrUpdate(accountId, childId, createdBy)
    
    whenReady(result) { resultMetadata =>
      resultMetadata shouldBe metadata
    }
    
    verify(daoAccountMetadata).getByAccountId(accountId)
    verify(daoAccountMetadata).create(accountId, childId, createdBy)
    verify(daoAccountMetadata, never()).update(accountId, childId, createdBy)
  }
  
  it should "update existing metadata when it exists" in {
    val accountId = 1L
    val childId = "child123"
    val updatedBy = "<EMAIL>"
    
    val existingMetadata = DtoAccountMetadata(
      id = 1L,
      accountId = accountId,
      childId = "oldChild",
      createdAt = now,
      updatedAt = now,
      createdBy = "<EMAIL>",
      updatedBy = "<EMAIL>"
    )
    
    val updatedMetadata = existingMetadata.copy(
      childId = childId,
      updatedAt = now,
      updatedBy = updatedBy
    )
    
    when(daoAccountMetadata.getByAccountId(accountId)).thenReturn(Future.successful(Some(existingMetadata)))
    when(daoAccountMetadata.update(accountId, childId, updatedBy)).thenReturn(Future.successful(Some(updatedMetadata)))
    
    val result = service.createOrUpdate(accountId, childId, updatedBy)
    
    whenReady(result) { resultMetadata =>
      resultMetadata shouldBe updatedMetadata
    }
    
    verify(daoAccountMetadata).getByAccountId(accountId)
    verify(daoAccountMetadata, never()).create(accountId, childId, updatedBy)
    verify(daoAccountMetadata).update(accountId, childId, updatedBy)
  }
  
  it should "get metadata for an account" in {
    val accountId = 1L
    val metadata = DtoAccountMetadata(
      id = 1L,
      accountId = accountId,
      childId = "child123",
      createdAt = now,
      updatedAt = now,
      createdBy = "<EMAIL>",
      updatedBy = "<EMAIL>"
    )
    
    when(daoAccountMetadata.getByAccountId(accountId)).thenReturn(Future.successful(Some(metadata)))
    
    val result = service.getByAccountId(accountId)
    
    whenReady(result) { resultMetadata =>
      resultMetadata shouldBe Some(metadata)
    }
    
    verify(daoAccountMetadata).getByAccountId(accountId)
  }
  
  it should "update metadata for an account" in {
    val accountId = 1L
    val childId = "child123"
    val updatedBy = "<EMAIL>"
    
    val updatedMetadata = DtoAccountMetadata(
      id = 1L,
      accountId = accountId,
      childId = childId,
      createdAt = now,
      updatedAt = now,
      createdBy = "<EMAIL>",
      updatedBy = updatedBy
    )
    
    when(daoAccountMetadata.update(accountId, childId, updatedBy)).thenReturn(Future.successful(Some(updatedMetadata)))
    
    val result = service.update(accountId, childId, updatedBy)
    
    whenReady(result) { resultMetadata =>
      resultMetadata shouldBe Some(updatedMetadata)
    }
    
    verify(daoAccountMetadata).update(accountId, childId, updatedBy)
  }
  
  it should "delete metadata for an account" in {
    val accountId = 1L
    
    when(daoAccountMetadata.delete(accountId)).thenReturn(Future.successful(1))
    
    val result = service.delete(accountId)
    
    whenReady(result) { count =>
      count shouldBe 1
    }
    
    verify(daoAccountMetadata).delete(accountId)
  }
}
