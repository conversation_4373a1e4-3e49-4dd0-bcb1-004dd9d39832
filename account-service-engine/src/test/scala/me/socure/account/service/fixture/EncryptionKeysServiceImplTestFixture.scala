package me.socure.account.service.fixture

import java.nio.ByteBuffer
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.model.{DecryptRequest, DecryptResult, EncryptRequest, EncryptResult, GenerateDataKeyRequest, GenerateDataKeyResult}
import me.socure.account.service.EncryptionKeysServiceImpl.EncryptionContextAccountId
import me.socure.account.service.fixture.EncryptionKeysServiceImplTestFixture.{dataKeyResult, encryptReq, encryptRes, randBytes}
import me.socure.common.clock.FakeClock
import me.socure.model.encryption.{AccountId, _}
import me.socure.storage.slick.tables.account.model.DtoServiceEncryptionKey
import me.socure.storage.slick.tables.account.{DtoAccount, DtoEncryptionKey}

import scala.collection.JavaConverters._
import scala.util.Random

/**
  * Created by jamesanto on 4/18/17.
  */
object EncryptionKeysServiceImplTestFixture {
  val kmsIdApEast1 = KmsId("arn:aws:kms:ap-east-1:************:alias/client-specific-encryption-stage")
  val kmsIdUsEast1 = KmsId("arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage")
  val kmsIdUsWest1 = KmsId("arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage")
  val kmsIdUsEast1Service1 = KmsId("arn:aws:kms:us-east-1:************:alias/service1-specific-encryption-stage")
  val kmsIdUsWest1Service1 = KmsId("arn:aws:kms:us-west-1:************:alias/service1-specific-encryption-stage")
  val kmsIdUsEast1Service2 = KmsId("arn:aws:kms:us-east-1:************:alias/service2-specific-encryption-stage")
  val kmsIdUsWest1Service2 = KmsId("arn:aws:kms:us-west-1:************:alias/service2-specific-encryption-stage")
  val externalKmsIdUsEast1 = KmsId("arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage")
  val externalKmsIdUsWest1 = KmsId("arn:aws:kms:us-west-1:************:alias/client-specific-encryption-stage")

  val kmsIdsConfig = KmsIdsConfig(Map(
    Regions.US_EAST_1 -> kmsIdUsEast1,
    Regions.US_WEST_1 -> kmsIdUsWest1
  ))

  val kmsIdsConfigService1 = KmsIdsConfig(Map(
    Regions.US_EAST_1 -> kmsIdUsEast1Service1,
    Regions.US_WEST_1 -> kmsIdUsWest1Service1
  ))

  val kmsIdsConfigService2 = KmsIdsConfig(Map(
    Regions.US_EAST_1 -> kmsIdUsEast1Service2,
    Regions.US_WEST_1 -> kmsIdUsWest1Service2
  ))

  val defaultEncryptedKeys = Map(
    Regions.US_EAST_1 -> EncryptedKey(randBytes()),
    Regions.US_WEST_1 -> EncryptedKey(randBytes())
  )

  val dataKeyLen = DataKeyLen(32) //Ref : https://docs.aws.amazon.com/encryption-sdk/latest/developer-guide/algorithms-reference.html . We use the first in the table.

  val clock = new FakeClock(0)

  val Service1EncryptionName = "Service1"
  val Service2EncryptionName = "ETL"

  case class DataKey(plainText: Array[Byte],
                     encrypted: EncryptedKey,
                     encryptRequest: EncryptRequest,
                     encryptResult: EncryptResult,
                     externalIdEncryptRequest: EncryptRequest,
                     decryptRequest: DecryptRequest,
                     decryptResult: DecryptResult,
                     externalIdDecryptResult: DecryptResult,
                     generateDataKeyRequest: GenerateDataKeyRequest,
                     externalIdGenerateDataKeyRequest: GenerateDataKeyRequest,
                     generateDataKeyResult: GenerateDataKeyResult,
                     externalIdGenerateDataKeyResult: GenerateDataKeyResult,
                     dtoKey: DtoEncryptionKey,
                     externalDtoKey: Option[DtoEncryptionKey] = None)

  case class ServiceDataKey(plainText: Array[Byte],
                            encrypted: EncryptedKey,
                            encryptRequest: EncryptRequest,
                            encryptResult: EncryptResult,
                            externalIdEncryptRequest: EncryptRequest,
                            decryptRequest: DecryptRequest,
                            decryptResult: DecryptResult,
                            externalIdDecryptResult: DecryptResult,
                            generateDataKeyRequest: GenerateDataKeyRequest,
                            externalIdGenerateDataKeyRequest: GenerateDataKeyRequest,
                            generateDataKeyResult: GenerateDataKeyResult,
                            externalIdGenerateDataKeyResult: GenerateDataKeyResult,
                            dtoKey: DtoServiceEncryptionKey,
                            externalDtoKey: Option[DtoServiceEncryptionKey] = None)


  case class AccountKeys(accountId: AccountId,
                         dataKeyUsEast1: DataKey,
                         dataKeyUsWest1: DataKey,
                         serviceKeysUsEast1: Seq[ServiceDataKey],
                         serviceKeysUsWest1: Seq[ServiceDataKey]
                         )
  case class AccountExternalKeys(accountId: AccountId,
                                 dataKey: DataKey
                                )

  def getAccountEncryptionKeys(id: AccountId): Map[Regions, DataKey] = {
    val rndBytes = randBytes()
    val generateDataKeyResult =  dataKeyResult(rndBytes, randBytes())
    val encryptedEast = EncryptedKey(generateDataKeyResult.getCiphertextBlob.array())
    val encryptedWest = EncryptedKey(encryptRes(rndBytes).getCiphertextBlob.array())
    val encryptRequestWest = encryptReq(id, kmsIdUsWest1, ByteBuffer.wrap(generateDataKeyResult.getPlaintext.array()))

    val usEast1 = DataKey(
      plainText = rndBytes,
      encrypted = encryptedEast,
      encryptRequest = encryptReq(id, kmsIdUsEast1, ByteBuffer.wrap(rndBytes)),
      encryptResult = encryptRes(rndBytes),
      externalIdEncryptRequest = encryptReq(id, externalKmsIdUsEast1, ByteBuffer.wrap(rndBytes)),
      decryptRequest = decryptReq(id, encryptedEast, kmsIdUsEast1),
      decryptResult = decryptRes(rndBytes),
      externalIdDecryptResult = decryptRes(rndBytes),
      generateDataKeyRequest = dataKeyRequest(kmsIdUsEast1, id),
      externalIdGenerateDataKeyRequest = dataKeyRequest(externalKmsIdUsEast1, id),
      generateDataKeyResult = generateDataKeyResult,
      externalIdGenerateDataKeyResult = generateDataKeyResult,
      dtoKey = dtoEncKey(id, encryptedEast, Regions.US_EAST_1, kmsIdUsEast1.value, isInternal = true),
      externalDtoKey =  Some(dtoEncKey(id, encryptedEast, Regions.US_EAST_1, externalKmsIdUsEast1.value, isInternal = false)))

    val usWest1 = DataKey(
      plainText = rndBytes,
      encryptRequest = encryptRequestWest,
      encryptResult = encryptRes(rndBytes),
      externalIdEncryptRequest = encryptReq(id, externalKmsIdUsWest1, ByteBuffer.wrap(generateDataKeyResult.getPlaintext.array())),
      decryptRequest = decryptReq(id, encryptedEast, kmsIdUsWest1),
      decryptResult = decryptRes(rndBytes),
      externalIdDecryptResult = decryptRes(rndBytes),
      generateDataKeyRequest = dataKeyRequest(kmsIdUsWest1, id),
      externalIdGenerateDataKeyRequest = dataKeyRequest(externalKmsIdUsWest1, id),
      generateDataKeyResult =  dataKeyResult(randBytes(), randBytes()),
      externalIdGenerateDataKeyResult = generateDataKeyResult,
      encrypted = encryptedWest,
      dtoKey = dtoEncKey(id, encryptedWest, Regions.US_WEST_1, kmsIdUsWest1.value, isInternal = true),
      externalDtoKey =  Some(dtoEncKey(id, encryptedWest, Regions.US_WEST_1, externalKmsIdUsWest1.value, isInternal = false)))

    Map(Regions.US_EAST_1 -> usEast1, Regions.US_WEST_1 -> usWest1)
  }

  def getServiceEncryptionKeysForAccount(id: AccountId, serviceName: String, internalKmsIdEast: KmsId, internalKmsIdWest: KmsId, externalKmsIdEast: KmsId, externalKmsIdWest: KmsId): Map[Regions, Map[String, ServiceDataKey]] = {
    val rndBytes = randBytes()
    val generateDataKeyResult = dataKeyResult(rndBytes, randBytes())
    val encryptedEast = EncryptedKey(generateDataKeyResult.getCiphertextBlob.array())
    val encryptedWest = EncryptedKey(encryptRes(rndBytes).getCiphertextBlob.array())

    val usEastServiceDataKey = ServiceDataKey(
      plainText = rndBytes,
      encrypted = encryptedEast,
      encryptRequest = encryptReq(id, internalKmsIdEast, ByteBuffer.wrap(rndBytes)),
      encryptResult = encryptRes(rndBytes),
      externalIdEncryptRequest = encryptReq(id, externalKmsIdEast, ByteBuffer.wrap(rndBytes)),
      decryptRequest = decryptReq(id, encryptedEast, internalKmsIdEast),
      decryptResult = decryptRes(rndBytes),
      externalIdDecryptResult = decryptRes(rndBytes),
      generateDataKeyRequest = dataKeyRequest(internalKmsIdEast, id),
      externalIdGenerateDataKeyRequest = dataKeyRequest(externalKmsIdEast, id),
      generateDataKeyResult =  generateDataKeyResult,
      externalIdGenerateDataKeyResult = generateDataKeyResult,
      dtoKey = dtoServiceEncKey(id, encryptedEast, Regions.US_EAST_1, internalKmsIdEast.value, isInternal = true, serviceName, isMultiRegionKms = true),
      externalDtoKey =  Some(dtoServiceEncKey(id, encryptedEast, Regions.US_EAST_1,
        externalKmsIdEast.value, isInternal = false, serviceName, isMultiRegionKms = false)))

    val usWestServiceDataKey = ServiceDataKey(
      plainText = rndBytes,
      encryptRequest = encryptReq(id, internalKmsIdWest, ByteBuffer.wrap(generateDataKeyResult.getPlaintext.array())),
      encryptResult = encryptRes(rndBytes),
      externalIdEncryptRequest = encryptReq(id, externalKmsIdWest, ByteBuffer.wrap(generateDataKeyResult.getPlaintext.array())),
      decryptRequest = decryptReq(id, encryptedEast, internalKmsIdWest),
      decryptResult = decryptRes(rndBytes),
      externalIdDecryptResult = decryptRes(rndBytes),
      generateDataKeyRequest = dataKeyRequest(internalKmsIdWest, id),
      externalIdGenerateDataKeyRequest = dataKeyRequest(externalKmsIdWest, id),
      generateDataKeyResult =  dataKeyResult(randBytes(), randBytes()),
      externalIdGenerateDataKeyResult = generateDataKeyResult,
      encrypted = encryptedWest,
      dtoKey = dtoServiceEncKey(id, encryptedWest, Regions.US_WEST_1, internalKmsIdWest.value, isInternal = true, serviceName, isMultiRegionKms = true),
      externalDtoKey =  Some(dtoServiceEncKey(id, encryptedWest, Regions.US_WEST_1,
        externalKmsIdWest.value, isInternal = false, serviceName, isMultiRegionKms = false)))

    Map(Regions.US_EAST_1 -> Map(
      serviceName -> usEastServiceDataKey
    ),
      Regions.US_WEST_1 ->  Map(
        serviceName -> usWestServiceDataKey
      ) )
  }

  def generateTestAccount(id: Long): AccountKeys = {
    val Service1DataKeysAccount = getServiceEncryptionKeysForAccount(AccountId(id), Service1EncryptionName, kmsIdUsEast1Service1, kmsIdUsWest1Service1, externalKmsIdUsEast1, externalKmsIdUsWest1)
    val Service2DataKeysAccount = getServiceEncryptionKeysForAccount(AccountId(id), Service2EncryptionName, kmsIdUsEast1Service2, kmsIdUsWest1Service2, externalKmsIdUsEast1, externalKmsIdUsWest1)
    val keys1 = getAccountEncryptionKeys(AccountId(id))
    val serviceKeys1 = Map(
      Regions.US_EAST_1 -> Map(
        Service1EncryptionName -> Service1DataKeysAccount(Regions.US_EAST_1)(Service1EncryptionName),
        Service2EncryptionName -> Service2DataKeysAccount1(Regions.US_EAST_1)(Service2EncryptionName)
      ), Regions.US_WEST_1 ->  Map(
        Service1EncryptionName -> Service1DataKeysAccount(Regions.US_WEST_1)(Service1EncryptionName),
        Service2EncryptionName -> Service2DataKeysAccount(Regions.US_WEST_1)(Service2EncryptionName)
      )
    )
    AccountKeys(accountId = AccountId(id),
      dataKeyUsEast1 = keys1(Regions.US_EAST_1),
      dataKeyUsWest1 = keys1(Regions.US_WEST_1),
      serviceKeysUsEast1 = Seq(
        serviceKeys1(Regions.US_EAST_1)(Service1EncryptionName),
        serviceKeys1(Regions.US_EAST_1)(Service2EncryptionName)
      ),
      serviceKeysUsWest1 = Seq(
        serviceKeys1(Regions.US_WEST_1)(Service1EncryptionName),
        serviceKeys1(Regions.US_WEST_1)(Service2EncryptionName)
      )
    )
  }

  private val Service1DataKeysAccount1 = getServiceEncryptionKeysForAccount(AccountId(1), Service1EncryptionName, kmsIdUsEast1Service1, kmsIdUsWest1Service1, externalKmsIdUsEast1, externalKmsIdUsWest1)
  private val Service2DataKeysAccount1 = getServiceEncryptionKeysForAccount(AccountId(1), Service2EncryptionName, kmsIdUsEast1Service2, kmsIdUsWest1Service2, externalKmsIdUsEast1, externalKmsIdUsWest1)
  val keys1 = getAccountEncryptionKeys(AccountId(1))
  val serviceKeys1 = Map(
    Regions.US_EAST_1 -> Map(
    Service1EncryptionName -> Service1DataKeysAccount1(Regions.US_EAST_1)(Service1EncryptionName),
    Service2EncryptionName -> Service2DataKeysAccount1(Regions.US_EAST_1)(Service2EncryptionName)
  ), Regions.US_WEST_1 ->  Map(
      Service1EncryptionName -> Service1DataKeysAccount1(Regions.US_WEST_1)(Service1EncryptionName),
      Service2EncryptionName -> Service2DataKeysAccount1(Regions.US_WEST_1)(Service2EncryptionName)
    )
  )
  val account1 = AccountKeys(accountId = AccountId(1),
    dataKeyUsEast1 = keys1(Regions.US_EAST_1),
    dataKeyUsWest1 = keys1(Regions.US_WEST_1),
    serviceKeysUsEast1 = Seq(
      serviceKeys1(Regions.US_EAST_1)(Service1EncryptionName),
      serviceKeys1(Regions.US_EAST_1)(Service2EncryptionName)
    ),
    serviceKeysUsWest1 = Seq(
      serviceKeys1(Regions.US_WEST_1)(Service1EncryptionName),
      serviceKeys1(Regions.US_WEST_1)(Service2EncryptionName)
    )
  )

  private val Service1DataKeysAccount2 = getServiceEncryptionKeysForAccount(AccountId(2), Service1EncryptionName, kmsIdUsEast1Service1, kmsIdUsWest1Service1, externalKmsIdUsEast1, externalKmsIdUsWest1)
  private val Service2DataKeysAccount2 = getServiceEncryptionKeysForAccount(AccountId(2), Service2EncryptionName, kmsIdUsEast1Service2, kmsIdUsWest1Service2, externalKmsIdUsEast1, externalKmsIdUsWest1)
  val keys2 = getAccountEncryptionKeys(AccountId(2))
  val serviceKeys2 = Map(
    Regions.US_EAST_1 -> Map(
      Service1EncryptionName -> Service1DataKeysAccount2(Regions.US_EAST_1)(Service1EncryptionName),
      Service2EncryptionName -> Service2DataKeysAccount2(Regions.US_EAST_1)(Service2EncryptionName)
    ), Regions.US_WEST_1 ->  Map(
      Service1EncryptionName -> Service1DataKeysAccount2(Regions.US_WEST_1)(Service1EncryptionName),
      Service2EncryptionName -> Service2DataKeysAccount2(Regions.US_WEST_1)(Service2EncryptionName)
    )
  )
  val account2 = AccountKeys(accountId = AccountId(2),
    dataKeyUsEast1 = keys2(Regions.US_EAST_1),
    dataKeyUsWest1 = keys2(Regions.US_WEST_1),
    serviceKeysUsEast1 = Seq(
      serviceKeys2(Regions.US_EAST_1)(Service1EncryptionName),
      serviceKeys2(Regions.US_EAST_1)(Service2EncryptionName)
    ),
    serviceKeysUsWest1 = Seq(
      serviceKeys2(Regions.US_WEST_1)(Service1EncryptionName),
      serviceKeys2(Regions.US_WEST_1)(Service2EncryptionName)
    ))

  private val Service1DataKeysAccount3 = getServiceEncryptionKeysForAccount(AccountId(3), Service1EncryptionName, kmsIdUsEast1Service1, kmsIdUsWest1Service1, externalKmsIdUsEast1, externalKmsIdUsWest1)
  private val Service2DataKeysAccount3 = getServiceEncryptionKeysForAccount(AccountId(3), Service2EncryptionName, kmsIdUsEast1Service2, kmsIdUsWest1Service2, externalKmsIdUsEast1, externalKmsIdUsWest1)
  val serviceKeys3 = Map(
    Regions.US_EAST_1 -> Map(
      Service1EncryptionName -> Service1DataKeysAccount3(Regions.US_EAST_1)(Service1EncryptionName),
      Service2EncryptionName -> Service2DataKeysAccount3(Regions.US_EAST_1)(Service2EncryptionName)
    ), Regions.US_WEST_1 ->  Map(
      Service1EncryptionName -> Service1DataKeysAccount3(Regions.US_WEST_1)(Service1EncryptionName),
      Service2EncryptionName -> Service2DataKeysAccount3(Regions.US_WEST_1)(Service2EncryptionName)
    )
  )
  val keys3 = getAccountEncryptionKeys(AccountId(3))
  val account3 = AccountKeys(accountId = AccountId(3),
    dataKeyUsEast1 = keys3(Regions.US_EAST_1),
    dataKeyUsWest1 = keys3(Regions.US_WEST_1),
    serviceKeysUsEast1 = Seq(
      serviceKeys3(Regions.US_EAST_1)(Service1EncryptionName),
      serviceKeys3(Regions.US_EAST_1)(Service2EncryptionName)
    ),
    serviceKeysUsWest1 = Seq(
      serviceKeys3(Regions.US_WEST_1)(Service1EncryptionName),
      serviceKeys3(Regions.US_WEST_1)(Service2EncryptionName)
    ))

  val keys4 = getAccountEncryptionKeys(AccountId(4))
  val account4 = AccountKeys(accountId = AccountId(4),
    dataKeyUsEast1 = keys4(Regions.US_EAST_1),
    dataKeyUsWest1 = keys4(Regions.US_WEST_1),
    serviceKeysUsEast1 = Seq.empty,
    serviceKeysUsWest1 = Seq.empty)

  val externalValidKmsId = KmsId("arn:aws:kms:us-east-1:************:key/my-key")

  val parentAccount: DtoAccount = DtoAccount(accountId = 1,
    name = "AccountName1",
    industrySector = "101-205",
    isInternal = false,
    isActive = true,
    parentId = None,
    isDeleted = false,
    firstActivatedAt = None,
    publicId = "123213",
    publicApiKey = "publicApiKey1"
  )

  val subAccount: DtoAccount = DtoAccount(accountId = 2,
    name = "AccountName2",
    industrySector = "101-205",
    isInternal = false,
    isActive = true,
    parentId = Some(1),
    isDeleted = false,
    firstActivatedAt = None,
    publicId = "123214",
    publicApiKey = "publicApiKey2"
  )

  val subAccount2: DtoAccount = DtoAccount(accountId = 3,
    name = "AccountName2",
    industrySector = "101-205",
    isInternal = false,
    isActive = true,
    parentId = Some(1),
    isDeleted = false,
    firstActivatedAt = None,
    publicId = "123214",
    publicApiKey = "publicApiKey2"
  )

  def generatedKeys(accountKeys: AccountKeys) = Map(
    Regions.US_EAST_1 -> accountKeys.dataKeyUsEast1.encrypted,
    Regions.US_WEST_1 -> accountKeys.dataKeyUsWest1.encrypted
  )

  def dtoKeys(accountKeys: AccountKeys) = Seq(accountKeys.dataKeyUsEast1.dtoKey, accountKeys.dataKeyUsWest1.dtoKey)
  def dtoServiceKeys(accountKeys: AccountKeys, serviceName: String): Seq[DtoServiceEncryptionKey] = {
    Seq(
      accountKeys.serviceKeysUsEast1.find(_.dtoKey.serviceName == serviceName),
      accountKeys.serviceKeysUsWest1.find(_.dtoKey.serviceName == serviceName)
    ).flatten.map(_.dtoKey)
  }
  def externalDtoKeys(accountKeys: AccountKeys) = Seq(accountKeys.dataKeyUsEast1.externalDtoKey.get, accountKeys.dataKeyUsWest1.externalDtoKey.get)
  def externalServiceDtoKeys(accountKeys: AccountKeys): Seq[DtoServiceEncryptionKey] = Seq(
    accountKeys.serviceKeysUsEast1,
    accountKeys.serviceKeysUsWest1
  ).flatMap(key => {
    key.map(_.externalDtoKey)
  }).flatten
  def dtoKeys1(accountKeys: AccountExternalKeys) = Seq(accountKeys.dataKey.dtoKey)

  private def randBytes(): Array[Byte] = {
    val bytes = new Array[Byte](256)
    Random.nextBytes(bytes)
    bytes
  }

  private def dataKeyRequest(kmsId: KmsId, accountId: AccountId): GenerateDataKeyRequest = {
    new GenerateDataKeyRequest()
      .withKeyId(kmsId.value)
      .withNumberOfBytes(dataKeyLen.value)
      .withEncryptionContext(Map(
        EncryptionContextAccountId -> String.valueOf(accountId.value)
      ).asJava)
  }

  private def dataKeyResult(encBytes: Array[Byte], plainTextBytes: Array[Byte]): GenerateDataKeyResult = {
    new GenerateDataKeyResult()
      .withPlaintext(ByteBuffer.wrap(plainTextBytes))
      .withCiphertextBlob(ByteBuffer.wrap(encBytes))
  }

  private def encryptReq(accountId: AccountId, kmsId: KmsId, dataKey: ByteBuffer): EncryptRequest = {
    new EncryptRequest()
      .withKeyId(kmsId.value)
      .withPlaintext(dataKey)
      .withEncryptionContext(Map(
        EncryptionContextAccountId -> String.valueOf(accountId.value)
      ).asJava)
  }

  private def encryptRes(bytes: Array[Byte]): EncryptResult = {
    new EncryptResult()
      .withCiphertextBlob(ByteBuffer.wrap(bytes))
  }

  def decryptReq(accountId: AccountId, encryptedKey: EncryptedKey, kmsId: KmsId): DecryptRequest = {
    new DecryptRequest()
      .withKeyId(kmsId.value)
      .withCiphertextBlob(ByteBuffer.wrap(encryptedKey.value))
      .withEncryptionContext(Map(
        EncryptionContextAccountId -> String.valueOf(accountId.value)
      ).asJava)
  }

  private def decryptRes(bytes: Array[Byte]): DecryptResult = {
    new DecryptResult()
      .withPlaintext(ByteBuffer.wrap(bytes))
  }

  private def dtoEncKey(accountId: AccountId, encryptedKey: EncryptedKey, region: Regions, arn: String, isInternal: Boolean): DtoEncryptionKey = {
    val dtoEncryptionKey = DtoEncryptionKey(
      id = 0,
      accountId = accountId.value,
      encryptionKey = encryptedKey.value,
      arn = arn,
      region = region,
      isActive = true,
      isInternal = isInternal,
      createdAt = clock.now(),
      updatedAt = clock.now()
    )
    dtoEncryptionKey
  }

  private def dtoServiceEncKey(accountId: AccountId, encryptedKey: EncryptedKey, region: Regions, arn: String, isInternal: Boolean, serviceName: String, isMultiRegionKms: Boolean): DtoServiceEncryptionKey = {
    val dtoEncryptionKey = DtoServiceEncryptionKey(
      id = 0,
      accountId = accountId.value,
      encryptionKey = encryptedKey.value,
      kmsArn = arn,
      region = region,
      isActive = true,
      isInternalKms = isInternal,
      createdAt = clock.now(),
      updatedAt = clock.now(),
      serviceName = serviceName,
      isMultiRegionKms = isMultiRegionKms
    )
    dtoEncryptionKey
  }
}
