package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.watchlist.HistoricalRange
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.validator.V2Validator
import me.socure.common.clock.RealClock
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferences, WatchlistCategories, WatchlistPreference}
import me.socure.model.dashboardv2.Creator
import me.socure.model.kyc.KycAddressMatchLogic.{KycAddressMatchLogic, default}
import me.socure.model.kyc.KycNationalIdMatchLogic.{KycNationalIdMatchLogicEnum, exact, fuzzy}
import me.socure.model.kyc.{KycNationalIdMatchLogic, KycPreferences, KycPreferencesForAccount, KycPreferencesRequest, KycPreferencesView}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.service.constants.DobMatchLogic
import me.socure.storage.slick.dao.{DaoAccountV2, DaoComplyWatchlistPreferences, DaoKycPreferences, DaoWatchlistPreferences}
import me.socure.storage.slick.tables.account.mappers.{CAWatchlistPreferenceMapper, WatchlistPreferenceMapper}
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountPreferencesServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MemcachedTestSupport {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(10, Seconds), interval = Span(500, Millis))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  val mysqlService: MysqlService = MysqlService("account-preferences-service")
  private val dbName = "socure"

  override def memcachedPodLabel(): String = "account-preferences-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))
  var service: AccountPreferencesService = null
  val watchlistPreferenceMapper = new WatchlistPreferenceMapper(new RealClock)

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoTblKycPreferences = new DaoKycPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoWatchlistService = new DaoWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val clock = new RealClock
    val daoComplyWatchlistPreferences = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val caWatchlistPreferenceMapper = new CAWatchlistPreferenceMapper(clock)
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    new AccountPreferencesService(daoTblKycPreferences, daoAccountV2, daoWatchlistService, watchlistPreferenceMapper, daoComplyWatchlistPreferences, caWatchlistPreferenceMapper, v2Validator, scalaCache, auditDetailsService)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)
    service = buildService(dataSource)
    val sqlExecutor = new SQLExecutor(dataSource)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${UserFixture.primaryUser.publicAccountId}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6')"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES" +
      s"(1, 'production')," +
      s"(2, 'development')"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES " +
      s"(1, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 1, '2018-09-12')," +
      s"(2, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 1, 2, '2018-09-12')," +
      s"(3, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 1, '2018-09-12'), "+
      s"(5, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 2, 2, '2018-09-12'), "+
      s"(6, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 3, 1, '2018-09-12'), "+
      s"(7, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 3, 2, '2018-09-12'), "+
      s"(8, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 4, 1, '2018-09-12'), "+
      s"(9, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 4, 2, '2018-09-12'), "+
      s"(10, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 6, 1, '2018-09-12'),"+
      s"(11, 'token1', 'secret 1', 'secret token 1', '0.0.0.0/0', 6, 2, '2018-09-12');"
    )

    //KYC preferences
    sqlExecutor.execute("INSERT INTO tbl_kyc_preferences (environment_id, exact_dob, is_force_inherit, inherited_by_account_id) VALUES (1, FALSE, null, null),(2, TRUE, null, null),(3, TRUE, null, null)")

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), "+
      "(2, 'v6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 6, true);"
    )

    // user_role
    sqlExecutor.execute("INSERT INTO `user_role`(id, name, description, by_business_user_id, by_account_id) VALUES " +
      "(1, 'PrimaryAdmin','Primary Administrator',1,1)," +
      "(2, 'InstanceAdmin','Instance Administrator',1,1);")

    // user_account_association
    sqlExecutor.execute("INSERT INTO user_account_association(id, business_user_id, account_id) VALUES(1, 1, 1),(2, 2, 6);")

    // user_account_role_association
    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,1,0), (2,1,0);")

    // permission_template
    sqlExecutor.execute("INSERT INTO permission_template(id, name,type,account_id,updated_by) VALUES(1,'template1',1,1,1), (2,'template2',1,1,1);")

    // permission_template_mapping
    sqlExecutor.execute("INSERT INTO permission_template_mapping(id, permission_template_id, environment_type_id, permissions) VALUES " +
      "(1, 1, 1, '1001,1002,1003,1004,1027'), " +
      "(2, 2, 1, '1009')," +
      "(3, 1, 2, '1009');")

    // role_permission_template_association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(id, permission_template_id, user_role_id) values(1,1,1),(2,2,2)")

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('1','1/',1,1,1), "+
      "('6','6/',1,1,1); ")

    // Account Hierarchy History
    sqlExecutor.execute("INSERT INTO `account_association_history`(account_hierarchy_id) VALUES ('1')")

    sqlExecutor.execute("INSERT INTO `tbl_account_permission`(account_id,permission) VALUES (1,140),(1,142),(1,143)")
    sqlExecutor.execute("INSERT INTO `tbl_account_permission`(account_id,permission) VALUES (6,140),(6,142),(6,143),(6,97)")
    sqlExecutor.execute("INSERT INTO `tbl_account_permission`(account_id,permission) VALUES (3,140),(3,143)")
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }


  test("should return default kyc preferences entry when there is none found") {
    val expected = KycPreferences(
      exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_ONE_YEAR_RADIUS_YYYY_MM_DD),
      ssnExactMatch = false
    )
    val actual = service.getKycPreference(environmentId = 5, None)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }


  test("should find kyc preferences entry - false") {
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = None,
      ssnExactMatch = true,
      nationalIdMatchLogic = Some(exact),
      creator = None,
      addressMatchLogic = None
    )
    val actual = service.getKycPreference(environmentId = 1, None)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should find kyc preferences entry - true") {
    val expected = KycPreferences(
      exactDob = Some(true),
      dobMatchLogic = None,
      ssnExactMatch = true,
      nationalIdMatchLogic = Some(exact),
      creator = None,
      addressMatchLogic = None
    )
    val actual = service.getKycPreference(environmentId = 2, None)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should save kyc preferences entry - false") {
    val environmentId = 1
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false,
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy)
    )


    val input = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false,
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy),
      Option(Creator(1, 1))
    )


    val actual = service.saveKycPreference(environmentId, input)
    whenReady(actual) { response =>
      response._2 shouldBe 'right
      response._2.fold(_ => fail, _ shouldBe input)

      val exists = service.getKycPreference(environmentId, Option(Creator(1, 1)))
      whenReady(exists) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("should save kyc preferences for accounts entry - false") {
    val environmentId = 1
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false
    )
    val kycPreferencesRequest = KycPreferencesRequest(
      KycPreferences(
        exactDob = Some(false),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        ssnExactMatch = false,
        Some(KycNationalIdMatchLogic.exact),
        Option(Creator(1, 1))
      ),
      isForceInherit = Some(false),
      accountIds = Seq(2),
      environmentTypes = Seq(1)
    )
    val actual = service.saveKycPreferenceForAccounts(environmentId, kycPreferencesRequest)
    whenReady(actual) { response =>
      response._2 shouldBe 'right
      response._2.fold(_ => fail, _ shouldBe kycPreferencesRequest)
      val exists = service.getKycPreference(environmentId, None)
      whenReady(exists) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("should return default watchlist preferences when no entry exist") {
    val environmentId = 1
    val expected = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = true,
      dobAndName = false,
      matchScore = 90,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    whenReady(service.getWatchlistPreference(environmentId = environmentId, None)) { response =>
      response shouldBe 'right
      response.right.get shouldBe expected
    }
  }

  test("should save watchlist preferences entry") {
    val environmentId = 1
    val data = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 73,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    whenReady(service.saveWatchlistPreference(data)) { response =>
      response shouldBe 'right
      response.right.get shouldBe data

      whenReady(service.getWatchlistPreference(environmentId, None)) { response2 =>
        response2 shouldBe 'right
        response2.right.get shouldBe data
      }
    }
  }

  test("should return error when watchlist match score is out of range - lesser") {
    val environmentId = 1
    val data = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 23,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    whenReady(service.saveWatchlistPreference(data)) { response =>
      response shouldBe 'left
      response.left.get shouldBe ErrorResponseFactory.get(ExceptionCodes.WlMatchScoreOutOfRange)
    }
  }

  test("should return error when watchlist match score is out of range - greater") {
    val environmentId = 1
    val data = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 105,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    whenReady(service.saveWatchlistPreference(data)) { response =>
      response shouldBe 'left
      response.left.get shouldBe ErrorResponseFactory.get(ExceptionCodes.WlMatchScoreOutOfRange)
    }
  }

  test("should return error when ca watchlist match score is out of range - greater") {
    val data = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic =Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 2,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = Option(Set("PEP")),
      country = Option(Set("US")),
      historicalRange = None,
      creator = Option(Creator(1, 1))
    )

    whenReady(service.saveCAWatchlistPreference(data)) { response =>
      response._2 shouldBe 'left
      response._2.left.get shouldBe ErrorResponseFactory.get(ExceptionCodes.CAWlMatchScoreNotInRange)
    }
  }

  test("should return error when ca watchlist match score is out of range - lesser") {
    val data = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = -1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = Option(Set("PEP")),
      country = Option(Set.empty),
      historicalRange = None,
      creator = Option(Creator(1, 1))
    )

    whenReady(service.saveCAWatchlistPreference(data)) { response =>
      response._2 shouldBe 'left
      response._2.left.get shouldBe ErrorResponseFactory.get(ExceptionCodes.CAWlMatchScoreNotInRange)
    }
  }

  test("should save ca watchlist preferences entry") {
    val environmentId = 1
    val data = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.5,
      limit = 10,
      screeningCategories = Set("sanction"),
      watchlistScreeningCategories = Option(Set("sanction")),
      country = Option(Set("us", "uk")),
      historicalRange = None,
      isWatchlistTransactionsAutoMonitored = Some(true),
      creator = Option(Creator(1, 1))
    )

    val expected = CAWatchlistPreference(
      environmentId = data.environmentId,
      exactDoB = data.exactDoB,
      dobMatchLogic = data.dobMatchLogic,
      dobAndName = data.dobAndName,
      monitoring = data.monitoring,
      matchingThresholds = data.matchingThresholds,
      limit = data.limit,
      screeningCategories = data.screeningCategories,
      watchlistScreeningCategories = data.watchlistScreeningCategories,
      country = data.country,
      historicalRange = data.historicalRange,
      isWatchlistTransactionsAutoMonitored = data.isWatchlistTransactionsAutoMonitored
    )

    whenReady(service.saveCAWatchlistPreference(data)) { response =>
      response._2 shouldBe 'right
      response._2.right.get shouldBe expected

      whenReady(service.getCAWatchListPreference(environmentId, None)) { response2 =>
        response2 shouldBe 'right
        response2.right.get shouldBe expected
      }

      whenReady(service.getCAWatchListPreferenceForAccount(1)) { response3 =>
        response3.fold(_ => fail, r => {
          r.size shouldBe  2
          r.filter(_.environmentTypeId==1).map{v =>
            v.caWatchlistPreferenceView.isWatchlistTransactionsAutoMonitored shouldBe Some(true)
          }
        })
      }
    }
  }

  test("should save ca watchlist preferences entry , with AdverseMedia") {
    val environmentId = 1
    val dataWithAdverseMedia = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.5,
      limit = 10,
      screeningCategories = Set("sanction","adverse-media"),
      watchlistScreeningCategories = Option(Set("sanction","adverse-media")),
      country = Option(Set("us", "uk")),
      historicalRange = Some(HistoricalRange.FiveYears.name)
    )

    whenReady(service.saveCAWatchlistPreference(dataWithAdverseMedia)) { response =>
      response._2.fold(_ => fail, _ shouldBe dataWithAdverseMedia.copy(historicalRange = Some(HistoricalRange.FiveYears.name)))

      whenReady(service.getCAWatchListPreference(environmentId, None)) { response0 =>
        response0.fold(_ => fail, _ shouldBe dataWithAdverseMedia.copy(historicalRange = Some(HistoricalRange.FiveYears.name)))
      }
    }
  }

  test("should save ca watchlist preferences entry , with AdverseMedia and historical range") {
    val environmentId = 1
    val dataWithAdverseMedia = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.5,
      limit = 10,
      screeningCategories = Set("sanction","adverse-media"),
      watchlistScreeningCategories = Option(Set("sanction","adverse-media")),
      country = Option(Set("us", "uk")),
      historicalRange = Some("All Time"),
      creator = Option(Creator(1, 1))
    )

    whenReady(service.saveCAWatchlistPreference(dataWithAdverseMedia)) { response =>
      response._2.fold(_ => fail, _ shouldBe dataWithAdverseMedia.copy(creator = None))

      whenReady(service.getCAWatchListPreference(environmentId, None)) { response0 =>
        response0.fold(_ => fail, _ shouldBe dataWithAdverseMedia.copy(creator = None))
      }
    }
  }

  test("should save ca watchlist preferences entry , with AdverseMedia and invalid historical range") {
    val dataWithAdverseMedia = CAWatchlistPreference(
      environmentId = 1,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.5,
      limit = 10,
      screeningCategories = Set("sanction","adverse-media"),
      watchlistScreeningCategories = Option(Set("sanction","adverse-media")),
      country = Option(Set("us", "uk")),
      historicalRange = Some("Good Time"),
      creator = Option(Creator(1, 1))
    )

    whenReady(service.saveCAWatchlistPreference(dataWithAdverseMedia)) { response =>
      response._2.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidHistoricalRange), _ => fail)
    }
  }

  test("should return default ca watchlist preferences when no entry exist") {
    val environmentId = 5
    val expected = CAWatchlistPreference(
      environmentId = 5,
      exactDoB = Some(true),
      dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.5,
      limit = 10,
      screeningCategories = Set("warning", "sanction", "fitness-probity", "pep"),
      watchlistScreeningCategories = Option(Set("warning", "sanction", "fitness-probity", "pep")),
      country = Option(Set.empty),
      historicalRange = None
    )

    whenReady(service.getCAWatchListPreference(environmentId = environmentId, None)) { response =>
      response shouldBe 'right
      response.right.get shouldBe expected
    }
  }

  test("should update an existing watchlist preference entry properly") {
    val environmentId = 2
    val data1 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 73,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    val data2 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 70,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.PEP)
    )

    whenReady(service.saveWatchlistPreference(data1)) { response =>
      response shouldBe 'right
      response.right.get shouldBe data1

      whenReady(service.getWatchlistPreference(environmentId, None)) { response2 =>
        response2 shouldBe 'right
        response2.right.get shouldBe data1

        whenReady(service.saveWatchlistPreference(data2)) { response3 =>
          response3 shouldBe 'right
          response3.right.get shouldBe data2

          whenReady(service.getWatchlistPreference(environmentId, None)) { response4 =>
            response4 shouldBe 'right
            response4.right.get shouldBe data2
          }
        }
      }
    }
  }

  ignore("should save kyc preferences v2 entry") {
    val environmentId = 1
    val kycPreferences = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false,
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.partial),
      Option(Creator(1, 1))
    )
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false,
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.partial)
    )
    val actual = service.saveKycPreference(environmentId, kycPreferences)
    whenReady(actual) { response =>
      response._2 shouldBe 'right
      response._2.fold(_ => fail, _ shouldBe kycPreferences)

      val exists = service.getKycPreference(environmentId, None)
      whenReady(exists) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("should save kyc preferences for accounts v2 entry") {
    val environmentId = 1
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false,
      creator = Option(Creator(1, 1))
    )
    val kycPreferencesRequest = KycPreferencesRequest(
      kycPreferences = expected,
      isForceInherit = Some(false),
      accountIds = Seq(1),
      environmentTypes = Seq(1)
    )
    val actual = service.saveKycPreferenceForAccounts(environmentId, kycPreferencesRequest)
    whenReady(actual) { response =>
      response._2 shouldBe 'right
      response._2.fold(_ => fail, _ shouldBe kycPreferencesRequest)

      val exists = service.getKycPreference(environmentId, None)
      whenReady(exists) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected.copy(creator = None))
      }
    }
  }

  test("should return kyc preferences with customPreferences") {
    val environmentId = 1
    val expected = KycPreferences(
      exactDob = Some(false),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = false
    )
    val preferences = service.getKycPreference(environmentId, None)
    whenReady(preferences) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should return kyc preferences with customPreferences - disabled some permissions") {
    val environmentId = 6
    val expected = KycPreferences(
      exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_ONE_YEAR_RADIUS_YYYY_MM_DD),
      ssnExactMatch = false
    )
    val preferences = service.getKycPreference(environmentId, None)
    whenReady(preferences) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should return kyc preferences with customPreferences - using subAccount") {
    val environmentId = 8
    val expected = KycPreferences(
      exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_ONE_YEAR_RADIUS_YYYY_MM_DD),
      ssnExactMatch = false
    )
    val preferences = service.getKycPreference(environmentId, None)
    whenReady(preferences) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should update an existing watchlist preference v2 entry properly") {
    val environmentId = 2
    val data1 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 73,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP),
      Option(Creator(1, 1))
    )

    val res1 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 73,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    val data2 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 70,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.PEP),
      Option(Creator(1, 1))
    )

    val res2 = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = false,
      dobAndName = false,
      matchScore = 70,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.PEP)
    )

    whenReady(service.saveWatchlistPreference(data1)) { response =>
      response shouldBe 'right
      response.right.get shouldBe data1
      whenReady(service.getWatchlistPreference(environmentId, None)) { response2 =>
        response2 shouldBe 'right
        response2.right.get shouldBe res1

        whenReady(service.saveWatchlistPreference(data2)) { response3 =>
          response3 shouldBe 'right
          response3.right.get shouldBe data2

          whenReady(service.getWatchlistPreference(environmentId, None)) { response4 =>
            response4 shouldBe 'right
            response4.right.get shouldBe res2
          }
        }
      }
    }
  }

  test("get all historical range values") {
    whenReady(service.getHistoricalRange()){ response =>
      response.isEmpty shouldBe false
    }
  }

  test("should return kyc preferences for an account") {
    val expected = List(
      KycPreferencesForAccount(
        1,
        KycPreferencesView(
          Some(false),
          None,
          true,
          KycNationalIdMatchLogic.partial.toString,
          "default"
        )
      ),
      KycPreferencesForAccount(
        2,
        KycPreferencesView(
          Some(true),
          None,
          true,
          KycNationalIdMatchLogic.partial.toString,
          "default"
        )
      )
    )

    val actual = service.getKycPreferenceForAccount(accountId = 1)
    whenReady(actual) { response =>
      response.fold(_ => fail, _.size shouldBe 2)
    }
  }

  test("should return kyc preferences for an invalid account") {
    val actual = service.getKycPreferenceForAccount(accountId = 200)
    whenReady(actual) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UnknownError), _ => fail)
    }
  }

  test("should return CA watchlist permissions, if it has only CREATE_TRANSACTION permission"){
    val expected = CAWatchlistPreferences(
      CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
        Set("warning", "sanction", "fitness-probity"), Some(Set("warning", "sanction", "fitness-probity")), Some(Set()),None,None,Some(false),Some(false)),
      CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
        Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,None,Some(false),Some(false)),
      CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
        Set("warning", "pep", "sanction", "adverse-media", "fitness-probity"),Some(Set("warning", "pep", "sanction", "adverse-media", "fitness-probity")),Some(Set()),None,None,Some(false),Some(false)))
    whenReady(service.getCAWatchListPreferences(11, Some(Creator(2, 6)))) { response =>
      response.fold(_ => fail, _ => expected)
    }
  }

  test("should return ca watchlist preferences for valid account VIEW_SETTINGS permission"){
      val expected = CAWatchlistPreferences(
        CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
          Set("warning", "sanction", "fitness-probity"), Some(Set("warning", "sanction", "fitness-probity")), Some(Set()),None,None,Some(false),Some(false)),
        CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
          Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,None,Some(false),Some(false)),
        CAWatchlistPreference(Some(true),11,Some(true),Some("one_year_radius_yyyy_mm_dd"),true,false,0.5,10,
          Set("warning", "pep", "sanction", "adverse-media", "fitness-probity"),Some(Set("warning", "pep", "sanction", "adverse-media", "fitness-probity")),Some(Set()),None,None,Some(false),Some(false)))
      whenReady(service.getCAWatchListPreferences(10, Some(Creator(2, 6)))) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }

}