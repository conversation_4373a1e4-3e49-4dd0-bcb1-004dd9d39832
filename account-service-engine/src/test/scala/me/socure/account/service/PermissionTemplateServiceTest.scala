package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.PermissionTemplate
import me.socure.model.account.{PermissionTemplateMapping, PermissionTemplateMappingInput}
import me.socure.model.dashboardv2.Creator
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}

/**
 * <AUTHOR> Kumar
 */
class PermissionTemplateServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: PermissionTemplateService = _
  override val mysqlService: MysqlService = MysqlService("permission-template-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildPermissionTemplateService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get permission template mappings by template id - Success") {
    val userId = 1
    val accountId = 1
    whenReady(service.getPermissionTemplateMappingsByTemplateId(1, userId, accountId)) { response =>
      val permissionTemplateMappingSeq = Seq(PermissionTemplateMapping(2,1,0,Set(1031, 1005, 1001, 2002, 2011, 1032, 1006, 1028, 1002, 1035, 2001, 1007, 1029, 1034, 1003, 2012, 1008, 2009, 1030, 2008, 1033, 1004),None), PermissionTemplateMapping(1,1,1,Set(1031, 1001, 1013, 1012, 1027, 1028, 1002, 1026, 1011, 1029, 1003, 1030, 1004, 1014),None))
      response shouldBe Right(permissionTemplateMappingSeq)
    }
  }

  test("Get permission template mappings by template id - No data") {
    val userId = 1
    val accountId = 1
    whenReady(service.getPermissionTemplateMappingsByTemplateId(100, userId, accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissionTemplateId))
    }
  }

  test("Insert permission template mapping - success") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 2, Set(1001,1002,1003), Some(Set(10)))
    whenReady(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Insert permission template mapping - failure") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 1, Set.empty, None)
    whenReady(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertPermissionTemplateMapping))
    }
  }

  test("Insert permission template mapping - invalid environment type") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 3, 4, Set(1001,1002,1003), None)
    whenReady(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided))
    }
  }

  test("Insert permission template mapping - invalid permission id") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 4, Set(1,2,3,1000), None)
    whenReady(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissionIdProvided))
    }
  }

  test("Update permission template mapping - success") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(Some(1), Creator(1,1), 1, 1, Set(1001,1002,1003), None)
    whenReady(service.updatePermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Right(1)
    }
  }

  test("Update permission template mapping - failure") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(Some(10), Creator(1,1), 1, 1, Set.empty, None)
    whenReady(service.updatePermissionTemplateMapping(permissionTemplateMappingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdatePermissionTemplateMapping))
    }
  }

  test("delete permission template mapping - success") {
    val permissionTemplateId = 1
    val environmentTypeId = 1
    val userId = 1
    val accountId = 1
    whenReady(service.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)) { response =>
      response shouldBe Right(1)
    }
  }

  test("delete permission template mapping - failure") {
    val permissionTemplateId = 1
    val environmentTypeId = 1
    val userId = 1
    val accountId = 1
    whenReady(service.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeletePermissionTemplateMapping))
    }
  }

  test("delete permission template mapping - invalid environment type") {
    val permissionTemplateId = 1
    val environmentTypeId = 4
    val userId = 1
    val accountId = 1
    whenReady(service.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided))
    }
  }

  test("Get permission template by template id - Success") {
    val userId = 1
    val accountId = 1
    whenReady(service.getPermissionTemplate(1, userId, accountId)) { response =>
      response.fold(_ => fail, r => {
        r.templateType shouldBe(1)
        r.name shouldBe("template1")
        r.updatedBy shouldBe(1)
        r.id shouldBe(1)
      })
    }
  }

  test("Get permission template by template id - Fail") {
    val userId = 1
    val accountId = 1
    whenReady(service.getPermissionTemplate(100, userId, accountId)) { response =>
      response.fold(_ => ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateFetchFailed), _ => fail)
    }
  }

  test("Insert permission template by template id - Success") {
    whenReady(service.insertPermissionTemplate(PermissionTemplate(id = 0, name = "TemplateName", updatedBy = 1,  templateType = 1, accountId = 1,  updatedAt = None))) { response =>
      response.fold(_ => fail,  _ => Right(1))
    }
  }

  test("Insert permission template by template id - Fail for invalid template type") {
    whenReady(service.insertPermissionTemplate(PermissionTemplate(id = 0, name = "TemplateName", updatedBy = 1,  templateType = 100, accountId = 1,  updatedAt = None))) { response =>
      response.fold(_ => ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateCreateFailed), _ => fail)
    }
  }

  test("Update permission template by template id - Success") {
    whenReady(service.updatePermissionTemplate(PermissionTemplate(id =5, name = "ModifiedName", updatedBy = 1,  templateType = 1, accountId = 1,  updatedAt = None))) { response =>
      response.fold(_ => fail, _ => Right(1))
    }
  }

  test("Update permission template by template id - Fail") {
    whenReady(service.updatePermissionTemplate(PermissionTemplate(id = 100, name = "ModifiedName", updatedBy = 1,  templateType = 1, accountId = 1,  updatedAt = None))) { response =>
      response.fold(_ => ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed), _ => fail)
    }
  }

  test("Update permission template by template id - Fail for invalid template type") {
    whenReady(service.updatePermissionTemplate(PermissionTemplate(id = 1, name = "ModifiedName", updatedBy = 1,  templateType = 100, accountId = 1,  updatedAt = None))) { response =>
      response.fold(_ => ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed), _ => fail)
    }
  }

  test("Delete permission template by template id - Success") {
    val userId = 1
    val accountId = 1
    whenReady(service.deletePermissionTemplate(3, userId, accountId)) { response =>
      response.fold(_ => fail, _ => Right(1))
    }
  }

  test("Delete permission template by template id - Fail") {
    val userId = 1
    val accountId = 1
    whenReady(service.deletePermissionTemplate(100, userId, accountId)) { response =>
      response.fold(_ => ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateDeleteFailed), _ => fail)
    }
  }

  test("Get permission templates for user association id - Success") {
    whenReady(service.getPermissionTemplates(1)) { response =>
      response.fold(_ => fail, r => {
        r.isEmpty shouldBe(false)
        r.exists(_.name.equals("template1")) shouldBe(true)
        r.exists(_.name.equals("template2")) shouldBe(true)
        r.exists(_.name.equals("template4")) shouldBe(true)
      })
    }
  }

  test("Get permission templates for user association id - should return empty list") {
    whenReady(service.getPermissionTemplates(100)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateFetchFailed))
    }
  }
}