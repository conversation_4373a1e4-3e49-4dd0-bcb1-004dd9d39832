package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.constants.DashboardUserPermissions
import me.socure.model.dashboardv2.Creator
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}

/**
 * <AUTHOR> Kumar
 */
class V2ValidationServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: V2ValidationService = _
  override val mysqlService: MysqlService = MysqlService("v2-validation-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildV2ValidationService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Is permission available - success") {
    whenReady(service.isPermissionAvailable(1,Set(DashboardUserPermissions.ACCOUNTS_CREATE.id.toString),Creator(1,1))) { response =>
      response shouldBe Right(true)
    }
  }

  test("Is permission available - failure") {
    whenReady(service.isPermissionAvailable(1,Set(DashboardUserPermissions.DECISION_LOGIC_VIEW.id.toString),Creator(1,1))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning))
    }
  }

  test("Is permission available for environment Id - success") {
    whenReady(service.isValidEnvironmentPermission(1,Set(DashboardUserPermissions.ACCOUNTS_CREATE.id.toString),Creator(1,1))) { response =>
      response shouldBe Right(true)
    }
  }

  test("Is permission available for environment Id - failure") {
    whenReady(service.isValidEnvironmentPermission(1,Set(DashboardUserPermissions.DECISION_LOGIC_VIEW.id.toString),Creator(1,1))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning))
    }
  }

  test("Is permission available for environment Name - success") {
    whenReady(service.isValidEnvironmentPermissionByEnvName("Production", 1, Set(DashboardUserPermissions.ACCOUNTS_CREATE.id.toString),Creator(1,1))) { response =>
      response shouldBe Right(true)
    }
  }

  test("Is permission available for environment Name - failure") {
    whenReady(service.isValidEnvironmentPermissionByEnvName("Production", 1, Set(DashboardUserPermissions.DECISION_LOGIC_VIEW.id.toString),Creator(1,1))) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning))
    }
  }

  test("Is valid active user account association, invalid user id - failure") {
    whenReady(service.isValidActiveUserAccountAssociation(1, 18)) { response =>
      response.fold(_ => fail, _ shouldBe false)
    }
  }

  test("Is valid active user account association - inactive failure") {
    whenReady(service.isValidActiveUserAccountAssociation(25, 17)) { response =>
      response.fold(_ => fail, _ shouldBe false)
    }
  }

  test("Is valid active user account association - success") {
    whenReady(service.isValidActiveUserAccountAssociation(1, 1)) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

}