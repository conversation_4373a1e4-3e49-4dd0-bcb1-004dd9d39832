package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.superadmin.DelegatedAdminService
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.BusinessUserRoles
import me.socure.model.superadmin.DelegatedAdmin
import me.socure.model.user.UserCredential
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import scalacache.ScalaCache

/**
  * Created by sunder<PERSON> on 6/9/16.
  */
class DelegatedAdminServiceTest extends FunSuite with ScalaFutures with Matchers with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig with MemcachedTestSupport {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(100, Millis))

  var service : DelegatedAdminService = _
  var businessUserService: BusinessUserService = _
  override val mysqlService: MysqlService = MysqlService("delegated-admin-service")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "delegated-admin-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildDelegatedAdminService(socureDb)
    businessUserService = buildBusinessUserService(socureDb, scalaCache)
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  val delegatedAdmin = DelegatedAdmin(7, "Delegated", "User", "AccountName7", "**********", "<EMAIL>", Some(Set(BusinessUserRoles.DEVELOPER.id)))

  test("should return return account name") {
    val result = service.getAccountName
    whenReady(result) {res =>
      res.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
      res.fold(_ => fail, _.map(_.email) should not contain ("<EMAIL>"))
    }
  }

  test("should return delegated admin list") {
    val result = service.getDelegatedAdminForBusinessUser("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (Vector(delegatedAdmin)))
    }
  }

  test("should return no delegated admin list") {
    val result = service.getDelegatedAdminForBusinessUser("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (0))
    }
  }

  //TODO:Need to write negative senario test all bellow functionalities
  test("delegated admin should be created"){
    val delegatedAdmin = DelegatedAdmin(0, "Aydcity", "Ayd", "city", "**********", "<EMAIL>", Some(Set(BusinessUserRoles.DEVELOPER.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id)))
    val result = service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "Pas12_word")
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (true))
      whenReady(service.getDelegatedAdminForBusinessUser("<EMAIL>")) { r =>
        r.fold(_ => fail, _.size shouldBe 2)
        whenReady(businessUserService.validateUser(UserCredential("<EMAIL>", "Pas12_word"))) { validationResponse =>
          validationResponse shouldBe 'right
        }
      }
    }
  }

  test("delegated admin should fail"){
    val delegatedAdmin = DelegatedAdmin(0, "Aydcity", "Ayd", "city", "**********", "<EMAIL>", Some(Set(BusinessUserRoles.DEVELOPER.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id)))
    val result = service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "Pas12_word")
    whenReady(result) { res =>
      res.fold(_.code should be (ExceptionCodes.NotSupportedForSAMLEnabledAccounts.id), _ => fail)
    }
  }

  test("delete delegated admin") {
    val result = service.deleteDelegatedAdmin("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (true))
      whenReady(service.getDelegatedAdminForBusinessUser("<EMAIL>")) { r =>
        r.fold(_ => fail, _.size should be (1))
      }
    }
  }

  test("update permission") {
    val result = service.updatePermission("<EMAIL>", Set(BusinessUserRoles.DELEGATED_ADMIN.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id))
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (true))
      whenReady(service.getDelegatedAdminForBusinessUser("<EMAIL>")) {res =>
        res.fold(_ => fail, _.headOption.fold(fail)(_.roles.value should contain (BusinessUserRoles.DELEGATED_ADMIN.id)))
      }
    }
  }

  test("update permission with non-existent email and error") {
    val result = service.updatePermission("<EMAIL>", Set(BusinessUserRoles.DELEGATED_ADMIN.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id))
    whenReady(result) { res =>
      res.fold(_.code should be (ExceptionCodes.UnknownError.id), _ => fail)
    }
  }

  test("promote delegated admin") {
    val result = service.promoteDelegatedAdmin("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (true))
      whenReady(service.getDelegatedAdminForBusinessUser("<EMAIL>")) {res =>
        res.fold(_ => fail, _.headOption.fold(fail)(_.roles.value should contain (BusinessUserRoles.ADMIN.id)))
      }
    }
  }

  test("promote delegated admin with non-existent email error") {
    val result = service.promoteDelegatedAdmin("<EMAIL>")
    whenReady(result) { res =>
      res.fold(_.code should be (ExceptionCodes.UnknownError.id), _ => fail)
    }
  }

  test("should return true") {
    val result = service.isUserExist("<EMAIL>")
    whenReady(result){ res =>
      res.fold(_ => fail, _ should be (true))
    }
  }

  test("should return businessuser not found") {
    val result = service.isUserExist("<EMAIL>")
    whenReady(result){ res =>
      res.fold(_.code should be (ExceptionCodes.BusinessUserNotFound.id), _ => fail)
    }
  }

  test("update user information") {
    val userInfo = DelegatedAdmin(0, "firstname", "surname", "AccountName7", "456", "<EMAIL>", None)
    val result = service.updateUserInformation("<EMAIL>", userInfo)
    whenReady(result) { res =>
      res.fold(_ => fail, _ should be (true))
      whenReady(service.getDelegatedAdminForBusinessUser("<EMAIL>")) { delegatedResponse =>
        delegatedResponse.fold(_ => fail, _.find(_.email == "<EMAIL>").headOption.fold(fail){theUser => {
          theUser.firstname shouldBe "firstname"
          theUser.lastname shouldBe "surname"
          theUser.contact shouldBe "456"
        }})
      }
    }
  }

  test("update user information negative scenario") {
    val userInfo = DelegatedAdmin(0, "firstname", "surname", "socure", "456", "<EMAIL>", None)
    val result = service.updateUserInformation("<EMAIL>", userInfo)
    whenReady(result) { res =>
      res.fold(_.code should be (ExceptionCodes.BusinessUserNotFound.id), _ => fail)
    }
  }

  test("resetPassword should fail for SAML provisioned account"){
    val result = service.resetPassword("<EMAIL>", "password")
    whenReady(result) {res =>
      res.fold(_.code should be (ExceptionCodes.NotSupportedForSAMLEnabledAccounts.id), _ => fail)
    }
  }

  test("resetPassword should return true for account which is not SAML enabled"){
    val result = service.resetPassword("<EMAIL>", "Password1*")
    whenReady(result){res =>
      res.fold(_ => fail, _ should be (true))
    }
  }
}
