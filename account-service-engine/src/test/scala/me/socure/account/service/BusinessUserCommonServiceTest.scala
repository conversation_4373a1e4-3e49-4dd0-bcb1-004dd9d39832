package me.socure.account.service

import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import org.mockito.Mockito
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

class BusinessUserCommonServiceTest extends FunSuite with Matchers with EitherValues with ScalaFutures with BeforeAndAfterAll with MockitoSugar with TestDataSourceConfig {
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : BusinessUserCommonService = _

  override val mysqlService: MysqlService = MysqlService("business-user-common-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    insertAdditionalData(sqlExecutor)
    service = buildBusinessUserCommonService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }
  def insertAdditionalData(sqlExecutor: SQLExecutor) = {
  sqlExecutor.execute("INSERT INTO tbl_business_user (activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
    "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Deleted', 'Account', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 9, true), " +
    "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive', 'Account',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 7, true ), " +
    "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'SomeInactive', 'Account',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 7, true ), " +
    "('a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'AlreadyUsed', 'Account',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 11, true );")

    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, is_primary_user, status) VALUES "+
      "(14, 2, 1, 2), " +
      "(9, 3, 1, 3), " +
      "(10, 3, 1, 0);")
  }

  val newUser = "<EMAIL>"
  val deletedUserV1 = "<EMAIL>"
  val inactiveUserV1 = "<EMAIL>"
  val existingUserV1 = "<EMAIL>"

  val deletedUserV2 = "<EMAIL>"
  val inactiveUserV2 = "<EMAIL>"
  val existingUserV2 = "<EMAIL>"
  val lockedUserV2 = "<EMAIL>"

    test("Check Email Availability - V1 Accounts"){
    val validationData = List(
      (newUser, (false, true, Set())),
      (deletedUserV1, (true, true, Set(9))),
      (inactiveUserV1, (true, true, Set(7))),
      (existingUserV1, (true, false, Set(11)))
    )
    validationData.foreach( rr =>
      whenReady(service.checkUserEmailAvailability(rr._1)){ res =>
        res shouldBe rr._2
      }
    )
  }

  test("Check Email Availability - V2 Accounts"){
    val validationData = List(
      (deletedUserV2, (true, true, Set(2))),
      (inactiveUserV2, (true, true, Set(3))),
      (existingUserV2, (true, false, Set(1,2,4,17))),
      (lockedUserV2, (true, false, Set(3)))
    )
    validationData.foreach( rr =>
      whenReady(service.checkUserEmailAvailability(rr._1)){ res =>
        res shouldBe rr._2
      }
    )
  }

  test("Check Email Availability, prefix email if it can be reused"){
    val validationData = List(
      (newUser, true, false),
      (deletedUserV1, true, true),
      (inactiveUserV1, true, true),
      (existingUserV1, false, false),
      (deletedUserV2, true, true),
      (inactiveUserV2, true, true),
      (existingUserV2, false, false),
      (lockedUserV2, false, false)
    )
    validationData.foreach( rr =>
      whenReady(service.checkNApplyUserEmailAvailability(rr._1)){ res =>
        println(rr._1)
        res shouldBe rr._2
      }
    )
  }

  test("Should delete user with email"){
    whenReady(service.handleIdleEmail("<EMAIL>", Set(10))){ res =>
      res shouldBe(true)
    }
  }
}
