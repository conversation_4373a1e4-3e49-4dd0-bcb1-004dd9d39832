package me.socure.account.service


import java.util.Base64
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.model.{AWSKMSException, DecryptResult, EncryptResult}
import me.socure.DaoAccount
import me.socure.account.service.EncryptionKeysService._
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.kms.KmsService
import me.socure.model.BusinessUserRoles
import me.socure.model.encryption._
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao.{DaoEncryptionKeys, DaoServiceEncryptionKeys}
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{BeforeAndAfterAll, FreeSpec, Matchers}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/18/17.
  */
class EncryptionKeysServiceImplTest extends FreeSpec with Matchers with ScalaFutures with MockFactory with BeforeAndAfterAll with MemcachedTestSupport {

  import me.socure.account.service.fixture.EncryptionKeysServiceImplTestFixture._

  private implicit val exe = ExecutionContext.Implicits.global
  private implicit val defaultPatience = PatienceConfig(timeout = Span(200, Seconds), interval = Span(500, Millis)) //todo:
  private val daoEncryptionKeys = mock[DaoEncryptionKeys]
  private val daoServiceEncryptionKeys = mock[DaoServiceEncryptionKeys]

  private class MockableKmsService extends KmsService(null)

  private val kmsServiceUsEast1 = mock[MockableKmsService]
  private val kmsServiceUsWest1 = mock[MockableKmsService]
  private val mockNoAccountKeysFetcher = mock[UnknownAccountEncryptionKeysFetcher]
  private class MockableDaoAccount extends DaoAccount(null, slick.driver.MySQLDriver)
  private val daoAccount = mock[MockableDaoAccount]
  override def memcachedPodLabel(): String = "encryption-keys-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private val serviceKMSDetails = Seq(
    ServiceKmsDetail(
      serviceName = DataKeyServiceId(Service1EncryptionName),
      kmsIds = kmsIdsConfigService1
    ),
    ServiceKmsDetail(
      serviceName = DataKeyServiceId(Service2EncryptionName),
      kmsIds = kmsIdsConfigService2
    )
  )

  private val encryptionKeysService: EncryptionKeysService = new EncryptionKeysServiceImpl(
    daoEncryptionKeys = daoEncryptionKeys,
    daoServiceEncryptionKeys = daoServiceEncryptionKeys,
    kmsIdsConfig = kmsIdsConfig,
    dataKeyLen = dataKeyLen,
    kmsServices = Map(
      Regions.US_EAST_1 -> kmsServiceUsEast1,
      Regions.US_WEST_1 -> kmsServiceUsWest1
    ),
    clock = clock,
    daoAccount = daoAccount,
    unknownAccountEncryptionKeysFetcher = mockNoAccountKeysFetcher,
    scalaCache = scalaCache,
    serviceKMSDetails = serviceKMSDetails
  )

  override def afterAll() {
    cleanupMemcached
  }

  "EncryptionKeysServiceImpl" - {

    "generate keys" - {
      "should generate IMK keys for parent account when no keys are present" in {
        (daoAccount.getAccount(_: Long)).expects(account1.accountId.value).returns(Future.successful(Some(parentAccount)))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account1.accountId, true).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account1.accountId, Service1EncryptionName, true).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account1.accountId, Service2EncryptionName, true).returns(Future.successful(false))
        expectGenerateIMKs(account1)
        val resultFuture = encryptionKeysService.generate(accountId = account1.accountId)

        whenReady(resultFuture) { result =>
          result shouldBe true
        }
      }

      "should not generate IMK keys for parent account when audit keys already exists, service key inserts aborted because of sequential steps" in {
        (daoAccount.getAccount(_: Long)).expects(account1.accountId.value).returns(Future.successful(Some(parentAccount)))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account1.accountId, true).returns(Future.successful(true))

        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account1.accountId, Service1EncryptionName, true).never()
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account1.accountId, Service2EncryptionName, true).never()
        val resultFuture = encryptionKeysService.generate(accountId = account1.accountId)
        whenReady(resultFuture.failed) { result =>
          result shouldBe EncryptionKeysAlreadyExistException(account1.accountId)
        }
      }

      "should generate IMK keys for child account when parent doesn't have EMK keys" in {
        (daoAccount.getAccount(_: Long)).expects(account2.accountId.value).returns(Future.successful(Some(subAccount)))
        (daoEncryptionKeys.getKeysWithStatusAndType _).expects(account1.accountId, true, false).returns(Future.successful(Seq.empty))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account2.accountId, true).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account2.accountId, Service1EncryptionName, true).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account2.accountId, Service2EncryptionName, true).returns(Future.successful(false))
        expectGenerateIMKs(account2)
        val resultFuture = encryptionKeysService.generate(accountId = account2.accountId)
        whenReady(resultFuture) { result =>
          result shouldBe true
        }
      }

      "should not generate IMK keys for child account when parent doesn't have EMK keys and keys already exists" in {
        (daoAccount.getAccount(_: Long)).expects(account2.accountId.value).returns(Future.successful(Some(subAccount)))
        (daoEncryptionKeys.getKeysWithStatusAndType _).expects(account1.accountId, true, false).returns(Future.successful(Seq.empty))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account2.accountId, true).returns(Future.successful(true))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _: String, _:Boolean)).expects(account2.accountId, Service1EncryptionName, true).never()
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _: String, _:Boolean)).expects(account2.accountId, Service2EncryptionName, true).never()
        val resultFuture = encryptionKeysService.generate(accountId = account2.accountId)
        whenReady(resultFuture.failed) { result =>
          result shouldBe EncryptionKeysAlreadyExistException(account2.accountId)
        }
      }

      "should generate EMK keys for child account when parent does have EMK keys. also generate EMK's for Service keys" ignore {
        (daoAccount.getAccount(_: Long)).expects(account3.accountId.value).returns(Future.successful(Some(subAccount)))
        val dtoKeys = externalDtoKeys(account3)

        (daoAccount.addPermission(_: Long, _: Int)).expects(account3.accountId.value, BusinessUserRoles.BYOK.id).returns(Future.successful(true))
        (daoEncryptionKeys.getKeysWithStatusAndType _).expects(account1.accountId, true, false).returns(Future.successful(dtoKeys))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account3.accountId, false).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account3.accountId, Service1EncryptionName, false).returns(Future.successful(false))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _:String, _:Boolean)).expects(account3.accountId, Service2EncryptionName, false).returns(Future.successful(false))

        //once for the audit external key, and twice for the service keys - one for each service key
        (kmsServiceUsEast1.generateDataKey _).expects(account3.dataKeyUsEast1.externalIdGenerateDataKeyRequest).returns(Future.successful(account3.dataKeyUsEast1.externalIdGenerateDataKeyResult))
        (kmsServiceUsEast1.generateDataKey _).expects(account3.serviceKeysUsEast1(0).externalIdGenerateDataKeyRequest).returns(Future.successful(account3.serviceKeysUsEast1(0).externalIdGenerateDataKeyResult))
        (kmsServiceUsEast1.generateDataKey _).expects(account3.serviceKeysUsEast1(1).externalIdGenerateDataKeyRequest).returns(Future.successful(account3.serviceKeysUsEast1(1).externalIdGenerateDataKeyResult))
        (kmsServiceUsWest1.generateDataKey _).expects(account3.dataKeyUsWest1.generateDataKeyRequest).never

        (kmsServiceUsWest1.encrypt _).expects(account3.dataKeyUsWest1.externalIdEncryptRequest).returns(Future.successful(account3.dataKeyUsWest1.encryptResult))
        (kmsServiceUsWest1.encrypt _).expects(account3.serviceKeysUsWest1(0).externalIdEncryptRequest).returns(Future.successful(account3.serviceKeysUsWest1(0).encryptResult))
        (kmsServiceUsWest1.encrypt _).expects(account3.serviceKeysUsWest1(1).externalIdEncryptRequest).returns(Future.successful(account3.serviceKeysUsWest1(1).encryptResult))
        (kmsServiceUsEast1.encrypt _).expects(account3.dataKeyUsEast1.encryptRequest).never
        (kmsServiceUsWest1.encrypt _).expects(account3.serviceKeysUsWest1(0).encryptRequest).never()
        (kmsServiceUsWest1.encrypt _).expects(account3.serviceKeysUsWest1(1).encryptRequest).never()

        (daoEncryptionKeys.add _).expects(*).returns(Future.successful(Some(2)))
        (daoServiceEncryptionKeys.add _).expects(externalServiceDtoKeys(account3).filter(_.serviceName == Service1EncryptionName)).returns(Future.successful(Some(1)))
        (daoServiceEncryptionKeys.add _).expects(externalServiceDtoKeys(account3).filter(_.serviceName == Service2EncryptionName)).returns(Future.successful(Some(1)))

        val resultFuture = encryptionKeysService.generate(accountId = account3.accountId)
        whenReady(resultFuture) { result =>
          result shouldBe true
        }
      }

      "should not generate EMK keys for child account when parent does have EMK keys and keys already exists" in {
        (daoAccount.getAccount(_: Long)).expects(account2.accountId.value).returns(Future.successful(Some(subAccount)))
        val dtoKeys = externalDtoKeys(account1)
        (daoAccount.addPermission(_: Long, _: Int)).expects(account2.accountId.value, BusinessUserRoles.BYOK.id).returns(Future.successful(true))
        (daoEncryptionKeys.getKeysWithStatusAndType _).expects(account1.accountId, true, false).returns(Future.successful(dtoKeys))
        (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account2.accountId, false).returns(Future.successful(true))
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _: String, _:Boolean)).expects(account2.accountId, Service1EncryptionName, false).never()
        (daoServiceEncryptionKeys.hasEncryptionKeys( _:AccountId, _: String, _:Boolean)).expects(account2.accountId, Service2EncryptionName, false).never()
        val resultFuture = encryptionKeysService.generate(accountId = account2.accountId)
        whenReady(resultFuture.failed) { result =>
          result shouldBe EncryptionKeysAlreadyExistException(account2.accountId)
        }
      }
    }

    "should regenerate keys" in {
      (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account4.accountId, true).returns(Future.successful(true))
      (daoEncryptionKeys.deactivateKeys _).expects(account4.accountId, true).returns(Future.successful(4))
      expectGenerateIMKs(account4)
      val resultFuture = encryptionKeysService.regenerate(accountId = account4.accountId)

      whenReady(resultFuture) { result =>
        result shouldBe generatedKeys(account4)
      }
    }

    "should not regenerate keys when keys don't already exist" in {
      (daoEncryptionKeys.hasEncryptionKeys( _:AccountId, _:Boolean)).expects(account1.accountId, true).returns(Future.successful(false))
      val resultFuture = encryptionKeysService.regenerate(accountId = account1.accountId)
      whenReady(resultFuture.failed) { result =>
        result shouldBe NoEncryptionKeyFoundException(account1.accountId)
      }
    }

    "should get keys by account id" in {
      (daoEncryptionKeys.getActiveKeys _).expects(account1.accountId).returns(Future.successful(dtoKeys(account1)))
      val resultFuture = encryptionKeysService.getKeys(accountId = account1.accountId)
      whenReady(resultFuture) { result =>
        result shouldBe generatedKeys(account1)
      }
    }

    "should check whether an account has keys" in {
      (daoEncryptionKeys.hasEncryptionKeys( _:AccountId)).expects(account1.accountId).returns(Future.successful(true))
      val resultFuture = encryptionKeysService.hasKeys(account1.accountId)
      whenReady(resultFuture)(_ shouldBe true)
    }

    "should check whether an account has keys when there are no keys" in {
      (daoEncryptionKeys.hasEncryptionKeys( _:AccountId)).expects(account1.accountId).returns(Future.successful(false))
      val resultFuture = encryptionKeysService.hasKeys(account1.accountId)
      whenReady(resultFuture)(_ shouldBe false)
    }

    "should check whether an account has keys for unknown account" in {
      val resultFuture = encryptionKeysService.hasKeys(AccountId(0))
      whenReady(resultFuture)(_ shouldBe true)
    }

    "should get unknown account keys" in {
      (mockNoAccountKeysFetcher.fetch _).expects().returns(Future.successful(defaultEncryptedKeys))
      val resultFuture = encryptionKeysService.getKeys(AccountId(0))
      whenReady(resultFuture) { result =>
        result shouldBe defaultEncryptedKeys
      }
    }

    "should return active external KMS list when it's available for the account" in {
      val dtoKeys = externalDtoKeys(account1)
      (daoEncryptionKeys.getKeysWithStatusAndType _).expects(account1.accountId, true, false).returns(Future.successful(dtoKeys))
      val expectedResponse = dtoKeys map ( dto => KmsArnDetails(dto.arn, dto.createdAt))
      val resultFuture = encryptionKeysService.getActiveCustomerKeys(account1.accountId)
      whenReady(resultFuture) { result =>
        result shouldBe expectedResponse
      }
    }

    "should return empty list when external KMS not available for the account" in {
      (daoEncryptionKeys.getKeysWithStatusAndType _).expects(AccountId(0), true, false).returns(Future.successful(Seq.empty))
      val resultFuture = encryptionKeysService.getActiveCustomerKeys(AccountId(0))
      whenReady(resultFuture) { result =>
        result shouldBe Seq.empty
      }
    }

    "should throw Exception for invalid KMS" in {
      val kmsId = KmsId("invalid-kms-id")
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=Set(AccountId(100), AccountId(101)), kmsId=kmsId)

      whenReady(resultFuture.failed) { result =>
        result shouldBe InvalidKMSIdException(account1.accountId, kmsId)
      }
    }

    "should throw Exception, if region not supported" in {
      val kmsId = kmsIdApEast1
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=Set(AccountId(100), AccountId(101)), kmsId=kmsId)

      whenReady(resultFuture.failed) { result =>
        result shouldBe RegionNotSupportedException(kmsId.value)
      }
    }

    "should return default keys alone when for account id 0" in {
      (mockNoAccountKeysFetcher.fetch _).expects().returns(Future.successful(defaultEncryptedKeys))
      val expectedResponse = EncryptedKeyDetails(Map.empty[String, List[String]], defaultEncryptedKeys map (item => (kmsIdsConfig.value(item._1).value, List(Base64.getEncoder.encodeToString(item._2.value)))))
      val resultFuture = encryptionKeysService.getAllActiveKeys(AccountId(0))
      whenReady(resultFuture) { result =>
        result shouldBe expectedResponse
      }
    }

    "should return internal keys alone when external KMS not configured for the account" in {
      (daoEncryptionKeys.getActiveKeys _).expects(account1.accountId).returns(Future.successful(dtoKeys(account1)))
      val grouping = dtoKeys(account1).groupBy(_.isInternal).map( keyType => (keyType._1, keyType._2.groupBy(_.arn)
        .map(arnGroup => (arnGroup._1, arnGroup._2.map(dtoEncryptionKey => Base64.getEncoder.encodeToString(dtoEncryptionKey.encryptionKey)).toList))))
      val expectedResponse = EncryptedKeyDetails(grouping.getOrElse(false, Map.empty[String, List[String]]), grouping.getOrElse(true, Map.empty[String, List[String]]))
      val resultFuture = encryptionKeysService.getAllActiveKeys(account1.accountId)
      whenReady(resultFuture) { result =>
        result shouldBe expectedResponse
      }
    }

    "should return both internal and customer keys when external KMS is configured for the account" in {
      val allKeys = dtoKeys(account1) ++ externalDtoKeys(account1)
      (daoEncryptionKeys.getActiveKeys _).expects(account1.accountId).returns(Future.successful(allKeys))
      val grouping = allKeys.groupBy(_.isInternal).map( keyType => (keyType._1, keyType._2.groupBy(_.arn)
        .map(arnGroup => (arnGroup._1, arnGroup._2.map(dtoEncryptionKey => Base64.getEncoder.encodeToString(dtoEncryptionKey.encryptionKey)).toList))))

      val expectedResponse = EncryptedKeyDetails(grouping.getOrElse(false, Map.empty[String, List[String]]), grouping.getOrElse(true, Map.empty[String, List[String]]))
      val resultFuture = encryptionKeysService.getAllActiveKeys(account1.accountId)
      whenReady(resultFuture) { result =>
        result shouldBe expectedResponse
      }
    }

    "Test customer KMS keys" - {
      "should fail if invalid KMS ARN is given" in {
        val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
        val resultFuture = encryptionKeysService.testCustomerKms(customerKey)
        whenReady(resultFuture) { result =>
          result.fold(_.code should be (CustomerKMSARNParsingError.id), _ => fail)
        }
      }

      "should fail if un-supported region is given" in {
        val customerKey = CustomerKeyDetails(AccountId(1), "arn:aws:kms:us-east-2:************:alias/client-specific-encryption-stage")
        val resultFuture = encryptionKeysService.testCustomerKms(customerKey)
        whenReady(resultFuture) { result =>
          result.fold(_.code should be (CustomerKMSRegionNotSupported.id), _ => fail)
        }
      }

      "should fail if encrypt/datakey generate access is not available" in {
        val kmsId = "arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"
        val account = AccountId(1)
        val customerKey = CustomerKeyDetails(account, kmsId)
        (kmsServiceUsEast1.generateDataKey _).expects(*).returns(Future.failed(new Exception()))
        (kmsServiceUsEast1.encrypt _).expects(*).returns(Future.failed(new Exception()))
        val resultFuture = encryptionKeysService.testCustomerKms(customerKey)
        whenReady(resultFuture) { result =>
          result.fold(_.code should be (CustomerKMSEncryptionError.id), _ => fail)
        }
      }

      "should fail if decrypt key access is not available" in {
        val kmsId = "arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"
        val account = AccountId(1)
        val customerKey = CustomerKeyDetails(account, kmsId)
        (kmsServiceUsEast1.generateDataKey _).expects(*).returns(Future.successful(account2.dataKeyUsEast1.generateDataKeyResult))
        (kmsServiceUsEast1.encrypt _).expects(*).returns(Future.successful(account2.dataKeyUsWest1.encryptResult))
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.failed(new Exception()))
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.failed(new Exception()))
        val resultFuture = encryptionKeysService.testCustomerKms(customerKey)
        whenReady(resultFuture) { result =>
          result.fold(_.code should be (CustomerKMSDecryptionError.id), _ => fail)
        }
      }

      "should succeed if encrypt/decrypt content are same" in {

        val kmsId = "arn:aws:kms:us-east-1:************:alias/client-specific-encryption-stage"
        val account = AccountId(1)
        val customerKey = CustomerKeyDetails(account, kmsId)

        (kmsServiceUsEast1.generateDataKey _).expects(*).returns(Future.successful(account2.dataKeyUsEast1.generateDataKeyResult))
        (kmsServiceUsEast1.encrypt _).expects(*).returns(Future.successful(new EncryptResult().withCiphertextBlob(account2.dataKeyUsEast1.generateDataKeyResult.getCiphertextBlob)))
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.successful(new DecryptResult().withPlaintext(account2.dataKeyUsEast1.generateDataKeyResult.getPlaintext)))
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.successful(new DecryptResult().withPlaintext(account2.dataKeyUsEast1.generateDataKeyResult.getPlaintext)))

        val resultFuture = encryptionKeysService.testCustomerKms(customerKey)
        whenReady(resultFuture) { result =>
          result.fold(_ => fail, _ == true)
        }
      }
    }

    "should throw exception when an account has both internal and external keys" in {
      val account = account1.accountId
      val keys = dtoKeys(account1) ++ Seq(account1.dataKeyUsEast1.externalDtoKey.get)
      (daoEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoServiceEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).never()
      (daoEncryptionKeys.getActiveKeys _).expects(account).returns(Future.successful(keys))
      val subAccounts = Set(account2, account3)
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=subAccounts.map(_.accountId), kmsId=externalValidKmsId)

      whenReady(resultFuture.failed) { result =>
        result shouldBe KeysAreNotMutuallyExclusiveException(externalValidKmsId.value, account1.accountId)
      }
    }

    "should generate new keys for external KMS" in {
      val account = account1.accountId
      val subAccounts = Set(account2, account3)
      val activekeys = dtoKeys(account1) ++ dtoKeys(account2) ++ dtoKeys(account3)
      val activeService1Keys = dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)
      val activeService2Keys = dtoServiceKeys(account1, Service2EncryptionName) ++ dtoServiceKeys(account2, Service2EncryptionName) ++ dtoServiceKeys(account3, Service2EncryptionName)
      (daoEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoServiceEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoEncryptionKeys.getActiveKeys _).expects(account).returns(Future.successful(dtoKeys(account1)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service1EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service1EncryptionName)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service2EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service2EncryptionName)))
      (daoEncryptionKeys.getKeys(_:Set[AccountId], _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), true).returns(Future.successful(activekeys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service1EncryptionName, true).returns(Future.successful(activeService1Keys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service2EncryptionName, true).returns(Future.successful(activeService2Keys))

      expectGenerateEDKs(account1, subAccounts)
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=subAccounts.map(_.accountId), kmsId=externalKmsIdUsEast1)

      whenReady(resultFuture) { result =>
        result shouldBe true
      }
    }

    "should generate keys for external KMS - new Region" in {
      val account = account1.accountId
      val subAccounts = Set(account2, account3)
      val activekeys = externalDtoKeys(account1) ++ externalDtoKeys(account2) ++ externalDtoKeys(account3)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      val activeService2Keys = (dtoServiceKeys(account1, Service2EncryptionName) ++ dtoServiceKeys(account2, Service2EncryptionName) ++ dtoServiceKeys(account3, Service2EncryptionName)).filter(_.serviceName == Service2EncryptionName)
      (daoEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoServiceEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoEncryptionKeys.getActiveKeys _).expects(account).returns(Future.successful(Seq(account1.dataKeyUsEast1.externalDtoKey.get)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service1EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service1EncryptionName)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service2EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service2EncryptionName)))
      (daoEncryptionKeys.getKeys(_:Set[AccountId], _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), true).returns(Future.successful(activekeys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service1EncryptionName, true).returns(Future.successful(activeService1Keys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service2EncryptionName, true).returns(Future.successful(activeService2Keys))

      expectGenerateEDKsDiffRegion(account1, subAccounts)
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=subAccounts.map(_.accountId), kmsId=externalKmsIdUsWest1)

      whenReady(resultFuture) { result =>
        result shouldBe true
      }
    }

    "should rotate keys for external KMS" ignore {
      val account = account1.accountId
      val subAccounts = Set(account2, account3)
      val activekeys = externalDtoKeys(account1) ++ externalDtoKeys(account2) ++ externalDtoKeys(account3)
      val activeService1Keys = (externalServiceDtoKeys(account1) ++ externalServiceDtoKeys(account2) ++ externalServiceDtoKeys(account3)).filter(_.serviceName == Service1EncryptionName)
      val activeService2Keys = (externalServiceDtoKeys(account1) ++ externalServiceDtoKeys(account2) ++ externalServiceDtoKeys(account3)).filter(_.serviceName == Service2EncryptionName)
      (daoServiceEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoEncryptionKeys.hasEncryptionKeys(_:AccountId)).expects(account).returns(Future.successful(true))
      (daoEncryptionKeys.getActiveKeys _).expects(account).returns(Future.successful(Seq(account1.dataKeyUsEast1.externalDtoKey.get)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service1EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service1EncryptionName)))
      (daoServiceEncryptionKeys.getKeys(_: AccountId, _: String, _: Boolean)).expects(account, Service2EncryptionName, true).returns(Future.successful(dtoServiceKeys(account1, Service2EncryptionName)))
      (daoEncryptionKeys.getKeys(_:Set[AccountId], _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), true).returns(Future.successful(activekeys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service1EncryptionName, true).returns(Future.successful(activeService1Keys))
      (daoServiceEncryptionKeys.getKeys(_:Set[AccountId], _: String, _:Boolean)).expects(Set(account1.accountId, account2.accountId, account3.accountId), Service2EncryptionName, true).returns(Future.successful(activeService2Keys))

      expectRotateEDKs(account1, subAccounts)
      val resultFuture = encryptionKeysService.generateCustomerKeys(account1.accountId, subAccountIds=subAccounts.map(_.accountId), kmsId=externalKmsIdUsEast1)

      whenReady(resultFuture) { result =>
        result shouldBe true
      }
    }

    "should return all service data keys for a certain region for a specific service" in {
      val account1 = generateTestAccount(1L)
      val account2 = generateTestAccount(2L)
      val account3 = generateTestAccount(3L)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      val expectedResult = Map(
        1L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 1).map(k => EncryptedKey(k.encryptionKey)).get,
        2L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(k => EncryptedKey(k.encryptionKey)).get,
        3L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(k => EncryptedKey(k.encryptionKey)).get
      )
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful( activeService1Keys))
      (kmsServiceUsEast1.decrypt _).expects(*).never()
      (kmsServiceUsWest1.decrypt _).expects(*).never()
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture) { result =>
        result shouldBe Right(EncryptedServiceKeys(expectedResult))
      }
    }

    "should decrypt external key and re-encrypt with an internal key for a specific service" in {
      val account1 = generateTestAccount(1L)
      val account2 = generateTestAccount(2L)
      val account3 = generateTestAccount(3L)
      val externalService1Keys = (externalServiceDtoKeys(account2) ++ externalServiceDtoKeys(account3)).filter(_.serviceName == Service1EncryptionName)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      val expectedResult = Map(
        1L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 1).map(k => EncryptedKey(k.encryptionKey)).get,
        2L -> externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(k => EncryptedKey(k.encryptionKey)).get,
        3L -> externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(k => EncryptedKey(k.encryptionKey)).get
      )
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful(activeService1Keys ++ externalService1Keys))
      val keyToDecryptAcc2 = account2.serviceKeysUsEast1.find(_.dtoKey.serviceName == Service1EncryptionName).get
      val decryptRequest2 = decryptReq(AccountId(2L), expectedResult(2), externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(_.kmsArn).map(KmsId).get)
      val decryptRequest3 = decryptReq(AccountId(3L), expectedResult(3), externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(_.kmsArn).map(KmsId).get)
      (kmsServiceUsEast1.decrypt _).expects(decryptRequest2).returns(Future.successful(keyToDecryptAcc2.decryptResult))
      (kmsServiceUsEast1.encrypt _).expects(keyToDecryptAcc2.encryptRequest).returns(Future.successful(keyToDecryptAcc2.encryptResult))
      val keyToDecryptAcc3 = account3.serviceKeysUsEast1.find(_.dtoKey.serviceName == Service1EncryptionName).get
      (kmsServiceUsEast1.decrypt _).expects(decryptRequest3).returns(Future.successful(keyToDecryptAcc3.decryptResult))
      (kmsServiceUsEast1.encrypt _).expects(keyToDecryptAcc3.encryptRequest).returns(Future.successful(keyToDecryptAcc3.encryptResult))
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture) { result =>
        result shouldBe Right(EncryptedServiceKeys(expectedResult))
      }
    }

    "should ignore decryption failure for external BYOK KMS key and return the rest that are able to be decrypted" in {
      val account1 = generateTestAccount(1L)
      val account2 = generateTestAccount(2L)
      val account3 = generateTestAccount(3L)
      val externalService1Keys = (externalServiceDtoKeys(account2) ++ externalServiceDtoKeys(account3)).filter(_.serviceName == Service1EncryptionName)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      val expectedResult = Map(
        1L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 1).map(k => EncryptedKey(k.encryptionKey)).get,
        3L -> externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(k => EncryptedKey(k.encryptionKey)).get
      )
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful(activeService1Keys ++ externalService1Keys))
      val encryptedKey2 = externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(k => EncryptedKey(k.encryptionKey)).get
      val decryptRequest2 = decryptReq(AccountId(2L), encryptedKey2, externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(_.kmsArn).map(KmsId).get)
      val decryptRequest3 = decryptReq(AccountId(3L), expectedResult(3), externalService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(_.kmsArn).map(KmsId).get)
      (kmsServiceUsEast1.decrypt _).expects(decryptRequest2).returns(Future.failed(new AWSKMSException("Failed to decrypt your key. What you gonna do!?")))
      val keyToDecryptAcc3 = account3.serviceKeysUsEast1.find(_.dtoKey.serviceName == Service1EncryptionName).get
      (kmsServiceUsEast1.decrypt _).expects(decryptRequest3).returns(Future.successful(keyToDecryptAcc3.decryptResult))
      (kmsServiceUsEast1.encrypt _).expects(keyToDecryptAcc3.encryptRequest).returns(Future.successful(keyToDecryptAcc3.encryptResult))
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture) { result =>
        result shouldBe Right(EncryptedServiceKeys(expectedResult))
      }
    }

    "should dedupe and return single key per account, preference to external kms key" in {
      val account2 = generateTestAccount(2L)
      val activekeysExternal = (externalServiceDtoKeys(account2)).filter(_.serviceName == Service1EncryptionName)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      val keyToDecryptAcc2 = account2.serviceKeysUsEast1.find(_.dtoKey.serviceName == Service1EncryptionName).get
      (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.successful(keyToDecryptAcc2.decryptResult))
      (kmsServiceUsEast1.encrypt _).expects(*).returns(Future.successful(keyToDecryptAcc2.encryptResult))
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful(activekeysExternal ++ activeService1Keys))
      val expectedResult = Map(
        1L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 1).map(k => EncryptedKey(k.encryptionKey)).get,
        2L -> activekeysExternal.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(k => EncryptedKey(k.encryptionKey)).get,
        3L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(k => EncryptedKey(k.encryptionKey)).get
      )
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture) { result =>
        result shouldBe Right(EncryptedServiceKeys(expectedResult))
      }
    }

    "should pick oldest key if duplicate keys found with the same exact data key" in {
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful(activeService1Keys))
      val expectedResult = Map(
        1L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 1).map(k => EncryptedKey(k.encryptionKey)).get,
        2L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 2).map(k => EncryptedKey(k.encryptionKey)).get,
        3L -> activeService1Keys.find(k => k.serviceName == Service1EncryptionName && k.accountId == 3).map(k => EncryptedKey(k.encryptionKey)).get
      )
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture) { result =>
        result shouldBe Right(EncryptedServiceKeys(expectedResult))
      }
    }

    "should fail with a duplicate active key error when duplicate internal keys don't have the same data key" in {
      val account2Alternate = generateTestAccount(2L)
      val activeService1Keys = (dtoServiceKeys(account1, Service1EncryptionName) ++ dtoServiceKeys(account2, Service1EncryptionName) ++ dtoServiceKeys(account2Alternate, Service1EncryptionName) ++ dtoServiceKeys(account3, Service1EncryptionName)).filter(_.serviceName == Service1EncryptionName)
      (daoServiceEncryptionKeys.getAllKeysForService( _: String)).expects(Service1EncryptionName).returns(Future.successful(activeService1Keys))
      val resultFuture = encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)
      whenReady(resultFuture.failed) { result =>
        result shouldBe KeysAreNotMutuallyExclusiveException(activeService1Keys.find(_.accountId == 2).get.kmsArn, account2.accountId)
      }
    }
  }

  private def expectRotateEDKs(parentAccount: AccountKeys, subAccounts: Set[AccountKeys]): Unit = {
    subAccounts.+(parentAccount).foreach{ account =>
      (kmsServiceUsEast1.decrypt _).expects(account.dataKeyUsEast1.decryptRequest).returns(Future.successful(account.dataKeyUsEast1.decryptResult))
      (kmsServiceUsWest1.decrypt _).expects(account.dataKeyUsWest1.decryptRequest).never
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.successful(account.serviceKeysUsEast1(0).externalIdDecryptResult))
        (kmsServiceUsEast1.decrypt _).expects(*).returns(Future.successful(account.serviceKeysUsEast1(1).externalIdDecryptResult))
        (kmsServiceUsWest1.decrypt _).expects(*).returns(Future.successful(account.serviceKeysUsWest1(0).externalIdDecryptResult))
        (kmsServiceUsWest1.decrypt _).expects(*).returns(Future.successful(account.serviceKeysUsWest1(1).externalIdDecryptResult))
      }

      (kmsServiceUsEast1.encrypt _).expects(account.dataKeyUsEast1.externalIdEncryptRequest).returns(Future.successful(account.dataKeyUsEast1.encryptResult))
      (kmsServiceUsWest1.encrypt _).expects(account.dataKeyUsWest1.externalIdEncryptRequest).never
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(0).externalIdEncryptRequest).returns(Future.successful(account.serviceKeysUsEast1(0).encryptResult))
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(1).externalIdEncryptRequest).returns(Future.successful(account.serviceKeysUsEast1(1).encryptResult))
        (kmsServiceUsWest1.encrypt _).expects(account.serviceKeysUsWest1(0).externalIdEncryptRequest).never()
        (kmsServiceUsWest1.encrypt _).expects(account.serviceKeysUsWest1(1).externalIdEncryptRequest).never()
      }
    }
    (daoEncryptionKeys.rotateKeys _).expects(*, *, *).returns(Future.successful(Some(subAccounts.size+1)))
    val serviceKeyAccounts = subAccounts.+(parentAccount).exists(_.serviceKeysUsWest1.nonEmpty)
    if(serviceKeyAccounts) {
      (daoServiceEncryptionKeys.rotateServiceKey _).expects(*, *, *, *).returns(Future.successful(Some(subAccounts.size+1)))
      (daoServiceEncryptionKeys.rotateServiceKey _).expects(*, *, *, *).returns(Future.successful(Some(subAccounts.size+1)))
    }
  }

  private def expectGenerateEDKs(parentAccount: AccountKeys, subAccounts: Set[AccountKeys]): Unit = {
    subAccounts.+(parentAccount).foreach{ account =>
      (kmsServiceUsEast1.decrypt _).expects(account.dataKeyUsEast1.decryptRequest).returns(Future.successful(account.dataKeyUsEast1.decryptResult))
      (kmsServiceUsWest1.decrypt _).expects(account.dataKeyUsWest1.decryptRequest).returns(Future.successful(account.dataKeyUsWest1.decryptResult))
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.decrypt _).expects(account.serviceKeysUsEast1(0).decryptRequest).returns(Future.successful(account.serviceKeysUsEast1(0).decryptResult))
        (kmsServiceUsEast1.decrypt _).expects(account.serviceKeysUsEast1(1).decryptRequest).returns(Future.successful(account.serviceKeysUsEast1(1).decryptResult))
        (kmsServiceUsWest1.decrypt _).expects(account.serviceKeysUsWest1(0).decryptRequest).returns(Future.successful(account.serviceKeysUsWest1(0).decryptResult))
        (kmsServiceUsWest1.decrypt _).expects(account.serviceKeysUsWest1(1).decryptRequest).returns(Future.successful(account.serviceKeysUsWest1(1).decryptResult))
      }

      (kmsServiceUsEast1.encrypt _).expects(account.dataKeyUsEast1.externalIdEncryptRequest).returns(Future.successful(account.dataKeyUsEast1.encryptResult))
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(0).externalIdEncryptRequest).returns(Future.successful(account.serviceKeysUsEast1(0).encryptResult))
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(1).externalIdEncryptRequest).returns(Future.successful(account.serviceKeysUsEast1(1).encryptResult))
        (kmsServiceUsWest1.encrypt _).expects(account.serviceKeysUsWest1(0).externalIdEncryptRequest).never()
        (kmsServiceUsWest1.encrypt _).expects(account.serviceKeysUsWest1(1).externalIdEncryptRequest).never()
      }
      (kmsServiceUsWest1.encrypt _).expects(account.dataKeyUsWest1.externalIdEncryptRequest).never
    }
    (daoEncryptionKeys.addCustomerKeys _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
    val serviceKeyAccounts = subAccounts.+(parentAccount).exists(_.serviceKeysUsWest1.nonEmpty)
    if(serviceKeyAccounts) {
      (daoServiceEncryptionKeys.addCustomerKeys _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
      (daoServiceEncryptionKeys.addCustomerKeys _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
    }
  }

  private def expectGenerateEDKsDiffRegion(parentAccount: AccountKeys, subAccounts: Set[AccountKeys]): Unit = {
    subAccounts.+(parentAccount).foreach{ account =>
      (kmsServiceUsEast1.decrypt _).expects(account.dataKeyUsEast1.decryptRequest).returns(Future.successful(account.dataKeyUsEast1.externalIdDecryptResult))
      (kmsServiceUsWest1.decrypt _).expects(account.dataKeyUsWest1.decryptRequest).returns(Future.successful(account.dataKeyUsWest1.externalIdDecryptResult))
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.decrypt _).expects(account.serviceKeysUsEast1(0).decryptRequest).returns(Future.successful(account.serviceKeysUsEast1(0).externalIdDecryptResult))
        (kmsServiceUsEast1.decrypt _).expects(account.serviceKeysUsEast1(1).decryptRequest).returns(Future.successful(account.serviceKeysUsEast1(1).externalIdDecryptResult))
        (kmsServiceUsWest1.decrypt _).expects(account.serviceKeysUsWest1(0).decryptRequest).returns(Future.successful(account.serviceKeysUsWest1(0).externalIdDecryptResult))
        (kmsServiceUsWest1.decrypt _).expects(account.serviceKeysUsWest1(1).decryptRequest).returns(Future.successful(account.serviceKeysUsWest1(1).externalIdDecryptResult))
      }

      (kmsServiceUsEast1.encrypt _).expects(account.dataKeyUsEast1.externalIdEncryptRequest).never
      (kmsServiceUsWest1.encrypt _).expects(*).returns(Future.successful(account.dataKeyUsWest1.encryptResult))
      if(account.serviceKeysUsWest1.nonEmpty) {
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(0).externalIdEncryptRequest).never()
        (kmsServiceUsEast1.encrypt _).expects(account.serviceKeysUsEast1(1).externalIdEncryptRequest).never()
        (kmsServiceUsWest1.encrypt _).expects(*).returns(Future.successful(account.serviceKeysUsWest1(1).encryptResult))
        (kmsServiceUsWest1.encrypt _).expects(*).returns(Future.successful(account.serviceKeysUsWest1(1).encryptResult))
      }
    }
    (daoEncryptionKeys.add _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
    val serviceKeyAccounts = subAccounts.+(parentAccount).exists(_.serviceKeysUsWest1.nonEmpty)
    if(serviceKeyAccounts) {
      (daoServiceEncryptionKeys.addCustomerKeys _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
      (daoServiceEncryptionKeys.addCustomerKeys _).expects(*).returns(Future.successful(Some(subAccounts.size+1)))
    }
  }

  private def expectGenerateIMKs(accountKeys: AccountKeys): Unit = {
    (kmsServiceUsEast1.generateDataKey _).expects(accountKeys.dataKeyUsEast1.generateDataKeyRequest).returns(Future.successful(accountKeys.dataKeyUsEast1.generateDataKeyResult))
    if(accountKeys.serviceKeysUsWest1.nonEmpty) {
      (kmsServiceUsEast1.generateDataKey _).expects(accountKeys.serviceKeysUsEast1(0).generateDataKeyRequest).returns(Future.successful(accountKeys.serviceKeysUsEast1(0).generateDataKeyResult))
      (kmsServiceUsEast1.generateDataKey _).expects(accountKeys.serviceKeysUsEast1(1).generateDataKeyRequest).returns(Future.successful(accountKeys.serviceKeysUsEast1(1).generateDataKeyResult))
    }
    (kmsServiceUsWest1.generateDataKey _).expects(accountKeys.dataKeyUsWest1.generateDataKeyRequest).never

    (kmsServiceUsEast1.encrypt _).expects(accountKeys.dataKeyUsEast1.encryptRequest).never
    if(accountKeys.serviceKeysUsWest1.nonEmpty) {
      (kmsServiceUsWest1.encrypt _).expects(accountKeys.serviceKeysUsEast1(0).encryptRequest).never()
      (kmsServiceUsWest1.encrypt _).expects(accountKeys.serviceKeysUsEast1(1).encryptRequest).never()
    }

    (kmsServiceUsWest1.encrypt _).expects(accountKeys.dataKeyUsWest1.encryptRequest).returns(Future.successful(accountKeys.dataKeyUsWest1.encryptResult))
    if(accountKeys.serviceKeysUsWest1.nonEmpty) {
      (kmsServiceUsWest1.encrypt _).expects(accountKeys.serviceKeysUsWest1(0).encryptRequest).returns(Future.successful(accountKeys.serviceKeysUsWest1(0).encryptResult))
      (kmsServiceUsWest1.encrypt _).expects(accountKeys.serviceKeysUsWest1(1).encryptRequest).returns(Future.successful(accountKeys.serviceKeysUsWest1(1).encryptResult))
    }

    (daoEncryptionKeys.add _).expects(dtoKeys(accountKeys)).returns(Future.successful(Some(4)))

    if(accountKeys.serviceKeysUsWest1.nonEmpty) {
      (daoServiceEncryptionKeys.add _).expects(dtoServiceKeys(accountKeys, Service1EncryptionName)).returns(Future.successful(Some(2)))
      (daoServiceEncryptionKeys.add _).expects(dtoServiceKeys(accountKeys, Service2EncryptionName)).returns(Future.successful(Some(2)))
    }
  }
}
