package me.socure.account.service

import com.mysql.cj.jdbc.Blob
import me.socure.DaoAccount
import me.socure.account.service.common.subscription.{ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.account.utils.BlobConversionUtility
import me.socure.account.validator.SubscriptionChannelValidator
import me.socure.common.exception.ErrorResponseException
import me.socure.model.subscription.DtoSubscriptionChannelRegistry
import me.socure.storage.slick.dao.{DaoEnvironment, DaoSubscriptionChannelRegistry, DaoSubscriptions}
import me.socure.storage.slick.tables.account.{DtoAccount, DtoEnvironment}
import me.socure.storage.slick.tables.subscription.SubscriptionChannelRegistry
import me.socure.storage.slick.tables.subscription.mapper.SubscriptionChannelRegistryMapper
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, EitherValues, FunSuite, Matchers}
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class SubscriptionChannelValidatorTest extends FunSuite with Matchers with MockitoSugar with EitherValues with ScalaFutures with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global
  val accountId = 1L
  val environmentTypeId = 1L
  val subscriptionTypeId = 1L
  val environmentId = 100L
  val channelId = 1L

  val envProd = DtoEnvironment(100L,"12345","accesstoken","accesstokensecret",Some("0.0.0.0"),1L,1L,None)
  val envSandbox = DtoEnvironment(200L,"12345","accesstoken","accesstokensecret",Some("0.0.0.0"),1L,2L,None)

  val envs:Seq[DtoEnvironment] = Seq(envProd,envSandbox)

  val logger: Logger = LoggerFactory.getLogger(this.getClass)

  val channel = SubscriptionChannelRegistry(
    id = 1L,
    communicationSource = "test.com",
    metadata = Some(DtoSubscriptionChannelRegistry.serialize(Map("CERTIFICATE" -> "test-certificate1"))),
    status = SubscriptionChannelRegistryStatus.ACTIVE.id,
    subscriptionTypeId = subscriptionTypeId,
    name = None,
    secretKey = None,
    environmentId = environmentId,
    channelType = ChannelType.PRIMARY.id,
    communicationMode = CommunicationMode.EMAIL.id,
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    featureTypeId = 1,
    alertTypes = "1,1,1"
  )

  val daoSubscriptionChannelRegistryMock = mock[DaoSubscriptionChannelRegistry]
  val daoEnvironmentMock = mock[DaoEnvironment]
  val daoSubscriptionsMock = mock[DaoSubscriptions]
  val daoAccountMock = mock[DaoAccount]

  val SubscriptionChannelValidatorMock = new SubscriptionChannelValidator(daoEnvironmentMock, daoAccountMock, daoSubscriptionsMock,daoSubscriptionChannelRegistryMock)

  val account = DtoAccount(1L,"name" ,"51",false,true,None,false,None,"acc-e4Mq2U2w2w","publickeyapikey",None)

  before {
    Mockito.reset(daoSubscriptionChannelRegistryMock , daoSubscriptionsMock  , daoEnvironmentMock)
  }

  test("doesAccountSubscriptionTypeProvisioned should return success") {
    val expected = true
    Mockito.when(daoSubscriptionsMock.isSubscriptionTypeEnabled(accountId,subscriptionTypeId)).thenReturn(Future.apply(true))
    Mockito.when(daoAccountMock.getAccount(accountId)).thenReturn(Future.apply(Some(account)))

     whenReady(SubscriptionChannelValidatorMock.doesAccountSubscriptionTypeProvisioned(accountId,subscriptionTypeId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoSubscriptionsMock).isSubscriptionTypeEnabled(accountId,subscriptionTypeId)
  }

  test("doesAccountSubscriptionTypeProvisioned should throw Exception") {
    Mockito.when(daoSubscriptionsMock.isSubscriptionTypeEnabled(accountId,subscriptionTypeId)).thenReturn(Future.apply(false))
    Mockito.when(daoAccountMock.getAccount(accountId)).thenReturn(Future.apply(Some(account)))
     val res =    SubscriptionChannelValidatorMock.doesAccountSubscriptionTypeProvisioned(accountId,subscriptionTypeId)
      whenReady(res.failed) {  e =>
        e shouldBe an [ErrorResponseException]
      }

    Mockito.verify(daoSubscriptionsMock).isSubscriptionTypeEnabled(accountId,subscriptionTypeId)
  }

  test("doesEnvBelongToAccount should return success") {
    val expected = true
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))
    whenReady(SubscriptionChannelValidatorMock.doesEnvBelongToAccount(accountId,environmentId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoEnvironmentMock).getEnvironment(environmentId)
  }

  test("doesEnvBelongToAccount should throw Exception") {
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))

    val res =  SubscriptionChannelValidatorMock.doesEnvBelongToAccount(2L,environmentId)
    whenReady(res.failed) {  e =>
      e shouldBe an [ErrorResponseException]
    }

    Mockito.verify(daoEnvironmentMock).getEnvironment(environmentId)
  }

  test("doesChannelBelongsToAccount should return success") {
    val expected = true
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(channelId)).thenReturn(Future.apply(Some(channel)))
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))


    whenReady(SubscriptionChannelValidatorMock.doesChannelBelongsToAccount(channelId,accountId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(channelId)

  }

  test("doesChannelBelongsToAccount should  throw exception") {
    val expected = true
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(channelId)).thenReturn(Future.apply(Some(channel)))
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))


    val res =SubscriptionChannelValidatorMock.doesChannelBelongsToAccount(channelId,3L)
    whenReady(res.failed) {  e =>
        e shouldBe an [ErrorResponseException]
    }

    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(channelId)

  }

  test("doesChannelBelongsToEnv should return success") {
    val expected = true
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(channelId)).thenReturn(Future.apply(Some(channel)))

    whenReady(SubscriptionChannelValidatorMock.doesChannelBelongsToEnv(channelId,environmentId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(channelId)

  }

  test("doesChannelBelongsToEnv should throw Exception ") {

    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(channelId)).thenReturn(Future.apply(Some(channel)))

    val res = SubscriptionChannelValidatorMock.doesChannelBelongsToEnv(channelId,5L)
    whenReady(res.failed) {  e =>
      e shouldBe an [ErrorResponseException]
    }

    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(channelId)

  }

  test("createSubscriptioChannelValidations should return success") {
    val expected = true
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))
    Mockito.when(daoSubscriptionsMock.isSubscriptionTypeEnabled(accountId,subscriptionTypeId)).thenReturn(Future.apply(true))

    whenReady(SubscriptionChannelValidatorMock.createSubscriptioChannelValidations(accountId,environmentId,subscriptionTypeId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoEnvironmentMock).getEnvironment(environmentId)
    Mockito.verify(daoSubscriptionsMock).isSubscriptionTypeEnabled(accountId,subscriptionTypeId)

  }

  test("updateSubscriptionChannelValidations should return success") {
    val expected = true
    Mockito.when(daoEnvironmentMock.getEnvironment(environmentId)).thenReturn(Future.successful(envs))
    Mockito.when(daoSubscriptionsMock.isSubscriptionTypeEnabled(accountId,subscriptionTypeId)).thenReturn(Future.apply(true))
    Mockito.when(daoSubscriptionChannelRegistryMock.getSubscriptionChannelRegistryById(channelId)).thenReturn(Future.apply(Some(channel)))
    Mockito.when(daoAccountMock.getAccount(accountId)).thenReturn(Future.apply(Some(account)))

    whenReady(SubscriptionChannelValidatorMock.updateSubscriptionChannelValidations(accountId,environmentId,subscriptionTypeId,channelId)) {
      result =>
        result should be(expected)
    }

    Mockito.verify(daoEnvironmentMock).getEnvironment(environmentId)
    Mockito.verify(daoSubscriptionsMock).isSubscriptionTypeEnabled(accountId,subscriptionTypeId)
    Mockito.verify(daoSubscriptionChannelRegistryMock).getSubscriptionChannelRegistryById(channelId)

  }


}
