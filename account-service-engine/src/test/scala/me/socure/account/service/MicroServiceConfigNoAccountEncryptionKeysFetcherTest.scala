package me.socure.account.service

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets

import com.amazonaws.regions.Regions
import me.socure.common.resource.ResourceLoader
import me.socure.model.encryption.EncryptedKey
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by jam<PERSON><PERSON> on 5/3/17.
  */
class MicroServiceConfigNoAccountEncryptionKeysFetcherTest extends FreeSpec with Matchers with MockFactory with ScalaFutures {

  private implicit val ec = ExecutionContext.Implicits.global

  private val keysStr =
    """
      |{
      |  "us-east-1": {
      |    "value": "l8Q0RMKyYnrfww=="
      |  },
      |  "us-west-1": {
      |    "value": "GI1fdR+lztuDUA=="
      |  }
      |}
    """.stripMargin

  "MicroServiceConfigNoAccountEncryptionKeysFetcher" - {
    "should fetch encryption keys properly" in {
      val resourceLoader = mock[ResourceLoader]
      (resourceLoader.load _).expects(MicroServiceConfigUnknownAccountEncryptionKeysFetcher.resourceName).returns(new ByteArrayInputStream(keysStr.getBytes(StandardCharsets.UTF_8)))
      val microServiceConfigNoAccountEncryptionKeysFetcher = new MicroServiceConfigUnknownAccountEncryptionKeysFetcher(resourceLoader)
      whenReady(microServiceConfigNoAccountEncryptionKeysFetcher.fetch()) { result =>
        result shouldBe Map(
          Regions.US_EAST_1 -> EncryptedKey(Array[Byte](-105, -60, 52, 68, -62, -78, 98, 122, -33, -61)),
          Regions.US_WEST_1 -> EncryptedKey(Array[Byte](24, -115, 95, 117, 31, -91, -50, -37, -125, 80))
        )
      }
    }
  }
}
