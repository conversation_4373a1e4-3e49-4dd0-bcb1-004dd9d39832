package me.socure.account.service

import me.socure.account.validator.V2Validator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.SubscriptionsProvisionValueGenerator
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account.mappers.SubscriptionTypeMapper
import me.socure.storage.slick.tables.account.{DtoEnvironment, DtoSubscriptionTypeValueGenerator}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
class SubscriptionServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: SubscriptionService = _
  override val mysqlService: MysqlService = MysqlService("subscription-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildSubscriptionService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  val daoSubscriptionType = mock[DaoSubscriptionType]
  val daoSubscriptionStatus = mock[DaoSubscriptionStatus]
  val daoSubscription = mock[DaoSubscriptions]
  val daoEnvironment = mock[DaoEnvironment]
  val daoSubscriptionChannelRegistry = mock[DaoSubscriptionChannelRegistry]
  val daoAccountV2Mock = mock[DaoAccountV2]
  val v2ValidatorMock = mock[V2Validator]
  val subscriptionService = new SubscriptionService(daoSubscription, daoSubscriptionType, daoSubscriptionStatus, daoEnvironment, daoSubscriptionChannelRegistry, daoAccountV2Mock, v2ValidatorMock)

  val dtoEnvironments = Seq(
    DtoEnvironment(1L, "secretkey1", "access_token1", "token_secret1", Some("domain1"), 1, 1, None),
    DtoEnvironment(2L, "secretkey2", "access_token2", "token_secret2", Some("domain2"), 1, 2, None),
    DtoEnvironment(3L, "secretkey3", "access_token3", "token_secret3", Some("domain3"), 1, 3, None)
  )

    test("List Subscription types empty") {
      Mockito.when(daoSubscriptionType.fetchSubscriptionTypes()).thenReturn(Future.successful(Seq.empty))
      whenReady(subscriptionService.listSubscriptionTypes) { result =>
        result shouldBe 'right
        result shouldBe Right(Seq.empty)
      }
    }

  test("List all Subscription types") {
    val dtoSubscriptionType = DtoSubscriptionTypeValueGenerator.aDtoSubscriptionType()
    Mockito.when(daoSubscriptionType.fetchSubscriptionTypes()).thenReturn(Future.successful(Seq(SubscriptionTypeMapper.toVo(dtoSubscriptionType))))
    whenReady(subscriptionService.listSubscriptionTypes) { result =>
      result shouldBe 'right
      result shouldBe Right(List(dtoSubscriptionType))
    }
  }

  test("Enable subscriptions") {
    Mockito.when(daoSubscriptionStatus.updateSubscriptionStatus(1,Set(1,2,3),1)).thenReturn(Future.successful(1))
    whenReady(subscriptionService.updateSubscriptionStatus(1,Set(1,2,3),1)) { result =>
      result shouldBe 'right
    }
  }

  test("Suspend subscriptions") {
    Mockito.when(daoSubscriptionStatus.updateSubscriptionStatus(1,Set(1,2,3),2)).thenReturn(Future.successful(1))
    whenReady(subscriptionService.updateSubscriptionStatus(1,Set(1,2,3),2)) { result =>
      result shouldBe 'right
    }
  }

  test("Delete subscriptions") {
    Mockito.when(daoSubscriptionStatus.deleteSubscriptionType(1,Set(1,2,3))).thenReturn(Future.successful(1))
    whenReady(subscriptionService.updateSubscriptionStatus(1,Set(1,2,3),4)) { result =>
      result shouldBe 'right
    }
  }

  test("List subscription types with provision should list all the subscriptions with provision details") {
    val accountId = 10
    val subscriptionsProvision = SubscriptionsProvisionValueGenerator.aSubscriptionsProvision()
    Mockito.when(daoSubscriptionType.fechSubscriptionTypesWithProvision(accountId)).thenReturn(Future.successful(Seq(subscriptionsProvision)))
    whenReady(subscriptionService.listSubscriptionTypesWithProvision(accountId)) { result =>
      result shouldBe 'right
    }
  }

  test("list subscriptions should list all the subscriptions for the account 1") {
    whenReady(service.listSubscriptions(1L)) { response =>
      response.fold(_ => fail,
        a => a.length shouldBe 2)
    }
  }

  test("list subscriptions should list all the subscriptions for the account 2") {
    whenReady(service.listSubscriptions(6L)) { response =>
      response.fold(_ => fail,
        a => a.length shouldBe 1)
    }
  }

  test("list subscriptions should return an empty list for an invalid account") {
    whenReady(service.listSubscriptions(10L)) { response =>
      response.fold(_ => fail,
        a => a.length shouldBe 0)
    }
  }

  test("Should subscribe for the account for valid subscription id") {
    whenReady(service.subscribe(1L, 100L)) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("list subscriptions should list all the subscriptions for the account 1 after subscribe") {
    whenReady(service.listSubscriptions(1L)) { response =>
      response.fold(_ => fail,
        a => a.length shouldBe 3)
    }
  }

  test("Should fail when subscribe for the invalid account for valid subscription id") {
    whenReady(service.subscribe(500L, 500L)) { response =>
      response.fold(_.code shouldBe 199, _ => fail)
    }
  }

  test("Should unsubscribe for the account for valid subscription id") {
    val accountId = 1L
    val subscriptionTypeId = 1L
    Mockito.when(daoSubscription.isSubscriptionTypeEnabled(accountId, subscriptionTypeId)).thenReturn(Future(true))
    Mockito.when(daoSubscription.unsubscribe(accountId, subscriptionTypeId)).thenReturn(Future(1))
    Mockito.when(daoEnvironment.getEnvironmentsByAccountId(accountId)).thenReturn(Future(dtoEnvironments))
    whenReady(subscriptionService.unsubscribe(1L, 1L)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      Mockito.verify(daoSubscriptionChannelRegistry).disableSubscriptionChannelRegistries(Seq(1,2,3),1L)
    }
  }

  test("Should fail when unsubscribe for the invalid account for valid subscription id") {
    whenReady(service.unsubscribe(500L, 500L)) { response =>
      response.fold(_.code shouldBe 199, _ => fail)
    }
  }

  test("Should fail when unsubscribe for the valid account for valid subscription id") {
    whenReady(service.unsubscribe(1L, 500L)) { response =>
      response.fold(_.code shouldBe 199, _ => fail)
    }
  }

}
