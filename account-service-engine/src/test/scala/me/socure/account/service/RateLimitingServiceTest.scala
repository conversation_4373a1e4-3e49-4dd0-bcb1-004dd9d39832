package me.socure.account.service

import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.{DeleteRateLimitingInput, RateLimiterPublicAPI, SaveRateLimitingInput, UpdateRateLimitingInput}
import me.socure.model.ratelimiter.RateLimitingConfig
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.tables.account.DtoRateLimit
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.joda.time.DateTime
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.collection.immutable

/**
 * <AUTHOR> Kumar
 */
class RateLimitingServiceTest extends FunSuite with MMatchers with ScalaFutures with MockitoSugar with EitherValues with BeforeAndAfterAll with OptionValues
  with TestDataSourceConfig with MemcachedTestSupport {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val logger: Logger = LoggerFactory.getLogger(getClass)

  var service: RateLimitingService = _
  var dashboardUserServiceV2: DashboardUserServiceV2 = _
  override val mysqlService: MysqlService = MysqlService("rate-limiting-service")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "rate-limiting-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  val timeNow = new DateTime("2020-09-21T05:30:00.000+05:30")
  val rateLimitDto = DtoRateLimit( 2, 2L , 2L , "api-cqeL05d3tE" ,
    1000 , 20 , timeNow, timeNow , "superadmin" , "superadmin" )

  val inactiveRateLimits: immutable.Seq[RateLimiterPublicAPI.EnumVal] = RateLimiterPublicAPI.values.filter(_.id == 4)

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildRateLimitingService(socureDb, scalaCache)
    dashboardUserServiceV2 = buildDashboardUserServiceV2(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("Save rate limits - success") {
    val saveRateLimitingInput = SaveRateLimitingInput(
      accountId = 1,
      environmentTypeId = 2,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000,
      limit = 1,
      createdBy = "test"
    )
    whenReady(service.saveRateLimits(saveRateLimitingInput)) { response =>
      response shouldBe Right(true)
    }
  }

  test("Save rate limits - failure") {
    val saveRateLimitingInput = SaveRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 100, // Should fail in validation
      limit = 1,
      createdBy = "test"
    )
    whenReady(service.saveRateLimits(saveRateLimitingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits))
    }
  }

  test("Update rate limits - success") {
    val updateRateLimitingInput = UpdateRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000,
      limit = 2,
      updatedBy = "test"
    )
    whenReady(service.updateRateLimits(updateRateLimitingInput)) { response =>
      response shouldBe Right(true)
    }
  }

  test("Update rate limits - failure") {
    val updateRateLimitingInput = UpdateRateLimitingInput(
      accountId = 2,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 100, // Should fail in validation
      limit = 1,
      updatedBy = "test"
    )
    whenReady(service.updateRateLimits(updateRateLimitingInput)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateRateLimits))
    }
  }

  test("upsert rate limits - success") {
    val updateRateLimitingInput = UpdateRateLimitingInput(
      accountId = 2,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000,
      limit = 1,
      updatedBy = "test"
    )
    whenReady(service.upsertRateLimits(Seq(updateRateLimitingInput))) { response =>
      response shouldBe Right(true)
    }
  }

  test("Get all rate limit entries - success"){
    whenReady(service.getRateLimits) { response =>
      response.fold(_ => fail, rateLimitingEntrySeq => {
        rateLimitingEntrySeq.length shouldBe 4
        rateLimitingEntrySeq.map(_.name) equals Seq("AccountName1", "AccountName2")
        rateLimitingEntrySeq.find(rateLimitingEntry =>
          rateLimitingEntry.accountId == 1 &&
          rateLimitingEntry.environmentTypeId == 1 &&
          rateLimitingEntry.api == "api-cqeL05d3tE" &&
          rateLimitingEntry.windowInMillis === 1000
        ).map(_.limit) shouldBe Some(2)
      })
    }
  }

  test("Get Rate limit policies - success"){
    whenReady(service.getRateLimits(2,2, "api-cqeL05d3tE")) { response =>
      response shouldBe Right(Seq(AccountConvertors.toRateLimitingConfig(rateLimitDto)))
    }
  }

  test("Get Rate limit policies - empty"){
    whenReady(service.getRateLimits(2,3, "api-cqeL05d3tE")) { response =>
      response shouldBe Right(Seq.empty)
    }
  }

  test("Should Not Return inactive Rate limit policies - empty"){
    whenReady(service.getRateLimits(2,2, inactiveRateLimits.head.publicId)) { response =>
      response shouldBe Right(Seq.empty)
    }
  }

  test("Should Return Only Active Rate limit policies - empty"){
    whenReady(service.getRateLimits(2)) { response =>
      val inactiveRateLimitPublicIdsMap: Map[String, String] = inactiveRateLimits.map(x => (x.publicId -> x.name)).toMap
      response.right.get.count(x => inactiveRateLimitPublicIdsMap.contains(x.api)) shouldBe 0
    }
  }

  test("Get Rate limit policies_v2 - success") {

    val api1 = RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId
    val api2 = RateLimiterPublicAPI.FEEDBACK.publicId

    val expectedResult = Map(
      api1 -> Seq(RateLimitingConfig(1000, 10), RateLimitingConfig(10000, 100)),
      api2 -> Seq(RateLimitingConfig(1000, 5))
    )

    val resultFuture = for {
      r1 <- service.saveRateLimits(genSaveLimit(api1, 1000, 10))
      r2 <- service.saveRateLimits(genSaveLimit(api1, 10000, 100))
      r3 <- service.saveRateLimits(genSaveLimit(api2, 1000, 5))
      limits <- service.getRateLimits(1, 2, Set(api1, api2))
    } yield limits

    whenReady(resultFuture) { actualResult =>
      actualResult shouldBe Right(expectedResult)
    }
  }

  test("Get Rate limit policies_v2 - partial") {

    val api1 = RateLimiterPublicAPI.TRANSACTION.publicId

    val expectedResult = Map(
      api1 -> Seq(RateLimitingConfig(1000, 10), RateLimitingConfig(10000, 100)),
      "__api4" -> Seq.empty
    )

    val resultFuture = for {
      _ <- service.saveRateLimits(genSaveLimit(api1, 1000, 10))
      _ <- service.saveRateLimits(genSaveLimit(api1, 10000, 100))
      limits <- service.getRateLimits(1, 2, Set(api1, "__api4"))
    } yield limits

    whenReady(resultFuture) { actualResult =>
      actualResult shouldBe Right(expectedResult)
    }
  }

  test("Get Active and Inactive Rate limit policies_v2 - partial"){
    inactiveRateLimits.map(x => (x.publicId -> Seq.empty)).toMap ++ Map(RateLimiterPublicAPI.TRANSACTION.publicId -> Seq(RateLimitingConfig(1000, 10), RateLimitingConfig(10000, 100)))
    val expectedResult = inactiveRateLimits.map(x => (x.publicId -> Seq.empty)).toMap ++ Map(RateLimiterPublicAPI.TRANSACTION.publicId -> Seq(RateLimitingConfig(1000, 10), RateLimitingConfig(10000, 100)))
    whenReady(service.getRateLimits(1,2, Set("api-jPbz8X0loV",RateLimiterPublicAPI.TRANSACTION.publicId))) { response =>
      response shouldBe Right(expectedResult)
    }
  }

  test("Get Rate limit policies_v2 - empty"){

    val expectedResult = Map(
      "api-cqeL05d3tEWhatever" -> Seq.empty,
      "api-cqeL05d3tEWhatever2" -> Seq.empty
    )

    whenReady(service.getRateLimits(2,3, Set("api-cqeL05d3tEWhatever", "api-cqeL05d3tEWhatever2"))) { response =>
      response shouldBe Right(expectedResult)
    }
  }

  test("Get Inactive Rate limit policies_v2 - empty"){
    inactiveRateLimits.map(x => (x.publicId -> Seq.empty)).toMap
    val expectedResult = inactiveRateLimits.map(x => (x.publicId -> Seq.empty)).toMap
    whenReady(service.getRateLimits(2,2, Set("api-jPbz8X0loV"))) { response =>
      response shouldBe Right(expectedResult)
    }
  }

  test("Get rate limiting apis - success") {
    whenReady(service.getRateLimitingApis) { response =>
      response shouldBe RateLimiterPublicAPI.getAll
    }
  }

  test("Delete Rate Limit - success"){
    val accId = 1;
    logger.info(s"Deleting $accId should be successful")
    val deleteInput = DeleteRateLimitingInput(
      accountId = accId,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000
    )

    whenReady(service.deleteRateLimit(deleteInput)){ response =>
      response shouldBe Right(true)
    }
  }

  test("Delete Rate Limit - Failure"){

    val accId = 199;
    logger.info(s"Deleting $accId should fail")

    val deleteInput = DeleteRateLimitingInput(
      accountId = accId,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000
    )

    whenReady(service.deleteRateLimit(deleteInput)){ response =>
      response shouldBe Left(ErrorResponse(567,"Could not delete rate limits"))
    }
  }

  test("Delete Rate Limit In Bulk - success"){
    val accId = 2;
    logger.info(s"Deleting $accId in bulk should be successful")
    val deleteInput = DeleteRateLimitingInput(
      accountId = accId,
      environmentTypeId = 1,
      api = "api-cqeL05d3tE",
      windowInMillis = 1000
    )

    whenReady(service.deleteRateLimits(Seq(deleteInput))){ response =>
      response shouldBe Right(true)
    }
  }


  private def genSaveLimit(apiId: String, window: Long, limit: Long): SaveRateLimitingInput = {
    SaveRateLimitingInput(
      accountId = 1,
      environmentTypeId = 2,
      api = apiId,
      windowInMillis = window,
      limit = limit,
      createdBy = "test"
    )
  }

}
