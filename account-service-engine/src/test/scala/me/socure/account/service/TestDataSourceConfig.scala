package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.audit.AccountAuditService
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService}
import me.socure.account.dashboard.DashboardUserService
import me.socure.account.dashboardv2.{DashboardUserServiceV2, EnvironmentSettingsService, IDMService}
import me.socure.account.saml.SamlValidator
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.account.superadmin._
import me.socure.account.validator.V2Validator
import me.socure.common.clock.{Clock, FakeClock}
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{ApiKeyRenewalConfig, BadLoginConfig}
import me.socure.constants.AccountManagementDefaults.PrefixForNonFunctionalEmail
import me.socure.mail.service.MailNotificationService
import me.socure.model.management.client.ModelManagementClient
import me.socure.salt.client.SaltClient
import me.socure.salt.model.SaltValueGenerator
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone, LocalDateTime}
import org.mockito.Mockito
import org.scalatest.mockito.MockitoSugar.mock
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sunderraj on 5/10/16.
 */
trait TestDataSourceConfig {
  implicit val ec = ExecutionContext.global

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  val modelManagementClient = org.mockito.Mockito.mock(classOf[ModelManagementClient])
  val saltClient = org.mockito.Mockito.mock(classOf[SaltClient])
  val aSalt = SaltValueGenerator.aSalt()
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))
  val mailNotificationService: MailNotificationService = org.mockito.Mockito.mock(classOf[MailNotificationService])
  val inactiveUserService: InactiveUserService = org.mockito.Mockito.mock(classOf[InactiveUserService])
  val mysqlService: MysqlService = MysqlService("account-service-tests")
  private val dbName = "socure"
  val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val separator = if (endpoint.contains("?")) "&" else "?"
    val jdbcUrlWithTimezone = s"$endpoint${separator}serverTimezone=UTC"
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(jdbcUrlWithTimezone)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  def prepareSchema(dataSource: DataSource, schema: String) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(schema)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    new AccountSettingService(daoAccount, apiKeyRenewalConfig=apiKeyRenewalConfig, v2Validator, clock=clock)
  }

  def buildActiveUserService(dataSource: DataSource): (EnvironmentSettingsService, ActiveUsersService) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, new FakeClock(10000), saltClient)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    val environmentSettingsService = new EnvironmentSettingsService(daoEnvironment, daoAccount, daoAccountV2, new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis),
      apiKeyRenewalConfig, v2Validator,auditDetailsService)
    (environmentSettingsService, new ActiveUsersService(daoAccount, daoBusiness, passwordStorageService, environmentSettingsService, daoEnvironment))
  }

  def buildLockedUserService(dataSource: DataSource): (DaoBusinessUser, LockedUserService)={
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    (daoBusiness, new LockedUserService(daoBusiness, clock))
  }

  def buildInactiveUserService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator = v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    new InactiveUserService(daoBusinessUser, samlValidator, clock)
  }

  def buildBusinessUserService(dataSource: DataSource, scalaCache: ScalaCache[_]) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusiness,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusiness)
    val passwordService = new PasswordService(passwordStorageService, daoBusiness, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val config = BadLoginConfig(3, false, 5 seconds)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val encryptionKeysService = Mockito.mock(classOf[EncryptionKeysService])
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val rateLimitingService = new RateLimitingService(daoRateLimit, scalaCache)
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusiness, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val accountAutomationService = mock[AccountAutomationService]
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserService(daoBusiness, daoAccount, daoEnvironment, config, passwordService, encryptionKeysService,
      samlValidator, clock, daoPublicApiKey, daoSubscriptions, daoAccountV2, daoRateLimit,pbeEncryptor,rateLimitingService,
      daoAccountUIConfiguration, modelManagementClient, businessUserCommonService, v2Validator, magicLinkAuditService, accountAutomationService, mailNotificationService, accountBundleAssociationService, sessionIdleTimeout = 480, daoProspect, whitelistedEmailDomain = Set("socure.com"))

  }

  def buildDelegatedAdminService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)

    new DelegatedAdminService(daoBusinessUser, passwordService, samlValidator)
  }

  def buildLeaderboardService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new LeaderboardService(daoAccount)
  }

  def buildFraudModelMappingService(dataSource: DataSource): AccountInfoService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new AccountInfoService(daoAccount, v2Validator)
  }
  def buildDashboardUserService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new DashboardUserService(daoAccount, daoBusiness, v2Validator)
  }

  def buildManageAccountsService(dataSource: DataSource, encryptionKeysService: EncryptionKeysService) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)

    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusiness, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    new ManageAccountsService(daoAccount = daoAccount,
      daoBusiness = daoBusiness,
      daoEnvironment = daoEnvironment,
      encryptionKeysService = encryptionKeysService,
      clock = clock,
      apiKeyRenewalConfig = apiKeyRenewalConfig,
      daoPublicApiKey = daoPublicApiKey,
      pbeEncryptor = pbeEncryptor,
      modelManagementClient = modelManagementClient,
      businessUserCommonService = businessUserCommonService,
      mailNotificationService = mailNotificationService,
      inactiveUserService = inactiveUserService)
  }

  def buildSubscriptionService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionType = new DaoSubscriptionType(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionStatus = new DaoSubscriptionStatus(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionChannelRegistry = new DaoSubscriptionChannelRegistry(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new SubscriptionService(daoSubscriptions, daoSubscriptionType, daoSubscriptionStatus, daoEnvironment, daoSubscriptionChannelRegistry, daoAccountV2, v2Validator)
  }

  def buildUserRoleService(dataSource: DataSource): UserRoleService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new UserRoleService(daoAccount,daoAccountV2, v2Validator, daoUIAccountConfiguration)
  }

  def buildSponsorBankService(dataSource: DataSource): SponsorBankService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoSponsorBankProgram = new DaoSponsorBankProgram(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountAudit = new DaoAccountAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountAuditService = new AccountAuditService(daoAccountAudit: DaoAccountAudit,
                                                      daoAccount: DaoAccount,
                                                      clock: Clock )
    new SponsorBankService(daoSponsorBankProgram: DaoSponsorBankProgram,
                            accountAuditService: AccountAuditService,
                            clock: Clock )
  }

  def buildV2ValidationService(dataSource: DataSource): V2ValidationService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val accountHierarchyService = buildAccountHierarchyService(dataSource)
    new V2ValidationService(accountHierarchyService, v2Validator, daoBusinessUser, daoAccountV2)
  }

  def buildUserAccountAssociationService(dataSource: DataSource): UserAccountAssociationService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics,slick.driver.MySQLDriver)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new UserAccountAssociationService(daoAccountV2, daoAccount, daoBusinessUser, daoSubscriptions, v2Validator, daoUIAccountConfiguration, passwordService)
  }

  def buildDashboardUserServiceV2(dataSource: DataSource): DashboardUserServiceV2 = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)

    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService, auditDetailsService)
  }

  def buildPartnerAndSubAccountInfoService(dataSource: DataSource, sponsorBankService: SponsorBankService): PartnerAndSubAccountInfoService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new PartnerAndSubAccountInfoService(daoAccountV2, v2Validator, sponsorBankService = sponsorBankService, clock)
  }

  def buildPermissionTemplateService(dataSource: DataSource): PermissionTemplateService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new PermissionTemplateService(daoAccountV2, v2Validator, clock)
  }

  def buildAccountHierarchyService(dataSource: DataSource): AccountHierarchyService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
  }

  def buildAccountAssociationHistoryService(dataSource: DataSource): AccountAssociationHistoryService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new AccountAssociationHistoryService(daoAccountV2)
  }

  def buildEINService(dataSource: DataSource): EINService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEIN = new DaoEIN(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new EINService(
      daoEIN = daoEIN,
      daoAccount = daoAccount,
      clock = clock)
  }

  def buildMLAService(dataSource: DataSource): MLAService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoMLAFields = new DaoMLAFields(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new MLAService(
      daoMLAFields = daoMLAFields,
      daoAccount = daoAccount,
      clock = clock)
  }

  def buildwatchlistSourceService(dataSource: DataSource, scalacache: ScalaCache[_]): WatchlistSourceService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoWatchlistSource = new DaoWatchlistSource(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    new WatchlistSourceService(daoWatchlistSource, daoAccountV2, clock = clock, v2Validator, scalacache, auditDetailsService)
  }

  def buildRateLimitingService(dataSource: ComboPooledDataSource, scalacache: ScalaCache[_]): RateLimitingService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new RateLimitingService(daoRateLimit = daoRateLimit, scalacache)
  }

  def buildBusinessUserCommonService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusiness = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserCommonService(daoBusiness, daoAccount, daoAccountV2)
  }

  def buildIDMService(dataSource: DataSource): IDMService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoIDMApiKey = new DaoIDMApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new IDMService(daoIDMApiKey, daoAccountV2, daoEnvironment, clock)
  }

  def insertData(sqlExecutor: SQLExecutor) = {

    //Insert into Industries
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Insert into account
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id, is_sponsor_bank) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1', 'public_api_key_1','externalId1', false)," +
      s"(2, 'AccountName2', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId2', 'public_api_key_2','externalId2', false), " +
      s"(3, 'AccountName3', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId3', 'public_api_key_3','externalId3', true), " +
      s"(4, 'AccountName4', '101-205', true, 1, NULL, false, '2017-05-05 00:00:00', 'publicId4', 'public_api_key_4','externalId4', false), " +
      s"(5, 'AccountName5', '101-205', false, 0, NULL, false, '2016-05-05 00:00:00', 'publicId5', 'public_api_key_5','externalId5', false), " +
      s"(6, 'AccountName6', '101-205', false, 0, NULL, false, '2016-05-05 00:00:00', 'publicId6', 'public_api_key_6','externalId6', false), " +
      s"(7, 'AccountName7', '101-205', false, 0, NULL, false, '2016-05-05 00:00:00', 'publicId7', 'public_api_key_7','externalId7', false), " +
      s"(8, 'AccountName8', '101-205', false, 0, 7, false, '2016-05-05 00:00:00', 'publicId8', 'public_api_key_8','externalId8', false), " +
      s"(9, 'AccountName9', '101-205', false, 0, NULL, true, '2016-05-05 00:00:00', 'publicId9', 'public_api_key_9','externalId9', false), " +
      s"(10, 'AccountName10', '101-205', false, 0, 9, true, '2016-05-05 00:00:00', 'publicId10', 'public_api_key_10','externalId10', false), " +
      s"(11, 'AccountName11', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId11', 'public_api_key_11','externalId11', false), " +
      s"(12, 'AccountName12', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId12', 'public_api_key_12','externalId12', false), " +
      s"(13, 'AccountName13', '101-205', false, 1, 12, false, '2016-05-05 00:00:00', 'publicId13', 'public_api_key_13','externalId13', false), " +
      s"(14, 'AccountName14', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId14', 'public_api_key_14','externalId14', false), " +
      s"(15, 'AccountName15', '101-205', false, 1, 14, false, '2016-05-05 00:00:00', 'publicId15', 'public_api_key_15','externalId15', false), "+
      s"(16, 'AccountName16', '101-205', false, 1, 6, false, '2016-05-05 00:00:00', 'publicId16', 'public_api_key_16','externalId16', false), " +
      s"(17, 'AccountName17', '101-205', false, 1, 4, false, '2016-05-05 00:00:00', 'publicId17', 'public_api_key_17','externalId17', false), " +
      s"(18, 'AccountName18', '101-205', false, 1, 4, false, '2016-05-05 00:00:00', 'publicId18', 'public_api_key_18','externalId18', false), " +
      s"(19, 'AccountName19', '101-205', false, 1, 17, false, '2016-05-05 00:00:00', 'publicId19', 'public_api_key_19','externalId19', false), " +
      s"(20, 'AccountName20', '101-205', false, 1, 18, false, '2016-05-05 00:00:00', 'publicId20', 'public_api_key_20','externalId20', false)," +
      s"(21, 'AccountName21', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId21', 'public_api_key_21','externalId21', false)," +
      s"(22, 'AccountName22', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId22', 'public_api_key_22','externalId22', false), " +
      s"(23, 'AccountName23', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId23', 'public_api_key_23','externalId23', false)," +
      s"(24, 'AccountName24', '101-205', true, 1, 23, false, '2016-05-05 00:00:00', 'publicId24', 'public_api_key_24','externalId24', true), " +
      s"(25, 'AccountName25', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId25', 'public_api_key_25','externalId25', false)," +
      s"(26, 'AccountName26', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId26', 'public_api_key_26','externalId26', false),"+
      s"(27, 'AccountName27', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId27', 'public_api_key_27','externalId27', false)," +
      s"(1919, 'test', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1919', 'public_api_key_1919','externalId1919', false)"
    )

    //Insert into Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 2, true ), " +
      "(3, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Locked', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 2, false), " +
      "(4, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive', 'User',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 5, true), " +
      "(5, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Inactive1', 'User1',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 6, true), " +
      "(6, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'User',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 7, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Delegated', 'User',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 7, false), " +
      "(8, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 8, true), " +
      "(9, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 11, true), "+
      "(10, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 12, true), "+
      "(11, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 14, true), "+
      "(12, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 16, true), "+
      "(13, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 21, true), "+
      "(14, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 22, true)," +
      "(15, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Locked', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 11, true), " +
      s"(16, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '${PrefixForNonFunctionalEmail}<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 25, true)," +
      "(17, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 26, true),"+
      "(18, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 27, true),"+
      "(1717, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Primary', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 1919, true)"
    )

    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(1,'8d7455495e0d09297c8ac626bc1d355a', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES(2,'49a11f5245eefff691abdd71f42c109a', 'active', 'v1', current_timestamp, current_timestamp)")

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES (1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES" +
      "(1, 'accessTokenProd', 'secretKeyProd', 'accessTokenSecretProd', 'domain.com,192.1.3.45,prod.com', 1, 1, '2016-05-05 00:00:00'), " +
      "(2, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain.com,192.1.3.45,dev.com', 1, 2, '2016-05-05 00:00:00'), " +
      "(3, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain.com,192.1.3.45,dev.com', 1, 2, '2016-05-05 00:00:00'), " +
      "(4, 'accessTokenProd4', 'secretKeyProd4', 'accessTokenSecretProd4', 'domain.com,192.1.3.45,prod.com,account24', 2, 1, '2016-05-05 00:00:00'), " +
      "(5, 'accessTokenProd5', 'secretKeyProd5', 'accessTokenSecretProd5', 'domain.com5', 5, 1, '2016-05-05 00:00:00'), " +
      "(6, 'accessTokenProd6', 'secretKeyProd6', 'accessTokenSecretProd6', 'domain.com6', 6, 1, '2016-05-05 00:00:00'), " +
      "(7, 'accessTokenProd7', 'secretKeyProd7', 'accessTokenSecretProd7', 'domain.com7', 7, 1, '2016-05-05 00:00:00'), " +
      "(8, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 8, 1, '2016-05-05 00:00:00'), " +
      "(9, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 9, 1, '2016-05-05 00:00:00'), " +
      "(10, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 10, 1, '2016-05-05 00:00:00')," +
      "(11, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 3, 1, '2017-05-05 00:00:00')," +
      "(12, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 3, 2, '2017-05-05 00:00:00')," +
      "(13, 'accessTokenProd13', 'secretKeyProd13', 'accessTokenSecretProd13', 'domain.com13', 11, 1, '2016-05-05 00:00:00')," +
      "(14, 'accessTokenProd14', 'secretKeyProd14', 'accessTokenSecretProd14', 'domain.com14', 11, 2, '2016-05-05 00:00:00'), " +
      "(15, 'accessTokenProd441', 'secretKeyProd481', 'accessTokenSecretProd481', 'domain.com481', 4, 1, '2017-05-07 00:00:00'), " +
      "(16, 'accessTokenProd442', 'secretKeyProd482', 'accessTokenSecretProd482', 'domain.com481', 4, 2, '2017-05-07 00:00:00')"
    )

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(0, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(0, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(0, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 3, '816ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 7, 'D-916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 3, '316ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 4, '406ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 5, '526ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 6, 'E-626ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 8, '876ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 9, '91-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 10, '92-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 13, '93-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 14, '106ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'); ")


    //Public Api Keys
    sqlExecutor.execute("INSERT INTO tbl_public_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(0, 1, 'p1-16ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(0, 1, 'p1-26ca6193-4149-456b-ae00-00fdad2437c0', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(0, 1, 'p1-36ca6193-4149-456b-ae00-00fdad2437c0', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 2, 'p2-6ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 3, 'p3-816ca6193-4149-456b-ae00-00fdad2437c0', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 7, 'p4-916ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 3, 'p5-6ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 4, 'p6-406ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 5, 'p7-526ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 6, 'p8-626ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 8, 'p9-876ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 9, 'p10-926ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 10, 'p11-92-926ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 13, 'p12-93-926ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(0, 14, 'p13-106ca6193-4149-456b-ae00-00fdad2437c0', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'); ")

    // Account Attribute
    sqlExecutor.execute("INSERT INTO tbl_account_attribute (account_id, name, value) VALUES" +
      "(6,'PRODUCTION',3), " +
      "(6,'SLA',3)"
    )

    //Environment Cache
    sqlExecutor.execute("INSERT INTO tbl_environment_cache (id, cache_skip_date, skip_cache, environment_id) VALUES" +
      "(100,'2016-05-05',1,1), " +
      "(101,'2016-05-05',1,2)"
    )

    //Environment Individual Cache
    sqlExecutor.execute("INSERT INTO tbl_environment_individual_cache (id, date, identifier, environment_id) VALUES" +
      "(99,'2016-05-05','individual',1), " +
      "(100,'2016-05-05','email',1), " +
      "(101,'2016-05-05','sam',2)"
    )

    //Environment Social Keys
    sqlExecutor.execute("INSERT INTO tbl_environment_social_key (id, application_key, application_secret, network, environment_id) VALUES" +
      "(1, 'appli_key1', 'secret', 1 ,1), " +
      "(2, 'appli_key2', 'secret', 1 ,2), " +
      "(3, 'appli_key3', 'secret', 1 ,1)"
    )

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES" +
      "(1, 1, 1), " +
      "(5, 4, 1), " +
      "(4, 3, 4), " +
      "(6, 5, 1), " +
      "(4, 2, 3), " +
      "(7, 6, 1), " +
      "(7, 7, 6), " +
      "(NULL, 8, 1)"
    )

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES " +
      "(1, 1), (1,2), (1, 94), (1,95), (2, 22), (2,108), (2, 268), (2, 273), (1, 29), (8, 47), (24, 13), (3, 47)," +
      "(11,2),(11,3), (11,47),(13,1), (13,2), (13,3), (13,45), (13,12), (6,1), (6,2), (16,1), (1,97)," +
      "(2,97), (3,97), (5,97), (4,97), (4,95), (17,97), (18,97), (19,97), (20,97), (23,13), (10,108)")

    // Account Permission provisioned alone
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission, enabled) VALUES (6, 65, false)")

    //Bad Login Count
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count(business_user_id,count, first_bad_login_try, last_bad_login_try, lock_time) VALUES" +
      "(3, 5, '2015-10-20', '" + LocalDateTime.now() + "', '2015-10-20')"
    )

    sqlExecutor.execute("INSERT INTO tbl_account_attribute(account_id, name, value) VALUES(1, 'SLA', '1')")

    //tbl_mla_fields
    sqlExecutor.execute("INSERT INTO tbl_mla_fields(account_id, member_number, security_code, updated_at) VALUES(10, '898989', '090909', CURRENT_TIMESTAMP);")

    //PGP Keys
    sqlExecutor.execute("INSERT INTO tbl_account_pgp_keys (id, account_id, public_key, private_key, created_at, status) VALUES (1, 1, 'asdfgh', '123456', '2017-05-18 15:18:23', '1');")
    sqlExecutor.execute("INSERT INTO tbl_account_pgp_keys (id, account_id, public_key, private_key, created_at, status) VALUES (2, 2, 'asdfgh12', '123456as', '2017-05-18 15:18:23', '1');")

    sqlExecutor.execute("INSERT IGNORE INTO subscription_type(id, name,description,business_user_roles_id,roles) VALUES (100, 'type100','type100',-1,'101, 102, 103');")

    //Subscriptions
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES (1, 183), (1, 184);")
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(6, 183);")

    //Webhook
    sqlExecutor.execute("INSERT INTO tbl_public_webhook(id, environment_id, endpoint, public_key_certificate, created_at, updated_at) VALUES" +
      s"(1, 1, 'localhost1', 'certificate 1', '2018-09-12', '2018-10-12')," +
      s"(2, 2, 'localhost1', 'certificate 2', '2017-09-12', '2017-10-12')," +
      s"(3, 3, 'localhost1', 'certificate 3', '2016-09-12', '2016-10-12')"
    )

    //Subscription status
    sqlExecutor.execute("insert into subscription_status(id,environment_id,webhook_id,subscription_type,subscription_status,is_deleted) values" +
      s"(1,1,1,1,1,false),"+
      s"(2,2,2,1,1,false),"+
      s"(3,3,2,2,1,false)"
    )

    //filter watchlist source
    sqlExecutor.execute("INSERT INTO tbl_filtered_watchlist_source(environment_id, source_id) VALUES(1, 1), (1, 5), (1, 10), (8, 1), (8, 5), (8, 10);")

    // User Consent reasons
    sqlExecutor.execute("INSERT INTO account_consent_reason (account_id, consent_id) VALUES" +
      "(1, 1), " +
      "(2, 2), " +
      "(3, 2), " +
      "(4, 1), " +
      "(5, 1), " +
      "(6, 1), " +
      "(7, 3) "
    )

    // User Role
    sqlExecutor.execute("INSERT INTO `user_role`(name, description, by_business_user_id, by_account_id) VALUES ('PrimaryAdmin','Primary Administrator',1,1),('InstanceAdmin','Instance Administrator',1,1),('InstanceAdmin','CustomRole',2,2);")

    // User Account Association
    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, is_primary_user, status) VALUES(1, 1, 1, 1);")
    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, revoked, updated_by) VALUES(2, 1, '1', 1);")
    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, is_primary_user, status) VALUES(2, 2, 0, 1), (3, 2, 1, 1), (2, 4, 1, 1), (2, 17, 1, 1), (6, 7, 1, 1), (7, 7, 0, 1), (17, 26, 1, 1), (17, 17, 0, 1), (17, 18, 1, 1), (17, 25, 1, 2);")
    sqlExecutor.execute("INSERT INTO `user_account_association`(id,business_user_id,account_id,status,is_primary_user,revoked,updated_by,updated_at,association_type) VALUES " +
      "(27,18,27,1,1,NULL,NULL,'2017-03-06 20:11:22',1) "
    )
    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,3,0), (2,3,0), (4,3,0), (4,4,0), (3,3,0), (3,4,0), (5,3,0), (6,3,0), (7,NULL,1), (8,NULL,2), (9,NULL,4), (10, NULL, 1), (11, NULL, 1),(27,NULL,12);")

    // Permission Template
    sqlExecutor.execute("INSERT INTO permission_template(name,type,account_id,updated_by) VALUES('template1',1,1,1),('template2',2,1,1),('template3',1,1,2),('template4',2,1,1),('template5',1,1,2);")

    sqlExecutor.execute("INSERT INTO permission_template_mapping(permission_template_id, environment_type_id, permissions) VALUES(1, 1, '1001,1002,1003,1004,1011,1012,1013,1014,1026,1027,1028,1029,1030,1031');")

    sqlExecutor.execute("INSERT INTO permission_template_mapping(permission_template_id, environment_type_id, permissions) VALUES(1, 0, '1031,1005,1001,2002,2011,1032,1006,1028,1002,1035,2001,1007,1029,1034,1003,2012,1008,2009,1030,2008,1033,1004');")

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('1','1/',1,1,1), " +
      "('2','1/2/',4,1,1)," +
      "('3','1/2/3',4,1,1)," +
      "('4','4/',2,1,1)," +
      "('5','1/2/5/',4,1,1)," +
      "('17','4/17/',4,1,1)," +
      "('18','4/18/',4,1,0), " +
      "('19','4/17/19',4,1,0), " +
      "('20','4/18/20',4,1,0), " +
      "('7','7/',1,1,1)," +
      "('8','7/8',1,1,1)," +
      "('26', '26/',1,1,1),"+
      "('27', '27/',5,1,0)")



    // Account Hierarchy History
    sqlExecutor.execute("INSERT INTO `account_association_history`(account_hierarchy_id) VALUES ('1')")

    // Role Permission Template Association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(permission_template_id, user_role_id) values(1,3)")

    //Rate limiting
    sqlExecutor.execute("INSERT into rate_limits(account_id,environment_type_id,api,window_in_millis,`limit`,created_by,last_updated_by,created_at , last_updated_at)  values(1,1,'api-cqeL05d3tE',1000,20,'superadmin','superadmin','2020-09-21','2020-09-21')")
    sqlExecutor.execute("INSERT into rate_limits(account_id,environment_type_id,api,window_in_millis,`limit`,created_by,last_updated_by,created_at , last_updated_at)  values(2,2,'api-cqeL05d3tE',1000,20,'superadmin','superadmin','2020-09-21','2020-09-21')")

    //ein
    sqlExecutor.execute("INSERT INTO employee_identification_number(account_id, ein, updated_at, lookup_api_key, service_id) VALUES(1, *********, '2021-06-01', null, null), (1, *********, '2021-07-03', null, null), (2, *********, '2021-08-03', 'YXBpS2V5OnNlcnZpY2VJZA==', 'c2VydmljZUlk')")

    sqlExecutor.execute("INSERT INTO subscription_channel_registry (id, environment_id,subscription_type_id,communication_source,metadata,channel_type,communication_mode,status,created_at,updated_at) VALUES " +
      s"(1, 1, 1, 'https://c50df21aff38.ngrok.io/', 0xACED0005737200237363616C612E636F6C6C656374696F6E2E696D6D757461626C652E4D6170244D617031FB65C59FB22B60D00200024C00046B6579317400124C6A6176612F6C616E672F4F626A6563743B4C000676616C75653171007E0001787074000B434552544946494341544574000474657374,1,2,1,'2020-08-14 08:57:48.0','2020-08-14 08:57:48.0'), " +
      s"(2, 1, 2, 'https://c50df21aff38.ngrok.io/', 0xACED0005737200237363616C612E636F6C6C656374696F6E2E696D6D757461626C652E4D6170244D617031FB65C59FB22B60D00200024C00046B6579317400124C6A6176612F6C616E672F4F626A6563743B4C000676616C75653171007E0001787074000B434552544946494341544574000474657374,1,2,2,'2020-08-14 08:57:48.0','2020-08-14 08:57:48.0');" )

    sqlExecutor.execute("INSERT INTO tbl_sponsor_bank_program (id, sponsor_bank_id, program_id, created_by, created_at) VALUES " +
      "(1, 3, 1, '<EMAIL>', '2023-07-10'), " +
      "(2, 3, 5, '<EMAIL>', '2023-07-10'), " +
      "(3, 24, 25, '<EMAIL>', '2023-07-10'), " +
      "(4, 24, 26, '<EMAIL>', '2023-07-10'), " +
      "(5, 1, 4, '<EMAIL>', '2023-07-10');" )

    sqlExecutor.execute("INSERT INTO `environment_dv_configuration` VALUES " +
      "(1,1,1,'test1',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(2,2,1,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(3,2,2,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(4,2,3,'test3',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(5,3,1,'test',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(6,3,2,'test',2,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL), " +
      "(7,3,3,'test3',1,CURRENT_TIMESTAMP,CURRENT_TIMESTAMP, NULL, NULL);")


    sqlExecutor.execute("INSERT INTO user_account_association (id, business_user_id, account_id, association_type, status) VALUES (1818, 1717, 1919, 1, 1)")
    sqlExecutor.execute("INSERT INTO user_role (id, name, by_business_user_id, by_account_id) VALUES (1515, 'Role 1', 1, 1)")
    sqlExecutor.execute("INSERT INTO user_role (id, name, by_business_user_id, by_account_id) VALUES (1516, 'Role 2', 1, 1)")
    sqlExecutor.execute("INSERT INTO user_account_role_association (id, user_account_association_id, user_role_id, role_type) VALUES (1616, 1818, null, 1)")
    sqlExecutor.execute("INSERT INTO user_account_role_association (id, user_account_association_id, user_role_id, role_type) VALUES (1617, 1818, null, 2)")
    sqlExecutor.execute("INSERT INTO user_account_role_association (id, user_account_association_id, user_role_id, role_type) VALUES (1618, 1818, 1515, 0)")
    sqlExecutor.execute("INSERT INTO user_account_role_association (id, user_account_association_id, user_role_id, role_type) VALUES (1619, 1818, 1516, 0)")

  }

}
