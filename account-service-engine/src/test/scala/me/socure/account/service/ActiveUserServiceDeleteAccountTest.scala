package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource

import javax.sql.DataSource
import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService, BundleManagementService}
import me.socure.account.dashboardv2.{DashboardUserServiceV2, EnvironmentSettingsService}
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.{AccountInfoService, ActiveUsersService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{ApiKeyRenewalConfig, BadLoginConfig}
import me.socure.mail.service.MailNotificationService
import me.socure.model.management.client.ModelManagementClient
import me.socure.salt.client.SaltClient
import me.socure.salt.model.SaltValueGenerator
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 10/28/16.
  */
class ActiveUserServiceDeleteAccountTest extends FunSuite with Matchers with ScalaFutures with BeforeAndAfterAll with EitherValues with MockitoSugar {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val mysqlService: MysqlService = MysqlService("active-user-service-delete-account")
  private val dbName = "socure"
  var service : ActiveUsersService = _
  var dashbaordService : DashboardUserServiceV2 = _
  var businessService : BusinessUserService = _

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  val modelManagementClient = mock[ModelManagementClient]
  val saltClient = mock[SaltClient]
  val mailNotificationService: MailNotificationService = mock[MailNotificationService]
  val aSalt = SaltValueGenerator.aSalt()
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database : Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource : DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, new FakeClock(10000), saltClient)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    val environmentSettingsService = new EnvironmentSettingsService(daoEnvironment, daoAccount, daoAccountV2, new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis),
      apiKeyRenewalConfig, v2Validator,auditDetailsService)
    new ActiveUsersService(daoAccount, daoBusinessUser, passwordStorageService, environmentSettingsService, daoEnvironment)
  }

  private def buildServiceDashboard(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)

    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)

    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService, auditDetailsService)
  }

  private def buildBusinessService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)

    val samlValidator = new SamlValidator(
      new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
    val config = BadLoginConfig(2, true, 60 seconds)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoTblPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val encryptionKeysService = Mockito.mock[EncryptionKeysService](classOf[EncryptionKeysService])
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val rateLimitingService = mock[RateLimitingService]
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val accountAutomationService = mock[AccountAutomationService]
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserService(daoBusinessUser, daoAccount, daoEnvironment, config, passwordService, encryptionKeysService,
      samlValidator, clock, daoTblPublicApiKey, daoSubscriptions, daoAccountV2, daoRateLimit,pbeEncryptor,rateLimitingService,
      daoAccountUIConfiguration, modelManagementClient, businessUserCommonService, v2Validator, magicLinkAuditService,
      accountAutomationService, mailNotificationService, accountBundleAssociationService, sessionIdleTimeout = 480, daoProspect, whitelistedEmailDomain = Set("socure.com"))

  }

  override protected def beforeAll(): Unit ={
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey4', 'externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, 4, false, '${PublicIdGenerator.account().value}','publicApiKey5', 'externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 0, 4, false, '${PublicIdGenerator.account().value}','publicApiKey6', 'externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey7', 'externalId7'), " +
      s"(8, 'AccountName8', '101-205', false, 0, 7, false, '${PublicIdGenerator.account().value}','publicApiKey8', 'externalId8') "
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, '-', '**********', '<EMAIL>', 'Delete1', 'Account1',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, '-', '**********', '<EMAIL>', 'Delete2', 'Account2',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 2, true), " +
      "(3, '-', '**********', '<EMAIL>', 'Delete3', 'Account3',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, true), " +
      "(4, '-', '**********', '<EMAIL>', 'Delete4', 'Account4',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 4, true), " +
      "(5, '-', '**********', '<EMAIL>', 'Delete4', 'Account4',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 4, false), " +
      "(6, '-', '**********', '<EMAIL>', 'Delete5', 'Account5',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 5, true), " +
      "(8, '-', '**********', '<EMAIL>', 'Delete7', 'Account7',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 7, true), " +
      "(9, '-', '**********', '<EMAIL>', 'Delete7', 'Account7',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 8, true), " +
      "(7, '-', '**********', '<EMAIL>', 'Delete6', 'Account6',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 6, true)"
    )

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, '-', '-', '-', '-', 1, 1), " +
      "(2, '-', '-', '-', '-', 1, 2), " +
      "(3,  '-', '-', '-', '-', 2, 1), " +
      "(4, '-', '-', '-', '-', 2, 2), " +
      "(5, '-', '-', '-', '-', 3, 1), " +
      "(6, '-', '-', '-', '-', 3, 2), " +
      "(7, '-', '-', '-', '-', 4, 1), " +
      "(8, '-', '-', '-', '-', 4, 2), " +
      "(9, '-', '-', '-', '-', 5, 1), " +
      "(10, '-', '-', '-', '-', 5, 2), " +
      "(11, '-', '-', '-', '-', 6, 1), " +
      "(12, '-', '-', '-', '-', 6, 2)"
    )

    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id,api_key,status,created_at,updated_at) VALUES" +
      "(1, 1, 'active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(13, 5, 'deprecated_api_key', 'deprecated', '2014-02-06', '2014-02-06'), " +
      "(2, 2, '2active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(3, 3, '3active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(4, 4, '4active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(5, 5, '5active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(6, 6, '6active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(7, 7, '7active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(8, 8, '8active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(9, 9, '9active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(10, 10, '10active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(11, 11, '11active_api_key', 'active', '2014-02-06', '2014-02-06'), " +
      "(12, 12, '12active_api_key', 'active', '2014-02-06', '2014-02-06') "
    )

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES " +
      "(NULL, 1, 1), " +
      "(NULL, 2, 1), " +
      "(NULL, 3, 1), " +
      "(NULL, 4, 1), " +
      "(NULL, 5, 1), " +
      "(NULL, 6, 1)"
    )

    //BadLogin Count
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count (business_user_id, count, first_bad_login_try, last_bad_login_try, lock_time) VALUES" +
      "(3, 4, '2015-10-20', '2015-10-20', '2015-10-20'), " +
      "(4, 4, '2015-10-20', '2015-10-20', '2015-10-20')"
    )

    //BadLogin Track
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_try(id, business_user_id, error_description, login_time) VALUES" +
      "(1, 2, 'login_failed', '2015-10-20'), " +
      "(2, 3, 'login_failed', '2015-10-20'), " +
      "(3, 4, 'login_failed', '2015-10-20')"
    )

    //Password Table
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, created_at, updated_at, hash_algorithm) VALUES(1,'bd612c69b0386a26c436b4cb15814bf1', 'deprecated', current_timestamp, current_timestamp, 'v1')")
    sqlExecutor.execute("INSERT INTO tbl_password(business_user_id, password, status, created_at, updated_at, hash_algorithm) VALUES(1,'8d7455495e0d09297c8ac626bc1d355a', 'active', current_timestamp, current_timestamp, 'v1')")

    sqlExecutor.execute("SET AUTOCOMMIT = ON")

    service = buildService(socureDb)
    dashbaordService = buildServiceDashboard(socureDb)
    businessService = buildBusinessService(socureDb)
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("delete account, it has no sub-account, no-delegated-users and no bad login entry") {
    whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { a =>
      a.size should be(4)
      a.map(_.optUser.map(_.email).getOrElse("")) should contain("<EMAIL>")
      whenReady(dashbaordService.listAllUsers(1, None)) { e =>
        e.right.value.size should be(1)
        val result = service.deleteAccount(1)
        whenReady(result) { res =>
          res.right.value shouldBe true
        }
      }
      whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { aa =>
        aa.size should be(3)
        aa.map(_.optUser.map(_.email).getOrElse("")) should not contain "<EMAIL>"
      }
    }
  }

  test("delete account, it has no sub-account, no-delegated-users and no bad-login-count and single entry in bad-login-audit") {
    whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { a =>
      a.size should be(3)
      a.map(_.optUser.map(_.email).getOrElse("")) should contain("<EMAIL>")
      whenReady(dashbaordService.listAllUsers(2, None)) { e =>
        e.right.value.size should be(1)
        val result = service.deleteAccount(2)
        whenReady(result) { res =>
          res.right.value shouldBe true
        }
      }
      whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { aa =>
        aa.size should be(2)
        aa.map(_.optUser.map(_.email).getOrElse("")) should not contain "<EMAIL>"
      }
    }
  }

  test("Account's active api key needs to be returned") {
    whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { activeAccounts =>
      activeAccounts.find(_.accountId==3).fold(fail)(_.apiKey shouldBe "5active_api_key")
    }
  }

  test("delete account, it has no sub-account, no-delegated-users and single entry on both bad-login-audit and bad-login-count") {
    whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { a =>
      a.size should be(2)
      a.map(_.optUser.map(_.email).getOrElse("")) should contain("<EMAIL>")
      whenReady(dashbaordService.listAllUsers(3, None)) { e =>
        e.right.value.size should be(1)
        val result = service.deleteAccount(3)
        whenReady(result) { res =>
          res.right.value shouldBe true
          whenReady(dashbaordService.listAllUsers(3, None)) { c =>
            c.right.value shouldBe empty
          }
        }
      }
      whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { aa =>
        aa.size should be(1)
        aa.map(_.optUser.map(_.email).getOrElse("")) should not contain "<EMAIL>"
      }
    }
  }

  test("delete account has all entries") {
    whenReady(businessService.getActivesPrimaryAccountAdmins(None, None, "")) { a =>
      a.map(_.optUser.map(_.email).getOrElse("")) should contain("<EMAIL>")
      whenReady(dashbaordService.listAllUsers(4, None, true)) { e =>
        e.right.value.size should be(4)
        val result = service.deleteAccount(4)
        whenReady(result) { res =>
          res.right.value shouldBe true
          whenReady(dashbaordService.listAllUsers(4, None)) { c =>
            c.right.value.foreach(usr => println("Failed to delete primary account user :" + usr.email))
            c.right.value shouldBe empty
            whenReady(dashbaordService.listAllUsers(5, None)) { c =>
              c.right.value.foreach(usr => println("Failed to delete subaccount(5) user :" + usr.email))
              c.right.value shouldBe empty
              whenReady(dashbaordService.listAllUsers(6, None)) { c =>
                c.right.value.foreach(usr => println("Failed to delete subaccount(6) user :" + usr.email))
                c.right.value shouldBe empty
              }
            }
          }
        }
      }
    }
  }

  test("delete account should return account not found") {
    val result = service.getDomainByAccountId(555)
    whenReady(result){res =>
      res.left.value.code should be (ExceptionCodes.AccountNotFound.id)
    }
  }

  test("list usersid by accountid should return result") {
    whenReady(dashbaordService.listAllUserIds(8, true)) { response =>
      response.right.value.size shouldBe (1)
      response.right.value shouldBe (Vector(9))
    }
  }

  test("list usersid by accountid should return subaccount list as well") {
    whenReady(dashbaordService.listAllUserIds(7, true)) { response =>
      response.right.value.size shouldBe (2)
      response.right.value shouldBe (Vector(8, 9))
    }
  }
}
