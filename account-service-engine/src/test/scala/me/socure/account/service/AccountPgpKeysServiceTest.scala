package me.socure.account.service

import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import javax.sql.DataSource
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.model.{DecryptRequest, DecryptResult, EncryptRequest, EncryptResult}
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.kms.KmsService
import me.socure.common.pgp.PgpKeyPairGenerator
import me.socure.common.pgp.model.GeneratedKeyPair
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.encryption.{KmsId, KmsIdsConfig}
import me.socure.model.pgp.PgpPrivateKey
import me.socure.storage.slick.dao.DaoAccountPgpKeys
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 15/05/2017.
  */
class AccountPgpKeysServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec = ExecutionContext.Implicits.global
  implicit val patience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  val daoAccountPgpKeys = mock[DaoAccountPgpKeys]
  val kmsService = mock[KmsService]
  val pGPKeyGenerator = mock[PgpKeyPairGenerator]
  val kmsKey = KmsIdsConfig(Map(Regions.US_EAST_1 -> KmsId("valid-kms-key")))
  val supportMail = "<EMAIL>"

  var service : AccountPgpKeysService = _
  val mysqlService: MysqlService = MysqlService("account-service-tests")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoAccountPgpKeys = new DaoAccountPgpKeys(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new AccountPgpKeysService(daoAccountPgpKeys, pGPKeyGenerator, kmsService, kmsKey, supportMail, clock)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 4, false, '${PublicIdGenerator.account().value}','publicApiKey4', 'externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5', 'externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey6', 'externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey7', 'externalId7')"
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 2, true), " +
      "(3, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, true), " +
      "(4, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','guest7', 'user7',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 7, true)"
    )

    //PGP Keys
    sqlExecutor.execute("INSERT INTO tbl_account_pgp_keys (id, account_id, private_key, public_key, created_at, status) VALUES " +
      s"(1, 1, 'private_key', 'publick_key','2013-09-20', true), " +
      s"(2, 3, 'private_key', 'publick_key','2013-09-20', false), " +
      s"(3, 4, 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cHVibGljS2V5RW5jcnlwdGVkV2l0aEttcw==','2013-09-20', true), " +
      s"(4, 6, 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cHVibGljS2V5RW5jcnlwdGVkV2l0aEttcw==','2013-09-20', false)"
    )

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(7, 274)")


    service = buildService(socureDb)

  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should save pgp key with encryption in db for an account and an email") {
    val reqPrivate = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap("private-key".getBytes(StandardCharsets.UTF_8)))
    val reqPublic = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap("public-key".getBytes(StandardCharsets.UTF_8)))
    Mockito.when(kmsService.encrypt(reqPrivate)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))})
    Mockito.when(kmsService.encrypt(reqPublic)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("publicKeyEncryptedWithKms".getBytes))})
    Mockito.when(pGPKeyGenerator.generatePgpKeys(6, supportMail, None)) thenReturn GeneratedKeyPair("private-key", "public-key")
    whenReady(service.createPgpKeys(6, None)){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should save pgp key with subkey and encryption in db for an account and an email") {
    val reqPrivate = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap("private-key".getBytes(StandardCharsets.UTF_8)))
    val reqPublic = new EncryptRequest().withKeyId(kmsKey.value.headOption.fold(fail)(_._2.value)).withPlaintext(ByteBuffer.wrap("public-key".getBytes(StandardCharsets.UTF_8)))
    Mockito.when(kmsService.encrypt(reqPrivate)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))})
    Mockito.when(kmsService.encrypt(reqPublic)) thenReturn Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap("publicKeyEncryptedWithKms".getBytes))})
    Mockito.when(pGPKeyGenerator.generatePgpKeysWithSubkey(7.toString, supportMail, None)) thenReturn GeneratedKeyPair("private-key", "public-key")
    whenReady(service.createPgpKeys(7, None)){ res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should blow account not found for an invalid account with email"){
    whenReady(service.createPgpKeys(15, None)){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should blow key already exist when createpgpKeys is called for an account with a pgp key pair"){
    whenReady(service.createPgpKeys(6, None)){ res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.PGPExistsAlready.id, _ => fail)
    }
  }


  test("should blow records not found"){
    whenReady(service.getAccountPgpPrivateKey(5)){res =>
      res shouldBe 'left
      res.fold(_.code should be (ExceptionCodes.RecordsNotFound.id), _ => fail)
    }
  }

  test("should return only active pgp account details list") {
    whenReady(service.getActivePgpAccountList()){res =>
      res.fold(_ => fail, _.size shouldBe 4)
    }
  }

  test("should return only active pgp account with email list") {
    whenReady(service.getActivePgpAccountWOPgpList()){res =>
      res.fold(_ => fail, _.size shouldBe 3)
      res.fold(_ => fail, _.filter(_.accountId == 2).map(_.primaryemail).headOption.fold(fail)(_ shouldBe ("<EMAIL>")))
    }
  }

  test("should deactivate pgp keys for the account") {
    whenReady(service.dactivatePgpKeys(1)){res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should throw error on deactivate pgp keys for the account which does not have a active key") {
    whenReady(service.dactivatePgpKeys(3)){res =>
      res.fold(_.code shouldBe ExceptionCodes.NoPGPKeys.id, _ => fail)
    }
  }

  test("should return pgp private key"){
    val decryptReq = new DecryptRequest().withCiphertextBlob(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))
    Mockito.when(kmsService.decrypt(decryptReq)) thenReturn Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap("privateKeyEncryptedWithKms".getBytes))})
    val expected = PgpPrivateKey("privateKeyEncryptedWithKms")
     whenReady(service.getAccountPgpPrivateKey(4)){res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("should return true if key exists") {
    whenReady(service.doesPgpKeyExists(4)){res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should return false if account does not have key exists") {
    whenReady(service.doesPgpKeyExists(8)){res =>
      res shouldBe 'right
      res.fold(_ => fail, _ shouldBe false)
    }
  }
}
