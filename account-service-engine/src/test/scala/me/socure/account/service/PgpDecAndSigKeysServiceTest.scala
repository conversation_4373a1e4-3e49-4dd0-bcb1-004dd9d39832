package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.encryption.KmsIdsConfig
import me.socure.model.pgp.{PgpDecAndSigKeys, PgpPrivate<PERSON>ey, PgpPublicKey}
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class PgpDecAndSigKeysServiceTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {
  implicit private val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit private val patience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  private class MockableAccountPgpKeysService extends AccountPgpKeysService(null, null, null, null.asInstanceOf[KmsIdsConfig], null, null)
  private class MockablePgpSignaturePublicKeyService extends PgpSignaturePublicKeyService(null, null, null, null.asInstanceOf[KmsIdsConfig], null)
  private val accountPgpKeysService = mock[MockableAccountPgpKeysService ]
  private val pgpSignaturePublicKeyService = mock[MockablePgpSignaturePublicKeyService]
  private val pgpDecAndSigKeysService = new PgpDecAndSigKeysService(
    accountPgpKeysService = accountPgpKeysService,
    pgpSignaturePublicKeyService = pgpSignaturePublicKeyService
  )

  private val accountId = 1L
  private val privateKeyStr = "decryption private key"
  private val publicKeyStr = "signature verification public key"
  private val privateKey = PgpPrivateKey(privateKeyStr)
  private val privateKeyWithSubkey = PgpPrivateKey(privateKeyStr, true)
  private val publicKey = PgpPublicKey(publicKeyStr)
  private val publicKeyWithSubkey = PgpPublicKey(publicKeyStr, true)
  private val pgpDecAndSigKeys = PgpDecAndSigKeys(
    decryptionPrivateKey = privateKeyStr,
    signatureVerificationPublicKey = Some(publicKeyStr)
  )

  private val pgpDecAndSigKeysWithSubkey = PgpDecAndSigKeys(
    decryptionPrivateKey = privateKeyStr,
    signatureVerificationPublicKey = Some(publicKeyStr),
    hasSubkey = true
  )

  private val pgpDecAndSigKeysSeq = Seq(PgpDecAndSigKeys(
    decryptionPrivateKey = privateKeyStr,
    signatureVerificationPublicKey = Some(publicKeyStr)
  ))

  "PgpDecAndSigKeysService" - {
    "should return both decryption key and signature verification key when available" in {
      (accountPgpKeysService.getAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(privateKey)))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Right(publicKey)))

      whenReady(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))(_ shouldBe Right(pgpDecAndSigKeys))
    }

    "should return both decryption key with subkey and signature verification key when available" in {
      (accountPgpKeysService.getAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(privateKeyWithSubkey)))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Right(publicKeyWithSubkey)))

      whenReady(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))(_ shouldBe Right(pgpDecAndSigKeysWithSubkey))
    }


    "should return both decryption keys and signature verification keys when available" in {
      (accountPgpKeysService.getAllAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(Seq(privateKey))))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Right(publicKey)))

      whenReady(pgpDecAndSigKeysService.getAllPgpDecAndSigKeys(accountId))(_ shouldBe Right(pgpDecAndSigKeysSeq))
    }

    "should return both decryption keys with subkey and signature verification keys when available" in {
      (accountPgpKeysService.getAllAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(Seq(privateKeyWithSubkey))))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Right(publicKeyWithSubkey)))

      whenReady(pgpDecAndSigKeysService.getAllPgpDecAndSigKeys(accountId))(_ shouldBe Right(Seq(pgpDecAndSigKeysWithSubkey)))
    }


    "should return only decryption key when signature verification key is not available" in {
      (accountPgpKeysService.getAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(privateKey)))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.NoPGPSignaturePublicKeyFound))))

      whenReady(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))(_ shouldBe Right(pgpDecAndSigKeys.copy(signatureVerificationPublicKey = None)))
    }

    "should fail when decryption keys fetching fails" in {
      val decryptionKeyError = Left(ErrorResponseFactory.get(ExceptionCodes.NoPGPKeys))
      (accountPgpKeysService.getAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(decryptionKeyError))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.NoPGPSignaturePublicKeyFound))))

      whenReady(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))(_ shouldBe decryptionKeyError)
    }

    "should fail when signature verification keys fetching fails" in {
      val signatureVerificationKeyError = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      (accountPgpKeysService.getAccountPgpPrivateKey _).expects(accountId).returns(Future.successful(Right(privateKey)))
      (pgpSignaturePublicKeyService.getPgpSignaturePublicKey _).expects(accountId).returns(Future.successful(signatureVerificationKeyError))

      whenReady(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))(_ shouldBe signatureVerificationKeyError)
    }
  }
}
