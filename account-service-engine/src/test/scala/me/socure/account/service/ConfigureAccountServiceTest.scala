package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, PublicExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.{SubAccountCreationRequest, SubAccountUserRequest}
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.utils.DBProxyWithMetrics
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.concurrent.Future

class ConfigureAccountServiceTest extends FunSuite with Matchers with EitherValues with ScalaFutures with BeforeAndAfterAll with MockitoSugar with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(120, Seconds), interval = Span(500, Millis))

  var service: ConfigureAccountService = _
  override val mysqlService: MysqlService = MysqlService("configure-account-service")
  private val dbName = "socure"

  val dashboardAccountServiceV2: DashboardAccountServiceV2 = mock[DashboardAccountServiceV2]

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES (1, 25)")
    val db = JdbcBackend.Database.forDataSource(socureDb)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = socureDb
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    service = new ConfigureAccountService(v2Validator, dashboardAccountServiceV2)
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("industry name doesn't exist") {
    val subAccountCreationRequest = SubAccountCreationRequest("api", "acnt_name", "")
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.IndustryRequired.id, _ => fail)
    }
  }

  test("industry name is not valid") {
    val subAccountCreationRequest = SubAccountCreationRequest("api", "acnt_name", "xxx")
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.IncorrectIndustry.id, _ => fail)
    }
  }

  test("account name empty") {
    val subAccountCreationRequest = SubAccountCreationRequest("api", "", "101-205")
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.AccountNameRequired.id, _ => fail)
    }
  }

  test("account name already exist") {
    val subAccountCreationRequest = SubAccountCreationRequest("api", "AccountName1", "101-205")
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.AccountNameAlreadyExists.id, _ => fail)
    }
  }

  test("modules are not valid") {
    val subAccountCreationRequest = SubAccountCreationRequest("api", "acnt_name", "101-205", modules = Some(Set("x")))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.ModulesIncorrect.id, _ => fail)
    }
  }

  test("api key is not valid") {
    val subAccountCreationRequest = SubAccountCreationRequest("xxx", "acnt_name", "101-205", modules = Some(Set("rid-gnKpF0yue2")))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.NonPrimaryAccountAPIKey.id, _ => fail)
    }
  }

  test("account is not v2 provisioned") {
    val subAccountCreationRequest = SubAccountCreationRequest("93-926ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205", modules = Some(Set("rid-gnKpF0yue2")))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.AccountNotV2Provisioned.id, _ => fail)
    }
  }

  test("account doesn't have allow sub account permission") {
    val subAccountCreationRequest = SubAccountCreationRequest("406ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205", modules = Some(Set("rid-gnKpF0yue2")))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.PermissionMissing.id, _ => fail)
    }
  }

  test("account has all proper permission without passing user") {
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205", modules = Some(Set("rid-gnKpF0yue2")))
    Mockito.when(dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("account has all proper permission - invalid user email") {
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205", user = Some(SubAccountUserRequest(email = "xxx")), modules = Some(Set("rid-gnKpF0yue2")))
    Mockito.when(dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.IncorrectEmailId.id, _ => fail)
    }
  }

  test("account has all proper permission - user exists - validations pass") {
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
      user = Some(SubAccountUserRequest(email = "<EMAIL>")),
      modules = Some(Set("rid-gnKpF0yue2")))
    Mockito.when(dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }


  test("account has all proper permission - user doesn't exists - validations fail") {
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
      user = Some(SubAccountUserRequest(Some("first_name"), None, "<EMAIL>", Some("contact"))),
      modules = Some(Set("rid-gnKpF0yue2")))
    Mockito.when(dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_.code shouldBe PublicExceptionCodes.UserLastNameRequired.id, _ => fail)
    }
  }

  test("account has all proper permission - user doesn't exists - validations pass") {
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
      user = Some(SubAccountUserRequest(Some("first_name"), Some("last_name"), "<EMAIL>", Some("contact"))),
      modules = Some(Set("rid-gnKpF0yue2")))
    Mockito.when(dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccount(subAccountCreationRequest)
    whenReady(result) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

}
