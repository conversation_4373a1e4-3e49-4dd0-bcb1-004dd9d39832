package me.socure.account.service

import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.constants.{DashboardUserPermissions, EnvironmentTypes}
import me.socure.model.account.AccountIdName
import me.socure.model.dashboardv2.{AccountWithCreator, Creator}
import me.socure.model.user.{PasswordChangeForm, UserCredential}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoMagicLinkAudit}
import me.socure.storage.slick.tables.account.{DtoAccountHierarchy, DtoUserAccountAssociation}
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoActivationToken, DtoBusinessUserValueGenerator}
import me.socure.user.MagicLinkAuditService
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
  * Created by alexandre on 3/26/17.
  */
class PasswordServiceTest extends FunSuite with Matchers with BeforeAndAfter with ScalaFutures with MockitoSugar {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  private val storage = mock[PasswordStorageService]
  private val userService = mock[DaoBusinessUser]
  private val daoAccountV2 = mock[DaoAccountV2]
  private val v2Validator = mock[V2Validator]
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  private val samlValidator = Mockito.spy(new SamlValidator(null, null, null, null))
  val daoMagicLinkAudit = mock[DaoMagicLinkAudit]
  val magicLinkAuditService = mock[MagicLinkAuditService]
  val service = new PasswordService(storage, userService, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)

  val dtoActivationToken: DtoActivationToken = DtoActivationToken(0, 1, None, Some(clock.now()))

  before {
    Mockito.reset(storage, userService, samlValidator, v2Validator)
  }

  test("updatePassword should respect password policies on syntax") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    whenReady(service.setPassword(user, "wrongpassword")) { result =>
      result shouldBe 'left
    }

  }

  test("updatePassword should respect password policies on reusability") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    Mockito.when(storage.alreadyUsed(1, "alexandre@p12")).thenReturn(Future.successful(true))
    whenReady(service.setPassword(user, "alexandre@p12")) { result =>
      result shouldBe 'left
    }
  }

  test("updatePassword should accept good passwords") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(storage.alreadyUsed(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.updatePassword(1, aPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    whenReady(service.setPassword(user, aPassword)) { result =>
      result shouldBe 'right
      result.right.toOption should contain(true)
      Mockito.verify(storage).updatePassword(1, aPassword)
    }

  }

  test("updatePassword and unlock user should accept good passwords and unlock user") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(storage.alreadyUsed(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.updatePassword(1, aPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    Mockito.when(userService.unlockUser(1, clock)).thenReturn(Future.successful(1))
    whenReady(service.setPasswordAndUnlockUser(user, aPassword)) { result =>
      result shouldBe 'right
      result.right.toOption should contain(true)
      Mockito.verify(storage).updatePassword(1, aPassword)
    }
  }

  test("updatePassword should accept good change password form") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(storage.alreadyUsed(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.updatePassword(1, aPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    val change = PasswordChangeForm(1, aPassword)
    whenReady(service.setPassword(change)) { result =>
      result shouldBe 'right
      result.right.toOption should contain(true)
      Mockito.verify(storage).updatePassword(1, aPassword)
    }
  }

  test("updatePassword should reject change password form with bad policy") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR012"
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    val change = PasswordChangeForm(1, aPassword)
    whenReady(service.setPassword(change)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
    }
  }

  test("updatePassword should reject password change with invalid users") {
     val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(None))
    val change = PasswordChangeForm(1, aPassword)
    whenReady(service.setPassword(change)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
    }
  }

  test("validate should accept good credentials") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser("<EMAIL>")).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, aPassword)).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.isAccountV2Provisioned(Set(1))).thenReturn(Future.successful(false))

    val change = UserCredential("<EMAIL>", aPassword)
    whenReady(service.validate(change)) { result =>
      result should contain(user)
    }
  }

  test("validate should accept good credentials for v2") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser("<EMAIL>")).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, aPassword)).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.isAccountV2Provisioned(Set(1))).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.getActiveUserAccountAssociation(1, 1)).thenReturn(Future.successful(Some(DtoUserAccountAssociation(1, 1 , 1, 1, false, None, None, DateTime.now()))))

    val change = UserCredential("<EMAIL>", aPassword)
    whenReady(service.validate(change)) { result =>
      result should contain(user)
    }
  }

  test("validate should accept good credentials for v2 when user is associated to sub-account") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser("<EMAIL>")).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, aPassword)).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.isAccountV2Provisioned(Set(1))).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.getActiveUserAccountAssociation(1, 1)).thenReturn(Future.successful(None))
    Mockito.when(daoAccountV2.getAssociatedAccounts(1)).thenReturn(Future.successful(Seq(AccountIdName(2, "Test"))))

    val change = UserCredential("<EMAIL>", aPassword)
    whenReady(service.validate(change)) { result =>
      result should contain(user.copy(accountId = 2))
    }
  }

  test("validate should reject for v2 when user is not associated to any sub-account") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser("<EMAIL>")).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, aPassword)).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.isAccountV2Provisioned(Set(1))).thenReturn(Future.successful(true))
    Mockito.when(daoAccountV2.getActiveUserAccountAssociation(1, 1)).thenReturn(Future.successful(None))
    Mockito.when(daoAccountV2.getAssociatedAccounts(1)).thenReturn(Future.successful(Seq()))

    val change = UserCredential("<EMAIL>", aPassword)
    whenReady(service.validate(change)) { result =>
      result shouldBe empty
    }
  }


  test("validate should reject bad credentials") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val aPassword = "al3OR0:_12"
    Mockito.when(userService.getUser("<EMAIL>")).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(daoAccountV2.isAccountV2Provisioned(Set(1))).thenReturn(Future.successful(false))
    val change = UserCredential("<EMAIL>", aPassword)
    whenReady(service.validate(change)) { result =>
      result shouldBe empty
    }
  }

  test("resetPassword using reset code should accept good change password form") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val code = "code"
    val aPassword = "al3OR0:_12"
    Mockito.when(storage.alreadyUsed(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.updatePassword(1, aPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.unlockUser(1, clock)).thenReturn(Future.successful(1))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    Mockito.when(userService.getUserByResetToken(code)).thenReturn(Future.successful(Some(user)))
    Mockito.when(userService.invalidatePasswordResetTokenByUserId(1)).thenReturn(Future.successful(true))
    whenReady(service.setPasswordWithResetCode(code, aPassword)) { result =>
      result shouldBe 'right
      result.right.toOption should contain(true)
      Mockito.verify(storage).updatePassword(1, aPassword)
      Mockito.verify(userService).invalidatePasswordResetTokenByUserId(1)
    }
  }

  test("resetPassword using reset code should reject bad policy") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val code = "code"
    val aPassword = "al3OR012"

    Mockito.when(userService.getUserByResetToken(code)).thenReturn(Future.successful(Some(user)))
    whenReady(service.setPasswordWithResetCode(code, aPassword)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(userService).getUserByResetToken(code)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("resetPassword using reset code should reject invalid code") {
    val code = "code"
    val aPassword = "al3OR012"

    Mockito.when(userService.getUserByResetToken(code)).thenReturn(Future.successful(None))
    whenReady(service.setPasswordWithResetCode(code, aPassword)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(userService).getUserByResetToken(code)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("setPassword using activation code should accept good change password form") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val code = "code"
    val aPassword = "al3OR0:_12"
    Mockito.when(storage.alreadyUsed(1, aPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.updatePassword(1, aPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    Mockito.when(userService.getUserByActivationCode(code)).thenReturn(Future.successful(Some(user)))
    Mockito.when(userService.invalidateActivationCodeByUserId(1)).thenReturn(Future.successful(true))
    whenReady(service.setPasswordWithActivationCode(code, aPassword)) { result =>
      result shouldBe 'right
      result.right.toOption should contain(true)
      Mockito.verify(storage).updatePassword(1, aPassword)
      Mockito.verify(userService).invalidateActivationCodeByUserId(1)
    }
  }

  test("setPassword using activation code should reject bad policy") {
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, "<EMAIL>", "alexandre", "agular")
    val code = "code"
    val aPassword = "al3OR012"

    Mockito.when(userService.getUserByActivationCode(code)).thenReturn(Future.successful(Some(user)))
    whenReady(service.setPasswordWithActivationCode(code, aPassword)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(userService).getUserByActivationCode(code)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("setPassword using activation code should reject invalid code") {
    val code = "code"
    val aPassword = "al3OR012"

    Mockito.when(userService.getUserByActivationCode(code)).thenReturn(Future.successful(None))
    whenReady(service.setPasswordWithActivationCode(code, aPassword)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(userService).getUserByActivationCode(code)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("updatePassword should accept good change password request") {
    val email = "<EMAIL>"
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, email, "alexandre", "agular")
    val currentPassword = "al3OR0:_12"
    val newPassword = "l30f_:3KKD1"
    Mockito.when(userService.getUser(email)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.alreadyUsed(1, newPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.validate(1, currentPassword)).thenReturn(Future.successful(true))
    Mockito.when(storage.updatePassword(1, newPassword)).thenReturn(Future.successful(()))
    Mockito.when(userService.resetBadLoginMagicTokenCount(1)).thenReturn(Future.successful(1))
    Mockito.doReturn(Future.successful(Right(()))).when(samlValidator).usersHaveNoSaml(emails = Set(email))
    whenReady(service.changePassword(email, currentPassword, newPassword)) { result =>
      result shouldBe 'right
      Mockito.verify(storage).validate(1, currentPassword)
      Mockito.verify(storage).updatePassword(1, newPassword)
      Mockito.verify(samlValidator).usersHaveNoSaml(emails = Set(email))
    }
  }

  test("updatePassword should reject unknown user") {
    val email = "<EMAIL>"
    val currentPassword = "al3OR0:_12"
    val newPassword = "l30f_:3KKD1"
    Mockito.when(userService.getUser(email)).thenReturn(Future.successful(None))
    Mockito.doReturn(Future.successful(Right(()))).when(samlValidator).usersHaveNoSaml(emails = Set(email))
    whenReady(service.changePassword(email, currentPassword, newPassword)) { result =>
      result shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(samlValidator).usersHaveNoSaml(emails = Set(email))
    }
  }

  test("updatePassword should reject with invalid current password") {
    val email = "<EMAIL>"
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, email, "alexandre", "agular")
    val currentPassword = "al3OR0:_12"
    val newPassword = "l30f_:3KKD1"
    Mockito.when(userService.getUser(email)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.validate(1, currentPassword)).thenReturn(Future.successful(false))
    Mockito.doReturn(Future.successful(Right(()))).when(samlValidator).usersHaveNoSaml(emails = Set(email))

    whenReady(service.changePassword(email, currentPassword, newPassword)) { result =>
      result shouldBe 'left
      Mockito.verify(storage).validate(1, currentPassword)
      Mockito.verifyNoMoreInteractions(storage)
      Mockito.verify(samlValidator).usersHaveNoSaml(emails = Set(email))
    }
  }

  test("updatePassword should reject with invalid policy") {
    val email = "<EMAIL>"
    val user  = DtoBusinessUserValueGenerator.aBusinessUser(1, email, "alexandre", "agular")
    val currentPassword = "al3OR0:_12"
    val newPassword = "invalidpolicy"
    Mockito.when(userService.getUser(email)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.alreadyUsed(1, newPassword)).thenReturn(Future.successful(false))
    Mockito.when(storage.validate(1, currentPassword)).thenReturn(Future.successful(true))
    Mockito.doReturn(Future.successful(Right(()))).when(samlValidator).usersHaveNoSaml(emails = Set(email))
    whenReady(service.changePassword(email, currentPassword, newPassword)) { result =>
      result shouldBe 'left
      Mockito.verify(storage).validate(1, currentPassword)
      Mockito.verifyNoMoreInteractions(storage)
      Mockito.verify(samlValidator).usersHaveNoSaml(emails = Set(email))
    }
  }

  test("updatePassword should reject when SAML associated account has SAML 2.0 enabled") {
    val email = "<EMAIL>"
    val errorResponse = Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts))
    Mockito.doReturn(Future.successful(errorResponse)).when(samlValidator).usersHaveNoSaml(emails = Set(email))

    whenReady(service.changePassword(email, "", "")) { result =>
      result shouldBe errorResponse
      Mockito.verify(samlValidator).usersHaveNoSaml(emails = Set(email))
    }
  }

  test("forceResetPassword should work for real users non primary non saml") {
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname")
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.invalidatePassword(1)).thenReturn(Future.successful(true))
    Mockito.when(userService.createPasswordResetCode(dtoActivationToken)).thenReturn(Future.successful(Some("activation")))
    Mockito.doReturn(Future.successful(false)).when(samlValidator).userHaveAllSaml(user)
    whenReady(service.forceResetPassword(1)) { response =>
      response.fold(_ => fail, _.activationCode shouldBe Some("activation"))
      Mockito.verify(storage).invalidatePassword(1)
      Mockito.verify(userService).createPasswordResetCode(dtoActivationToken)
    }
  }

  test("forceResetPassword should not work for real users non primary only saml") {
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname")
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    Mockito.doReturn(Future.successful(true)).when(samlValidator).userHaveAllSaml(user)
    whenReady(service.forceResetPassword(1)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts), _ => fail)
    }
  }

  test("forceResetPassword should not work for real users primary") {
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname", isPrimary = true)
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.invalidatePassword(1)).thenReturn(Future.successful(true))
    Mockito.when(userService.createActivationCode(dtoActivationToken)).thenReturn(Future.successful(Some("activation")))
    whenReady(service.forceResetPassword(1, None)) { response =>
      response shouldBe 'left
      Mockito.verifyZeroInteractions(storage)
      Mockito.verify(userService).getUser(1)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("forceResetPassword should generate activation code even when no password is invalidated") {
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname")
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.invalidatePassword(1)).thenReturn(Future.successful(false))
    Mockito.when(userService.createPasswordResetCode(dtoActivationToken)).thenReturn(Future.successful(Some("activation")))
    Mockito.doReturn(Future.successful(false)).when(samlValidator).userHaveAllSaml(user)
    whenReady(service.forceResetPassword(1)) { response =>
      response.fold(_ => fail, _.activationCode shouldBe Some("activation"))
      Mockito.verify(storage).invalidatePassword(1)
      Mockito.verify(userService).createPasswordResetCode(dtoActivationToken)
    }
  }

  test("forceResetPassword should return an error when user does not exist") {
    Mockito.when(userService.getUser(1)).thenReturn(Future.successful(None))
    whenReady(service.forceResetPassword(1, None)) { response =>
      response shouldBe 'left
      Mockito.verify(userService).getUser(1)
      Mockito.verifyZeroInteractions(storage)
      Mockito.verifyNoMoreInteractions(userService)
    }
  }

  test("forceResetPassword V2 should should be successful non saml") {
    val dtoAccounthierarchy = DtoAccountHierarchy(1, 1, "1/", 1, 1, administer = false, 2)
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname")
    Mockito.when(v2Validator.isValidV2AccountRequest(2, Some(Creator(1,1)))).thenReturn(Future.successful(true))
    Mockito.when(v2Validator.validateAccountAccess(2, 1, ignoreAdministerFlag = false)).thenReturn(Future.successful(dtoAccounthierarchy))
    Mockito.when(v2Validator.validatePermissions(1, 1, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)).thenReturn(Future.successful())
    Mockito.when(userService.getUser(2)).thenReturn(Future.successful(Some(user)))
    Mockito.when(storage.invalidatePassword(2)).thenReturn(Future.successful(false))
    Mockito.when(userService.createPasswordResetCode(dtoActivationToken)).thenReturn(Future.successful(Some("activation")))
    Mockito.doReturn(Future.successful(false)).when(samlValidator).userHaveAllSaml(user)
    whenReady(service.forceResetPassword(2, Some(AccountWithCreator(2, Creator(1, 1))))) { response =>
      response.fold(_ => fail, _.activationCode shouldBe Some("activation"))
      Mockito.verify(storage).invalidatePassword(2)
      Mockito.verify(userService).createPasswordResetCode(dtoActivationToken)
      Mockito.verify(v2Validator).isValidV2AccountRequest(2, Some(Creator(1,1)))
      Mockito.verify(v2Validator).validateAccountAccess(2, 1, ignoreAdministerFlag = false)
      Mockito.verify(v2Validator, Mockito.times(2)).validatePermissions(1, 1, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
    }
  }

  test("forceResetPassword V2 should should fail only saml") {
    val dtoAccounthierarchy = DtoAccountHierarchy(1, 1, "1/", 1, 1, administer = false, 2)
    val user = DtoBusinessUserValueGenerator.aBusinessUser(1, "email", "firstname", "surname")
    Mockito.when(v2Validator.isValidV2AccountRequest(2, Some(Creator(1,1)))).thenReturn(Future.successful(true))
    Mockito.when(v2Validator.validateAccountAccess(2, 1, ignoreAdministerFlag = false)).thenReturn(Future.successful(dtoAccounthierarchy))
    Mockito.when(v2Validator.validatePermissions(1, 1, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)).thenReturn(Future.successful())
    Mockito.when(userService.getUser(2)).thenReturn(Future.successful(Some(user)))
    Mockito.doReturn(Future.successful(true)).when(samlValidator).userHaveAllSaml(user)
    whenReady(service.forceResetPassword(2, Some(AccountWithCreator(2, Creator(1, 1))))) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts), _ => fail)
      Mockito.verify(v2Validator).isValidV2AccountRequest(2, Some(Creator(1,1)))
      Mockito.verify(v2Validator).validateAccountAccess(2, 1, ignoreAdministerFlag = false)
      Mockito.verify(v2Validator, Mockito.times(2)).validatePermissions(1, 1, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
    }
  }

  test("password should have been expired") {
    Mockito.when(service.isPasswordExpired(1)) thenReturn Future.successful(true)
    whenReady(service.isPasswordExpired(1)){r =>
      r shouldBe true
    }
  }

  test("password should not expired") {
    Mockito.when(service.isPasswordExpired(1)) thenReturn Future.successful(false)
    whenReady(service.isPasswordExpired(1)){r =>
      r shouldBe false
    }
  }
}
