package me.socure.account.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.CouldNotAddSFTPUserForAccount
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.storage.slick.dao.{DaoAccountPgpKeys, DaoAccountSftpUser}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

class AccountSftpUserServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec = ExecutionContext.Implicits.global
  implicit val patience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  var service : AccountSftpUserService = _
  val mysqlService: MysqlService = MysqlService("account-sftp-user-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    val daoAccountSftpUser = new DaoAccountSftpUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new AccountSftpUserService(daoAccountSftpUser, clock)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    //Industry
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1', 'externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2', 'externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3', 'externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 4, false, '${PublicIdGenerator.account().value}','publicApiKey4', 'externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5', 'externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey6', 'externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey7', 'externalId7')"
    )

    //PGP Keys
    sqlExecutor.execute("INSERT INTO tbl_account_pgp_keys (id, account_id, private_key, public_key, created_at, status) VALUES " +
      s"(1, 1, 'private_key', 'publick_key','2013-09-20', true), " +
      s"(2, 3, 'private_key', 'publick_key','2013-09-20', false), " +
      s"(3, 4, 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cHVibGljS2V5RW5jcnlwdGVkV2l0aEttcw==','2013-09-20', true), " +
      s"(4, 6, 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cHVibGljS2V5RW5jcnlwdGVkV2l0aEttcw==','2013-09-20', true)"
    )

    //AccountSftpUser
    sqlExecutor.execute("INSERT INTO tbl_account_sftp_user (id, account_id, sftp_user, created_at, updated_at) VALUES " +
      s"(1, 1, 'sftp_user1', '2013-09-20', '2013-09-20'), " +
      s"(2, 4, 'sftp_user1', '2013-09-20', '2013-09-20')"
    )

    service = buildService(socureDb)

  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Should list SFTP Users") {
    whenReady(service.listSftpUsers()){ response =>
      response.fold(_ => fail, s => {
        s.nonEmpty shouldBe true
        s.size shouldBe 2
      })
    }
  }

  test("Should fail to add SFTP User for an account - Invalid Account") {
    whenReady(service.saveAccountSftpUser(100L, "sftp_user1")){ response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount), _ => fail)
    }
  }

  test("Should fail to add SFTP User for an account - Duplicate User") {
    whenReady(service.saveAccountSftpUser(1L, "sftp_user1")){ response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount), _ => fail)
    }
  }

  test("Should fail to add SFTP User for an account - Invalid PGP status for the account") {
    whenReady(service.saveAccountSftpUser(3L, "sftp_user1")){ response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount), _ => fail)
    }
  }

  test("Should fail to add SFTP User for an account - No PGP available for the account") {
    whenReady(service.saveAccountSftpUser(5L, "sftp_user1")){ response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount), _ => fail)
    }
  }

  test("Should add SFTP User for an account") {
    whenReady(service.saveAccountSftpUser(6L, "sftp_user6")){ response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("Should add SFTP User for an account - Add an User") {
    whenReady(service.saveAccountSftpUser(4L, "sftp_user4")){ response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }
}
