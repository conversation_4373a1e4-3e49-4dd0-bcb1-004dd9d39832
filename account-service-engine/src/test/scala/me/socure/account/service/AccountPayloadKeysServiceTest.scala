package me.socure.account.service

import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.model.{DecryptRequest, DecryptResult, EncryptRequest, EncryptResult, GenerateDataKeyPairWithoutPlaintextRequest, GenerateDataKeyPairWithoutPlaintextResult}
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.kms.KmsService
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.{AccountEnvironment, AccountPayloadKey, MergePayloadKeysRequest}
import me.socure.model.encryption.{KmsId, KmsIdsConfig}
import me.socure.storage.slick.dao.DaoAccountPayloadKeys
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import java.nio.ByteBuffer
import java.util.Base64
import javax.sql.DataSource
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.utils.DBProxyWithMetrics
import scalacache.{Cache, ScalaCache}

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

/**
 * Created by abhishek on 11/15/2021.
 */
class AccountPayloadKeysServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec = ExecutionContext.Implicits.global
  implicit val patience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service: AccountPayloadKeysService = _
  val kmsService = mock[KmsService]
  val kmsServiceUSE2 = mock[KmsService]
  val kmsKey = KmsIdsConfig(Map(Regions.US_EAST_1 -> KmsId("fake-kms-key")))
  val multiRegionKmsKey = KmsIdsConfig(
    Map(
      Regions.US_EAST_1 -> KmsId("arn:aws:kms:us-east-1:12345:alias/fake-multi-region-key"),
      Regions.US_EAST_2 -> KmsId("arn:aws:kms:us-east-2:12345:alias/fake-multi-region-key")
    )
  )
  val kmsServices = Map(
    Regions.US_EAST_1 -> kmsService,
    Regions.US_EAST_2 -> kmsServiceUSE2
  )
  val clock = new FakeClock(new DateTime("2021-11-19").withZone(DateTimeZone.UTC).getMillis)
  val cache = mock[Cache[Seq[AccountPayloadKey]]]
  val scalaCache = ScalaCache(cache)
  val mysqlService: MysqlService = MysqlService("account-service-tests")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountPayloadKeys = new DaoAccountPayloadKeys(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new AccountPayloadKeysService(daoAccountPayloadKeys, kmsServices, kmsKey, multiRegionKmsKey, scalaCache, clock)
  }

  private def randomBytes(): Array[Byte] = {
    val bytes = new Array[Byte](256)
    Random.nextBytes(bytes)
    bytes
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)

    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1', 'externalId1')," +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2', 'externalId2')," +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3', 'externalId3')")

    sqlExecutor.execute(s"INSERT INTO tbl_environment_type (id, name) VALUES (1, 'production'), (2, 'certification')")

    sqlExecutor.execute("INSERT INTO tbl_account_payload_keys (id, account_id, environment_type_id, public_key, private_key, customer_public_key, wrapping_key_ref, status, created_at, updated_at) VALUES " +
      s"(1, 1, 1, 'public_key1', 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cust_key1', 'wrapref1', true, '2021-11-15', '2021-11-15')," +
      s"(2, 2, 1, 'public_key2', 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cust_key2', 'fake-multi-region-key', true, '2021-11-15', '2021-11-15')," +
      s"(3, 3, 1, 'public_key3', 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cust_key3', 'fake-multi-region-key', true, '2021-11-15', '2021-11-15')," +
      s"(4, 3, 3, 'public_key3', 'cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM=', 'cust_key3', 'wrapref1', true, '2021-11-15', '2021-11-15')"

    )
    service = buildService(socureDb)
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("should return list of active account payload keys") {
    val fakeWrappedPrivateKey = "cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="
    val fakeWrappingRef = "wrapref1"
    val decryptReq = new DecryptRequest().withKeyId(fakeWrappingRef).withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(fakeWrappedPrivateKey.getBytes)))
    Mockito.when(kmsService.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    whenReady(service.getPayloadKeys(Some(1))) {res =>
      res shouldBe 'right
      res.right.get.size shouldBe 1
    }

    Mockito.verify(kmsService, Mockito.times(1)).decrypt(decryptReq)
  }

  test("should save payload keys and return public key") {
    val generateRequest = new GenerateDataKeyPairWithoutPlaintextRequest().withKeyId("fake-kms-key").withKeyPairSpec("RSA_2048")
    val generateResponse = new GenerateDataKeyPairWithoutPlaintextResult()
      .withPublicKey(ByteBuffer.wrap(randomBytes()))
      .withPrivateKeyCiphertextBlob(ByteBuffer.wrap(randomBytes()))
    Mockito.when(kmsService.generateDataKeyPairWithoutPlaintext(generateRequest)).thenReturn(Future.successful(generateResponse))

    whenReady(service.createPayloadKeys(1, 2, 2048, "cust_key2", false)){ res =>
      res shouldBe 'right
      val publicKey = Base64.getEncoder.encodeToString(generateResponse.getPublicKey.array())
      res.right.get shouldBe publicKey
    }

    Mockito.verify(kmsService, Mockito.times(1)).generateDataKeyPairWithoutPlaintext(generateRequest)
  }

  test("should save payload keys using v2 endpoint and return public key") {
    val generateRequest = new GenerateDataKeyPairWithoutPlaintextRequest().withKeyId("arn:aws:kms:us-east-1:12345:alias/fake-multi-region-key").withKeyPairSpec("RSA_2048")
    val generateResponse = new GenerateDataKeyPairWithoutPlaintextResult()
      .withPublicKey(ByteBuffer.wrap(randomBytes()))
      .withPrivateKeyCiphertextBlob(ByteBuffer.wrap(randomBytes()))
    Mockito.when(kmsService.generateDataKeyPairWithoutPlaintext(generateRequest)).thenReturn(Future.successful(generateResponse))

    whenReady(service.createPayloadKeysV2(2, 2, 2048, "cust_key2", false)){ res =>
      res shouldBe 'right
      val publicKey = Base64.getEncoder.encodeToString(generateResponse.getPublicKey.array())
      res.right.get shouldBe publicKey
    }
  }

  test("should update payload keys and return public key") {
    val fakeWrappedPrivateKey = "cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="
    val fakeWrappingRef = "wrapref1"
    val decryptReq = new DecryptRequest().withKeyId(fakeWrappingRef).withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(fakeWrappedPrivateKey.getBytes)))
    Mockito.when(kmsService.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    whenReady(service.updatePayloadKeys(1, 1, Some("cust_key3"), false, 2048)){ res =>
      res shouldBe 'right
      res.right.get.customerPublicKey shouldBe "cust_key3"
    }

    Mockito.verify(kmsService, Mockito.times(2)).decrypt(decryptReq)
  }

  test("should update payload keys using v2 endpoint and return public key") {
    val fakeWrappedPrivateKey = "cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="
    val fakeWrappingRef = "arn:aws:kms:us-east-1:12345:alias/fake-multi-region-key"
    val decryptReq = new DecryptRequest().withKeyId(fakeWrappingRef).withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(fakeWrappedPrivateKey.getBytes)))
    Mockito.when(kmsService.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))
    Mockito.when(kmsServiceUSE2.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    whenReady(service.updatePayloadKeysV2(2, 1, Some("cust_key_new"), false, 2048)){ res =>
      res shouldBe 'right
      res.right.get.customerPublicKey shouldBe "cust_key_new"
    }
  }

  test("should clone payload keys and return public key") {
    val fakeWrappedPrivateKey = "cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="
    val fakeWrappingRef = "wrapref1"
    val multiRegionWrappingRef = "arn:aws:kms:us-east-1:12345:alias/fake-multi-region-key"

    val decryptReq = new DecryptRequest().withKeyId(fakeWrappingRef).withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(fakeWrappedPrivateKey.getBytes)))
    Mockito.when(kmsService.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))
    Mockito.when(kmsServiceUSE2.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    val encryptReq = new EncryptRequest().withKeyId(multiRegionWrappingRef).withPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))
    Mockito.when(kmsService.encrypt(encryptReq)).thenReturn(Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))
    Mockito.when(kmsServiceUSE2.encrypt(encryptReq)).thenReturn(Future.successful(new EncryptResult(){setCiphertextBlob(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    whenReady(service.clonePayloadKeys(3, 3)){ res =>
      res shouldBe 'right
      res.right.get shouldBe "public_key3"
    }
  }

  test("should merge payload keys and return list of merged keys") {
    val mergePayloadKeysRequest = MergePayloadKeysRequest(1, 1, Seq(AccountEnvironment(1, 2), AccountEnvironment(1,3)))
    val fakeWrappedPrivateKey = "cHJpdmF0ZUtleUVuY3J5cHRlZFdpdGhLbXM="
    val fakeWrappingRef = "wrapref1"
    val decryptReq = new DecryptRequest().withKeyId(fakeWrappingRef).withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(fakeWrappedPrivateKey.getBytes)))
    Mockito.when(kmsService.decrypt(decryptReq)).thenReturn(Future.successful(new DecryptResult(){setPlaintext(ByteBuffer.wrap(fakeWrappedPrivateKey.getBytes))}))

    whenReady(service.mergePayloadKeys(mergePayloadKeysRequest)){ res =>
      res shouldBe 'right
      res.right.get.size shouldBe 2
    }
  }

  test("should delete payload keys and return true") {
    whenReady(service.deletePayloadKeys(1, 1)){ res =>
      res shouldBe 'right
      res.right.get shouldBe true
    }
  }

  test("should fetch region correctly from kms key") {
    //simulate keys for commercial
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws:kms:us-east-1:123:alias/socure-account-service-payload-encryption-stage") shouldBe Regions.US_EAST_1
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws:kms:us-east-1:123:key/086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe Regions.US_EAST_1

    //simulate keys for govcloud
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws-us-gov:kms:us-gov-west-1:456:alias/socure-account-service-payload-encryption-stage") shouldBe Regions.GovCloud
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws-us-gov:kms:us-gov-west-1:456:key/086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe Regions.GovCloud

    //simulate keys for govcloud us-east region
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws-us-gov:kms:us-gov-east-1:789:alias/socure-account-service-payload-encryption-stage") shouldBe Regions.US_GOV_EAST_1
    AccountPayloadKeysService.getRegionFromWrappingKey("arn:aws-us-gov:kms:us-gov-east-1:789:key/086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe Regions.US_GOV_EAST_1

    //simulate keys without having arn prefix should default to us-east-1
    AccountPayloadKeysService.getRegionFromWrappingKey("086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe Regions.US_EAST_1
  }

  test("should fetch key id or alias name correctly from kms key") {
    //simulate keys for commercial
    AccountPayloadKeysService.getKeyIdOrAliasFromKey("arn:aws:kms:us-east-1:123:alias/socure-account-service-payload-encryption-stage") shouldBe "socure-account-service-payload-encryption-stage"
    AccountPayloadKeysService.getKeyIdOrAliasFromKey("arn:aws:kms:us-east-1:123:key/086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe "086a5d39-acdb-4c19-b5bf-a172428b6424"

    //simulate keys for govcloud
    AccountPayloadKeysService.getKeyIdOrAliasFromKey("arn:aws-us-gov:kms:us-gov-west-1:456:alias/socure/account-service-payload-encryption-stage") shouldBe "socure/account-service-payload-encryption-stage"
    AccountPayloadKeysService.getKeyIdOrAliasFromKey("arn:aws-us-gov:kms:us-gov-west-1:456:key/086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe "086a5d39-acdb-4c19-b5bf-a172428b6424"

    //simulate keys without having arn prefix should return the input key as it is
    AccountPayloadKeysService.getKeyIdOrAliasFromKey("086a5d39-acdb-4c19-b5bf-a172428b6424") shouldBe "086a5d39-acdb-4c19-b5bf-a172428b6424"
  }
}
