package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.BusinessUserRoles
import me.socure.model.account.{BusinessUserRolesLess, EnvironmentWithAccount, SocialNetworkAppKeys}
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.Creator
import me.socure.user.fixure.UserFixture
import org.joda.time.DateTime
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

/**
  * Created by sunderraj on 5/10/16.
  */
class AccountSettingServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with TestDataSourceConfig {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : AccountSettingService = _

  override val mysqlService: MysqlService = MysqlService("account-settings-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildService(socureDb)
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  /**
    * Get Account Setting
    */

  test("should return left when no new accountsettings entry exist") {
    val actual = service.getNewAccountSetting(22323)
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should return new accountsettings object when it exists") {
    val actual = service.getNewAccountSetting(1)
    whenReady(actual) { response =>
      response shouldBe 'right
      val expected = UserFixture.accountSettingsWithApiKeys
      response.fold(_ => fail,
        _.environments.headOption.fold(fail)(_.accessCredentials.secretKey shouldBe expected.environments.headOption.fold(fail)(_.accessCredentials.secretKey)))
      response.fold(_ => fail, _.environments.headOption.fold(fail)(_.accessCredentials.apiKeys.size shouldBe (2)))
      response.fold(_ => fail, _.environments.headOption.fold(fail)(_.canRenewApiKey equals  false))
    }
  }

  /*
  *  Test case for UPDATE DOMAIN
  * */

  test("should return account not found exception"){
    val actual = service.updateDomain(22323, List.empty)
    whenReady(actual) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should return successful response") {
    val response = service.updateDomain(1, List("0.0.0.0/0"))
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { res =>
        res.fold(_ => fail, _.environments.headOption.fold(fail)(_.domain should contain ("0.0.0.0/0")))
      }
    }
  }

  /*
  *  Test case for DELETE INDIVIDUAL CACHE
  * */

  test("should return individual cache id not found") {
    val response = service.deleteInvidiualCache(33)
    whenReady(response) { res =>
      res.fold(_.code shouldBe ExceptionCodes.IndividualCacheIdNotFound.id, _ => fail)
    }
  }

  test("should return cache deleted successfully") {
    val response = service.deleteInvidiualCache(100)
    whenReady(response) { res =>
      res shouldBe 'right
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.invidiualCache.headOption.fold(fail)(_.identifier shouldBe "individual")))
      }
    }
  }

  test("should return account not found using accountId") {
    val response = service.deleteInvidiualCache(33, "individual")
    whenReady(response) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should return cache deleted successfully using accountId") {
    val response = service.deleteInvidiualCache(1, "individual")
    whenReady(response) { res =>
      res shouldBe 'right
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.invidiualCache shouldBe empty))
      }
    }
  }

  /*
  *  Test case for UPDATE INDIVIDUAL CACHE
  *  def updateIndividualCache(accountIndividualCache: AccountIndividualCache)
  * */

  test("update individual cache should return account not found ") {
    val response = service.upsertIndividualCache(AccountIndividualCache(100, "sample", DateTime.now(), 33))
    whenReady(response) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("update individual cache should return successful response") {
    val response = service.upsertIndividualCache(AccountIndividualCache(102, "newly_inserted_identifier", DateTime.now(), 1))
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.invidiualCache should not be empty))
        as.fold(_ => fail,
          _.environments.headOption.fold(fail)(_.invidiualCache.headOption.fold(fail)(_.identifier should be ("newly_inserted_identifier"))))
      }
    }
  }

  /*
  *  Test case for DELETE ACCOUNT CACHE
  * */

  test("should return account not found on deleting cache") {
    val response = service.deleteAccountCache(33)
    whenReady(response) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should return account cache deleted successfully") {
    val response = service.deleteAccountCache(1)
    whenReady(response) { res =>
      res shouldBe 'right
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.overallCache shouldBe None))
      }
    }
  }

  /*
  *  Test case for UPDATE ACCOUNT CACHE
  * */

  test("update account cache should return account not found exception when invalid account id passed") {
    val response = service.upsertAccountCache(AccountOverallCache(100, DateTime.now(), 33))
    whenReady(response) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("update account cache should return successful response") {
    val response = service.upsertAccountCache(AccountOverallCache(102, DateTime.now(), 1))
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) {as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.overallCache should not be None))
      }
    }
  }

  /*
*  Test case for REMOVE SOCIAL NETWORK KEY
*  def removeSocialNetworkkeys(id : Long)
* */

  test("delete social key should return Social id not found") {
    val response = service.removeSocialNetworkkeys(20)
    whenReady(response) { res =>
      res.fold(_.code should be (ExceptionCodes.SocialKeyIdNotFound.id), _ => fail)
    }
  }

  test("delete social key should return success reponse") {
    val response = service.removeSocialNetworkkeys(1)
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts.headOption.fold(fail)(_.appkey shouldBe "appli_key3")))
      }
    }
  }

  test("delete social key should return account not found") {
    val response = service.removeSocialNetworkkeys(33, "Facebook")
    whenReady(response) { res =>
      res.fold(_.code should be (ExceptionCodes.AccountNotFound.id), _ => fail)
    }
  }

  test("delete social key should return success reponse by accountid and network") {
    val response = service.removeSocialNetworkkeys(1, "Facebook")
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts shouldBe empty))
      }
    }
  }

  /*
  *  Test case for UPDATE SOCIAL NETWORK KEY
  *  def updateSocialNetworkKey(socialNetworkAppKeys: SocialNetworkAppKeys)
  * */

  test("should return account not found when invalid account") {
    val response = service.upsertSocialNetworkKey(SocialNetworkAppKeys(10, "Facebook", "secret", "stage", 1, 33))
    whenReady(response) { res =>
      res.fold(_.code should be (ExceptionCodes.AccountNotFound.id), _ => fail)
    }
  }

  test("should return success response when ") {
    val response = service.upsertSocialNetworkKey(SocialNetworkAppKeys(10, "Facebook", "secret", "stage", 1, 1))
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts.size shouldBe 1))
      }
    }
  }

  test("update social key should return success response on different network") {
    val response = service.upsertSocialNetworkKey(SocialNetworkAppKeys(11, "Twitter", "secret", "stage", 1, 1))
    whenReady(response) { res =>
      res should be ('right)
      whenReady(service.getNewAccountSetting(1)) { as =>
        as.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts.size shouldBe 2))
      }
    }
  }

  test("get all environment list with account details") {
    val response = service.getAllEnvironmentWithAccountDetails
    whenReady(response) { res =>
      res.fold(_ => fail, _.nonEmpty shouldBe true)
      res.fold(_ => fail, _ should contain (EnvironmentWithAccount(1L, "AccountName1", "<EMAIL>", 1L, 1L)))
    }
  }

  test("get environment details by email") {
    val response = service.getEnvironmentByEmail("<EMAIL>")
    whenReady(response){ res =>
      res.fold(_ => fail, _.size shouldBe 3)
      res.fold(_ => fail, _.headOption.fold(fail)(_.id should be (1)))
    }
  }

  test("get environment details should say account not found") {
    val response = service.getEnvironmentByEmail("<EMAIL>")
    whenReady(response) { res =>
      res.fold(_.code should be (ExceptionCodes.AccountNotFound.id), _ => fail)
    }
  }

  test("get modules by account id should return modules provisioned for an v2 account") {
    val accountId = 1
    val creator = Creator(1, 1)
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res.fold(_ => fail, res => res.exists(_.id == BusinessUserRoles.EMAIL_RISK_SCORE.id) shouldBe true)
    }
  }

  test("get modules by v2 account id empty") {
    val accountId = 2
    val creator = Creator(2, 2)
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      //FIXME
      //res shouldBe Right(Seq.empty)
      res shouldBe Right(List(BusinessUserRolesLess(273, "MFA Orchestration")))
    }
  }

  test("get modules by v2 account id - invalid creator node") {
    val accountId = 2
    val creator = Creator(1, 2)
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
    }
  }

  test("get modules by v2 account id - access forbidden") {
    val accountId = 4
    val creator = Creator(2, 2)
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("get modules by account id should return modules provisioned for a v1 account") {
    val accountId = 24
    val creator = Creator(22, 24)
    val modules = Seq(BusinessUserRolesLess(13,"KYC"))
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res shouldBe Right(modules)
    }
  }

  test("get modules by account id should return empty modules for a v1 account") {
    val accountId = 22
    val creator = Creator(22, 22)
    val modules = List.empty
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res shouldBe Right(modules)
    }
  }

  test("get modules by account id should return invalid sub account") {
    val accountId = 24
    val creator = Creator(2, 2)
    whenReady(service.getModulesByAccountId(accountId, Some(creator),true)) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidSubAccount))
    }
  }

  test("get modules by account id should return modules provisioned") {
    val accountId = 24
    val modules = Seq(BusinessUserRolesLess(13, "KYC"))
    whenReady(service.getModulesByAccountId(accountId,None,false)) { res =>
      res.fold(_ => fail, res => res shouldBe (modules))
    }
  }


  test("get modules by account id with should return empty seq if no modules are provisioned") {
    val accountId = 22
    val modules = Seq.empty
    whenReady(service.getModulesByAccountId(accountId,None,false)) { res =>
      res.fold(_ => fail, res => res shouldBe (modules))
    }
  }

}
