package me.socure.account.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, InvalidEIN}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.ein.{EIN, EINRequest, EINResponse, LookupApiKey, LookupApiKeyRequest, LookupApiKeyResponse, LookupApiKeyServiceIdRequest, LookupApiKeyServiceIdResponse}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}

import java.util.Base64

class EINServiceTest extends FunSuite
  with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with Matchers with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var einService: EINService = _

  override val mysqlService: MysqlService = MysqlService("ein-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    einService = buildEINService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("update ein should not update for invalid ein"){
    val request = EINRequest(accountId = 100, ein = "789")
    val result = einService.upsertEIN(request)
    whenReady(result){ res =>
      res.fold(_ => ErrorResponseFactory.get(InvalidEIN), _ => fail)
    }
  }

  test("update ein should not update for invalid account"){
    val request = EINRequest(accountId = 100, ein = "*********")
    val result = einService.upsertEIN(request)
    whenReady(result){ res =>
      res.fold(_ => ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("update ein should update for valid account"){
    val request = EINRequest(accountId = 10, ein = "*********")
    val result = einService.upsertEIN(request)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe EINResponse(10, "*********"))
    }
  }

  test("update ein should update for valid account with preceding 0"){
    val request = EINRequest(accountId = 10, ein = "*********")
    val result = einService.upsertEIN(request)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe EINResponse(10, "*********"))
    }
  }


  test("should fetch ein for valid account and ECBSV provisioned"){
    val result = einService.fetchEIN(1L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe Some(EIN("*********")))
    }
  }

  test("update ein should update for valid account, delete and insert, in case of multiple eins found"){
    val request = EINRequest(accountId = 1L, ein = "*********")
    whenReady(einService.fetchEIN(1L)){ res =>
      res.fold(_ => fail, _ shouldBe Some(EIN("*********")))
    }
    val result = einService.upsertEIN(request)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe EINResponse(1, "*********"))
    }
  }

  test("should return None for valid account and ECBSV not provisioned"){
    val result = einService.fetchEIN(2L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe None)
    }
  }

  test("should return empty for valid account and does not have ein configured"){
    val result = einService.fetchEIN(2L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe None)
    }
  }

  test("should fail to fetch ein for invalid account"){
    val result = einService.fetchEIN(100L)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("update loopkupKey should not update for invalid account"){
    val request = LookupApiKeyRequest(accountId = 100L, lookupApiKey = "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==")
    val result = einService.upsertLookupApiKey(request)
    whenReady(result){ res =>
      res.fold(_ => ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("update lookupapikey should update for valid account"){
    val request = LookupApiKeyRequest(accountId = 10L, lookupApiKey = "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==")
    val result = einService.upsertLookupApiKey(request)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe LookupApiKeyResponse(10, "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ=="))
    }
  }

  test("should return empty for valid account and does not have lookupApiKey configured"){
    val result = einService.fetchLookupKey(1L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe None)
    }
  }

  test("should fail to fetch lookupApiKey for invalid account"){
    val result = einService.fetchLookupKey(100L)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("should fetch LookupApiKey for valid account and SimSwapLookup provisioned"){
    val result = einService.fetchLookupKey(2L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe Some(LookupApiKey("YXBpS2V5OnNlcnZpY2VJZA==")))
    }
  }

  test("update otpWorkFlowInfo should not update for invalid account"){
    val request = LookupApiKeyServiceIdRequest(accountId = 100L, apiKey = "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==", serviceId = "c2VydmljZUlk")
    val result = einService.upsertLookupApiKeyAndServiceSid(request)
    whenReady(result){ res =>
      res.fold(_ => ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("update otpWorkFlowInfo should update for valid account"){
    val request = LookupApiKeyServiceIdRequest(accountId = 10L, apiKey = "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==", serviceId = "c2VydmljZUlk")
    val result = einService.upsertLookupApiKeyAndServiceSid(request)
    whenReady(result){ res =>
      println(result)
      res.fold(_ => fail, _ shouldBe LookupApiKeyServiceIdResponse(10, "QUtJQUlPU0ZPRE5ON0VYQU1QTEU6d0phbHJYVXRuRkVNSS9LN01ERU5HL2JQeFJmaUNZRVhBTVBMRUtFWQ==", serviceId = "c2VydmljZUlk"))
    }
  }

  test("should return empty for valid account and does not have otpWorkFlowInfo configured"){
    val result = einService.fetchLookupKeyServiceSid(1L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe None)
    }
  }

  test("should fail to fetch otpWorkFlowInfo for invalid account"){
    val result = einService.fetchLookupKeyServiceSid(100L)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("should fetch otpWorkFlowInfo for valid account and MFAOrchestration provisioned"){
    val result = einService.fetchLookupKeyServiceSid(2L)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe Some(LookupApiKeyServiceIdResponse(2L, "YXBpS2V5OnNlcnZpY2VJZA==", "c2VydmljZUlk")))
    }
  }

}
