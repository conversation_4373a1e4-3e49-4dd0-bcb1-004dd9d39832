package me.socure.account.service

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.common.k8s.mysql.MysqlService
import org.flywaydb.core.Flyway
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 8/31/16.
  */
trait TestConfiguration extends FunSuite with BeforeAndAfterAll with Matchers with EitherValues with ScalaFutures{
  implicit val ec = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val mysqlService: MysqlService = MysqlService("account-service-tests")
  val dbName = "socure"
  val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  def prepareSchema(dataSource: DataSource, schema: String) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(schema)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def startMysqlService(schema: String) = {
    val dataSource = buildDataSource()
    prepareSchema(dataSource, schema)
  }

  override def afterAll() {
    mysqlService.stop
  }
}
