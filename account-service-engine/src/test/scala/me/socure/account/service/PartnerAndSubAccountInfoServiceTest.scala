package me.socure.account.service

import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.constants.{AccountTypes, DashboardUserPermissions, EnvironmentConstants, EnvironmentTypes, Status, SystemDefinedRoles}
import me.socure.DaoAccount
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.common.k8s.mysql.MysqlService
import me.socure.model.{BusinessUserRoles, PermissionTemplateInfo}
import me.socure.model.account.{MergeAccountDetailsInput, MigrationAccountDetails, _}
import me.socure.model.dashboardv2.Creator
import me.socure.model.user.{BusinessUserWithRolesV2, BusinessUserWithUserAccountAssociation}
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.utils.DBProxyWithMetrics
import org.joda.time.DateTime
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}
import slick.jdbc.JdbcBackend

/**
 * <AUTHOR> Kumar
 */
class PartnerAndSubAccountInfoServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var daoAccountV2: DaoAccountV2 = _
  var daoAccount:DaoAccount = _
  var service: PartnerAndSubAccountInfoService = _
  var dashboardUserServiceV2: DashboardUserServiceV2 = _
  var accountHierarchyService: AccountHierarchyService = _
  var accountAssociationHistoryService: AccountAssociationHistoryService = _
  var permissionTemplateService: PermissionTemplateService = _
  var userRoleService: UserRoleService = _
  var userAccountAssociationService: UserAccountAssociationService = _
  var sponsorBankService: SponsorBankService = _
  override val mysqlService: MysqlService = MysqlService("partner-and-sub-account-info-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    val db = JdbcBackend.Database.forDataSource(socureDb)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = socureDb
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    sponsorBankService = buildSponsorBankService(socureDb)
    service = buildPartnerAndSubAccountInfoService(socureDb, sponsorBankService)
    dashboardUserServiceV2 = buildDashboardUserServiceV2(socureDb)
    accountHierarchyService =  buildAccountHierarchyService(socureDb)
    accountAssociationHistoryService = buildAccountAssociationHistoryService(socureDb)
    permissionTemplateService = buildPermissionTemplateService(socureDb)
    userAccountAssociationService = buildUserAccountAssociationService(socureDb)
    userRoleService = buildUserRoleService(socureDb)

  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Fetch migration account details") {
    val accountId = 1
    val users = Vector(BusinessUserWithRolesV2(1,"Sunder", "Raj", "<EMAIL>", "**********", isPrimryAdmin = true, Vector.empty, Map("Production" -> Set("Admin"), "Development" -> Set.empty)))
    val modules = Vector("Admin", "User", "Email Risk Score", "Ecbsv", "Can Administer SubAccounts", "Improved Account Management", "Watchlist Monitoring", "Document Verification")
    val subAccounts =  Seq()
    val migrationAccountDetails = MigrationAccountDetails(accountId, "AccountName1", users, modules, subAccounts)
    whenReady(service.fetchAccountDetails(accountId)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe migrationAccountDetails.accountId
        r.users shouldBe migrationAccountDetails.users
        r.modules shouldBe migrationAccountDetails.modules
        r.subAccounts shouldBe migrationAccountDetails.subAccounts
      })
    }
  }

  test("Fetch migration account details - failure") {
    val accountId = 100
    whenReady(service.fetchAccountDetails(accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
  }

  test("migrate direct account") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 6, accountType = 1, Seq.empty, subAccounts = Vector(SubAccountMigrationDetails(accountId = 16, userId = None, administer = None)), associateAllUsersToSubAccount = true,  None, None, "username")
    val msg = service.migrateAccount(migrationAccountDetails, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }

    validateMigratedAccountDetails(migrationAccountDetails.accountId, migrationAccountDetails.accountType, None,  isPromote = false)

    whenReady(dashboardUserServiceV2.getUsers(migrationAccountDetails.accountId)) { res =>
      res.fold(_ => fail, users => {
        users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
      })
    }

    migrationAccountDetails.subAccounts.foreach(subAccount => {
      validateMigratedAccountDetails(subAccount.accountId, migrationAccountDetails.accountType, Some(migrationAccountDetails.accountId), isPromote = false)
      whenReady(dashboardUserServiceV2.getUsers(subAccount.accountId)) { res =>
        res.fold(_ => fail, users => {
          users.map { u => validateMigratedUser(u.id, subAccount.accountId, u.isLocked) }
        })
      }
      if(migrationAccountDetails.associateAllUsersToSubAccount) {
        whenReady(dashboardUserServiceV2.getUsers(migrationAccountDetails.accountId)) { res =>
          res.fold(_ => fail, users => {
            users.map { u => validateMigratedUser(u.id, subAccount.accountId, u.isLocked) }
          })
        }
      }
    })

  }

  test("migrate partner account") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 11, accountType = 2, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val msg = service.migrateAccount(migrationAccountDetails, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }

    validateMigratedAccountDetails(migrationAccountDetails.accountId, migrationAccountDetails.accountType, None, isPromote = false)

    whenReady(dashboardUserServiceV2.getUsers(migrationAccountDetails.accountId)) { res =>
      res.fold(_ => fail, users => {
        users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
      })

    }
  }

  test("migrate account as sub-accouunt of migrated account") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 21, accountType = 1, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val msg = service.migrateAccount(migrationAccountDetails, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }

    validateMigratedAccountDetails(migrationAccountDetails.accountId, migrationAccountDetails.accountType, None, isPromote = false)
    whenReady(dashboardUserServiceV2.getUsers(migrationAccountDetails.accountId)) { res =>
      res.fold(_ => fail, users => {
        users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
      })
    }

    val migrationAccountDetails1 = MigrationAccountDetailsInput(accountId = 22, accountType = 4, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, Some(21), None, "username")
    val res = service.migrateAccount(migrationAccountDetails1, DateTime.now())
    whenReady(res) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    validateMigratedAccountDetails(migrationAccountDetails1.accountId, migrationAccountDetails1.accountType, migrationAccountDetails1.parentAccountId, isPromote = false)
  }



  test("sub account migration") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 12, accountType = 1, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val msg = service.migrateAccount(migrationAccountDetails, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    validateMigratedAccountDetails(migrationAccountDetails.accountId, migrationAccountDetails.accountType, None, isPromote = false)
    whenReady(dashboardUserServiceV2.getUsers(migrationAccountDetails.accountId)) { res =>
      res.fold(_ => fail, users => {
        users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
      })
    }

    val migrationSubAccountDetails = MigrationSubAccountDetailsInput(accountId = 12, subAccounts = Vector(SubAccountMigrationDetails(accountId = 13, userId = None, administer = None)), associateAllUsersToSubAccount = false, "username")
    val res = service.migrateSubAccount(migrationSubAccountDetails, DateTime.now())
    whenReady(res) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    migrationSubAccountDetails.subAccounts.foreach(subAccount => {
      validateMigratedAccountDetails(subAccount.accountId, AccountTypes.SUB_ACCOUNT.id, Some(migrationSubAccountDetails.accountId), isPromote = false)

      whenReady(dashboardUserServiceV2.listAllUsers(migrationAccountDetails.accountId, Some(Creator(10, 12)))) { res =>
        res.fold(_ => fail, users => {
          users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
        })
      }
    })

  }

  ignore("promote account") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 14, accountType = 1, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val msg = service.migrateAccount(migrationAccountDetails, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    validateMigratedAccountDetails(migrationAccountDetails.accountId, migrationAccountDetails.accountType, None, isPromote = false)
    whenReady(dashboardUserServiceV2.listAllUsers(migrationAccountDetails.accountId, None)) { res =>
      res.fold(_ => fail, users => {
        users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
      })
    }

    val migrationSubAccountDetails = MigrationAccountDetailsInput(accountId = 14, accountType = 1, Seq.empty, subAccounts = Vector(SubAccountMigrationDetails(accountId = 15, userId = None, administer = None)), associateAllUsersToSubAccount = false, None, None, "username")
    val res = service.promoteSubAccount(migrationSubAccountDetails, DateTime.now())
    whenReady(res) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }
    migrationSubAccountDetails.subAccounts.foreach(subAccount => {
      validateMigratedAccountDetails(subAccount.accountId, migrationSubAccountDetails.accountType, None, isPromote = true)

      whenReady(dashboardUserServiceV2.listAllUsers(migrationAccountDetails.accountId, None)) { res =>
        res.fold(_ => fail, users => {
          users.map { u => validateMigratedUser(u.id, migrationAccountDetails.accountId, u.isLocked) }
        })
      }
    })

  }

  test("migrate account -failure") {
    val migrationAccountDetails = MigrationAccountDetailsInput(accountId = 10, accountType = 1, Seq.empty, subAccounts = Vector.empty, associateAllUsersToSubAccount = false, None, None, "username")
    whenReady(service.migrateAccount(migrationAccountDetails, DateTime.now())) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("merge account - success") {
    val mergeAccountDetailsInput = MergeAccountDetailsInput(1, 23, Seq.empty, Seq(SubAccountMigrationDetails(24, None, None)), false, "username")
    val msg = service.mergeAccount(mergeAccountDetailsInput, DateTime.now())
    whenReady(msg) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe true)
    }

    whenReady(daoAccountV2.isAccountPermissionProvisioned(mergeAccountDetailsInput.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) { res =>
      res shouldBe true
    }

    whenReady(dashboardUserServiceV2.listAllUsers(mergeAccountDetailsInput.mergeAccountId, Some(Creator(2, mergeAccountDetailsInput.mergeAccountId)))) { res =>
      res.fold(_ => fail, users => {
        users.map { u =>
          whenReady(userAccountAssociationService.getUserAccountAssociation(u.id, mergeAccountDetailsInput.accountId)) { uaa =>
            uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
              uaar.userRoles.isEmpty shouldBe false
            })
          }
        }
      })
    }

    mergeAccountDetailsInput.subAccounts.foreach(subAccount => {
      whenReady(daoAccountV2.isAccountPermissionProvisioned(subAccount.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) { res =>
        res shouldBe true
      }

      whenReady(accountHierarchyService.getAccountHierarchyByAccountId(subAccount.accountId)) { res =>
        res.fold(_ => fail, ah => {
          ah.hierarchyPath shouldBe s"${mergeAccountDetailsInput.mergeAccountId}/${subAccount.accountId}/"
          ah.hierarchyStatus shouldBe Status.ACTIVE.id
          ah.accountId shouldBe subAccount.accountId
          whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
            res0.fold(_ => fail, aah => {
              aah.accountHierarchyId shouldBe ah.id
            })
          }
        })
      }

      whenReady(dashboardUserServiceV2.listAllUsers(subAccount.accountId, Some(Creator(1, mergeAccountDetailsInput.mergeAccountId)))) { res =>
        res.fold(_ => fail, users => {
          users.map { u =>
            whenReady(userAccountAssociationService.getUserAccountAssociation(u.id, subAccount.accountId)) { uaa =>
              uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
                uaar.userRoles.isEmpty shouldBe false
              })
            }
          }
        })
      }
    })

  }

  test("merge account - failure") {
    val mergeAccountDetailsInput = MergeAccountDetailsInput(1, 100, Seq.empty, Seq.empty, false, "username")
    whenReady(service.mergeAccount(mergeAccountDetailsInput, DateTime.now())) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Fetch partner and sub account information with linkedPrograms") {
    val accountId = 3
     whenReady(service.fetchAccountDetailsV2(accountId)) { response =>
       println(response)
      response.fold(_ => fail, r => {
        r.accountId shouldBe accountId
        r.sponsorBankDetails shouldBe None
        r.linkedPrograms.nonEmpty shouldBe true
        r.linkedPrograms.find(_.programId.equals(1)).map(r0 =>
          r0.programName.equals("AccountName1") shouldBe true
        )
      })
    }
  }

  test("Fetch partner and sub account information with SponsorBank Details") {
    val accountId = 1
    val users = Set(BusinessUserWithUserAccountAssociation("Sunder", "Raj", "<EMAIL>", UserAccountAssociation(1,1,1,1,isPrimaryUser = false, Some(Set(3)),None)), BusinessUserWithUserAccountAssociation("Gopal", "haris", "<EMAIL>", UserAccountAssociation(2,2,1,1,isPrimaryUser = false, Some(Set(3)),Some(Set(1)))))
    val roles = Set(
      UserRole(None,"Administrator",Some(""),2),
      UserRole(Some(3),"PrimaryAdmin",Some("Primary Administrator")),
      UserRole(None,"Case Supervisor",Some(""),6),
      UserRole(None,"Analyst",Some(""),3),
      UserRole(Some(4),"InstanceAdmin",Some("Instance Administrator")),
      UserRole(None,"Account Owner",Some(""),1),
      UserRole(None,"Developer",Some(""),4),
      UserRole(None,"Case Analyst",Some(""),5),
      UserRole(None,"Case Officer",Some(""),7),
      UserRole(None,"DocV Analyst",Some("DocV Analyst"),8),
      UserRole(None,"RiskOS Support User",Some(""),14,None),
      UserRole(None,"BSA Officer",Some(""),9),
      UserRole(None,"Compliance Analyst",Some(""),10),
      UserRole(None,"Integration Manager",Some(""),11),
      UserRole(None,"Prospect",Some(""),12),
      UserRole(Some(1516),"Role 2",None,0,None),
      UserRole(None,"RiskOS Support Viewer",Some(""),13,None),
      UserRole(Some(1515),"Role 1",None,0,None)
    )
    val permissionTemplates = Seq(PermissionTemplateInfo(1,"template1",1,Some(3),Some("PrimaryAdmin"),Set(PermissionTemplateMappingInfo(1, "Production",Set("ACCOUNTS_CREATE", "ACCOUNTS_MODIFY", "ACCOUNTS_DELETE", "ACCOUNTS_VIEW"),None)),UserAccountAssociationMinimalInfo(1,1),"2020-08-04T14:52:08.000Z"), PermissionTemplateInfo(2,"template2",2,None,None,Set(),UserAccountAssociationMinimalInfo(1,1),"2020-08-04T14:52:08.000Z"), PermissionTemplateInfo(3,"template3",1,None,None,Set(),UserAccountAssociationMinimalInfo(2,1),"2020-08-04T14:52:08.000Z"), PermissionTemplateInfo(4,"template4",2,None,None,Set(),UserAccountAssociationMinimalInfo(1,1),"2020-08-04T14:52:08.000Z"), PermissionTemplateInfo(5,"template5",1,None,None,Set(),UserAccountAssociationMinimalInfo(2,1),"2020-08-04T14:52:08.000Z"))
    val partnerAndSubAccountInfo = PartnerAndSubAccountInfo(accountId = 1, name = "AccountName1", firstActivatedAt = Some("2016-05-05T00:00:00.000Z"), publicId = "publicId1", hierarchyPath = "1/", accountTypeId = AccountTypes.DIRECT_CUSTOMER.id ,accountType = AccountTypes.DIRECT_CUSTOMER.name, users = users, roles = roles, permissionTemplates = permissionTemplates, Seq(), Seq(), administer = false)
    whenReady(service.fetchAccountDetailsV2(accountId)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe accountId
        r.name shouldBe partnerAndSubAccountInfo.name
        r.accountType shouldBe partnerAndSubAccountInfo.accountType
        r.firstActivatedAt shouldBe partnerAndSubAccountInfo.firstActivatedAt
        r.publicId shouldBe partnerAndSubAccountInfo.publicId
        r.hierarchyPath shouldBe partnerAndSubAccountInfo.hierarchyPath
        r.roles shouldBe partnerAndSubAccountInfo.roles
        r.permissionTemplates.map(_.id) shouldBe partnerAndSubAccountInfo.permissionTemplates.map(_.id)
        r.linkedPrograms.nonEmpty shouldBe false
        r.sponsorBankDetails shouldBe Some(AccountIdName(3,"AccountName3"))
      })
    }
  }

  test("Fetch partner and sub account information - failure") {
    val accountId = 10
    whenReady(service.fetchAccountDetailsV2(accountId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  test("Fetch partner and sub account user information by user id") {
    val userId = 1
    val partnerAndSubAccountUserInfo = PartnerAndSubAccountUserInfo(1,"Sunder","Raj","<EMAIL>","2015-10-20T00:00:00.000Z",Set(UserAccountAssociationInfo(1,"active",Vector(UserRoleInfo("PrimaryAdmin", List(EnvironmentPermissions(EnvironmentTypes.GLOBAL_ENVIRONMENT.name, Set("ACCOUNTS_CREATE, ACCOUNTS_MODIFY, ACCOUNTS_DELETE, SETTINGS_MODIFY, ACCOUNTS_VIEW")),EnvironmentPermissions(EnvironmentTypes.PRODUCTION_ENVIRONMENT.name, Set("BATCHJOB_MODIFY, TEMPLATES_CREATE, ACCOUNTS_CREATE, ACCOUNTS_MODIFY, ACCOUNTS_DELETE, TEMPLATES_VIEW, BATCHJOB_VIEW, SETTINGS_MODIFY, BATCHJOB_DELETE, ACCOUNTS_VIEW, TEMPLATES_DELETE, TEMPLATES_MODIFY, BATCHJOB_CREATE"))))), None)))
    whenReady(service.fetchUserDetails(userId)) { response =>
      response equals Right(partnerAndSubAccountUserInfo)
    }
  }

  test("Fetch partner and sub account user information for invalid user id") {
    val userId = 100
    whenReady(service.fetchUserDetails(userId)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser))
    }
  }

  test("Fetch partner and sub account user information by email") {
    val email = "<EMAIL>"
    val partnerAndSubAccountUserInfo = PartnerAndSubAccountUserInfo(1,"Sunder","Raj","<EMAIL>","2015-10-20T00:00:00.000Z",Set(UserAccountAssociationInfo(1,"active",Vector(UserRoleInfo("PrimaryAdmin", List(EnvironmentPermissions(EnvironmentTypes.GLOBAL_ENVIRONMENT.name, Set("ACCOUNTS_CREATE, ACCOUNTS_MODIFY, ACCOUNTS_DELETE, SETTINGS_MODIFY, ACCOUNTS_VIEW")),EnvironmentPermissions(EnvironmentTypes.PRODUCTION_ENVIRONMENT.name, Set("BATCHJOB_MODIFY, TEMPLATES_CREATE, ACCOUNTS_CREATE, ACCOUNTS_MODIFY, ACCOUNTS_DELETE, TEMPLATES_VIEW, BATCHJOB_VIEW, SETTINGS_MODIFY, BATCHJOB_DELETE, ACCOUNTS_VIEW, TEMPLATES_DELETE, TEMPLATES_MODIFY, BATCHJOB_CREATE"))))), None)))
    whenReady(service.fetchUserDetailsByEmail(email)) { response =>
      response equals Right(partnerAndSubAccountUserInfo)
    }
  }

  test("Fetch partner and sub account user information by invalid email") {
    val email = "<EMAIL>"
    whenReady(service.fetchUserDetailsByEmail(email)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser))
    }
  }

  def validateMigratedAccountDetails(accountId: Long, accountType: Int, parentId: Option[Long], isPromote: Boolean): Unit = {

    if(parentId.isDefined) {
      whenReady(daoAccount.getAccountPermissions(parentId.get)) { parentPermissions =>
        whenReady(daoAccount.getAccountPermissions(accountId)) { subAccountPermissions =>
          parentPermissions.map(_.permission).toSet.subsetOf(subAccountPermissions.map(_.permission).toSet) shouldBe true
        }
      }

      whenReady(daoAccountV2.getAccountAttributes(parentId.get)) { parentAttributes =>
        whenReady(daoAccountV2.getAccountAttributes(accountId)) { subAccountAttributes =>
          subAccountAttributes.map(_.name).toSet.subsetOf(parentAttributes.map(_.name).toSet) shouldBe true
        }
      }
    }

    whenReady(daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) { res =>
      res shouldBe true
    }

    whenReady(accountHierarchyService.getAccountHierarchyByAccountId(accountId)) { res =>
      res.fold(_ => fail, ah => {
        parentId match {
          case None =>
            ah.hierarchyPath shouldBe s"$accountId/"
            ah.hierarchyStatus shouldBe Status.ACTIVE.id
            ah.accountId shouldBe accountId
            ah.accountType shouldBe accountType
          case Some(parentAccountId) =>
            ah.hierarchyPath shouldBe s"$parentAccountId/$accountId/"
            ah.hierarchyStatus shouldBe Status.ACTIVE.id
            ah.accountId shouldBe accountId
            ah.accountType shouldBe AccountTypes.SUB_ACCOUNT.id
        }


        whenReady(accountAssociationHistoryService.getAccountAssociationHistory(ah.id)) { res0 =>
          res0.fold(_ => fail, aah => {
            aah.accountHierarchyId shouldBe ah.id
          })
        }
      })
    }
  }

  def validateMigratedUser(userId: Long, accountId: Long, isLocked: Boolean): Unit = {
    whenReady(userAccountAssociationService.getUserAccountAssociation(userId, accountId)) { uaa =>
      uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
        uaar.userRoles.isEmpty shouldBe false
        if(isLocked)
          uaar.status shouldBe UserAccountAssociationStatuses.LOCKED.id
        else
          uaar.status shouldBe UserAccountAssociationStatuses.ACTIVE.id
        whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
          pts.fold(_ => fail, ptsms => {
            ptsms.map { ptsm =>
              whenReady(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(ptsm.id, uaar.userId, uaar.accountId)) { pstmOpt =>
                pstmOpt.fold(_ => fail, { a =>
                  a.exists(_.environmentTypeId.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id))
                  a.exists(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                  a.exists(_.environmentTypeId.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id))
                  a.filter(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id)).map { perms =>
                    perms.permissions.isEmpty shouldBe false
                    perms.permissions.exists(_.equals(DashboardUserPermissions.TRANSACTIONS_VIEW.id))
                  }
                })
              }
            }
          })
        }
      })
    }
    whenReady(userRoleService.getUserRolesByAccountId(accountId, Creator(userId, accountId))) { ur =>
      ur.fold(_ => fail, r => {
        r.filter(role => role.roleType == SystemDefinedRoles.CUSTOMROLE.roleType).map { r0 =>
          whenReady(userRoleService.getRolePermissionTemplateAssociation(r0.id.getOrElse(0), userId, accountId)) { ura =>
            ura.fold(_ => fail, rr => rr.userRoleId shouldBe r0.id.get)
          }
        }
      })
    }
  }

  test("Should list all migrated accounts") {
    val account1 = MigratedAccount(1,"AccountName1",1,"1/")

    whenReady(service.getMigratedParentAccounts()) { response =>
      response.fold(_ => fail, l => {
        l.nonEmpty shouldBe true
        l.contains(account1) shouldBe true
      })
    }
  }

  test("Should fail to associate User, Account and Role, while trying to associate invalid user") {
    whenReady(service.associateUserAccountRole(userId = 200, accountId = 5, userRoleId = Option(5), roleType = 0, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UnknownError), _ => fail)
    }
  }

  test("Should fail to associate User, Account and Role, while trying to associate invalid account") {
    whenReady(service.associateUserAccountRole(userId = 2, accountId = 500, userRoleId = Option(5), roleType = 0, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.AccessForbidden), _ => fail)
    }
  }

  test("Should fail to associate User, Account and Role, while trying to associate invalid role") {
    whenReady(service.associateUserAccountRole(userId = 2, accountId = 3, userRoleId = Option(150), roleType = 0, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound), _ => fail)
    }
  }

  test("Should associate User, Account and Role") {
    whenReady(service.associateUserAccountRole(userId = 2, accountId = 5, userRoleId = Option(5), roleType = 0, updatedBy = None)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.associateUserAccountRole(userId = 2, accountId = 5, userRoleId = Option(5), roleType = 0, updatedBy = None)) { response0 =>
        response0.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UserAccountRoleAssociationExists), _ => fail)
      }
    }
  }

  test("Should associate User, Account and System Role") {
    whenReady(service.associateUserAccountRole(userId = 2, accountId = 5, userRoleId = None, roleType = 1, updatedBy = None)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.associateUserAccountRole(userId = 2, accountId = 5, userRoleId = None, roleType = 1, updatedBy = None)) { response0 =>
        response0.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UserAccountRoleAssociationExists), _ => fail)
      }
    }
  }

  test("Should fail to associate more than max System Role") {
    whenReady(service.associateUserAccountRole(userId = 3, accountId = 5, userRoleId = None, roleType = 1, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.RoleLimitReached), _ => fail)
    }
  }

  test("Should not associate an invalid role type") {
    whenReady(service.associateUserAccountRole(userId = 3, accountId = 5, userRoleId = None, roleType = 99, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidRoleType), _ => fail)
    }
  }

  test("Should not associate an invalid custom role") {
    whenReady(service.associateUserAccountRole(userId = 3, accountId = 5, userRoleId = None, roleType = 0, updatedBy = None)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidCustomRole), _ => fail)
    }
  }

  test("Should associate a system role that does not have limit restrictions") {
    whenReady(service.associateUserAccountRole(userId = 3, accountId = 5, userRoleId = None, roleType = 3, updatedBy = None)) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("Should not update account type to Direct") {
    whenReady(service.updateAccountType(accountId = 6, accountType = 1)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType), _ => fail)
    }
  }

  test("Should not update account type to Aggregator") {
    whenReady(service.updateAccountType(accountId = 6, accountType = 3)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType), _ => fail)
    }
  }

  test("Should not update account type to SubAccount") {
    whenReady(service.updateAccountType(accountId = 6, accountType = 4)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType), _ => fail)
    }
  }

  test("Should update account type to Reseller") {
    whenReady(service.updateAccountType(accountId = 6, accountType = 2)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.fetchAccountDetailsV2(accountId = 6)) { response0 =>
        response0.fold(_ => fail, { a =>
          a.accountType.equals("2")
        })
      }
    }
  }

  test("Should not update account type if same account type") {
    whenReady(service.updateAccountType(accountId = 6, accountType = 2)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountType), _ => fail)
    }
  }

  test("Should not update account type if account is sub  account") {
    whenReady(service.updateAccountType(accountId = 13, accountType = 2)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountType), _ => fail)
    }
  }

  test("Should update prospect account type to Direct customer") {
    whenReady(service.updateAccountType(accountId = 27, accountType = 1)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.fetchAccountDetailsV2(accountId = 27)) { response0 =>
        response0.fold(_ => fail, { a =>
          a.accountType.equals("1")
        })
      }
    }
  }

  test("Should update adminster flag") {
    whenReady(service.updateAdminister(accountId = 6, administer = true)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.fetchAccountDetailsV2(accountId = 6)) { response0 =>
        response0.fold(_ => fail, { a =>
          a.administer.equals(true)
        })
      }
    }
  }

  test("Should fail to associate User(non functional), Account and Role") {
    whenReady(service.associateUserAccountRole(userId = 15, accountId = 25, userRoleId = Option(5), roleType = 0 , updatedBy = None)) { response =>
      println(response)
    }
  }

  test("Should fail to swap user due to incorrect userId account association") {
    whenReady(service.swapUserRoles(1, 3, 1)) {
      response => response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
    }
  }

  test("Should fail to swap user due to incorrect swappingUserId Account association") {
    whenReady(service.swapUserRoles(1, 1, 3)) {
      response => response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
    }
  }

  test("Should swap user roles") {
    whenReady(service.swapUserRoles(2, 2, 3)) {
      response => response shouldBe Right(true)
    }
  }

  test("Should update isSponsorBank option") {
    whenReady(service.updateIsSponsorBank(accountId = 6, isSponsorBank = true, initiatedBy = "<EMAIL>")) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(service.fetchAccountDetailsV2(accountId = 6)) { response0 =>
        response0.fold(_ => fail, { a =>
          a.isSponsorBank shouldBe true
        })
      }
    }
  }

  test("Should fail to update isSponsorBank option") {
    whenReady(service.updateIsSponsorBank(accountId = 600, isSponsorBank = true, initiatedBy = "<EMAIL>")) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateIsSponsorBank), _ => fail)
    }
  }
}