package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.superadmin.LockedUserService
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.{MySQLServerAwait, SQLExecutor}
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

/**
  * Created by sunderraj on 6/3/16.
  */
class LockedUserServiceTest extends FunSuite with Matchers with EitherValues with ScalaFutures with BeforeAndAfterAll with TestDataSourceConfig {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  var service : LockedUserService = _
  var daoBusinessUser : DaoBusinessUser = _
  override val mysqlService: MysqlService = MysqlService("locked-user-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    val objs = buildLockedUserService(socureDb)
    service = objs._2
    daoBusinessUser = objs._1
  }

  override protected def afterAll(): Unit = {
		dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("size of the record should be ge 1") {
    val result = service.getLockedUserList
    whenReady(result) {res =>
      res.fold(_ => fail, _.size should be >= 1)
      res.fold(_ => fail, _.headOption.fold(fail)(_.getEmail shouldBe "<EMAIL>"))
    }
  }

  test("affected record size should 1 and last login should be updated") {
    val result = service.unlockUser(List("<EMAIL>"))
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe 1)

      whenReady(daoBusinessUser.getUser("<EMAIL>")){ u =>
        u.fold(fail)(_.lastLoginOn.fold(fail)(_.getMillis should be (clock.now().getMillis)))
      }
    }
  }

  test("should return business user not found") {
    val result = service.unlockUser(List("<EMAIL>"))
    whenReady(result){ res =>
      res.fold(_.code shouldBe ExceptionCodes.BusinessUserNotFound.id, _ => fail)
    }
  }

  test("delete bad login count should return 0, because record wont be available") {
    val result = service.removeBadLockCount(3)
    whenReady(result){ res =>
      res should be (0)
    }
  }

}
