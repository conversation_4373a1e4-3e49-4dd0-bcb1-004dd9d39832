package me.socure.account.service


import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.model.WatchlistSourceForEnvironment
import me.socure.model.dashboardv2.Creator
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import scalacache.ScalaCache


class WatchlistSourceServiceTest extends FunSuite with ScalaFutures with Matchers with EitherValues with BeforeAndAfterAll with OptionValues with TestDataSourceConfig with MemcachedTestSupport {

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))
  var service: WatchlistSourceService = _
  override val mysqlService: MysqlService = MysqlService("watchlist-source-service")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "watchlist-source-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildwatchlistSourceService(socureDb, scalaCache)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("list watchlist sources should list all available sources") {
    whenReady(service.list) { response =>
      response.fold(_ => fail, a =>
        a.nonEmpty shouldBe true
      )
    }
  }

  test("list watchlist sources should list all available sources for the category") {
    whenReady(service.listByCategory(1)) { response =>
      response.fold(_ => fail, a =>
        a.nonEmpty shouldBe true
      )
    }
  }

  test("list watchlist sources should return empty list for invalid category") {
    whenReady(service.listByCategory(100)) { response =>
      response.fold(_ => fail, a =>
        a.isEmpty shouldBe true
      )
    }
  }

  test("list watchlist sources should list all available sources for the sub-category") {
    whenReady(service.listBySubCategory(1)) { response =>
      response.fold(_ => fail, a =>
        a.nonEmpty shouldBe true
      )
    }
  }

  test("list watchlist sources should return empty list for invalid subcategory") {
    whenReady(service.listBySubCategory(100)) { response =>
      response.fold(_ => fail, a =>
        a.isEmpty shouldBe true
      )
    }
  }

  test("list included watchlist sources for an environment") {
    whenReady(service.includedWatchlistSources(1L, Some(Creator(1,1)))) { response =>
      response.fold(_ => fail, { a =>
        a.nonEmpty shouldBe true
        a.exists(_.id == 1) shouldBe true
        a.exists(_.id == 5) shouldBe true
        a.exists(_.id == 10) shouldBe true
        a.exists(_.id == 15) shouldBe false
      }
      )
    }
  }

  test("list included watchlist sources for an invalid environment should be empty") {
    whenReady(service.includedWatchlistSources(10L, None)) { response =>
      response.fold(_ => fail, a =>
        a.isEmpty shouldBe true
      )
    }
  }

  test("include watchlist source for an environment") {
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(1L, Set(12L, 5L, 1L), Some(Creator(1, 1)))
    whenReady(service.includeWatchlistSource(watchlistSourceForEnvironment)) { response =>
      response._2.fold(_ => fail, { a =>
        a shouldBe true
        whenReady(service.includedWatchlistSources(1L, Some(Creator(1, 1)))) { response =>
          response.fold(_ => fail, { fl =>
            fl.exists(_.id == 12) shouldBe true
            fl.exists(_.id == 10) shouldBe false
          })
        }
      })
    }
  }

  test("should fail for - include watchlist source for an invalid environment/source") {
    val watchlistSourceForEnvironment: WatchlistSourceForEnvironment = WatchlistSourceForEnvironment(100L, Set(12L), Some(Creator(2, 6)))
    whenReady(service.includeWatchlistSource(watchlistSourceForEnvironment)) { response =>
      response._2.fold(_ => ErrorResponseFactory.get(ExceptionCodes.EnvironmentNotFound), _ => fail)
    }
  }

  test("exclude watchlist source for an environment if sourceIds is empty") {
    val watchlistSourceForEnvironment: WatchlistSourceForEnvironment = WatchlistSourceForEnvironment(1L, Set.empty, Some(Creator(1, 1)))
    whenReady(service.includeWatchlistSource(watchlistSourceForEnvironment)) { response =>
      response._2.fold(_ => fail, { a =>
        a shouldBe true
        whenReady(service.includedWatchlistSources(1L, Some(Creator(1, 1)))) { response =>
          response.fold(_ => fail, _.isEmpty shouldBe true)
          whenReady(service.includedWatchlistSourcesNames(1L)) { aresponse =>
            aresponse.fold(_ => fail, _.isEmpty shouldBe true)
          }
        }
      })
    }
  }

  test("list included sources for an environment") {
    whenReady(service.includedWatchlistSourcesNames(1L)) { response =>
      response.fold(_ => fail, a => a.size shouldBe 0)
    }
  }

  test("list included sources should be an emptylist for an invalid environment") {
    whenReady(service.includedWatchlistSourcesNames(100L)) { response =>
      response.fold(_ => fail, a => a.isEmpty shouldBe true)
    }
  }

  test("list watchlist sub-categories") {
    whenReady(service.listWatchlistSubCategories()) { response =>
      response.nonEmpty shouldBe true
    }
  }

  test("list watchlist categories") {
    whenReady(service.listWatchlistCategories()) { response =>
      response.nonEmpty shouldBe true
    }
  }

  test("import watchlist sources with valid category and valid sub category") {
    val sources: String = "Source List Name,Category,List Type,Location\nArgentina Ministerio de Relaciones Exteriores y Culto Sanciones de la ONU,Sanctions,Warnings,ArgentinaIndia\nAustralian National Security Terrorism List,Sanctions,Sanctions,Australiaa\nnewdata,Sanctions,Sanctions,newlocation123456\nKumar,Sanctions,Sanctions,klafjdk\nSumit,Sanctions,Sanctions,lsjfls"
    whenReady(service.importWatchlistSources(sources)) { invalidLRecordist =>
      invalidLRecordist.fold(_ => fail, a => a.isEmpty shouldBe true)
    }
  }

  test("import watchlist sources with unicode chars") {
    val sources: String = "Source List Name,Category,List Type,Location\n" +
      "Brazil Bolsa BalcÃ£o S.A. Notifications and Penalties Applied,Sanctions,Warnings,ArgentinaIndia\n" +
      "Decisions Bureau de décision et de révision Quebec,Sanctions,Sanctions,Australiaa\nnewdata,Sanctions,Sanctions,newlocation123456\n" +
      "Germany Bundesamt für Verfassungsschutz Prohibited Foreigner Extremist Organisations,Sanctions,Sanctions,klafjdk\n" +
      "Ukraine Sanctions National Security and Defense Council (NSDC) Special Economic and Other Restrictive Measures – Organisations,Sanctions,Sanctions,klafjdk\n" +
      "Mexico Comision Nacional de Seguros Y Fianzas - Sanciones a personas que intermediaron sin autorización,Sanctions,Sanctions,lsjfls"
    whenReady(service.importWatchlistSources(sources)) { invalidLRecordist =>
      invalidLRecordist.fold(_ => fail, a => {
        a.isEmpty shouldBe true
      })
    }
  }

  test("import watchlist sources with invalid category") {
    val sources: String = "Source List Name,Category,List Type,Location\nArgentina Ministerio de Relaciones Exteriores y Culto Sanciones de la ONU,Sanctions,Warnings,ArgentinaIndia\nAustralian National Security Terrorism List,InvalidCategory,Sanctions,Australiaa"
    whenReady(service.importWatchlistSources(sources)) { invalidCategoryList =>
      invalidCategoryList.fold(_ => fail, a => a.isEmpty shouldBe false)
    }
  }

  test("import watchlist sources with invalid sub category") {
    val sources: String = "Source List Name,Category,List Type,Location\nArgentina Ministerio de Relaciones Exteriores y Culto Sanciones de la ONU,Sanctions,Warnings,ArgentinaIndia\nAustralian National Security Terrorism List,InvalidCategory,Sanctions,InvalidSubCategory"
    whenReady(service.importWatchlistSources(sources)) { invalidSubCategoryList =>
      invalidSubCategoryList.fold(_ => fail, a => a.isEmpty shouldBe false)
    }
  }

  test("should fail for - include watchlist source v2 for an invalid environment/source") {
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(100L, Set(12L), Some(Creator(1,1)))
    whenReady(service.includeWatchlistSource(watchlistSourceForEnvironment)) { response =>
      response._2.fold(e => e.code shouldBe 122, _ => fail)
    }
  }

  test("exclude watchlist source v2 for an environment if sourceIds is empty") {
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(1L, Set.empty, Some(Creator(1,1)))
    whenReady(service.includeWatchlistSource(watchlistSourceForEnvironment)) { response =>
      response._2.fold(_ => fail, { a =>
        a shouldBe true
        whenReady(service.includedWatchlistSources(1L, Some(Creator(1,1)))) { response =>
          response.fold(_ => fail, _.isEmpty shouldBe true)
          whenReady(service.includedWatchlistSourcesNames(1L)) { aresponse =>
            aresponse.fold(_ => fail, _.isEmpty shouldBe true)
          }
        }
      })
    }
  }
}
