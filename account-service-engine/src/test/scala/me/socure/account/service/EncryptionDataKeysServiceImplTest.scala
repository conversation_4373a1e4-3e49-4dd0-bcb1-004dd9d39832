package me.socure.account.service

import java.security.SecureRandom
import javax.crypto.SecretKey
import javax.crypto.spec.SecretKeySpec

import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.ExecutionContext
import scala.util.Random

class EncryptionDataKeysServiceImplTest extends FreeSpec with Matchers with ScalaFutures {

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(
    timeout = Span(5, Seconds),
    interval = Span(50, Milliseconds)
  )
  private val secretKey = generateSecretKey()
  private val keyId = "some key id"
  private val service = EncryptionDataKeysServiceFactory.get(
    secretKey = secretKey,
    keyId = keyId
  )


  "EncryptionDataKeysServiceImpl" - {
    "should encrypt and decrypt properly" in {
      for (_ <- 1 to 10) {
        val message = createMsg()
        whenReady(service.encryptMessage(message)) { encryptedMessage =>
          whenReady(service.decryptMessage(encryptedMessage = encryptedMessage)) { decryptedMessage =>
            decryptedMessage shouldBe message
          }
        }
      }
    }
  }

  private def generateSecretKey(): SecretKey = {
    val secretKeyBytes = new Array[Byte](16) //128 bits
    new SecureRandom().nextBytes(secretKeyBytes)
    new SecretKeySpec(secretKeyBytes, "AES")
  }

  private def createMsg(): String = {
    Random.alphanumeric.take(2097152).mkString
  }
}
