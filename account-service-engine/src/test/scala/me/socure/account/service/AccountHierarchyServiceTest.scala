package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes.UnableToUpdateAccountHierarchy
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.sql.SQLExecutor
import me.socure.constants.Status
import me.socure.model.account.{AccountHierarchy, AccountHierarchyInput, AccountInfoV2WithIndustry}
import me.socure.storage.slick.dao.DaoAccountV2
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, OptionValues, Matchers => MMatchers}

class AccountHierarchyServiceTest extends FunSuite
  with MMatchers with ScalaFutures with MockitoSugar with EitherValues
  with BeforeAndAfterAll with OptionValues with TestDataSourceConfig {
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  var service: AccountHierarchyService = _
  var accountAssociationHistoryService: AccountAssociationHistoryService = _
  override val mysqlService: MysqlService = MysqlService("account-hierarchy-service")
  private val dbName = "socure"

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource, dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)
    insertData(sqlExecutor)
    service = buildAccountHierarchyService(socureDb)
    accountAssociationHistoryService = buildAccountAssociationHistoryService(socureDb)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  val daoAccountV2: DaoAccountV2 = mock[DaoAccountV2]
  val fakeClock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("Get account hierarchy by id - success") {
    val accountHierarchy = AccountHierarchy(1,1,"1/",1,1,administer = true,0)
    whenReady(service.getAccountHierarchy(1)) { response =>
      response shouldBe Right(accountHierarchy)
    }
  }

  test("Get account hierarchy by id - failure") {
    whenReady(service.getAccountHierarchy(100)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound))
    }
  }

  test("Get account hierarchy by account id - success") {
    val expected = AccountHierarchy(1,1,"1/",1,1,administer = true,0)
    whenReady(service.getAccountHierarchyByAccountId(1)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Get account hierarchy by account id - failure") {
    whenReady(service.getAccountHierarchyByAccountId(100)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound))
    }
  }

  test("Insert account hierarchy - success") {
    val accountHierarchy = AccountHierarchyInput(None,6,"/6",2,1,administer = true,Some(0))
    val expected = 1
    whenReady(service.insertAccountHierarchy(accountHierarchy)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Insert account hierarchy - failure") {
    val accountHierarchy = AccountHierarchyInput(None,100,"/100",2,1,administer = true,Some(0))
    whenReady(service.insertAccountHierarchy(accountHierarchy)) { response =>
      response shouldBe 'Left
    }
  }

  test("Update account hierarchy - success") {
    val accountHierarchy = AccountHierarchyInput(Some(1),1,"1/",2,1,administer = true,Some(0))
    val expected = 1
    whenReady(service.updateAccountHierarchy(accountHierarchy)) { response =>
      response shouldBe Right(expected)
    }
  }

  test("Update account hierarchy - failure") {
    val accountHierarchy = AccountHierarchyInput(Some(2),100,"/100",2,1,administer = true,Some(0))
    whenReady(service.updateAccountHierarchy(accountHierarchy)) { response =>
      response shouldBe 'Left
    }
  }

  test("update account hierarchy status -  success") {
    whenReady(service.updateHierarchyStatus(1, Status.ACTIVE.id)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(accountAssociationHistoryService.getAssociationHistoryForAccount(1)) { response0 =>
        response0.fold(_ => fail, s => {
          s.accountHierarchyId shouldBe 1
          s.deactivatedAt shouldBe None
        })
      }
    }
  }

  test("update account hierarchy status (inactive)-  success") {
    whenReady(service.updateHierarchyStatus(1, Status.INACTIVE.id)) { response =>
      response.fold(_ => fail, _ shouldBe true)
      whenReady(accountAssociationHistoryService.getAssociationHistoryForAccount(1)) { response0 =>
        response0.fold(_ => fail, s => {
          s.accountHierarchyId shouldBe 1
          s.deactivatedAt should be ('defined)
        })
      }
    }
  }

  test("update account hierarchy status -  failure") {
    whenReady(service.updateHierarchyStatus(100, Status.ACTIVE.id)) { response =>
      response.fold(_ should be (ErrorResponseFactory.get(UnableToUpdateAccountHierarchy)), _ => fail)
    }
  }

  test("List account hierarchy for Direct customer (parent account): Success") {
    val accountInfo = Vector(AccountInfoV2WithIndustry(1,1,"publicId1","AccountName1","1/",1,administer = true,0,state = true,"Banking",accountStatus = true),
      AccountInfoV2WithIndustry(2,2,"publicId2","AccountName2","1/2/",4,administer = true,0,state = true,"Banking",accountStatus = true),
      AccountInfoV2WithIndustry(3,3,"publicId3","AccountName3","1/2/3/",4,true,0,state = true,"Banking",accountStatus = true),
      AccountInfoV2WithIndustry(5,5,"publicId5","AccountName5","1/2/5/",4,administer = true,0,state = true,"Banking",accountStatus = false))
    whenReady(service.listAccountHierarchy(1, 1)) { response =>
      response.fold(_ =>fail, fb = res => {
        res should not be empty
        res.size should be >= 3
        val accountNames = accountInfo.map(_.accountName).toSet
        res.map(_.accountName).toSet.subsetOf(accountNames) shouldBe true
      })
    }
  }

  test("List account hierarchy Direct customer (sub account) and non primary user: Success") {
    whenReady(service.listAccountHierarchy(2, 2)) { response =>
      response.fold(_ =>fail, res => {
        res should not be empty
        res.size shouldBe 1
        res.exists(k => !k.state) shouldBe false
      })
    }
  }

  test("List account hierarchy - administer flag: Success") {
    whenReady(service.listAccountHierarchy(4, 2)) { response =>
      val acc17 = AccountInfoV2WithIndustry(6,17,"publicId17","AccountName17","4/17/",4,administer = true,0,state = true, "Banking", accountStatus = true)
      val acc18 = AccountInfoV2WithIndustry(7,18,"publicId18","AccountName18","4/18/",4,administer = false,0,state = false, "Banking", accountStatus = true)
      val acc19 = AccountInfoV2WithIndustry(8,19,"publicId19","AccountName19","4/17/19",4,administer = false,0,state = true, "Banking", accountStatus = true)
      val acc20 = AccountInfoV2WithIndustry(9,20,"publicId20","AccountName20","4/18/20",4,administer = false,0,state = false, "Banking", accountStatus = true)
      val checkRes = List(acc17, acc18, acc19, acc20)
      response.fold(_ =>fail, res => {
        res should not be empty
        res.exists(f => checkRes.contains(f)) shouldBe true
        res.exists(f => checkRes.contains(f.copy(id=1))) shouldBe false

      })
    }
  }

  test("List account hierarchy - sub account: Success") {
    whenReady(service.listAccountHierarchy(17, 2)) { response =>
      val acc17 = AccountInfoV2WithIndustry(6,17,"publicId17","AccountName17","4/17/",4,administer = true,0,state = true, "Banking", accountStatus = true)
      val acc19 = AccountInfoV2WithIndustry(8,19,"publicId19","AccountName19","4/17/19",4,administer = false,0,state = true, "Banking", accountStatus = true)
      val checkRes = List(acc17, acc19)
      response.fold(_ =>fail, res => {
        res should not be empty
        res.exists(f => checkRes.contains(f)) shouldBe true
        res.exists(f => checkRes.contains(f.copy(id=18))) shouldBe false

      })
    }
  }

  test("Validate account access - success") {
    whenReady(service.validateAccountAccess(2, 1)) { response =>
      response.fold(_ => fail, res => {
        res shouldBe true
      })
    }
  }

  test("Validate account access - failure") {
    whenReady(service.validateAccountAccess(1, 2)) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("Validate account access - Request for V2 with invalid V1 Creator - failure") {
    whenReady(service.validateAccountAccess(13, 1)) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("Validate account access - Request for V1 with invalid V2 Creator - failure") {
    whenReady(service.validateAccountAccess(1, 13)) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("Validate account access - Request for V2 with invalid V2 Creator - failure") {
    whenReady(service.validateAccountAccess(17, 13)) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("validate account access with permissions V2 - fail") {
    whenReady(service.validateAccountAccess(2, 1, Set(13))) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("validate account access with permissions V2 - success") {
    whenReady(service.validateAccountAccess(2, 1, Set(22))) { response =>
      response.fold(_ => fail, res => {
        res shouldBe true
      })
    }
  }

  test("validate account access with permissions V1 - fail for invalid access") {
    whenReady(service.validateAccountAccess(24, 1, Set(47))) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

    test("validate account access with permissions V1 - fail for invalid permission") {
    whenReady(service.validateAccountAccess(24, 23, Set(7))) { response =>
      response.fold(_ => fail, res => {
        res shouldBe false
      })
    }
  }

  test("validate account access with permissions V1 - success") {
    whenReady(service.validateAccountAccess(24, 23, Set(13))) { response =>
      response.fold(_ => fail, res => {
        res shouldBe true
      })
    }
  }

  test("get parent account - for v1 parent account") {
    whenReady(service.getRootParent(6)) { response =>
      response shouldBe 6
    }
  }

  test("get parent account - for v1 sub account") {
    whenReady(service.getRootParent(8)) { response =>
      response shouldBe 7
    }
  }

  test("get parent account - for v2 parent account") {
    whenReady(service.getRootParent(1)) { response =>
      response shouldBe 1
    }
  }

  test("get parent account - for v2 sub account level 1") {
    whenReady(service.getRootParent(2)) { response =>
      response shouldBe 1
    }
  }

  test("get parent account - for v2 sub account level 2") {
    whenReady(service.getRootParent(5)) { response =>
      response shouldBe 1
    }
  }

  test("List subaccounts (DirectCustomer): Success") {
    whenReady(service.getSubAccounts(1)) { response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.contains(5) shouldBe true
      })
    }
  }

  test("List subaccounts(Partner): Success") {
    whenReady(service.getSubAccounts(4)) { response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.contains(17) shouldBe true
        r.contains(18) shouldBe false
      })
    }
  }

  test("List subaccounts(SubAccount): Success") {
    whenReady(service.getSubAccounts(5)) { response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe false
      })
    }
  }
}
