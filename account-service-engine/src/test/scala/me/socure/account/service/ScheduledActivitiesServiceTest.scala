package me.socure.account.service

import java.util.concurrent.TimeUnit
import javax.sql.DataSource
import com.amazonaws.regions.Regions
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService, BundleManagementService}
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.{AccountInfoService, InactiveUserService, LockedUserService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.kms.KmsService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{BadLoginConfig, ScheduledActivityConfig}
import me.socure.mail.service.MailNotificationService
import me.socure.model.management.client.ModelManagementClient
import me.socure.salt.client.SaltClient
import me.socure.salt.model.SaltValueGenerator
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao.{DaoAccountBundleAssociation, DaoAccountUIConfiguration, DaoAccountV2, DaoEncryptionKeys, DaoEnvironment, DaoMagicLinkAudit, DaoProspect, DaoPublicApiKey, DaoRateLimit, DaoServiceEncryptionKeys, DaoSubscriptions}
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.mockito.Mockito.when
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by mariabaskar on 03/04/2017.
  */
class ScheduledActivitiesServiceTest extends FunSuite with Matchers with ScalaFutures with BeforeAndAfterAll with EitherValues with MockitoSugar with MemcachedTestSupport {
  implicit val ex : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(15, Seconds), interval = Span(500, Millis))

  import me.socure.account.service.fixture.EncryptionKeysServiceImplTestFixture._
  val mailNotificationServiceMock = mock[MailNotificationService]
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  val config = ScheduledActivityConfig(90, 20, 24, 24, 90)

  val modelManagementClient = mock[ModelManagementClient]
  val saltClient = mock[SaltClient]
  val aSalt = SaltValueGenerator.aSalt()
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))

  var daoBusinessUser : DaoBusinessUser = _
  var samlValidator: SamlValidator = _
  var service : ScheduledActivitiesService = _
  var businessUserService : BusinessUserService = _
  var lockedUserService : LockedUserService = _
  var passwordService : PasswordService = _
  private val daoEncryptionKeys = mock[DaoEncryptionKeys]
  private val daoServiceEncryptionKeys = mock[DaoServiceEncryptionKeys]

  private class MockableKmsService extends KmsService(null)

  private val kmsServiceUsEast1 = mock[MockableKmsService]
  private val kmsServiceUsWest1 = mock[MockableKmsService]
  private val mockNoAccountKeysFetcher = mock[UnknownAccountEncryptionKeysFetcher]
  val mysqlService: MysqlService = MysqlService("scheduled-activities-service")
  private val dbName = "socure"
  private val daoAccount = mock[DaoAccount]
  override def memcachedPodLabel(): String = "scheduled-activities-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private val encryptionKeysService: EncryptionKeysService = new EncryptionKeysServiceImpl(
    daoEncryptionKeys = daoEncryptionKeys,
    daoServiceEncryptionKeys = daoServiceEncryptionKeys,
    kmsIdsConfig = kmsIdsConfig,
    dataKeyLen = dataKeyLen,
    kmsServices = Map(
      Regions.US_EAST_1 -> kmsServiceUsEast1,
      Regions.US_WEST_1 -> kmsServiceUsWest1
    ),
    clock = clock,
    daoAccount = daoAccount,
    unknownAccountEncryptionKeysFetcher = mockNoAccountKeysFetcher,
    scalaCache = scalaCache,
    serviceKMSDetails = Seq.empty
  )

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildPasswordService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)

    val samlValidator = new SamlValidator(
      accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
  }

  private def buildScheduledService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val inactiveUserService =  new InactiveUserService(daoBusinessUser, samlValidator, clock)
    new ScheduledActivitiesService(daoBusinessUser, clock, config, passwordService = passwordService, mailNotificationService = mailNotificationServiceMock, magicLinkAuditService = magicLinkAuditService, inactiveUserService = inactiveUserService)
  }

  private def buildLockedUserService() = {
    new LockedUserService(daoBusinessUser, clock)
  }

  private def buildBusinessuserService(dataSource : DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val samlValidator = new SamlValidator(
      new AccountInfoService(daoAccount = daoAccount, v2Validator),
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val config = BadLoginConfig(2, true, FiniteDuration(1, TimeUnit.MINUTES))
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val rateLimitingService = mock[RateLimitingService]
    val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val accountAutomationService = mock[AccountAutomationService]
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserService(daoBusinessUser, daoAccount, daoEnvironment, config, passwordService,
      encryptionKeysService, samlValidator, clock, daoPublicApiKey, daoSubscriptions, daoAccountV2,
      daoRateLimit, pbeEncryptor, rateLimitingService, daoAccountUIConfiguration, modelManagementClient,
      businessUserCommonService, v2Validator, magicLinkAuditService, accountAutomationService, mailNotificationServiceMock, accountBundleAssociationService, sessionIdleTimeout = 480, daoProspect, whitelistedEmailDomain = Set("socure.com"))
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5')"
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,NULL, '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, NULL, '2015-10-20', 1, NULL, NULL, 1, false), " +
      "(3, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2017-03-01',  '2015-10-20', 1, NULL, NULL, 1, false)," +
      "(5, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 1, false), " +
      "(6, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 0, NULL, NULL, 2, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,NULL, '2015-10-20', 1, NULL, NULL, 3, true), " +
      "(8, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,NULL, '2017-04-04', 1, NULL, NULL, 4, true), " +
      "(9, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2017-03-12', '2017-03-04', 1, NULL, NULL, 5, true)"
    )

    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (1, '49a11f5245eefff691abdd71f42c109a', 'active', 'v1', current_timestamp, current_timestamp)")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (5, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, '2016-12-14 23:00:00')")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (7, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, '2016-12-13 23:00:00')")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (8, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, '2016-12-18 23:00:00')")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (9, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, '2016-12-23 23:00:00')")
    sqlExecutor.execute("INSERT INTO tbl_password (business_user_id, password, status, hash_algorithm, created_at, updated_at) VALUES (2, '149185a7d4462666fed26ad66dc01bec', 'active', 'v1', current_timestamp, '2017-01-23 23:00:00')")

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 2, 1), " +
      "(4, 'accesstoken_sand', 'secretkey_sand', 'accesstokensecret_sand', 'development.com',2, 2)"
    )

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1), (1, 5, 4), (1, 5, 6), (2, 5, 5), (2, 5, 7), (NULL, 1, 8)")

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(1, 22)")

    //Account Attribute
    sqlExecutor.execute("INSERT INTO tbl_account_attribute(account_id, name, value) VALUES(1, 'SLA', '1')")

    sqlExecutor.execute("INSERT INTO tbl_activation_token(id, business_user_id, token, created_at) VALUES(0, 1, 'valid_activation_code', '2017-03-13'), (0, 2, 'valid_activation_code1', '2017-03-14')")

    passwordService = buildPasswordService(socureDb)
    service = buildScheduledService(socureDb)
    businessUserService = buildBusinessuserService(socureDb)
    lockedUserService = buildLockedUserService()
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("should lock all user inactive for more than 90 days") {
    whenReady(service.lockInactiveBusinessUser){res =>
      res shouldBe 'right
      whenReady(lockedUserService.getLockedUserList){r =>
        r.right.value.size should be (2)
        r.right.value.map(_.email) should not contain "<EMAIL>"
        r.right.value.map(_.email) should contain ("<EMAIL>")
      }
    }
  }

  test("should lock primary business user not logged in for more than 20 days after register") {
    whenReady(service.lockPrimaryUserNotLoggedIn){ res =>
      res shouldBe 'right
      whenReady(lockedUserService.getLockedUserList) { r =>
        r.right.value.map(_.email) should contain ("<EMAIL>")
        r.right.value.map(_.email) should not contain "<EMAIL>"
        r.right.value.map(_.email) should not contain "<EMAIL>"
      }
    }
  }

  test("should invalidated activation token after 24 hours"){
    whenReady(service.invalidateActivationCode){res =>
      res shouldBe 'right
      whenReady(businessUserService.activateUserByActivationcode("valid_activation_code_err")){r =>
        r shouldBe 'left
        r.left.value.code shouldBe ExceptionCodes.InvalidActivationCode.id
        whenReady(businessUserService.activateUserByActivationcode("valid_activation_code1")){rr =>
          rr shouldBe 'right
          rr.right.value shouldBe true
        }
      }
    }
  }

  ignore("should send reset password notification email and invalidate password on expiry date"){
    when(mailNotificationServiceMock.sendResetPasswordNotification(firstName = "firstName", lastName = "lastName", days=org.mockito.Matchers.any[Int], email=org.mockito.Matchers.any[String], activationCode=org.mockito.Matchers.any[String], 0L))
      .thenReturn(Future.successful(Right(true)))
    whenReady(passwordService.hasValidPassword(7)) { r =>
      r shouldBe true
    }
    whenReady(service.passwordResetNotification) { res =>
      res shouldBe 'right
      res.right.value shouldBe true
      org.mockito.Mockito.verify(mailNotificationServiceMock,
                                org.mockito.Mockito.times(3)).sendResetPasswordNotification(
                                                                  firstName = "firstName",
                                                                  lastName = "lastName",
                                                                  days=org.mockito.Matchers.any[Int],
                                                                  email=org.mockito.Matchers.any[String],
                                                                  activationCode=org.mockito.Matchers.any[String], 0L)
      whenReady(passwordService.hasValidPassword(9)) { r =>
        r shouldBe true
      }
      whenReady(passwordService.hasValidPassword(7)) { r =>
        r shouldBe false
      }
    }
  }

}
