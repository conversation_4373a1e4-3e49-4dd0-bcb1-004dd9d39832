package me.socure.account.prospect

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.prospect.{ProspectExclusionInput, ProspectInclusionInput}
import me.socure.storage.slick.dao.DaoProspect
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class ProspectServiceTest extends FunSuite
  with ScalaFutures
  with EitherValues
  with MockitoSugar
  with Matchers
  with BeforeAndAfterAll {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2024-04-23").withZone(DateTimeZone.UTC).getMillis)
  val mysqlService: MysqlService = MysqlService("prospect-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  var prospectService: ProspectService = _

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildServices(dataSource: DataSource): Unit = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    prospectService = new ProspectService(daoProspect)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)
    buildServices(dataSource)

    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_prospect_blacklisted_domains (category, email_domain, created_by, updated_by) VALUES " +
      "(1, 'testPersona.com', 'Admin', 'Admin')," +
      "(1,'testidology.com','Admin','Admin')," +
      "(1,'testAtdata.com','Admin','Admin'),"+
      "(1,'testAU10TIX.com','Admin','Admin')," +
      "(1,'testEkata.com','Admin','Admin');"
    )

    sqlExecutor.execute("INSERT INTO tbl_prospect_inclusion_list_domains (email_domain, created_by, updated_by) VALUES " +
      "('testactivisionblizzard.com', 'Admin', 'Admin')," +
      "('testepicgames.com', 'Admin', 'Admin')," +
      "('testea.com', 'Admin', 'Admin')," +
      "('testzynga.com', 'Admin', 'Admin')," +
      "('testgrilla.gg', 'Admin', 'Admin');"
    )
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Should list All exclusion details") {
    whenReady(prospectService.getExclusionList(Option.empty, Option.empty, "")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should get count of exclusion details") {
    whenReady(prospectService.getExclusionListTotalCount) { response =>
      response.fold(_ => fail, { r =>
        r should not be 0
      })
    }
  }

  test("Should search in list of available exclusion details(endsWith)") {
    whenReady(prospectService.getExclusionList(Option.empty, Option.empty, "data")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should search in list of available exclusion details(Case Insensitive substring)") {
    whenReady(prospectService.getExclusionList(Option.empty, Option.empty, "au10")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should update an exclusion detail 1") {
    val input = ProspectExclusionInput(Some(1), "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateExclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should insert an exclusion detail 1") {
    val input = ProspectExclusionInput(Option.empty, "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateExclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should create an exclusion detail when given id is unavailable") {
    val input = ProspectExclusionInput(Some(10), "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateExclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should delete an exclusion detail") {
    val input = 5
    whenReady(prospectService.deleteExclusionDetail(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should not delete an exclusion detail when given id is unavailable") {
    val input = 50
    whenReady(prospectService.deleteExclusionDetail(input)) { response =>
      response.fold(_ => fail, _ => false)
    }
  }

  test("Should list All inclusion details") {
    whenReady(prospectService.getInclusionList(Option.empty, Option.empty, "")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should get count of inclusion details") {
    whenReady(prospectService.getInclusionListTotalCount) { response =>
      response.fold(_ => fail, { r =>
        r should not be 0
      })
    }
  }

  test("Should search in list of available inclusion details(endsWith)") {
    whenReady(prospectService.getInclusionList(Option.empty, Option.empty, "games")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should search in list of available inclusion details(Case Insensitive substring)") {
    whenReady(prospectService.getInclusionList(Option.empty, Option.empty, "Gril")) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
      })
    }
  }

  test("Should update an inclusion detail 1") {
    val input = ProspectInclusionInput(Some(1), "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateInclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should insert an inclusion detail 1") {
    val input = ProspectInclusionInput(Option.empty, "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateInclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should create an inclusion detail when given id is unavailable") {
    val input = ProspectInclusionInput(Some(10), "<EMAIL>", "test_user")
    whenReady(prospectService.insertOrUpdateInclusionList(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should delete an inclusion detail") {
    val input = 5
    whenReady(prospectService.deleteInclusionDetail(input)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("Should not delete an inclusion detail when given id is unavailable") {
    val input = 50
    whenReady(prospectService.deleteInclusionDetail(input)) { response =>
      response.fold(_ => fail, _ => false)
    }
  }

}
