package me.socure.account.sponsorbank

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.audit.AccountAuditService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{NoSponsorBankLinked, UnableToLinkSponsorBankProgram, UnableToUnLinkSponsorBankProgram}
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.SponsorBankProgramLinkRequest
import me.socure.storage.slick.dao.{DaoAccountAudit, DaoSponsorBankProgram}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mockito.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class SponsorBankServiceTest
  extends FunSuite
    with ScalaFutures
    with EitherValues
    with MockitoSugar
    with Matchers
    with BeforeAndAfterAll {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  val mysqlService: MysqlService = MysqlService("sponsor-bank-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  var sponsorBankService: SponsorBankService = _

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildServices(dataSource: DataSource): Unit = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountAudit = new DaoAccountAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountAuditService = new AccountAuditService(daoAccountAudit, daoAccount, clock)
    val daoSponsorBankProgram = new DaoSponsorBankProgram(dbProxyWithMetrics, slick.driver.MySQLDriver)
    sponsorBankService = new SponsorBankService(daoSponsorBankProgram, accountAuditService, clock)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)
    buildServices(dataSource)

    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id, is_sponsor_bank) VALUES " +
      "(1, 'AccountName1', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1', 'public_api_key_1','externalId1', false)," +
      "(2, 'AccountName2', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId2', 'public_api_key_2','externalId2', true), " +
      "(3, 'AccountName3', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId3', 'public_api_key_3','externalId3', true), " +
      "(4, 'AccountName4', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId4', 'public_api_key_4','externalId4', false), " +
      "(5, 'AccountName5', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId5', 'public_api_key_5','externalId5', false); ")

    sqlExecutor.execute("INSERT INTO tbl_sponsor_bank_program (id, sponsor_bank_id, program_id, created_by, created_at) VALUES " +
      "(1, 3, 1, '<EMAIL>', '2023-07-10'), " +
      "(2, 3, 5, '<EMAIL>', '2023-07-10');" )
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Should list All Programs for Sponsor Bank") {
    whenReady(sponsorBankService.getLinkedPrograms(3)) { response =>
      response.fold(_ => fail, { r =>
        r.nonEmpty shouldBe true
        r.exists(_.programId.equals(1))
      })
    }
  }

  test("Should list empty Programs for Sponsor Bank") {
    whenReady(sponsorBankService.getLinkedPrograms(4)) { response =>
      response.fold(_ => fail, { r =>
        r.isEmpty shouldBe true
      })
    }
  }

  test("List All Non Linked Sponsor bank") {
    whenReady(sponsorBankService.getNonSponsorBankPrograms()) { response =>
      response.fold(_ => fail, { r =>
        println(r)
        r.nonEmpty shouldBe true
        r.exists(_.id.equals(2))
      })
    }
  }

  test("should link Program with Sponsor Bank") {
    whenReady(sponsorBankService.linkSponsorBankProgram(SponsorBankProgramLinkRequest(2,4, "<EMAIL>"))) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should fail to link Program with Sponsor Bank]") {
    whenReady(sponsorBankService.linkSponsorBankProgram(SponsorBankProgramLinkRequest(1,4, "<EMAIL>"))) { response =>
      response.fold(_ => ErrorResponseFactory.get(UnableToLinkSponsorBankProgram), _ => fail)
    }
  }

  test("should unlink Program from Sponsor Bank") {
    whenReady(sponsorBankService.unlinkSponsorBankProgram(SponsorBankProgramLinkRequest(3,1, "<EMAIL>"))) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("should fail to unlink Program from Sponsor Bank") {
    whenReady(sponsorBankService.unlinkSponsorBankProgram(SponsorBankProgramLinkRequest(4,4, "<EMAIL>"))) { response =>
      response.fold(_ => ErrorResponseFactory.get(UnableToUnLinkSponsorBankProgram), _ => fail)
    }
  }

  test("should unlink all Programs from Sponsor Bank") {
    whenReady(sponsorBankService.unlinkSponsorBankPrograms(1, "<EMAIL>")) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("should fetch Sponsor Bank for the Program") {
    whenReady(sponsorBankService.getSponsorBank(5)) { response =>
      println(response)
      response.fold(_ => fail, _ => AccountIdName(5, "AccountName5"))
    }
  }

  test("should fail fetch Sponsor Bank for the Program") {
    whenReady(sponsorBankService.getSponsorBank(3)) { response =>
      response.fold(_ => ErrorResponseFactory.get(NoSponsorBankLinked), _ => fail)
    }
  }

}
