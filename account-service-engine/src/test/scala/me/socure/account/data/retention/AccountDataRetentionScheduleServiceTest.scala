package me.socure.account.data.retention

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.storage.slick.dao.{DaoAccountDataRetentionSchedule, DaoAccountV2}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{BeforeAndAfter<PERSON>ll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

class AccountDataRetentionScheduleServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  var accountDataRetentionScheduleService: AccountDataRetentionScheduleService = _
  val mysqlService: MysqlService = MysqlService("account-data-retention-schedule-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildServices(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountDataRetentionSchedule = new DaoAccountDataRetentionSchedule(dbProxyWithMetrics, slick.driver.MySQLDriver)
    accountDataRetentionScheduleService = new AccountDataRetentionScheduleService(daoAccountDataRetentionSchedule, daoAccountV2, clock)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1', 'public_api_key_1','externalId1')," +
      s"(2, 'AccountName2', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId2', 'public_api_key_2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId3', 'public_api_key_3','externalId3'); ")

    sqlExecutor.execute("INSERT INTO tbl_account_permission(account_id, permission, enabled) VALUES" +
      "(1, 187, 1) ")

    sqlExecutor.execute("INSERT INTO tbl_account_data_retention_schedule(account_id, cadence, routine, updated_by, updated_at) VALUES" +
    "(1, 3, 'Days', '<EMAIL>', '2022-09-09') ")
    buildServices(dataSource)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get Account Data Retention Schedule for account id - success") {
    whenReady(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(1)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe(1)
        r.routine shouldBe("Days")
        r.cadence shouldBe(3)
        r.updatedBy shouldBe("<EMAIL>")
      })
    }
  }

  test("Get Account Data Retention Schedule for account id - fail") {
    val expected = Left(AccountNotFound)
    whenReady(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(100)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("Get Account Data Retention Schedule with hierarchy for account id - success") {
    whenReady(accountDataRetentionScheduleService.getAccountDataRetentionScheduleWithHierarchy(1)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe(1)
        r.routine shouldBe("Days")
        r.cadence shouldBe(3)
        r.subAccounts shouldBe(Set.empty)
      })
    }
  }

  test("Get Account Data Retention Schedule with hierarchy for account id - fail") {
    val expected = Left(AccountNotFound)
    whenReady(accountDataRetentionScheduleService.getAccountDataRetentionScheduleWithHierarchy(100)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

  test("Get Account Data Retention Schedule  - success") {
    val expected = Seq(AccountDataRetentionSchedule(1, 3, "Days"))
    whenReady(accountDataRetentionScheduleService.getAccountDataRetentionSchedule()) { response =>
      response.fold(_ => fail, _ => expected)
    }
  }

  test("Update Account Data Retention Schedule - success") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(2, "Days", "<EMAIL>")
    whenReady(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(3, updateAccountDataRetentionSchedule)) { response =>
      response.fold(_ => fail, k => {
        k.accountId shouldBe 3
        k.cadence shouldBe 2
        k.routine shouldBe "Days"
      })
    }
  }

  ignore("Update Account Data Retention Schedule - fail") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(2, "Days", "<EMAIL>")
    whenReady(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(300, updateAccountDataRetentionSchedule)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

}
