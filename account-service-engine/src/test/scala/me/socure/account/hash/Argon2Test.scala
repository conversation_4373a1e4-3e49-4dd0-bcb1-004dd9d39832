package me.socure.account.hash

import org.scalatest.{FunSuite, Matchers}

class Argon2Test extends FunSuite with Match<PERSON> {

//  test("should work with same password") {
//    val salt = "AF6990D8-96DA-4170-A6DC-5FB619BDB4E0"
//    val hash = Argon2.hash("new_password", salt)
//    Argon2.verify(hash, "new_password", salt) shouldBe true
//  }
//
//  test("should work with different password") {
//    val salt = "AF6990D8-96DA-4170-A6DC-5FB619BDB4E0"
//    val hash = Argon2.hash("new_password", salt)
//    Argon2.verify(hash, "wrong", salt) shouldBe false
//    Argon2.verify(hash, "new_password", salt) shouldBe true
//  }
}
