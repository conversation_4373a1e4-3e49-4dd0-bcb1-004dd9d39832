package me.socure.account.automation

import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class BundleManagementServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val bundleManagementService: BundleManagementService = new BundleManagementService()

  test("Get account bundle association by account id") {
    whenReady(bundleManagementService.getBundles()) { response =>
      response.fold(_ => fail, r => {
        r.size should be (8)
      })
    }
  }
}
