package me.socure.account.automation

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.audit.AccountAuditService
import me.socure.account.service.AccountHierarchyService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountBundleAssociationNotFound, AccountNotFound}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.automation
import me.socure.model.account.automation.AccountBundleAssociation
import me.socure.storage.slick.dao.{Dao<PERSON><PERSON>unt<PERSON><PERSON>t, DaoAccountBundleAssociation, DaoAccountV2}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountBundleAssociationServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  var service: AccountBundleAssociationService = _
  var accountBundleAssociationAuditService: AccountAuditService = _
  val mysqlService: MysqlService = MysqlService("account-bundle-association-service")
  private val dbName = "socure"
  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildAccountBundleAssociationService(dataSource: DataSource): AccountBundleAssociationService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountBundleAuditAssociation = new DaoAccountAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountBundleAssociationAuditService = new AccountAuditService(daoAccountBundleAuditAssociation, daoAccount, clock)

    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)

    new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1', 'public_api_key_1','externalId1')," +
      s"(2, 'AccountName2', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId2', 'public_api_key_2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', true, 1, 2, false, '2016-05-05 00:00:00', 'publicId3', 'public_api_key_3','externalId3'); ")

      sqlExecutor.execute("INSERT INTO tbl_account_bundle_association(account_id, bundle_reference, created_by, created_at) VALUES "+
      "(1, 'Bundle One', '<EMAIL>', '2022-09-09'), " +
      "(2, 'Bundle Two', '<EMAIL>', '2022-09-09');")
    service = buildAccountBundleAssociationService(dataSource)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  test("Get account bundle association by account id - success") {
    val expected = automation.DtoAccountBundleAssociation(1,1,"Bundle One","<EMAIL>", clock.now, None, None)
    whenReady(service.getAccountBundleAssociation(1)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe expected.accountId
        r.bundleReference shouldBe expected.bundleReference
        r.createdBy shouldBe expected.createdBy
      })
    }
  }

  test("Get account bundle association for sub account id - success") {
    val expected = automation.DtoAccountBundleAssociation(1, 2, "Bundle Two", "<EMAIL>", clock.now, None, None)
    whenReady(service.getAccountBundleAssociation(3)) { response =>
      response.fold(_ => fail, r => {
        r.accountId shouldBe expected.accountId
        r.bundleReference shouldBe expected.bundleReference
        r.createdBy shouldBe expected.createdBy
      })
    }
  }

  test("Get account bundle association by account id - fail") {
    whenReady(service.getAccountBundleAssociation(100)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccountBundleAssociationNotFound), _ => fail)
    }
  }

  test("Update account bundle association For account id - success") {
    val expected = automation.DtoAccountBundleAssociation(2,3,"Bundle Three","<EMAIL>", clock.now, None, None)
    whenReady(service.upsertAccountBundleAssociation(AccountBundleAssociation(3, "Bundle Three", "<EMAIL>"))) { response =>
      response.fold(_ => fail, k => {
        k.accountId shouldBe expected.accountId
        k.bundleReference shouldBe expected.bundleReference
        (k.id > 0)  shouldBe true
      })
    }
  }

  test("Update account bundle association For account id - fail") {
    whenReady(service.upsertAccountBundleAssociation(AccountBundleAssociation(300, "Bundle Three", "<EMAIL>"))) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
  }

}
