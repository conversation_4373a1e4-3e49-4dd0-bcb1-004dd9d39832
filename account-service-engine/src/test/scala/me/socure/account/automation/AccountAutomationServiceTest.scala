package me.socure.account.automation

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.audit.AccountAuditService
import me.socure.account.data.retention.AccountDataRetentionScheduleService
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, AccountProvisioningNotUpdated, InvalidProduct, ProductConfigNotUpdated, UnknownError}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{AccountHierarchyService, AccountPgpKeysService, AccountPreferencesService, AuditDetailsService, DvConfigurationService, EINService, ProductService, RateLimitingService, SaiPreferencesService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.account.{AccountDashboardDomain, DvConfiguration, ProductProvisioningTypes, RateLimiterPublicAPI}
import me.socure.model.account.automation.{AccountProvisioningDetails, ProductConfiguration, UpdateAccountProvisioningDetails}
import me.socure.model.account.data.retention.{DtoAccountDataRetentionSchedule, RetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.model.ein.{EIN, EINRequest, EINResponse, LookupApiKey}
import me.socure.model.{AccountProducts, BusinessUserRoles, RateLimitingWindowSizes, UpdateProduct, UpdateProductsRequest}
import me.socure.storage.slick.dao.{DaoAccountAudit, DaoAccountBundleAssociation, DaoAccountV2, DaoDvConfiguration, DaoKycPreferences, DaoRateLimit, DaoWatchlistPreferences}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{never, times}
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import me.socure.account.superadmin.ActiveUsersService
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.constants.{AccountProvisioningConfiguration, DecisionConfiguration, EnvironmentTypes, KycPreferences, RateLimit}
import me.socure.decision.service.client.DecisionServiceClientV2
import me.socure.decision.service.common.models.v2.{BestLogicCloneRequest, DecisionLogicFilterRequest, DecisionLogicSortRequest, DecisionLogicStates, ListLogicRequest, LogicDetailsResponse, LogicDetailsWithMappingsResponse, RecommendedLogicCloneResponse}
import me.socure.document.manager.client.DocumentManagerClient
import me.socure.document.manager.model.doctype.{DtoDocumentType, DtoDocumentTypeAccount, DtoProvisionAccount}
import me.socure.docv.client.DocvOrchestraClient
import me.socure.model.dashboardv2.EnvironmentNameAndId
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.tables.account.DtoProduct
import me.socure.storage.slick.tables.account.mappers.WatchlistPreferenceMapper
import net.spy.memcached.{AddrUtil, MemcachedClient}
import scalacache.ScalaCache

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class AccountAutomationServiceTest extends FunSuite with ScalaFutures with EitherValues with MockitoSugar with Matchers with BeforeAndAfterAll with MemcachedTestSupport{
  implicit val ec: ExecutionContextExecutor = ExecutionContext.Implicits.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(500, Millis))

  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  var service: AccountBundleAssociationService = _
  var accountBundleAssociationService: AccountBundleAssociationService = _
  var productService: ProductService = mock[ProductService]
  var einService: EINService = mock[EINService]
  var saiPreferencesService: SaiPreferencesService = mock[SaiPreferencesService]
  var activeUsersService: ActiveUsersService = mock[ActiveUsersService]
  var decisionServiceClientV2: DecisionServiceClientV2 = mock[DecisionServiceClientV2]
  var accountDataRetentionScheduleService: AccountDataRetentionScheduleService = mock[AccountDataRetentionScheduleService]
  val accountPgpKeysService: AccountPgpKeysService = mock[AccountPgpKeysService]
  val documentManagerClient: DocumentManagerClient = mock[DocumentManagerClient]
  var accountAutomationService: AccountAutomationService = _
  var rateLimitingService:RateLimitingService = _

  override def memcachedPodLabel(): String = "account-automation-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private val cacheKey = DashboardDomainCacheKeyProvider.provide(1)
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"
  private val successResult = AccountDashboardDomain(
    accountId = 1,
    domainWhiteEnabled = true,
    whiteListedDomain = Some("domain.com")
  )
  private val documentTypes = List(DtoDocumentType(1, "publicId1", "passport", Some("Passport"), 1, 1, None),
                                   DtoDocumentType(2, "publicId2", "license", Some("License"), 1, 1, None))
  private val accountPublicId = Some("AccountPublicId")
  val mysqlService: MysqlService = MysqlService("account-automation-service")
  private val dbName = "socure"

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  private val supportEmail = "<EMAIL>"

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  def buildServices(dataSource: DataSource): Unit = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountAudit = new DaoAccountAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoKycPreferences = new DaoKycPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val watchlistPreference = new DaoWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val accountPreferencesService = mock[AccountPreferencesService]
    val daoDvConfiguration = new DaoDvConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val docvOrchestraClient = mock[DocvOrchestraClient]
    val auditDetailsService = mock[AuditDetailsService]
    val dvConfigurationService = new DvConfigurationService(daoDvConfiguration, v2Validator, auditDetailsService)
    val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
    rateLimitingService = new RateLimitingService(daoRateLimit, scalaCache)
    val accountProvisioningConfig = AccountProvisioningConfiguration(
      bundleReference = "Bundle 5",
      documentTypes = Set("passport", "license"),
      decisionConfiguration = DecisionConfiguration(1, "modelName", "modelVersion"),
      dvConfigurations = Some(Set(DvConfiguration(1, "18", 0))),
      kycConfiguration = Some(KycPreferences(true,"yyyy-mm-dd",true)),
      rateLimit = Set(RateLimit(environmentTypeId = EnvironmentTypes.PRODUCTION_ENVIRONMENT.id, limit=0, api = "api-c6LXBEdl5o")),
      authenticIdStrategy = Some("DocV V2 Standard")
    )
    accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val accountAuditService = new AccountAuditService(daoAccountAudit, daoAccount, clock)
    val pgpKeyExpiryDuration = 63072000L
    accountAutomationService = new AccountAutomationService(accountBundleAssociationService,
      productService,
      einService,
      saiPreferencesService,
      accountAuditService,
      accountDataRetentionScheduleService,
      new BundleManagementService(),
      decisionServiceClientV2,
      accountPgpKeysService,
      activeUsersService,
      pgpKeyExpiryDuration,
      documentManagerClient,
      accountProvisioningConfig,
      rateLimitingService,
      docvOrchestraClient,
      dvConfigurationService,
      accountPreferencesService,
      clock,
      scalaCache)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource(Some(dbName))
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, first_activated_at, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '2016-05-05 00:00:00', 'publicId1', 'public_api_key_1','externalId1')," +
      s"(2, 'AccountName2', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId2', 'public_api_key_2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId3', 'public_api_key_3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', true, 1, NULL, false, '2016-05-05 00:00:00', 'publicId4', 'public_api_key_4','externalId4'); ")

    sqlExecutor.execute("INSERT INTO tbl_account_bundle_association(account_id, bundle_reference, created_by, created_at) VALUES "+
      "(1, 'Bundle One', '<EMAIL>', '2022-09-09'), " +
      "(2, 'Bundle Two', '<EMAIL>', '2022-09-09');")
    buildServices(dataSource)
  }

  override protected def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("Get account provisioning details by account id - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val ein = EIN("*********")
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    val product = Seq(AccountProducts(1, "ECBSV", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(saiPreferencesService.fetchSaiPreferences(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(None)))
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(einService.fetchEIN(MMatchers.anyLong())).thenReturn(Future.successful(Right(Some(ein))))
    Mockito.when(einService.fetchLookupKey(MMatchers.anyLong())).thenReturn(Future.successful(Right(Some(LookupApiKey("*********")))))
    Mockito.when(einService.fetchLookupKeyServiceSid(MMatchers.anyLong())).thenReturn(Future.successful(Right(None)))
    Mockito.when(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(1)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    val expected = Right(AccountProvisioningDetails(Some("Bundle One"),
      List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,defaultState = false,provisioned = false,enabled = false,None,None,None,None)),
      ProductConfiguration(Some("*********"), None, None)))
    whenReady(accountAutomationService.getAccountProvisioningDetails(1)) { response =>
      response.fold(_ => fail, _ => expected)
    }
    Mockito.verify(productService, times(1)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(einService, times(1)).fetchEIN(MMatchers.any(classOf[Long]))
  }

  test("Get account provisioning details by account id, No EIN - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val product = Seq(AccountProducts(1, "Watchlist", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    Mockito.when(saiPreferencesService.fetchSaiPreferences(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(None)))
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(einService.fetchEIN(MMatchers.anyLong())).thenReturn(Future.successful(Right(None)))
    Mockito.when(einService.fetchLookupKey(MMatchers.anyLong())).thenReturn(Future.successful(Right(Some(LookupApiKey("*********")))))
    Mockito.when(einService.fetchLookupKeyServiceSid(MMatchers.anyLong())).thenReturn(Future.successful(Right(None)))
    Mockito.when(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(1)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    val expected = Right(AccountProvisioningDetails(Some("Bundle One"),
      List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,defaultState = false,provisioned = false,enabled = false,None,None,None,None)),
      ProductConfiguration(Some("*********"), None, None)))

    whenReady(accountAutomationService.getAccountProvisioningDetails(1)) { response =>
      response.fold(_ => fail, _ => expected)
    }
    Mockito.verify(productService, times(1)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(einService, times(1)).fetchEIN(MMatchers.any(classOf[Long]))
  }

  test("Get account provisioning details by account id, No Bundle Info - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val product = Seq(AccountProducts(1, "Watchlist", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(saiPreferencesService.fetchSaiPreferences(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(None)))
    Mockito.when(einService.fetchEIN(MMatchers.anyLong())).thenReturn(Future.successful(Right(None)))
    Mockito.when(einService.fetchLookupKey(MMatchers.anyLong())).thenReturn(Future.successful(Right(Some(LookupApiKey("*********")))))
    Mockito.when(einService.fetchLookupKeyServiceSid(MMatchers.anyLong())).thenReturn(Future.successful(Right(None)))
    Mockito.when(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(100)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    val expected = Right(AccountProvisioningDetails(None,
      List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,defaultState = false,provisioned = false,enabled = false,None,None,None,None)),
      ProductConfiguration(Some("*********"), None, None)))

    whenReady(accountAutomationService.getAccountProvisioningDetails(100)) { response =>
      response.fold(_ => fail, _ => expected)
    }
    Mockito.verify(productService, times(1)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(einService, times(1)).fetchEIN(MMatchers.any(classOf[Long]))
  }

  test("Get account provisioning details by account id, No Product Info - failure") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val error = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Left(error)))
    Mockito.when(einService.fetchEIN(MMatchers.anyLong())).thenReturn(Future.successful(Left(error)))

    whenReady(accountAutomationService.getAccountProvisioningDetails(100)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
    Mockito.verify(productService, times(1)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(einService, never).fetchEIN(MMatchers.any(classOf[Long]))
  }

  test("Get account provisioning details by account id, No Product Info, case 2 - failure") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val error = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Left(error)))
    Mockito.when(einService.fetchEIN(MMatchers.anyLong())).thenReturn(Future.successful(Left(error)))

    whenReady(accountAutomationService.getAccountProvisioningDetails(1)) { response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccountNotFound), _ => fail)
    }
    Mockito.verify(productService, times(1)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(einService, never).fetchEIN(MMatchers.any(classOf[Long]))
  }

  test("Update account provisioning details by account id - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = EINResponse(1,"*********")
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "ECBSV", 94, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Right(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, times(1)).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)

  }

  test("Update account provisioning details by account id - update bundle passed and update product failed") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = EINResponse(1,"*********")
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "ECBSV", 94, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(ein = Some("*********"), Some(RetentionSchedule(1, "Days")), None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(InvalidProduct))))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Right(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountProvisioningNotUpdated), _ => fail)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, times(1)).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id, bundle reference is with empty or null- update bundle passed and update product failed") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = EINResponse(1,"*********")
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "ECBSV", 94, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(InvalidProduct))))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Right(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountProvisioningNotUpdated), _ => fail)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, times(1)).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id - update bundle passed and update product config failed") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "ECBSV", 94, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(InvalidProduct))))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountProvisioningNotUpdated), _ => fail)
    }
    Mockito.verify(productService, never).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, times(1)).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id - failed") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(InvalidProduct))))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(100)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(100)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(100, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccountProvisioningNotUpdated), _ => fail)
    }
    Mockito.verify(productService, never).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(einService, never).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, never).getPublicAccountId(100)
    Mockito.verify(activeUsersService, never).getEnvironments(100)
  }

  test("Update account provisioning with product configuration change only and PII retention schedule turned on already") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val dataRetentionRequest = UpdateAccountDataRetentionSchedule(1, "days", supportEmail)
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    val products = Seq.empty[UpdateProduct]
    val existingProducts = Seq(AccountProducts(1, "PII Retention Schedule", 187, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = true, enabled = true, None, None, None, None))
    val productConfig = ProductConfiguration(None, retentionSchedule = Some(RetentionSchedule(1, "days")))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(1, dataRetentionRequest)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = supportEmail)
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, never).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, never).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(accountDataRetentionScheduleService, times(1)).upsertAccountDataRetentionSchedule(1, dataRetentionRequest)
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
  }

  test("Update account provisioning with product configuration change only fails when PII retention schedule turned off ") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val dataRetentionRequest = UpdateAccountDataRetentionSchedule(1, "days", supportEmail)
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    val products = Seq.empty[UpdateProduct]
    val existingProducts = Seq(AccountProducts(1, "PII Retention Schedule", 187, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = true, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(None, retentionSchedule = Some(RetentionSchedule(1, "days")))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(1, dataRetentionRequest)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = supportEmail)
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, never).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, never).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(accountDataRetentionScheduleService, never).upsertAccountDataRetentionSchedule(1, dataRetentionRequest)
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
  }

  test("Update account provisioning with PII retention schedule turned off doesn't update the schedule") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val dataRetentionRequest = UpdateAccountDataRetentionSchedule(1, "days", supportEmail)
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    val products = Seq(UpdateProduct(1, provisioned = false, enabled = false))
    val existingProducts = Seq(AccountProducts(1, "PII Retention Schedule", 187, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = true, enabled = true, None, None, None, None))
    val productConfig = ProductConfiguration(None, retentionSchedule = Some(RetentionSchedule(1, "days")))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(1, dataRetentionRequest)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = supportEmail)
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, never).upsertEIN(MMatchers.any(classOf[EINRequest]))
    //Mockito.verify(accountDataRetentionScheduleService, never).upsertAccountDataRetentionSchedule(1, dataRetentionRequest)
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning with no product configuration change") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val einResponse = ErrorResponseFactory.get(AccountNotFound)
    val dataRetentionRequest = UpdateAccountDataRetentionSchedule(1, "days", supportEmail)
    val dataRetentionResponse = DtoAccountDataRetentionSchedule(1, 1, 1, "days", supportEmail, clock.now())
    val products = Seq(UpdateProduct(1, provisioned = false, enabled = false))
    val existingProducts = Seq(AccountProducts(1, "PII Retention Schedule", 187, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = true, enabled = true, None, None, None, None))
    val productConfig = ProductConfiguration(None, None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Left(einResponse)))
    Mockito.when(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(1, dataRetentionRequest)).thenReturn(Future.successful(Right(dataRetentionResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = supportEmail)
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(einService, never).upsertEIN(MMatchers.any(classOf[EINRequest]))
    Mockito.verify(accountDataRetentionScheduleService, never).upsertAccountDataRetentionSchedule(1, dataRetentionRequest)
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, never).getPublicAccountId(1)
    Mockito.verify(activeUsersService, never).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id with decision service enabled - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, decisionServiceClientV2)

    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "Use Decision Service", 103, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(decisionServiceClientV2.listLogicGroupedByPublicId(ListLogicRequest(DecisionLogicFilterRequest("1", "1", Some(Set(DecisionLogicStates.ACTIVE.id)), None),
      DecisionLogicSortRequest(None)))).thenReturn(Future.successful(Right(LogicDetailsWithMappingsResponse(List.empty, 0, None))))
    Mockito.when(decisionServiceClientV2.listLogicGroupedByPublicId(ListLogicRequest(DecisionLogicFilterRequest("1", "2", Some(Set(DecisionLogicStates.ACTIVE.id)), None),
      DecisionLogicSortRequest(None)))).thenReturn(Future.successful(Right(LogicDetailsWithMappingsResponse(List.empty, 0, None))))
    Mockito.when(decisionServiceClientV2.listLogicGroupedByPublicId(ListLogicRequest(DecisionLogicFilterRequest("1", "3", Some(Set(DecisionLogicStates.ACTIVE.id)), None),
      DecisionLogicSortRequest(None)))).thenReturn(Future.successful(Right(LogicDetailsWithMappingsResponse(List.empty, 1, None))))
    Mockito.when(decisionServiceClientV2.cloneRecommendedLogic(MMatchers.any(classOf[BestLogicCloneRequest]))).thenReturn(Future.successful(Right(RecommendedLogicCloneResponse(1))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = ProductConfiguration(None, None, None),
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(decisionServiceClientV2, times(3)).listLogicGroupedByPublicId(MMatchers.any(classOf[ListLogicRequest]), MMatchers.any(classOf[Boolean]))
    Mockito.verify(decisionServiceClientV2, times(2)).cloneRecommendedLogic(MMatchers.any(classOf[BestLogicCloneRequest]))
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id with decision service already enabled - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, decisionServiceClientV2, documentManagerClient, activeUsersService)

    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "Use Decision Service", 103, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = true, enabled = true, None, None, None, None))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = ProductConfiguration(None, None, None),
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(decisionServiceClientV2, never()).cloneRecommendedLogic(MMatchers.any(classOf[BestLogicCloneRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, never).getPublicAccountId(1)
    Mockito.verify(activeUsersService, never).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id with customer upload enabled - success") {
    Mockito.reset(productService, accountPgpKeysService)

    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "Allow Customer File Upload", 88, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(accountPgpKeysService.createPgpKeys(1, Some(63072000L))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = ProductConfiguration(None, None, None),
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(accountPgpKeysService, times(1)).createPgpKeys(1, Some(63072000L))
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  //ignore this case, need to fix data. Will fix it
  ignore("Update account provisioning details by account id with document verification enabled - success") {
    Mockito.reset(productService, accountPgpKeysService, documentManagerClient, activeUsersService)

    val products = Seq(UpdateProduct(6, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(6, "Document Verification", 35, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(accountPgpKeysService.createPgpKeys(1, Some(63072000L))).thenReturn(Future.successful(Right(true)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(documentManagerClient.provisionDocumentType(MMatchers.any(classOf[DtoProvisionAccount]))).thenReturn(Future.successful(Right(DtoDocumentTypeAccount(1L, "DocumentPublicId", "AccountPublicId"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = ProductConfiguration(None, None, None),
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      println(response)
      response.fold(_ => fail, _ => true)
    }
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(accountPgpKeysService, never).createPgpKeys(1, Some(63072000L))
    Mockito.verify(documentManagerClient, times(2)).provisionDocumentType(MMatchers.any(classOf[DtoProvisionAccount]))
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Update account provisioning details by account id with document verification enabled - fail") {
    Mockito.reset(productService, accountPgpKeysService, documentManagerClient, activeUsersService)

    val products = Seq(UpdateProduct(6, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(6, "Document Verification", 35, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(accountPgpKeysService.createPgpKeys(1, Some(63072000L))).thenReturn(Future.successful(Right(true)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(documentManagerClient.provisionDocumentType(MMatchers.any(classOf[DtoProvisionAccount]))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(UnknownError))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = ProductConfiguration(None, None, None),
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => ErrorResponseFactory.get(ProductConfigNotUpdated), _ => fail)
    }
    Mockito.verify(productService, never).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(productService, times(1)).getProductsForAccount(1)
    Mockito.verify(accountPgpKeysService, never).createPgpKeys(1, Some(63072000L))
    Mockito.verify(documentManagerClient, times(2)).provisionDocumentType(MMatchers.any(classOf[DtoProvisionAccount]))
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

  test("Account Automation - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService)
    val product = Seq(AccountProducts(4, "Watchlist", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    Mockito.when(activeUsersService.addDefaultDomains(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right()))
    Mockito.when(activeUsersService.getEnvironments(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(activeUsersService.getPublicAccountId(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(accountPublicId))
    whenReady(accountAutomationService.autoProvision(4, false, 1)) { response =>
      response.fold(_ => fail, r => {
        r shouldBe true
        whenReady(rateLimitingService.getRateLimits(4)) { response0 =>
          response0.fold(_ => fail, r0 => {
            r0.filter(f => f.environmentTypeId==1 && f.api.equals(RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId)).map { rl =>
              rl.limit shouldBe 0L
              rl.windowInMillis shouldBe 1000
            }
          })
        }
      })
    }
    Mockito.verify(productService, times(2)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
//    Mockito.verify(activeUsersService, times(1)).addDefaultDomains(MMatchers.any(classOf[Long]))
  }

  test("Account Automation - success - internal account") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, activeUsersService)
    val product = Seq(AccountProducts(1, "Watchlist", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(activeUsersService.addDefaultDomains(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right()))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    whenReady(accountAutomationService.autoProvision(4, isInternal = true)) { response =>
      response.fold(_ => fail, r => {
        r shouldBe true
        whenReady(rateLimitingService.getRateLimits(4)) { response0 =>
          response0.fold(_ => fail, r0 => {
            r0.filter(f => f.environmentTypeId==1 && f.api.equals(RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId)).map { rl =>
              rl.limit shouldBe 0L
              rl.windowInMillis shouldBe 1000
            }
          })
        }
      })
    }
    Mockito.verify(productService, times(2)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(activeUsersService, times(1)).addDefaultDomains(MMatchers.any(classOf[Long]))
  }

  test("Account Automation - fail") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    val product = Seq(AccountProducts(1, "Watchlist", 65, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    Mockito.when(productService.getProductsForAccount(MMatchers.any(classOf[Long]))).thenReturn(Future.successful(Right(product)))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(false)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    whenReady(accountAutomationService.autoProvision(4)) { response =>
      response.fold(_ => fail, _ shouldBe false)
    }
    Mockito.verify(productService, times(2)).getProductsForAccount(MMatchers.any(classOf[Long]))
    Mockito.verify(productService, times(1)).updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, never).getPublicAccountId(1)
    Mockito.verify(activeUsersService, never).getEnvironments(1)
  }

  test("Update account provisioning details and invalidate cache for whitelisting provision by account id - success") {
    Mockito.reset(productService, einService, accountDataRetentionScheduleService, documentManagerClient, activeUsersService)
    scalaCache.cache.put[AccountDashboardDomain](cacheKey, successResult, None)
    whenReady(scalaCache.cache.get[AccountDashboardDomain](cacheKey))(_ shouldBe Some(successResult))
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))

    val einResponse = EINResponse(1, "*********")
    val products = Seq(UpdateProduct(BusinessUserRoles.WHITELIST_DASHBOARD.id, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val existingProducts = Seq(AccountProducts(1, "ECBSV", 94, ProductProvisioningTypes.CASCADE, None, 1, defaultState = false, provisioned = false, enabled = false, None, None, None, None))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    Mockito.when(productService.updateProductsForAccount(MMatchers.any(classOf[UpdateProductsRequest]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(productService.getProductsForAccount(1)).thenReturn(Future.successful(Right(existingProducts)))
    Mockito.when(einService.upsertEIN(MMatchers.any(classOf[EINRequest]))).thenReturn(Future.successful(Right(einResponse)))
    Mockito.when(documentManagerClient.listDocTypes()).thenReturn(Future.successful(Right(documentTypes)))
    Mockito.when(activeUsersService.getPublicAccountId(1)).thenReturn(Future.successful(accountPublicId))
    Mockito.when(activeUsersService.getEnvironments(1)).thenReturn(Future.successful(Seq(EnvironmentNameAndId(1, "Production"))))
    Mockito.when(productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)).thenReturn(Future.successful(Seq.empty[DtoProduct]))
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    whenReady(accountAutomationService.saveAccountAutomation(1, updateAccountProvisioningDetails)) { response =>
      response.fold(_ => fail, _ => true)
    }
    whenReady(scalaCache.cache.get[AccountDashboardDomain](cacheKey))(_ shouldBe None)
    Mockito.verify(documentManagerClient, never).listDocTypes()
    Mockito.verify(activeUsersService, times(1)).getPublicAccountId(1)
    Mockito.verify(activeUsersService, times(1)).getEnvironments(1)
    Mockito.verify(productService, times(1)).getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id)
  }

}
