package me.socure.account.saml

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.constants.AccountTypes
import me.socure.model.BusinessUserRoles
import me.socure.model.account.AccountIdName
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}
import org.joda.time.DateTime
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Milliseconds, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}
import slick.driver.MySQLDriver

import scala.concurrent.{ExecutionContext, Future}

class SamlValidatorTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  private implicit val exe: ExecutionContext = ExecutionContext.Implicits.global
  private implicit val patienceConf: PatienceConfig = PatienceConfig(timeout = Span(2, Seconds), interval = Span(50, Milliseconds))

  class MockableAccountInfoService extends AccountInfoService(null, null)

  class MockableDaoBusinessUser extends DaoBusinessUser(null, MySQLDriver)

  class MockableDaoAccountV2 extends DaoAccountV2(null, MySQLDriver)

  private val accountInfoService = mock[MockableAccountInfoService]
  private val daoBusinessUser = mock[MockableDaoBusinessUser]
  private val daoAccountV2 = mock[MockableDaoAccountV2]
  private val v2Validator = mock[V2Validator]

  private val validator = new SamlValidator(accountInfoService = accountInfoService, daoBusinessUser = daoBusinessUser, daoAccountV2 = daoAccountV2, v2Validator = v2Validator)

  private val accountId = 100L
  private val accountIds = Set(accountId, accountId + 1)
  private val email = "<EMAIL>"
  private val internalEmail = "<EMAIL>"
  private val emails = Set(email, "<EMAIL>")
  private val notSupportedError = Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts))
  private val accNotFoundError = Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
  private val success = Right(())
  private val actionResult = Right(1)
  private val actionResultFuture = Future.successful(actionResult)
  val user1 = DtoBusinessUser(100, email, "gopal", "haris", None,
    "**********", None, None, DateTime.now(), false, None, None, accountId, true)
  val user2 = DtoBusinessUser(101, email, "gopal", "haris", None,
    "**********", None, None, DateTime.now(), false, None, None, accountId + 1, true)
  val internalUser = DtoBusinessUser(102, internalEmail, "interanl", "socure", None,
    "**********", None, None, DateTime.now(), false, None, None, accountId + 2, true)

  "SamlValidator" - {
    "for given account id" - {
      "should return not supported error when SAML 2.0 enabled" in {
        mockSamlTrue()
        whenReady(validator.accountIdHasNoSaml(accountId = accountId))(_ shouldBe notSupportedError)
        whenReady(validator.whenAccountIdHasNoSaml(accountId = accountId)(actionResultFuture))(_ shouldBe notSupportedError)
      }

      "should return success when account found and SAML 2.0 not enabled" in {
        mockSamlFalse()
        whenReady(validator.accountIdHasNoSaml(accountId = accountId))(_ shouldBe success)
        whenReady(validator.whenAccountIdHasNoSaml(accountId = accountId)(actionResultFuture))(_ shouldBe actionResult)
      }
    }

    "for given account ids" - {
      "should return not supported error when SAML 2.0 enabled" in {
        mockSamlTrueM()
        whenReady(validator.accountIdsHaveNoSaml(accountIds = accountIds))(_ shouldBe notSupportedError)
        whenReady(validator.whenAccountIdsHaveNoSaml(accountIds = accountIds)(actionResultFuture))(_ shouldBe notSupportedError)
      }

      "should return success when account found and SAML 2.0 not enabled" in {
        mockSamlFalseM()
        whenReady(validator.accountIdsHaveNoSaml(accountIds = accountIds))(_ shouldBe success)
        whenReady(validator.whenAccountIdsHaveNoSaml(accountIds = accountIds)(actionResultFuture))(_ shouldBe actionResult)
      }
    }

    "for given email" - {
      "when given email found V1 account" - {
        "should return not supported error when SAML 2.0 enabled" in {
          mockgetUser()
          mockgetAccountV2ProvisionedFalse(user1)
          mockSamlTrue()
          whenReady(validator.emailHasNoSaml(email = email))(_ shouldBe notSupportedError)
          whenReady(validator.whenUserHasNoSaml(email = email)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return success when account found and SAML 2.0 not enabled" in {
          mockgetUser()
          mockgetAccountV2ProvisionedFalse(user1)
          mockSamlFalse()
          whenReady(validator.emailHasNoSaml(email = email))(_ shouldBe success)
          whenReady(validator.whenUserHasNoSaml(email = email)(actionResultFuture))(_ shouldBe actionResult)
        }
      }

      "when given email found V2 account" - {
        "should return not supported error when all associated accounts are SAML 2.0 enabled" in {
          mockgetUser()
          mockgetAccountV2ProvisionedTrue(user1)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId)
          mockGetRootParentAccountTypes(Seq(user1.accountId))
          mockSamlTrue()
          whenReady(validator.emailHasNoSaml(email = email))(_ shouldBe notSupportedError)
          whenReady(validator.whenUserHasNoSaml(email = email)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return success when account found and atleast one non SAML account is associated" in {
          mockgetUser()
          mockgetAccountV2ProvisionedTrue(user1)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId)
          mockGetRootParentAccountTypes(Seq(user1.accountId))
          mockSamlFalse()
          whenReady(validator.emailHasNoSaml(email = email))(_ shouldBe success)
          whenReady(validator.whenUserHasNoSaml(email = email)(actionResultFuture))(_ shouldBe actionResult)
        }

        "should bypass saml validation for internal user for risk os account found" in {
          mockGetRiskosUser()
          mockgetAccountV2ProvisionedTrue(internalUser)
          mockgetAllAssociatedAccounts(internalUser.id, internalUser.accountId)
          mockGetRiskOsRootParentAccountTypes(Seq(internalUser.accountId))
          whenReady(validator.emailHasNoSaml(email = internalEmail))(_ shouldBe success)
          whenReady(validator.whenUserHasNoSaml(email = internalEmail)(actionResultFuture))(_ shouldBe actionResult)
        }
      }

      "when given email not found should return user not found error" in {
        (daoBusinessUser.getUser(_: List[String])).expects(List(email)).returns(Future.successful(Seq.empty))
        whenReady(validator.emailHasNoSaml(email = email))(_ shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
      }
    }

    "for given emails" - {
      "when given emails found V1 account" - {
        "should return not supported error when both accounts are SAML 2.0 enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedFalse(user1)
          mockgetAccountV2ProvisionedFalse(user2)
          mockSamlTrue(user1.accountId)
          mockSamlTrue(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe notSupportedError)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return not supported error when atleast one account is SAML 2.0 enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedFalse(user1)
          mockgetAccountV2ProvisionedFalse(user2)
          mockSamlFalse(user1.accountId)
          mockSamlTrue(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe notSupportedError)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return success when account found and SAML 2.0 not enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedFalse(user1)
          mockgetAccountV2ProvisionedFalse(user2)
          mockSamlFalse(user1.accountId)
          mockSamlFalse(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe success)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe actionResult)
        }
      }

      "when given emails found V2 account" - {
        "should return not supported error when both accounts are SAML 2.0 enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedTrue(user1)
          mockgetAccountV2ProvisionedTrue(user2)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId)
          mockgetAllAssociatedAccounts(user2.id, user2.accountId)
          mockGetRootParentAccountTypes(Seq(user1.accountId))
          mockGetRootParentAccountTypes(Seq(user2.accountId))
          mockSamlTrue(user1.accountId)
          mockSamlTrue(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe notSupportedError)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return not supported error when atleast one account is SAML 2.0 enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedTrue(user1)
          mockgetAccountV2ProvisionedTrue(user2)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId)
          mockgetAllAssociatedAccounts(user2.id, user2.accountId)
          mockGetRootParentAccountTypes(Seq(user1.accountId))
          mockGetRootParentAccountTypes(Seq(user2.accountId))
          mockSamlFalse(user1.accountId)
          mockSamlTrue(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe notSupportedError)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe notSupportedError)
        }

        "should return success when account found and SAML 2.0 not enabled" in {
          mockgetUserM()
          mockgetAccountV2ProvisionedTrue(user1)
          mockgetAccountV2ProvisionedTrue(user2)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId)
          mockgetAllAssociatedAccounts(user2.id, user2.accountId)
          mockGetRootParentAccountTypes(Seq(user1.accountId))
          mockGetRootParentAccountTypes(Seq(user2.accountId))
          mockSamlFalse(user1.accountId)
          mockSamlFalse(user2.accountId)
          whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe success)
          whenReady(validator.whenUsersHaveNoSaml(emails = emails)(actionResultFuture))(_ shouldBe actionResult)
        }
      }

      "when given emails not found should return user not found error" in {
        (daoBusinessUser.getUser(_: List[String])).expects(emails.toList).returns(Future.successful(Seq.empty))
        whenReady(validator.usersHaveNoSaml(emails = emails))(_ shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
      }
    }

    "for given user dto object" - {
      "when given user found V1 account" - {
        "should return true if SAML 2.0 enabled" in {
          mockgetAccountV2ProvisionedFalse(user1, 1)
          mockSamlTrue(count = 1)
          whenReady(validator.userHaveAllSaml(user1))(_ shouldBe true)
        }

        "should return false when SAML 2.0 not enabled" in {
          mockgetAccountV2ProvisionedFalse(user1, 1)
          mockSamlFalse(count = 1)
          whenReady(validator.userHaveAllSaml(user1))(_ shouldBe false)
        }
      }

      "when given user found V2 account" - {
        "should return true if SAML 2.0 enabled" in {
          mockgetAccountV2ProvisionedTrue(user1, 1)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId, 1)
          mockSamlTrue(count = 1)
          whenReady(validator.userHaveAllSaml(user1))(_ shouldBe true)
        }

        "should return false when SAML 2.0 not enabled" in {
          mockgetAccountV2ProvisionedTrue(user1, 1)
          mockgetAllAssociatedAccounts(user1.id, user1.accountId, 1)
          mockSamlFalse(count = 1)
          whenReady(validator.userHaveAllSaml(user1))(_ shouldBe false)
        }
      }
    }
  }

  private def mockAccInfo(res: Boolean, account: Long = accountId, count: Integer = 2): Unit = {
    (accountInfoService.anyHasRole(_: Set[Long], _: Int))
      .expects(Set(account), BusinessUserRoles.SAML_2_0.id)
      .returns(Future.successful(res))
      .repeated(count)
  }

  private def mockSamlTrue(account: Long = accountId, count: Integer = 2): Unit = mockAccInfo(true, account, count)

  private def mockSamlFalse(account: Long = accountId, count: Integer = 2): Unit = mockAccInfo(false, account, count)

  private def mockAccId(): Unit = {
    (daoBusinessUser.getAccountIdsByEmails _)
      .expects(Set(email))
      .returns(Future.successful(Set(accountId)))
      .repeated(2)
  }

  private def mockAccInfoM(res: Boolean): Unit = {
    (accountInfoService.anyHasRole _)
      .expects(accountIds, BusinessUserRoles.SAML_2_0.id)
      .returns(Future.successful(res))
      .repeated(2)
  }

  private def mockSamlTrueM(): Unit = mockAccInfoM(true)

  private def mockSamlFalseM(): Unit = mockAccInfoM(false)

  private def mockAccIdM(): Unit = {
    (daoBusinessUser.getAccountIdsByEmails _)
      .expects(emails)
      .returns(Future.successful(accountIds))
      .repeated(2)
  }

  private def mockgetUser(): Unit = {
    (daoBusinessUser.getUser(_: List[String]))
      .expects(List(email))
      .returns(Future.successful(Seq(user1)))
      .repeated(2)
  }

  private def mockGetRiskosUser(): Unit = {
    (daoBusinessUser.getUser(_: List[String]))
      .expects(List(internalEmail))
      .returns(Future.successful(Seq(internalUser)))
      .repeated(2)
  }

  private def mockgetUserM(): Unit = {
    (daoBusinessUser.getUser(_: List[String]))
      .expects(emails.toList)
      .returns(Future.successful(Seq(user1, user2)))
      .repeated(2)
  }

  private def mockgetAccountV2Provisioned(user: DtoBusinessUser, res: Boolean, count: Integer = 2): Unit = {
    (daoAccountV2.isAccountV2Provisioned _)
      .expects(Set(user.accountId))
      .returns(Future.successful(res))
      .repeated(count)
  }

  private def mockgetAllAssociatedAccounts(userId: Long, account: Long, count: Integer = 2): Unit = {
    (daoAccountV2.getAllAssociatedAccounts(_: Long))
      .expects(userId)
      .returns(Future.successful(Seq(AccountIdName(account, "AccountName"))))
      .repeated(count)
  }

  private def mockGetRootParentAccountTypes(accountIds: Seq[Long], count: Integer = 2): Unit = {
    (v2Validator.getRootParentAccountTypes(_: Seq[Long]))
      .expects(accountIds)
      .returns(Future.successful(accountIds.map(id => id -> AccountTypes.DIRECT_CUSTOMER.id).toMap))
      .repeated(count)
  }

  private def mockGetRiskOsRootParentAccountTypes(accountIds: Seq[Long], count: Integer = 2): Unit = {
    (v2Validator.getRootParentAccountTypes(_: Seq[Long]))
      .expects(accountIds)
      .returns(Future.successful(accountIds.map(id => id -> AccountTypes.DIRECT_EFFECTIV.id).toMap))
      .repeated(count)
  }

  private def mockgetAccountV2ProvisionedTrue(user: DtoBusinessUser, count: Integer = 2): Unit = mockgetAccountV2Provisioned(user, true, count)
  private def mockgetAccountV2ProvisionedFalse(user: DtoBusinessUser, count: Integer = 2): Unit = mockgetAccountV2Provisioned(user, false, count)
}
