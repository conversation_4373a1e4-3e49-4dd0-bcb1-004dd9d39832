package dashboardv2

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService}
import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.saml.SamlValidator
import me.socure.account.service.{AuditDetailsService, _}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.BadLoginConfig
import me.socure.constants.{AccountManagementDefaults, CaseManagementPermissions, DashboardUserPermissions}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account.{AccountIdName, UserAccountAssociationStatuses, UserRole}
import me.socure.model.dashboardv2._
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{DashboardUserRole, UpdateQuicksightUserStatus, UserCredential}
import me.socure.salt.client.SaltClient
import me.socure.salt.model.{Salt, SaltValueGenerator}
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import java.util.concurrent.TimeUnit
import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sunderraj on 8/18/16.
 */
class DashboardUserServiceV2Test extends FunSuite with Matchers with EitherValues with ScalaFutures with BeforeAndAfterAll with MockitoSugar {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(20, Seconds), interval = Span(500, Millis))

  val mysqlService: MysqlService = MysqlService("dashboard-user-service-vtwo")
  private val dbName = "socure"

  var service : DashboardUserServiceV2 = _
  var businessUserService : BusinessUserService = _
  var userAccountAssociationService: UserAccountAssociationService = _
  var permissionTemplateService: PermissionTemplateService = _
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  val saltClient: SaltClient = mock[SaltClient]
  val aSalt: Salt = SaltValueGenerator.aSalt()
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))
  val mailNotificationService: MailNotificationService = mock[MailNotificationService]

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database : Option[String] = None) = {
    val endpoint = mysqlService.endpoint()
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource : DataSource): Int = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  override protected def beforeAll(): Unit ={
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val sqlExecutor = new SQLExecutor(dataSource)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1', 'external_id1'), " +
      s"(2, 'sub-account', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey2', 'external_id2'), " +
      s"(3, 'AccountName2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey3', 'external_id3'), " +
      s"(4, 'sub-account1', '101-205', false, 1, 2, false, '${PublicIdGenerator.account().value}','publicApiKey4', 'external_id4'), " +
      s"(5, 'Account3', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey5', 'external_id5'), " +
      s"(6, 'sub-account2', '101-205', true, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey6', 'external_id6'), " +
      s"(7, 'sub-account3', '101-205', true, 1, 5, false, '${PublicIdGenerator.account().value}','publicApiKey7', 'external_id7'), " +
      s"(8, 'Account8', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey8', 'external_id8'), " +
      s"(9, 'Account9', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey9', 'external_id9'), " +
      s"(10, 'Account10', '101-205', true, 1, 8, false, '${PublicIdGenerator.account().value}','publicApiKey10', 'external_id10'), " +
      s"(11, 'Account11', '101-205', true, 1, 8, false, '${PublicIdGenerator.account().value}','publicApiKey11', 'external_id11')," +
      "(12, 'Account12', '101-205', true, 1, 8, false, 'public-id','publicApiKey12', 'external_id12')," +
      s"(16, 'inactive-account2', '101-205', true, 0, 5, false, '${PublicIdGenerator.account().value}','publicApiKey13', 'external_id16'), " +
      s"(17, 'deleted-account2', '101-205', true, 0, 5, true, '${PublicIdGenerator.account().value}','publicApiKey14', 'external_id17') "
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user, is_registered_with_quicksight) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true, false), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false, false), " +
      "(3, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 1, false, false)," +
      "(4, 'c7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 2, false, false), " +
      "(5, 'c7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal5', 'haris5',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, true, true), " +
      "(6, 'c7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal6', 'haris6',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, false, false), " +
      "(7, 'c7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal7', 'haris7',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, false, false), " +
      "(8, 'd7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal8', 'haris8',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 4, false, false), " +
      "(9, 'e7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest9', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 5, false, false), " +
      "(10, 'f7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest10', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 6, false, false), " +
      "(11, 'g7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest11', 'user',NULL, '2014-02-06', '2015-10-20', 0, NULL, NULL, 6, false, false), " +
      "(12, 'h7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest12', 'user',NULL, '2014-02-06', '2015-10-20', 0, NULL, NULL, 7, false, false), " +
      "(13, 'h7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest13', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 8, false, false), " +
      "(14, 'h7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest14', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 12, false, false), " +
      "(15, 'h7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest15', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 10, false, false), " +
      "(16, 'h7741e94-2323-414d-95be-56a015157917', '**********', '<EMAIL>', 'guest16', 'user',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 10, false, false)"
    )

    sqlExecutor.execute("INSERT INTO tbl_password (id, business_user_id, password, status, created_at, updated_at, hash_algorithm) VALUES(1, 1, '8d7455495e0d09297c8ac626bc1d355a', 'active', current_timestamp, current_timestamp, 'v1')")
    sqlExecutor.execute("INSERT INTO tbl_password (id, business_user_id, password, status, created_at, updated_at, hash_algorithm) VALUES(2, 6, '8d7455495e0d09297c8ac626bc1d355a', 'active', current_timestamp, current_timestamp, 'v1'), (3, 14, '8d7455495e0d09297c8ac626bc1d355a', 'active', current_timestamp, current_timestamp, 'v1')")
    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 1, 2), " +
      "(3, 'accesstoken', 'secretkey', 'accesstokensecret', 'domain.com,192.1.3.45,prod.com', 3, 1), " +
      "(4, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 3, 2), " +
      "(5, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 2, 1), " +
      "(6, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 2, 2), " +
      "(7, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 4, 1), " +
      "(8, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 4, 2), " +
      "(9, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 5, 1), " +
      "(10, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 5, 2), " +
      "(11, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 6, 1), " +
      "(12, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 6, 2), " +
      "(13, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 7, 1), " +
      "(14, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 7, 2), " +
      "(15, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 5, 3), " +
      "(16, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 8, 1), " +
      "(17, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 8, 2), " +
      "(18, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 8, 3), " +
      "(19, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 9, 1), " +
      "(20, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 10, 1), " +
      "(21, 'accesstokensand', 'secretkeysand', 'accesstokensecretsand', '192.1.3.45,prod.com', 11, 1)")

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(3, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(4, 9, 'api-key9', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(5, 10, 'api-key10', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(6, 11, 'api-key11', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(7, 12, 'api-key12', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(8, 13, 'api-key13', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(9, 14, 'api-key14', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(10, 15, 'api-key15', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(11, 16, 'api-key16', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(12, 17, 'api-key17', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(13, 18, 'api-key18', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(14, 19, 'api-key19', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(15, 20, 'api-key20', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(16, 21, 'api-key21', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(17, 2, 'api-key2', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(18, 4, 'api-key4', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES" +
      "(NULL, 1, 1), " +
      "(1, 1, 6), " +
      "(1, 1, 5), " +
      "(NULL, 5, 1), " +
      "(5, 4, 4), " +
      "(5, 4, 5), " +
      "(5, 4, 6), " +
      "(6, 4, 7), " +
      "(6, 4, 4), " +
      "(6, 4, 13), " +
      "(6, 4, 14), " +
      "(4, 6, 5), " +
      "(7, 8, 7), " +
      "(4, 7, 5)")

    //BadLogin Count
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_count (business_user_id, count, first_bad_login_try, last_bad_login_try, lock_time) VALUES" +
      "(6, 4, '2015-10-20', '2015-10-20', '2015-10-20')")

    //BadLogin Track
    sqlExecutor.execute("INSERT INTO tbl_business_user_bad_login_try(id, business_user_id, error_description, login_time) VALUES" +
      "(1, 6, 'login_failed', '2015-10-20')")

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(6, 97),(5, 97), (2, 96), (8, 97), (8, 95), (9, 97), (10, 97), (11, 97), (12, 97)")

    // User Role
    sqlExecutor.execute("INSERT INTO `user_role`(id, name, description, by_business_user_id, by_account_id) VALUES " +
      "(1, 'Role-9','Primary Administrator',9,5), " +
      "(2, 'Role-10','Primary Administrator',10,6), " +
      "(3, 'Role-11','Primary Administrator',11,7), " +
      "(4, 'Role-14','Primary Administrator',13,8);")

    // User Account Association
    sqlExecutor.execute("INSERT INTO user_account_association(account_id, business_user_id, is_primary_user, status) VALUES" +
      "(5, 9, 1, 1), " +
      "(6, 10, 1, 1), " +
      "(6, 11, 0, 1), " +
      "(8, 13, 0, 1), " +
      "(1, 1, 1, 1), " +
      "(10, 15, 0, 1)," +
      "(8, 14, 0, 1)," +
      "(12, 14, 1, 1)," +
      "(12, 13, 0, 1)," +
      "(16, 10, 0, 1)," +
      "(17, 10, 0, 1)"
    )
    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,1,0),(3,1,0),(5,1,0),(5,NULL,5),(4,4,0),(2,2,0),(6,NULL,1),(6,4,0), (7, NULL, 2), (8, NULL, 1), (9, NULL, 2), (10, NULL, 2);")

    // Permission Template
    sqlExecutor.execute("INSERT INTO permission_template(name,type,account_id,updated_by) VALUES" +
      "('template1',1,5,1)," +
      "('template2',2,6,1)," +
      "('template3',1,1,1)," +
      "('template4',1,8,4);")
    sqlExecutor.execute("INSERT INTO permission_template_mapping(permission_template_id, environment_type_id, permissions) VALUES" +
      "(3, 0, '1001,1002,1003,1004,1005,1006,1007,1008,1009'), " +
      "(4, 0, '1031,1005,1007,1001,2002,2011,1032,1006,1028,1002,1035,1029,1034,1003,2012,1008,2009,1030,2008,1033,1004,2003,2004,2005,2006,2007,2010'), " +
      "(2, 0, '1031,1005,1007,1001,2002,2011,1032,1006,1028,1002,1035,1029,1034,1003,2012,1008,2009,1030,2008,1033,1004,2003,2004,2005,2006,2007,2010'), " +
      "(1, 0, '1031,1005,1001,2002,2011,1032,1006,1028,1002,1035,1007,1029,1034,1003,2012,1008,2009,1030,2008,1033,1004,2003,2004,2005,2006,2007,2010,2001'), "+
      "(1, 1, '1010,1048')," +
      "(1, 2, '1010,1048')," +
      "(1, 3, '1010');"
    )

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer, no_of_primary_admins) VALUES " +
      "(5, '5/',1,1,1,2), " +
      "(1, '1/',1,1,1,2), " +
      "(6, '5/6/',4,1,1,1), " +
      "(8, '8/',2,1,1,2), " +
      "(9, '9/',2,1,1,1), " +
      "(10, '8/10/',4,1,1,1), " +
      "(11, '8/11/',4,1,0,1), " +
      "(12, '8/12/',4,1,0,1), " +
      "(7, '5/7',4,1,1,1);")

    // Role Permission Template Association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(permission_template_id, user_role_id) values(1,1),(2,2),(3,3),(4,4)")

      val db = JdbcBackend.Database.forDataSource(dataSource)
      val dbSlave = DB.slave(
        databaseConfiguration = DatabaseConfiguration.slave(
          profile = slick.driver.MySQLDriver,
          dataSource = dataSource
        )
      )

      val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
      val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val v2Validator = new V2Validator(daoAccountV2, daoAccount)
      val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)

      val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
      val config = BadLoginConfig(2, isAutoReset = true, FiniteDuration(1, TimeUnit.MINUTES))
      val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val encryptionKeysService = Mockito.mock[EncryptionKeysService](classOf[EncryptionKeysService])
      val pbeEncryptor = new SocurePBEStringEncryptor()
      pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
      val samlValidator = new SamlValidator(
        accountInfoService = new AccountInfoService(daoAccount = daoAccount, v2Validator),
        daoBusinessUser = daoBusinessUser,
        daoAccountV2 = daoAccountV2,
        v2Validator = v2Validator
      )
      val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
      val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
      val accountAutomationService = mock[AccountAutomationService]
      val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, 90, v2Validator, magicLinkAuditService)
      val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
      service = new DashboardUserServiceV2(daoAccount, daoAccountV2,  daoBusinessUser, passwordService, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService, auditDetailsService)
      userAccountAssociationService = new UserAccountAssociationService(daoAccountV2, daoAccount, daoBusinessUser, daoSubscriptions, v2Validator, daoUIAccountConfiguration, passwordService)
      permissionTemplateService = new PermissionTemplateService(daoAccountV2, v2Validator, clock)
      val rateLimitingService = mock[RateLimitingService]
      val modelManagementClient = mock[ModelManagementClient]
      val daoAccountUIConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, slick.driver.MySQLDriver)
      val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2, v2Validator, clock)
      val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
      val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
      businessUserService = new BusinessUserService(
      daoBusinessUser,
      daoAccount,
      daoEnvironment,
      config,
      passwordService,
      encryptionKeysService,
      samlValidator,
      clock,
      daoPublicApiKey,
      daoSubscriptions,
      daoAccountV2,
      daoRateLimit,
      pbeEncryptor,
      rateLimitingService,
      daoAccountUIConfiguration,
      modelManagementClient,
      businessUserCommonService,
      v2Validator,
      magicLinkAuditService,
      accountAutomationService,
      mailNotificationService,
      accountBundleAssociationService,
      sessionIdleTimeout = 480,
      daoProspect,
      whitelistedEmailDomain = Set("socure.com"))

    println("___BEFORE_ALL_FINISHED___")
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
  }


  test("should return business user list") {
    val result = service.listAllUsers(1,None)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (3))
      res.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
      res.fold(_ => fail, _.filter(_.email.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail))).isEmpty shouldBe true
    }
  }

  test("should return subaccount's business user list") {
    val result = service.listAllUsers(1, None, showSubAccount = true)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (4))
      res.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
      res.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
    }
  }

  test("business user list should be empty") {
    val result = service.listAllUsers(0, None)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (0))
    }
  }

  test("should fail to list business users for V2") {
    whenReady(service.listAllUsers(6, None)) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(CreatorDetailsNotAvailable), _ => fail)
    }
  }

  test("should fail to list business users for V2 for invalid account access") {
    whenReady(service.listAllUsers(8, Some(Creator(10, 6)))) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("should fail to list business users for V2 (accountId is not a sub account of creators.accountID") {
    whenReady(service.listAllUsers(8, Some(Creator(1, 6)))) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("should list business users for V2") {
    val user10 = DashboardUserV2(10,"guest10","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = true,isLocked = false)
    val user11 = DashboardUserV2(11,"guest11","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = false,isLocked = false)
    whenReady(service.listAllUsers(6, Some(Creator(10, 6)))) { res =>
      res.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.exists( _.email == user10.email) shouldBe true
        r.exists( _.email == user11.email) shouldBe true
        r.exists(_.accountsWithRoles.isDefined)
      })
    }
  }

  /***
   * Business User Creation
   */
  //TODO: Should get roles environment based, to check whether roles updated correctly when user created.
  test("create business user should say user exists already"){
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_.code should be (UnknownError.id), _ => fail)
    }
  }

  test("create business user should fail, as email starts with idle"){
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_.code should be (UnknownError.id), _ => fail)
    }
  }

  test("create business user"){
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm)
    val details = UserActivationDetails(UserFixture.delegatedUserForm.firstname, UserFixture.delegatedUserForm.lastname, UserFixture.delegatedUserForm.email, Option(""))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
      whenReady(service.listAllUsers(1, None)) { res0 =>
        res0.fold(_ => fail, _.size should be(4))
        res0.fold(_ => fail, _.map(_.email) should contain (UserFixture.staticUserForm.email))
      }
    }
  }

  test("create business user for sub account should succeed"){
    val email = "<EMAIL>"
    val userForm = BusinessUserForm(4, List(EnvironmentRoles(1, Set(DashboardUserRole.LIST_TRANSACTION.id))), UserFixture.delegatedUserForm.copy(email = email))
    val details = UserActivationDetails(UserFixture.delegatedUserForm.firstname, UserFixture.delegatedUserForm.lastname, email, Option(""))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
      whenReady(service.listAllUsers(4, None)) { res0 =>
        res0.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
      }
    }
  }

  test("create business user for a sub account should fail for invalid role"){
    val userForm = BusinessUserForm(4, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm.copy(email="<EMAIL>"))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(InvalidRoleProvisioning)), _ => fail)
    }
  }

  test("create business user for sub account should fail, if it does not have permission"){
    val email = "<EMAIL>"
    val userForm = BusinessUserForm(2, List(EnvironmentRoles(1, Set(DashboardUserRole.LIST_TRANSACTION.id))), UserFixture.delegatedUserForm.copy(email = email))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(OperationNotSupported)), _ => fail)
    }
  }

  test("create business user with no roles") {
    val userForm = BusinessUserForm(1, List.empty, UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res shouldBe 'left
      res.fold(_.message shouldBe "Business user has no roles", _ => fail)
    }
  }

  test("create business user with environments but no roles") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1,Set.empty),EnvironmentRoles(2,Set.empty)), UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res shouldBe 'left
      res.fold(_.message shouldBe "Business user has no roles", _ => fail)
    }
  }

  test("create business user with multiple environment role"){
    val user = UserFixture.delegatedUserForm.copy(email = "<EMAIL>")
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6)), EnvironmentRoles(2, Set(6, 7, 13, 14))), user)
    val details = UserActivationDetails(user.firstname, user.lastname, user.email, Option(""))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
      whenReady(service.listAllUsers(1, None)){ res =>
        res.fold(_ => fail, _.size should be (5))
        res.fold(_ => fail, _.map(_.email) should contain ("<EMAIL>"))
      }
    }
    whenReady(service.getUserIdByUsername("<EMAIL>")){ res1 =>
      res1 shouldBe 'right
      whenReady(service.getUserWithRoles(res1.right.value, None)){ res2 =>
        res2 shouldBe 'right
        res2.fold(_ => fail, _.commonRoles should contain (13))
        res2.fold(_ => fail, _.commonRoles should contain (14))
      }
    }

  }

  test("create user should fail when account does not exists"){
    val userForm = BusinessUserForm(0, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  test("create non primary user with admin role(s) should fail"){
    val userForm = BusinessUserForm(0, List(EnvironmentRoles(1, Set(8))), UserFixture.delegatedUserForm.copy(email = "<EMAIL>"))
    val result = service.createUser(userForm)
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(InvalidRoleProvisioning)), _ => fail)
    }
  }

  /***
   * Update Business User Information and Roles
   */

  test("update primary business details but roles should not be updated") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8)), EnvironmentRoles(2, Set(6))), UserFixture.delegatedUserForm.copy(firstname = "first_update", lastname = "last_update", email = "something that we don't care"))
    whenReady(service.listAllUsers(1, None)){ res =>
      val userid = res.fold(_ => fail, _.find(_.email == "<EMAIL>").fold(fail)(_.id))
      whenReady(service.updateUser(userid, userForm)){ ups =>
        ups.fold(_ => fail, _ should be (true))
        whenReady(service.listAllUsers(1, None)){ list =>
          list.fold(_ => fail, _.size should be (5))
          list.fold(_ => fail, _.map(_.firstname) should contain ("first_update"))
        }
      }
    }
  }

  test("update user info should fail if roles to be provisioned has admin roles for a non-primary users") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8)), EnvironmentRoles(2, Set(6))), UserFixture.delegatedUserForm.copy(firstname = "first_update", lastname = "last_update", email = "something that we don't care"))
    whenReady(service.updateUser(7, userForm)){ ups =>
      ups.fold(_ should be (ErrorResponseFactory.get(InvalidRoleProvisioning)),_ => fail)
    }
  }

  test("update user details along with password") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8, 13, 14)), EnvironmentRoles(2, Set(6))), UserFixture.delegatedUserForm.copy(firstname = "first_update3", lastname = "last_update3"))
    whenReady(service.listAllUsers(1, None)){ res =>
      val userid = res.fold(_ => fail, _.find(_.email == "<EMAIL>").fold(fail)(_.id))
      whenReady(service.updateUser(userid, userForm)){ ups =>
        ups.fold(_ => fail, _ should be (true))
        whenReady(service.listAllUsers(1, None)){ list =>
          list.fold(_ => fail, _.map(_.firstname) should contain ("first_update3"))
        }
        whenReady(service.getUserIdByUsername("<EMAIL>")){ res1 =>
          res1 shouldBe 'right
          whenReady(service.getUserWithRoles(res1.right.value, None)){ res2 =>
            res2 shouldBe 'right
            res2.fold(_ => fail, _.commonRoles should contain (13))
            res2.fold(_ => fail, _.commonRoles should contain (14))
          }
        }
      }
    }
  }

  test("business user details must be updated") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8))), UserFixture.delegatedUserForm.copy(email = "<EMAIL>", firstname = "primary_user_update", lastname = "primary_user_update_last"))
    whenReady(service.updateUser(1, userForm)){ ups =>
      ups.fold(_ => fail, _ should be (true))
      whenReady(businessUserService.validateUser(UserCredential("<EMAIL>", "current_password"))){ login =>
        login.fold(_ => fail, _.user.firstName should be ("primary_user_update"))
        login.fold(_ => fail, _.user.lastName should be ("primary_user_update_last"))
        login.fold(_ => fail, _.user.isAdmin should be (true))
      }
    }
  }

  test("business user with no roles should return error") {
    val userForm = BusinessUserForm(1, List.empty, UserFixture.delegatedUserForm.copy(email = "<EMAIL>", firstname = "primary_user_update", lastname = "primary_user_update_last"))
    whenReady(service.updateUser(1, userForm)){ ups =>
      ups shouldBe 'left
      ups.fold(_.message shouldBe "Business user has no roles", _ => fail)

    }
  }

  test("user details must be updated when password is empty") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8)), EnvironmentRoles(2, Set(6,13,14))), UserFixture.delegatedUserForm.copy(firstname = "first_update2", lastname = "last_update2"))
    whenReady(service.listAllUsers(1, None)){ res =>
      val userid = res.fold(_ => fail, _.find(_.email == "<EMAIL>").fold(fail)(_.id))
      whenReady(service.updateUser(userid, userForm)){ ups =>
        ups.fold(_ => fail, _ should be (true))
        whenReady(service.listAllUsers(1, None)){ list =>
          list.fold(_ => fail, _.size should be (5))
          list.fold(_ => fail, _.map(_.firstname) should contain ("first_update2"))
        }
      }
    }
  }

  test("update not found") {
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6, 8))), UserFixture.delegatedUserForm)
    whenReady(service.updateUser(0, userForm)){ ups =>
      ups.fold(_ should be (ErrorResponseFactory.get(UserNotFound)), _ => fail)
    }
  }

  test("update user info should fail while updating sub-account users with invalid roles") {
    val userForm = BusinessUserForm(4, List(EnvironmentRoles(1, Set(6, 8)), EnvironmentRoles(2, Set(6))), UserFixture.delegatedUserForm.copy(firstname = "first_update", lastname = "last_update", email = "something that we don't care"))
    whenReady(service.updateUser(4, userForm)){ ups =>
      ups.fold(_ should be (ErrorResponseFactory.get(InvalidRoleProvisioning)),_ => fail)
    }
  }

  test("sub-account business user details must be updated") {
    val userForm = BusinessUserForm(4, List(EnvironmentRoles(1, Set(DashboardUserRole.LIST_TRANSACTION.id))), UserFixture.delegatedUserForm.copy(email = "guestuser+2@@socure.com", firstname = "primary_user_update", lastname = "primary_user_update_last"))
    whenReady(service.updateUser(7, userForm)){ ups =>
      ups.fold(_ => fail, _ should be (true))
    }
  }

  test("lock user should should return true") {
    whenReady(service.listAllUsers(1, None)) {list =>
      list.fold(_ => fail, _.find(_.id == 3).fold(fail)(_.isLocked should be (false)))
      whenReady(service.toggleBusinessUserLock(3, isLocked = true, None)){ status =>
        status.fold(_ => fail, _ shouldBe true)
        whenReady(service.listAllUsers(1, None)) { check =>
          check.fold(_ => fail, _.find(_.id == 3).fold(fail)(_.isLocked should be (true)))
        }
      }
    }
  }

  test("lock user should return user is not found"){
    whenReady(service.toggleBusinessUserLock(500, isLocked = true, None)){ res =>
      res.fold(_.code should be (UserNotFound.id), _ => fail)
    }
  }

  test("unlock user should should return true") {
    whenReady(service.listAllUsers(1, None)) {list =>
      list.fold(_ => fail, _.find(_.id == 3).fold(fail)(_.isLocked should be (true)))
      whenReady(service.toggleBusinessUserLock(3, isLocked = false, None)){ status =>
        status.fold(_ => fail, _ shouldBe true)
        whenReady(service.listAllUsers(1, None)) { check =>
          check.fold(_ => fail, _.find(_.id == 3).fold(fail)(_.isLocked should be (false)))
        }
      }
    }
  }

  test("unlock user should return user is not found"){
    whenReady(service.toggleBusinessUserLock(500, isLocked = false, None)){ res =>
      res.fold(_.code should be (UserNotFound.id), _ => fail)
    }
  }

  test("deleting user should say primary user cant delete") {
    val result = service.deleteBusinessUser(5, None)
    whenReady(result) { res =>
      res._2.fold(_.code should be (PrimaryBusinessUserDeletion.id), _ => fail)
    }
  }

  test("delete business user should say user not found") {
    val result = service.deleteBusinessUser(1000, None)
    whenReady(result) { res =>
      res._2.fold(_.code should be (UserNotFound.id), _ => fail)
    }
  }

  test("delete business user should delete user") {
    whenReady(service.listAllUsers(3, None)) { list =>
      list.fold(_ => fail, _.size should be (3))
      whenReady(service.deleteBusinessUser(6, None)) { res =>
        res._2.fold(_ => fail, _ shouldBe true)
        whenReady(service.listAllUsers(3, None)) { list2 =>
          list2.fold(_ => fail, _.size should be (2))
        }
      }
    }
  }

  test("get business user roles should return user roles") {
    whenReady(service.getUserWithRoles(4, None)){ res =>
      res.fold(_ => fail, _.commonRoles should contain (13))
      res.fold(_ => fail, _.commonRoles should contain (14))
      res.fold(_ => fail, _.environmentRoles.size shouldBe 2)
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.environmentId shouldBe 5))
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.roles.size should be (3)))
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.roles should contain (6)))
      res.fold(_ => fail, _.info.email should be ("<EMAIL>"))
    }
  }

  test("get business user roles with valid creator should return user roles") {
    whenReady(service.getUserWithRoles(8, Some(AccountWithCreator(4,Creator(2,2))))){ res =>
      res.fold(_ => fail, _.info.email should be ("<EMAIL>"))
    }
  }

  test("get business user roles with valid creator for different account should return error") {
    whenReady(service.getUserWithRoles(7, Some(AccountWithCreator(3, Creator(2, 2))))) { res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.AccessForbidden.id
        r.message shouldBe ExceptionCodes.AccessForbidden.description
      }  , _ => fail)
    }
  }

  test("get business user roles with invalid creator should return error") {
    whenReady(service.getUserWithRoles(8, Some(AccountWithCreator(4, Creator(2, 3))))) { res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.AccessForbidden.id
        r.message shouldBe ExceptionCodes.AccessForbidden.description
      }  , _ => fail)
    }
  }

  test("get business user roles should return empty role with environment id") {
    whenReady(service.getUserWithRoles(7, None)){ res =>
      res.fold(_ => fail, _.commonRoles shouldBe empty)
      res.fold(_ => fail, _.environmentRoles.size shouldBe 2)
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.environmentId shouldBe 3))
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.roles shouldBe empty))
      res.fold(_ => fail, _.info.email should be ("<EMAIL>"))
    }
  }

  test("get business user roles should return no roles found") {
    whenReady(service.getUserWithRoles(2, None)) { res =>
      res.fold(_.code shouldBe RolesNotFound.id, _ => fail)
    }
  }

  test("get business user roles should return user not found") {
    whenReady(service.getUserWithRoles(400, None)) { res =>
      res.fold(_.code shouldBe UserNotFound.id, _ => fail)
    }
  }

  test("get business user id by username") {
    whenReady(service.getUserIdByUsername("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe 1)
    }
  }

  test("get business user id by username should fail when there is no user") {
    whenReady(service.getUserIdByUsername("<EMAIL>")) { res =>
      res.fold(_.code shouldBe UserNotFound.id, _ => fail)
    }
  }

  test("Success: create business user V1 - if V2 not provisioned for account, but has creator details"){
    val email = "<EMAIL>"
    val userForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(6))), UserFixture.delegatedUserForm.copy(email=email), Some(Creator(10,6)), Some(false))
    whenReady(service.createUser(userForm)) { res =>
        val details = UserActivationDetails(UserFixture.delegatedUserForm.firstname, UserFixture.delegatedUserForm.lastname, email, Option(""))
        res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
        whenReady(service.listAllUsers(1, None)) { res0 =>
          res0.fold(_ => fail, _.size should be(6))
          res0.fold(_ => fail, _.map(_.email) should contain(email))
        }

    }
  }

  test("Fail: create business user v2 - Rate limit exceeded for Primary Admin "){
    val userForm = BusinessUserForm(6, List(EnvironmentRoles(9, Set(14))), UserFixture.delegatedUserForm3.copy(email="<EMAIL>"), Some(Creator(10,6)), Some(true))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.PrimaryAdminLimitExceeded.id
        r.message shouldBe ExceptionCodes.PrimaryAdminLimitExceeded.description
      }  , _ => fail)
    }
  }

  test("Fail: create business user v2 - Sub account cannot create users for its parent"){
    val userForm = BusinessUserForm(5, List(EnvironmentRoles(9, Set(6))), UserFixture.delegatedUserForm3.copy(email="<EMAIL>"), Some(Creator(10,6)))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.AccessForbidden.id
        r.message shouldBe ExceptionCodes.AccessForbidden.description
      }  , _ => fail)
    }
  }

  test("Fail: create business user v2 - creator id and account id does not fall under same parent"){
    val userForm = BusinessUserForm(6,
      List(EnvironmentRoles(11, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
      UserFixture.delegatedUserForm3.copy(email = "<EMAIL>"),
      Some(Creator(8, 13)),
      isPrimaryAdmin = Some(false))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.AccessForbidden.id
        r.message shouldBe ExceptionCodes.AccessForbidden.description
      }  , _ => fail)
    }
  }

  test("Fail: create business user v2 - Partner Account "){
    val userForm = BusinessUserForm(9,
      List(EnvironmentRoles(19, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
      UserFixture.delegatedUserForm3.copy(email = "<EMAIL>"),
      Some(Creator(13,9)),
      isPrimaryAdmin = Some(false))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning) , _ => fail)
    }
  }

  test("Fail: create business user v2 - Partner Account - Administer flag Off"){
    val userForm = BusinessUserForm(11,
      List(EnvironmentRoles(21, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
      UserFixture.delegatedUserForm3.copy(email = "<EMAIL>"),
      Some(Creator(13,8)),
      isPrimaryAdmin = Some(false))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(r => {
        r.code shouldBe ExceptionCodes.AdminsterNotProvisioned.id
        r.message shouldBe ExceptionCodes.AdminsterNotProvisioned.description
      }  , _ => fail)
    }
  }

  test("Success: create business user v2 as non primary user - Creator is Primary Admin"){
    val userForm = BusinessUserForm(5,
                            List(EnvironmentRoles(9, Set(4, 6, 7, 5, 16, 13, 15, 17, 18)),
                                 EnvironmentRoles(10, Set(4, 6, 7, 5, 16, 13, 15, 17, 18)),
                                 EnvironmentRoles(15, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
                            UserFixture.delegatedUserForm3,
                            Some(Creator(9,5)),
                            isPrimaryAdmin = Some(false))
    val details = UserActivationDetails(UserFixture.delegatedUserForm3.firstname, UserFixture.delegatedUserForm3.lastname, UserFixture.delegatedUserForm3.email, Option(""))
    whenReady(service.createUser(userForm)){ res =>
     res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }

  ignore("Success: create business user v2 as primary user - Creator is a Primary Admin"){
    val email = "<EMAIL>"
    val userForm = BusinessUserForm(5,
      List(EnvironmentRoles(9, Set(4, 6, 7, 5, 16, 13, 15, 17, 18)),
        EnvironmentRoles(10, Set(4, 6, 7, 5, 16, 13, 15, 17, 18)),
        EnvironmentRoles(15, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
      UserFixture.delegatedUserForm3.copy(email = email),
      Some(Creator(9,5)),
      isPrimaryAdmin = Some(true))
    val details = UserActivationDetails(UserFixture.delegatedUserForm3.firstname, UserFixture.delegatedUserForm3.lastname, email, Option(""))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }

  ignore("Success: create business user v2 - Partner Account - Administer flag On"){
    val email = "<EMAIL>"
    val userForm = BusinessUserForm(10,
      List(EnvironmentRoles(20, Set(4, 6, 7, 5, 16, 13, 15, 17, 18))),
      UserFixture.delegatedUserForm3.copy(email = email),
      Some(Creator(13,8)),
      isPrimaryAdmin = Some(false))
    val details = UserActivationDetails(UserFixture.delegatedUserForm3.firstname, UserFixture.delegatedUserForm3.lastname, email, Option(""))
    whenReady(service.createUser(userForm)){ res =>
      res.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }

  test("lock user V2 should should return true") {
      whenReady(service.toggleBusinessUserLock(11, isLocked = true, Some(AccountWithCreator(6,Creator(10,6))))) { status =>
        status.fold(_ => fail, _ shouldBe true)
        whenReady(userAccountAssociationService.getUserAccountAssociation(10, 6)) { res =>
          res.fold(_ => fail, u => {
            u.map(_.status == UserAccountAssociationStatuses.LOCKED.id)
          })
        }
      }
  }

  test("lock user V2 should fail for invalid user id"){
    whenReady(service.toggleBusinessUserLock(500, isLocked = true, Some(AccountWithCreator(5,Creator(9,5))))){ res =>
      res.fold(_.code should be (UpdateUserAccountStatusFailed.id), _ => fail)
    }
  }

  test("lock user V2 should fail for invalid account id") {
    whenReady(service.toggleBusinessUserLock(9, isLocked = true, Some(AccountWithCreator(1, Creator(9, 5))))){ res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("get public account id by username") {
    whenReady(service.getPublicAccountIdByUserName("<EMAIL>")) { res =>
      res.fold(_ => fail, _ shouldBe "public-id")
    }
  }

  test("get public account id by username should fail when there is no user") {
    whenReady(service.getPublicAccountIdByUserName("<EMAIL>")) { res =>
      res.fold(_.code shouldBe UserNotFound.id, _ => fail)
    }
  }

  test("get business user roles v2 should return user roles") {
    whenReady(service.getUserWithRoles(9, Some(AccountWithCreator(5, Creator(9, 5))))){ res =>
      res.fold(_ => fail, _.commonRoles should contain (13))
      res.fold(_ => fail, _.commonRoles should contain (14))
      res.fold(_ => fail, _.environmentRoles.size shouldBe 3)
      res.fold(_ => fail, _.environmentRoles.headOption.fold(fail)(_.roles should contain (7)))
      res.fold(_ => fail, _.info.email should be ("<EMAIL>"))
    }
  }

  test("get business user roles v2 should return fail for invalid account") {
    whenReady(service.getUserWithRoles(9, Some(AccountWithCreator(1, Creator(9, 5))))){ res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("get business user roles for v2 account") {
    whenReady(service.getUserWithRolesV2(15, AccountWithCreator(10, Creator(13, 8)))){ res =>
      res.fold(_ => fail, _.commonRoles should contain (13))
      res.fold(_ => fail, _.commonRoles should contain (14))
      res.fold(_ => fail, _.commonRoles should contain (14))
      res.fold(_ => fail, _.environmentRoles.size shouldBe 1)
      res.fold(_ => fail, _.info.email should be ("<EMAIL>"))
    }
  }

  test("update primary business details but roles should not be updated in v2") {
    val userId = 9
    val userForm = BusinessUserForm(5, List.empty, UserFixture.delegatedUserForm2, Some(Creator(9, 5)))
    whenReady(userAccountAssociationService.getUserAccountAssociation(userId, 5)) { uaa =>
      uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
        uaar.isPrimaryUser shouldBe true
        whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
          whenReady(service.updateUser(userId, userForm)){ res =>
            res shouldBe Right(true)
            whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts1 =>
              pts1 shouldBe pts
            }
          }
        }
      })

    }
  }

  test("update business user v2 should be a success") {
    val userId = 9
    val userForm = BusinessUserForm(5, List(EnvironmentRoles(9, Set(6,13,14))), UserFixture.delegatedUserForm2, Some(Creator(9, 5)))
    whenReady(service.updateUser(userId, userForm)){ res =>
      res shouldBe Right(true)
    }
  }

  test("update business user v2 should be a failure") {
    val userId = 9
    val userForm = BusinessUserForm(20, List(EnvironmentRoles(1, Set(6,13,14))), UserFixture.delegatedUserForm2, Some(Creator(9, 5)))
    whenReady(service.updateUser(userId, userForm)){ res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
  }

  test("should list business users V2 for account creation - direct account") {
    val user10 = DashboardUserV2(10,"guest10","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = true,isLocked = false)
    val user11 = DashboardUserV2(11,"guest11","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = false,isLocked = false)
    whenReady(service.listAllUsers(6, Some(Creator(10, 6)), false)) { res =>
      res.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.exists( _.email == user10.email) shouldBe true
        r.exists( _.email == user11.email) shouldBe true
      })
    }
  }

  test("should list business users V2 for account creation - partner account") {
    whenReady(service.listAllUsers(8, Some(Creator(13, 8)), false)) { res =>
      res.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
      })
    }
  }

  test("should list business user with associations") {
    whenReady(service.getUserWithRolesAndAssociations(10, Creator(10, 6))) { res =>
      res.fold(_ => fail, r => {
        r shouldBe DashboardUserWithAssociations(
          UserDetails(10,"guest10","user","<EMAIL>","**********"),
          List(
            AccountWithRoles(
              AccountIdName(6,"sub-account2"),
              Vector(UserRole(Some(2),"Role-10",None,0, Some(6)))
            )
          )
        )
      })
    }
  }

  test("should list business user with associations only for active account") {
    whenReady(service.getUserWithRolesAndAssociations(10, Creator(10, 6))) { res =>
      res.fold(_ => fail, r => {
        r shouldBe DashboardUserWithAssociations(
          UserDetails(10, "guest10", "user", "<EMAIL>", "**********"),
          List(
            AccountWithRoles(
              AccountIdName(6, "sub-account2"),
              Vector(UserRole(Some(2), "Role-10", None, 0, Some(6)))
            )
          )
        )
      })
    }
  }

  test("should list business user with associations and system defined roles") {
    whenReady(service.getUserWithRolesAndAssociations(15, Creator(13, 8))) { res =>
      res.fold(_ => fail, r => {
        r shouldBe DashboardUserWithAssociations(
          UserDetails(15,"guest15","user","<EMAIL>","**********"),
          List(
            AccountWithRoles(
              AccountIdName(10,"Account10"),
              Vector(UserRole(None,"Account Owner",None, 1), UserRole(Some(4),"Role-14",None, 0, Some(8)))
            )
          )
        )
      })
    }
  }

  test("should not list business user with associations for v1 account") {
    whenReady(service.getUserWithRolesAndAssociations(1 , Creator(1, 1))) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("should not list business user when not found") {
    whenReady(service.getUserWithRolesAndAssociations(1000000, Creator(10, 6))) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
    }
  }

  test("should not list business user when user not associated with account") {
    whenReady(service.getUserWithRolesAndAssociations(1, Creator(10, 6))) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidUserAccountAssociation))
    }
  }

  test("should update business user with associations") {
//    user getting associated to non saml account without any active password, so send him an set password mail
    Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = Creator(10,6))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe Right(true)
    }
  }

  test("should update business user with associations and system defined permissions") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 2, roleId = None)))),creator = Creator(10,6))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe Right(true)
    }
  }

  test("should fail to update business user with access denied error: when user is not related to creator") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 2,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = Creator(10,6))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe 'Left
    }
  }

  test("should failure to update business user with access denied error") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 3,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 1, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = Creator(1,1))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe 'Left
    }
  }

  test("should failure to update business user with invalid system defined role type") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 3,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 1, roles= Seq(RolesInputDetails(roleType = 15, roleId = None)))),creator = Creator(1,1))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe 'Left
    }
  }

  test("should not create business user with associations, as email start with idle") {
    val email = AccountManagementDefaults.PrefixForNonFunctionalEmail+"<EMAIL>"
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = email, firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq.empty,creator = Creator(10,6))
    val details = UserActivationDetails("test","test","<EMAIL>",Some(""))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2.fold(_ shouldBe ErrorResponseFactory.get(UnknownError), _.copy(activationCode = Option("")) shouldBe details)
    }
  }

  test("should create business user with associations") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = "<EMAIL>", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq.empty,creator = Creator(10,6))
    val details = UserActivationDetails("test","test","<EMAIL>",Some(""))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }

  test("should create business user with associations and system defined role") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = "<EMAIL>", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2)), RolesInputDetails(roleType = 1, roleId = None)))),creator = Creator(9,5))
    val details = UserActivationDetails("test","test","<EMAIL>",Some(""))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }
  test("should update user during create user call for existing V2 user") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = "<EMAIL>", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq.empty,creator = Creator(10,6))
    val details = UserActivationDetails("test","test","<EMAIL>",Some(""))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2.fold(_ => fail, _.copy(activationCode = Option("")) shouldBe details)
    }
  }
    test("should update parent's  business user from not associated sub account") {
      Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
      val updateBusinessUserInput = UpdateBusinessUserInput(id = 9, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 2, roleId = None)))), creator = Creator(11, 6))
      whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
        res shouldBe Right(true)
      }
    }

    test("should fail to update sub account's  business user from not associated parent account") {
      Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
      val updateBusinessUserInput = UpdateBusinessUserInput(id = 10, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 5, roles = Seq(RolesInputDetails(roleType = 2, roleId = None)))), creator = Creator(9, 5))
      whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
        res shouldBe 'Left
      }
    }

    test("should fail to update parent's  business user from non valid sub account") {
      Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
      val updateBusinessUserInput = UpdateBusinessUserInput(id = 9, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 2, roleId = None)))), creator = Creator(11, 12))
      whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
        res shouldBe 'Left
      }
    }

    test("should fail to update sub account's  business user from non valid parent account") {
      Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
      val updateBusinessUserInput = UpdateBusinessUserInput(id = 15, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 2, roleId = None)))), creator = Creator(11, 5))
      whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
        res shouldBe 'Left
      }
    }

  ignore("should create business user with associations of parent's custom role can associated to child account") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = "<EMAIL>", firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 5, roles = Seq(RolesInputDetails(roleType = 0, roleId = Some(1)), RolesInputDetails(roleType = 1, roleId = None))), AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 0, roleId = Some(1)), RolesInputDetails(roleType = 1, roleId = None)))), creator = Creator(9, 5))
    val details = UserActivationDetails("test", "test", "<EMAIL>", Some(""))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res shouldBe 'Right
    }
  }

  test("should update business user with parent's role associations with child account") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 2, roleId = None), RolesInputDetails(roleType = 0, roleId = Some(1))))), creator = Creator(10, 6))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res shouldBe Right(true)
    }
  }

  test("should not update business user with non accessible account and  role associations with child account") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 9, roles = Seq(RolesInputDetails(roleType = 2, roleId = None), RolesInputDetails(roleType = 0, roleId = Some(1))))), creator = Creator(10, 6))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput)) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountRoleAssociation))
    }
  }

  test("should failure to create business user with access denied error") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 1,email = "<EMAIL>", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 1, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(1)), RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = Creator(1,1))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2 shouldBe 'Left
    }
  }

  test("should failure to create business user with invalid system defined role type") {
    val updateBusinessUserInput = CreateBusinessUserInput(accountId = 6,email = "<EMAIL>", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(1)), RolesInputDetails(roleType = 120, roleId = None)))),creator = Creator(9,5))
    whenReady(service.createBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { res =>
      res._2 shouldBe 'Left
    }
  }

  test("delete business user v2 should fail for invalid user id") {
    val result = service.deleteBusinessUserV2(1000, AccountWithCreator(6,Creator(10,6)))
    whenReady(result) { res =>
      res.fold(_.code should be (AccessForbidden.id), _ => fail)
    }
  }

  test("deleting user v2 should fail to delete account owner primary user") {
    val result = service.deleteBusinessUserV2(14, AccountWithCreator(12,Creator(14,12)))
    whenReady(result) { res =>
      res.fold(_.code should be (UpdateUserAccountStatusFailed.id), _ => fail)
    }
  }

  test("delete business user v2 should fail for invalid account id") {
    whenReady(service.deleteBusinessUserV2(9, AccountWithCreator(1, Creator(9, 5)))){ res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("filter users by permission - All criteria, no users success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(CaseManagementPermissions.CaseManagementSchedule.name, CaseManagementPermissions.CaseManagementReview.name), permissionCriteria = 1, None, None)
    val expectedResp = UsersByPermissionFilterResponse(Seq.empty, None, None, 0)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("filter users by permission - All criteria, success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(CaseManagementPermissions.CaseManagementComment.name, CaseManagementPermissions.CaseManagementReview.name), permissionCriteria = 1, None, None)
    val usersResp = Seq(UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", None,1,1), UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", None,2,1))
    val expectedResp = UsersByPermissionFilterResponse(usersResp, None, None, 2)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("filter users by any permission - All criteria, success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(DashboardUserPermissions.TRANSACTIONS_VIEW.name), permissionCriteria = 1, None, None)
    val usersResp = Seq(UserDetailsByPermissionFilter(1, "first_update2", "last_update2", "<EMAIL>", None, 1, 1), UserDetailsByPermissionFilter(1, "first_update2", "last_update2", "<EMAIL>", None, 2, 1), UserDetailsByPermissionFilter(1, "first_update2", "last_update2", "<EMAIL>", None, 3, 1))
    val expectedResp = UsersByPermissionFilterResponse(usersResp, None, None, 3)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("filter users by permission - All criteria, pagination success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(CaseManagementPermissions.CaseManagementComment.name, CaseManagementPermissions.CaseManagementReview.name), permissionCriteria = 1, Some(1), Some(10))
    val usersResp = Seq(UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", None,1,1), UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", None,2,1))
    val expectedResp = UsersByPermissionFilterResponse(usersResp, Some(1), Some(10), 2)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("filter users by permission - All criteria, empty page success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(CaseManagementPermissions.CaseManagementComment.name, CaseManagementPermissions.CaseManagementReview.name), permissionCriteria = 1, Some(2), Some(10))
    val expectedResp = UsersByPermissionFilterResponse(Seq.empty, Some(2), Some(10), 2)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("Filter users by permission - Any criteria success") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq(CaseManagementPermissions.CaseManagementView.name, CaseManagementPermissions.CaseManagementReview.name), permissionCriteria = 2, None, None)
    val usersResp = Seq(UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", Some(Seq("CASE_MANAGEMENT_REVIEW", "CASE_MANAGEMENT_VIEW")), 1,1), UserDetailsByPermissionFilter(1,"first_update2","last_update2","<EMAIL>", Some(Seq("CASE_MANAGEMENT_REVIEW", "CASE_MANAGEMENT_VIEW")),2,1))
    val expectedResp = UsersByPermissionFilterResponse(usersResp, None, None, 2)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => fail, _ shouldBe expectedResp)
    }
  }

  test("Filter users by permission - Unknown permission string, failure") {
    val permissionFilterReq = UsersByPermissionFilterRequest(accountIds = Seq(1), environmentIds = Some(Seq(1,2,3)), permissions = Seq("test"), permissionCriteria = 2, None, None)
    whenReady(service.getUsersFilteredByPermissions(permissionFilterReq)) { response =>
      response.fold(_ => ErrorResponseFactory.get(UserNotFound), _ => fail)
    }
  }

  test("should update business user with associations for current account and subaccounts without removing parent account associations") {
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 14,firstName = "guest14",lastName = "user",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 12, roles= Seq(RolesInputDetails(roleType = 4, roleId = None), RolesInputDetails(roleType = 1, roleId = None)))),creator = Creator(13,12))
    whenReady(service.updateBusinessUserWithAssociations(updateBusinessUserInput, false)) { res =>
      res.fold(_ => fail, _ => true)
      whenReady(service.listAllUsers(8, Some(Creator(14,8)))){ list =>
        //Check if parent account association is still present
        list.fold(_ => fail, _.filter(user => user.id == 14).map(_.accountsWithRoles.get.filter(account => account.accountDetails.id == 8)).size should be (1))
      }
    }
  }

  test("delete business user v2 should mark the user-account-association as DELETED") {
    whenReady(service.deleteBusinessUserV2(11, AccountWithCreator(6,Creator(10,6)))) { res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(userAccountAssociationService.getRolesByUserIdAndAccountId(11, 6)){ res1 =>
        res1.fold(_ => fail, _ shouldBe Seq.empty)
      }
    }
  }

  test("return if user is registered in quicksight") {
    whenReady(service.isQuicksightUser(5)) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should update registered quicksight user status - success") {
    whenReady(service.updateQuicksightUserStatus(UpdateQuicksightUserStatus(11, true))) { res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(service.isQuicksightUser(11)){ res1 =>
        res1.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should update terms of service - success") {
    whenReady(service.updateUserTOS(11)) { res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should update terms of service - fail for same user") {
    whenReady(service.updateUserTOS(11)) { res =>
      res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.EmailAlreadyExists))
    }
  }

  test("should not update business user with audit details isSuccess false") {
    Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("test"), MMatchers.eq("test"), MMatchers.eq("<EMAIL>"), MMatchers.any(classOf[String]), MMatchers.eq(false), MMatchers.eq(6L))).thenReturn(Future.successful(Right(true)))
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 15, firstName = "test", lastName = "test", contactNumber = "**********", accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles = Seq(RolesInputDetails(roleType = 2, roleId = None)))), creator = Creator(11, 5))
    whenReady(service.updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) { case (audit,response) =>
      audit.isSuccess shouldBe false
      response shouldBe 'Left
    }
  }


}
