package dashboardv2.environment.settings.service

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.AuditDetailsService
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.ApiKeyRenewalConfig
import me.socure.model.account.SocialNetworkAppKeys
import me.socure.model.dashboardv2.{Creator, EnvironmentDomain}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Mill<PERSON>, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

trait TestConfiguration extends FunSuite with BeforeAndAfterAll with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(500, Seconds), interval = Span(500, Millis))

  val mysqlService: MysqlService = MysqlService("account-service-tests")
  private val dbName = "socure"

  var service: EnvironmentSettingsService = _

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    new EnvironmentSettingsService(daoEnvironment, daoAccount, daoAccountV2, new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis), apiKeyRenewalConfig, v2Validator,auditDetailsService)
  }

  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()

    val dataSource = buildDataSource()

    prepareSchema(dataSource)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('101-205', 'Banking')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, public_id, public_api_key,external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 1, 5, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 1, 5, '${PublicIdGenerator.account().value}','publicApiKey7','externalId7'), " +
      s"(8, 'AccountName8', '101-205', false, 1, 4, '${PublicIdGenerator.account().value}','publicApiKey8','externalId8')," +
      s"(9, 'AccountName9', '101-205', false, 1, NULL, '${PublicIdGenerator.account().value}','publicApiKey9','externalId9');"
    )

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 2, true), " +
      "(3, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Locked', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 2, false), " +
      "(4, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 4, true), " +
      "(5, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 5, true), " +
      "(6, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 7, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 3, true), " +
      "(8, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Guest', 'User',NULL,'2016-09-20', '2015-12-20', 0, NULL, NULL, 9, true);"
    )

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development'), (4, 'Sandbox')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id, updated_at) VALUES" +
      "(1, 'accesstokenProd', 'secretkeyProd', 'accesstokensecretProd', 'prod.com', 1, 1, '2017-03-06 20:11:22'), " +
      "(2, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', 'prod.com', 1, 2, '2017-03-22 20:11:22'), " +
      "(3, 'accesstokenProd', 'secretkeyProd', 'accesstokensecretProd', 'prod.com', 2, 1, '2017-03-06 20:11:22'), " +
      "(4, 'accesstokenProd', 'secretkeyProd', 'accesstokensecretProd', 'prod.com', 2, 2, '2017-03-06 20:11:22'), " +
      "(5, 'accesstokenProd', 'secretkeyProd', 'accesstokensecretProd', '121.123.123.1', 3, 1, '2017-03-06 20:11:22'), " +
      "(6, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 3, 2, '2017-03-22 20:11:22'), " +
      "(7, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 4, 1, '2017-03-22 20:11:22'), " +
      "(8, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 5, 1, '2017-03-22 20:11:22'), " +
      "(9, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 6, 1, '2017-03-22 20:11:22'), " +
      "(10, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 6, 2, '2017-03-22 20:11:22'), " +
      "(11, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 7, 1, '2017-03-22 20:11:22'), " +
      "(12, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 8, 1, '2017-03-22 20:11:22'), " +
      "(13, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 9, 1, '2017-03-22 20:11:22'), " +
      "(14, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 9, 2, '2017-03-22 20:11:22'), " +
      "(15, 'accesstokenSand', 'secretkeySand', 'accesstokensecretSand', null, 9, 3, '2017-03-22 20:11:22')")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1), (1, 1, 6), (1, 1, 5), (3, 2, 6), (3, 3, 6)")

    //Environment Social Key
    sqlExecutor.execute("INSERT INTO tbl_environment_social_key (id, application_key, application_secret, network, environment_id) VALUES " +
      "(1, 'app-facebook', 'secret-facebook', 1, 3), " +
      "(2, 'app_key', 'secret_key', 2, 5), " +
      "(3, 'app_key', 'secret_key', 2, 7), " +
      "(4, 'app_key', 'secret_key', 2, 11), "+
      "(5, 'app_key', 'secret_key', 2, 12) ")

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES " +
      "(0, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), " +
      "(0, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 3, '816ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 4, '716ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22')," +
      "(0, 5, '516ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 6, '616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 7, '7616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 8, '8616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 9, '9616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 12, '1616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 11, '1016ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    //Public Api Keys
    sqlExecutor.execute("INSERT INTO tbl_public_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES " +
      "(0, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 1, '516ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 3, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), " +
      "(0, 8, '9616ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 11, '1016ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(4, 97), (5, 97), (5, 95), (6, 97), (7, 97), (8, 97), (9, 97)")

    // user_role
    sqlExecutor.execute("INSERT INTO `user_role`(id, name, description, by_business_user_id, by_account_id) VALUES (1, 'PrimaryAdmin','Primary Administrator',1,1),(2, 'InstanceAdmin','Instance Administrator',1,1);")

    // user_account_association
    sqlExecutor.execute("INSERT INTO user_account_association(id, business_user_id, account_id) VALUES(1, 4, 4), (2, 5, 5), (3, 6, 7), (4, 8, 9);")

    // user_account_role_association
    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,1,0), (2,1,0), (3,2,0), (4,NULL,1), (4,NULL,6);")

    // permission_template
    sqlExecutor.execute("INSERT INTO permission_template(id, name,type,account_id,updated_by) VALUES(1,'template1',1,1,1), (2,'template2',1,2,2);")

    // permission_template_mapping
    sqlExecutor.execute("INSERT INTO permission_template_mapping(id, permission_template_id, environment_type_id, permissions) VALUES(1, 1, 1, '1001,1002,1003,1004,1009,1017,1026,1027,1015,1016'), (2, 1, 2, '1001,1002,1003,1004,1017'), (3, 2, 1, 'asd');")

    // role_permission_template_association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(id, permission_template_id, user_role_id) values(1,1,1), (2,2,2)")

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('4','4/',1,1,1), " +
      "('8','4/8/',4,1,1), " +
      "('5','5',2,1,1)," +
      "('6','5/6/',4,1,1)," +
      "('7','7/',1,1,1) ," +
      "('9','9/',1,1,1);"
    )

    // Account Hierarchy History
    sqlExecutor.execute("INSERT INTO `account_association_history`(account_hierarchy_id) VALUES ('1')")
    service = buildService(socureDb)
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop
  }

  lazy val domain: EnvironmentDomain = EnvironmentDomain(2, List("sunder.com"), creator = Some(Creator(1, 1)))
  lazy val domainV2: EnvironmentDomain = EnvironmentDomain(1, List("sunder.com"), Some(Creator(1, 1)))
  lazy val socialUpdateKeys: SocialNetworkAppKeys = SocialNetworkAppKeys(0, "Facebook", "new_appkey", "new_secret", 1, 1, Some(Creator(1, 1)))
}
