package dashboardv2.environment.settings.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes.ApiKeyCannotBeRenewed
import me.socure.common.k8s.mysql.MysqlService
import me.socure.model.account.{ApiKeyInfo, ApiKeyStatus}
import me.socure.model.dashboardv2.{ApiKeyUpdateRequest, Creator}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

class ApiKeysTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestConfiguration {
  override val mysqlService: MysqlService = MysqlService("api-keys")
  test("should not generate apikey v2 for environment - wrong environment"){
    whenReady(service.generateApiKey(1, None)) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(ApiKeyCannotBeRenewed), _ => fail)
    }
  }

  test("should generate apikey v2 for environment"){
    whenReady(service.generateApiKey(7, Some(Creator(4,4)))) { res =>
      res.fold(_ => fail,r => {
        r.equals(1) shouldBe true
      })
    }
  }

  test("should throw unique key constraint exception on duplicate apikey for environment"){
    whenReady(service.generateApiKey(2, "916ca6193-4149-456b-ae00-00fdad2437c6")) { res =>
      res should be ('left)
    }
  }

  test("should not generate apikey for environment"){
    whenReady(service.generateApiKey(1, Some(Creator(1,1)))) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(ApiKeyCannotBeRenewed), _ => fail)
    }
  }

  test("should deprecate apikey for environment and calculate timeleft for renewal"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))) { r1 =>
      r1.fold(_ => fail, _.environments.find(_.name == "Production").fold(fail)(_.accessCredentials.apiKeys.count(_.status == ApiKeyStatus.NEW) should be(1)))
    }
    whenReady(service.changeApiKeyStatus(1, Some(Creator(1,1)))) { res =>
      res.fold(_ => fail, _.size should be (2))
      whenReady(service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))){ r2 =>
        r2.fold(_ => fail, _.environments.find(_.name == "Production").fold(fail)(a => {
          a.accessCredentials.apiKeys.count(_.status == ApiKeyStatus.NEW) should be (0)
          a.timeLeftToRenew should not be (0)
        }))
      }
    }
  }

  test("should do nothing for the environment when there is no new key"){
    whenReady(service.changeApiKeyStatus(4, None) ){ res =>
      res should be ('right)
      res.fold(_ => fail, _.size should be (0))
    }
  }

  test("generateMissingPublicApiKeys should generate missing PublicApiKeys") {
    whenReady(service.generateMissingPublicApiKeys()){ res =>
      res.fold(_ => fail, _ shouldBe true)
    }
  }

  test("fetch apikey by environmentId should return apikeys - V2-Subaccount with valid Creator") {
    val apiKeys = service.getApiKeys(11L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      println(res)
    }
  }

  test("fetch apikey by environmentId should fail - V2 with invalid Creator") {
    val apiKeys = service.getApiKeys(11L, Some(Creator(4, 4)))
    whenReady(apiKeys) { res =>
      println(res)
    }
  }

  test("fetch apikey by environmentId should return apikeys - V2 with valid Creator") {
    val apiKeys = service.getApiKeys(8L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(10,ApiKeyStatus.ACTIVE,"8616ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("fetch apikey by environmentId should return apiKeys - V1 with No Creator") {
    val apiKeys = service.getApiKeys(1L, None)
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(2,ApiKeyStatus.ACTIVE,"88-16ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("should generate apikey for environment - V1 with invalid creator"){
    whenReady(service.generateApiKey(2, Some(Creator(5,5)))) { res =>
      println(res)
    }
  }

  test("fetch apikey by environmentId should fail - InvalidRoleProvisioing") {
    val apiKeys = service.getApiKeys(10L, Some(Creator(5,6)))
    whenReady(apiKeys) { res =>
      res.fold(_.code shouldBe ExceptionCodes.NoUserAccountAssociation.id, _ => fail)
    }
  }

  test("fetch apikey by environmentId should fail - AccessDenied") {
    val apiKeys = service.getApiKeys(10L, Some(Creator(5,3)))
    whenReady(apiKeys) { res =>
      res.fold(_.code shouldBe ExceptionCodes.AccessForbidden.id, _ => fail)
    }
  }

  test("fetch apikey by environmentId should fail - ??") {
    val apiKeys = service.getApiKeys(11L, None)
    whenReady(apiKeys) { res =>
      res.fold(_.code shouldBe ExceptionCodes.CreatorDetailsNotAvailable.id, _ => fail)
    }
  }

  test("fetch public apikey by environmentId should return public apiKey-V1") {
    val apiKeys = service.getPublicApiKeys(1L, None)
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(2,ApiKeyStatus.ACTIVE,"516ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("fetch public apikey by environmentId should return public apikey-V2") {
    val apiKeys = service.getPublicApiKeys(8L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(4,ApiKeyStatus.ACTIVE,"9616ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("fetch public apikey by environmentId should return public apiKey-V1 for developer") {
    val apiKeys = service.getPublicApiKeysDev(1L, None)
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(2,ApiKeyStatus.ACTIVE,"516ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("fetch public apikey by environmentId should return public apikey-V2 for developer") {
    val apiKeys = service.getPublicApiKeysDev(8L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(4,ApiKeyStatus.ACTIVE,"9616ca6193-4149-456b-ae00-00fdad2437c6")
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("update apikey by apikey id should be true - v2") {
    val updateApiKey = service.updateApiKey(ApiKeyUpdateRequest(10, "label"), Some(Creator(5, 5)))
    whenReady(updateApiKey) { res =>
      res.fold(_ => fail, result => {
        result shouldBe true
      })
    }
  }

  test("fetch apikey by environmentId should return apikeys-V2 after update") {
    val apiKeys = service.getApiKeys(8L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(10,ApiKeyStatus.ACTIVE,"8616ca6193-4149-456b-ae00-00fdad2437c6", Some("label"))
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("fetch apikey by environmentId should return apikeys-V2 after update for developer") {
    val apiKeys = service.getApiKeysDev(8L, Some(Creator(5, 5)))
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(10,ApiKeyStatus.ACTIVE,"8616ca6193-4149-456b-ae00-00fdad2437c6", Some("label"))
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("update public apikey by public apikey id should be true") {
    val updateApiKey = service.updatePublicApiKey(ApiKeyUpdateRequest(2, "label"), None)
    whenReady(updateApiKey) { res =>
      res.fold(_ => fail, result => {
        result shouldBe true
      })
    }
  }

  test("fetch public apikey by environmentId should return public apiKey-V1 after update") {
    val apiKeys = service.getPublicApiKeys(1L, None)
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(2,ApiKeyStatus.ACTIVE,"516ca6193-4149-456b-ae00-00fdad2437c6",Some("label"))
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }

  test("update public apikey by public apikey id should be true for developer") {
    val updateApiKey = service.updatePublicApiKeyDev(ApiKeyUpdateRequest(2, "label"), None)
    whenReady(updateApiKey) { res =>
      res.fold(_ => fail, result => {
        result shouldBe true
      })
    }
  }

  test("fetch public apikey by environmentId should return public apiKey-V1 after update for developer") {
    val apiKeys = service.getPublicApiKeysDev(1L, None)
    whenReady(apiKeys) { res =>
      res.fold(_ => fail, apiKeys => {
        val expected = ApiKeyInfo(2,ApiKeyStatus.ACTIVE,"516ca6193-4149-456b-ae00-00fdad2437c6",Some("label"))
        apiKeys.nonEmpty shouldBe true
        apiKeys.length shouldBe 1
        apiKeys.contains(expected) shouldBe true
      })
    }
  }
}
