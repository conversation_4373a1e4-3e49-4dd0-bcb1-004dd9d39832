package dashboardv2.environment.settings.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccessForbidden, AccountNotFound, InvalidPermissions}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.model.account.ApiKeyStatus
import me.socure.model.dashboardv2.Creator
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

class GetEnvironmentSettingsWithApiKeysTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestConfiguration {
  override val mysqlService: MysqlService = MysqlService("get-environment-settings-with-api-keys")
  test("should return environment  settings") {
    val result = service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))
    whenReady(result){ res =>
      res.fold(_ => fail, a => {
        a.accountId should be (1)
        a.environments.size should be (2)
        a.environments.headOption.fold(fail)(_.socialAccounts.size should be (4))
        a.environments.map(_.name) should contain ("Production")
        a.environments.headOption.fold(fail)(_.accessCredentials.apiKeys.count(_.status == ApiKeyStatus.DEPRECATED) should be (0))
        a.environments.find(_.name == "Production").headOption.fold(fail)(_.accessCredentials.apiKeys.size should be (2))
        a.environments.find(_.name == "Production").headOption.fold(fail)(_.accessCredentials.apiKeys.headOption.fold(fail)(_.environmentId should be (1)))
      })
    }
  }

  test("get environment setting for another account which facebook is configured") {
    val result = service.getEnvironmentSettingsWithApiKeys(2, None)
    whenReady(result){ res =>
      res.fold(_ => fail, _.accountId should be (2))
      res.fold(_ => fail, _.environments.size should be (2))
      res.fold(_ => fail, _.environments.find(_.name == "Production").fold(fail)(a => {
        a.socialAccounts.size should be (4)
        a.socialAccounts.headOption.fold(fail)(_.appkey should be ("app-facebook"))
      }))
    }
  }

  test("get environment settings should say account not found") {
    val result = service.getEnvironmentSettingsWithApiKeys(0, Some(Creator(1,1)))
    whenReady(result){ res =>
      res.fold(_ should be (ErrorResponseFactory.get(AccountNotFound)), _ => fail)
    }
  }

  test("getEnvironmentSettingsWithApiKeys - should fail for invalid account - Request for V1 with invalid V2 Creator"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(6,7))))  { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("getEnvironmentSettingsWithApiKeys - should fail for invalid account - Request for V2 with invalid V2 Creator"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(5, Some(Creator(6,7))))  { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }


  test("getEnvironmentSettingsWithApiKeys - should return environments for V1 with No Creator"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(1, None))  { res =>
      res.fold(_ => fail, r => {
        r.environments.nonEmpty shouldBe true
        r.environments.exists(_.id == 1) shouldBe true
      })
    }
  }

  test("getEnvironmentSettingsWithApiKeys - Should return environments associated with the account - Request for V2 with Valid V2 Creator"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(5, Some(Creator(5,5))))  { res =>
      res.fold(_ => fail, r => {
        r.environments.nonEmpty shouldBe true
        r.environments.exists(_.id == 8) shouldBe true
      })
    }
  }

  test("getEnvironmentSettingsWithApiKeys - Should return environments associated with the account, and ignore the env which does not have the view settings permission"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(6, Some(Creator(5,5))))  { res =>
      res.fold(_ => fail, r => {
        r.environments.nonEmpty shouldBe true
        r.environments.exists(_.id == 9) shouldBe true
      })
    }
  }

  test("getEnvironmentSettingsWithApiKeys - should fail for invalid permissions"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(7, Some(Creator(6,7))))  { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(InvalidPermissions), _ => fail)
    }
  }
}
