package dashboardv2.environment.settings.service

import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.ipvalidator.IPAddressValidator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.constants.EnvironmentConstants
import me.socure.model.account.{ApiKeyInfo, ApiKeyStatus, EnvironmentWithDomains}
import me.socure.model.dashboardv2.{ApiKeyUpdateRequest, Creator, EnvironmentDomain, EnvironmentNameAndId}
import me.socure.model.user.DashboardUserRole
import org.apache.commons.csv.{CSVFormat, CSVParser, CSVPrinter}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

import scala.collection.JavaConverters._

/**
  * Created by sun<PERSON><PERSON> on 8/26/16.
  */
class EnvironmentSettingsServiceTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestConfiguration {
  override val mysqlService: MysqlService = MysqlService("environment-settings-service")
  test("should return environment list") {
    whenReady(service.getAccountEnvironmentList(1)) { res =>
      res.fold(_ => fail, _.size should be (2))
      res.fold(_ => fail, _.headOption.fold(fail)(_.id should be (1)))
      res.fold(_ => fail, _.headOption.fold(fail)(_.name should be ("Production")))
    }
  }

  test("should return environment not found") {
    whenReady(service.getAccountEnvironmentList(100)) { res =>
      res.fold(_.code should be (EnvironmentNotFound.id), _ => fail)
    }
  }



  test("should return environments with domains for the account id"){
    whenReady(service.getEnvironmentWithDomains(2)) { res =>
      def onSuccess(env: Seq[EnvironmentWithDomains]) = {
        val e1 = EnvironmentWithDomains(3, "Production", "prod.com")
        val e2 = EnvironmentWithDomains(4, "Development", "prod.com")
        env.size shouldBe 2
        env.contains(e1) shouldBe true
        env.contains(e2) shouldBe true
      }
      res.fold(_ => fail, onSuccess(_))
    }
  }

  test("should return environments with domains for the account id, ignoring the null/empty domain"){
    whenReady(service.getEnvironmentWithDomains(3)) { res =>
      def onSuccess(env: Seq[EnvironmentWithDomains]) = {
        val e1 = EnvironmentWithDomains(5, "Production", "121.123.123.1")
        env.size shouldBe 1
        env.contains(e1) shouldBe true
      }
      res.fold(_ => fail, onSuccess(_))
    }
  }

  test("should return error for invalid account id"){
    whenReady(service.getEnvironmentWithDomains(100)) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(EnvironmentNotFound), _ => fail)
    }
  }

  ignore("test for valid domains in the data file") {
    import java.io._

    val csvReader = new CSVParser(new InputStreamReader(getClass.getResourceAsStream("/testDomainData.csv")), CSVFormat.DEFAULT.withFirstRecordAsHeader())
    val writer = new BufferedWriter(new FileWriter("result3.csv"))

    val csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT
      .withHeader("ID", "Name", "environment", "Domain(s)", "Validity"))

    csvReader.iterator().asScala.foreach { csvRecord =>
      def optS (key: String): Option[String] = Option(csvRecord.get(key)).map(_.trim).filter(_.nonEmpty)

      val id = optS("id").getOrElse("0")
      val name = optS("name").getOrElse("NoName")
      val domain = optS("domain").getOrElse("")
      val environmentTypeId = optS("environment_type_id").getOrElse("")
      if (domain.trim.isEmpty || IPAddressValidator.validate(domain.split(",", -1).toSet))
        csvPrinter.printRecord(id, name, EnvironmentConstants(environmentTypeId.toInt).toString, domain, "Valid")
      else
        csvPrinter.printRecord(id, name, EnvironmentConstants(environmentTypeId.toInt).toString, domain, "Invalid")
    }
    csvPrinter.flush()
  }

  test("getEnvironmentSettings should return all env for primary user v1") {
    whenReady(service.getEnvironments(2, DashboardUserRole.CREATE_TRANSACTION.id, Some(Creator(2,2)))) { res =>
      res.fold(_ => fail, _.size should be (2))
      res.fold(_ => fail, _.headOption.fold(fail)(_.id should be (3)))
      res.fold(_ => fail, _.headOption.fold(fail)(_.name should be ("Production")))
    }
  }

  test("getEnvironmentSettings should return env which has permission for secondary user v1") {
    whenReady(service.getEnvironments(2, DashboardUserRole.CREATE_TRANSACTION.id, Some(Creator(3,2)))) { res =>
      res.fold(_ => fail, _.size should be (1))
      res.fold(_ => fail, _.headOption.fold(fail)(_.id should be (3)))
      res.fold(_ => fail, _.headOption.fold(fail)(_.name should be ("Production")))
    }
  }

  test("getEnvironmentSettings should return empty list if permission not present from any env for secondary user v2") {
    whenReady(service.getEnvironments(2, DashboardUserRole.EVENT_MANAGER.id, Some(Creator(3,2)))) { res =>
      res.fold(_ should be (ErrorResponseFactory.get(EnvironmentNotFound)), _ => fail)
    }
  }

  test("getEnvironmentSettings should return enironment name and id"){
    val expected = Vector(EnvironmentNameAndId(9,"Production"), EnvironmentNameAndId(10,"Development"))
    whenReady(service.getEnvironments(6, DashboardUserRole.EVENT_MANAGER.id, Some(Creator(5, 5))))  { res =>
      res.fold(_ => fail, _ shouldBe expected)
    }
  }

  test("getEnvironmentSettings should return empty list"){
    whenReady(service.getEnvironments(6, DashboardUserRole.BATCH_JOB.id, Some(Creator(5, 5))))  { res =>
      res.fold(_ => fail, _.isEmpty shouldBe true)
    }
  }

  test("getEnvironmentSettings should return all env for user with 2 overlapping system defined roles") {
    whenReady(service.getEnvironmentSettingsWithApiKeys(9, Some(Creator(8,9)), true)) { res =>
      res.fold(_ => fail, _.environments.size shouldBe(3))
      res.fold(_ => fail, _.environments.filter(_.name.equals("Production")).headOption.fold(fail)(_.name should be("Production")))
      res.fold(_ => fail, _.environments.filter(_.name.equals("Development")).headOption.fold(fail)(_.name should be("Development")))
    }
  }

}
