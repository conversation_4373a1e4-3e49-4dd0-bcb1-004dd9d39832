package dashboardv2.environment.settings.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccessForbidden, DomainNotValid, EnvironmentNotFound}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.model.dashboardv2.{Creator, EnvironmentDomain}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

class UpdateDomainTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestConfiguration {
  override val mysqlService: MysqlService = MysqlService("update-domain")
  test("update domain should fail for empty domains") {
    val envDomain = EnvironmentDomain(2, List(",,"))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain should fail for empty list") {
    val envDomain = EnvironmentDomain(2, List.empty)
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
    }
  }

  test("update domain should say environment not found") {
    val result = service.updateDomain(domain.copy(id =100))
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(EnvironmentNotFound)), _ => fail)
    }
  }

  test("update domain should fail for invalid domains") {
    val envDomain = EnvironmentDomain(2, List("localhost, ************"))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain should fail for domains and empty value") {
    val envDomain = EnvironmentDomain(2, List("************,,"))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain should pass for valid domains list") {
    val envDomain = EnvironmentDomain(2, List("************", "www.google.com"), Some(Creator(1,1)))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
    }
  }

  test("update domain v2 should be success - Request for V2 with valid Creator") {
    val result = service.updateDomain(EnvironmentDomain(7, List("sunder.com"), Some(Creator(4, 4))))
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
      whenReady(service.getEnvironmentSettingsWithApiKeys(4, Some(Creator(4, 4)))){ setting =>
        setting.fold(_ => fail, e => (e.environments.find(_.id == 7)).fold(fail)(_.domain should contain ("sunder.com")))
      }
    }
  }

  test("update domain v2 should be success - Request for V2-Subaccount with valid Creator") {
    val result = service.updateDomain(EnvironmentDomain(9, List("sunderv2.com"), Some(Creator(5, 5))))
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
      whenReady(service.getEnvironmentSettingsWithApiKeys(6, Some(Creator(5, 5)))){ setting =>
        setting.fold(_ => fail, _.environments.filter(_.id == 9).headOption.fold(fail)(_.domain should contain ("sunderv2.com")))
      }
    }
  }

  test("update domain v2 should fail - Request for V2-Subaccount with invalid Creator") {
    val result = service.updateDomain(EnvironmentDomain(11, List("sunder.com"), Some(Creator(5, 5))))
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(AccessForbidden)), _ => fail)
    }
  }

  test("update domain v2 should be success - Request for V2 with invalid Creator") {
    val result = service.updateDomain(EnvironmentDomain(7, List("sunder.com"), Some(Creator(4, 1))))
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(AccessForbidden)), _ => fail)
    }
  }

  test("update domain should be success - Request for V1 with valid Creator") {
    val result = service.updateDomain(domain)
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
      whenReady(service.getEnvironmentSettingsWithApiKeys(1,Some(Creator(1,1)))){ setting =>
        setting.fold(_ => fail, _.environments.filter(_.id == 2).headOption.fold(fail)(_.domain should contain ("sunder.com")))
      }
    }
  }

  test("update domain should fail - Request for V1 with invalid Creator") {
    val result = service.updateDomain(EnvironmentDomain(2, List("socure.com"), Some(Creator(4, 1))))
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(EnvironmentNotFound)), _ => fail)
    }
  }

  test("update domain v2 should fail for invalid domains") {
    val envDomain = EnvironmentDomain(7, List("localhost, ************"), Some(Creator(4, 4)))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain v2 should fail for empty list") {
    val envDomain = EnvironmentDomain(7, List(",,"), Some(Creator(4, 4)))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain v2 should fail for domains and empty value") {
    val envDomain = EnvironmentDomain(7, List("************,,"), Some(Creator(4, 4)))
    val result = service.updateDomain(envDomain)
    whenReady(result){ res =>
      res._2.fold(_ should be (ErrorResponseFactory.get(DomainNotValid)), _ => fail)
    }
  }

  test("update domain v2 should pass for valid domains list") {
    val envDomainV2 = EnvironmentDomain(7, List("************", "www.google.com"), Some(Creator(4, 4)))
    val result = service.updateDomain(envDomainV2)
    whenReady(result){ res =>
      res._2.fold(_ => fail, _ should be (true))
    }
  }
}
