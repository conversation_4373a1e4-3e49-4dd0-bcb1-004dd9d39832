package dashboardv2.environment.settings.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccessForbidden, EnvironmentNotFound, SocialKeyIdNotFound}
import me.socure.common.k8s.mysql.MysqlService
import me.socure.model.dashboardv2.Creator
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}

class SocialNetworkKeysTest extends FunSuite with Matchers with ScalaFutures with EitherValues with BeforeAndAfterAll with TestConfiguration {
  override val mysqlService: MysqlService = MysqlService("social-network-keys")
  test("should return social app key id for given account") {
    whenReady(service.getSocialAppKeyIds(2)) {res =>
      res should be ('right)
      res.fold(_ => fail, _ should contain (1))
    }
  }

  test("should return empty list given account") {
    whenReady(service.getSocialAppKeyIds(1)) {res =>
      res should be ('left)
      res.fold(_.code shouldBe SocialKeyIdNotFound.id, _ => fail)
    }
  }

  /**
   * Deleting Social Network Keys
   */
  test("delete social network keys should be success"){
    val result = service.deletedSocialNetworkKeys(1, None)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(service.getEnvironmentSettingsWithApiKeys(2, None)){ c =>
        c.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts.headOption.fold(fail)(a => {
          a.appkey should be ("")
          a.id should be (0)
        })))
      }
    }
  }

  test("delete social network keys should return SocialKeyIdNotFound"){
    val result = service.deletedSocialNetworkKeys(100, None)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(SocialKeyIdNotFound), _ => fail)
    }
  }

  test("upsert social keys should return true"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))){ a =>
      a.fold(_ => fail, _.environments.headOption.fold(fail)(_.socialAccounts.headOption.fold(fail)(a => {
        a.appkey should be ("")
        a.id should be (0)
      })))
      whenReady(service.upsertSocialNetworkKeys(socialUpdateKeys)){ b =>
        b.fold(_ => fail, k => (k>0) shouldBe true )
        whenReady(service.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))){ c =>
          c.fold(_ => fail, _.environments.find(_.name.equalsIgnoreCase("production")).fold(fail)(a => {
            a.socialAccounts.headOption.fold(fail)(a => {
              a.appkey should be ("new_appkey")
              a.provider should be ("Facebook")
            })
          }))
        }
      }
    }
  }

  test("upsert social keys should return true and should update existing one"){
    whenReady(service.getEnvironmentSettingsWithApiKeys(3, None)){ a =>
      a.fold(_ => fail,
        _.environments.find(_.name.equalsIgnoreCase("production")).fold(fail)
        (_.socialAccounts.find(_.provider.equals("Twitter")).fold(fail)(sa => {
          sa.appkey should be ("app_key")
          sa.appsecret should be ("secret_key")
        })))
      whenReady(service.upsertSocialNetworkKeys(socialUpdateKeys.copy(provider="Twitter", appkey = "updated_appkey", appsecret = "updated_secretkey", accountId = 3, environment = 5, creator = None))){ b =>
        whenReady(service.getEnvironmentSettingsWithApiKeys(3, None)){ c =>
          c.fold(_ => fail, _.environments.find(_.name.equalsIgnoreCase("production")).fold(fail)(a => {
            a.socialAccounts.find(_.provider.equals("Twitter")).fold(fail)(a => {
              a.appkey should be ("updated_appkey")
              a.appsecret should be ("updated_secretkey")
            })
          }))
        }
      }
    }
  }

  test("upsert social keys should say environment not found"){
    whenReady(service.upsertSocialNetworkKeys(socialUpdateKeys.copy(environment = 100))){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(EnvironmentNotFound), _ => fail)
    }
  }

  test("delete social network keys should fail - Request for V1 with invalid Creator"){
    val result = service.deletedSocialNetworkKeys(3, Some(Creator(5, 5)))
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("delete social network keys should pass - Request for V1 with valid Creator"){
    val result = service.deletedSocialNetworkKeys(2, Some(Creator(7, 3)))
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(service.getEnvironmentSettingsWithApiKeys(3, Some(Creator(7, 3)))){ c =>
        c.fold(_ => fail, environmentSettings => {
          environmentSettings.environments.exists(_.socialAccounts.exists(_.id==3)) shouldBe false
        })
      }
    }
  }

  test("delete social network keys should be success - Request for V2 with valid Creator"){
    val result = service.deletedSocialNetworkKeys(3, Some(Creator(4,4)))
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(service.getEnvironmentSettingsWithApiKeys(4, Some(Creator(4,4)))){ c =>
        c.fold(_ => fail, environmentSettings => {
          environmentSettings.environments.exists(_.socialAccounts.exists(_.id==3)) shouldBe false
        })
      }
    }
  }

  test("delete social network keys should fail - Request for V2 with invalid Creator"){
    val result = service.deletedSocialNetworkKeys(3, Some(Creator(5,5)))
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(SocialKeyIdNotFound), _ => fail)
    }
  }

  test("delete social network keys should fail - Request for V2-subaccount with invalid Creator"){
    val result = service.deletedSocialNetworkKeys(4, Some(Creator(5, 5)))
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("delete social network keys should pass - Request for V2-subaccount with valid Creator"){
    val result = service.deletedSocialNetworkKeys(5, Some(Creator(4, 4)))
    whenReady(result){ res =>
      whenReady(service.getEnvironmentSettingsWithApiKeys(8, Some(Creator(4,4)))){ c =>
        c.fold(_ => fail, environmentSettings => {
          environmentSettings.environments.exists(_.socialAccounts.exists(_.id==5)) shouldBe false
        })
      }
    }
  }
}
