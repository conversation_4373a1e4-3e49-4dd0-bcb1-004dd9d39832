package dashboardv2

import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.DaoAccount
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.account.dashboardv2.{DashboardAccountServiceV2, DashboardUserServiceV2, EnvironmentSettingsService}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.service._
import me.socure.account.validator.V2Validator
import me.socure.common.clock.FakeClock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.configuration.{AnalyticsConfig, ApiKeyRenewalConfig}
import me.socure.constants.{AccountManagementDefaults, DashboardUserPermissions, EnvironmentConstants, Status}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account._
import me.socure.model.dashboardv2.{AccountInfoWithEnvDetails, Creator, SubAccountV2}
import me.socure.model.encryption.AccountId
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.{IdAndName, RequestActions, RequestSources}
import me.socure.salt.client.SaltClient
import me.socure.salt.model.{Salt, SaltValueGenerator}
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account.DtoAccount
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.fixure.UserFixture
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.verify
import org.mockito.{ArgumentCaptor, Mockito, Matchers => MMatchers}
import org.scalatest._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import java.util.UUID
import javax.sql.DataSource
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sunderraj on 8/18/16.
 */
class DashboardAccountServiceV2Test extends FunSuite with Matchers with EitherValues with ScalaFutures with BeforeAndAfterAll with MockitoSugar with MemcachedTestSupport {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(120, Seconds), interval = Span(500, Millis))

  private val dbName = "socure"
  val mysqlService: MysqlService = MysqlService("dashboard-account-service-v2")
  override def memcachedPodLabel(): String = "dashboard-account-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  var service : DashboardAccountServiceV2 = _
  var userService : DashboardUserServiceV2 = _
  var permissionTemplateService: PermissionTemplateService = _
  var userAccountAssociationService: UserAccountAssociationService = _
  var environmentService : EnvironmentSettingsService = _
  var businessUserRoleService: BusinessUserRoleService = _
  var userRoleService: UserRoleService = _
  val encryptionKeysService: EncryptionKeysService = Mockito.mock[EncryptionKeysService](classOf[EncryptionKeysService])
  val rateLimitingService = mock[RateLimitingService]
  val accountPgpKeysService = mock[AccountPgpKeysService]
  val pgpKeyExpiryDuration = 63072000L
  //val encrypterUtil: PBEEncryptorUtil = mock[PBEEncryptorUtil]
  val mailNotificationService: MailNotificationService = mock[MailNotificationService]
  val modelManagementClient: ModelManagementClient = mock[ModelManagementClient]
  val saltClient: SaltClient = mock[SaltClient]
  val aSalt: Salt = SaltValueGenerator.aSalt()
  val customerSuccessMailId = "<EMAIL>"
  Mockito.when(saltClient.generate()).thenReturn(Future.successful(Right(aSalt)))
  Mockito.when(saltClient.get(aSalt.id)).thenReturn(Future.successful(Right(aSalt)))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database : Option[String] = None) = {
    val endpoint = mysqlService.endpoint()
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource : DataSource): Int = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas("socure")
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildService(dataSource: DataSource): DashboardAccountServiceV2 = {
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, slick.driver.MySQLDriver, clock, saltClient)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val passwordService = mock[PasswordService]
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser = daoBusinessUser, daoAccount = daoAccount, daoAccountV2 = daoAccountV2)
    permissionTemplateService = new PermissionTemplateService(daoAccountV2, v2Validator, clock)
    userService = new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService,auditDetailsService)
    userAccountAssociationService = new UserAccountAssociationService(daoAccountV2, daoAccount, daoBusinessUser, daoSubscriptions, v2Validator, daoUIAccountConfiguration, passwordService)
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig("?D(G+KbPeShVkYp3"))
    val auditRequestEncipher: AuditRequestEncipher = new AuditRequestEncipher("longtestpassword")
    val analyticsConfig = AnalyticsConfig(true)
    new DashboardAccountServiceV2(
      daoAccount, daoAccountV2, daoBusinessUser,
      passwordStorageService, daoEnvironment,
      clock, encryptionKeysService,
      apiKeyRenewalConfig,
      Some(customerSuccessMailId),
      mailNotificationService,
      daoPublicApiKey,
      v2Validator,
      pbeEncryptor,
      auditRequestEncipher,
      rateLimitingService,
      modelManagementClient,
      daoUIAccountConfiguration,
      businessUserCommonService,
      userService,
      accountPgpKeysService,
      pgpKeyExpiryDuration = pgpKeyExpiryDuration,
      auditDetailsService,
      analyticsConfig
    )
  }

  private def buildEnvironmentService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val apiKeyRenewalConfig = ApiKeyRenewalConfig(48, 1)

    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val auditDetailsService =new AuditDetailsService(daoAccountV2, v2Validator)
    new EnvironmentSettingsService(daoEnvironment, daoAccount, daoAccountV2, new FakeClock(System.currentTimeMillis()), apiKeyRenewalConfig, v2Validator,auditDetailsService)
  }

  private def buildBusinessUserRoleService(dataSource: ComboPooledDataSource): BusinessUserRoleService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val subscriptionService: SubscriptionService = Mockito.mock(classOf[SubscriptionService])
    class MockableAccountInfoCacheInvalidator extends AccountInfoCacheInvalidator(null, null, null)
    val accountInfoCacheInvalidator = mock[MockableAccountInfoCacheInvalidator]
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoCaWatchlistPreferences = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new BusinessUserRoleService(dbProxyWithMetrics, slick.driver.MySQLDriver, daoBusinessUser, daoAccountV2, accountInfoCacheInvalidator, subscriptionService, daoAccount, daoCaWatchlistPreferences, v2Validator, scalaCache)
  }

  def buildUserRoleService(dataSource: DataSource): UserRoleService = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db,dbSlave)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    new UserRoleService(daoAccount,daoAccountV2, v2Validator, daoUIAccountConfiguration)
  }

  override protected def beforeAll(): Unit ={
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource(Some(dbName))

    prepareSchema(dataSource)
    val sqlExecutor = new SQLExecutor(dataSource)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES "+
      "('101-205', 'Banking'), "+
      "('101', 'some industry')")

    //Account Details
    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES " +
      s"(1, 'AccountName1', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1'), " +
      s"(2, 'AccountName2', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey2','externalId2'), " +
      s"(3, 'AccountName3', '101-205', false, 1, 1, false, '${PublicIdGenerator.account().value}','publicApiKey3','externalId3'), " +
      s"(4, 'AccountName4', '101-205', false, 1, 2, false, '${PublicIdGenerator.account().value}','publicApiKey4','externalId4'), " +
      s"(5, 'AccountName5', '101', false, 1, 2, false, '${PublicIdGenerator.account().value}','publicApiKey5','externalId5'), " +
      s"(6, 'AccountName6', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey6','externalId6'), " +
      s"(7, 'AccountName7', '101-205', false, 0, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey7','externalId7'), " +
      s"(8, 'AccountName8', '101-205', false, 0, 7, false, '${PublicIdGenerator.account().value}','publicApiKey8','externalId8'), " +
      s"(9, 'AccountName9', '101-205', false, 0, NULL, true, '${PublicIdGenerator.account().value}','publicApiKey9','externalId9'), " +
      s"(10, 'AccountName10', '101-205', false, 0, 9, true, '${PublicIdGenerator.account().value}','publicApiKey10','externalId10')," +
      s"(11, 'AccountName11', '101-205', true, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey11','externalId11'), " +
      s"(12, 'AccountName12', '101-205', true, 1, 7, false, '${PublicIdGenerator.account().value}','publicApiKey12','externalId12'), " +
      s"(13, 'AccountName13', '101-205', true, 1, 7, false, '${PublicIdGenerator.account().value}','publicApiKey13','externalId13'), " +
      s"(14, 'TestAccountV2', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey14','externalId14'), " +
      s"(15, 'AccountName15', '101-205', false, 1, 15, false, '${PublicIdGenerator.account().value}','publicApiKey15','externalId15'), " +
      s"(16, 'AccountName16', '101-205', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey16','externalId16'), " +
      s"(17, 'AccountName17', '101-205', false, 1, 16, false, '${PublicIdGenerator.account().value}','publicApiKey17','externalId17'), " +
      s"(18, 'AccountName18', '101-205', false, 1, 16, false, '${PublicIdGenerator.account().value}','publicApiKey18','externalId18')"
    )

    // Account Hierarchy
    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('1','1/',1,1,1), " +
      "('2','1/2/',4,1,1)," +
      "('3','1/2/3',4,1,1)," +
      "('4','4/',2,1,1)," +
      "('14','14/',3,1,1)," +
      "('5','1/2/5/',4,1,1), " +
      "('15','14/15/',4,1,1), " +
      "('16','16/',2,1,1), " +
      "('17','16/17/',4,1,1), " +
      "('18','16/18/',4,1,0);")

    //Business User
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on, registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Gopal', 'haris',NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true), " +
      "(2, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>','gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 2, true), " +
      "(3, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal', 'haris',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 3, true), " +
      "(4, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal2', 'haris2',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 4, true), " +
      "(5, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal3', 'haris3',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 5, true), " +
      "(6, 'c7741e94-be93-414d-95be-56a015157917', '**********', '<EMAIL>', 'gopal4', 'haris4',NULL, '2014-02-06', '2015-10-20', 1, NULL, NULL, 6, true), " +
      "(7, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Delegated', 'User',NULL,'2016-09-20','2015-12-20', 1, NULL, NULL, 7, false), " +
      "(8, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 8, true), " +
      "(9, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'Account',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 11, true), " +
      "(10, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'AccountV2',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 14, true), " +
      "(11, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'AccountV2',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 15, true), " +
      "(12, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'AccountV2',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 15, false), "+
      "(13, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sub', 'AccountV2',NULL,'2016-09-20', '2015-12-20', 1, NULL, NULL, 14, false) "
    )

    //Environment Type
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production'), (2, 'Development'), (4, 'Sandbox')")

    //Environment
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES" +
      "(1, 'accessTokenProd', 'secretKeyProd', 'accessTokenSecretProd', 'domain.com,192.1.3.45,prod.com', 1, 1), " +
      "(2, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain.com,192.1.3.45,dev.com', 1, 2), " +
      "(3, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain.com,192.1.3.45,sandbox.com', 1, 4), " +
      "(4, 'accessTokenProd4', 'secretKeyProd4', 'accessTokenSecretProd4', 'domain.com,192.1.3.45,prod.com,account24', 2, 1), " +
      "(5, 'accessTokenProd5', 'secretKeyProd5', 'accessTokenSecretProd5', 'domain.com5', 5, 1), " +
      "(6, 'accessTokenProd6', 'secretKeyProd6', 'accessTokenSecretProd6', 'domain.com6', 6, 1), " +
      "(7, 'accessTokenProd7', 'secretKeyProd7', 'accessTokenSecretProd7', 'domain.com7', 7, 1), " +
      "(8, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 8, 1), " +
      "(9, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 9, 1), " +
      "(10, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 10, 1)," +
      "(11, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 3, 1)," +
      "(12, 'accessTokenProd8', 'secretKeyProd8', 'accessTokenSecretProd8', 'domain.com8', 3, 2)," +
      "(13, 'accessTokenProd13', 'secretKeyProd13', 'accessTokenSecretProd13', 'domain.com13', 11, 1)," +
      "(14, 'accessTokenProd14', 'secretKeyProd14', 'accessTokenSecretProd14', 'domain.com14', 11, 2)," +
      "(15, 'accessTokenProd15', 'secretKeyProd15', 'accessTokenSecretProd15', 'domain.com15', 12, 1)," +
      "(16, 'accessTokenProd16', 'secretKeyProd16', 'accessTokenSecretProd16', 'domain.com16', 12, 2), " +
      "(17, 'accessTokenProd17', 'secretKeyProd17', 'accessTokenSecretProd17', 'domain.com17', 13, 1), " +
      "(18, 'accessTokenProd18', 'secretKeyProd18', 'accessTokenSecretProd18', 'domain.com18', 13, 2), " +
      "(19, 'accessTokenProd19', 'secretKeyProd19', 'accessTokenSecretProd19', 'domain.com19', 14, 1), " +
      "(20, 'accessTokenProd20', 'secretKeyProd20', 'accessTokenSecretProd20', 'domain.com20', 14, 2), " +
      "(21, 'accessTokenProd21', 'secretKeyProd21', 'accessTokenSecretProd21', 'domain.com21', 4, 1), " +
      "(22, 'accessTokenProd22', 'secretKeyProd22', 'accessTokenSecretProd22', 'domain.com22', 15, 1), " +
      "(23, 'accessTokenProd23', 'secretKeyProd23', 'accessTokenSecretProd23', 'domain.com23', 16, 1), " +
      "(24, 'accessTokenProd24', 'secretKeyProd24', 'accessTokenSecretProd24', 'domain.com24', 17, 1), " +
      "(25, 'accessTokenProd25', 'secretKeyProd25', 'accessTokenSecretProd25', 'domain.com25', 17, 2), " +
      "(26, 'accessTokenProd26', 'secretKeyProd26', 'accessTokenSecretProd26', 'domain.com26', 18, 1);"
    )

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(3, 2, '916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(4, 3, '816ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(5, 3, '316ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(6, 7, 'D-916ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(7, 4, '406ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(8, 5, '526ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(9, 6, 'E-626ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(10, 8, '876ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(11, 9, '91-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(12, 10, '92-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(13, 12, '93-926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(14, 13, '106ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(15, 14, '13-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), "+
      "(16, 15, '11926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(17, 16, '126ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), " +
      "(18, 17, '17926ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(19, 17, '1716ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), "+
      "(20, 18, '181926ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(21, 18, '1826ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), "+
      "(22, 19, '2026ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), "+
      "(23, 20, '1926ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), "+
      "(24, 21, '2126ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), "+
      "(25, 22, '2226ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), " +
      "(26, 23, '2326ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), " +
      "(27, 24, '2426ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), " +
      "(28, 25, '2526ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'), " +
      "(29, 26, '2626ca6193-4149-456b-ae00-00fdad2437c6a', 'active', '2017-03-06 20:11:22', '2017-03-13 20:11:22'); ")

    //Business User Role
    sqlExecutor.execute("INSERT INTO tbl_business_user_environment_role (environment_id, business_user_id, role) VALUES(NULL, 1, 1)")
    // User Role
    sqlExecutor.execute("INSERT INTO `user_role`(id, name, description, by_business_user_id, by_account_id) VALUES " +
      "(1, 'Role-1','Primary Administrator',10, 14), " +
      "(2, 'Role-2','Primary Administrator',10, 14); ")
    // User Account Association
    sqlExecutor.execute("INSERT INTO user_account_association(business_user_id, account_id, is_primary_user) VALUES(1, 1, 1), (10, 14, 1), (2, 2, 1), (3, 2, 0),(11,16,1),(11,18,1),(12,16,0), (13, 14, 0);")
    sqlExecutor.execute("INSERT INTO user_account_role_association(user_account_association_id, user_role_id, role_type) VALUES(1,1,0),(3,1,0), (2,1,0), (4,2,0), (5,1,0), (6,1,0);")
    // Permission Template
    sqlExecutor.execute("INSERT INTO permission_template(name,type,account_id,updated_by) VALUES('template1',1,14,2),('template2',2,1,1),('template3',1,1,3),('template4',2,1,1),('template5',1,1,3);")
    sqlExecutor.execute("INSERT INTO permission_template_mapping(permission_template_id, environment_type_id, permissions) VALUES" +
      "(1, 0, '1001,1002,1003,1004,1030,1034'), "+
      "(1, 1, '1009,1010'); ")
    // Role Permission Template Association
    sqlExecutor.execute("INSERT INTO role_permission_template_association(permission_template_id, user_role_id) values(1,1)")
    //Account Permission
    sqlExecutor.execute("INSERT INTO tbl_account_permission (account_id, permission) VALUES(14,62), (14,65), (14,77), (14, 95), (14, 97), (14, 1), (14, 2), (14, 35), (14, 49), (14, 59), (14, 66), (14, 24), (15, 97), (16,95), (16, 97), (17, 97), (18, 97), (1, 52), (1, 13), (14, 52), (1, 25), (1, 96), (14, 25), (14, 96), (14, 88), (14,94)")
    // Account Hierarchy History
    sqlExecutor.execute("INSERT INTO `account_association_history`(account_hierarchy_id) VALUES (1),(2),(3),(4),(5),(6),(7),(8),(9),(10)")
    //DefaultModules
    sqlExecutor.execute("INSERT INTO `default_modules`(account_Id, modules) VALUES (14, '110, 94, 99'), (17, '77, 93, 99'), (18, '77, 93, 99')")

    service = buildService(dataSource)
    environmentService = buildEnvironmentService(dataSource)
    businessUserRoleService = buildBusinessUserRoleService(dataSource)
    userRoleService = buildUserRoleService(dataSource)
  }

  override def afterAll() {
    dataSources.foreach(_.close())
    mysqlService.stop()
    cleanupMemcached
  }

  /***
   * Get SubAccount Lists
   */
  test("get subaccount count with details"){
    val result = service.listAllAccounts(1)
    whenReady(result) { res =>
      res.fold(_ => fail, _.headOption.fold(fail)(a => a.parentAccount should be (None)))
      res.fold(_ => fail, _.flatMap(_.parentAccount).headOption.fold(fail)(_ should be ("AccountName1")))
      res.fold(_ => fail, _.size should be (3))
      res.fold(_ => fail, _.headOption.fold(fail)(_.userCount should be (1)))
      res.fold(_ => fail, _.headOption.fold(fail)(_.email should be ("<EMAIL>")))
    }
  }

  test("should get sub accounts with environment details"){
    val expectedResult = Set(
      AccountInfoWithEnvDetails(
        id = 2,
        name = Some("AccountName2"),
        environments = Set(
          IdAndName(id = 4, name = None)
        )
      ),
      AccountInfoWithEnvDetails(
        id = 3,
        name = Some("AccountName3"),
        environments = Set(
          IdAndName(id = 11, name = None),
          IdAndName(id = 12, name = None)
        )
      )
    )
    val result = service.listSubAccountsWithEnvDetails(1)
    whenReady(result)(_ shouldBe Right(expectedResult))
  }

  test("get subaccount count with details and industry name"){
    val result = service.listAllAccounts(1)
    whenReady(result) { res =>
      res.fold(_ => fail, _.headOption.fold(fail)(_.industry should be ("Banking")))
    }
  }

  test("should get subaccount details for accountid 2") {
    val result = service.listAllAccounts(2)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (3))
      res.fold(_ => fail, _.headOption.fold(fail)(_.email shouldBe "<EMAIL>"))
    }
  }

  test("no subaccount should return") {
    val result = service.listAllAccounts(0)
    whenReady(result) { res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(SubAccountNotFound), _ => fail)
    }
  }

  test("should get subaccount info for an account") {
    val result = service.listSubAccounts(2)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (2))
      res.fold(_ => fail, _.toSet shouldBe Set(SubAccountV2(4,"AccountName4","Banking",status = true, Some("2/4/")), SubAccountV2(5,"AccountName5","some industry",status = true, Some("2/5/"))))
    }
  }

  test("should return an empty when no sub account found") {
    val result = service.listSubAccounts(6)
    whenReady(result) { res =>
      res.fold(_ => fail, _.size should be (0))
    }
  }


  /***
   * Create SubAccount
   */
  test("should create subaccount"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))

    val result = service.createSubAcount(1, UserFixture.staticUserForm.copy(modules = Some(Set.empty)), true)
    whenReady(result){ res =>
      res.right.value should be (true)
      whenReady(service.listSubAccounts(1)){ list =>
        list.fold(_ => fail, _.size should be (3))
        val subAccountId = list.right.get.find(_.name == UserFixture.staticUserForm.companyname).map(_.id)
        subAccountId.isDefined shouldBe true
        list.fold(_ => fail, _.map(_.name) should contain (UserFixture.staticUserForm.companyname))
        whenReady(userService.listAllUsers(subAccountId.get, None)){ users =>
          users.fold(_ => fail, _.size should be (1))
          users.fold(_ => fail, _.map(_.email) should contain (UserFixture.staticUserForm.email))
        }
        val resultEnvSrv = environmentService.getEnvironmentSettingsWithApiKeys(accountId.getValue, None)
        whenReady(resultEnvSrv){ env =>
          env.fold(_ => fail, _.environments.nonEmpty shouldBe true)
          env.fold(_ => fail, _.environments.map(_.name) should contain ("Production"))
          env.fold(_ => fail, _.environments.find(_.name == "Production").fold(fail)(_.accessCredentials.apiKeys.headOption.fold(fail)(_.environmentId should be (27))))
        }
      }
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }

  test("should not create subaccount, as email starts with idle"){
    val email = AccountManagementDefaults.PrefixForNonFunctionalEmail+"<EMAIL>"
    val result = service.createSubAcount(1, UserFixture.staticUserForm.copy(email=email , modules = Some(Set.empty)), true)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(UnknownError), _ => fail)
    }
  }


  test("should create subaccount with no password and return activation details"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithNoPassword(1, UserFixture.userFormWithNoPassword)
    whenReady(result){ res =>
      res.fold(_ => fail, _.firstname should be (UserFixture.userFormWithNoPassword.firstname))
      res.fold(_ => fail, _.activationCode.isEmpty should be (false))
      whenReady(service.listAllAccounts(1)){ list =>
        list.fold(_ => fail, _.size should be (4))
        list.fold(_ => fail, _.map(_.email) should contain (UserFixture.userFormWithNoPassword.email.toLowerCase))
        list.fold(_ => fail, _.map(_.id) should contain (1))
      }
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }

  test("should create subaccount with min details and return true"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val subAccount = UserFixture.subAccount
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(1L), MMatchers.eq("AccountName1"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithMinDetails(1L, subAccount, userId = None, accountId = None, primaryUserIdOpt = None)
    whenReady(result){ res =>
      res.fold(_ => fail, k => {k._1.name shouldBe "AccountName1"})
      verify(mailNotificationService).sendSubAccountCreatedNotification(MMatchers.eq(1L), MMatchers.eq("AccountName1"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.never()).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("should create subaccount V2 with min details and return true"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val accountName = "AccountName14"
    val subAccount = UserFixture.subAccount.copy(companyname = accountName, administer = Some(true), modules = Some(Set(1, 2)))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimitCollection(MMatchers.any(classOf[Seq[SaveRateLimitingInput]]), MMatchers.anyBoolean())).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithMinDetails(14L, subAccount, userId = Some(10L), accountId = Some(14L), primaryUserIdOpt = Some(10L))
    whenReady(result){ res =>
      res.fold(_ => fail, k => {k._1.name shouldBe "TestAccountV2"})
      whenReady(service.listSubAccounts(14)) { res0 =>
        whenReady(userRoleService.getUserRolesByAccountId(res0.right.get.head.id, Creator(10L,14L))) { uaa =>
          uaa.fold(_ => fail, _.length shouldBe 14)
        }
      }
      verify(mailNotificationService).sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("should create subaccount V2 with min details and primary admin and return true"){
    Mockito.reset(mailNotificationService, accountPgpKeysService)
    val accountName = "AccountV2WithPrimaryAdmin"
    val subAccount = UserFixture.subAccount.copy(companyname = accountName, administer = Some(true), modules = Some(Set(1, 2)))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithMinDetails(14L, subAccount, userId = Some(10L), accountId = Some(14L), primaryUserIdOpt = Some(10L))
    whenReady(result){ res =>
      res.fold(_ => fail, k => {k._1.name shouldBe "TestAccountV2"})
      whenReady(service.listSubAccountsV2(14L, Creator(10L, 14L), None, None)) { subaccounts =>
        subaccounts.fold(_ => fail,subaccountOpt => subaccountOpt.map { subAccount =>
          if(subAccount.name == accountName) {
            whenReady(userAccountAssociationService.getUserAccountAssociation(10L, subAccount.id)) { uaa =>
              uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>

                uaar.userRoles.isEmpty shouldBe false
                uaar.isPrimaryUser shouldBe true
                whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
                  pts.fold(_ => fail, ptsms => {
                    ptsms.map { ptsm =>
                      whenReady(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(ptsm.id, uaar.userId, uaar.accountId)) { pstmOpt =>
                        pstmOpt.fold(_ => fail, { a =>

                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id))
                          a.filter(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                        })
                      }
                    }
                  })
                }
              })
            }
          }
        })
      }
      verify(mailNotificationService).sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
      Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
    }
  }

  test("should create subaccount V2 with min details should fail as requested permissions are not valid"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val accountName = "AccountName14"
    val subAccount = UserFixture.subAccount.copy(companyname = accountName, administer = Some(true), modules = Some(Set(1, 2, 3)))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithMinDetails(14L, subAccount, userId = Some(10L), accountId = Some(14L), primaryUserIdOpt = Some(10L))
    whenReady(result){ res =>
      res.fold(f => {
        f.code shouldBe ExceptionCodes.InvalidPermissions.id
        f.message shouldBe ExceptionCodes.InvalidPermissions.description
      }, _ => fail)
      verify(mailNotificationService, Mockito.never).sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
    }
    Mockito.verify(modelManagementClient, Mockito.never).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.never).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("should create subaccount V2 with min details - check for feature flags"){
    Mockito.reset(mailNotificationService, modelManagementClient, accountPgpKeysService)
    val accountName = "AccountName100"
    val subAccount = UserFixture.subAccount.copy(companyname = accountName, administer = Some(true), modules = Some(Set(1, 2, 35)))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimitCollection(MMatchers.any(classOf[Seq[SaveRateLimitingInput]]), MMatchers.anyBoolean())).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAcountWithMinDetails(14L, subAccount, userId = Some(10L), accountId = Some(14L), primaryUserIdOpt = Some(10L))
    whenReady(result){ res =>
      res.fold(_ => fail, k => {k._1.name shouldBe "TestAccountV2"})
      whenReady(service.fetchAccountPermissions(accountId.getValue)) { res0 =>
        res0 shouldBe 'right
        val r = res0.right.value
        r.map(_.permission).toSet subsetOf Set(1,2,24,35,49,59,66,95,97)
      }
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    verify(mailNotificationService).sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
    Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("should return a error when the account is not a parent account") {
    val result = service.createSubAcountWithMinDetails(2, UserFixture.subAccount, userId = None, accountId = None, primaryUserIdOpt = None)
    whenReady(result) { res =>
      res.fold(_.code should be (ParentAccountNotFound.id), _ => fail)
    }
  }

  test("should return a error when the parent account is not valid") {
    val result = service.createSubAcountWithMinDetails(110, SubAccount("AccountName2", "101-205", None, None), userId = None, accountId = None, primaryUserIdOpt = None)
    whenReady(result) { res =>
      res.fold(_.code should be (ParentAccountNotFound.id), _ => fail)
    }
  }

  test("should return a error for duplicate account name") {
    val result = service.createSubAcountWithMinDetails(1, SubAccount("AccountName2", "101-205", None, None), userId = None, accountId = None, primaryUserIdOpt = None)
    whenReady(result) { res =>
      res.fold(_.code should be (AccountAlreadyExists.id), _ => fail)
    }
  }

  test("should return a generic error when the user already exists") {
    val result = service.createSubAcount(0, UserFixture.staticUserForm)
    whenReady(result) { res =>
      res.fold(_.code should be (UnknownError.id), _ => fail)
    }
  }

  test("should says registration failed when password policy does not meet") {
    whenReady(service.createSubAcount(0, UserFixture.staticUserForm.copy(email = "<EMAIL>", password = "first_name"))) { res =>
      res.fold(_.code should be (PasswordContainsPersonalInfo.id), _ => fail)
    }
  }

  /***
   * Update SubAccount Details
   */
  test("should update subaccount details") {
    val updatedForm = UserFixture.staticUserForm.copy(firstname = "fist_update", lastname = "last_update", contactnumber = "*********")
    val result = service.updateSubAccount(UserFixture.staticUserForm.email, updatedForm)
    whenReady(result){ res =>
      res.fold(_ => fail, _ should be (true))
    }
  }

  test("update subaccount should say sub account not found") {
    val updatedForm = UserFixture.staticUserForm.copy(firstname = "fist_update", lastname = "last_update", contactnumber = "*********")
    val result = service.updateSubAccount("<EMAIL>", updatedForm)
    whenReady(result){ res =>
      res.fold(_ shouldBe ErrorResponseFactory.get(SubAccountNotFound), _ => fail)
    }
  }

  /**
   * Activate Account
   */

  test("activate account should return true") {
    whenReady(service.listAllAccounts(6)){ list =>
      list.fold(_ => fail, _.headOption.fold(fail)(_.status should be (false)))
      whenReady(service.toggleAccountActivateStatus(6, isActive = true, None)){ updates =>
        updates.fold(_ => fail, _ shouldBe true)
        whenReady(service.listAllAccounts(6)){ list1 =>
          list1.fold(_ => fail, _.headOption.fold(fail)(_.status should be (true)))
        }
      }
    }
  }

  test("activate account V2 should return true") {
    whenReady(service.toggleAccountActivateStatus(15, isActive = true, Some(Creator(10,14)))){ updates =>
      updates.fold(_ => fail, _ shouldBe true)
      whenReady(service.getAccountHierarchy(15)){ res =>
        res.fold(_ => fail, a => a.hierarchyStatus shouldBe Status.ACTIVE.id)
      }
    }
  }

  test("activate account V2 should return true - Partner Account with administer flag off") {
    whenReady(service.toggleAccountActivateStatus(18, isActive = true, Some(Creator(11,16)))){ updates =>
      updates.fold(_ => fail, _ shouldBe true)
      whenReady(service.getAccountHierarchy(18)){ res =>
        res.fold(_ => fail, a => a.hierarchyStatus shouldBe Status.ACTIVE.id)
      }
    }
  }

  test("deactivate account V2 should fail - as it has active sub accounts") {
    whenReady(service.toggleAccountActivateStatus(14, isActive = false, Some(Creator(10,14)))){ updates =>
      updates.fold(_ should be (ErrorResponseFactory.get(UpdateAccountStatusFailedHasActiveSubAccounts)), _ => fail)
      whenReady(service.getAccountHierarchy(14)){ res =>
        res.fold(_ => fail, a => a.hierarchyStatus shouldBe Status.ACTIVE.id)
      }
    }
  }


  test("activate account should return account not found") {
    val result = service.toggleAccountActivateStatus(200, isActive = true, None)
    whenReady(result) { res =>
      res.fold(_.code should be (AccountNotFound.id), _ => fail)
    }
  }

  /**
   * Deactivate Account
   */

  test("deactivate account should return true") {
    whenReady(service.listAllAccounts(6)){ list =>
      list.fold(_ => fail, _.headOption.fold(fail)(_.status should be (true)))
      whenReady(service.toggleAccountActivateStatus(6, isActive = false, None)){ updates =>
        updates.fold(_ => fail, _ shouldBe true)
        whenReady(service.listAllAccounts(6)){ list1 =>
          list1.fold(_ => fail, _.headOption.fold(fail)(_.status should be (false)))
        }
      }
    }
  }

  test("deactivate account should return account not found") {
    val result = service.toggleAccountActivateStatus(200, isActive = false, None)
    whenReady(result) { res =>
      res.fold(_.code should be (AccountNotFound.id), _ => fail)
    }
  }

  /**
   * Get All Account Details
   */

  test("get all account details should return details") {
    whenReady(service.getAllAccounts(1)){ list =>
      list.fold(_ => fail, _.nonEmpty shouldBe true)
      list.fold(_ => fail, _.map(_.name) should contain ("AccountName2"))
    }
  }

  test("get all account details should return account not found") {
    whenReady(service.getAllAccounts(100)){ list =>
      list.fold(_.code should be (AccountNotFound.id), _ => fail)
    }
  }

  test("deprecate all apikeys should return Api Key status updated") {
    whenReady(service.deprecateApiKeys()){ res =>
      res.fold(_ => fail, _ should be ("Api keys status updated"))
    }
  }

  test("deprecate all apikeys should return No Api Keys updated") {
    whenReady(service.deprecateApiKeys()){ res =>
      res.fold(_ => fail, _ should be ("No Api keys updated"))
    }
  }

  test("should return left when no sub account exists for getApiKeysForSubAccounts") {
    val actual = service.getApiKeysForSubAccounts(233, None)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe Seq.empty)
    }
  }

  test("should return new api keys when sub accounts exists") {
    val actual = service.getApiKeysForSubAccounts(7, None)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.size shouldBe 4)
      response.fold(_ => fail, _.headOption.fold(fail)(_.accountName shouldBe "AccountName13"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentId shouldBe 17))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentType shouldBe "Production"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.apiKeys.size shouldBe 1))
    }
  }

  test("getApiKeysForAccountAndSubAccounts: should return left when no account exists") {
    val actual = service.getApiKeysForAccountAndSubAccounts(233)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _ shouldBe List.empty)
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub accounts - V2") {
    val data = SubAccountApiKeys("AccountName15",List(ApiKeyDetails("2226ca6193-4149-456b-ae00-00fdad2437c6a", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),22,"Production")
    whenReady(service.getApiKeysForSubAccounts(14, Some(Creator(10,14)))){ response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.contains(data) shouldBe true
      })
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub accounts with administer flag On - V2") {
    val data = SubAccountApiKeys("AccountName17",List(ApiKeyDetails("2426ca6193-4149-456b-ae00-00fdad2437c6a",ApiKeyStatus.ACTIVE,canBeRenewed = true,None)),24,"Production")
    whenReady(service.getApiKeysForSubAccounts(16, Some(Creator(11,16)))){ response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe true
        r.contains(data) shouldBe true
        r.exists(_.accountName.equalsIgnoreCase("accountname18")) shouldBe false
      })
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub accounts(Secondary User) - V2") {
    whenReady(service.getApiKeysForSubAccounts(16, Some(Creator(12,16)))){ response =>
      response.fold(_ => fail, r => {
        r.isEmpty shouldBe true
      })
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub accounts(Primary User) - V2") {
    whenReady(service.getApiKeysForSubAccounts(16, Some(Creator(11,16)))){ response =>
      response.fold(_ => fail, r => {
        r.isEmpty shouldBe false
      })
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub accounts with administer flag Off - V2") {
    whenReady(service.getApiKeysForSubAccounts(18, Some(Creator(11,18)))){ response =>
      response.fold(_ => fail, r => {
        r.isEmpty shouldBe true
      })
    }
  }

  test("getApiKeysForSubAccounts should fetch all the apikeys for sub account - V2") {
    whenReady(service.getApiKeysForSubAccounts(15, Some(Creator(10,14)))){ response =>
      response.fold(_ => fail, r => {
        r.nonEmpty shouldBe false
      })
    }
  }

  test("getApiKeysForSubAccounts should fail to fecth all the apikeys for sub account - V2") {
    whenReady(service.getApiKeysForSubAccounts(14, Some(Creator(10,19)))){ response =>
      response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("getApiKeysForAccountAndSubAccounts: should return new api keys when accounts exists") {
    val actual = service.getApiKeysForAccountAndSubAccounts(7L)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.size shouldBe 4)
      response.fold(_ => fail, _.headOption.fold(fail)(_.accountName shouldBe "AccountName13"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentId shouldBe 17))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentType shouldBe "Production"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.apiKeys.size shouldBe 1))
    }
  }

  test("should return account info for environment id") {
    val actual = service.getAccountInfoForEnvironment(17)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.name shouldBe "AccountName13")
      response.fold(_ => fail, _.parentId shouldBe Some(7))
    }
  }

  test("should return left when invalid environment id") {
    val actual = service.getAccountInfoForEnvironment(233)
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(_.code should be (EnvironmentNotFound.id), _ => fail)
    }
  }

  test("Test sub account creation") {
    val account: DtoAccount = DtoAccount(accountId = 1,
      name = "AccountName1",
      industrySector = "101-205",
      isInternal = false,
      isActive = true,
      parentId = None,
      isDeleted = false,
      firstActivatedAt = None,
      publicId = "123213",
      publicApiKey = "publicApiKey1"
    )

    val subAccount: DtoAccount = DtoAccount(accountId = 2L,
      name = "AccountName2",
      industrySector = "101-205",
      isInternal = false,
      isActive = true,
      parentId = Some(1L),
      isDeleted = false,
      firstActivatedAt = None,
      publicId = "123213",
      publicApiKey = "publicApiKey2"
    )
    val clock: FakeClock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)


    val eventualLongs = service.addSubAccountEnvironments(account, subAccount, clock)
    whenReady(eventualLongs) {environmentIds =>
      environmentIds.size shouldBe EnvironmentConstants.values.size
    }
  }

  test("Get sub accounts, should list only immediate sub accounts - success") {
    val creator = Creator(userId = 10, accountId = 14)

    whenReady(service.listSubAccountsV2(14, creator, None, None)) { response =>
      response.fold(_ => fail, s => {
        s.nonEmpty shouldBe true
        s.size should be >= 1
        s.exists(_.name.equals("AccountName5")) shouldBe false
        s.exists(_.name.equals("AccountName15")) shouldBe true
      })
    }
  }

  test("Get sub accounts, should list only immediate sub accounts skip permission check - success") {
    val creator = Creator(userId = 12, accountId = 16)
    whenReady(service.listSubAccountsV2(16, creator, None, None, true)) { response =>
      response.fold(_ => fail, s => {
        s.nonEmpty shouldBe true
        s.size should be >= 1
        s.exists(_.name.equals("AccountName17")) shouldBe true
        s.exists(_.name.equals("AccountName18")) shouldBe true
        s.exists(_.name.equals("AccountName12")) shouldBe false
      })
    }
  }

  test("Get sub accounts, should list only immediate sub accounts, permission check needed - failure") {
    val creator = Creator(userId = 12, accountId = 16)
    whenReady(service.listSubAccountsV2(16, creator, None, None)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning))
    }
  }

  test("Get sub accounts, should list only immediate sub accounts, permission check needed parameter passed - failure") {
    val creator = Creator(userId = 12, accountId = 16)
    whenReady(service.listSubAccountsV2(16, creator, None, None, false)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning))
    }
  }

  test("Get sub accounts - failure") {
    val creator = Creator(userId = 1, accountId = 1)
    whenReady(service.listSubAccountsV2(100, creator, None, None)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
    }
  }

  test("Get sub accounts pagination - success") {
    val creator = Creator(userId = 10, accountId = 14)

    whenReady(service.listSubAccountsV2(14, creator, Some(1), Some(1))) { response =>
      response.fold(_ => fail, s => {
        s.nonEmpty shouldBe true
        s.size shouldBe 1
      })
    }
  }

  test("validate permissions by apikey") {
    val creator = Creator(userId = 1, accountId = 1)
    whenReady(service.validateAccessPermission("526ca6193-4149-456b-ae00-00fdad2437c6", creator, DashboardUserPermissions.TRANSACTIONS_CREATE.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidRoleProvisioning), _ => fail)
    }
  }

  test("validate permissions by apikey, for partner account - success") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessPermission("2026ca6193-4149-456b-ae00-00fdad2437c6", creator, DashboardUserPermissions.TRANSACTIONS_CREATE.id.toString)) { response =>
      response.fold(_ => fail, _ => true)
    }
  }

  test("validate permissions by apikey, for partner account - should fail if administer flag is off") {
    val creator = Creator(userId = 12, accountId = 16)
    whenReady(service.validateAccessPermission("2626ca6193-4149-456b-ae00-00fdad2437c6a", creator, DashboardUserPermissions.TRANSACTIONS_CREATE.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AdministerSubAccountsNotProvisioned), _ => fail)
    }
  }


  test("validate permissions by apikey, for invalid permissions - fail") {
    val creator = Creator(userId = 1, accountId = 1)
    whenReady(service.validateAccessPermission("526ca6193-4149-456b-ae00-00fdad2437c6", creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidPermissions), _ => fail)
    }
  }

  test("validate permissions by apikey, for invalid account association - fail") {
    val creator = Creator(userId = 1, accountId = 190)
    whenReady(service.validateAccessPermission("526ca6193-4149-456b-ae00-00fdad2437c6", creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("validate permissions by apikey, for partner accessing its sub account - fail") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessPermission("2226ca6193-4149-456b-ae00-00fdad2437c6a", creator, DashboardUserPermissions.TRANSACTIONS_CREATE.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidPermissions), _ => fail)
    }
  }

  test("validate permissions by invalid apikey - fail") {
    val creator = Creator(userId = 1, accountId = 1)
    whenReady(service.validateAccessPermission("2126ca6193-4149-456b-ae00-00fdad2437c6a", creator, DashboardUserPermissions.TRANSACTIONS_CREATE.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("validate permissions by environment type - success") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessPermission(1, creator, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString)) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("validate permissions by environment type - fail for invalid permission") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidPermissions), _ => fail)
    }
  }

  test("validate permissions by environment type - fail for invalid account id") {
    val creator = Creator(userId = 10, accountId = 140)
    whenReady(service.validateAccessPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(EnvironmentNotFound), _ => fail)
    }
  }

  test("validate permissions by environment type - fail for invalid user id") {
    val creator = Creator(userId = 140, accountId = 14)
    whenReady(service.validateAccessPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidUserAccountAssociation), _ => fail)
    }
  }

  test("validate permissions by environment type with or permissions - success") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessWithOrPermission(1, creator, DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString, None)) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("validate permissions by environment type with or permissions - fail for invalid permission") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.validateAccessWithOrPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString, Some(DashboardUserPermissions.TRANSACTIONS_VIEW.id.toString))) { response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("validate permissions by environment type with or permissions - fail for invalid account id") {
    val creator = Creator(userId = 10, accountId = 140)
    whenReady(service.validateAccessWithOrPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString, None)) { response =>
      response.fold(_ => ErrorResponseFactory.get(EnvironmentNotFound), _ => fail)
    }
  }

  test("validate permissions by environment type with or permissions - fail for invalid user id") {
    val creator = Creator(userId = 140, accountId = 14)
    whenReady(service.validateAccessWithOrPermission(1, creator, DashboardUserPermissions.DOCUMENTATION_VIEW.id.toString, None)) { response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidUserAccountAssociation), _ => fail)
    }
  }

  test("get all sub accounts of parent - V1") {
    whenReady(service.getParentsSubAccounts(1)){ response =>
      response.fold(_ => fail, _ shouldBe Set(2, 3, 19, 20, 21))
    }
  }

  test("get all sub accounts of parent - V2") {
    whenReady(service.getParentsSubAccounts(14)){ response =>
      response.fold(_ => fail, _ shouldBe Set(15, 22, 23, 24))
    }
  }

  test("get all sub accounts of parent - fail") {
    whenReady(service.getParentsSubAccounts(2)){ response =>
      response.fold(_ => ErrorResponseFactory.get(InvalidParentAccount), _ => fail)
    }
  }

  test("get all default modules for accounts") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.getDefaultModulesForAccounts(14, Some(creator),true)){ response =>
      response.fold(_ => fail, modules => {
        modules.modules.nonEmpty shouldBe true
        Set(110,94,99).subsetOf(modules.modules) shouldBe true
      })
    }
  }

  test("get all default modules for account without creator and validation") {
    whenReady(service.getDefaultModulesForAccounts(14, None, false)) { response =>
      response.fold(_ => fail, modules => {
        modules.modules.nonEmpty shouldBe true
        Set(110, 94, 99).subsetOf(modules.modules) shouldBe true
      })
    }
  }

  test("get all default modules") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.getDefaultModules(14, creator)) { response =>
      response.fold(_ => fail, modules => {
        modules.nonEmpty shouldBe true
        Set(110, 94, 99).subsetOf(modules) shouldBe true
      })
    }
  }

  test("get all default modules should fail, when account is not V2 migrated") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.getDefaultModules(1, creator)){ response =>
      response.fold(_ => ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("get account modules") {
    whenReady(service.getAccountModules(14, 14)){ response =>
      response.fold(_ => fail, modules => {
        modules.nonEmpty shouldBe true
        Set("rff-Ffj0fL0vj3", "rid-FV2XxwgfUH").subsetOf(modules) shouldBe true
      })
    }
  }

  test("get account modules should fail, when account is not V2 migrated") {
    whenReady(service.getAccountModules(1, 14)){ response =>
      response.fold(_ => ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("update valid modules") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.saveDefaultModules(14, Set(110, 33, 99), creator)){ response =>
      response._2.fold(_ => fail, _ shouldBe true)
    }
  }

  test("should create subaccount V2 with min details with user propagation only for system defined roles"){
    Mockito.reset(mailNotificationService)
    val accountName = "AccountV2WithPrimaryAdminAndUserPropogate"
    val subAccount = UserFixture.subAccount.copy(companyname = accountName, administer = Some(true), modules = Some(Set(1, 2)), propogateUsers = Some(true))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    val result = service.createSubAcountWithMinDetails(14L, subAccount, userId = Some(10L), accountId = Some(14L), primaryUserIdOpt = Some(10L))
    whenReady(result){ res =>
      res.fold(_ => fail, k => {k._1.name shouldBe "TestAccountV2"})
      whenReady(service.listSubAccountsV2(14L, Creator(10L, 14L), None, None)) { subaccounts =>
        subaccounts.fold(_ => fail,subaccountOpt => subaccountOpt.map { subAccount =>
          if(subAccount.name == accountName) {
            whenReady(userAccountAssociationService.getUserAccountAssociation(10L, subAccount.id)) { uaa =>
              uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>

                uaar.userRoles.isEmpty shouldBe false
                uaar.isPrimaryUser shouldBe true
                whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
                  pts.fold(_ => fail, ptsms => {
                    ptsms.map { ptsm =>
                      whenReady(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(ptsm.id, uaar.userId, uaar.accountId)) { pstmOpt =>
                        pstmOpt.fold(_ => fail, { a =>

                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id))
                          a.filter(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                        })
                      }
                    }
                  })
                }
              })
            }
            whenReady(userService.listAllUsers(14L, Option(Creator(10L, 14L)))){ users =>
              users.fold(_ => fail, _.size should be (2))
            }
            whenReady(userService.listAllUsers(subAccount.id,Option(Creator(10L, 14L)))){ users =>
              users.fold(_ => fail, _.size should be (1))
            }
          }

        })
      }
      verify(mailNotificationService).sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq(subAccount.companyname), MMatchers.eq("<EMAIL>"))
    }
  }



  test("update empty modules for accounts") {
    val creator = Creator(userId = 10, accountId = 18)
    whenReady(service.saveDefaultModulesForAccounts(Seq(18), Set.empty[Int], false, creator)){ response =>
      response._2.fold(_ => fail, res => {
        res shouldBe true
        whenReady(service.getDefaultModulesForAccounts(18,  Some(creator),true)) { response0 =>
          response0.fold(_ => fail, _.modules.isEmpty shouldBe true)
        }
      })
    }
  }

  test("update empty modules") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.saveDefaultModules(14, Set.empty[Int], creator)) { response =>
      response._2.fold(_ => fail, res => {
        res shouldBe true
        whenReady(service.getDefaultModules(14, creator)) { response0 =>
          response0.fold(_ => fail, _.isEmpty shouldBe true)
        }
      })
    }
  }

  test("fail into update valid modules") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.saveDefaultModules(14, Set(3,5, 62, 65, 77), creator)){ response =>
      response._2.fold(_ => ErrorResponseFactory.get(InvalidModules), _ => fail)
    }
  }

  test("fail into clear modules for invalid account") {
    val creator = Creator(userId = 10, accountId = 14)
    whenReady(service.clearDefaultModules(10, creator)){ response =>
      response.fold(_ => ErrorResponseFactory.get(AccessForbidden), _ => fail)
    }
  }

  test("clear default modules") {
    val creator = Creator(userId = 11, accountId = 16)
    whenReady(service.clearDefaultModules(17, creator)){ response =>
      response.fold(_ => fail, _ shouldBe true)
    }
  }

  test("save API audit info - success") {
    val createdBy = UUID.randomUUID().toString
    val apiAudit = ApiAudit(
      accountId = 1,
      source = RequestSources.API.id,
      action = RequestActions.SUB_ACCOUNT_CREATION.id,
      created_by = createdBy,
      created_at = DateTime.now(),
      request =
        """
          |{
          |    "companyname": "test",
          |    "industry": "0"
          |}
          |""".stripMargin,
      response =
        """{
          |    "status": "ok",
          |    "data": true
          |}""".stripMargin,
      responseCode = 200,
      processingTime = 50
    )
    whenReady(service.saveApiAudit(apiAudit = apiAudit)) { response =>
      response shouldBe Right(true)
    }
  }

  test("save API audit info should fail for invalid request source") {
    val createdBy = UUID.randomUUID().toString
    val apiAudit = ApiAudit(
      accountId = 1,
      source = 100,
      action = RequestActions.SUB_ACCOUNT_CREATION.id,
      created_by = createdBy,
      created_at = DateTime.now(),
      request =
        """
          |{
          |    "companyname": "test",
          |    "industry": "0"
          |}
          |""".stripMargin,
      response =
        """{
          |    "status": "ok",
          |    "data": true
          |}""".stripMargin,
      responseCode = 200,
      processingTime = 50
    )
    whenReady(service.saveApiAudit(apiAudit = apiAudit)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRequestSource))
    }
  }

  test("save API audit info should fail for invalid request action") {
    val createdBy = UUID.randomUUID().toString
    val apiAudit = ApiAudit(
      accountId = 1,
      source = RequestSources.API.id,
      action = 100,
      created_by = createdBy,
      created_at = DateTime.now(),
      request =
        """
          |{
          |    "companyname": "test",
          |    "industry": "0"
          |}
          |""".stripMargin,
      response =
        """{
          |    "status": "ok",
          |    "data": true
          |}""".stripMargin,
      responseCode = 200,
      processingTime = 50
    )
    whenReady(service.saveApiAudit(apiAudit = apiAudit)) { response =>
      response shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRequestAction))
    }
  }

  test("createSubAccountWithMinDetailsWithoutCreator - invalid API key") {
    val subAccountCreationRequest = SubAccountCreationRequest("xxx", "acc_name", "101-205", None, None, None)
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(error => error shouldBe ErrorResponseFactory.get(PublicExceptionCodes.NonPrimaryAccountAPIKey) , _ => fail)
    }
  }

  test("createSubAccountWithMinDetailsWithoutCreator - modules requested for sub account which are not assigned to parent account") {
    val subAccountCreationRequest = SubAccountCreationRequest("2026ca6193-4149-456b-ae00-00fdad2437c6", "acc_name", "101-205", None, None, Some(Set("rid-gnKpF0yue2")))
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(error => error shouldBe ErrorResponseFactory.get(PublicExceptionCodes.ModulesIncorrect), _ => fail)
    }
  }

  test("createSubAccountWithMinDetailsWithoutCreator - email address present but not associated to parent account") {
    val subAccountCreationRequest = SubAccountCreationRequest("2026ca6193-4149-456b-ae00-00fdad2437c6", "acc_name", "101-205", Some(SubAccountUserRequest(email = "<EMAIL>")), None)
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(error => error shouldBe ErrorResponseFactory.get(PublicExceptionCodes.UserNotAssociatedToParentAccount), _ => fail)
    }
  }

  test("createSubAccountWithMinDetailsWithoutCreator - success with existing user") {
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq("acc_name"), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimitCollection(MMatchers.any(classOf[Seq[SaveRateLimitingInput]]), MMatchers.anyBoolean())).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val subAccountCreationRequest = SubAccountCreationRequest("2026ca6193-4149-456b-ae00-00fdad2437c6", "acc_name_1", "101-205", Some(SubAccountUserRequest(email = "<EMAIL>")), None, None)
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(_ => fail, result => result shouldBe true)
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("createSubAccountWithMinDetailsWithoutCreator - success with no user") {
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq("acc_name"), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimitCollection(MMatchers.any(classOf[Seq[SaveRateLimitingInput]]), MMatchers.anyBoolean())).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val subAccountCreationRequest = SubAccountCreationRequest("2026ca6193-4149-456b-ae00-00fdad2437c6", "acc_name_2", "101-205", None, None, None)
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(_ => fail, result => result shouldBe true)
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("createSubAccountWithMinDetailsWithoutCreator - success with new user") {
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient, accountPgpKeysService)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    val activationToken = ArgumentCaptor.forClass(classOf[String])
    Mockito.when(mailNotificationService.sendSubAccountCreatedNotification(MMatchers.eq(14L), MMatchers.eq("TestAccountV2"), AccountId(accountId.capture()).value,  MMatchers.eq("acc_name"), MMatchers.eq("<EMAIL>"))).thenReturn(Future.successful(Right(true)))
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(mailNotificationService.sendSetPasswordEmail(MMatchers.eq("first_name"), MMatchers.eq("last_name"), MMatchers.eq("<EMAIL>"), activationToken.capture(), MMatchers.eq(false), MMatchers.eq(14L))).thenReturn(Future.successful(Right(true)))
    Mockito.when(rateLimitingService.saveRateLimitCollection(MMatchers.any(classOf[Seq[SaveRateLimitingInput]]), MMatchers.anyBoolean())).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(accountPgpKeysService.createPgpKeys(MMatchers.any(classOf[Long]), MMatchers.eq(Some(pgpKeyExpiryDuration)))).thenReturn(Future.successful(Right(true)))
    val subAccountCreationRequest = SubAccountCreationRequest("2026ca6193-4149-456b-ae00-00fdad2437c6", "acc_name_3", "101-205", Some(SubAccountUserRequest(Some("first_name"), Some("last_name"), "<EMAIL>", Some("contact"))), None, None)
    whenReady(service.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)){ response =>
      response.fold(_ => fail, result => result shouldBe true)
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
    Mockito.verify(accountPgpKeysService, Mockito.times(1)).createPgpKeys(accountId.capture(), MMatchers.eq(Some(pgpKeyExpiryDuration)))
  }

  test("should create subaccount V2"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient)
    val accountName = "Sub1"
    val subAccount = UserFixture.subAccountFormV2.copy(companyname = accountName, administer = Some(true), modules = Some(Set.empty))
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccountV2(14L, subAccount)
    whenReady(result){ res =>
      res.fold(_ => fail, _ shouldBe true)
      whenReady(service.listSubAccountsV2(14L, Creator(10L, 14L), None, None)) { subaccounts =>
        subaccounts.fold(_ => fail,subaccountOpt => subaccountOpt.map { subAccount =>
          if(subAccount.name == accountName) {
            whenReady(userAccountAssociationService.getUserAccountAssociation(10L, subAccount.id)) { uaa =>
              uaa.fold(_ => fail, uaarOpt => uaarOpt.map { uaar =>
                uaar.userRoles.isEmpty shouldBe false
                uaar.isPrimaryUser shouldBe true
                whenReady(permissionTemplateService.getPermissionTemplates(uaar.id)) { pts =>
                  pts.fold(_ => fail, ptsms => {
                    ptsms.map { ptsm =>
                      whenReady(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(ptsm.id, uaar.userId, uaar.accountId)) { pstmOpt =>
                        pstmOpt.fold(_ => fail, { a =>
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                          a.exists(_.environmentTypeId.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id))
                          a.filter(_.environmentTypeId.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id))
                        })
                      }
                    }
                  })
                }
              })
            }
          }
        })
      }
    }
    Mockito.verify(modelManagementClient).copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))
  }

  test("should create subaccount V2 should fail if account name is already present"){
    Mockito.reset(mailNotificationService, encryptionKeysService, rateLimitingService, modelManagementClient)
    val accountName = "AccountName17"
    val email = "<EMAIL>"
    val subAccount = UserFixture.subAccountFormV2.copy(companyname = accountName, administer = Some(true), email = email)
    val accountId = ArgumentCaptor.forClass(classOf[Long])
    Mockito.when(encryptionKeysService.generate(AccountId(accountId.capture()))).thenReturn(Future.successful(true))
    Mockito.when(rateLimitingService.saveRateLimits(MMatchers.any(classOf[SaveRateLimitingInput]))).thenReturn(Future.successful(Right(true)))
    Mockito.when(modelManagementClient.copyAccountModels(MMatchers.any(classOf[String]), MMatchers.any(classOf[String]))).thenReturn(Future.successful(Right(true)))
    val result = service.createSubAccountV2(14L, subAccount)
    whenReady(result) { res =>
      res.fold(f => {
        f.code shouldBe ExceptionCodes.UnknownError.id
        f.message shouldBe ExceptionCodes.UnknownError.description
      }, _ => fail)
    }
    //Mockito.verify(modelManagementClient, Mockito.never).mapDefaultModels(MMatchers.any(classOf[String]))
  }

  test("should fetch all accounts for an user - success") {
    val result = service.getAssociatedAccounts(10)
    whenReady(result) { accounts =>
      accounts.fold(_ => fail, _.size should be (1))
    }
  }

  test("should fetch all accounts for an user - failure") {
    val result = service.getAssociatedAccounts(100)
    whenReady(result) { response =>
      response shouldBe 'left
    }
  }

  test("getApiKeysForAccountByEnvironmentType: should return api keys when accounts exists") {
    val actual = service.getApiKeysForAccountByEnvironmentType(13, 1)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, _.size shouldBe 1)
      response.fold(_ => fail, _.headOption.fold(fail)(_.accountName shouldBe "AccountName13"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentId shouldBe 17))
      response.fold(_ => fail, _.headOption.fold(fail)(_.environmentType shouldBe "Production"))
      response.fold(_ => fail, _.headOption.fold(fail)(_.apiKeys.size shouldBe 1))
    }
  }

}