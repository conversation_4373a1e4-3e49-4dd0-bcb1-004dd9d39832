package industry

import javax.sql.DataSource
import me.socure.account.industries.IndustriesManagementService
import me.socure.account.service.TestConfiguration
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.Industry
import me.socure.storage.slick.dao.DaoIndustries
import me.socure.utils.DBProxyWithMetrics
import slick.jdbc.JdbcBackend

/**
  * Created by sunder<PERSON> on 8/31/16.
  */
class IndustriesManagementServiceTest extends TestConfiguration {
  var service : IndustriesManagementService = _
  override val mysqlService: MysqlService = MysqlService("industries-management-service")
  private def buildService(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoIndustries = new DaoIndustries(dbProxyWithMetrics, slick.driver.MySQLDriver)

    new IndustriesManagementService(daoIndustries)
  }


  override protected def beforeAll(): Unit = {
    mysqlService.start()
    mysqlService.waitForService()
    startMysqlService(dbName)
    val socureDb = buildDataSource(Some(dbName))
    val sqlExecutor = new SQLExecutor(socureDb)

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description, is_deleted) VALUES ('11', 'Agriculture, Forestry, Fishing and Hunting', false)")
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description, is_deleted) VALUES ('22', 'Utilities', false)")
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description, is_deleted) VALUES ('23', 'Construction', false)")
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description, is_deleted) VALUES ('31-33', 'Manufacturing', false)")


    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) " +
      s"VALUES (1, 'AccountName1', '23', false, 1, NULL, false, '${PublicIdGenerator.account().value}','publicApiKey1','externalId1')")

    sqlExecutor.execute("INSERT INTO tbl_business_user (id, activation_code, contact_number, email, first_name, last_name, last_logout_on, last_login_on,  registered_on, account_non_locked, last_locked_on, is_recaptcha_valid, account_id, is_primary_user) VALUES " +
      "(1, 'a6a07d36-769c-43bb-aa29-853e46f61d7a', '**********', '<EMAIL>', 'Sunder', 'Raj', NULL,'2013-09-20', '2015-10-20', 1, NULL, NULL, 1, true)")



    service = buildService(socureDb)
  }



  test("get indistry list") {
    val result = service.getIndustriesList
    whenReady(result){ res =>
      res.right.value.size should be (51)
      res.right.value.headOption.fold(fail)(_.sector should be ("11"))
      res.right.value.headOption.fold(fail)(_.description should be ("Agriculture, Forestry, Fishing and Hunting"))
    }
  }

  test("should insert industry") {
    val newIndustry = Industry("new_sector", "new_description")
    val result = service.upsertIndustry(newIndustry)
    whenReady(result) { res =>
      res.right.value should be (true)

      //List Industries to make sure new industry is added.

      whenReady(service.getIndustriesList) { r =>
        r.right.value.contains(newIndustry) should be (true)
      }
    }
  }

  test("should return account for given industry"){
    val result = service.getAccountByIndustry("23")
    whenReady(result) { res =>
      res.right.value.nonEmpty shouldBe true
    }
  }

  test("should NOT delete industry when account associated") {

    val result = service.deleteIndustry("23")
    whenReady(result) { res =>
      res.left.value.code should be (AccountsAssociatedWithIndustry.id)

      //List Industries to make sure new industry is added.

      whenReady(service.getIndustriesList) { r =>
        r.right.value.contains(Industry("23","Construction")) should be (true)
      }
    }
  }

  test("should return account not found for given industry") {
    val result = service.getAccountByIndustry("dff")
    whenReady(result) { res =>
      res.left.value.code shouldBe ExceptionCodes.AccountNotFound.id
    }
  }

  test("should return industry") {
    whenReady(service.getIndustryBySector("22")) { res =>
      res.right.value.sector should be ("22")
    }
  }

  test("should return industry not found") {
    whenReady(service.getIndustryBySector("500")) { res =>
      res.left.value.code should be (IndustryNotFound.id)
    }
  }

  test("should delete industry") {

    val result = service.deleteIndustry("31-33")
    whenReady(result) { res =>
      res.right.value should be (true)
      whenReady(service.getIndustriesList) { r =>
        r.right.value.contains(Industry("31-33","Manufacturing")) should be (false)
      }
    }
  }

  override def afterAll(): Unit = {
    super.afterAll()
		dataSources.foreach(_.close())
    mysqlService.stop
  }
}
