package me.socure.account.ccm

import java.nio.charset.StandardCharsets
import java.util.Base64
import javax.crypto.Cipher
import javax.crypto.spec.SecretKeySpec

/**
 * <AUTHOR> Kumar
 */
class AuditRequestEncipher(key: String) {

  private val secret = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), 0, 5, "RC4")

  def encrypt(bytes: Array[Byte]): Array[Byte] = {
    val cipher = Cipher.getInstance("RC4")
    cipher.init(Cipher.ENCRYPT_MODE, secret)
    cipher.doFinal(bytes)
  }

  def encrypt(request: String): String = {
    val encryptedBytes = encrypt(request.getBytes(StandardCharsets.UTF_8))
    Base64.getEncoder.withoutPadding().encodeToString(encryptedBytes)
  }

  def decrypt(bytes: Array[Byte]): Array[Byte] = {
    val cipher = Cipher.getInstance("RC4")
    cipher.init(Cipher.DECRYPT_MODE, secret)
    cipher.doFinal(bytes)
  }

  def decrypt(encrypted: String): String = {
    val base64DecodedBytes = Base64.getDecoder.decode(encrypted)
    val decryptedBytes = decrypt(base64DecodedBytes)
    new String(decryptedBytes)
  }
}
