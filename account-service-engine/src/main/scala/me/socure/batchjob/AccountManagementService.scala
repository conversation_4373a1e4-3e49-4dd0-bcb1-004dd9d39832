package me.socure.batchjob

import me.socure.account.service.BusinessUserRoleService
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountManagementService(businessUserRoleService: BusinessUserRoleService)(implicit ec: ExecutionContext) {
    val logger: Logger = LoggerFactory.getLogger(getClass)

    def updateAccountPermissions(updateRequest: List[AccountPermissionUpdateRequest]): Future[List[AccountPermissionUpdateResponse]] = {
        val resultFuture = updateRequest.map{ accountPermissionUpdateRequest =>
            businessUserRoleService.switchPermissions(accountPermissionUpdateRequest.accountId,
                accountPermissionUpdateRequest.permissions,
                accountPermissionUpdateRequest.switch) map {
                case Right(_)  =>
                    AccountPermissionUpdateResponse(accountPermissionUpdateRequest.accountId,
                        accountPermissionUpdateRequest.permissions,
                        accountPermissionUpdateRequest.switch,
                        UpdateStatus.Success.toString,
                        ""
                        )
                case Left(e) =>
                    AccountPermissionUpdateResponse(accountPermissionUpdateRequest.accountId,
                        accountPermissionUpdateRequest.permissions,
                        accountPermissionUpdateRequest.switch,
                        UpdateStatus.Failure.toString,
                        e.message
                    )
            }
        }
        Future.sequence(resultFuture)
    }
}
