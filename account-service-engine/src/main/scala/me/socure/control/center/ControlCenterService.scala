package me.socure.control.center

import me.socure.control.center.client.ControlCenterServiceClient
import me.socure.control.center.model.ControlCenterSettings
import me.socure.model.ErrorResponse

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sumit<PERSON> on 28/06/21
 **/
class ControlCenterService(controlCenterServiceClient: ControlCenterServiceClient)
                          (implicit val executionContext: ExecutionContext){

  def updateSetting(settingName: String, updatedValue: Boolean): Future[Either[ErrorResponse, ControlCenterSettings]] = {
    controlCenterServiceClient.fetchSettings() flatMap {
      case Right(controlCenterSettings) =>
        val matchedSettingRecordOpt = controlCenterSettings.toggleableSettings.find(record => record.name.equals(settingName)).headOption
        matchedSettingRecordOpt match {
          case Some(matchedRecord) =>
            val filteredSettings = controlCenterSettings.toggleableSettings.filterNot(record => record.name.equals(matchedRecord.name))
            val updatedSettings = filteredSettings :+ matchedRecord.copy(value = updatedValue)
            controlCenterServiceClient.updateSettings(ControlCenterSettings(toggleableSettings = updatedSettings))
          case None =>
            Future.successful(Left(ErrorResponse(101, "Invalid Setting Provided")))
        }
      case Left(error) =>
        Future.successful(Left(error))
    }
  }



}
