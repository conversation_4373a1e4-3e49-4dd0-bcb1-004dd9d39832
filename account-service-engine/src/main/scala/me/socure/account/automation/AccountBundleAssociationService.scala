package me.socure.account.automation

import me.socure.DaoAccount
import me.socure.account.service.AccountHierarchyService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountBundleAssociation, DtoAccountBundleAssociation}
import me.socure.storage.slick.dao.DaoAccountBundleAssociation
import me.socure.storage.slick.tables.account.automation.mapper.AccountBundleAssociationMapper
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountBundleAssociationService(daoAccountBundleAssociation: DaoAccountBundleAssociation,
                                      daoAccount: DaoAccount,
                                      accountHierarchyService: AccountHierarchyService,
                                      clock: Clock )(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getAccountBundleAssociation(accountId: Long): Future[Either[ErrorResponse, DtoAccountBundleAssociation]] = {
    accountHierarchyService.getRootParent(accountId) flatMap {
      rootAccountId =>
        daoAccountBundleAssociation.getAccountBundleAssociation(rootAccountId) map {
          case dtoAccountBundleAssociations: Seq[DtoAccountBundleAssociation] if dtoAccountBundleAssociations.nonEmpty =>
            Right(dtoAccountBundleAssociations.head)
          case _ =>
            logger.info(s"Account Bundle Association not found for Account $accountId")
            Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotFound))
        }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Bundle Association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotFound))
    }
  }

  def saveAccountBundleAssociation(accountBundleAssociation: AccountBundleAssociation): Future[Either[ErrorResponse, DtoAccountBundleAssociation]] = {
    daoAccount.getAccount(accountBundleAssociation.accountId) flatMap {
      case Some(_) =>
        val dtoAccountBundleAssociation = AccountBundleAssociationMapper.toDtoAccountBundleAssociation(accountBundleAssociation.accountId, accountBundleAssociation.bundleReference, accountBundleAssociation.initiatedBy, clock.now)
        daoAccountBundleAssociation.saveAccountBundleAssociation(dtoAccountBundleAssociation) map {
          case dtoAccountBundleAssociation: DtoAccountBundleAssociation =>
            Right(dtoAccountBundleAssociation)
          case _ =>
            logger.info(s"Error occurred while saving Account Bundle Association")
            Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotSaved))
        }
      case _ =>
        logger.info(s"Account with Id: ${accountBundleAssociation.accountId}, not found, account bundle association not saved")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while saving Account Bundle Association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotSaved))
    }
  }

  def upsertAccountBundleAssociation(accountBundleAssociation: AccountBundleAssociation): Future[Either[ErrorResponse, DtoAccountBundleAssociation]] = {
      daoAccountBundleAssociation.getAccountBundleAssociation(accountBundleAssociation.accountId) flatMap {
        case accountBundleAssociations: Seq[DtoAccountBundleAssociation] if accountBundleAssociations.nonEmpty =>
          if (accountBundleAssociations.head.bundleReference.equals(accountBundleAssociation.bundleReference)) {
            logger.info(s"No change in Bundle Reference. So not updated")
            Right(accountBundleAssociations.head)
          }
          val dtoAccountBundleAssociation = AccountBundleAssociationMapper.toDtoAccountBundleAssociation(accountBundleAssociations.head, accountBundleAssociation.bundleReference, accountBundleAssociation.initiatedBy, clock.now)
          daoAccountBundleAssociation.updateAccountBundleAssociation(dtoAccountBundleAssociation) map {
            case i: Int if i > 0 =>
              Right(dtoAccountBundleAssociation)
            case _ =>
              logger.info(s"Error occurred while updating Account Bundle Association")
              Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotSaved))
          }
        case _ =>
          logger.info(s"AccountBundleAssociation does not exists, inserting the bundle information")
          saveAccountBundleAssociation(accountBundleAssociation)
      } recover {
        case e: Exception =>
          logger.info(s"Error occurred while updating Account Bundle Association", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.AccountBundleAssociationNotSaved))
      }
    }

}
