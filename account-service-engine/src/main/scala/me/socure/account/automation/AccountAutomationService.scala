package me.socure.account.automation

import me.socure.account.audit.AccountAuditService
import me.socure.account.data.retention.AccountDataRetentionScheduleService
import me.socure.account.service._
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountProvisioningNotUpdated, ProductConfigNotUpdated}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.superadmin.ActiveUsersService
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.common.clock.Clock
import me.socure.constants.AccountManagementDefaults.{DefaultBundleForProspectAccounts, DefaultRateLimit, ProspectsDefaultRateLimitDays, ProspectsDefaultRateLimitTPS}
import me.socure.constants._
import me.socure.decision.service.client.DecisionServiceClientV2
import me.socure.decision.service.common.models.v2._
import me.socure.document.manager.client.DocumentManagerClient
import me.socure.document.manager.model.doctype.DtoProvisionAccount
import me.socure.docv.client.DocvOrchestraClient
import me.socure.docv.common.dto.AccountStrategyDto
import me.socure.model._
import me.socure.model.account.RateLimiterPublicAPI.RateLimiterPublicAPI
import me.socure.model.account.automation.{AccountBundleAssociation, AccountProvisioningDetails, ProductConfiguration, UpdateAccountProvisioningDetails}
import me.socure.model.account.data.retention.{RetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.model.account.{RateLimiterPublicAPI, SaveRateLimitingInput}
import me.socure.model.dashboardv2.EnvironmentNameAndId
import me.socure.model.ein.{EINRequest, LookupApiKeyRequest, LookupApiKeyServiceIdRequest}
import me.socure.model.kyc.{KycAddressMatchLogic, KycPreferencesRequest, KycPreferences => KKycPreferences}
import me.socure.model.sai.{SAIPreferences, SAIPreferencesUpsertRequest}
import me.socure.storage.slick.tables.account.audit.DtoAccountAudit
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.annotation.tailrec
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}


class AccountAutomationService(accountBundleAssociationService: AccountBundleAssociationService,
                               productService: ProductService,
                               einService: EINService,
                               saiPreferencesService: SaiPreferencesService,
                               accountAuditService: AccountAuditService,
                               accountDataRetentionScheduleService: AccountDataRetentionScheduleService,
                               bundleManagementService: BundleManagementService,
                               decisionServiceClientV2: DecisionServiceClientV2,
                               accountPgpKeysService: AccountPgpKeysService,
                               activeUsersService: ActiveUsersService,
                               pgpKeyExpiryDuration: Long,
                               documentManagerClient: DocumentManagerClient,
                               accountProvisioningConfiguration: AccountProvisioningConfiguration,
                               rateLimitingService: RateLimitingService,
                               docvOrchestraClient: DocvOrchestraClient,
                               dvConfigurationService: DvConfigurationService,
                               accountPreferencesService: AccountPreferencesService,
                               clock: Clock,
                               scalaCache: ScalaCache[_]) (implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  implicit def jsonFormats: Formats = JsonFormats.formats
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"

  private val decisionLogicSortRequest = DecisionLogicSortRequest(None)
  private val decisionLogicFilterRequest = DecisionLogicFilterRequest("", "", Some(Set(DecisionLogicStates.ACTIVE.id)), None)
  private lazy val defaultDocumentTypePublicIds = documentManagerClient.listDocTypes().map {
    case Right(l) =>
      accountProvisioningConfiguration.documentTypes.flatMap { documentType =>
        l.filter(v => v.name.equalsIgnoreCase(documentType)).map(_.publicId)
      }
    case _ => Set.empty[String]
  }
  private lazy val defaultAuthenticIdStrategy: Future[Option[String]] = getDefaultAuthenticIdStrategy(accountProvisioningConfiguration.authenticIdStrategy)

  def getDefaultAuthenticIdStrategy(strategy: Option[String] = None): Future[Option[String]] = {
    docvOrchestraClient.fetchAllAuthenticIdStrategies().map {
      case Right(l) =>
        l.find(v => v.name.equalsIgnoreCase(strategy.getOrElse(""))).flatMap(_.strategyId)
      case _ => None
    }
  }

  def getAccountProvisioningDetails(accountId: Long): Future[Either[ErrorResponse, AccountProvisioningDetails]] = {
    val result = for {
      products <- productService.getProductsForAccount(accountId)
      accountBundleAssociation <- accountBundleAssociationService.getAccountBundleAssociation(accountId)
      productConfiguration <- if (products.isRight) getProductConfiguration(accountId) else getDefaultProductConfiguration
    } yield (accountBundleAssociation, products, productConfiguration)
    result map {
      case (Right(accountBundleAssociation), Right(products), productConfiguration) =>
        Right(AccountProvisioningDetails(Some(accountBundleAssociation.bundleReference), products, productConfiguration))
      case (Left(_), Right(products), productConfiguration) =>
        Right(AccountProvisioningDetails(bundleReference = None, products = products, productConfiguration = productConfiguration))
      case (_, Left(error), _) =>
        Left(error)
    }
  }

  def getDefaultProductConfiguration: Future[ProductConfiguration] = {
    Future.successful(ProductConfiguration(None, None, None, None))
  }

  def getProductConfiguration(accountId: Long): Future[ProductConfiguration] = {
    val result = for {
      ein <- einService.fetchEIN(accountId).map {
        case Right(Some(e)) => Some(e.number)
        case _ => None
      }
      saiPreferencesOpt <- saiPreferencesService.fetchSaiPreferences(accountId).map {
        case Right(saiPreferences: Option[SAIPreferences]) => saiPreferences
        case _ => None
      }
      retentionSchedule <- accountDataRetentionScheduleService.getAccountDataRetentionSchedule(accountId).map {
        case Right(adrs) => Some(RetentionSchedule(adrs.cadence, adrs.routine))
        case _ => None
      }
      lookupApiKey <- einService.fetchLookupKey(accountId).map {
        case Right(Some(e)) => Some(e.value)
        case _ => None
      }
      (apiKey: Option[String], serviceId: Option[String]) <- einService.fetchLookupKeyServiceSid(accountId).map {
        case Right(Some(otpWorkflowInfo)) =>
          (Some(otpWorkflowInfo.apiKey), Some(otpWorkflowInfo.serviceId))
        case _ =>
          (None, None)
      }
    } yield (ein, retentionSchedule, saiPreferencesOpt, lookupApiKey, apiKey, serviceId)
    result map (e => ProductConfiguration(ein = e._1,
      retentionSchedule = e._2,
      saiPreferences = e._3,
      lookupApiKey = e._4.orElse(e._5),
      serviceId = e._6
    ))
  }

  def updateProductConfiguration(accountId: Long,
                                 products: Seq[UpdateProduct],
                                 productConfiguration: ProductConfiguration,
                                 initiatedBy: String): Future[Boolean] = {
    def updateEIN(): Future[Boolean] = {
      einService.upsertEIN(EINRequest(accountId, productConfiguration.ein.get)).map {
        case Right(_) => true
        case _ => false
      }
    }

    def updateLookupApiKey(): Future[Boolean] = {
      einService.upsertLookupApiKey(LookupApiKeyRequest(accountId, productConfiguration.lookupApiKey.get)).map {
        case Right(_) => true
        case _ => false
      }
    }

    def updateLookupApiKeyAndServiceSid(): Future[Boolean] = {
      einService.upsertLookupApiKeyAndServiceSid(LookupApiKeyServiceIdRequest(accountId, productConfiguration.lookupApiKey.get, productConfiguration.serviceId.get)).map {
        case Right(_) => true
        case _ => false
      }
    }

    def updateSaiPreferences(): Future[Boolean] = {
      val saiPreferencesToUpdate = productConfiguration.saiPreferences.get
      saiPreferencesService
          .saveSaiPreferences(
            SAIPreferencesUpsertRequest(
              accountId = accountId.toString,
              memo = saiPreferencesToUpdate.memo,
              depositorName = saiPreferencesToUpdate.depositorName,
              physicalAddress = saiPreferencesToUpdate.physicalAddress,
              city = saiPreferencesToUpdate.city,
              state = saiPreferencesToUpdate.state,
              zip = saiPreferencesToUpdate.zip,
              country = saiPreferencesToUpdate.country,
              initiatedBy = Some(initiatedBy)
              )
            ).map {
            case Right(_) => true
            case _ => false
          }
    }

    def updateRetentionSchedule(): Future[Boolean] = {
      val retentionScheduleData = productConfiguration.retentionSchedule.get
      accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(accountId,
        UpdateAccountDataRetentionSchedule(retentionScheduleData.cadence, retentionScheduleData.routine, initiatedBy)) map {
        case Right(_) => true
        case _ => false
      }
    }

    def isUpdateNeeded(existingProducts: Seq[AccountProducts], businessUserRoleId: Int ): Boolean = {
      val existingProductOption = existingProducts.find(product => product.businessUserRoleId == businessUserRoleId)
      val requestOption = if(existingProductOption.isEmpty) None else products.find(_.id == existingProductOption.get.id)

      (existingProductOption, requestOption) match {
        case (Some(existingProduct), _) if existingProduct.provisioned => false // already enabled
        case (_, Some(requestProduct)) if requestProduct.enabled => true // enable now
        case (_, _) => false
      }
    }

    def isDefaultMappingAssociationNeeded(existingProducts: Seq[AccountProducts]): Boolean = {
      val enforceSegmentationOpt = existingProducts.find(_.businessUserRoleId == BusinessUserRoles.EnforceSegmentation.id)
      val enableMultipleLiveLogicsOpt = existingProducts.find(_.businessUserRoleId == BusinessUserRoles.EnableMultipleLiveLogics.id)

      val requestEnforceSegmentationOpt = enforceSegmentationOpt.flatMap(ep => products.find(_.id == ep.id))
      val requestEnableMultipleLiveLogicsOpt = enableMultipleLiveLogicsOpt.flatMap(ep => products.find(_.id == ep.id))

      (enforceSegmentationOpt, enableMultipleLiveLogicsOpt, requestEnforceSegmentationOpt, requestEnableMultipleLiveLogicsOpt) match {
        case (Some(enforceSegmentation), Some(enableMultipleLiveLogics), _, _)
          if enforceSegmentation.enabled && enableMultipleLiveLogics.enabled => false

        case (_, _, Some(requestEnforceSegmentation), Some(requestEnableMultipleLiveLogics))
          if requestEnforceSegmentation.enabled && requestEnableMultipleLiveLogics.enabled => true

        case (Some(enforceSegmentation), _, _, Some(requestEnableMultipleLiveLogics))
          if enforceSegmentation.enabled && requestEnableMultipleLiveLogics.enabled => true

        case (_, Some(enableMultipleLiveLogics), Some(requestEnforceSegmentation), _)
          if requestEnforceSegmentation.enabled && enableMultipleLiveLogics.enabled => true

        case _ => false
      }
    }

    val existingProductsFuture = productService.getProductsForAccount(accountId) map {
      case Right(products) => products
      case Left(_) => Seq.empty[AccountProducts]
    }

    existingProductsFuture flatMap { existingProducts => {
      val decisionLogicCloneNeeded =  isUpdateNeeded(existingProducts, BusinessUserRoles.DECISION_SERVICE.id)
      val kycUpdateNeeded =  isUpdateNeeded(existingProducts, BusinessUserRoles.KYC.id)
      val docVUpdateNeeded =  isUpdateNeeded(existingProducts, BusinessUserRoles.DOCUMENT_VERIFICATION.id)
      val authenticIdStrategyUpdatedNeeded =  isUpdateNeeded(existingProducts, BusinessUserRoles.EnableDocvOrchestra.id)
      val isMultiModel =  isUpdateNeeded(existingProducts, BusinessUserRoles.EnableMultiModelDecisioning.id)
//      insert PGP keys if file upload comes as enabled from the request
      val existingFileUploadProductOption = existingProducts.find(product => product.businessUserRoleId == BusinessUserRoles.CUSTOMER_FILE_UPLOAD.id)
      val requestFileUploadProductOption = if(existingFileUploadProductOption.isEmpty) None else products.find(_.id == existingFileUploadProductOption.get.id)
      val defaultMappingAssociationNeeded = isDefaultMappingAssociationNeeded(existingProducts)

      val insertPgpNeeded = requestFileUploadProductOption match {
        case Some(fileUploadProduct) if fileUploadProduct.enabled => true
        case _ => false
      }

      def associateDefaultEventMappings() = {
        if(defaultMappingAssociationNeeded) {
          logger.info(s"Associating the default Event Mappings for Account $accountId")
          decisionServiceClientV2.associateDefaultMappings(accountId)
        } else {
          Future.successful(true)
        }
      }

      def updateIfEligible[T](
                               configValue: Option[T],
                               roleId: Int,
                               logMessage: String,
                               updateFunction: () => Future[Boolean]
                             ): Future[Boolean] = {
        configValue match {
          case Some(value) =>
            existingProducts.find(_.businessUserRoleId == roleId) match {
              case Some(existingProduct) =>
                logger.info(s"$logMessage: $value for account: $accountId")
                val isExistingEnabled = existingProduct.provisioned && existingProduct.enabled
                val updatedProduct = products.find(_.id == existingProduct.id)
                val canUpdate = updatedProduct.exists(p => p.provisioned && p.enabled) || isExistingEnabled
                if (canUpdate) updateFunction() else Future.successful(true)
              case None =>
                logger.info(s"No existing product found for roleId: $roleId")
                Future.successful(true)
            }
          case None => Future.successful(true)
        }
      }

      def updateIfEligibleRoleIdsList[T](
                               configValue: Option[T],
                               roleIds: Seq[Int],
                               logMessage: String,
                               updateFunction: () => Future[Boolean]
                             ): Future[Boolean] = {
        configValue match {
          case Some(value) =>
            val filteredExistingProducts = existingProducts.filter(p => roleIds.contains(p.businessUserRoleId))
            if (filteredExistingProducts.isEmpty) {
              logger.info(s"No existing product found for the roleIds: $roleIds")
              Future.successful(true)
            } else {
              val canUpdate = filteredExistingProducts.exists(product => {
                logger.info(s"$logMessage: $value for account: $accountId")
                val isExistingEnabled = product.provisioned && product.enabled
                products.find(_.id == product.id).exists(p => p.provisioned && p.enabled) || isExistingEnabled
              })
              if (canUpdate) updateFunction() else Future.successful(true)
            }
          case None => Future.successful(true)
        }
      }

      def ein(): Future[Boolean] =
        updateIfEligible(productConfiguration.ein, BusinessUserRoles.ECBSV.id, "Updating EIN", updateEIN)

      def lookupApiKey(): Future[Boolean] =
        updateIfEligible(productConfiguration.lookupApiKey, BusinessUserRoles.SimSwapLookup.id, "Updating LookupApiKey", updateLookupApiKey)

      def lookupApiKeyAndServiceSid(): Future[Boolean] = {
        updateIfEligibleRoleIdsList((productConfiguration.lookupApiKey, productConfiguration.serviceId) match {
          case (Some(ak), Some(sid)) => Some(s"$ak, $sid")
          case _ => None
        }, Seq(BusinessUserRoles.MFAOrchestration.id, BusinessUserRoles.SilentNetworkAuthentication.id), "Updating LookupApiKeyAndServiceSid", updateLookupApiKeyAndServiceSid)
      }

      def saiPreferences(): Future[Boolean] =
        updateIfEligible(productConfiguration.saiPreferences, BusinessUserRoles.EnableSaiPennyDrop.id, "Updating SaiPreferences", updateSaiPreferences)

      def retentionSchedule(): Future[Boolean] =
        updateIfEligible(productConfiguration.retentionSchedule, BusinessUserRoles.PIIRetentionSchedule.id, "Updating retentionSchedule", updateRetentionSchedule)

      def decisionClone() = {
        if (decisionLogicCloneNeeded) {

          val decisionCloneFuture = Future.sequence(EnvironmentConstants.values.map(envId => {

            logger.info(s"Calling decision service to fetch existing live logics for account $accountId to environment $envId")

            decisionServiceClientV2.listLogicGroupedByPublicId(ListLogicRequest(decisionLogicFilterRequest.copy(accountId = accountId.toString, environmentTypeId = envId.id.toString), decisionLogicSortRequest)) flatMap {
              case Right(details) if details.total == 0 =>

                logger.info(s"Calling decision service to clone best logic for account $accountId to environment $envId")
                val defaultLogic = isMultiModel match {
                  case true => DecisionConfiguration(9, accountProvisioningConfiguration.decisionConfiguration.modelName, accountProvisioningConfiguration.decisionConfiguration.modelVersion)
                  case _ => accountProvisioningConfiguration.decisionConfiguration
                }
                val bestLogicCloneRequest = BestLogicCloneRequest(defaultLogic.logicId, accountId, envId.id, defaultLogic.modelName,
                  defaultLogic.modelVersion, initiatedBy, None, Some(true), isMultiModelEnabled = isMultiModel)
                decisionServiceClientV2.cloneRecommendedLogic(bestLogicCloneRequest)

              case Right(_) => Future.successful(Right(true)) // live logic already exists
              case Left(errorResponse) => Future.successful(Left(errorResponse))
            }
          }))

          decisionCloneFuture map (responses => {
            val leftResponses = responses.foldLeft(Seq.empty[ErrorResponse]) { case (errors, item) =>
              item match {
                case Left(errorResponse: ErrorResponse) => errors ++ Seq(errorResponse)
                case Right(_) => errors
              }
            }
            if (leftResponses.nonEmpty) {
              val errors = leftResponses.map(error => error.message).toSet.mkString("\n")
              logger.error(s"Error while cloning recommended decision logic for $accountId" + errors)
              false
            } else {
              true
            }
          })
        } else {
          Future.successful(true)
        }
      }

      def documentTypes(publicAccountId: Option[String]) = {
        if(docVUpdateNeeded && publicAccountId.isDefined) {
          defaultDocumentTypePublicIds.map { defaultPublicIds =>
            defaultPublicIds.map { defaultPublicId =>
              logger.info(s"Add document type $defaultPublicId to ${publicAccountId.get}")
              documentManagerClient.provisionDocumentType(DtoProvisionAccount(defaultPublicId, publicAccountId.get)) map {
                case Right(_) => true
                case Left(_) =>
                  logger.info(s"Failed to Add document type $defaultPublicId to ${publicAccountId.get}")
                  false
              }
            }
          }.flatMap { response0 =>
            Future.sequence(response0).map(!_.contains(false))
          }
        } else {
          Future.successful(true)
        }
      }

      def dvConfig(environments: Seq[EnvironmentNameAndId], publicAccountId: Option[String]) = {
        if(docVUpdateNeeded) {
          Future.sequence(environments.map { e =>
            accountProvisioningConfiguration.dvConfigurations.map { dvConfig =>
              dvConfigurationService.saveEnvironmentDvConfiguration(dvConfig.toSeq, e.id)  map {
                case Right(_) => true
                case Left(_) =>
                  logger.info(s"Failed to Add DV Config ${dvConfig.map(_.configId).toString} to ${publicAccountId.get}")
                  false
              }
            }.getOrElse(Future.successful(false))
          }).map(!_.contains(false))
        } else {
          Future.successful(true)
        }
      }

      def authenticIdStrategy(publicAccountId: Option[String]) = {
        if(authenticIdStrategyUpdatedNeeded && publicAccountId.isDefined) {
          val docVStrategy = if(productConfiguration.docVStrategy.isDefined) getDefaultAuthenticIdStrategy(productConfiguration.docVStrategy) else defaultAuthenticIdStrategy
          docVStrategy.flatMap{ strategyId =>
            if(strategyId.isDefined){
              docvOrchestraClient.associateAuthenticIdStrategy(AccountStrategyDto(publicAccountId.get, strategyId.get)) map {
                case Right(_) => true
                case Left(_) =>
                  logger.info(s"Failed to Add document type ${strategyId.get} to ${publicAccountId.get}")
                  false
              }
            } else {
              Future.successful(true)
            }
          }
        } else {
          Future.successful(true)
        }
      }

      def kyc() = {
        if(kycUpdateNeeded && accountProvisioningConfiguration.kycConfiguration.isDefined) {
          val kycConfig = accountProvisioningConfiguration.kycConfiguration.get
          accountPreferencesService.saveKycPreference(KycPreferencesRequest(
                                        KKycPreferences(Some(kycConfig.exactDob), Some(kycConfig.dobMatchLogic), kycConfig.ssnExactMatch, addressMatchLogic = kycConfig.addressMatchLogic.map(KycAddressMatchLogic.byName)),
                                        Seq(EnvironmentTypes.PRODUCTION_ENVIRONMENT.id,EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id,EnvironmentTypes.SANDBOX_ENVIRONMENT.id),
                                        Seq(accountId),
                                        isForceInherit = None
                                        )) map {
                case Right(_) => true
                case Left(_) =>
                  logger.info(s"Failed to update KYC Preferences to $accountId")
                  false
              }
            } else {
          Future.successful(true)
        }
      }

      val proceedWithProductConfigUpdate = List(
        productConfiguration.ein,
        productConfiguration.saiPreferences,
        productConfiguration.retentionSchedule,
        productConfiguration.lookupApiKey,
        productConfiguration.serviceId
      ).exists(_.isDefined) || List(decisionLogicCloneNeeded, insertPgpNeeded, docVUpdateNeeded, kycUpdateNeeded, defaultMappingAssociationNeeded).contains(true)

      if (!proceedWithProductConfigUpdate) Future.successful(true)
      else {
        for {
          publicAccountId <- activeUsersService.getPublicAccountId(accountId)
          environments <- activeUsersService.getEnvironments(accountId)
          einInsertFuture <- ein()
          lookupKeyFuture <- lookupApiKey()
          lookupApiKeyAndServiceSidFuture <- lookupApiKeyAndServiceSid()
          saiPreferencesFuture <- saiPreferences()
          retentionScheduleInsertFuture <- retentionSchedule()
          decCloneFuture <- decisionClone()
          pgpInsertFuture <- if(insertPgpNeeded) {
            accountPgpKeysService.createPgpKeys(accountId, Some(pgpKeyExpiryDuration)) map (_ => true) // this function add pgp keys if active pgp key doesn't exist for the account
          } else {
            Future.successful(true)
          }
          kycInsertFuture <- kyc()
          dvConfigInsertFuture <- dvConfig(environments, publicAccountId)
          authenticIdStrategyInsertFuture <- authenticIdStrategy(publicAccountId)
          documentTypesInsertFuture <- documentTypes(publicAccountId)
          associateDefaultEventMappingsFuture <- associateDefaultEventMappings()
        } yield !Seq(lookupKeyFuture, lookupApiKeyAndServiceSidFuture, einInsertFuture, saiPreferencesFuture, retentionScheduleInsertFuture, decCloneFuture, pgpInsertFuture, documentTypesInsertFuture, dvConfigInsertFuture, authenticIdStrategyInsertFuture, kycInsertFuture, associateDefaultEventMappingsFuture).contains(false)
      }
    }}
  }

  def saveAccountAutomation(accountId: Long,
                            updateAccountProvisioningDetails: UpdateAccountProvisioningDetails,
                            client: String = "SuperAdmin",
                            isInternal: Boolean = false,
                            ignoreDecision: Boolean = false): Future[Either[ErrorResponse, Boolean]] = {
    val startTime = clock.now
    def saveAudit(response: String, status: String): Future[Either[ErrorResponse, DtoAccountAudit]] = {
      accountAuditService.saveAccountAudit(accountId = accountId,
        client = client,
        component = "Account Automation",
        componentReference = "0",
        payload = Serialization.write(updateAccountProvisioningDetails),
        response = response,
        status = status,
        processingTime = clock.now.getMillis - startTime.getMillis,
        createdBy = updateAccountProvisioningDetails.initiatedBy)
    }

    def updateBundle(): Future[Either[ErrorResponse, Unit]] = {
      if(updateAccountProvisioningDetails.bundleReference.trim.isEmpty){
        Future.successful(Right())
      } else {
        accountBundleAssociationService.upsertAccountBundleAssociation(
          AccountBundleAssociation(accountId,
            updateAccountProvisioningDetails.bundleReference,
            updateAccountProvisioningDetails.initiatedBy)) map {
          case Right(_) => Right()
          case Left(e) => Left(e)
        }
      }
    }

    def addDefaultDomains(): Future[Either[ErrorResponse, Unit]] = {
      if(isInternal)
        activeUsersService.addDefaultDomains(accountId)
      else
        Future.successful(Right())
    }

    def addDecisionDefaultLogic(): Future[Either[ErrorResponse, Unit]] = {
      if (!ignoreDecision && updateAccountProvisioningDetails.products.nonEmpty) {
        productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id) flatMap {
          case products if products.nonEmpty =>
            val docVFlagIDs = products.filter(product => product.businessUserRoleId == BusinessUserRoles.EnableDecisionForCustomersWithDocV.id).map(_.id)
            val useDocVBaseLine = updateAccountProvisioningDetails.products.find(conf => conf.enabled && docVFlagIDs.contains(conf.id))
            val isDocVDeprovisioned = updateAccountProvisioningDetails.products.find(conf => !conf.enabled &&  docVFlagIDs.contains(conf.id))
            val isMultiModelFlags = products.filter(product => product.businessUserRoleId == BusinessUserRoles.EnableMultiModelDecisioning.id).map(_.id)
            val isMultiModel = updateAccountProvisioningDetails.products.find(conf => conf.enabled && isMultiModelFlags.contains(conf.id)) match {
              case Some(_) => true
              case _ => false
            }

            val migrateLogics = (useDocVBaseLine, isDocVDeprovisioned) match {
              case (Some(_), _) => true
              case (_, Some(_)) => true
              case (None, None) => false
            }
            if(migrateLogics){
              decisionServiceClientV2.migrateLogics(accountId) flatMap {
                case Right(_) =>
                  val decisionCloneFuture = Future.sequence(EnvironmentConstants.values.map(envId => {
                    val decConf = (useDocVBaseLine, isDocVDeprovisioned) match {
                      case (Some(_), _) => DecisionConfiguration(5, "DocV Baseline", "1.0")
                      case (_, Some(_)) => if (isMultiModel) {
                        DecisionConfiguration(7, accountProvisioningConfiguration.decisionConfiguration.modelName, accountProvisioningConfiguration.decisionConfiguration.modelVersion)
                      } else {
                        accountProvisioningConfiguration.decisionConfiguration
                      }
                      case (None, None) => if (isMultiModel) {
                        DecisionConfiguration(9, accountProvisioningConfiguration.decisionConfiguration.modelName, accountProvisioningConfiguration.decisionConfiguration.modelVersion)
                      } else {
                        accountProvisioningConfiguration.decisionConfiguration
                      }
                    }
                    val bestLogicCloneRequest = BestLogicCloneRequest(decConf.logicId, accountId, envId.id, decConf.modelName,
                      decConf.modelVersion, "<EMAIL>", None, Some(true), isMultiModelEnabled = isMultiModel)
                    decisionServiceClientV2.cloneRecommendedLogic(bestLogicCloneRequest)
                  }))

                  decisionCloneFuture flatMap  (responses => {
                    val leftResponses = responses.foldLeft(Seq.empty[ErrorResponse]) { case (errors, item) =>
                      item match {
                        case Left(errorResponse: ErrorResponse) => errors ++ Seq(errorResponse)
                        case Right(_) => errors
                      }
                    }
                    if (leftResponses.nonEmpty) {
                      val errors = leftResponses.map(error => error.message).toSet.mkString("\n")
                      logger.error(s"Error while cloning recommended decision logic for $accountId" + errors)
                      Future.successful(Right())
                    } else {
                      Future.successful(Right())
                    }
                  })
                case Left(errorResponse: ErrorResponse) => Future.successful(Left(errorResponse))
              }
            } else {
              Future.successful(Right())
            }
          case _ => Future.successful(Right())
        }
      } else {
        Future.successful(Right())
      }
    }

    val result = updateBundle() zip addDefaultDomains flatMap {
      case (Right(_), Right(_)) =>
        addDecisionDefaultLogic flatMap {
          case Right(_) =>
            updateProductConfiguration(accountId, updateAccountProvisioningDetails)
          case Left(errorResponse: ErrorResponse) =>
            logger.info(s"Failed to Update Bundle and Product Information, ${errorResponse.message}")
            updateProductConfiguration(accountId, updateAccountProvisioningDetails)
        }
      case (Left(error), _) =>
        logger.info(s"Failed to Update Bundle and Product Information ${error.message}")
        Future.successful(Left(ErrorResponseFactory.get(AccountProvisioningNotUpdated)))
      case (_, Left(error)) =>
        logger.info(s"Failed to add default domains, ${error.message}")
        Future.successful(Left(ErrorResponseFactory.get(AccountProvisioningNotUpdated)))
    }

    result onComplete {
      case Success(_) =>
        saveAudit(s"Updated Bundle/Feature Flag/Product Configuration", "200")
      case Failure(t) =>
        saveAudit(t.toString, "400")
    }
    result
  }

  def addDecisionDefaultLogicForSubAccount(accountId: Long, parentAccountId: Long): Future[Either[ErrorResponse, Unit]] = {
    // Fetch products for the parent account
    productService.getProductsForAccount(parentAccountId) flatMap {
      case Right(parentProducts) if parentProducts.nonEmpty =>
        // Fetch products by the role ID for "EnableDecisionForCustomersWithDocV"
        productService.getProductByBusinessUserRoleId(BusinessUserRoles.EnableDecisionForCustomersWithDocV.id) flatMap { productsList =>

          // Find the relevant product IDs and use them to define `useDocVBaseLine` and `isMultiModel`
          val docVFlagIDs = productsList.map(_.businessUserRoleId)

          // Use parentProducts to determine the logic states for useDocVBaseLine and isMultiModel
          val useDocVBaseLine = parentProducts.find(conf => conf.enabled && docVFlagIDs.contains(conf.businessUserRoleId))

          val isMultiModelFlags = parentProducts.filter(product => product.businessUserRoleId == BusinessUserRoles.EnableMultiModelDecisioning.id).map(_.businessUserRoleId)
          val isMultiModel = parentProducts.exists(conf => conf.enabled && isMultiModelFlags.contains(conf.businessUserRoleId))

          // Only migrate logics if `useDocVBaseLine` is defined
          val migrateLogicsFuture: Future[Either[ErrorResponse, Unit]] =
            if (useDocVBaseLine.isDefined) {
              decisionServiceClientV2.migrateLogics(accountId) map {
                case Right(_) => Right(())
                case Left(errorResponse) => Left(errorResponse)
              }
            } else {
              Future.successful(Right(()))
            }

          // Continue to cloning logic for all cases
          migrateLogicsFuture flatMap {
            case Right(_) =>
              // Clone decision logic across environments
              val decisionCloneFuture = Future.sequence(EnvironmentConstants.values.map { envId =>
                // Adjust the decision configuration based on the presence of `useDocVBaseLine` and `isMultiModel`
                val decConf = useDocVBaseLine match {
                  case Some(_) =>
                    DecisionConfiguration(AccountManagementDefaults.defaultDecisionLogicForDocV, "DocV Baseline", "1.0")
                  case None =>
                    if (isMultiModel) {
                      DecisionConfiguration(AccountManagementDefaults.defaultDecisionLogicIdForMultiModel, accountProvisioningConfiguration.decisionConfiguration.modelName, accountProvisioningConfiguration.decisionConfiguration.modelVersion)
                    } else {
                      accountProvisioningConfiguration.decisionConfiguration
                    }
                }

                val bestLogicCloneRequest = BestLogicCloneRequest(
                  decConf.logicId, accountId, envId.id, decConf.modelName,
                  decConf.modelVersion, "<EMAIL>", None, Some(true), isMultiModelEnabled = isMultiModel
                )
                decisionServiceClientV2.cloneRecommendedLogic(bestLogicCloneRequest)
              })

              decisionCloneFuture map { responses =>
                val leftResponses = responses.collect { case Left(errorResponse) => errorResponse }
                if (leftResponses.nonEmpty) {
                  val errors = leftResponses.map(error => error.message).mkString("\n")
                  logger.info(s"Error while cloning recommended decision logic for $accountId: $errors")
                }
                Right(())
              }

            case Left(errorResponse) => Future.successful(Left(errorResponse))
          }
        }

      case Left(errorResponse) => Future.successful(Left(errorResponse))
      case _ => Future.successful(Right(()))
    }
  }


  def updateProductConfiguration(accountId: Long, updateAccountProvisioningDetails:  UpdateAccountProvisioningDetails): Future[Either[ErrorResponse, Boolean]] = {
    updateProductConfiguration(accountId,
      updateAccountProvisioningDetails.products,
      updateAccountProvisioningDetails.productConfiguration,
      initiatedBy = updateAccountProvisioningDetails.initiatedBy) flatMap {
      case true =>
        if (updateAccountProvisioningDetails.products.nonEmpty) {
          updateAccountProvisioningDetails.products.find(_.id == BusinessUserRoles.WHITELIST_DASHBOARD.id) match {
            case Some(_) => val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
              removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(productService updateProductsForAccount UpdateProductsRequest(updateAccountProvisioningDetails.initiatedBy, accountId, updateAccountProvisioningDetails.products)) map {
                case Right(res) if res =>
                  Right(res)
                case _ =>
                  logger.info("Failed to Update Bundle and Product Information ")
                  Left(ErrorResponseFactory.get(AccountProvisioningNotUpdated))
              } recover {
                case e: Exception =>
                  logger.info("Failed to Update Bundle and Product Information ", e)
                  Left(ErrorResponseFactory.get(AccountProvisioningNotUpdated))
              }
            case _ => productService.updateProductsForAccount(UpdateProductsRequest(updateAccountProvisioningDetails.initiatedBy, accountId, updateAccountProvisioningDetails.products))
          }
        } else {
          Future.successful(Right(true))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ProductConfigNotUpdated)))
    }
  }

  def getAllFeatureFlags(allDefaultFeatureFlags: Seq[AccountProducts], defaultModules: Seq[AccountProducts], arr: ListBuffer[AccountProducts]): Seq[AccountProducts] = {
    @tailrec def getFeatureFlags(featureFlags: Seq[AccountProducts]): Seq[AccountProducts] = {
      if(featureFlags.nonEmpty) {
        arr ++= featureFlags
        getFeatureFlags(allDefaultFeatureFlags.filter(p=> featureFlags.exists(_.id==p.parentId.get)))
      } else {
        Seq.empty[AccountProducts]
      }
    }
    getFeatureFlags(defaultModules)
  }

  def baselineLogicProvisioningForSubaccount(accountId: Long, parentAccountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    // Directly call the addDecisionDefaultLogicForSubAccount method
    addDecisionDefaultLogicForSubAccount(accountId, parentAccountId).map {
      case Right(_) =>
        Right(true)
      case Left(error) =>
        logger.info(s"Failed to provision baseline logic for account $accountId: ${error.message}")
        Left(error)
    }.recover {
      case ex: Exception =>
        logger.error(s"Error while provisioning baseline logic for account $accountId: ${ex.getMessage}", ex)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  private def modifyRateLimitsIfInternalUsers(rateLimits: Set[RateLimit], isInternal: Boolean): Set[RateLimit] = {
    rateLimits.map{ rateLimit =>
      val rateLimitCount = if (isInternal) DefaultRateLimit else rateLimit.limit
      rateLimit.copy(limit = rateLimitCount)
    }
  }

  def autoProvision(accountId: Long,
                    isInternal: Boolean = false,
                    accountType: Int = AccountTypes.DIRECT_CUSTOMER.id,
                    bundleReferenceOpt: Option[String] = None): Future[Either[ErrorResponse, Boolean]] = {

    val isProspectAccount = accountType.equals(AccountTypes.PROSPECT.id)

    val defaultBundleReference = if(isProspectAccount) {
      DefaultBundleForProspectAccounts
    } else {
      bundleReferenceOpt.getOrElse(accountProvisioningConfiguration.bundleReference)
    }
    val modulesInBundle = bundleManagementService.getModules(defaultBundleReference, isInternal, accountType)

    for {
      _ <- if(isProspectAccount) {
            saveProspectsRateLimits(accountId)
          } else {
            saveRateLimits(accountId, modifyRateLimitsIfInternalUsers(accountProvisioningConfiguration.rateLimit, isInternal))
          }
      saveBundlesFuture <- modulesInBundle match {
        case Right(modules) =>
          productService.getProductsForAccount(accountId) flatMap {
            case Right(products) =>
              val productsForExternalAccounts = products.filter(f => modules.contains(f.businessUserRoleId))
              val arr = new ListBuffer[AccountProducts]
              getAllFeatureFlags(products.filter(p => p.defaultState && p.parentId.isDefined), productsForExternalAccounts, arr)
              val updateProducts = (productsForExternalAccounts ++ arr).map(p => UpdateProduct(p.id, provisioned = true, enabled = true))
              saveAccountAutomation(accountId, UpdateAccountProvisioningDetails(
                defaultBundleReference,
                updateProducts.distinct,
                ProductConfiguration(None, None, None),
                AccountManagementDefaults.defaultInitiatedBy
              ), "New Account Registration", isInternal = isInternal)
            case Left(ex1) =>
              logger.info(s"Account Automation: Unable to get the default feature flags for Modules for account $accountId", ex1)
              throw new Exception(s"Account Automation: Unable to get the default feature flags for Modules for account $accountId")
          }
        case Left(ex2) =>
          logger.info(s"Account Automation: Unable to get the Modules for external account $accountId", ex2)
          throw new Exception(s"Account Automation: Unable to get the Modules for external account $accountId")
      }
    } yield saveBundlesFuture
  }

  private def getActiveApiList(): Seq[RateLimiterPublicAPI] = {
    Seq(RateLimiterPublicAPI.EMAIL_AUTH_SCORE, RateLimiterPublicAPI.TRANSACTION,
      RateLimiterPublicAPI.REASON_CODE, RateLimiterPublicAPI.FEEDBACK, RateLimiterPublicAPI.ACCOUNT,
      RateLimiterPublicAPI.EVENTS, RateLimiterPublicAPI.FEEDBACK_NEW)
  }

  private def saveRateLimits(accountId: Long, rateLimits: Set[RateLimit]): Future[Either[ErrorResponse, Boolean]] = {
    val saveRateLimitSeq = getActiveApiList flatMap { publicAPI =>
     EnvironmentConstants.values map { env =>
        val limit = rateLimits.filter(v => v.environmentTypeId.equals(env.id) && v.api.equals(publicAPI.publicId)) match {
          case l if l.isEmpty  =>
            DefaultRateLimit
          case l0 =>
            l0.head.limit
        }
        SaveRateLimitingInput(accountId = accountId,
          environmentTypeId = env.id,
          api = publicAPI.publicId,
          windowInMillis = RateLimitingWindowSizes.SECOND.value,
          limit = limit,
          createdBy = RateLimitingEntryCreators.SERVICE.value
        )
      }
    }
    rateLimitingService.saveRateLimitCollection(saveRateLimitSeq, forceValidation=false)
  }

  private def saveProspectsRateLimits(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val saveRateLimitSeq = getActiveApiList flatMap { publicAPI =>
      EnvironmentConstants.values map { env =>
        val (limit, tps) = env.id match {
          case p if p.equals(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id) => (0L, RateLimitingWindowSizes.SECOND.value)
          case d if d.equals(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id) => (ProspectsDefaultRateLimitTPS, ProspectsDefaultRateLimitDays)
          case s if s.equals(EnvironmentConstants.SANDBOX_ENVIRONMENT.id) => (publicAPI.getDefaultRateLimit().getOrElse(DefaultRateLimit), RateLimitingWindowSizes.SECOND.value)
        }

          SaveRateLimitingInput(accountId = accountId,
            environmentTypeId = env.id,
            api = publicAPI.publicId,
            windowInMillis = tps,
            limit = limit,
            createdBy = RateLimitingEntryCreators.SERVICE.value
          )
      }
    }
    rateLimitingService.saveRateLimitCollection(saveRateLimitSeq, forceValidation=false)
  }

}
