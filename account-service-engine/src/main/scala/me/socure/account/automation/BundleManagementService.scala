package me.socure.account.automation

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.BundlesInformationNotFound
import me.socure.constants.AccountManagementDefaults.{DefaultBundleForExternalAccounts, DefaultBundleForProspectAccounts, DefaultModulesForInternalAccounts}
import me.socure.constants.{AccountTypes, Bundles, JsonFormats}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.bundles.Bundle
import org.json4s.Formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class BundleManagementService()(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  implicit def jsonFormats: Formats = JsonFormats.formats

  def getBundles(): Future[Either[ErrorResponse, Set[Bundle]]] = {
    val bundles = Bundles.getBundleInformation
    bundles.size match {
      case l if l > 0 => Future.successful(Right(bundles))
      case _ => Future.successful(Left(ErrorResponseFactory.get(BundlesInformationNotFound)))
    }
  }

  def getModules(bundleReference: String, isInternal: Boolean, accountType: Int): Either[ErrorResponse, Set[Long]] = {
    Bundles.getModules(bundleReference) match {
      case Some(b) =>
        if(isInternal && !accountType.equals(AccountTypes.DIRECT_EFFECTIV.id)) {
          val DefaultModulesForGovCloudInternalAccounts = DefaultModulesForInternalAccounts.filterNot(_ == BusinessUserRoles.AccountIntelligence.id)
          Right(b.resources ++ (if(bundleReference.equals("Public Sector")) DefaultModulesForGovCloudInternalAccounts else DefaultModulesForInternalAccounts))
        } else {
          Right(b.resources)
        }
      case None =>
        logger.info(s"No Modules associated with $bundleReference")
        Left(ErrorResponseFactory.get(BundlesInformationNotFound))
    }
  }

  def getModules(bundleReference: String): Either[ErrorResponse, Set[Long]] = {
    Bundles.getModules(bundleReference) match {
      case Some(b) =>
        Right(b.resources)
      case None =>
        logger.info(s"No Modules associated with $bundleReference")
        Left(ErrorResponseFactory.get(BundlesInformationNotFound))
    }
  }

  def getModulesForInternalAccounts: Either[ErrorResponse, Set[Long]] = {
    Bundles.getModules(DefaultBundleForExternalAccounts) match {
      case Some(b) =>
        Right(b.resources ++ DefaultModulesForInternalAccounts)
      case None =>
        logger.info(s"No Modules associated with $DefaultBundleForExternalAccounts")
        Left(ErrorResponseFactory.get(BundlesInformationNotFound))
    }
  }
}
