package me.socure.account.audit

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.account.automation.DtoAccountBundleAssociation
import me.socure.storage.slick.dao.DaoAccountAudit
import me.socure.storage.slick.tables.account.audit.DtoAccountAudit
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountAuditService(daoAccountAudit: DaoAccountAudit,
                          daoAccount: DaoAccount,
                          clock: Clock )(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getAccountAudits(publicAccountId: String): Future[Either[ErrorResponse, Seq[DtoAccountAudit]]] = {
    daoAccountAudit.getAccountAudit(publicAccountId) map {
      case dtoAccountBundleAssociationAudits: Seq[DtoAccountAudit] if dtoAccountBundleAssociationAudits.nonEmpty =>
        Right(dtoAccountBundleAssociationAudits)
      case _ =>
        logger.info(s"Account Audit not found for Account $publicAccountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAuditNotUpdated))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account =Audit Information", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAuditNotUpdated))
    }
  }

  def saveAccountAudit(accountId: Long,
                       client: String,
                       component: String,
                       componentReference: String,
                       payload: String,
                       response: String,
                       status: String,
                       processingTime: Long,
                       createdBy: String,
                       action: Option[String] = None): Future[Either[ErrorResponse, DtoAccountAudit]] = {
    daoAccount.getAccount(accountId) flatMap {
      case Some(accountInfo) =>
        val dtoAccountAudit = DtoAccountAudit(0,
          accountInfo.publicId,
          client,
          component,
          action,
          componentReference,
          payload,
          response,
          status,
          processingTime,
          createdBy,
          clock.now)
        daoAccountAudit.saveAccountAudit(dtoAccountAudit) map {
          case dtoAccountBundleAssociationAudit: DtoAccountAudit =>
            Right(dtoAccountBundleAssociationAudit)
          case _ =>
            logger.info(s"Error occurred while saving Account Audit", dtoAccountAudit)
            Left(ErrorResponseFactory.get(ExceptionCodes.AccountAuditNotSaved))
        }
      case _ =>
        logger.info(s"Account with Id: $accountId, not found, account bundle association audit not saved")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while saving Account Bundle Association Audit", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAuditNotSaved))
    }
  }
}
