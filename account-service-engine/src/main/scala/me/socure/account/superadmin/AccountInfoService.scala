package me.socure.account.superadmin

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.validator.V2Validator
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account._
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.{AccountId, ApiKeyString}
import me.socure.model.user.authorization.{AccountWithEnvironmentDetails, AccountWithEnvironmentDetailsWithPublicId}
import me.socure.storage.slick.tables.account._
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

/**
 * Created by jamesanto on 9/15/16.
 */
class AccountInfoService(daoAccount: DaoAccount, v2Validator: V2Validator)(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)

  def fetchPublicIdByAccountId(accountId: Long): Future[Option[String]] = {
    daoAccount.fetchPublicIdByAccountId(accountId)
  }

  def list(): Future[Either[ErrorResponse, Seq[PublicAccount]]] = {
    daoAccount.getAllAccounts map { list =>
      Right(
        list.map { a =>
          PublicAccount(
            id = a.publicId,
            name = a.name
          )
        }
      )
    }
  }

  def listAccountIds(): Future[Either[ErrorResponse, Seq[Long]]] = {
    daoAccount.getAllAccountsIds.map(Right(_)) recover {
      case e: Exception =>
        logger.error("Failed to get all Account Ids", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getActiveAccountNameList: Future[Either[ErrorResponse, Map[Long, AccountIdName]]] = {
    daoAccount.getAllAccountNames map {
      case an if an.nonEmpty => Right(an.map(a => a._1 -> AccountIdName(a._1, a._2)).toMap)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getNonInternalActiveAccountList: Future[Either[ErrorResponse, Seq[Long]]] = {
    daoAccount.getAllNonInternalActiveAccounts map (Right(_))
  }

  def hasRole(apiKeyString: ApiKeyString, role: Int): Future[Option[Boolean]] = {
    getAccountIdByApiKey(apiKeyString).flatMap {
      case Some(accountId) => hasRole(accountId = accountId.value, role = role).map(Some(_))
      case None => Future.successful(None)
    }
  }

  def getAccountIdByApiKey(apiKeyString: ApiKeyString): Future[Option[AccountId]] = {
    daoAccount
      .getAccountIdByApiKey(apiKeyString.value)
      .map(_.map(AccountId.apply))
  }

  def hasRole(accountId: Long, role: Int): Future[Boolean] = {
    anyHasRole(accountIds = Set(accountId), role = role)
  }

  def anyHasRole(accountIds: Set[Long], role: Int): Future[Boolean] = {
    val accountAttributeValueOpt = AccountConvertors.convertRoleToAttribute(role)
    daoAccount.anyHasRole(accountIds = accountIds, businessUserRole = role, accountAttributeValueOpt = accountAttributeValueOpt)
  }

  def getParentAccountsWithoutPrimaryUsers(): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    daoAccount.getParentAccountsWithoutPrimaryUsers map (Right(_))
  }

  def getAccountWithEnvironmentDetailsByPublicId(publicId: String): Future[Option[AccountWithEnvironmentDetails]] = {
    daoAccount.getAccountWithEnvironmentDetailsByPublicId(publicId = publicId).map(toAccWithEnvDetails)
  }

  private def toAccWithEnvDetails(details: Option[(DtoAccount, Set[DtoEnvironment], Set[DtoAccountPermission], Set[DtoApiKey], Map[Long, String])]): Option[AccountWithEnvironmentDetails] = {
    details.map {
      case (account, environments, permissions, apiKeys, subscriptionTypes) =>
        AccountConvertors.convertToAccountWithEnv(
          account = account,
          environments = environments,
          permissions = permissions,
          apiKeys = apiKeys,
          subscriptions = subscriptionTypes.map(subscriptionType => Subscription(subscriptionType._1, subscriptionType._2)).toSet
        )
    }
  }

  def getAccountWithEnvironmentDetailsById(id: Long): Future[Option[AccountWithEnvironmentDetailsWithPublicId]] = {
    for {
      rootAccountDetails <- v2Validator.getRootParentAccountDetail(id)
      accountInfo <-  daoAccount.getAccountWithEnvironmentDetailsById(id = id).map(toAccWithEnvDetailsWithPublicId(_, rootAccountDetails))
    } yield accountInfo
  }

  private def toAccWithEnvDetailsWithPublicId(details: Option[(DtoAccount, Set[DtoEnvironment], Set[DtoAccountPermission], Set[DtoApiKey], Map[Long, String])], rootAccountDetails: Option[(DtoAccount, DtoAccountHierarchy)]): Option[AccountWithEnvironmentDetailsWithPublicId] = {
    details.map {
      case (account, environments, permissions, apiKeys, subscriptionTypes) =>
        AccountConvertors.convertToAccountWithEnvWithPublicId(
          account = account,
          environments = environments,
          permissions = permissions,
          apiKeys = apiKeys,
          subscriptions = subscriptionTypes.map(subscriptionType => Subscription(subscriptionType._1, subscriptionType._2)).toSet,
          rootAccountDetails = rootAccountDetails
        )
    }
  }

  def getAccountIdNamesWithRoles(roles: Set[Int], onlyParents: Boolean): Future[Set[AccountIdName]] = {
    daoAccount.getAccountIdNamesWithRoles(
      roles = roles,
      onlyParents = onlyParents
    )
  }

  def getAccountNameById(accountId: Long): Future[Option[String]] = {
    daoAccount.getAccount(accountId).map(_.map(_.name))
  }

  def getAccountNameAndPublicId(accountId: Long): Future[Option[PublicAccount]] = {
    daoAccount.getAccount(accountId).map { a =>
      a.map { acc =>
        PublicAccount(id = acc.publicId, name = acc.name)
      }
    }
  }

  def getAccountInfoWithPermission(publicId: String): Future[Option[AccountInfoWithPermission]] = {

    daoAccount.fetchAccountInfoAndPermission(publicId).map { info =>
      info.headOption match {
        case Some((accountInfo, permissions)) =>

          val roles: Set[Int] = permissions.map(_.permission).toSet
          Some(AccountInfoWithPermission(accountInfo.accountId, accountInfo.publicId, accountInfo.name, accountInfo.isInternal, roles))

        case _ =>
          logger.info(s"No account found with publicId: $publicId")
          None
      }
    }

  }


  def getAccountInfoWithPermission(accountId: Long): Future[Option[AccountInfoWithPermission]] = {

    daoAccount.fetchAccountInfoAndPermission(accountId).map { info =>
      info.headOption match {
        case Some((accountInfo, permissions)) =>

          val roles: Set[Int] = permissions.map(_.permission).toSet
          Some(AccountInfoWithPermission(accountInfo.accountId, accountInfo.publicId, accountInfo.name, accountInfo.isInternal, roles))

        case _ =>
          logger.info(s"No account found for $accountId")
          None
      }
    }
  }

  def retrieveApiKeyByPublicKey(publicKey: String): Future[Option[DtoApiKey]] = daoAccount.fetchActiveApikeyByPublicKey(publicKey)

  def getEncryptedParentAccounts(): Future[Either[ErrorResponse, Seq[PublicAccount]]] = {
    daoAccount.getEncryptedParentAccounts map { accList =>
      Right(
        accList.map { acc => PublicAccount(id = acc.id, name = acc.name) }
      )
    }
  }

  def getAccountPreferences(accountId: Long): Future[Option[AccountPreferences]] = {
    daoAccount.getAccount(accountId).map(_.map { a =>
      AccountPreferences(id = a.accountId,
        internal = a.isInternal,
        active = a.isActive,
        deleted = a.isDeleted)
    })
  }

  def retrieveAccountInfoByPublicKey(publicApiKey: String): Future[Option[AccountInfoV2WithPermission]] = {
    daoAccount.fetchAccountByPublicKey(publicApiKey).flatMap { accountInfo =>
      accountInfo.headOption match {
        case Some(x) =>
          val account: DtoAccount = x._1
          val permissions: Seq[DtoAccountPermission] = x._2
          val roles: Set[Int] = permissions.map(_.permission).toSet

          val accountInfoV2: AccountInfoV2WithPermission = AccountInfoV2WithPermission(
            id = account.accountId,
            publicId = account.publicId,
            name = account.name,
            isInternal = account.isInternal,
            parentId = account.parentId,
            deleted = account.isDeleted,
            active = account.isActive,
            roles = roles
          )

          val accountInfoV2WithPermission: Future[Some[AccountInfoV2WithPermission]] = account.parentId
            .map(parentAccountId => daoAccount.getAccountPermissions(parentAccountId))
            .map(_.map { parentAccountRoles =>
              val parentAccountRoleIds: Set[Int] = parentAccountRoles.map(_.permission).toSet

              val finalRoles: Set[Int] = parentAccountRoleIds ++ accountInfoV2.roles

              Some(accountInfoV2.copy(roles = finalRoles))

            }).getOrElse(Future.successful(Some(accountInfoV2)))
          accountInfoV2WithPermission

        case _ => Future.successful(None)
      }
    }
  }

  def retrieveAccountInfoByPrivateKey(privateApiKey: String): Future[Option[AccountInfoWithPermission]] = {
    daoAccount.fetchAccountByPrivateKey(privateApiKey).map { accountInfo =>
      accountInfo.headOption match {
        case Some(x) =>
          val account: DtoAccount = x._1
          val permissions: Seq[DtoAccountPermission] = x._2
          val roles: Set[Int] = permissions.map(_.permission).toSet

          val accountInfoV2: AccountInfoWithPermission = AccountInfoWithPermission(
            id = account.accountId,
            publicId = account.publicId,
            name = account.name,
            isInternal = account.isInternal,
            roles = roles
          )
          Some(accountInfoV2)
        case _ => None
      }
    }
  }


  def retrieveAccountInfoByPublicKeyV3(publicApiKey: String): Future[Option[AccountInfoV3]] = {
    daoAccount.fetchAccountByPublicKey(publicApiKey).flatMap { accountInfo =>
      generateAccountInfoV3(accountInfo)
    }
  }

  def generateAccountInfoV3(accountInfo: Map[DtoAccount, Seq[DtoAccountPermission]]): Future[Option[AccountInfoV3]] = {
    accountInfo.headOption match {
      case Some(x) =>
        val account: DtoAccount = x._1
        val permissions: Seq[DtoAccountPermission] = x._2
        val roles: Set[Int] = permissions.map(_.permission).toSet

        val accountInfoV3 = AccountInfoV3(
          id = account.accountId,
          publicId = account.publicId,
          name = account.name,
          isInternal = account.isInternal,
          deleted = account.isDeleted,
          active = account.isActive,
          roles = roles,
          parent = None
        )

        val maybeEventualMaybeAccount = account.parentId.map(daoAccount.getAccount(_).flatMap {
          case Some(parentAccount) =>
            val parentAccountInfo = daoAccount
              .getAccountPermissions(parentAccount.accountId)
              .map(_.map(_.permission).toSet)
              .map { roles =>
                val v3Parent = AccountInfoV3Parent(
                  id = parentAccount.accountId,
                  publicId = parentAccount.publicId,
                  name = parentAccount.name,
                  isInternal = parentAccount.isInternal,
                  deleted = parentAccount.isDeleted,
                  active = parentAccount.isActive,
                  roles = roles
                )
                Some(v3Parent)
              }
            parentAccountInfo
          case _ => Future.successful(None)
        })
        val eventualV = maybeEventualMaybeAccount.map(_.map { pa =>
          Some(accountInfoV3.copy(parent = pa))
        }).getOrElse(Future.successful(Some(accountInfoV3)))
        eventualV
      case _ => Future.successful(None)
    }
  }

  def retrieveAccountInfoByPrivateKeyV3(privateApiKey: String): Future[Option[AccountInfoV3]] = {
    daoAccount.fetchAccountByPrivateKey(privateApiKey).flatMap { accountInfo =>
      generateAccountInfoV3(accountInfo)
    }
  }

  def getAllAccountNamesWithPublicId: Future[Either[ErrorResponse, Map[String, PublicAccountIdName]]] = {
    daoAccount.getAllAccountNamesWithPublicId map {
      case list if list.nonEmpty => Right(list.map(a => a._2 -> PublicAccountIdName(a._1, a._2, a._3)).toMap)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  @deprecated
  def getAnalyticsImportInfo(accountId: Long): Future[Either[ErrorResponse, Option[AccountAnalyticsInfoResponse]]] = {
    daoAccount.getAccountAnalyticsinfo(accountId).map(Right(_)).recover {
      case e: Exception =>
        logger.error(s"Get analytics import info: Error occures for Account ID - ${accountId}", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAnalyticsGlobalImportInfo(): Future[Either[ErrorResponse, Option[AnalyticsGlobalInfoResponse]]] = {
    daoAccount.getAnalyticsGlobalinfo().map(Right(_)).recover {
      case e: Exception =>
        logger.error(s"Get analytics import info: Error occured", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  @deprecated
  def addAnalyticsImportInfo(analyticsInfoReq: AccountAnalyticsInfoRequest): Future[Either[ErrorResponse, Boolean]] = {
    val dtoAccountAnalytics = DtoAccountAnalytics(0, analyticsInfoReq.accountId, analyticsInfoReq.isHostoricDataImported, analyticsInfoReq.lastImportedDate)
    daoAccount.addAccountAnalyticsInfo(dtoAccountAnalytics).map{
      case true => Right(true)
      case false => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  @deprecated
  def updateAnalyticsLastImportedDate(accountId: Long, lastImportedDate: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.updateAccountAnalyticsLastImportedDate(accountId, lastImportedDate).map{
      case true => Right(true)
      case false => Left(ErrorResponseFactory.get(AccountAnalyticsInfoNotFound))
    }
  }

  def addAnalyticsGlobalImportInfo(analyticGlobalInfoReq: AnalyticsGlobalInfoRequest): Future[Either[ErrorResponse, Boolean]] = {
    val dtoAnalyticsGlobal = DtoAnalyticsGlobal(0, analyticGlobalInfoReq.lastImportedDate)
    daoAccount.addAnalyticsGlobalInfo(dtoAnalyticsGlobal).map{
      case true => Right(true)
      case false => Left(ErrorResponseFactory.get(UnknownError))
    }.recover {
      case e: Exception =>
        logger.error(s"Error occured while adding analytics global info", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAccountsWithPermission(permissionId: Int): Future[Either[ErrorResponse, Set[Long]]] = {
    daoAccount.getAccountsWithPermission(permissionId).map(accounts => Right(accounts.toSet)).recover {
      case e: Exception =>
        logger.error(s"Failed to get Account Ids with Permission Id - ${permissionId}", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAccountIdNamesWithAndWithoutPermission(withPermission: Int, withoutPermission: Int): Future[Either[ErrorResponse, List[AccountIdName]]] = {
   daoAccount.getAccountIdNamesWithAndWithoutPermission(withPermission,withoutPermission).map(accounts => Right(accounts.toList)).recover {
     case e: Exception =>
       logger.error(s"Failed to get Account Ids with Permission Id - ${withPermission}", e)
       Left(ErrorResponseFactory.get(UnknownError))
   }
  }

  def getActiveApiKeyForAccountByEnvironmentType(accountId: Long, envTypeId: Long) : Future[Either[ErrorResponse, ActiveAccountApiKey]] = {
    daoAccount.getActiveApiKeyForAccountByEnvironmentType(accountId, envTypeId) map {
      case Some(apiKey) => Right(apiKey)
      case None =>
        logger.error(s"Active Api Key not found for Account ID - ${accountId} & env type ID - ${envTypeId}")
        Left(ErrorResponseFactory.get(ApiKeyNotFound))
    } recover {
      case e: Exception =>
        logger.error(s"Failed to get active api key for Account ID - ${accountId} & env type ID - ${envTypeId}", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }
}
