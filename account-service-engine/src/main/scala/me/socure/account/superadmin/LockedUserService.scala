package me.socure.account.superadmin

import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.superadmin.LockedUsers
import me.socure.storage.slick.tables.user.DaoBusinessUser

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/3/16.
  */
class LockedUserService(daoBusinessUser: DaoBusinessUser, clock: Clock)(implicit executionContext: ExecutionContext) {

  def getLockedUserList : Future[Either[ErrorResponse, Vector[LockedUsers]]] = {

    daoBusinessUser.fetchLockedUsers map {
      case lu : Vector[LockedUsers] => Right(lu)
      case _ => Left(ErrorResponse(UnknownError.id, UnknownError.description))
    }
  }

  def unlockUser(emails : List[String]) : Future[Either[ErrorResponse, Int]] = {

    daoBusinessUser.unlockUsers(emails, clock) map {
      case count if count == emails.size =>
        daoBusinessUser.getUser(emails).flatMap(u => removeBadLockCount(
                                                          u.map(_.id).
                                                          headOption.
                                                          getOrElse(throw new IllegalArgumentException("Failed to retrieve user for any emails"))))
        Right(count)
      case _ => Left(ErrorResponse(BusinessUserNotFound.id, BusinessUserNotFound.description))
    }
  }

  def removeBadLockCount(userId : Long) : Future[Int] = {
    daoBusinessUser.deleteBadLoginDetails(userId)
  }

}
