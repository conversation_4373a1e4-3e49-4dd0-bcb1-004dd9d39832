package me.socure.account.superadmin

import me.socure.account.saml.SamlValidator
import me.socure.account.service.PasswordService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.RealClock
import me.socure.model.superadmin.{AccountName, DelegatedAdmin}
import me.socure.model.ErrorResponse
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunder<PERSON> on 6/9/16.
  */
class DelegatedAdminService(daoBusiness: DaoBusinessUser, passwordService: PasswordService, samlValidator: SamlValidator)(implicit ec : ExecutionContext)  extends RealClock {

  def getAccountName : Future[Either[ErrorResponse, Vector[AccountName]]] = {

    daoBusiness.getAdminAndSuperAdminList map {
      case list if list.nonEmpty => Right(list)
      case list if list.isEmpty => Right(Vector.empty)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }

  }

  def getDelegatedAdminForBusinessUser(email : String) : Future[Either[ErrorResponse, Vector[DelegatedAdmin]]] = {

    daoBusiness.getDelegatedAdminList(email) flatMap  {
      case userList if userList.nonEmpty =>
        val adminRolesFuture = userList map {
          case user if user.roles.isDefined => Future.successful(user -> user.roles.getOrElse(throw new Exception("Failed to find roles for user")))
          case user =>
          daoBusiness.getBusinessUserRoles(user.id) map {
            case roles if roles.nonEmpty => user -> roles.toSet
            case _ => user -> Set.empty[Int]
          }
        }

        Future.sequence(adminRolesFuture).map { adminRoles =>
          Right(adminRoles.map {
            case (admin, roles) => admin.copy(roles = Some(roles))
          })
        }
      case userList if userList.isEmpty => Future.successful(Right(Vector.empty[DelegatedAdmin]))
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def optToFuture[A](opt: Option[(A)]): Future[A] = {
    opt match{
      case Some(a) => Future.successful(a)
      case _ => Future.failed(throw new Exception("Empty option conversion failure"))
    }
  }

  def createDelegatedAdmin(email : String, newUser : DelegatedAdmin, password : String) = {
    samlValidator.whenUserHasNoSaml(email = email) {
      create(email, newUser, password)
    }
  }

  private def create(adminEmail : String, newUser : DelegatedAdmin, password : String) : Future[Either[ErrorResponse, Boolean]] = {
    val result = for {
      b <- daoBusiness.getUser(adminEmail)
      bf <- optToFuture(b)
      e <- daoBusiness.getDefaultEnvironmentForAccount(bf.accountId)
      bu <- {
        val bu = convertToBusinessUser(newUser, bf.accountId)
        val roles = newUser.roles match {
          case Some(bur) => bur
          case _ => Set.empty[Int]
        }
        daoBusiness.addDelegatedAdmin(bu, roles, e.id)
      }
    } yield bu

    result flatMap  {
      case Some(count) if count > 0 => passwordService.setPasswordWithEmail(newUser.email, password)
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }

  }

  def deleteDelegatedAdmin(email : String) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusiness.deletedDelegatedAdmin(email) map {
      case count if count > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }
  }

  def updatePermission(email : String, roles : Set[Int]) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusiness.updateDelegatedUserPermission(email, roles) map {
      case Some(count) if count > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def updateUserInformation(email : String, updateInfo : DelegatedAdmin) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusiness.updateUserInformation(email, updateInfo) map {
      case count if count > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }
  }

  def promoteDelegatedAdmin(email : String) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusiness.promoteUserToAdmin(email) map {
      case Some(count) if count > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def isUserExist(email : String) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusiness.getUser(email) map {
      case Some(u) => Right(true)
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }
  }

  def resetPassword(email: String, password: String) : Future[Either[ErrorResponse, Boolean]]= {
    samlValidator.whenUserHasNoSaml(email = email) {
      passwordService.setPasswordWithEmail(email, password)
    }
  }

  private def convertToBusinessUser(user : DelegatedAdmin, accountId : Long) : DtoBusinessUser = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contact,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = accountId,
      isPrimaryUser = false
    )
  }

}
