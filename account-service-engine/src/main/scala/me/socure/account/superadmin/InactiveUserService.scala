package me.socure.account.superadmin

import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.utils.UUIDUtilities
import me.socure.common.clock.Clock
import me.socure.convertors.BusinessUserConvertors
import me.socure.model.ErrorResponse
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.PrimaryAccountUser
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoActivationToken}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunder<PERSON> on 6/7/16.
  */
class InactiveUserService(daoBusiness: DaoBusinessUser, samlValidator: SamlValidator, clock : Clock)(implicit ec : ExecutionContext){
  val logger: Logger = LoggerFactory.getLogger(getClass)
  def getInactivesPrimaryAccountAdmins : Future[Vector[PrimaryAccountUser]] = {

    daoBusiness.getInactiveAccountAndPrimaryUser().map(dtoAdmins => BusinessUserConvertors.getInactiveAccountPrimaryAdmins(dtoAdmins))
  }

  def getActivationLink(emails: List[String]) : Future[Either[ErrorResponse, List[UserActivationDetails]]] = {
    def generateUserActivationTokens(): Future[Seq[(Long, UserActivationDetails)]] = {
        daoBusiness.getUser(emails).map {
          case users if users.nonEmpty =>
            logger.info(s"UAT: ${users.size} Users ")
            users.foreach(v => logger.info(s"UAT: User: ${v.email}, ${v.id}"))
            users.map { user =>
            (user.id, UserActivationDetails(user.firstName, user.lastName, user.email, Some(UUIDUtilities.getRandomUUID)))
        }
      }
    }

    def getUserAccountAndResetToken(): Future[Either[ErrorResponse, List[UserActivationDetails]]] = {
      daoBusiness.getUserAccountAndResetToken(emails) map {
        case list if list.nonEmpty =>
          Right(list.map {
          case (businessUser, account, activationToken) => BusinessUserConvertors.getActivationDetails(
            daoUser = businessUser,
            account = account,
            dtoToken = activationToken
          )
        }.toList )
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound))
      }
    }

    samlValidator.whenUsersHaveNoSaml(emails = emails.toSet) {
      emails.foreach(v => logger.info(s"UAT: Email not registered as SAML: $v"))
      for {
        uats <- generateUserActivationTokens()
        userActivationDetails <- {
          uats.foreach(v => logger.info(s"UAT: Business userId, Activation Token: ${v._1}, ${v._2.activationCode}"))
          val dtoActivationTokens = uats map { uat =>
            DtoActivationToken(
              id = 0,
              businessUserId = uat._1,
              token = uat._2.activationCode,
              createdAt = Some(clock.now()))
          }
          daoBusiness.createActivationCode(dtoActivationTokens) flatMap {
            case Some(n) if n > 0 =>
              logger.info(s"UAT: createActivationCode inserted $n rows")
              Future.successful(Right(uats.map(_._2).toList))
            case _ =>
              logger.info(s"UAT: createActivationCode failed!!!")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
          } recover {
            case _: Throwable =>
              logger.info("UAT: Unknown Error")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
          }
        }
      } yield userActivationDetails
    }
  }

  def getPasswordResetLinkAsActivationDetails(emails : List[String]) : Future[Either[ErrorResponse, List[UserActivationDetails]]] = {
    samlValidator.whenUsersHaveNoSaml(emails = emails.toSet) {
      val flist = emails map { e =>
        daoBusiness.getUser(e).flatMap {
          case Some(uu) => daoBusiness.createPasswordResetCode(DtoActivationToken(
            id = 0,
            businessUserId = uu.id,
            token = None,
            createdAt = Some(clock.now())
          ))
          case None => Future.successful(None)
        }
      }

      Future.sequence(flist).flatMap { _ =>
        daoBusiness.getUserAccountAndResetToken(emails) map {
          case list if list.nonEmpty => Right(list.map {
            case (businessUser, account, activationToken) => BusinessUserConvertors.getActivationDetails(
              daoUser = businessUser,
              account = account,
              dtoToken = activationToken
            )
          }.toList )
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound))
        }
      }
    }
  }
}
