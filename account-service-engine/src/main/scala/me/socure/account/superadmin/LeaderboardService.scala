package me.socure.account.superadmin

import me.socure.DaoAccount
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountDetails, AccountIdName}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON> on 6/17/16.
  */
class LeaderboardService(daoAccount: DaoAccount)(implicit ec : ExecutionContext) {

  def getInternalAccountIds : Future[Either[ErrorResponse, Seq[Long]]] = {
    daoAccount.getInternalAccountsId map {
      case list => Right(list)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAccountNameList : Future[Either[ErrorResponse, Map[Long, AccountIdName]]] = {

    daoAccount.getAllAccountNames map {
      case an if an.nonEmpty => Right(an.map(a => a._1 -> AccountIdName(a._1, a._2)).toMap)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
     }
  }

  def getAccountByApiKey(apiKey : String) : Future[Either[ErrorResponse, AccountDetails]] = {

    daoAccount.getAccountDetailsByApiKey(apiKey) map {
      case Some(account) => Right(AccountDetails(account._1, account._2, account._3))
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

}
