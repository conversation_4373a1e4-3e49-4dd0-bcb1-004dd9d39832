package me.socure.account.superadmin

import me.socure.DaoAccount
import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.PasswordStorageService
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.ipvalidator.IPAddressValidator
import me.socure.model.ErrorResponse
import me.socure.model.account.DeletedAccountDetails
import me.socure.model.dashboardv2.EnvironmentNameAndId
import me.socure.storage.slick.dao.DaoEnvironment
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunder<PERSON> on 5/30/16.
  */

class ActiveUsersService(daoAccount: DaoAccount, daoBusinessUser: DaoBusinessUser, passwordStorageService: PasswordStorageService,
                         environmentSettingsService: EnvironmentSettingsService, daoEnvironment: DaoEnvironment)(implicit ec : ExecutionContext) {

  private val logger : Logger = LoggerFactory.getLogger(this.getClass)
  private val defaultDomains = Set("0.0.0.0/1", "*********/1")

  def markAsInternal(emails : List[String]) : Future[Either[ErrorResponse, Int]] = {
    makingInternal(emails, value = true)
  }

  def unmarkAsInternal(emails : List[String]) : Future[Either[ErrorResponse, Int]] = {
    makingInternal(emails, value = false)
  }

  private def makingInternal(emails : List[String], value : Boolean) : Future[Either[ErrorResponse, Int]] = {

    daoBusinessUser.getUser(emails) flatMap {
      case list if list.nonEmpty => daoAccount.markInternal(list.map(_.accountId).toList, value) map {
        case count if count > 0 => Right(list.size)
        case _ => Left(ErrorResponseFactory.get(AccountNotFound))
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def deactivateUser(emails : List[String]) : Future[Either[ErrorResponse, Int]] = {

    daoBusinessUser.getUser(emails) flatMap {
      case list if list.nonEmpty => daoAccount.deactivateAccounts(list.map(_.accountId).toList) map {
        case count if count > 0 => Right(list.size)
        case _ => Left(ErrorResponseFactory.get(AccountNotFound))
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def addDefaultDomains(accountId : Long): Future[Either[ErrorResponse, Unit]] = {
    val allEnvironmentsFuture = environmentSettingsService getAccountEnvironmentList accountId // return all environments
    val environmentDomainsFuture = environmentSettingsService getEnvironmentWithDomains accountId // return environments with domains alone

    allEnvironmentsFuture zip environmentDomainsFuture flatMap {
      case (Right(allEnvironments), Right(environmentDomains)) =>
        val updateDomains = allEnvironments map ( env => {
          val existingDomains = environmentDomains.find(_.id == env.id)
          val finalDomains = existingDomains match {
            case Some(domains) => domains.domains.split(",").toSet ++ defaultDomains
            case None => defaultDomains
          }
          daoEnvironment updateDomain(env.id, finalDomains.toList)
        })
        Future.sequence(updateDomains) map {
          case updates if updates.forall(_ > 0) => Right()
          case _ =>
            logger.error(s"Error while updating domains for internal account $accountId")
            Left(ErrorResponseFactory.get(EnvironmentNotFound))
        }
      case (_, Left(errorResponse)) =>
        logger.error(s"Error $errorResponse while updating domains for internal account $accountId")
        Future.successful(Left(errorResponse))
      case (Left(errorResponse), _) =>
        logger.error(s"Error $errorResponse while updating domains for internal account $accountId")
        Future.successful(Left(errorResponse))
    }
  }

  def markAccountAsInternal(accountId : Long) : Future[Either[ErrorResponse, Int]] = {
    markInternal(accountId, value = true) flatMap {
      case Right(updated) =>
        addDefaultDomains(accountId) map {
          case Right(_) => Right(updated)
          case Left(errorResponse) => Left(errorResponse)
        }
      case Left(errorResponse) => Future.successful(Left(errorResponse))
    }
  }

  def unmarkAccountAsInternal(accountId : Long) : Future[Either[ErrorResponse, Int]] = {
    markInternal(accountId, value = false) flatMap {
      case Right(updated) =>
        val environmentDomainsFuture = environmentSettingsService getEnvironmentWithDomains accountId // return environments with domains alone
        environmentDomainsFuture flatMap {
          case Right(environmentDomains) =>
            val updateDomains = environmentDomains flatMap ( env => {
              val existingDomains = env.domains.split(",").toSet
              if(defaultDomains.intersect(existingDomains).nonEmpty) {
                val finalDomains = existingDomains -- defaultDomains
                Some(daoEnvironment updateDomain(env.id, finalDomains.toList))
              } else {
                None
              }
            })
            Future.sequence(updateDomains) map {
              case updates if updates.forall(_ > 0) => Right(updated)
              case _ =>
                logger.error(s"Error while updating domains for internal account $accountId")
                Left(ErrorResponseFactory.get(EnvironmentNotFound))
            }
          case Left(errorResponse) =>
            logger.error(s"Error $errorResponse while updating domains for internal account $accountId")
            Future.successful(Left(errorResponse))
        }
      case Left(errorResponse) => Future.successful(Left(errorResponse))
    }
  }

  private def markInternal(accountId : Long, value : Boolean) : Future[Either[ErrorResponse, Int]] = {
      daoAccount.markInternal(List(accountId), value) map {
        case count if count > 0 => Right(count)
        case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def deactivateAccount(accountId : Long) : Future[Either[ErrorResponse, Int]] = {
    daoAccount.deactivateAccounts(List(accountId).toList) map {
      case count if count > 0 => Right(count)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getDomainByAccountId(accountId : Long) : Future[Either[ErrorResponse, String]] = {
    daoAccount.getDomainsListByAccountId(accountId) map {
      case Some(d) => Right(d.getOrElse(""))
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
  }

  def addDomainByAccountId(accountId : Long, domains : List[String]) : Future[Either[ErrorResponse, Boolean]] = {
    if(domains.isEmpty || IPAddressValidator.validate(domains.toSet))
      daoAccount.updateDomain(accountId, domains) map {
        case count if count > 0 => Right(true)
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.ParentAccountNotFound))
      }
    else Future.successful(Left(ErrorResponseFactory.get(DomainNotValid)))
  }

  def deleteAccount(accountId : Long) : Future[Either[ErrorResponse, Boolean]] = {

    //Gets all business user of given accountId. Even pulls out sub-account business user of given account id
    val deleteUserFuture = daoBusinessUser.getBusinessUserListByAccountId(accountId, showSubAccount = true) flatMap { list =>
      Future.sequence(
        list map { b =>
          val badDetailsFuture = daoBusinessUser.deleteBadLoginDetails(b.id) //Delete bad login count
          val badCountFuture = daoBusinessUser.deleteBadLoginAttemptInfo(b.id) //Delete bad login audit
          val roleFuture = daoBusinessUser.deleteAllUserEnvironmentRoles(b.id) //Delete all user roles
          val passwordFuture = passwordStorageService.deletePassword(b.id) //Delete associated passwords
          Future.sequence(List(badDetailsFuture, badCountFuture, roleFuture, passwordFuture)) map (_ => daoBusinessUser.deleteUser(b.id))
        }
      )
    }

    //Marks as deleted including sub-account of given accountId
    val markDeleteAccounts = daoAccount.getWithSubAccounts(accountId) flatMap {
      list => Future.sequence(list.map(a => daoAccount.deleteAccount(a.accountId)))
    }

    Future.sequence(Vector(deleteUserFuture, markDeleteAccounts)) map {
      case list if list.flatten.nonEmpty => Right(true)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getDeletedAccountsList : Future[Vector[DeletedAccountDetails]] = daoAccount.getAllDeletedAccounts

  def getPublicAccountId(accountId: Long): Future[Option[String]] = {
    daoAccount.getAccount(accountId) map {
      case Some(acc) => Some(acc.publicId)
      case _ => None
    }
  }

  def getEnvironments(accountId: Long): Future[Seq[EnvironmentNameAndId]] = {
    environmentSettingsService.getAccountEnvironmentList(accountId)  map {
      case Right(l) => l
      case _ => Seq.empty[EnvironmentNameAndId]
    }
  }

}
