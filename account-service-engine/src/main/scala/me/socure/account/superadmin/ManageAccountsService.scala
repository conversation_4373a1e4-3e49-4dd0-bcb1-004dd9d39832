package me.socure.account.superadmin

import me.socure.DaoAccount
import me.socure.account.service.{BusinessUserCommonService, EncryptionKeysService}
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, InvalidInputFormat, UpdateAccountStatusFailed}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.common.clock.Clock
import me.socure.configuration.ApiKeyRenewalConfig
import me.socure.convertors.AccountConvertors
import me.socure.mail.service.MailNotificationService
import me.socure.model.ErrorResponse
import me.socure.model.account.{ApiKeyStatus, ParentAccount}
import me.socure.model.encryption.AccountId
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.superadmin.AccountCreationForm
import me.socure.storage.slick.dao.{DaoEnvironment, DaoPublicApiKey}
import me.socure.storage.slick.tables.account.{Dto<PERSON>ccount, DtoAccountConsentReason}
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.apache.commons.lang3.StringUtils
import org.apache.commons.validator.routines.EmailValidator
import org.jasypt.encryption.StringEncryptor
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by angayarkanni on 10/25/17.
  */
class ManageAccountsService(daoAccount: DaoAccount,
                            daoBusiness: DaoBusinessUser,
                            daoEnvironment: DaoEnvironment,
                            clock: Clock,
                            encryptionKeysService: EncryptionKeysService,
                            apiKeyRenewalConfig: ApiKeyRenewalConfig,
                            daoPublicApiKey: DaoPublicApiKey,
                            pbeEncryptor: StringEncryptor,
                            modelManagementClient: ModelManagementClient,
                            businessUserCommonService: BusinessUserCommonService,
                            inactiveUserService: InactiveUserService)(implicit ec : ExecutionContext) {
  val LOGGER = LoggerFactory.getLogger(this.getClass)

  def getParentAccounts(): Future[Either[ErrorResponse, Seq[ParentAccount]]] = {
    daoAccount.getParentAccounts.map{
      an => Right(an.map(a => AccountConvertors.toParentAccounts(a)).toList)
    }
  }

  def createAccountIfNotExists(accountCreationForm: AccountCreationForm) : Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.doesAccountNameExist(accountCreationForm.companyName).flatMap {
      case true => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)))
      case false => createAccount(accountCreationForm)
    }
  }

  private def createAccount(accountCreationForm: AccountCreationForm): Future[Either[ErrorResponse, Boolean]] = {
    val account = AccountConvertors.getAccount(accountCreationForm, pbeEncryptor)
    val dtoEnvironmentsTemplate = AccountConvertors.getDtoEnvironment(0, clock)
    val dtoApiKeyTemplate = AccountConvertors.getDtoApiKey(0, ApiKeyStatus.ACTIVE, clock)
    val dtoPublicApiKeyTemplate = AccountConvertors.getPublicDtoApiKey(0, ApiKeyStatus.ACTIVE, clock)

    daoAccount.createAccount(account, dtoEnvironmentsTemplate, dtoApiKeyTemplate, dtoPublicApiKeyTemplate) flatMap {
      case (accountId, savedEnvironmentIds, apiKeysId, publicApiKeysId) =>
        generateEncryptionKeys(accountId) flatMap {
          case true => mapDefaultModels(accountId)
          case false => revertCreatedAccount(accountId, savedEnvironmentIds, apiKeysId, publicApiKeysId)
        }
    } recover {
      case e: Exception =>
        LOGGER.error(s"Account creation failed", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountCreationFailed))
    }
  }

  private def generateEncryptionKeys(accountId: Long): Future[Boolean] = {
    encryptionKeysService.generate(AccountId(accountId))
      .map(_ => true) recover {
      case e: Throwable =>
        LOGGER.error(s"Unable to generate encryption keys", e)
        false
    }
  }

  private def mapDefaultModels(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    modelManagementClient.mapDefaultModels(accountId.toString).map{
      case Right(status) if status =>
        LOGGER.info(s"Associated default models for account $accountId")
        Right(true)
      case Right(_) =>
        LOGGER.error(s"Unable to associate default models for account $accountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.ModelMappingFailed))
      case Left(e) =>
        LOGGER.error(s"Unable to associate default models for account $accountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.ModelMappingFailed))
    }
  }

  private def revertCreatedAccount(accountId: Long, savedEnvironmentIds: Seq[Long], apiKeysId: Seq[Long], publicApiKeysId: Seq[Long]): Future[Left[ErrorResponse, Nothing]] = {
    daoAccount.revertCreatedAccount(accountId, savedEnvironmentIds, apiKeysId, publicApiKeysId) map {
      _ => Left(ErrorResponseFactory.get(ExceptionCodes.EncryptionKeyGenerationWhenAccountCreationFailed))
    } recover {
      case e: Exception =>
        LOGGER.error(s"Unable to revert created account $accountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.EncryptionKeyGenerationAndRevertAccountFailed))
    }
  }

  def activateAccounts(accountIds : List[Long]) : Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.updateIsActive(accountIds, clock).map {
      case count if count > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(UpdateAccountStatusFailed))
    } recover {
      case err : Exception =>
        LOGGER.info("Error occurred while modifying account status", err)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def updateAccountNamebyPublicId(publicId: String, accountName: String): Future[Either[ErrorResponse, Int]] = {
    if(StringUtils.isNotEmpty(accountName)) {
      daoAccount.updateAccountName(publicId, accountName).map {
        case count if count > 0 => Right(count)
        case _ => Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }else{
      Future.successful(Left(ErrorResponseFactory.get(InvalidInputFormat)))
    }
  }

  def updateIsDeleted(publicId: String, isDeleted: Boolean): Future[Either[ErrorResponse, Int]] = {
    daoAccount.updateIsDeleted(publicId, isDeleted).map {
      case count if count > 0 => Right(count)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def updateIsActive(publicId: String, isActive: Boolean): Future[Either[ErrorResponse, Int]] = {
    daoAccount.updateIsActive(publicId, isActive).map {
      case count if count > 0 => Right(count)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    } recover {
      case err : Exception =>
        LOGGER.info("Error occurred while modifying account status", err)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getAccountInfo(accountId: Long): Future[Either[ErrorResponse, DtoAccount]] = {
    daoAccount.getAccount(accountId).map {
      case Some(x:DtoAccount) => Right(x)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def searchAccounts(asr: AccountSearchRequest): Future[Either[ErrorResponse, Seq[AccountSearchResponse]]] = {
      daoAccount.searchAccounts(asr).map { accounts =>  Right(accounts.flatten.sortBy(_.accountId))}
  }

  def updateEmail(userId: Long, email: String): Future[Either[ErrorResponse, String]] = {
    val validEmail: Boolean = EmailValidator.getInstance().isValid(email)
    if (validEmail) {
      daoAccount.updateEmail(userId, email).map(_ => Right("Email updated successfully"))
    } else {
      Future.successful(Left(ErrorResponse(ExceptionCodes.InvalidRequestPayload.id, "Email should be valid one")))
    }

  }

  def updateConsentReason(accountId : Long,consentId : Int) : Future[Either[ErrorResponse, Int]] = {
    val dtoAccountConsentReason = DtoAccountConsentReason(accountId, consentId)
    daoAccount.updateConsentId((dtoAccountConsentReason)) map {
      case count if count > 0 => Right(count)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getConsentId(accountId:Long) : Future[Either[ErrorResponse, Int]] = {
    daoAccount.getConsentId(accountId) map {
      case Some(reason:DtoAccountConsentReason) => Right(reason.consentId)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def handlePasswordNotification(
                                  email: String,
                                  notificationAction: (String, String, String, String, Long) => Unit,
                                  logMessage: String
                                ): Future[Either[ErrorResponse, Boolean]] = {
    inactiveUserService.getPasswordResetLinkAsActivationDetails(List(email)).flatMap {
      case Right(activationDetails) =>
        activationDetails.headOption match {
          case Some(activationDetail) =>
            LOGGER.info(s"$logMessage to $email")
            notificationAction(
              activationDetail.firstname,
              activationDetail.surname,
              email,
              activationDetail.activationCode.getOrElse(""),
              0
            )
            Future.successful(Right(true))
          case None =>
            LOGGER.info(s"User with Email id not found: $email")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
        }
      case Left(e) =>
        LOGGER.info(s"Error occurred while generating the activation code for $email")
        Future.successful(Left(e))
    }
  }

  def resetPassword(email: String): Future[Either[ErrorResponse, Boolean]] = {
    Future.successful(Right(true))
  }

  def sendOnboardMail(email: String): Future[Either[ErrorResponse, Boolean]] = {
    Future.successful(Right(true))
  }

}
