package me.socure.account.service

import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.constants.SubscriptionTypes
import me.socure.model.BusinessUserRoles.BusinessUserRole
import me.socure.model.account.ProductAccountTypes.ProductAccountType
import me.socure.model.account.{ProductAccountTypes, ProductProvisioningTypes}
import me.socure.model.{AccountProducts, BusinessUserRoles, ErrorResponse, UpdateProductsRequest}
import me.socure.storage.slick.dao.{DaoAccountPermission, DaoEnvironment, DaoProduct}
import me.socure.storage.slick.tables.account.{DtoAccountPermission, DtoProduct, DtoProductOverride}
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class ProductService(accountHierarchyService: AccountHierarchyService,
                     dashboardAccountServiceV2: DashboardAccountServiceV2,
                     daoProduct: DaoProduct,
                     daoAccountPermission: DaoAccountPermission,
                     daoEnvironment: DaoEnvironment)(implicit ec: ExecutionContext) {
  private val logger: Logger = LoggerFactory.getLogger(getClass)

  private val hidePermissionList = List(275, 276, 277)

  def getProductsForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[AccountProducts]]] = {
    def getAccountProducts(results: Seq[((DtoProduct, Option[DtoProductOverride]), Option[DtoAccountPermission])]): Seq[AccountProducts] = {
      results.flatMap { case ((product, productOverride), accountPermission) =>
        if (!hidePermissionList.contains(product.businessUserRoleId)) {
          Some(AccountProducts(
            id = product.id,
            name = product.name,
            businessUserRoleId = product.businessUserRoleId,
            provisioningType = productOverride.map(_.provisioningType).getOrElse(product.provisioningType),
            parentId = product.parentId,
            order = product.order,
            defaultState = product.defaultState,
            provisioned = accountPermission.isDefined,
            enabled = accountPermission.exists(_.enabled),
            createdBy = accountPermission.flatMap(_.createdBy),
            updatedBy = accountPermission.flatMap(_.updatedBy),
            createdAt = accountPermission.flatMap(_.createdAt),
            updatedAt = accountPermission.flatMap(_.updatedAt)
          ))
        } else None
      }
    }

    // check if account exists and see internal or not to include test only flags
    // get parent account type to fetch provisioningType
    // use current account id to check for provisioningType override

    val internalAccountFuture = accountHierarchyService.isInternal(accountId)
    val parentIdFuture = accountHierarchyService.getRootParent(accountId)

    internalAccountFuture zip parentIdFuture flatMap { results =>
      if (results._1.isEmpty) {
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
      } else {
        val isInternal = results._1.get
        val parentId = results._2

        val parentAccountTypeFuture = accountHierarchyService.getAccountHierarchyByAccountId(parentId) map {
          case Left(_) => ProductAccountTypes.DIRECT_CUSTOMER
          case Right(accountHierarchy) => ProductAccountTypes.byId(accountHierarchy.accountType).getOrElse(ProductAccountTypes.DIRECT_CUSTOMER)
        }

        parentAccountTypeFuture flatMap { parentAccountType =>
          daoProduct.getProductsForAccount(accountId, parentId, isInternal, parentAccountType) flatMap { results: Seq[((DtoProduct, Option[DtoProductOverride]), Option[DtoAccountPermission])] =>
            val accountProducts = getAccountProducts(results)
            if(accountId.equals(parentId)) {
              Future.successful(Right(accountProducts))
            } else {
              val updatedAccountProducts = daoAccountPermission.getByAccountAndStatus(parentId, true) map { permissions =>
                Right(accountProducts.map { product =>
                  permissions.exists(_.permission==product.businessUserRoleId) match {
                    case true => product.copy(allowEditing = Some(true))
                    case false => product.copy(allowEditing = Some(false))
                  }
                })
              }
              updatedAccountProducts
            }
          }
        }
      }
    }
  }

  def updateProductsForAccount(updateProductsRequest: UpdateProductsRequest): Future[Either[ErrorResponse, Boolean]] = {
    // check if account exists and see internal or not to include test only flags
    // get parent account type to fetch provisioningType
    // use current parent id to check for provisioningType override
    val accountId = updateProductsRequest.accountId
    val internalAccountFuture = accountHierarchyService.isInternal(accountId)
    val parentIdFuture = accountHierarchyService.getRootParent(accountId)
    internalAccountFuture zip parentIdFuture flatMap { results =>
      if (results._1.isEmpty) {
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
      } else {
        val isInternal = results._1.get
        val parentId = results._2

        val parentAccountTypeFuture = accountHierarchyService.getAccountHierarchyByAccountId(parentId) map {
          case Left(_) => ProductAccountTypes.DIRECT_CUSTOMER
          case Right(accountHierarchy) => ProductAccountTypes.byId(accountHierarchy.accountType).getOrElse(ProductAccountTypes.DIRECT_CUSTOMER)
        }

        parentAccountTypeFuture flatMap { parentAccountType =>

          val productsToBeUpdated = updateProductsRequest.products
          val productsToBeUpdatedIds = updateProductsRequest.products.map(_.id).toSet

          val productsAvailableTuple = daoProduct.getProductsWithOverrideForAccount(parentId, isInternal, parentAccountType) map { results =>
            results.map(record => (record._1.id, if (record._2.isDefined) record._1.copy(provisioningType = record._2.get.provisioningType) else record._1)).toMap
          }

          productsAvailableTuple flatMap { allProducts =>

            val allProductIds = allProducts.keySet
            val allCascadeProductIds = allProducts.values.filter(_.provisioningType.equals(ProductProvisioningTypes.CASCADE)).map(_.id).toSet
            val allInheritProductIds = allProducts.values.filter(_.provisioningType.equals(ProductProvisioningTypes.INHERIT)).map(_.id).toSet

            val validUpdateList = productsToBeUpdatedIds subsetOf allProductIds // updating products should be subset of all products

            val cascadeProductsToBeUpdated = productsToBeUpdatedIds intersect allCascadeProductIds // find cascade products to be updated

            val cascadeProductUpdateAtChild = if (parentId == accountId) false else cascadeProductsToBeUpdated.nonEmpty // can't update cascade products at child, only at root allowed
            //cascadeProductUpdateAtChild is removed as From Super Admin we should be able to update all products
            if (!validUpdateList) {
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidProductUpdate)))
            } else {

              //              1. Get all sub accounts
              //              2. Loop over all products
              //              3. For each product, check if it's cascade, then push it for all sub accounts, if not just enable for parent

              val subAccountIdsFuture = dashboardAccountServiceV2.getParentsSubAccounts(accountId) map {
                case Left(_) => Set.empty[Long]
                case Right(subAccountIds) => subAccountIds
              }

              subAccountIdsFuture flatMap { subAccountIds =>

                  val productIdToPermission = (productsToBeUpdatedIds map (allProducts(_)) map (product => (product.id, product.businessUserRoleId))).toMap // product id to businessuserrole id

                  val allAccountIds = subAccountIds + updateProductsRequest.accountId // parent + sub accounts

                  val parentSubAccountExistingPermissionsFuture = daoAccountPermission.getByAccountIdsAndProductsId(allAccountIds,
                    productIdToPermission.values.toSet) map { records => records.groupBy(ap => (ap.accountId, ap.permission))
                  }

                  parentSubAccountExistingPermissionsFuture flatMap { existingPermissions => // all existing permissions for all accounts and for the products to be updated

                    def getAccountPermissionTuple(currentAccountId: Long): (Seq[DtoAccountPermission], Seq[DtoAccountPermission], Seq[DtoAccountPermission]) = {

//                      loop over all products to find if the product has to be inserted or updated or deleted for this account

                      productsToBeUpdated.foldLeft((Seq.empty[DtoAccountPermission], Seq.empty[DtoAccountPermission], Seq.empty[DtoAccountPermission]))
                      { case ((insertList, updateList, deleteList), productRequest) =>

                        val permission = productIdToPermission(productRequest.id) // get the businessuserroleid
                        val product = allProducts(productRequest.id)

                        existingPermissions.get((currentAccountId, permission)) match {
                          case None => // permission is not existing for this account

                            if (productRequest.provisioned) // if we have to provision it

                              if (currentAccountId == accountId || (allCascadeProductIds ++ allInheritProductIds).contains(product.id)) // if it's parent or a cascade/inherit product, use the state as well

                                (insertList ++ Seq(DtoAccountPermission(currentAccountId, permission, productRequest.enabled, Some(updateProductsRequest.updatedBy), Some(updateProductsRequest.updatedBy),
                                  Some(DateTime.now()), Some(DateTime.now()))),
                                  updateList,
                                  deleteList)

                              else // don't use state, just provision

                                (insertList ++ Seq(DtoAccountPermission(currentAccountId, permission, enabled = false, Some(updateProductsRequest.updatedBy), Some(updateProductsRequest.updatedBy),
                                  Some(DateTime.now()), Some(DateTime.now()))),
                                  updateList,
                                  deleteList)

                            else  // not present, not provisioned now, so leave it as it is

                              (insertList, updateList, deleteList)

                          case Some(accountPermissions) => // permission already exists

                            if (productRequest.provisioned) // want to provision

                              if (currentAccountId == accountId || (allCascadeProductIds ++ allInheritProductIds).contains(product.id)) // if it's parent or a cascade/inherit product, use the state as well

                                (insertList,
                                  updateList ++ accountPermissions.map(_.copy(enabled = productRequest.enabled, updatedBy = Some(updateProductsRequest.updatedBy), updatedAt = Some(DateTime.now()))),
                                  deleteList)

                              else // already provisioned product, don't have to change state bec adhoc, so leave it

                                (insertList, updateList, deleteList)

                            else// already provisioned product, now unprovision it, so delete it

                              (insertList,
                                updateList,
                                deleteList ++ accountPermissions)

                        }
                      }
                    }

//                  loop through all accounts and check what all permissions to be inserted, updated and deleted for that account

                    val (apInsertList, apUpdateList, apDeleteList) = allAccountIds.foldLeft((Seq.empty[DtoAccountPermission], Seq.empty[DtoAccountPermission], Seq.empty[DtoAccountPermission]))
                    { case ((insertList, updateList, deleteList), accountId) =>
                      val (accountInsertList, accountUpdateList, accountDeleteList) = getAccountPermissionTuple(accountId)

                      (
                        insertList ++ accountInsertList,
                        updateList ++ accountUpdateList,
                        deleteList ++ accountDeleteList
                      )

                    }

                    val disableList = apUpdateList.filter(!_.enabled) ++ apDeleteList
                    val enableList = apInsertList ++ apUpdateList.filter(_.enabled)
                    val enableWLSubscriptionAccounts = enableList.filter(f => f.permission.equals(BusinessUserRoles.WATCHLIST_MONITORING_SUBSCRIPTION.id) && f.enabled).map(_.accountId)
                    val disableWLSubscriptionAccounts = disableList.filter(f => f.permission.equals(BusinessUserRoles.WATCHLIST_MONITORING_SUBSCRIPTION.id)).map(_.accountId)
                    val enableDVSubscriptionAccounts = enableList.filter(f => f.permission.equals(BusinessUserRoles.DOCUMENT_VERIFICATION_SUBSCRIPTION.id) && f.enabled).map(_.accountId)
                    val disableDVSubscriptionAccounts = disableList.filter(f => f.permission.equals(BusinessUserRoles.DOCUMENT_VERIFICATION_SUBSCRIPTION.id)).map(_.accountId)
                    for {
                      enableWLSubscriptionEnvironments <- daoEnvironment.getEnvironments(enableWLSubscriptionAccounts.toSet)
                      disableWLSubscriptionEnvironments <- daoEnvironment.getEnvironments(disableWLSubscriptionAccounts.toSet)
                      enableDVSubscriptionEnvironments <- daoEnvironment.getEnvironments(enableDVSubscriptionAccounts.toSet)
                      disableDVSubscriptionEnvironments <- daoEnvironment.getEnvironments(disableDVSubscriptionAccounts.toSet)

                      updateStatus <- daoAccountPermission.updateAccountPermissionsAndSubscriptions(apInsertList,
                        apUpdateList,
                        apDeleteList,
                        enableWLSubscriptionEnvironments,
                        disableWLSubscriptionEnvironments,
                        enableDVSubscriptionEnvironments,
                        disableDVSubscriptionEnvironments).map(Right(_)) // do it in a transaction
                    } yield updateStatus
                  }
              }

            }
          }
        }
      }
    }

  }

  def getProductByBusinessUserRoleId(businessUserRoleId: Int): Future[Seq[DtoProduct]] = {
    daoProduct.getProductByBusinessUserRoleId(businessUserRoleId)
  }

  def getProductByBusinessUserRoleId(businessUserRoleIds: Set[Int], accountType: ProductAccountType): Future[Seq[DtoProduct]] = {
    daoProduct.getProductForModules(businessUserRoleIds, accountType)
  }

}
