package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CouldNotAddSFTPUserForAccount, UnableToListSFTPUsers}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountSftpUser
import me.socure.storage.slick.dao.DaoAccountSftpUser
import me.socure.storage.slick.tables.account.DtoAccountSftpUser
import org.joda.time.DateTime
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class AccountSftpUserService(daoAccountSftpUser: DaoAccountSftpUser,
                              clock: Clock) (implicit ec : ExecutionContext) {
  private val logger = LoggerFactory.getLogger(classOf[AccountSftpUserService])

  def saveAccountSftpUser(accountId : Long, sftpUser: String) : Future[Either[ErrorResponse, Boolean]] = {
    if(isValidSftpUser(sftpUser)) {
      daoAccountSftpUser.canAddSftpUser(accountId).flatMap {
        case true =>
          logger.info(s"Can Add SFTP User for $accountId")
          val currentTime = clock.now
          daoAccountSftpUser.saveSftpUser(DtoAccountSftpUser(0, accountId, sftpUser, currentTime, currentTime)) map {
            case i if i == 1 =>
              logger.info(s"SFTP User: $sftpUser, added for account:$accountId")
              Right(true)
            case _ =>
              logger.info(s"SFTP User: $sftpUser, not added for account:$accountId")
              Left(ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount))
          }
        case false =>
          logger.info(s"Cannot add SFTP User: $sftpUser, for account: $accountId. Either the account is not active or it does not have valid PGP Keys")
          Future.successful(Left(ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount)))
      } recover {
        case t: Throwable =>
          logger.info(s"Unable to Save the SFTP User: $sftpUser, for account: $accountId", t)
          Left(ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount))
      }
    } else {
      logger.info(s"Unable to Save invalid SFTP User: $sftpUser, for account: $accountId")
      Future.successful(Left(ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount)))
    }
  }

  def listSftpUsers() : Future[Either[ErrorResponse, Seq[AccountSftpUser]]] = {
    daoAccountSftpUser.listSftpUsers().map {
      case list if list.nonEmpty =>
        Right(list.map{l => toAccountSftpUser(l._2, l._1.id, l._1.accountId, l._1.sftpUser, l._1.createdAt, l._1.updatedAt)})
      case _ => Right(Seq.empty[AccountSftpUser])
    } recover {
      case t: Throwable =>
        logger.info(s"Unable to list the SFTP Users", t)
        Left(ErrorResponseFactory.get(UnableToListSFTPUsers))
    }
  }

  private def toAccountSftpUser(accountName: String, id: Long, accountId: Long, sftpUser: String, createdAt: DateTime, updatedAt: DateTime) = {
    AccountSftpUser(id = id,
      accountId = accountId,
      accountName = accountName,
      sftpUser = sftpUser,
      createdAt = createdAt,
      updatedAt = updatedAt)
  }

  private def isValidSftpUser(sftpUser: String) = {
    sftpUser.trim.nonEmpty
  }

}
