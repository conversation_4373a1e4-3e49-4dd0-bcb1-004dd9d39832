package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.platform.PlatformPermissionScope
import me.socure.constants.{AccountTypes, DashboardUserPermissions, Domains, EnvironmentTypes, SystemDefiendRolesPermissions}
import me.socure.convertors.{AccountConvertors, DashboardEnvironmentConvertors}
import me.socure.model.ErrorResponse
import me.socure.model.account._
import me.socure.model.user.authorization.UserAuth
import me.socure.model.user.platform.{PlatformEnvironmentPermissions, PlatformWorkflowPermission}
import me.socure.storage.slick.dao.{DaoAccountUIConfiguration, DaoAccountV2, DaoSubscriptions}
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}
import org.slf4j.{<PERSON><PERSON>, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserAccountAssociationService(daoAccountV2: DaoAccountV2,
                                    daoAccount: DaoAccount,
                                    daoBusinessUser: DaoBusinessUser,
                                    daoSubscriptions: DaoSubscriptions,
                                    v2Validator: V2Validator,
                                    daoUIAccountConfiguration: DaoAccountUIConfiguration,
                                    passwordService: PasswordService
                                   )(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getUserAccountAssociationsByUserId(userId: Long): Future[Either[ErrorResponse, Seq[UserAccountAssociation]]] = {
    daoAccountV2.getUserAccountAssociationsByUserId(userId) flatMap { dtoUserAccountAssociationSeq =>
      val userAccountAssociationIds = dtoUserAccountAssociationSeq.map(_.id)
      daoAccountV2.fetchUserAccountRoles(userAccountAssociationIds) map { uaaIdVsRolesMap =>
        Right(dtoUserAccountAssociationSeq.map(dtoUserAccountAssociation => AccountConvertors.toUserAccountAssociation(dtoUserAccountAssociation, uaaIdVsRolesMap.getOrElse(dtoUserAccountAssociation.id, Set.empty))))
      }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching user account associations", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
    }
  }

  def getUserAccountAssociationsByEmail(email: String): Future[Either[ErrorResponse, Seq[UserAccountAssociation]]] = {
    daoAccountV2.getUserAccountAssociationsByEmail(email) flatMap { dtoUserAccountAssociationSeq =>
      val userAccountAssociationIds = dtoUserAccountAssociationSeq.map(_.id)
      daoAccountV2.fetchUserAccountRoles(userAccountAssociationIds) map { uaaIdVsRolesMap =>
        Right(dtoUserAccountAssociationSeq.map(dtoUserAccountAssociation => AccountConvertors.toUserAccountAssociation(dtoUserAccountAssociation, uaaIdVsRolesMap.getOrElse(dtoUserAccountAssociation.id, Set.empty))))
      }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching user account associations", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
    }
  }

  def getUserAccountAssociation(userId: Long, accountId: Long): Future[Either[ErrorResponse, Option[UserAccountAssociation]]] = {
    daoAccountV2.getUserAccountAssociationWithRoles(userId, accountId) flatMap { uaraOPt =>
      val userAccountAssociation = uaraOPt.headOption.map { uara =>
        val userRoles = uara._2.map(_.userRoleId.get).toSet
        UserAccountAssociation(uara._1.id, uara._1.businessUserId, uara._1.accountId, uara._1.status, uara._1.isPrimaryUser, Some(userRoles), None)
      }
      Future.successful(Right(userAccountAssociation))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching user account associations for user id and account id", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
    }
  }


  def getRolesByUserIdAndAccountId(userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[Long]]] = {
    daoAccountV2.fetchUserAccountRoles(userId, accountId) map { roles =>
      Right(roles)
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching user roles by user_id and account_id", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserRolesFetchError))
    }
  }

  def insertUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    val dtoUserAccountAssociation = AccountConvertors.toDtoUserAccountAssociation(userAccountAssociationInput)
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(userAccountAssociationInput.updatedBy)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.USERS_MODIFY.id, DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateAccountAccess(userAccountAssociationInput.accountId, uaa.accountId)
      res <- daoAccountV2.insertUserAccountAssociation(dtoUserAccountAssociation) flatMap { dtoUserAccountAssociationRes =>
        userAccountAssociationInput.userRoles match {
          case Some(userRoles) =>
            daoAccountV2.insertOrUpdateUserAccountRoleAssociation(dtoUserAccountAssociationRes.id, userRoles) map {
              case Some(_) =>
                Right(1)
              case _ =>
                logger.info("Error occurred while inserting roles")
                Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole))
            }
          case _ =>
            logger.info("Roles not provided in the request/ No change in roles.")
            Future.successful(Right(1))
        }
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not insert user account association", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not insert user account association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation))
    }
  }

  def insertUserAccountAssociations(accountId: Long, userAssociationInputSeq: Seq[UserAssociationInput], updatedBy: Long): Future[Seq[UserAssociationResponse]] = {
    val userAccountAssociationResult = userAssociationInputSeq map { userAssociationInput =>
      val dtoUserAccountAssociation = AccountConvertors.toDtoUserAccountAssociation(userAssociationInput, accountId, updatedBy)
      val userRoles = userAssociationInput.userRoles.getOrElse(Set.empty[Long])
      (for {
        uaa <- daoAccountV2.getUserAccountAssociationById(updatedBy)
        _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.USERS_MODIFY.id, DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
        _ <- v2Validator.validateAccountAccess(accountId, uaa.accountId)
        res <- daoAccountV2.insertUserAccountAssociationWithRoles(dtoUserAccountAssociation, userRoles) map {
          case res if res > 0 =>
            Right(res)
          case _ =>
            logger.info("Error occurred while inserting user account association")
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation))
        }
      } yield res) recover {
        case ex: ErrorResponseException =>
          logger.info("Could not insert user account association", ex)
          Left(ex.errorResponse)
        case e: Exception =>
          logger.info("Could not insert user account association", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation))
      }
    }
    Future.sequence(userAccountAssociationResult).map(_.map(_.fold(err => UserAssociationResponse("error", err.message), res => UserAssociationResponse("ok", "true"))))
  }

  def updateUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    val dtoUserAccountAssociation = AccountConvertors.toDtoUserAccountAssociation(userAccountAssociationInput)
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(userAccountAssociationInput.updatedBy)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.USERS_MODIFY.id, DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateAccountAccess(userAccountAssociationInput.accountId, uaa.accountId)
     res <- daoAccountV2.updateUserAccountAssociation(dtoUserAccountAssociation) flatMap {
       case res if res > 0 =>
         userAccountAssociationInput.userRoles match {
           case Some(userRoles) =>
             daoAccountV2.insertOrUpdateUserAccountRoleAssociation(dtoUserAccountAssociation.id, userRoles) map {
               case Some(_) =>
                 Right(res)
               case _ =>
                 logger.info("Error occurred while inserting roles")
                 Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole))
             }
           case _ =>
             logger.info("Roles not provided in the request/ No change in roles.")
             Future.successful(Right(res))
         }
       case _ =>
         logger.info(s"Could not update user account association with id - ${dtoUserAccountAssociation.id}")
         Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserAccountAssociation)))
     }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not update user account association", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not update user account association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserAccountAssociation))
    }
  }

  def deleteUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(userAccountAssociationInput.updatedBy)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.USERS_DELETE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateAccountAccess(userAccountAssociationInput.accountId, uaa.accountId)
      res <- daoAccountV2.deleteUserAccountAssociation(userAccountAssociationInput.userId, userAccountAssociationInput.accountId) map {
        case res if res > 0 =>
          logger.info(s"Association with userId - ${userAccountAssociationInput.userId} and accountId - ${userAccountAssociationInput.accountId} deleted successfully")
          Right(res)
        case _ =>
          logger.info(s"Could not delete user account association  with userId - ${userAccountAssociationInput.userId} and accountId - ${userAccountAssociationInput.accountId}")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserAccountAssociation))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not delete user account association  with userId - ${userAccountAssociationInput.userId} and accountId - ${userAccountAssociationInput.accountId}", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Could not delete user account association  with userId - ${userAccountAssociationInput.userId} and accountId - ${userAccountAssociationInput.accountId}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserAccountAssociation))
    }
  }

  def getUserAccountOrAccountOwnerAssociation(userId: Long, accountId: Long): Future[Option[DtoUserAccountAssociation]] ={
      for{
        uaa <-  daoAccountV2.getUserAccountAssociation(userId, accountId)
        res <- v2Validator.validateHasAccountOwnerInParentHierarchy(userId, accountId) map {
          case accountOwnerUaa if accountOwnerUaa.length > 0 =>
            accountOwnerUaa.headOption
          case _ =>
            uaa
        }
      } yield res
  }

  def validateUserAccountAssociation(userId: Long, accountId: Long): Future[Either[ErrorResponse, UserAuth]] = {
    getUserAccountOrAccountOwnerAssociation(userId, accountId) flatMap {
      case Some(dtoUserAccountAssociation) =>
        val futureBusinessUser: Future[Option[DtoBusinessUser]] = daoBusinessUser.getUser(userId)
        val futureAllEnv: Future[Map[DtoEnvironment, Seq[DtoApiKey]]] = daoBusinessUser.getEnvironmentsWithApiKeysByAccountId(accountId)
        val futureAccount: Future[Option[DtoAccount]] = daoAccount.getAccount(accountId)
        val futureAssociatedAccount: Future[Seq[AccountIdName]] = daoAccountV2.getAssociatedAccounts(userId)
        val futurePermission: Future[Seq[DtoAccountPermission]] = daoAccount.getAccountPermissionsWithParentFeatureFlag(accountId)
        val subscriptionsFuture: Future[Seq[Subscription]] = daoSubscriptions.fetchSubscriptionsForLogin(accountId)
        val futureDashboardUserPermissions: Future[Map[Int, Set[Int]]] = daoAccountV2.getDashboardUserPermissions(dtoUserAccountAssociation.id, accountId, userId)
        val dtoAccountUIConfigurationOptFuture = daoUIAccountConfiguration.getUIAccountConfiguration(accountId)
        val isAccountOwnerFuture = daoAccountV2.isAccountOwner(dtoUserAccountAssociation.id)
        val passwordUpdatedAt = passwordService.getPasswordUpdatedAt(userId)
        val isProgram = daoAccount.getSponsorBankIdsByProgramId(accountId) map {
          case sponsorBankIds if sponsorBankIds.nonEmpty => Some(true)
          case _ => Some(false)
        }
        val tosTimeStamp = daoAccount.getTOSTimeStamp(userId)
        val isAnalyticsHistoricalDataLoadedFuture = daoAccount.getAnalyticsGlobalinfo()
        val futureAccountHierarchy = daoAccountV2.getAccountHierarchyByAccountId(accountId)
        val userAuth = for {
          businessUser <- futureBusinessUser
          account <- futureAccount
          accounts <- futureAssociatedAccount
          accountOwnerAccessAccounts <- v2Validator.getAccountOwnerAssociatedAccounts(userId, accounts)
          rootAccHierarchy <- v2Validator.getRootParentAccountHierarchy(accountId)
          rootAccountDetail <- getRootAccountDetail(rootAccHierarchy)
          accountEnvWorkflowMapping <- getAssociatedAccountsAndWorkflowIds(rootAccHierarchy.map(_.accountType), accounts.map(_.id).toSet)
          platformEnvPermissions <- getPlatformEnvironmentPermissions(userId, account.getOrElse(throw new Exception("Failed to retrieve account id from database")), rootAccHierarchy.map(_.accountType), accountEnvWorkflowMapping)
          programs <- daoAccount.getProgramIdNameByUserId(userId)
          env <- futureAllEnv
          permissions <- futurePermission
          subscriptions <- subscriptionsFuture
          dashboardCustomUserPermissions <- futureDashboardUserPermissions
          dashboardSystemUserPermissions <- daoAccountV2.getDashboardUserSystemPermissions(dtoUserAccountAssociation.id, accountId, userId, rootAccHierarchy.map(_.accountType))
          dtoAccountUIConfigurationOpt <- dtoAccountUIConfigurationOptFuture
          isAccountOwner <- isAccountOwnerFuture
          passwordUpdatedAt <-  passwordUpdatedAt
          isProgram <- isProgram
          isAnalyticsHistoricalDataLoaded <- isAnalyticsHistoricalDataLoadedFuture
          accountHierarchy <- futureAccountHierarchy
          tosTimeStamp <- tosTimeStamp
        } yield AccountConvertors.toUserAuth(businessUser.getOrElse(throw new Exception("Failed to retrieve business user from database")),
          account.getOrElse(throw new Exception("Failed to retrieve account id from database")),
          env,
          permissions,
          subscriptions,
          SystemDefiendRolesPermissions.mergePermissions(dashboardCustomUserPermissions, dashboardSystemUserPermissions),
          dtoUserAccountAssociation,
          accounts ++ accountOwnerAccessAccounts,
          dtoAccountUIConfigurationOpt,
          isAccountOwner,
          passwordUpdatedAt,
          passwordService.getPasswordExpireAt(passwordUpdatedAt),
          Some(programs),
          isProgram,
          isAnalyticsHistoricalDataLoaded,
          accountHierarchy.map(_.accountType),
          tosTimeStamp,
          rootAccHierarchy,
          rootAccountDetail,
          platformEnvPermissions)
        userAuth.map(Right(_))
      case _ =>
        logger.info("User account association doesn't exist")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation)))
    } recover {
      case err =>
        logger.info("Error occurred while checking for user account association", err)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToValidateUserAccountAssociation))
    }
  }

  private def getRootAccountDetail(rootAccHierarchy: Option[DtoAccountHierarchy]): Future[Option[DtoAccount]] = {
    if (rootAccHierarchy.isDefined) daoAccount.getAccount(rootAccHierarchy.get.accountId) else Future.successful(None)
  }

  private def getAssociatedAccountsAndWorkflowIds(rootAccType: Option[Int], accountIds: Set[Long]): Future[Map[Int, Map[Long, Seq[String]]]] = {
    if(rootAccType.isDefined && rootAccType.get == AccountTypes.DIRECT_EFFECTIV.id) {
      daoAccountV2.getAssociatedAccountsAndWorkflowIds(accountIds)
    } else {
      Future.successful(Map.empty)
    }
  }

  private def getPlatformEnvironmentPermissions(userId: Long, currentAccount: DtoAccount, rootAccountType: Option[Int], accountEnvWorkflows: Map[Int, Map[Long, Seq[String]]]): Future[Option[Seq[PlatformEnvironmentPermissions]]] = {
    if(rootAccountType.isDefined && rootAccountType.get == AccountTypes.DIRECT_EFFECTIV.id){
      val platformGlobalScopeDomains = Domains.getDomainsFilteredByPlatformScope(PlatformPermissionScope.GLOBAL.id)
      val platformWorkflowScopeDomains = Domains.getDomainsFilteredByPlatformScope(PlatformPermissionScope.WORKFLOW_SPECIFIC.id)

      daoAccountV2.getPlatformUserSystemPermissions(userId, rootAccountType).map { platformAccountEnvSysPermissions =>
        Some(accountEnvWorkflows.map { case (envType, accountWfMap) =>
          val workflowIds = accountWfMap.values.flatten.toSeq
          val currentAccountPermissions = platformAccountEnvSysPermissions.get(currentAccount.accountId)
            .map(permissions => DashboardEnvironmentConvertors.toPlatformPermissions(envType, permissions, platformGlobalScopeDomains))

          val workflowSpecificPermissions = accountWfMap.flatMap { case (wfAccountId, workflowIds) =>
            workflowIds.map { workflowId =>
              val wfPermissions = platformAccountEnvSysPermissions.get(wfAccountId)
                .map(permissions => DashboardEnvironmentConvertors.toPlatformPermissions(envType, permissions, platformWorkflowScopeDomains))
              PlatformWorkflowPermission(
                workflowId = workflowId,
                permissions = wfPermissions
              )
            }
          }.toSeq

          PlatformEnvironmentPermissions(
            envType = envType,
            envName = EnvironmentTypes.byId(envType).get.name,
            workflows = workflowIds,
            currentAccountPermissions = currentAccountPermissions,
            workflowSpecificPermissions = if (workflowSpecificPermissions.nonEmpty) Some(workflowSpecificPermissions) else None
          )
        }.toSeq)
      }
    } else Future.successful(None)
  }
}
