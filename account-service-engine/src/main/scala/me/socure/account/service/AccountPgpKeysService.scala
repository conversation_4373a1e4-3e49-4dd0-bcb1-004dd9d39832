package me.socure.account.service

import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.util.Base64
import com.amazonaws.services.kms.model.{DecryptRequest, EncryptRequest}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.Clock
import me.socure.common.kms.KmsService
import me.socure.common.pgp.PgpKeyPairGenerator
import me.socure.convertors.AccountConvertors
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.account.AccountDetails
import me.socure.model.encryption.KmsIdsConfig
import me.socure.model.pgp.{PgpKeysPair, PgpPrivateKey, PgpPublicKey}
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.storage.slick.dao.DaoAccountPgpKeys
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 14/05/2017.
  */
class AccountPgpKeysService(daoService : DaoAccountPgpKeys, pgpKeyGenerator: PgpKeyPairGenerator, kmsService: KmsService, kmsKey : KmsIdsConfig, supportMailId: String, clock : Clock)(implicit ec : ExecutionContext) {

  val logger : Logger = LoggerFactory.getLogger(getClass)
  private val mailId = "<EMAIL>"

  def createPgpKeys(accountId: Long, expiryInSeconds: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val validations = for {
      isValid <- daoService.isValidAccount(accountId)
      keyExists <- daoService.doesKeyExist(accountId)
      permission <- daoService.isPermissionAvailable(accountId, BusinessUserRoles.PgpEncryptionWithSubKey.id)
    } yield (isValid, keyExists, permission)
    validations.flatMap {
      case (false, _, _) =>
        Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
      case (_, true, _) =>
        Future.successful(Left(ErrorResponseFactory.get(PGPExistsAlready)))
      case (true, false, needSubKey) =>
        val keyPairs = if (needSubKey) {
          logger.info(s"Generating RSA key pairs with Subkeys for account $accountId")
          pgpKeyGenerator.generatePgpKeysWithSubkey(accountId.toString, supportMailId, expiryInSeconds)
        } else {
          logger.info(s"Generating RSA key pairs for account $accountId")
          pgpKeyGenerator.generatePgpKeys(accountId, supportMailId, expiryInSeconds)
        }
        (for {
          fPubKey <- encryptPgpKey(keyPairs.publicKey)
          fPriKey <- encryptPgpKey(keyPairs.privateKey)
        } yield (PgpPublicKey(fPubKey, needSubKey), PgpPrivateKey(fPriKey, needSubKey))).flatMap { encKeys =>
          daoService.save(AccountConvertors.getAccountPgpDto(accountId, PgpKeysPair(encKeys._1, encKeys._2), clock, needSubKey)).map {
            case ar if ar > 0 => Right(true)
            case _ => Left(ErrorResponseFactory.get(UnknownError))
          }
        }
    }
  }

  def dactivatePgpKeys(accountId : Long) : Future[Either[ErrorResponse, Boolean]] = {
    logger.info(s"Deactivating PGP key pairs for account $accountId")
    daoService.doesKeyExist(accountId).flatMap {
      case true =>
        daoService.deactivateKey(accountId, clock).map {
          case i if i > 0 => Right(true)
          case _ => Left(ErrorResponseFactory.get(DeactivatePGPKeysFailed))
        }
      case false => Future.successful(Left(ErrorResponseFactory.get(NoPGPKeys)))
    }
  }

  def getAccountPgpPrivateKey(accountId : Long) : Future[Either[ErrorResponse, PgpPrivateKey]] = {
    logger.info(s"Getting private key for account $accountId")
    daoService.getKeysforAccount(accountId).flatMap {
      case Some(k) => decryptPgpKey(k.privateKey).map(kk => Right(PgpPrivateKey(kk, k.hasSubkey)))
      case _ =>
        logger.info(s"No Private key found for given account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(RecordsNotFound)))
    }
  }

  def getAllAccountPgpPrivateKey(accountId : Long) : Future[Either[ErrorResponse, Seq[PgpPrivateKey]]] = {
    logger.info(s"Getting all private keys for account $accountId")
    daoService.getAllKeysforAccount(accountId).flatMap {
      case keys => Future.sequence(keys.map(key => decryptPgpKey(key.privateKey).map(t => PgpPrivateKey(t, key.hasSubkey)))) map (result => Right(result))
      case _ =>
        logger.info(s"No Private key found for given account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(RecordsNotFound)))
    }
  }


  def getAccountPgpPublicKey(accountId : Long) : Future[Either[ErrorResponse, String]] = {
    logger.info(s"Getting public key for account $accountId")
    daoService.getKeysforAccount(accountId).flatMap {
      case Some(k) => decryptPgpKey(k.publicKey).map(kk => Right(kk))
      case _ =>
        logger.info(s"No Public key found for given account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(RecordsNotFound)))
    }
  }

  def getActivePgpAccountList() : Future[Either[ErrorResponse, List[AccountPgpInfo]]] = {
    logger.info("Getting active PGP account list")
    daoService.getActivePgpAccountList().map {
      case list  => Right(list.map(a => AccountPgpInfo(a._1, a._2, a._3, a._4)).toList)
      case _ => Left(ErrorResponseFactory.get(RecordsNotFound))
    }
  }

  def getActivePgpAccountWOPgpList() : Future[Either[ErrorResponse, List[AccountDetails]]] = {
    logger.info("Getting active PGP account with userlist")
    daoService.getActiveAccountListWOPgp().map {
      case list  => Right(list.filter(_._4.isEmpty).map(a => AccountDetails(a._1, a._2.getOrElse(""), a._3)).toList)
      case _ => Left(ErrorResponseFactory.get(RecordsNotFound))
    }
  }

  def doesPgpKeyExists(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoService.doesKeyExist(accountId).map(Right(_))
  }

  private def encryptPgpKey(key : String) : Future[String] = {
    logger.info("Call to KMS to encrypt given input")
    val keyId = kmsKey.value.headOption.map(_._2.value).getOrElse(throw new Exception("Invalid state of KMS configuration"))
    val req = new EncryptRequest().withKeyId(keyId).withPlaintext(ByteBuffer.wrap(key.getBytes(StandardCharsets.UTF_8)))

    kmsService.encrypt(req).map(r => Base64.getEncoder.encodeToString(r.getCiphertextBlob.array()))
  }

  private def decryptPgpKey(key : String) : Future[String] = {
    logger.info(s"Call to KMS to decrypt given input $key")
    val req = new DecryptRequest().withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(key.getBytes(StandardCharsets.UTF_8))))

    kmsService.decrypt(req).map(r => new String(r.getPlaintext.array(), StandardCharsets.UTF_8))
  }

}
