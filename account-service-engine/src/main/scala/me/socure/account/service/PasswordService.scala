package me.socure.account.service

import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccessForbidden, AccountOrUserLocked, InvalidActivationCode, UnknownError, UserNotFound, UsernamePasswordMismatch}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.{Clock, RealClock}
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.UserMagicTokenConstants.documentLinkGuestUser
import me.socure.constants.{DashboardUserPermissions, EnvironmentTypes, UserMagicTokenConstants}
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.AccountWithCreator
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.authorization.UserAuth
import me.socure.model.user.{ PasswordChangeForm, PasswordlessLoginCredential, UserCredential}
import me.socure.password.PasswordConstraintCheck
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoActivationToken, DtoBusinessUser, DtoDocumentLinkToken, DtoMagicToken}
import me.socure.user.MagicLinkAuditService
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 3/26/17.
  */
class PasswordService(storage: PasswordStorageService,
                      userService: DaoBusinessUser,
                      daoAccountV2: DaoAccountV2,
                      samlValidator: SamlValidator,
                      clock : Clock,
                      expireDays : Int,
                      v2Validator: V2Validator,
                      magicLinkAuditService: MagicLinkAuditService)(implicit ec: ExecutionContext) extends RealClock{
  val logger: Logger = LoggerFactory.getLogger(this.getClass)

  def setPassword(user: DtoBusinessUser, password: String): Future[Either[ErrorResponse, Boolean]] = {
    PasswordConstraintCheck.validatePasswordConstraint(user.email, user.firstName, user.lastName, password) match {
      case Right(true) =>
        storage.alreadyUsed(user.id, password).flatMap {
          case true =>
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordAlreadyUsed)))
          case false =>
            (for {
              pwd <- storage.updatePassword(user.id, password).map(_ => Right(true))
              _ <- userService.resetBadLoginMagicTokenCount(user.id)
            } yield pwd).recover {
              case e : Throwable =>
                logger.info("Could not update password", e)
                Left(ErrorResponseFactory.get(UnknownError))
            }
        }
      case Right(false) =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy)))
      case Left(x) =>
        Future.successful(Left(x))
    }
  }

  def setPasswordAndUnlockUser(user: DtoBusinessUser, password: String): Future[Either[ErrorResponse, Boolean]] = {
    PasswordConstraintCheck.validatePasswordConstraint(user.email, user.firstName, user.lastName, password) match {
      case Right(true) =>
        storage.alreadyUsed(user.id, password).flatMap {
          case true =>
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordAlreadyUsed)))
          case false =>
            (for {
              pwd <- storage.updatePassword(user.id, password).map(_ => Right(true))
              _ <- userService.resetBadLoginMagicTokenCount(user.id)
              _ <- userService.unlockUser(user.id, clock)
            } yield pwd).recover {
              case e : Throwable =>
                logger.info("Could not update password", e)
                Left(ErrorResponseFactory.get(UnknownError))
            }
        }
      case Right(false) =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy)))
      case Left(x) =>
        Future.successful(Left(x))
    }
  }

  def setPassword(form: PasswordChangeForm): Future[Either[ErrorResponse, Boolean]] = {

    userService.getUser(form.id).flatMap {
      case Some(user) =>
        setPassword(user, form.password)
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
    }
  }

  def setPasswordWithEmail(email: String, newPassword: String): Future[Either[ErrorResponse, Boolean]] = {
    userService.getUser(email).flatMap {
      case Some(user) =>
        setPassword(user, newPassword)
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
    }
  }

  def changePassword(email: String, currentPassword: String, newPassword: String): Future[Either[ErrorResponse, Boolean]] = {
    samlValidator.whenUserHasNoSaml(email = email) {
      userService.getUser(email).flatMap {
        case Some(user) =>
          storage.validate(user.id, currentPassword).flatMap {
            case true =>
              setPassword(user, newPassword)
            case false =>
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UsernamePasswordMismatch)))
          }
        case None =>
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
      }
    }
  }

  def passwordlessLoginChangePassword(email: String, newPassword: String): Future[Either[ErrorResponse, Boolean]] = {
    samlValidator.whenUserHasNoSaml(email = email) {
      userService.getUser(email).flatMap {
        case Some(user) =>
          if(!user.accountNonLocked) {
            setPasswordAndUnlockUser(user, newPassword)
          } else {
            setPassword(user, newPassword)
          }
        case None =>
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
      }
    }
  }

  def validate(credentials: UserCredential): Future[Option[DtoBusinessUser]] = {
    userService.getUser(credentials.username).flatMap {
      case Some(user) =>
        storage.validate(user.id, credentials.password).flatMap {
          case true =>{
            daoAccountV2.isAccountV2Provisioned(Set(user.accountId)) flatMap  {
              case false =>
                Future.successful(Some(user))
              case true =>
                daoAccountV2.getActiveUserAccountAssociation(user.id, user.accountId) flatMap {
                  case Some(_) =>
                    Future.successful(Some(user))
                  case _ =>
                    daoAccountV2.getAssociatedAccounts(user.id) map {
                      associatedAccounts =>
                        associatedAccounts.headOption match {
                          case None => None
                          case Some(associatedAccount) => Some(user.copy(accountId = associatedAccount.id))
                        }
                    }
                }
            }
          }

          case false => Future.successful(None)
        }
      case None => Future.successful(None)
    }
  }

  def validateUserCredentials(credentials: UserCredential): Future[Either[ErrorResponse, DtoBusinessUser]] = {
    userService.getUser(credentials.username).flatMap {
      case Some(user) =>
        storage.validate(user.id, credentials.password).flatMap {
          case true =>
            daoAccountV2.isAccountV2Provisioned(Set(user.accountId)) flatMap {
              case false =>
                Future.successful(Right(user))
              case true =>
                daoAccountV2.getActiveUserAccountAssociation(user.id, user.accountId) flatMap {
                  case Some(_) =>
                    Future.successful(Right(user))
                  case _ =>
                    daoAccountV2.getAssociatedAccounts(user.id) flatMap  {
                      associatedAccounts =>
                        associatedAccounts.headOption match {
                          case None => Future.successful(Left(ErrorResponseFactory.get(AccountOrUserLocked)))
                          case Some(associatedAccount) => Future.successful(Right(user.copy(accountId = associatedAccount.id)))
                        }
                    }
                }
            }
          case false => Future.successful(Left(ErrorResponseFactory.get(UsernamePasswordMismatch)))
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(UsernamePasswordMismatch)))
    }
  }

  def isUserLockedByAdmin(email: String): Future[Either[ErrorResponse, Boolean]] = {
    userService.getUser(email).flatMap {
      case Some(user) =>
        daoAccountV2.getAssociatedAccounts(user.id) flatMap {
          associatedAccounts =>
            associatedAccounts.headOption match {
              case None => Future.successful(Right(true))
              case Some(associatedAccount) => Future.successful(Right(false))
            }
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def setPasswordWithResetCode(resetCode: String, password: String): Future[Either[ErrorResponse, Boolean]] = {
    userService.getUserByResetToken(resetCode) flatMap {
      case Some(user) =>
        setPasswordAndUnlockUser(user, password).flatMap {
          case Left(e) => Future.successful(Left(e))
          case Right(false) => Future.successful(Right(false))
          case Right(true) => userService.invalidatePasswordResetTokenByUserId(user.id).map(Right.apply)
        }
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    }
  }

  def setPasswordWithActivationCode(activationCode: String, password: String): Future[Either[ErrorResponse, Boolean]] = {
    userService.getUserByActivationCode(activationCode) flatMap {
      case Some(user) =>
        setPassword(user, password).flatMap {
          case Left(e) => Future.successful(Left(e))
          case Right(false) => Future.successful(Right(false))
          case Right(true) => userService.invalidateActivationCodeByUserId(user.id).map(Right.apply)
        }
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    }
  }

  def forceResetPassword(userId: Long, accountWithCreator: Option[AccountWithCreator] = None): Future[Either[ErrorResponse, UserActivationDetails]] = {
    accountWithCreator match {
      case Some(ac) =>
        v2Validator.isValidV2AccountRequest(accountId = ac.accountId, creator = Some(ac.creator)) flatMap{
          case true => resetPasswordV2(userId, ac)
          case false => v2Validator.isValidBusinessUser(ac.accountId, userId) flatMap {
            case true => resetPasswordV1(userId)
            case false => Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
          }
        }
      case None => resetPasswordV1(userId: Long)
    }
  }

  private def resetPasswordV2(userId: Long, accountWithCreator: AccountWithCreator): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val response = for {
      _ <- v2Validator.validateAccountAccess(accountWithCreator.accountId, accountWithCreator.creator.accountId)
      _ <- v2Validator.validatePermissions(accountWithCreator.creator.accountId, accountWithCreator.creator.userId, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- resetPasswordV1(userId: Long)
    } yield res
    response recover {
      case ex: ErrorResponseException =>
        logger.info("Could not reset password", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not reset password", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def resetPasswordV1(id: Long): Future[Either[ErrorResponse, UserActivationDetails]] = {
    userService.getUser(id) flatMap {
      case Some(user) if !user.isPrimaryUser =>
        samlValidator.userHaveAllSaml(user) flatMap {
          case onlySAMLAccounts if onlySAMLAccounts => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts)))
          case _ =>
            storage.invalidatePassword(id) flatMap {
              _ =>
                userService.createPasswordResetCode(DtoActivationToken(0, user.id, None, Some(clock.now()))).map {
                  case Some(uuid) =>
                    Right(
                      UserActivationDetails(
                        email = user.email,
                        firstname = user.firstName,
                        surname = user.lastName,
                        activationCode = Option(uuid)
                      )
                    )
                  case None =>
                    Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound))
                }.recover {
                  case e => Left(ErrorResponseFactory.get(e))
                }
            }
        }
      case Some(_) =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)))
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
    }
  }

  def isPasswordExpired(userId : Long): Future[Boolean] = {
    storage.isPasswordExpired(userId, clock.now(), expireDays)
  }

  def invalidatePassword(userId : Long): Future[Boolean] = {
    storage.invalidatePassword(userId)
  }

  def deleteAllPassword(userId: Long): Future[Boolean] = {
    storage.deletePassword(userId)
  }

  def hasValidPassword(userId: Long): Future[Boolean] = {
    storage.hasValidPassword(userId)
  }

  def getPasswordUpdatedAt(userId: Long): Future[Option[DateTime]] = {
    storage.getPasswordUpdatedAt(userId)
  }

  def getPasswordExpireAt(passwordUpdatedAt: Option[DateTime]): Option[DateTime] = {
    passwordUpdatedAt match {
      case Some(passwordUpdatedAt) => Some(passwordUpdatedAt.plusDays(expireDays))
      case _ => None
    }
  }

  def validatePasswordlessLogin(credential: PasswordlessLoginCredential): Future[Either[ErrorResponse, Option[DtoBusinessUser]]] = {
    userService.getUser(credential.email).flatMap {
      case Some(user) =>
        validateMagicTokenAndDeviceIdentifier(user.id, credential.magicToken, credential.deviceidentifier, credential.userAgent).flatMap {
          case Right(res) if res =>
            daoAccountV2.isAccountV2Provisioned(Set(user.accountId)) flatMap  {
              case false =>
                Future.successful(Right(Some(user)))
              case true =>
                daoAccountV2.getActiveUserAccountAssociation(user.id, user.accountId) flatMap {
                  case Some(_) =>
                    Future.successful(Right(Some(user)))
                  case _ =>
                    daoAccountV2.getAssociatedAccounts(user.id) map {
                      associatedAccounts =>
                        associatedAccounts.headOption match {
                          case None => Right(None)
                          case Some(associatedAccount) => Right(Some(user.copy(accountId = associatedAccount.id)))
                        }
                    }
                }
            }
          case Left(err) => Future.successful(Left(err))
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
    }
  }

  def validateDocsPasswordlessLogin(credential: PasswordlessLoginCredential): Future[Either[ErrorResponse, Option[UserAuth]]] = {
        validateDocumentLinkTokenAndDeviceIdentifier(credential.email, credential.magicToken, credential.deviceidentifier,credential.userAgent).flatMap {
          case Right(res) if res > 0 =>
             Future.successful(Right(Some(documentLinkGuestUser(credential.email))))
          case Left(err) => Future.successful(Left(err))
        }
  }

  def validateMagicTokenAndDeviceIdentifier(userId: Long, magicToken: String, deviceIdentifier: String, userAgent: String): Future[Either[ErrorResponse, Boolean]] = {
    userService.getMagicTokenForUser(userId).flatMap{
      case Some(dtoMagicToken) =>
        userService.updateMagicTokenClickCount(userId, dtoMagicToken.clickCount+1).flatMap {
          case rows if rows > 0 =>  isValidMagicTokenWithAudit(userId, dtoMagicToken, magicToken, userAgent).flatMap {
            case true =>
              if (deviceIdentifier.equals(dtoMagicToken.identifier)) {
                Future.successful(Right(true))
              } else {
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidDeviceIdentifier)))
              }
            case false =>
              deleteExpiredMagicTokenWithAudit(userId, dtoMagicToken.token, userAgent).map { deleted =>
                if (deleted > 0)
                  logger.info(s"Deleted expired Magic token for User Id: ${userId}")
                else
                  logger.error(s"Unable to delete expired Magic token for User Id: ${userId}")
                Left(ErrorResponseFactory.get(ExceptionCodes.MagicTokenExpired))
              }
          case _=>logger.error(s"Unable To Update Click Count: ${userId}")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
          }
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.MagicTokenForUserNotFound)))
    }
  }

  private def validateDocumentLinkTokenAndDeviceIdentifier(email: String, magicToken: String, deviceIdentifier: String, userAgent: String): Future[Either[ErrorResponse, Long]] = {
    userService.getDocumentLinkTokenForUser(email).flatMap{
      case Some(dtoDocumentLinkToken) =>
        userService.updateDocumentLinkTokenClickCount(email, dtoDocumentLinkToken.clickCount+1).flatMap {
          case rows if rows > 0 =>  isValidDocumentLinkTokenWithAudit(email, dtoDocumentLinkToken, magicToken, userAgent).flatMap {
            case true =>
              if (deviceIdentifier.equals(dtoDocumentLinkToken.identifier)) {
                Future.successful(Right(dtoDocumentLinkToken.id))
              } else {
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidDeviceIdentifier)))
              }
            case false =>
              deleteExpiredDocumentLinkTokenWithAudit(email, dtoDocumentLinkToken.token, userAgent).map { deleted =>
                if (deleted > 0)
                  logger.info(s"Deleted expired Magic token for email: $email")
                else
                  logger.error(s"Unable to delete expired Magic token for email: $email")
                Left(ErrorResponseFactory.get(ExceptionCodes.MagicTokenExpired))
              }
            case _=>logger.error(s"Unable To Update Click Count: email: $email")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
          }
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.DocumentLinkTokenForEmailNotFound)))
    }
  }

  private def deleteExpiredMagicTokenWithAudit(userId: Long, token: String, userAgent: String): Future[Int] = {
    for {
      resp <- userService.deleteExpiredMagicTokenByUserId(userId)
      _ <- magicLinkAuditService.saveMagicLinkAudit(userId = userId, token = token, deletedAt = Some(clock.now()), userAgent = Some(userAgent))
    } yield resp
  }

  private def deleteExpiredDocumentLinkTokenWithAudit(email: String, token: String, userAgent: String): Future[Int] = {
    for {
      resp <- userService.deleteExpiredDocumentLinkTokenByEmailId(email)
      _ <- magicLinkAuditService.saveDocumentLinkAudit(email = email, token = token, deletedAt = Some(clock.now()), userAgent = Some(userAgent))
    } yield resp
  }

  private def isValidMagicTokenWithAudit(userId: Long, dtoMagicToken: DtoMagicToken, magicToken: String, userAgent: String): Future[Boolean] = {
    val resp = isTokenValidAndNotExpired(dtoMagicToken.createdAt, dtoMagicToken.token, dtoMagicToken.clickCount, magicToken, UserMagicTokenConstants.expiryTime, UserMagicTokenConstants.maxClickCount)
    magicLinkAuditService.saveMagicLinkAudit(userId = userId, token = magicToken, isExpired = !resp, validatedAt = Some(clock.now()), userAgent = Some(userAgent)).map(_ => resp)
  }

  private def isTokenValidAndNotExpired(magicTokenCreatedAt: DateTime, dtoToken: String, dtoMagicTokenClickCount: Int, magicToken: String, expiryTime: Int, maxClickCount: Int): Boolean = {
    val isValidMagicToken = dtoToken.equals(magicToken)
    val isMagicTokenExpired = magicTokenCreatedAt.plusMinutes(expiryTime).isBeforeNow
    val isValidClickCount = dtoMagicTokenClickCount < maxClickCount
    val res = isValidMagicToken && !isMagicTokenExpired && isValidClickCount
    res
  }

  private def isValidDocumentLinkTokenWithAudit(email: String, dtoDocumentLinkToken: DtoDocumentLinkToken, magicToken: String, userAgent: String): Future[Boolean] = {
    val resp = isTokenValidAndNotExpired(dtoDocumentLinkToken.createdAt, dtoDocumentLinkToken.token, dtoDocumentLinkToken.clickCount, magicToken, UserMagicTokenConstants.expiryTimeForDocumentLink, UserMagicTokenConstants.maxClickCountForDocumentLink)
    magicLinkAuditService.saveDocumentLinkAudit(email = email, token = magicToken, isExpired = !resp, validatedAt = Some(clock.now()), userAgent = Some(userAgent)).map(_ => resp)
  }
}
