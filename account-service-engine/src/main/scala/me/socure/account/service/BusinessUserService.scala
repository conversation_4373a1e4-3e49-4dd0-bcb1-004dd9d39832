package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService}
import me.socure.account.saml.SamlValidator
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.utils.UUIDUtilities
import me.socure.account.validator.V2Validator
import me.socure.common.clock.{Clock, RealClock}
import me.socure.configuration.BadLoginConfig
import me.socure.constants.AccountManagementDefaults.{DefaultRateLimit, internalEmailDomains}
import me.socure.constants.platform.PlatformPermissionScope
import me.socure.constants.{AccountManagementDefaults, AccountTypes, DashboardUserPermissions, Domains, EnvironmentConstants, Status, SystemDefiendRolesPermissions, SystemDefinedRoles, UserMagicTokenConstants}
import me.socure.constants._
import me.socure.convertors.{AccountConvertors, BusinessUserConvertors, DashboardEnvironmentConvertors}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account.RateLimiterPublicAPI.RateLimiterPublicAPI
import me.socure.model.account.{AccountHierarchyInput, ApiKeyStatus, RateLimiterPublicAPI, SaveRateLimitingInput}
import me.socure.model.encryption.AccountId
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.DashboardUserRole.{ACCOUNTS, BATCH, USERS}
import me.socure.model.user._
import me.socure.model.user.authorization.{CognitoStatus, UserAuth, UserAuthV2, UserStatus}
import me.socure.model.user.platform.{PlatformEnvironmentPermissions, PlatformWorkflowPermission}
import me.socure.model.{BusinessUserRoles, ErrorResponse, RateLimitingEntryCreators, RateLimitingWindowSizes}
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.user._
import me.socure.user.MagicLinkAuditService
import org.jasypt.encryption.StringEncryptor
import org.joda.time.{DateTime, Seconds}
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}


/**
  * Created by gopal on 08/05/16.
  */
class BusinessUserService(daoBusiness: DaoBusinessUser,
                          daoAccount: DaoAccount,
                          daoEnvironment: DaoEnvironment,
                          config : BadLoginConfig,
                          passwordService: PasswordService,
                          encryptionKeysService: EncryptionKeysService,
                          samlValidator: SamlValidator,
                          clock: Clock,
                          daoPublicApiKey: DaoPublicApiKey,
                          daoSubscriptions: DaoSubscriptions,
                          daoAccountV2: DaoAccountV2,
                          daoRateLimit: DaoRateLimit,
                          pbeEncryptor: StringEncryptor,
                          rateLimitingService: RateLimitingService,
                          daoUIAccountConfiguration: DaoAccountUIConfiguration,
                          modelManagementClient: ModelManagementClient,
                          businessUserCommonService: BusinessUserCommonService,
                          v2Validator: V2Validator,
                          magicLinkAuditService: MagicLinkAuditService,
                          accountAutomationService: AccountAutomationService,
                          accountBundleAssociation: AccountBundleAssociationService,
                          sessionIdleTimeout: Int,
                          daoProspect: DaoProspect,
                          whitelistedEmailDomain: Set[String]
                         )(implicit ec : ExecutionContext) extends RealClock {

  val LOGGER: Logger = LoggerFactory.getLogger(this.getClass)

  val defaultAutoTimeoutInMinutes = 30
  val defaultIdleTimeoutInMinutes = if (sessionIdleTimeout > 30) 30 else sessionIdleTimeout

  def register(user: UserForm, isActive: Boolean) : Future[Either[ErrorResponse, Boolean]] = {
   LOGGER.info("User: ", user)
    businessUserCommonService.checkNApplyUserEmailAvailability(user.email).flatMap {
      case false =>
        LOGGER.info("Email already exists")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed)))
      case true =>
        val bu = BusinessUserConvertors.getDtoBusinessUser(user)
        val account = AccountConvertors.getAccount(user, pbeEncryptor, isActive)

        daoAccount.saveAccount(account).flatMap(savedAccount => {
          val dtoEnvironmentSeq: Seq[DtoEnvironment] = AccountConvertors.getDtoEnvironment(savedAccount.accountId, clock)
          daoBusiness.saveEnvironmentReturnIds(dtoEnvironmentSeq) flatMap (savedEnvironment => {
            val apiKeys: Seq[DtoApiKey] = savedEnvironment.map(e => {
              AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)
            })
            val encryptionKeysFuture = encryptionKeysService.generate(AccountId(savedAccount.accountId))

            encryptionKeysFuture.onFailure {
              case e: Throwable => LOGGER.info("Unable to generate encryption keys", e)
            }

            //saveApiKeys - inserts keys to tbl_api_key and updates time in tbl_environment, transactionally and so, the response will be 1 if updated
            val apiKeyResult = daoEnvironment.saveApiKeys(apiKeys, clock).map(_ == 1)

            val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
            val publicApiKeysResult: Future[Boolean] = daoPublicApiKey.saveOrUpdate(publicApiKeys).map(_ != 0)

            val userResult = daoBusiness.saveUser(bu.copy(accountId = savedAccount.accountId)) flatMap { savedUser =>
              val futureUpdatePassword = passwordService.setPassword(savedUser, user.password)
              val futureRole = daoBusiness.addEnvironmentUserRoles(userId = savedUser.id, roles = Set(BusinessUserRoles.ADMIN.id))
              Future.sequence(Seq(futureRole, futureUpdatePassword)).map(_ => true)
            }
            val defaultModelsFuture = modelManagementClient.mapDefaultModels(savedAccount.accountId.toString).map{
              case Right(status) if status =>
                LOGGER.info(s"Associated default models for account ${savedAccount.accountId}")
                true
              case Right(_) =>
                LOGGER.info(s"Unable to associate default models for account ${savedAccount.accountId}")
                false
              case Left(e) =>
                LOGGER.info(s"Unable to associate default models for account ${savedAccount.accountId}", e)
                false
            }
            val futureRateLimits: Future[Option[Int]] = saveRateLimits(dtoEnvironmentSeq)
            val adminDashboardRateLimit = rateLimitingService.saveRateLimits(
              SaveRateLimitingInput(savedAccount.accountId,
                EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
                RateLimiterPublicAPI.ADMIN_DASHBOARD.publicId,
                RateLimitingWindowSizes.SECOND.value,
                RateLimiterPublicAPI.ADMIN_DASHBOARD.getDefaultRateLimit.getOrElse(DefaultRateLimit),
                RateLimitingEntryCreators.SERVICE.value))

            adminDashboardRateLimit.onFailure {
              case e: Throwable => LOGGER.info(s"Unable to generate admin dashboard rate limits for account ${savedAccount.accountId}", e)
            }

            val futureAccountPermissions = daoAccountV2.addAccountPermissions(savedAccount.accountId, user.modules.getOrElse(Set.empty))
            Future.sequence(Seq(apiKeyResult, userResult, defaultModelsFuture, encryptionKeysFuture, publicApiKeysResult, futureRateLimits, futureAccountPermissions, adminDashboardRateLimit)).map(a => {
              Right(!a.contains(false))
            })
          })
        })
    } recover {
      case e: Throwable =>
        LOGGER.info("Registration failed", e)
        Left(ErrorResponse(RegistrationFailed.id, RegistrationFailed.description))
    }
  }

  private def isProspectAccountRestrictedDomain(accountType: Int, email: String): Future[Boolean] = {
    if (accountType.equals(AccountTypes.PROSPECT.id) && !email.matches(AccountManagementDefaults.internalEmailDomains)) {
      val emailDomain = email.split("@").lastOption.getOrElse("")
      daoProspect.isRestrictedProspectDomain(emailDomain)
    } else {
      Future.successful(false)
    }
  }

  def isDomainFromInclusionList(email: String, accountType: Int): Future[Either[ErrorResponse,Boolean]] = {
    if (accountType.equals(AccountTypes.PROSPECT.id)) {
      val emailDomain = email.split("@").lastOption.getOrElse("")
      daoProspect.isDomainFromInclusionList(emailDomain).flatMap{
        res => Future.successful(Right(res))
      }recover{
        case e:Throwable =>
          LOGGER.info("Data fetch failed", e)
          Left(ErrorResponse(InclusionListCheckFailed.id,InclusionListCheckFailed.description))
      }

    } else {
      Future.successful(Right(false))
    }
  }

  def registerV2(user: UserFormV2, isActive: Boolean, isDashboardV3: Boolean, isInternalOpt: Option[Boolean]=None) : Future[Either[ErrorResponse, Boolean]] = {
    businessUserCommonService.checkNApplyUserEmailAvailability(user.email).flatMap {
      case false =>
        LOGGER.info("Email already exists")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.EmailAlreadyExists)))
      case true =>
        if(!AccountTypes.isValid(user.accountType)) {
          LOGGER.info("Invalid Account Type")
          throw new Exception("Invalid Account Type")
        }
        val bu = BusinessUserConvertors.getDtoBusinessUser(user)
        val isInternal = isInternalOpt.getOrElse(bu.email.matches(AccountManagementDefaults.internalEmailDomains))

        isProspectAccountRestrictedDomain(user.accountType, bu.email). flatMap {
          case true =>
            LOGGER.info(ExceptionCodes.DemoRestrictedDomain.description)
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.DemoRestrictedDomain)))
          case false =>
            val account = AccountConvertors.getAccountV2(user, pbeEncryptor, isActive, isInternal)
            daoAccount.saveAccount(account).flatMap(savedAccount => {
              val dtoEnvironmentSeq = AccountConvertors.getDtoEnvironment(savedAccount.accountId, clock)
              daoBusiness.saveEnvironmentReturnIds(dtoEnvironmentSeq) flatMap (savedEnvironment => {
                val apiKeys: Seq[DtoApiKey] = savedEnvironment.map(e => {
                  AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)
                })
                val encryptionKeysFuture = encryptionKeysService.generate(AccountId(savedAccount.accountId))

                encryptionKeysFuture.onFailure {
                  case e: Throwable => LOGGER.info("Unable to generate encryption keys", e)
                }

                //saveApiKeys - inserts keys to tbl_api_key and updates time in tbl_environment, transactionally and so, the response will be 1 if updated
                val apiKeyResult = daoEnvironment.saveApiKeys(apiKeys, clock).map(_ == 1)

                val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
                val publicApiKeysResult: Future[Boolean] = daoPublicApiKey.saveOrUpdate(publicApiKeys).map(!_.contains(0))
                val accountHirearchy = AccountHierarchyInput(id = None,
                  accountId = savedAccount.accountId,
                  hierarchyPath = s"${savedAccount.accountId}/",
                  accountType = user.accountType,
                  hierarchyStatus = Status.ACTIVE.id,
                  administer = false,
                  numberOfPrimaryAdmins = user.numberOfPrimaryAdmins)
                val defaultPermissions = AccountManagementDefaults.userPermissions(user.accountType).map { dp =>
                  (dp._1, dp._2.mkString(","))
                }

                val userResult = daoBusiness.saveUser(bu.copy(accountId = savedAccount.accountId)) flatMap { savedUser =>
                  val futureUpdatePassword = passwordService.setPassword(savedUser, user.password)
                  val futureRole = daoBusiness.addEnvironmentUserRoles(userId = savedUser.id, roles = Set(BusinessUserRoles.ADMIN.id))
                  val dtoBusinessUserInfo = BusinessUserConvertors.getDtoBusinessUserInfo(user, savedUser.id)
                  val futureBusinessUserInfo: Future[Int] = if (isBusinessUserInfoDefined(user.addressLine1, user.addressLine2, user.jobTitle, user.businessWebsite)) daoBusiness.addBusinessUserInfo(dtoBusinessUserInfo) else Future.successful(0)
                  val roleType = if (user.accountType.equals(AccountTypes.PROSPECT.id)) SystemDefinedRoles.PROSPECT.roleType else SystemDefinedRoles.ACCOUNTOWNER.roleType
                  val futureAccountAdditionalDetails = daoAccountV2.addAccountAdditionalDetails(savedAccount,
                    savedUser.id,
                    AccountConvertors.toDtoAccountHierarchy(accountHirearchy),
                    defaultPermissions.toSet,
                    isDashboardV3,
                    roleType,
                    clock)
                  Future.sequence(Seq(futureRole, futureUpdatePassword, futureAccountAdditionalDetails, futureBusinessUserInfo)).map(_ => true)
                }
                val defaultModelsFuture = modelManagementClient.mapDefaultModels(savedAccount.accountId.toString, user.accountType.toString).map {
                  case Right(status) if status =>
                    LOGGER.info(s"Associated default models for account ${savedAccount.accountId}")
                    true
                  case Right(_) =>
                    LOGGER.info(s"Unable to associate default models for account ${savedAccount.accountId}")
                    false
                  case Left(e) =>
                    LOGGER.info(s"Unable to associate default models for account ${savedAccount.accountId}", e)
                    false
                }
                val futureAccountPermissions = daoAccountV2.addAccountPermissions(savedAccount.accountId, user.modules.getOrElse(Set.empty))


                  accountAutomationService.autoProvision(savedAccount.accountId, isInternal, user.accountType, user.bundleReference).onFailure {
                    case e: Throwable =>
                      LOGGER.info(s"Unable to process Auto Provision for account ${savedAccount.accountId}", e)
                  }


                val adminDashboardRateLimit = rateLimitingService.saveRateLimits(SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
                  RateLimiterPublicAPI.ADMIN_DASHBOARD.publicId, RateLimitingWindowSizes.SECOND.value, RateLimiterPublicAPI.ADMIN_DASHBOARD.getDefaultRateLimit.getOrElse(DefaultRateLimit), RateLimitingEntryCreators.SERVICE.value))

                adminDashboardRateLimit.onFailure {
                  case e: Throwable => LOGGER.info(s"Unable to generate admin dashboard rate limits for account ${savedAccount.accountId}", e)
                }

                val daoUIAccountConfigurationFuture = daoUIAccountConfiguration.saveUIAccountConfiguration(DtoAccountUIConfiguration(0, savedAccount.accountId, defaultAutoTimeoutInMinutes.toShort, defaultIdleTimeoutInMinutes.toShort, DateTime.now(), DateTime.now()))

                daoUIAccountConfigurationFuture.onFailure {
                  case e: Throwable => LOGGER.info(s"Unable to save default UI Account Configuration for account ${savedAccount.accountId}", e)
                }

                Future.sequence(Seq(apiKeyResult, userResult, defaultModelsFuture, encryptionKeysFuture, publicApiKeysResult, futureAccountPermissions, adminDashboardRateLimit)).map(a => {
                  Right(!a.contains(false))
                })
              })
            })
        }
    } recover {
      case e: Throwable =>
        LOGGER.info("Registration failed", e.getMessage)
        Left(ErrorResponse(RegistrationFailed.id, RegistrationFailed.description))
    }
  }

  private def isBusinessUserInfoDefined(values: Option[String]*): Boolean = {
    values.exists(value => value.isDefined)
  }

  def registerPrimaryUserParentAccount(userForm: ParentAccountPrimaryUserForm) : Future[Either[ErrorResponse, UserActivationDetails]] = {

    businessUserCommonService.checkNApplyUserEmailAvailability(userForm.email).flatMap {
      case false =>
        LOGGER.info("Validation failed while creating primary user for parent accounts: Email already exists")
        Future.successful(Left(ErrorResponse(RegistrationFailed.id, UserExistsAlready.description)))
      case true =>
        val futureParentAccount: Future[Boolean] = validateParentAccount(userForm)
        val futureAccountWithPrimaryUser: Future[Boolean] = daoBusiness.doesAccountPrimaryUserExist(userForm.accountId)

        val doneFuture = futureParentAccount.flatMap{
          case false => {
            LOGGER.info("Validation failed while creating primary user for parent accounts: Account is not of Parent type or is deleted")
            Future.successful(Left(ErrorResponse(RegistrationFailed.id, InvalidParentAccount.description)))
          }
          case true => futureAccountWithPrimaryUser flatMap {
            case true => {
              LOGGER.info("Validation failed while creating primary user for parent accounts: Account already has primary user")
              Future.successful(Left(ErrorResponse(RegistrationFailed.id, PrimaryUserAlreadyPresent.description)))
            }
            case false =>
              samlValidator.accountIdHasNoSaml(userForm.accountId) flatMap {
                case left @ Left(_) => Future.successful(left.asInstanceOf[Either[ErrorResponse, UserActivationDetails]])
                case Right(_) => for {
                  (userActivation, userId) <- saveUserAndCreateActivation(userForm)
                  _ <- activateUsers(List(userActivation.getEmail))
                  _ <- daoBusiness.addEnvironmentUserRoles(userId = userId, roles = Set(BusinessUserRoles.ADMIN.id))
                } yield {
                  Right(userActivation)
                }
              }
          }
        }
        doneFuture map {
          case r @ Right(_) => r
          case l @ Left(_) =>
            LOGGER.info(s"Primary user registration failed due to $l")
            Left(ErrorResponse(RegistrationFailed.id, UnknownError.description))
        }
    } recover {
      case ex =>
        LOGGER.info(s"Primary user registration failed", ex)
        Left(ErrorResponse(RegistrationFailed.id, RegistrationFailed.description))
    }
  }

  private def validateParentAccount(userForm: ParentAccountPrimaryUserForm): Future[Boolean] = {
    daoAccount.getAccount(userForm.accountId).map{
      case Some(acct) => acct.parentId.isEmpty && !acct.isDeleted
      case _ => false
    }
  }

  private def saveUserAndCreateActivation(userForm: ParentAccountPrimaryUserForm): Future[(UserActivationDetails, Long)] = {
    val bu = BusinessUserConvertors.getDtoBusinessUser(userForm)
    daoBusiness.saveUser(bu) flatMap { savedUser =>
      daoBusiness.createPasswordResetCode(DtoActivationToken(0, savedUser.id, None, Some(clock.now()))) map { uuid =>
        LOGGER.info("Successfully registered primary user for account: " + userForm.accountId + " : " + savedUser.email)
        (UserActivationDetails(
          firstname = savedUser.firstName,
          surname = savedUser.lastName,
          email = savedUser.email,
          activationCode = uuid
        ), savedUser.id)
      }
    }
  }

  def validateAccountManagementV2AndFetchDetails(u: DtoBusinessUser): Future[Either[ErrorResponse, UserAuth]] = {
    daoAccountV2.isAccountV2Provisioned(Set(u.accountId)) flatMap  {
      case false =>
        fetchAllDetailsOfBusinessUser(u).map(Right(_))
      case true =>
        daoAccountV2.getUserAccountAssociation(u.id, u.accountId) flatMap {
          case Some(uaa) =>
            fetchAllDetailsOfBusinessUserForV2AccountManagement(u, uaa).map(Right(_))
          case _ =>
            LOGGER.error(s"Could not login - no user account association for user ${u.id} and account ${u.accountId}")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
        }
    }
  }

  def validateUser(userCredential: UserCredential) : Future[Either[ErrorResponse, UserAuth]] = {
    if(AccountManagementDefaults.validateEmailUserName(userCredential.username)) {
      passwordService.validateUserCredentials(userCredential) flatMap {
        case Right(u) =>
          u.isPrimaryUser match {
            case false =>
              passwordService.isPasswordExpired(u.id).flatMap {
                case true => Future.successful(Left(ErrorResponseFactory.get(PasswordExpired)))
                case false =>
                  validateAccountManagementV2AndFetchDetails(u)
              }
            case true =>
              validateAccountManagementV2AndFetchDetails(u)
          }
        case Left(e) => Future.successful(Left(e))
      }
    }else{
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }


  def validateUserV2(userCredential: UserCredential) : Future[Either[ErrorResponse, UserAuthV2]] = {
    if(AccountManagementDefaults.validateEmailUserName(userCredential.username)) {
      passwordService.validateUserCredentials(userCredential) flatMap {
        case Right(u) =>
          if (u.isPrimaryUser) {
            fetchAllDetailsOfBusinessUserV2(u).map(Right(_))
          } else {
            passwordService.isPasswordExpired(u.id).flatMap {
              case true => Future.successful(Left(ErrorResponseFactory.get(PasswordExpired)))
              case false => fetchAllDetailsOfBusinessUserV2(u).map(Right(_))
            }
          }
        case Left(e) => Future.successful(Left(e))
      }
    }else{
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }
def getValidatedUserAuthV2(userId: Long, username: String) : Future[Either[ErrorResponse, UserAuthV2]] = {
  if(AccountManagementDefaults.validateEmailUserName(username)) {
  getBusinessUserFromUserId(userId) flatMap {
    case Right(u) =>
      if (u.isPrimaryUser) {
        fetchAllDetailsOfBusinessUserV2(u).map(Right(_))
      } else {
        passwordService.isPasswordExpired(u.id).flatMap {
          case true => Future.successful(Left(ErrorResponseFactory.get(PasswordExpired)))
          case false => fetchAllDetailsOfBusinessUserV2(u).map(Right(_))
        }
      }
    case Left(e) => Future.successful(Left(e))
  }
  } else {
    Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
  }
}

  def getValidatedUserAuth(userId: Long, username: String): Future[Either[ErrorResponse, UserAuth]] = {
    if (AccountManagementDefaults.validateEmailUserName(username)) {
      getBusinessUserFromUserId(userId) flatMap {
        case Right(u) =>
            validateAccountManagementV2AndFetchDetails(u)
        case Left(e) => Future.successful(Left(e))
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }

  def getCognitoStatus(username: String): Future[Either[ErrorResponse, CognitoStatus]] = {
    if (AccountManagementDefaults.validateEmailUserName(username)) {
      daoBusiness.getUser(username) flatMap {
        case Some(user) =>
          Future.successful(Right(CognitoStatus(user.cognitoScope, user.cognitoMigrationStatus, user.cognitoMigrationTimestamp)))
        case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }

  def getCognitoStatusById(userId: Long): Future[Either[ErrorResponse, CognitoStatus]] = {
      daoBusiness.getUser(userId) flatMap {
        case Some(user) =>
          Future.successful(Right(CognitoStatus(user.cognitoScope, user.cognitoMigrationStatus, user.cognitoMigrationTimestamp)))
        case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
  }
  def updateCognitoStatus(username: String, cognitoStatus: Int): Future[Either[ErrorResponse, Boolean]] = {
    if (AccountManagementDefaults.validateEmailUserName(username)) {
      daoBusiness.getUser(username) flatMap {
        case Some(user) =>
          try {
            if (user.cognitoScope)
              daoBusiness.updateCognitoMigrationStatus(user.id, cognitoStatus) map   {
                case res: Boolean => Right(res)
              } else {
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CognitoUserNotMigrated)))
            }
          }
            catch {
            case e: Exception =>
            LOGGER.error(s"Error updating cognito status, ${e.getMessage}")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
          }
        case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }
  def getBusinessUserFromUserId(userId:Long): Future[Either[ErrorResponse, DtoBusinessUser]]={
    daoBusiness.getUser(userId).flatMap {
      case Some(user) =>
        daoAccountV2.isAccountV2Provisioned(Set(user.accountId)) flatMap {
          case false =>
            Future.successful(Right(user))
          case true =>
            daoAccountV2.getActiveUserAccountAssociation(user.id, user.accountId) flatMap {
              case Some(_) =>
                Future.successful(Right(user))
              case _ =>
                daoAccountV2.getAssociatedAccounts(user.id) flatMap {
                  associatedAccounts =>
                    associatedAccounts.headOption match {
                      case None => Future.successful(Left(ErrorResponseFactory.get(AccountOrUserLocked)))
                      case Some(associatedAccount) => Future.successful(Right(user.copy(accountId = associatedAccount.id)))
                    }
                }
            }
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def unlockCognitoUser(email: String): Future[Either[ErrorResponse, Int]] = {
    daoBusiness.getUser(email) flatMap {
      case Some(user) =>
        if(user.cognitoScope && user.cognitoMigrationStatus.equals(CognitoMigrationStatus.Migrated.id)){
          daoBusiness.resetBadLoginMagicTokenCount(user.id) flatMap {
            case res => Future.successful(Right(res))
          }
        } else {
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CognitoUserNotMigrated)))
        }
      case None => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def lockUser(username : String) : Future[Either[ErrorResponse, String]] = {
    getStatus(daoBusiness.updateLockStatus(username, unlock = true),s"$username is locked")
  }

  def unlockUser(username: String) : Future[Either[ErrorResponse, String]] = {
    getStatus(daoBusiness.updateLockStatus(username, unlock = false),s"$username is unlocked")
  }

  def regenerateActivationCode(username : String) : Future[Either[ErrorResponse, String]]  = {
    daoBusiness.getUser(username).flatMap {
      case Some(user) =>
        val dto = DtoActivationToken(0, user.id, None, Some(clock.now()))
        daoBusiness.createActivationCode(dto).map {
          case Some(uuid) => Right(uuid)
          case _ => Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def activateUserByActivationcode(activationCode : String) : Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.getUserByActivationCode(activationCode).flatMap {
      case Some(user) => {
        val activate = daoBusiness.activateUsers(user.accountId, user.email).
                                                    map(_ => daoBusiness.invalidateActivationCodeByUserId(user.id))
        Future.sequence(Seq(activate)).map(_ => Right(true))
      }
      case _ => Future.successful(Left(ErrorResponse(InvalidActivationCode.id, InvalidActivationCode.description)))
    } recover {
      case e: Throwable =>
        LOGGER.info("Activation failed", e)
        Left(ErrorResponse(InvalidActivationCode.id, InvalidActivationCode.description))
    }
  }

  def activateUsers(users : List[String]) : Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.activateUsers(users).map{
      case a if a > 0 => Right(true)
      case _ => Left(ErrorResponse(UserNotActivated.id, UserNotActivated.description))
    } recover {
      case e: Throwable =>
        LOGGER.info("Activation failed", e)
        Left(ErrorResponse(UserNotActivated.id, UserNotActivated.description))
    }
  }

  def getUserByActivationCode(activationCode : String) : Future[Either[ErrorResponse, BusinessUser]] = {

    daoBusiness.getUserWithAccountByActivationCode(activationCode) flatMap {
      case Some(user) =>
        if(user.roles.isEmpty)
          daoBusiness.getBusinessUserRoles(user.id).map(r => Right(user.copy(roles = r)))
        else
          Future.successful(Right(user))
      case _ => Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    }
  }

  def getUserByResetCode(resetCode : String) : Future[Either[ErrorResponse, BusinessUser]] = {
    daoBusiness.getUserWithAccountByResetCode(resetCode) flatMap {
      case Some(user) =>
        if(user.roles.isEmpty)
          daoBusiness.getBusinessUserRoles(user.id).map(r => Right(user.copy(roles = r)))
        else
          Future.successful(Right(user))
      case _ => Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    }
  }

  def getActivesPrimaryAccountAdmins(start: Option[Int], size: Option[Int], search: String): Future[Vector[PrimaryAccountUser]] = {
    daoBusiness.getParentAccountsAndPrimaryUser(start.getOrElse(0), size.getOrElse(20), search).map(dtoAdmins => BusinessUserConvertors.getParentAccountPrimaryAdmins(dtoAdmins))
  }

  def getActivesPrimaryAccountAdminsCount: Future[Int] = {
    daoBusiness.getTotalActiveAccount()
  }

  def unlockIfNeeded(email : String) : Future[Boolean] = {
    if(config.isAutoReset) {
      daoBusiness.getUser(email) flatMap {
        case Some(u) => processUnlockAutomatically(u)
        case _ => Future.successful(false)
      }
    } else {
      LOGGER.info("Unlock automatically is disabled")
      Future.successful(false)
    }
  }

  def logBadLoginAttempt(email : String, errorMsg : String) : Future[Option[UserStatus]] = {
    daoBusiness.getUser(email) flatMap {
      case Some(u) => processBadLoginLog(u, errorMsg).map(Some(_))
      case _ => Future.successful(None)
    }
  }

  def getInvalidAttemptsByEmail(email: String) : Future[Int] = {
    daoBusiness.getUser(email) flatMap {
      case Some(u) => daoBusiness.getBadLoginDetails(u.id).map(res=>res.map(r=>r.count).getOrElse(0))
      case _ => Future.successful(0)
    }
  }


  private def processBadLoginLog(user : DtoBusinessUser, errorMsg : String) : Future[UserStatus] = {
    val badLoginDetailsFuture = daoBusiness.getBadLoginDetails(user.id) flatMap {
      case Some(details) =>
        val currentBadAttempt = details.count + 1
        daoBusiness.updateBadLoginDetails(details.businessUserId, currentBadAttempt, now()).map(_ => details.copy(count = currentBadAttempt))
      case _ =>
        val badLoginDetails = DtoBadLoginCount(businessUserId = user.id, count = 1, firstBadTry = Some(now()), magicTokenCount = 0)
        daoBusiness.insertBadLoginDetails(badLoginDetails).map(_ => badLoginDetails)
    }

    badLoginDetailsFuture.flatMap { details =>
      //Current bad login attempt count
      val currentBadAttempt = details.count

      val updateBadLoginDetailsFuture = daoBusiness.updateBadLoginDetails(details.businessUserId, currentBadAttempt, now())

      val lockIfNecessaryFuture: Future[Boolean] = {
        if(currentBadAttempt >= config.maxTry) {
          daoBusiness.updateLockStatus(user.email, unlock = true).map(_ => true)
        } else {
          Future.successful(false)
        }
      }

      def updateLockTimeIfNecessary(lockedNow: Boolean): Future[Unit] = {
        if(lockedNow) {
          daoBusiness.updateLockTime(details.businessUserId, now()).map(_ => ())
        } else {
          Future.successful(())
        }
      }

      for {
        _           <- updateBadLoginDetailsFuture
        lockStatus  <- lockIfNecessaryFuture
        _           <- updateLockTimeIfNecessary(lockStatus)
      } yield UserStatus(locked = lockStatus, badLoginCount = currentBadAttempt)
    }
  }

  private def processUnlockAutomatically(user : DtoBusinessUser) : Future[Boolean] = {
    val resetDuration = config.resetDuration
    daoBusiness.getBadLoginDetails(user.id) flatMap {
      case Some(details) => {
        details.lastBadTry match{
          case Some(badTryDetail) => {
            val duration = Seconds.secondsBetween(badTryDetail, now()).getSeconds
            if(duration >= resetDuration.toSeconds) {
              LOGGER.info("Unlocked automatically")
              unlockUser(user.email).map(_ => true)
            } else {
              LOGGER.info("Unlock is not done, due to cooling period")
              Future.successful(false)
            }
          }
          case _ => Future.successful(false)
        }
      }
      case _ => Future.successful(false)
    }
  }

  private def getStatus(count : Future[Int], datamsg : String) : Future[Either[ErrorResponse, String]] = {
    count map {
      case c if c > 0 => Right(datamsg) // If number of records updated is more than one then the db update was successful.
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    }
  }

  def getAccountIdByUsername(username : String) : Future[Either[ErrorResponse, Long]]  = {
    daoBusiness.getUserAccount(username) map {
      case Some(accountId) if(accountId != 0) => Right(accountId)
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    }
  }

  def doesUserExist(email: String): Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.doesUserExist(email) map { res =>
      Right(res)
    } recover {
      case e: Exception =>
        LOGGER.info("Exception occurred while checking if user exist for email")
        Left(ErrorResponse(UnknownError.id, UnknownError.description))
    }
  }

  private def fetchAllDetailsOfBusinessUser(u : DtoBusinessUser) : Future[UserAuth]  = {
    val futureUpdateSign = daoBusiness.updateSignInTime(u.id, clock.now())
    val futureAllRoles = daoBusiness.getEnvironmentUserRolesByUserId(u.id)
    val futureAllEnv = daoBusiness.getEnvironmentsWithApiKeysByAccountId(u.accountId)
    val futureAccount = daoAccount.getAccount(u.accountId)
    val futurePermission = daoAccount.getAccountPermissions(u.accountId)
    val clearBadLoginCounts = daoBusiness.deleteBadLoginDetails(u.id)
    val subscriptionsFuture = daoSubscriptions.fetchSubscriptionsForLogin(u.accountId)
    val accountsFuture = daoAccountV2.getAssociatedAccounts(u.id)
    val dtoAccountUIConfigurationOptFuture = daoUIAccountConfiguration.getUIAccountConfiguration(u.accountId)
    val passwordUpdatedAt = passwordService.getPasswordUpdatedAt(u.id)
    for {
      _ <- futureUpdateSign
      roles <- futureAllRoles
      account <- futureAccount
      env <- futureAllEnv
      permissions <- futurePermission
      _ <- clearBadLoginCounts
      subscriptions <- subscriptionsFuture
      accounts <- accountsFuture
      dtoAccountUIConfigurationOpt <- dtoAccountUIConfigurationOptFuture
      passwordUpdatedAt <- passwordUpdatedAt
    } yield BusinessUserConvertors.getUserAuth(u,
                                               account.getOrElse(throw new Exception("Failed to retrieve account id from database")),
                                               roles,
                                               env,
                                               permissions,
                                               subscriptions,
                                               accounts,
                                               dtoAccountUIConfigurationOpt,
                                               passwordUpdatedAt,
                                               passwordService.getPasswordExpireAt(passwordUpdatedAt))
  }

  private def fetchAllDetailsOfBusinessUserForV2AccountManagement(u : DtoBusinessUser, dtoUserAccountAssociation: DtoUserAccountAssociation) : Future[UserAuth]  = {
    val futureUpdateSign = daoBusiness.updateSignInTime(u.id, clock.now())
    val futureAllEnv = daoBusiness.getEnvironmentsWithApiKeysByAccountId(u.accountId)
    val futureAccount = daoAccount.getAccount(u.accountId)
    val futurePermission = daoAccount.getAccountPermissionsWithParentFeatureFlag(u.accountId)
    val futureAccountHierarchy = daoAccountV2.getAccountHierarchyByAccountId(u.accountId)
    val clearBadLoginCounts = daoBusiness.deleteBadLoginDetails(u.id)
    val subscriptionsFuture = daoSubscriptions.fetchSubscriptionsForLogin(u.accountId)
    val accountsFuture = daoAccountV2.getAssociatedAccounts(u.id)
    val futureDashboardUserPermissions: Future[Map[Int, Set[Int]]] = daoAccountV2.getDashboardUserPermissions(dtoUserAccountAssociation.id)
    val dtoAccountUIConfigurationOptFuture = daoUIAccountConfiguration.getUIAccountConfiguration(u.accountId)
    val isAccountOwnerFuture = daoAccountV2.isAccountOwner(dtoUserAccountAssociation.id)
    val passwordUpdatedAt = passwordService.getPasswordUpdatedAt(u.id)
    val isProgram = daoAccount.getSponsorBankIdsByProgramId(u.accountId) map {
      case sponsorBankIds if sponsorBankIds.nonEmpty => Some(true)
      case _ => Some(false)
    }
    val tosTimeStamp = daoAccount.getTOSTimeStamp(u.id)
    val isAnalyticsHistoricalDataLoadedFuture = daoAccount.getAnalyticsGlobalinfo()
    for {
      _ <- futureUpdateSign
      account <- futureAccount
      env <- futureAllEnv
      permissions <- futurePermission
      accountHierarchy <- futureAccountHierarchy
      _ <- clearBadLoginCounts
      subscriptions <- subscriptionsFuture
      accounts <- accountsFuture
      accountOwnerAccessAccounts <- v2Validator.getAccountOwnerAssociatedAccounts(u.id, accounts)
      rootAccHierarchy <- v2Validator.getRootParentAccountHierarchy(u.accountId)
      rootAccountDetail <- getRootAccountDetail(rootAccHierarchy)
      accountEnvWorkflowMapping <- getAssociatedAccountsAndWorkflowIds(rootAccHierarchy.map(_.accountType), accounts.map(_.id).toSet)
      platformEnvPermissions <- getPlatformEnvironmentPermissions(u.id, account.getOrElse(throw new Exception("Failed to retrieve account id from database")), rootAccHierarchy.map(_.accountType), accountEnvWorkflowMapping)
      programs <- daoAccount.getProgramIdNameByUserId(u.id)
      dashboardCustomUserPermissions <- futureDashboardUserPermissions
      dashboardSystemUserPermissions <- daoAccountV2.getDashboardUserSystemPermissions(dtoUserAccountAssociation.id, rootAccHierarchy.map(_.accountType))
      dtoAccountUIConfigurationOpt <- dtoAccountUIConfigurationOptFuture
      isAccountOwner <- isAccountOwnerFuture
      passwordUpdatedAt <- passwordUpdatedAt
      isProgram <- isProgram
      isAnalyticsHistoricalDataLoaded <- isAnalyticsHistoricalDataLoadedFuture
      tosTimeStamp <- tosTimeStamp
    } yield AccountConvertors.toUserAuth(u,
      account.getOrElse(throw new Exception("Failed to retrieve account id from database")),
      env,
      permissions,
      subscriptions,
      SystemDefiendRolesPermissions.mergePermissions(
        dashboardCustomUserPermissions,
        getSystemPermissionsBasedOnAccountType(account.get.accountId, accountHierarchy, dashboardSystemUserPermissions)
      ),
      dtoUserAccountAssociation,
      accounts ++ accountOwnerAccessAccounts,
      dtoAccountUIConfigurationOpt,
      isAdmin(isAccountOwner, accountHierarchy.map(_.accountType), u.isPrimaryUser),
      passwordUpdatedAt,
      passwordService.getPasswordExpireAt(passwordUpdatedAt),
      Some(programs),
      isProgram,
      isAnalyticsHistoricalDataLoaded,
      accountHierarchy.map(_.accountType),
      tosTimeStamp,
      rootAccHierarchy,
      rootAccountDetail,
      platformEnvPermissions)
  }

  private def getSystemPermissionsBasedOnAccountType(accountId: Long, accountHierarchy: Option[DtoAccountHierarchy], dashboardSystemUserPermissions: Map[Int, Set[Int]]): Map[Int, Set[Int]] = {
    if(accountHierarchy.isDefined && accountHierarchy.get.accountType == AccountTypes.AGGREGATOR.id && accountHierarchy.get.hierarchyPath.startsWith(accountId.toString)) {
      var updatedDashboardSystemUserPermissions : Map[Int, Set[Int]] = Map()
      dashboardSystemUserPermissions.foreach(x=> {
        var envPermissions : mutable.Set[Int] = mutable.Set[Int]()
        envPermissions.++=(x._2.filterNot(y => y == DashboardUserPermissions.TRANSACTIONS_CREATE.id))
        updatedDashboardSystemUserPermissions = updatedDashboardSystemUserPermissions.+(x._1 -> envPermissions.toSet)
      })
      updatedDashboardSystemUserPermissions
    } else dashboardSystemUserPermissions
  }

  private def fetchAllDetailsOfBusinessUserV2(u : DtoBusinessUser) : Future[UserAuthV2]  = {
    val futureUpdateSign = daoBusiness.updateSignInTime(u.id, clock.now())
    val futureAllRoles = daoBusiness.getEnvironmentUserRolesByUserId(u.id)
    val futureAllEnv = daoBusiness.getEnvironmentsWithApiKeysByAccountId(u.accountId)
    val futureAccount = daoAccount.getAccount(u.accountId)
    val futurePermission = daoAccount.getAccountPermissions(u.accountId)
    val clearBadLoginCounts = daoBusiness.deleteBadLoginDetails(u.id)
    val subscriptionsFuture = daoSubscriptions.fetchSubscriptionsForLogin(u.accountId)
    val accountsFuture = daoAccountV2.getAssociatedAccounts(u.id)
    val passwordUpdatedAt = passwordService.getPasswordUpdatedAt(u.id)

    for {
      _ <- futureUpdateSign
      roles <- futureAllRoles
      account <- futureAccount
      env <- futureAllEnv
      permissions <- futurePermission
      _ <- clearBadLoginCounts
      subscriptions <- subscriptionsFuture
      accounts <- accountsFuture
      passwordUpdatedAt <- passwordUpdatedAt
    } yield BusinessUserConvertors.getUserAuthV2(u,
      account.getOrElse(throw new Exception("Failed to retrieve account id from database")),
      roles,
      env,
      permissions,
      subscriptions,
      accounts,
      passwordUpdatedAt,
      passwordService.getPasswordExpireAt(passwordUpdatedAt))
  }

  def isUserLocked(email : String) : Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.isUserLocked(email).map(Right.apply)
  }

  def isUserInternal(email: String): Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.isUserInternal(email).map(Right.apply)
  }

  def getBusinessUsersWithRoleForAccount(accountId: Long) : Future[Either[ErrorResponse, Seq[BusinessUserWithRoles]]] = {
    daoBusiness.isAccountExist(accountId) flatMap {
      case true =>
        daoBusiness.getUsersForAccount(accountId).flatMap { list: Seq[DtoBusinessUser] =>
          val usersWithRoles = list.map { u =>
            getRolesWithEnvironmentType(u.id, u.accountId) map {
              case Right(r) => BusinessUserWithRoles(u.id, u.firstName, u.lastName, u.email, u.contactNumber, u.isPrimaryUser, r._1, r._2)
              case Left(_) => BusinessUserWithRoles(u.id, u.firstName, u.lastName, u.email, u.contactNumber, u.isPrimaryUser, Seq.empty, Seq.empty[(String,Set[String])])
            }
          }
          Future.sequence(usersWithRoles)
        }.map(seq => Right(seq))
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  private def getRolesWithEnvironmentType(userId: Long, accountId: Long) : Future[Either[ErrorResponse, (Seq[String], Seq[(String, Set[String])])]] = {
    daoEnvironment.getEnvironmentsByAccountId(accountId) flatMap { env =>
      daoBusiness.getEnvironmentUserRolesByUserId(userId) map {
        case roles if roles.nonEmpty =>
          val commonRoles: Seq[String] = roles.filter(r => r.role == BATCH.id || r.role == USERS.id || r.role == ACCOUNTS.id).map(d => DashboardUserRole(d.role).label).toSeq
          val envRoles = env.map{ a:DtoEnvironment =>
            (EnvironmentConstants(a.environmentType.toInt).toString,
              roles.filter{ _.environmentId.contains(a.id)}.
                filterNot(er => er.role == BATCH.id || er.role == USERS.id || er.role == ACCOUNTS.id).
                map(d => DashboardUserRole(d.role).label).toSet)
          }
          Right(commonRoles.distinct, envRoles)
        case _ => Left(ErrorResponseFactory.get(RolesNotFound))
      }
    }
  }

  def promotePrimaryUser(userId: Long): Future[Either[ExceptionCodes, DtoBusinessUser]] = {

    validateUser(userId).flatMap {
      case Right(user) =>
        val b: Future[Either[ExceptionCodes, DtoBusinessUser]] = for {
          currentPrimaryUser <- daoBusiness.fetchPrimaryUserByAccountId(user.accountId)
          a <- swapPrimaryUser(currentPrimaryUser, user)
        } yield a
        b
      case _ =>
        Future.successful(Left(ExceptionCodes.UserNotFound))
    }
  }

  private def swapPrimaryUser(currentPrimaryUserOpt: Option[DtoBusinessUser], user: DtoBusinessUser) = {

    val res: Either[ExceptionCodes, DtoBusinessUser] = currentPrimaryUserOpt match {
      case Some(currentPrimaryUser) =>
        daoBusiness.swapAccountPrimaryUser(currentPrimaryUser.id, user.id)
        Right(user.copy(isPrimaryUser = true))
      case None =>
        Left(ExceptionCodes.UserNotFound)
    }
    Future.successful(res)

  }

  private def validateUser(userId: Long): Future[Either[ExceptionCodes, DtoBusinessUser]] = {
    daoBusiness.getUser(userId).map {
      case Some(user) =>
        // locked user
        if (user.isPrimaryUser == true) {
          LOGGER.info(s"User ${user.id} is already primary user")
          Left(ExceptionCodes.UserNotFound)
        } else {
          Right(user)
        }
      case None =>
        LOGGER.info(s"Couldn't find a user with userId $userId")
        Left(ExceptionCodes.UserNotFound)
    }
  }

  private def saveRateLimits(dtoEnvironmentSeq: Seq[DtoEnvironment]): Future[Option[Int]] = {
    val prodCert = Set(EnvironmentConstants.PRODUCTION_ENVIRONMENT.id, EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id)
    val dtoRateLimitSeq = getActiveApiList() flatMap { publicAPI =>
      dtoEnvironmentSeq map { dtoEnvironment =>
//        set limit to 0 for EMAIL_AUTH_SCORE API for Production and Certification alone
        val limit = if(publicAPI.equals(RateLimiterPublicAPI.EMAIL_AUTH_SCORE) && prodCert.contains(dtoEnvironment.environmentType.toInt)) 0L else publicAPI.getDefaultRateLimit().getOrElse(DefaultRateLimit)
        AccountConvertors.toDtoRateLimit(
          SaveRateLimitingInput(accountId = dtoEnvironment.accountId,
            environmentTypeId = dtoEnvironment.environmentType,
            api = publicAPI.publicId,
            windowInMillis = RateLimitingWindowSizes.SECOND.value,
            limit = limit,
            createdBy = RateLimitingEntryCreators.SERVICE.value
          )
        )
      }
    }
    daoRateLimit.saveRateLimits(dtoRateLimitSeq)
  }

  private def getActiveApiList(): Seq[RateLimiterPublicAPI] = {
   Seq(RateLimiterPublicAPI.EMAIL_AUTH_SCORE, RateLimiterPublicAPI.TRANSACTION,
     RateLimiterPublicAPI.REASON_CODE, RateLimiterPublicAPI.FEEDBACK, RateLimiterPublicAPI.ACCOUNT,
     RateLimiterPublicAPI.EVENTS, RateLimiterPublicAPI.FEEDBACK_NEW)
  }

  def getUserLoginDetails(email: String): Future[Either[ErrorResponse, Map[String, Int]]] = {
    if(AccountManagementDefaults.validateEmailUserName(email)) {
      daoBusiness.getUser(email).flatMap {
        case Some(businessUser) => isLoginRestrictedForSaml(businessUser).flatMap {
          case Right(false) => daoBusiness.getUserLoginDetails(email).map {
            case Some(loginDetails) => Right(Map("badLoginCount" -> loginDetails._1, "magicTokenCount" -> loginDetails._2))
            case _ => Right(Map("badLoginCount" -> 0, "magicTokenCount" -> 0))
          }.recover {
            case ex =>
              LOGGER.info(s"Fetch user login details failure", ex)
              Left(ErrorResponseFactory.get(UserNotFound))
          }
          case _ => LOGGER.info(s"Magic token can't be created for SAML users.")
            Future.successful(Left(ErrorResponseFactory.get(MagicLinkNotCreatedForSAMLUser)))
        }
        case _ => LOGGER.info(s"Fetch user login details failure")
          Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
      }

    } else {
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }

  def validateEmailDomain(email: String, accountType: Int): Future[Either[ErrorResponse, Boolean]] = {
    if (AccountManagementDefaults.validateEmailUserName(email)) {
      val emailDomain = email.split("@").lastOption.getOrElse("")
      def sendEmailToClientAndAdmin0(businessUser: DtoBusinessUser) = {
        daoAccount.anyHasRole(accountIds = Set(businessUser.accountId), businessUserRole = BusinessUserRoles.DashboardV3.id, accountAttributeValueOpt = None) flatMap {
          v3 => {
            Future.successful(Right(true)) map {
              case Right(sent) if sent => LOGGER.info(s"Email sent successfully to ${businessUser.email}")
              case _ => LOGGER.info(s"Error while sending set password mail to ${businessUser.email}")
            }
          }
        }
        Future.successful(Right(true)) map {
          case Right(sent) if sent => LOGGER.info(s"Email sent successfully to ${email}")
          case _ => LOGGER.info(s"Error while sending set password mail to ${email}")
        }
        Future.successful(Left(ErrorResponseFactory.get(EmailDomainAlreadyExists)))
      }

      isProspectAccountRestrictedDomain(accountType, email).flatMap {
        case true =>
          LOGGER.info(ExceptionCodes.DemoRestrictedDomain.description)
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.DemoRestrictedDomain)))
        case false =>
          if (whitelistedEmailDomain.contains(emailDomain.toLowerCase()) || accountType == AccountTypes.PROSPECT.id) {
            Future.successful(Right(true))
          } else {
            daoBusiness.getAccountOwnerUserByEmailDomain(emailDomain).flatMap {
              case Some(businessUser) => sendEmailToClientAndAdmin0(businessUser)
              case _ => Future.successful(Right(true))
            }
          }
      }.recover {
        case e: Throwable =>
          LOGGER.info("Validate Email Domain failed, {}", e.getMessage)
          Left(ErrorResponseFactory.get(NonFunctionalMail))
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(NonFunctionalMail)))
    }
  }

  def checkAndGenerateMagicToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    daoBusiness.getUser(email).flatMap {
      case Some(user) =>
        daoAccount.getAccount(user.accountId) flatMap {
          case Some(account) if account.isActive => daoAccountV2.getAssociatedAccounts(user.id) flatMap { // fetch all associations for an user in which atleast 1 account is active and his association in that is not locked
              associatedAccounts =>
                associatedAccounts.headOption match {
                  case None => Future.successful(Left(ErrorResponseFactory.get(AccountOrUserLocked)))
                  case Some(_) => daoBusiness.isMagicTokenCreated(user.id).flatMap {
                    case None =>
                      generateMagicTokenWithAudit(user, email, userAgent)
                    case Some(dtoMagicToken) =>
                      if (isMagicTokenExpired(dtoMagicToken)) {
                        deleteExpiredMagicTokenWithAudit(user, dtoMagicToken.token, userAgent).flatMap {
                          case i if i > 0 =>
                            generateMagicTokenWithAudit(user, email, userAgent)
                          case _ =>
                            LOGGER.info(s"Magic token is already created for user Id ${user.id}. Error while deleting expired magic")
                            Future.successful(Left(ErrorResponseFactory.get(MagicTokenAlreadyCreated)))
                        }
                      } else {
                        LOGGER.info(s"Magic token is already created for user Id ${user.id} at ${dtoMagicToken.createdAt}")
                        Future.successful(Left(ErrorResponseFactory.get(MagicTokenAlreadyCreated)))
                      }
                  }.recover {
                    case e: Exception =>
                      LOGGER.info(s"Error occurred while generating magic token for user Id ${user.id}", e)
                      Left(ErrorResponseFactory.get(UnknownError))
                  }
                }
            }
          case _=> Future.successful(Left(ErrorResponseFactory.get(AccountOrUserInactive)))
        }
      case None =>
        LOGGER.info(s"User Not Found")
        Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  private def generateMagicTokenWithAudit(user: DtoBusinessUser, email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    for {
      resp <- generateMagicToken(user, email)
      magicTokenCount <- daoBusiness.getMagicTokenCount(user.id)
      _ <- resp match {
        case Right(res) => magicLinkAuditService.saveMagicLinkAudit(userId = user.id, token = res.magicToken, count = magicTokenCount, createdAt = Some(clock.now()), userAgent = Some(userAgent))
        case Left(_) =>
          LOGGER.info(s"Error while generating magic token for user Id ${user.id}, audit is not saved")
          Future.successful(Right(0))
      }
    } yield resp
  }

  def checkAndGenerateDocumentLinkToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    val alreadyCreatedError = ErrorResponseFactory.get(DocumentTokenAlreadyCreated)
    def tokenAlreadyCreated(message: String): Future[Either[ErrorResponse, UserMagicToken]] = {
      LOGGER.info(s"Document token is already created for email $email. $message")
      Future.successful(Left(alreadyCreatedError))
    }
    isProspectAccountRestrictedDomain(AccountTypes.PROSPECT.id, email).flatMap {
      case true =>
        LOGGER.info(ExceptionCodes.DocRestrictedDomain.description)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.DocRestrictedDomain)))
      case false =>
        daoBusiness.isDocumentLinkTokenCreated(email).flatMap {
          case None =>
            generateDocumentLinkTokenWithAudit(email, userAgent)
          case Some(dto) if isDocumentLinkTokenExpired(dto) =>
            deleteExpiredDocumentLinkTokenWithAudit(email, dto.token, userAgent).flatMap { deletedCount =>
              if (deletedCount > 0) generateDocumentLinkTokenWithAudit(email, userAgent)
              else tokenAlreadyCreated("Error while deleting expired token.")
            }
          case Some(dto) =>
            tokenAlreadyCreated(s"Token created at ${dto.createdAt}.")
        }
    }
  }


  private def generateDocumentLinkTokenWithAudit(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    generateDocumentLinkToken(email).flatMap { tokenResponse =>
      daoBusiness.getDocumentTokenCount(email).flatMap { tokenCount =>
        tokenResponse match {
          case Right(token) =>
            magicLinkAuditService
              .saveDocumentLinkAudit(
                email = email,
                token = token.magicToken,
                count = tokenCount,
                createdAt = Some(clock.now()),
                userAgent = Some(userAgent)
              )
              .map(_ => tokenResponse)
          case Left(_) =>
            LOGGER.info(s"Error while generating document token for email Id $email, audit is not saved")
            Future.successful(tokenResponse)
        }
      }
    }
  }


  private def deleteExpiredDocumentLinkTokenWithAudit(email: String, token: String, userAgent: String): Future[Int] = {
    for {
      resp <- daoBusiness.deleteExpiredDocumentLinkTokenByEmailId(email)
      _ <- magicLinkAuditService.saveDocumentLinkAudit(email = email, token = token, deletedAt = Some(clock.now()), userAgent = Some(userAgent))
    } yield resp
  }

  private def isDocumentLinkTokenExpired(dtoMagicToken: DtoDocumentLinkToken): Boolean = {
    val magicTokenCreatedAt = dtoMagicToken.createdAt
    val expiryTime = UserMagicTokenConstants.expiryTimeForDocumentLink
    magicTokenCreatedAt.plusMinutes(expiryTime).isBeforeNow
  }

  private def generateDocumentLinkToken(email: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    val magicToken = UUIDUtilities.getRandomUUID
    val deviceIdentifier = UUIDUtilities.getRandomUUID
    val createdAt = DateTime.now()
    val dtoMagicToken = BusinessUserConvertors.getDtoDocumentLinkToken(email, magicToken, deviceIdentifier, createdAt, 0)
    daoBusiness.addDocumentToken(dtoMagicToken).map {
      case res if res =>
        Right(UserMagicToken(
          firstName = "", // not required for document token
          lastName = "", // not required for document token
          email = email,
          magicToken = dtoMagicToken.token,
          deviceIdentifier = dtoMagicToken.identifier,
          createdAt = dtoMagicToken.createdAt
        ))
      case _ =>
        LOGGER.info(s"Error while generating document token for email ${email}")
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def deleteExpiredMagicTokenWithAudit(user: DtoBusinessUser, token: String, userAgent: String): Future[Int] = {
    for {
      resp <- daoBusiness.deleteExpiredMagicTokenByUserId(user.id)
      _ <- magicLinkAuditService.saveMagicLinkAudit(userId = user.id, token = token, deletedAt = Some(clock.now()), userAgent = Some(userAgent))
    } yield resp
  }

  private def generateMagicToken(user: DtoBusinessUser, email: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    val magicToken = UUIDUtilities.getRandomUUID
    val deviceIdentifier = UUIDUtilities.getRandomUUID
    val createdAt = DateTime.now()
    val dtoMagicToken = BusinessUserConvertors.getDtoBuMagicToken(user, magicToken, deviceIdentifier, createdAt, 0)
    daoBusiness.getBadLoginDetails(user.id).flatMap {
      case Some(dtoBadlogin) =>
        daoBusiness.addMagicTokenForUserAndIncrementCount(user.id, dtoMagicToken).map {
          case res if res =>
            Right(UserMagicToken(
              firstName = user.firstName,
              lastName = user.lastName,
              email = email,
              magicToken = dtoMagicToken.token,
              deviceIdentifier = dtoMagicToken.identifier,
              createdAt = dtoMagicToken.createdAt
            ))
          case _ =>
            LOGGER.info(s"Error while generating magic token for user Id ${user.id}")
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case None =>
        val newDtoBadLogin = DtoBadLoginCount(
          businessUserId = user.id,
          count = 0,
          firstBadTry = None,
          lastBadTry = None,
          lockTime = None,
          magicTokenCount = 1
        )
        daoBusiness.addMagicTokenForUserAndAddBadLoginEntry(dtoMagicToken, newDtoBadLogin).map {
          case res if res =>
            Right(UserMagicToken(
              firstName = user.firstName,
              lastName = user.lastName,
              email = email,
              magicToken = dtoMagicToken.token,
              deviceIdentifier = dtoMagicToken.identifier,
              createdAt = dtoMagicToken.createdAt
            ))
          case _ =>
            LOGGER.info(s"Error while generating magic token for user Id ${user.id}")
            Left(ErrorResponseFactory.get(UnknownError))
        }
    }
  }

  private def isMagicTokenExpired(dtoMagicToken: DtoMagicToken): Boolean = {
    val magicTokenCreatedAt = dtoMagicToken.createdAt
    val expiryTime = UserMagicTokenConstants.expiryTime
    magicTokenCreatedAt.plusMinutes(expiryTime).isBeforeNow
  }

  def validateUserForPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, UserAuth]] = {
    passwordService.validatePasswordlessLogin(credential) flatMap {
      case Right(res) =>
        res match {
          case Some(u) =>
            validateAccountManagementV2AndFetchDetails(u)
          case None =>
            Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
        }
      case Left(err) => Future.successful(Left(err))
    }
  }

  def validateEmailForDocsPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, UserAuth]] = {
    passwordService.validateDocsPasswordlessLogin(credential) flatMap {
      case Right(res) =>
        res match {
          case Some(u) => Future.successful(Right(u))
          case None =>
            Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
        }
      case Left(err) => Future.successful(Left(err))
    }
  }

  def deleteMagicTokenForUser(userId: Long, userAgent: String): Future[Either[ErrorResponse, Int]] = {
    daoBusiness.deleteExpiredMagicTokenReturnToken(userId).flatMap{
      case Some(token) => magicLinkAuditService.saveMagicLinkAudit(userId = userId, token = token, deletedAt = Some(clock.now()), userAgent = Some(userAgent))
      case None => Future.successful(Right(0))
    }.recover{
      case ex =>
        LOGGER.info(s"Exception occured while deleting magic token for user Id : ${userId}",ex)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def isAdmin(isAccountOwner: Boolean, accountType: Option[Int], isPrimaryUser: Boolean): Boolean = {
    if(accountType.isDefined && accountType.get.equals(AccountTypes.PROSPECT.id)){
      return isPrimaryUser
    }
    isAccountOwner
  }

  def getBusinessUserInfo(businessUserId: Long): Future[Either[ErrorResponse,BusinessUserInfo]] = {
    daoBusiness.getBusinessUserInfo(businessUserId).map {
      case Some(bu_info)=>
        Right(BusinessUserConvertors.getBusinessUserInfo(bu_info))
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }.recover{
      case ex =>
        LOGGER.info(s"Exception occured while fetching BusinessUserInfo for user Id: ${businessUserId}",ex)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  private def getAssociatedAccountsAndWorkflowIds(rootAccType: Option[Int], accountIds: Set[Long]): Future[Map[Int, Map[Long, Seq[String]]]] = {
    if(rootAccType.isDefined && rootAccType.get == AccountTypes.DIRECT_EFFECTIV.id) {
      daoAccountV2.getAssociatedAccountsAndWorkflowIds(accountIds)
    } else {
      Future.successful(Map.empty)
    }
  }

  private def getRootAccountDetail(rootAccHierarchy: Option[DtoAccountHierarchy]): Future[Option[DtoAccount]] = {
    if (rootAccHierarchy.isDefined) daoAccount.getAccount(rootAccHierarchy.get.accountId) else Future.successful(None)
  }

  private def getPlatformEnvironmentPermissions(userId: Long, currentAccount: DtoAccount, rootAccountType: Option[Int], accountEnvWorkflows: Map[Int, Map[Long, Seq[String]]]): Future[Option[Seq[PlatformEnvironmentPermissions]]] = {
    if(rootAccountType.isDefined && rootAccountType.get == AccountTypes.DIRECT_EFFECTIV.id){
      val platformGlobalScopeDomains = Domains.getDomainsFilteredByPlatformScope(PlatformPermissionScope.GLOBAL.id)
      val platformWorkflowScopeDomains = Domains.getDomainsFilteredByPlatformScope(PlatformPermissionScope.WORKFLOW_SPECIFIC.id)

      daoAccountV2.getPlatformUserSystemPermissions(userId, rootAccountType).map { platformAccountEnvSysPermissions =>
        Some(accountEnvWorkflows.map { case (envType, accountWfMap) =>
          val workflowIds = accountWfMap.values.flatten.toSeq
          val currentAccountPermissions = platformAccountEnvSysPermissions.get(currentAccount.accountId)
            .map(permissions => DashboardEnvironmentConvertors.toPlatformPermissions(envType, permissions, platformGlobalScopeDomains))

          val workflowSpecificPermissions = accountWfMap.flatMap { case (wfAccountId, workflowIds) =>
            workflowIds.map { workflowId =>
              val wfPermissions = platformAccountEnvSysPermissions.get(wfAccountId)
                .map(permissions => DashboardEnvironmentConvertors.toPlatformPermissions(envType, permissions, platformWorkflowScopeDomains))
              PlatformWorkflowPermission(
                workflowId = workflowId,
                permissions = wfPermissions
              )
            }
          }.toSeq

          PlatformEnvironmentPermissions(
            envType = envType,
            envName = EnvironmentTypes.byId(envType).get.name,
            workflows = workflowIds,
            currentAccountPermissions = currentAccountPermissions,
            workflowSpecificPermissions = if (workflowSpecificPermissions.nonEmpty) Some(workflowSpecificPermissions) else None
          )
        }.toSeq)
      }
    } else Future.successful(None)
  }

  def getByEmail(email: String) = {
    daoBusiness.getUser(email)
  }

  def isLoginRestrictedForSaml(businessUser: DtoBusinessUser): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.getAssociatedV2Accounts(businessUser.id).flatMap {
      case accountsWithParentType if accountsWithParentType.nonEmpty =>
        val accountIds = accountsWithParentType.map(_._1.accountId).toSet
        val hasDirectEffectiv = accountsWithParentType.exists(_._2 == AccountTypes.DIRECT_EFFECTIV.id)
        val isInternalUser = businessUser.email.matches(internalEmailDomains)

        if (hasDirectEffectiv && isInternalUser) {
          Future.successful(Right(false))
        } else {
          val samlPermissions = Set(BusinessUserRoles.SAML_2_0.id, BusinessUserRoles.NEW_SAML.id)
          daoAccountV2.checkPermissionsForAccounts(accountIds, samlPermissions).map {
            case accountPermissions if accountPermissions.nonEmpty =>
              val groupedAccountPermissions = accountPermissions.groupBy(_.accountId)
              val accountsWithSamlPermission = groupedAccountPermissions.collect {
                case (accountId, perms) if perms.map(_.permission).toSet == samlPermissions => accountId
              }.toSet
              Right(accountsWithSamlPermission == accountIds)
            case _ =>
              Right(false)
          }
        }
      case _ =>
        validateAccountIdsHaveNoSaml(businessUser.accountId)
    }
  }

  private def validateAccountIdsHaveNoSaml(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    samlValidator.accountIdsHaveNoSaml(Set(accountId)).map {
      case Right(()) => Right(false)
      case Left(e) => Left(e)
    }
  }

}
