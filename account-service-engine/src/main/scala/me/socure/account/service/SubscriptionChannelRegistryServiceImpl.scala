package me.socure.account.service

import com.mysql.cj.jdbc.Blob
import me.socure.account.service.common.SubscriptionChannelRegistryCacheKeyProvider
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, EnvironmentNotFound, InvalidRequestPayload, SubscriptionChannelRegistryAlreadyExists, SubscriptionChannelRegistryNotDeleted, SubscriptionChannelRegistryNotFound, SubscriptionChannelRegistryUpdateFailed, UnknownError, WebhookSecretKeySizeExceeded, WebhookSecretKeyUpdateFailed}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.subscription.{ChannelAction, ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.account.utils.BlobConversionUtility
import me.socure.account.validator.{SubscriptionChannelValidator, V2Validator}
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.common.publicid.PublicId
import me.socure.common.retry.Retry
import me.socure.common.retry.decider.Decider
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.constants.{DashboardUserPermissions, JsonFormats, SubscriptionFeatureTypes, SubscriptionTypes}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{AlertTypes, DtoSubscriptionChannelRegistry, Metadata, SubscriptionChannelRegistryForAccount, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails, WebhookSecretSourceDetails}
import me.socure.storage.slick.dao.{DaoEnvironment, DaoSubscriptionChannelRegistry}
import me.socure.storage.slick.tables.subscription.{DtoWebhookSecretCredentials, DtoWebhookSecretSource, SubscriptionChannelRegistry, mapper}
import me.socure.storage.slick.tables.subscription.mapper.SubscriptionChannelRegistryMapper
import org.joda.time.DateTimeZone
import org.json4s.JValue
import org.slf4j.LoggerFactory
import scalacache.ScalaCache

import scala.concurrent.duration.Duration
import scala.concurrent.{Await, ExecutionContext, Future, duration}

class SubscriptionChannelRegistryServiceImpl
(daoSubscriptionChannelRegistry: DaoSubscriptionChannelRegistry,
 daoEnvironment: DaoEnvironment,
 v2Validator: V2Validator,
 subscriptionChannelValidator: SubscriptionChannelValidator,
 clock: Clock,
 secretKeyExpiryCheck: Int,
 scalaCache: ScalaCache[_])
(implicit val ec: ExecutionContext)
  extends SubscriptionChannelRegistryService {
  implicit val formats = JsonFormats.formats
  private val logger = LoggerFactory.getLogger(getClass)

  private final lazy val DvSubscriptionTypeId = getDocumentVerificationSubscriptionTypeId()

  private val inputObjectValidations: Seq[SubscriptionChannelRegistry => Boolean] = Seq(
    inputObject => inputObject.status != SubscriptionChannelRegistryStatus.DELETED.id,
    inputObject => SubscriptionChannelRegistryStatus.isValid(inputObject.status),
    inputObject => ChannelType.isValid(inputObject.channelType),
    inputObject => CommunicationMode.isValid(inputObject.communicationMode),
    inputObject => !(inputObject.subscriptionTypeId == DvSubscriptionTypeId && inputObject.communicationMode == CommunicationMode.EMAIL.id)
  )

  override def getSubscriptionChannelRegistry(id: Long,creator: Creator): Future[Either[ErrorResponse, DtoSubscriptionChannelRegistry]] = {

    (for {
      _ <- subscriptionChannelValidator.doesChannelBelongsToAccount(id,creator.accountId)
      res <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(id) map  {
        case Some(result) => Right(SubscriptionChannelRegistryMapper.toDTO(result))
        case None => Left(ErrorResponseFactory.get(SubscriptionChannelRegistryNotFound))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error while fetching SubscriptionChannelRegistry", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while fetching SubscriptionChannelRegistry for id  for $id account ${creator.accountId}", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def hasActiveChannels(accountId: Long, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]] = {
    (for {
      _ <- subscriptionChannelValidator.doesEnvBelongToAccount(accountId,environmentId)
      res <- daoSubscriptionChannelRegistry.hasActiveChannels(environmentId,subscriptionTypeId).map(r=> Right(r))
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error on checking hasActiveChannels", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error on checking hasActiveChannels", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def hasActiveChannelsV2(creator: Creator, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]] = {
    val permissions:Set[Int] = Set()
    (for {
      _ <- v2Validator.validateUserAccountAssociation(creator.userId, creator.accountId)
      _ <- v2Validator.isValidV2EnvironmentRequest(environmentId, Some(creator), permissions)
      res <- daoSubscriptionChannelRegistry.hasActiveChannels(environmentId,subscriptionTypeId).map(r=> Right(r))
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error on checking hasActiveChannelsV2", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error on checking hasActiveChannelsV2", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def createSubscriptionChannelRegistry(requestPayload: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestObject = getCreateSubscriptionChannelRegistryFromRequestPayload(requestPayload = requestPayload)
    val subscriptionChannelRegistry = requestObject._1
    val validRequest = isRequestObjectValid(requestObject._1, inputObjectValidations)
    val creatorOpt = requestObject._5
    val featureTypes = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id) requestObject._2 else Set(0)
    val validFeatureType = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id) SubscriptionFeatureTypes.isValid(subscriptionChannelRegistry.subscriptionTypeId.toInt, featureTypes.filterNot(_ == 0)) else true

    val creationResult =
      if (validRequest && validFeatureType){
        for {
          _ <- subscriptionChannelValidator.createSubscriptioChannelValidations(creatorOpt.get.accountId , subscriptionChannelRegistry.environmentId,subscriptionChannelRegistry.subscriptionTypeId)
          _ <- validateSubscriptionChannelExists(subscriptionChannelRegistry.environmentId, subscriptionChannelRegistry.subscriptionTypeId, subscriptionChannelRegistry.channelType, featureTypes)
          created <- saveSubscriptionChannelRegistry(subscriptionChannelRegistry, requestObject._3, requestObject._4, featureTypes)
        } yield created
      }
      else
        Future.successful(Left(ErrorResponseFactory.get(InvalidRequestPayload)))

    creationResult
      .recover {
        case ex: ErrorResponseException =>
          logger.info("Error while fetching SubscriptionChannelRegistry", ex)
          Left(ex.errorResponse)
        case ex =>
          logger.info("Error while creating a new SubscriptionChannelRegistry", ex)
          Left(ErrorResponseFactory.get(UnknownError))
      }
  }

  override def createSubscriptionChannelRegistryV2(requestPayload: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestObject = getCreateSubscriptionChannelRegistryFromRequestPayload(requestPayload = requestPayload)
    val subscriptionChannelRegistry = requestObject._1
    val creatorOpt = requestObject._5
    val featureTypes = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id) requestObject._2 else Set(0)
    val validRequest = isRequestObjectValid(subscriptionChannelRegistry, inputObjectValidations)
    val validFeatureType = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id) SubscriptionFeatureTypes.isValid(subscriptionChannelRegistry.subscriptionTypeId.toInt, featureTypes.filterNot(_ == 0)) else true

    if (!(subscriptionChannelValidator.isSecretKeyValid(subscriptionChannelRegistry.secretKey.getOrElse("")))) {
      Future.successful(Left(ErrorResponseFactory.get(WebhookSecretKeySizeExceeded)))
    }else {
      val creationResult =
        if (validRequest && validFeatureType) {
          for {
            _ <- v2Validator.isValidV2EnvironmentRequest(subscriptionChannelRegistry.environmentId, creatorOpt, Set(DashboardUserPermissions.EVENT_MANAGERS_CREATE.id))
            //          _ <- subscriptionChannelValidator.doesAccountSubscriptionTypeProvisioned(creatorOpt.get.accountId,subscriptionChannelRegistry.subscriptionTypeId)
            _ <- validateSubscriptionChannelExists(subscriptionChannelRegistry.environmentId, subscriptionChannelRegistry.subscriptionTypeId, subscriptionChannelRegistry.channelType, featureTypes)
            created <- saveSubscriptionChannelRegistry(subscriptionChannelRegistry, requestObject._3, requestObject._4, featureTypes)
          } yield created
        } else
          Future.successful(Left(ErrorResponseFactory.get(InvalidRequestPayload)))

      creationResult
        .recover {
          case ex: ErrorResponseException =>
            logger.info("Error while validating SubscriptionChannelRegistry", ex)
            Left(ex.errorResponse)
          case ex =>
            logger.info("Error while creating a new SubscriptionChannelRegistry", ex)
            Left(ErrorResponseFactory.get(UnknownError))
        }
    }
  }

  override def updateSubscriptionChannelRegistry(id: Long, requestPayload: JValue): Future[Either[ErrorResponse, Boolean]] = {
    val requestObject = getSubscriptionChannelRegistryFromRequestPayload(id, requestPayload)
    val subscriptionChannelRegistry = requestObject._1
    val validRequest = isRequestObjectValid(subscriptionChannelRegistry, inputObjectValidations)
    val creatorOpt = requestObject._4
    val validFeatureType = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id && subscriptionChannelRegistry.featureTypeId != 0) SubscriptionFeatureTypes.isValid(subscriptionChannelRegistry.subscriptionTypeId.toInt, subscriptionChannelRegistry.featureTypeId) else true

    val updateResult =
      if (validRequest && validFeatureType)
        for {
          _ <- subscriptionChannelValidator.updateSubscriptionChannelValidations(creatorOpt.get.accountId , requestObject._1.environmentId,requestObject._1.subscriptionTypeId , channelId = id)
          existingSubscriptionChannelRegistry <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(id)
          potentialConflictingRegistry <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(subscriptionChannelRegistry.environmentId, subscriptionChannelRegistry.subscriptionTypeId, subscriptionChannelRegistry.channelType, Set(subscriptionChannelRegistry.featureTypeId))
          updated <- validateAndUpdateSubscriptionChannelRegistry(existingSubscriptionChannelRegistry, potentialConflictingRegistry.headOption, subscriptionChannelRegistry, requestObject._2, requestObject._3)
        } yield updated
      else
        Future.successful(Left(ErrorResponseFactory.get(InvalidRequestPayload)))
    updateResult
      .recover {
        case ex: ErrorResponseException =>
          logger.info("Error while fetching SubscriptionChannelRegistry", ex)
          Left(ex.errorResponse)
        case ex =>
          logger.info(s"Error while updating SubscriptionChannelRegistry for Id $id", ex)
          Left(ErrorResponseFactory.get(UnknownError))
      }
  }

  override def updateSubscriptionChannelRegistryV2(id: Long, requestPayload: JValue): Future[Either[ErrorResponse, Boolean]] = {
    val requestObject = getSubscriptionChannelRegistryFromRequestPayload(id = id, requestPayload = requestPayload)
    val subscriptionChannelRegistry = requestObject._1
    val creatorOpt = requestObject._4
    val validRequest = isRequestObjectValid(subscriptionChannelRegistry, inputObjectValidations)
    val validFeatureType = if(subscriptionChannelRegistry.subscriptionTypeId.toInt == SubscriptionTypes.Watchlist_Monitoring.id && subscriptionChannelRegistry.featureTypeId != 0) SubscriptionFeatureTypes.isValid(subscriptionChannelRegistry.subscriptionTypeId.toInt, subscriptionChannelRegistry.featureTypeId) else true

    if (!(subscriptionChannelValidator.isSecretKeyValid(subscriptionChannelRegistry.secretKey.getOrElse("")))) {
      Future.successful(Left(ErrorResponseFactory.get(WebhookSecretKeySizeExceeded)))
    } else {
      val updateResult =
        if (validRequest && validFeatureType)
          for {
            _ <- v2Validator.isValidV2EnvironmentRequest(subscriptionChannelRegistry.environmentId, creatorOpt, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
            //          _ <- subscriptionChannelValidator.doesAccountSubscriptionTypeProvisioned(creatorOpt.get.accountId,subscriptionChannelRegistry.subscriptionTypeId)
            existingSubscriptionChannelRegistry <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(id)
            potentialConflictingRegistry <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(subscriptionChannelRegistry.environmentId, subscriptionChannelRegistry.subscriptionTypeId, subscriptionChannelRegistry.channelType, Set(subscriptionChannelRegistry.featureTypeId))
            updated <- validateAndUpdateSubscriptionChannelRegistry(existingSubscriptionChannelRegistry, potentialConflictingRegistry.headOption, subscriptionChannelRegistry, requestObject._2, requestObject._3)
          } yield updated
        else
          Future.successful(Left(ErrorResponseFactory.get(InvalidRequestPayload)))

      updateResult
        .recover {
          case ex: ErrorResponseException =>
            logger.info("Error while fetching SubscriptionChannelRegistry", ex)
            Left(ex.errorResponse)
          case ex =>
            logger.info(s"Error while updating SubscriptionChannelRegistry for Id $id", ex)
            Left(ErrorResponseFactory.get(UnknownError))
        }
    }
  }

  override def updateSubscriptionChannelState(id: Long, actionName: String , creator: Creator): Future[Either[ErrorResponse, Boolean]] = {

    ChannelAction.getActionByName(actionName) match {
      case Some(action) => {
        SubscriptionChannelRegistryStatus.byId(action.statusId) match {
          case Some(status) =>
            subscriptionChannelValidator.doesChannelBelongsToAccount(accountId = creator.accountId , channelId = id) flatMap {
              case true => {
                daoSubscriptionChannelRegistry.updateSubscriptionChannelState(id, status.id) map {
                  case res if res => Right(res)
                  case _ => Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelStatusNotUpdated))
                } recover {
                  case ex =>
                    logger.info(s"Error while updating the status to ${status.id} of channel $id", ex)
                    Left(ErrorResponseFactory.get(UnknownError))
                }
              }
              case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelInvalidActionStatusMapping)))
            }
          case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelInvalidActionStatusMapping)))
        }
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelInvalidAction)))
    }
  }

  override def updateSubscriptionChannelStateV2(id: Long, actionName: String, creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    ChannelAction.getActionByName(actionName) match {
      case Some(action) => {
        SubscriptionChannelRegistryStatus.byId(action.statusId) match {
          case Some(status) =>
            (for {
              environmentIdOpt <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(id).map(_.map(_.environmentId))
              _ <- v2Validator.validateUserAccountAssociation(creator.userId, creator.accountId)
              _ <- v2Validator.isValidV2EnvironmentRequest(environmentId = environmentIdOpt.get, Some(creator), permissions =  Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id))
              res <- daoSubscriptionChannelRegistry.updateSubscriptionChannelState(id, status.id) map {
                case res if res => Right(res)
                case _ => Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelStatusNotUpdated))
              }
            } yield res) recover{
              case ex =>
                logger.info(s"Error while updating the status to ${status.id} of channel $id", ex)
                Left(ErrorResponseFactory.get(UnknownError))
            }
          case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelInvalidActionStatusMapping)))
        }
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelInvalidAction)))
    }
  }

  override def deleteSubscriptionChannelRegistry(id: Long,creator: Creator): Future[Either[ErrorResponse, Boolean]] = {

    (for {
      _ <- subscriptionChannelValidator.doesChannelBelongsToAccount(id,creator.accountId)
      res <- daoSubscriptionChannelRegistry.deleteSubscriptionChannelRegistryById(id) map{ isDeleted =>
        if (isDeleted)
          Right(isDeleted)
        else
          Left(ErrorResponseFactory.get(SubscriptionChannelRegistryNotDeleted))
      }
    } yield res)
      .recover {
        case ex: ErrorResponseException =>
          logger.info("Error while deleting SubscriptionChannelRegistry", ex)
          Left(ex.errorResponse)
        case ex =>
          logger.info(s"Error while deleting SubscriptionChannelRegistry for Id $id", ex)
          Left(ErrorResponseFactory.get(UnknownError))
      }
  }

  override def getSubscriptionChannelRegistries(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {

    (for {
      _ <- subscriptionChannelValidator.doesEnvBelongToAccount(creator.accountId, environmentId)
      res <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByEnvironmentId(environmentId) map { results =>
        Right(results.map( res => SubscriptionChannelRegistryMapper.toDTO(res._1._1, res._1._2, res._2)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error while fetching SubscriptionChannelRegistry", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while fetching SubscriptionChannelRegistry for environmentId for $environmentId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def getSubscriptionChannelRegistriesForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[SubscriptionChannelRegistryForAccount]]] = {
    daoSubscriptionChannelRegistry.getSubscriptionTypes() flatMap {
      case st if st.nonEmpty =>
        daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesForAccount(accountId) map {
          case subscriptionChannelRegistries if subscriptionChannelRegistries.nonEmpty =>
            val subscriptions = subscriptionChannelRegistries.flatMap{sr => sr._2.map(SubscriptionChannelRegistryMapper.toView(_, st, sr._1))}
            val subscriptionChannelRegistryForAccount = subscriptions.groupBy(_.environmentTypeId).map(v => SubscriptionChannelRegistryForAccount(v._1, v._2)).toSeq
            Right(subscriptionChannelRegistryForAccount)
          case _ =>
            logger.info(s"Error while fetching SubscriptionChannelRegistry for account($accountId)")
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ =>
        logger.info(s"Error while fetching SubscriptionTypes")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
        case ex: ErrorResponseException =>
          logger.info(s"Error while fetching SubscriptionChannelRegistry for account($accountId)", ex)
          (Left(ex.errorResponse))
        case e: Exception =>
          logger.info(s"Error while fetching SubscriptionChannelRegistry for account($accountId)", e)
          (Left(ErrorResponseFactory.get(UnknownError)))
      }
  }

  override def getSubscriptionChannelRegistries(environmentId: Long, subscriptionTypeId: Long , accountId: Long): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {
    (for {
      _ <- subscriptionChannelValidator.doesEnvBelongToAccount(accountId, environmentId)
      res <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByEnvironmentIdAndSubscriptionTypeId(environmentId, subscriptionTypeId) map { results =>
        Right(results.map(res => SubscriptionChannelRegistryMapper.toDTO(res._1._1, res._1._2, res._2)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error while fetching SubscriptionChannelRegistry", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while fetching SubscriptionChannelRegistry for (environmentId, subscriptionTypeId) for ($environmentId, $subscriptionTypeId)", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def getSubscriptionChannelRegistriesV2(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {
    (for {
      _ <- v2Validator.validateUserAccountAssociation(creator.userId, creator.accountId)
      _ <- v2Validator.isValidV2EnvironmentRequest(environmentId, Some(creator), Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id))
      res <- daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByEnvironmentId(environmentId) map { results =>
        Right(results.map( res => SubscriptionChannelRegistryMapper.toDTO(res._1._1, res._1._2, res._2)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error while fetching SubscriptionChannelRegistry", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while fetching SubscriptionChannelRegistry for environmentId for $environmentId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def getSubscriptionChannelRegistryWithAccount(
                                                          accountId: Long,
                                                          environmentTypeId: Long,
                                                          subscriptionTypeId: Long, featureTypeId: Option[Int]
                                                        ): Future[Either[ErrorResponse, SubscriptionChannelRegistryWithAccount]] = {

    def getWebhooks(parentId: Long, publicId: String) = {
      isSubscriptionProvisionedForAccount(parentId, subscriptionTypeId) flatMap { p =>
        getSubscriptionChannelRegistriesByAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId) map {
          case channelRegistrySeq =>
            val dtoChannelRegistrySeq = channelRegistrySeq.map(channel => SubscriptionChannelRegistryMapper.toDTO(channel._1, channel._2, channel._3))
            Right(SubscriptionChannelRegistryWithAccount(dtoChannelRegistrySeq, PublicId(publicId), p, dtoChannelRegistrySeq.headOption.flatMap(_.secretKey)))
          case _ =>
            Right(SubscriptionChannelRegistryWithAccount(Seq.empty[DtoSubscriptionChannelRegistry], PublicId(publicId), p, None))
        }
      }
    }

    daoSubscriptionChannelRegistry.getAccountDetails(accountId) flatMap {
        case Some(ad) =>
          v2Validator.getRootParentAccount(accountId) map {
            case Some(rp) => rp
            case None => ad.parentId.getOrElse(accountId)
          } flatMap { parentId: Long =>
            getWebhooks(parentId, ad.publicId)
          }
        case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
      } recover {
        case e =>
          logger.info(s"Error while fetching subscription channel registry for (accountId, environmentTypeId, subscriptionTypeId) for ($accountId, $environmentTypeId, $subscriptionTypeId)", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
  }

  def getSubscriptionChannelRegistriesByAccount(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int]): Future[Seq[(SubscriptionChannelRegistry, Option[DtoWebhookSecretSource], Option[DtoWebhookSecretCredentials])]] = {
    featureTypeId match {
      case Some(feature) => daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByAccountEnvSubTypeAndFeatureTypeWithOauth(accountId, environmentTypeId, subscriptionTypeId, feature)
      case None => daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByAccount(accountId, environmentTypeId, subscriptionTypeId)
    }
  }

  override def updateSecretKeyForWatchlistWebhook(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String, featureTypeId: Option[Int]): Future[Either[ErrorResponse, Int]] = {
    if(!subscriptionChannelValidator.isSecretKeyValid(secretKey)) Future.successful(Left(ErrorResponseFactory.get(WebhookSecretKeySizeExceeded)))
    else{
      v2Validator.validateAccountPermissionProvisioned(accountId, BusinessUserRoles.OauthAuthenticatedWebhook.id).flatMap {
        case true =>
          logger.info(s"Webhook secretkey cannot be updated when Oauth is enabled for Account: $accountId, EnvironmentType:$environmentTypeId, SubscriptionType: $subscriptionTypeId")
          Future.successful(Left(ErrorResponseFactory.get(WebhookSecretKeyUpdateFailed)))
        case false =>
          getSubscriptionChannelRegistries(accountId, environmentTypeId, subscriptionTypeId, CommunicationMode.WEBHOOK.id, getFeatureTypeForUpdateSecretKey(subscriptionTypeId, featureTypeId)) flatMap {
            case subscriptionChannelRegistries if subscriptionChannelRegistries.length == 1 =>
              val scr = subscriptionChannelRegistries.head
              scr.secretKey match {
                case Some(sk) if sk.equals(secretKey) =>
                  logger.info(s"No Updates as Existing and provided SecretKey is same for SubscriptionChannelRegistrations found for Account:$accountId, EnvironmentType:$environmentTypeId, SubscriptionType: $subscriptionTypeId, CommunicationMode:Webhook, FeatureType: $featureTypeId")
                  Future.successful(Right(0))
                case _ =>
                  logger.info(s"Update SecretKey for SubscriptionChannelRegistrations found for Account:$accountId, EnvironmentType:$environmentTypeId, SubscriptionType: $subscriptionTypeId, CommunicationMode:Webhook, FeatureType: $featureTypeId")
                  daoSubscriptionChannelRegistry.updateSecretKey(scr.id, secretKey).map(Right(_))
              }
            case subscriptionChannelRegistries if subscriptionChannelRegistries.length > 1 =>
              logger.info(s"Invalid: ${subscriptionChannelRegistries.length} SubscriptionChannelRegistrations found for Account:$accountId, EnvironmentType:$environmentTypeId, SubscriptionType: $subscriptionTypeId, CommunicationMode:Webhook, FeatureType: $featureTypeId")
              Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
            case _ =>
              logger.info(s"No SubscriptionChannelRegistrations found for Account:$accountId, EnvironmentType:$environmentTypeId, SubscriptionType: $subscriptionTypeId, CommunicationMode:Webhook, FeatureType: $featureTypeId")
              Future.successful(Left(ErrorResponseFactory.get(SubscriptionChannelRegistryNotFound)))
          }
      }
    }
  }

  private def getFeatureTypeForUpdateSecretKey(subTypeId: Long, featureTypeId: Option[Int]): Option[Int] = {
    featureTypeId match {
      case Some(featureType) => Some(featureType)
      case None => if(subTypeId == SubscriptionTypes.Watchlist_Monitoring.id) Some(SubscriptionFeatureTypes.Watchlist_Monitoring_Event.id) else None
    }
  }

  private def getSubscriptionChannelRegistries(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, communicationMode: Int, featureTypeId: Option[Int]): Future[Seq[SubscriptionChannelRegistry]] = {
    featureTypeId match {
      case Some(feature) => daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByAccountEnvSubTypeCommAndFeatureType(accountId, environmentTypeId, subscriptionTypeId, communicationMode, feature)
      case None => daoSubscriptionChannelRegistry.getSubscriptionChannelRegistries(accountId, environmentTypeId, subscriptionTypeId, communicationMode)
    }
  }

  override def getSubscriptionTypes(): Future[List[Metadata]] = {
    daoSubscriptionChannelRegistry.getSubscriptionTypes() map {
      case types if types.nonEmpty => types.map(t => Metadata(t.id.toInt, t.name)).toList
      case _ => List.empty[Metadata]
    }
  }

  override def getChannelTypes(): Future[List[Metadata]] = Future {
    ChannelType.values.map(v => Metadata(v.id, v.toString)).toList
  }

  override def getCommunicationModes(): Future[List[Metadata]] = Future {
    CommunicationMode.values.map(v => Metadata(v.id, v.toString)).toList
  }

  override def getActions(): Future[List[Metadata]] = Future {
    ChannelAction.values.map(v => Metadata(v.id, v.toString)).toList
  }

  override def getSecretKeyRotationDetails(): Future[Either[ErrorResponse, Seq[WebhookSecretKeyRotationDetails]]] = {
    daoSubscriptionChannelRegistry.getAllExpiredWebhookSecretSources(secretKeyExpiryCheck).map{ res =>
      Right(res.map(secretSrc =>
        WebhookSecretKeyRotationDetails(
          subscriptionChannelId = secretSrc._1.subscriptionChannelId,
          secretKeyEndpoint = secretSrc._1.secretKeyEndpoint,
          clientId = secretSrc._2.clientId,
          clientSecret = secretSrc._2.clientSecret,
          secretExpiresOn = secretSrc._1.secretExpiresOn,
          lastSuccessfulSync = secretSrc._1.lastSuccessfulSync,
          lastFailedSync = secretSrc._1.lastFailedSync,
          errorCode = secretSrc._1.errorCode,
          failedSyncCount = secretSrc._1.failedSyncCount
        )
      ))
    }.recover{
      case e =>
        logger.info(s"Error while fetching expired secret key sources", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  override def updateSecretKeyRotation(updateWebhookSecretKey: Seq[UpdateWebhookSecretKeyRotation]): Future[Either[ErrorResponse, Boolean]] = {
    daoSubscriptionChannelRegistry.getSubscriptionAccountAndEnvironmentId(updateWebhookSecretKey.map(_.subscriptionChannelId)).flatMap { cacheDetails =>
      val cacheKeys = cacheDetails.map{ cd =>
        val featureTypeKey = if(cd._4 == 0) None else Some(cd._4)
        SubscriptionChannelRegistryCacheKeyProvider.provide(cd._1, cd._2, cd._3, featureTypeKey)
      }.toSet
      removeCacheKeysWrapped("/settings/channel/secret_key_rotation", scalaCache, cacheKeys)(daoSubscriptionChannelRegistry.updateWebhookSecretKeyRotation(updateWebhookSecretKey)) map {
        case res => Right(res)
        case _ =>
          logger.info("Could not update webhook secret key rotation")
          Left(ErrorResponseFactory.get(ExceptionCodes.SubscriptionChannelRegistryUpdateFailed))
      }
    }.recover{
      case e =>
        logger.info(s"Error while updating webhook secret key rotation", e)
        Left(ErrorResponseFactory.get(SubscriptionChannelRegistryUpdateFailed))
    }
  }

  private def getSubscriptionChannelRegistryFromRequestPayload(id: Long = 0L, requestPayload: JValue): (SubscriptionChannelRegistry, Option[DtoWebhookSecretSource], Option[DtoWebhookSecretCredentials], Option[Creator]) = {
    val subscriptionChannelRegistry = SubscriptionChannelRegistry(
      id = id,
      communicationSource = (requestPayload \ "communicationSource").extract[String],
      metadata = Option(DtoSubscriptionChannelRegistry.serialize(
        (requestPayload \ "metadata").extract[Map[String, Any]]
      )),
      status = (requestPayload \ "status").extract[Int],
      subscriptionTypeId = (requestPayload \ "subscriptionTypeId").extract[Long],
      name = (requestPayload \ "name").extract[Option[String]],
      secretKey = (requestPayload \ "secretKey").extract[Option[String]],
      environmentId = (requestPayload \ "environmentId").extract[Long],
      channelType = (requestPayload \ "channelType").extract[Int],
      communicationMode = (requestPayload \ "communicationMode").extract[Int],
      createdAt = clock.now(),
      updatedAt = clock.now(),
      featureTypeId = (requestPayload \ "featureTypes").extract[Set[Int]].head,
      alertTypes = getAlertTypes((requestPayload \ "alertTypes").extract[AlertTypes])
    )
    val creator = (requestPayload \ "creator").extract[Option[Creator]]
    val webhookSecretSourceDetails = (requestPayload \ "webhookSecretSourceDetails").extract[Option[WebhookSecretSourceDetails]]
    webhookSecretSourceDetails match {
      case Some(secretSourceDetails) =>
        val dtoWebhookSecretSource = DtoWebhookSecretSource(
          id = 0L,
          subscriptionChannelId = id,
          secretKeyEndpoint = secretSourceDetails.secretSourceUrl,
          createdAt = clock.now(),
          updatedAt = Some(clock.now()),
          secretExpiresOn = secretSourceDetails.secretExpiresOn,
          lastSuccessfulSync = secretSourceDetails.lastSuccessfulSync,
          lastFailedSync = secretSourceDetails.lastFailedSync,
          errorCode = secretSourceDetails.errorCode,
          failedSyncCount = secretSourceDetails.failedSyncCount
        )
        val dtoWebhookSecretCredentials = DtoWebhookSecretCredentials(
          webhookSecretSourceId = 0L,
          clientId = secretSourceDetails.clientId,
          clientSecret = secretSourceDetails.clientSecret
        )
        (subscriptionChannelRegistry, Some(dtoWebhookSecretSource), Some(dtoWebhookSecretCredentials), creator)
      case None =>
        (subscriptionChannelRegistry, None, None, creator)
    }

  }

  private def getAlertTypes(alertTypes: AlertTypes) : String = {
    val alertTypesDAO = stringValue(alertTypes.add) + "," + stringValue(alertTypes.change) + "," + stringValue(alertTypes.delete)
    alertTypesDAO
  }

  private def stringValue(input: Boolean): String = {
    input match {
      case true => "1"
      case false => "0"
    }
  }

  private def getCreateSubscriptionChannelRegistryFromRequestPayload(id: Long = 0L, requestPayload: DtoSubscriptionChannelRegistry): (SubscriptionChannelRegistry, Set[Int], Option[DtoWebhookSecretSource], Option[DtoWebhookSecretCredentials], Option[Creator]) = {
    val subscriptionChannelRegistry = SubscriptionChannelRegistry(
      id = id,
      communicationSource = requestPayload.communicationSource,
      metadata = Option(DtoSubscriptionChannelRegistry.serialize(requestPayload.metadata)),
      status = requestPayload.status,
      subscriptionTypeId = requestPayload.subscriptionTypeId,
      name = requestPayload.name,
      secretKey = requestPayload.secretKey,
      environmentId = requestPayload.environmentId,
      channelType = requestPayload.channelType,
      communicationMode = requestPayload.communicationMode,
      createdAt = clock.now(),
      updatedAt = clock.now(),
      alertTypes = getAlertTypes(requestPayload.alertTypes)
    )
    val creator = requestPayload.creator
    val webhookSecretSourceDetails = requestPayload.webhookSecretSourceDetails
    webhookSecretSourceDetails match {
      case Some(secretSourceDetails) =>
        val dtoWebhookSecretSource = DtoWebhookSecretSource(
          id = 0L,
          subscriptionChannelId = id,
          secretKeyEndpoint = secretSourceDetails.secretSourceUrl,
          createdAt = clock.now(),
          updatedAt = Some(clock.now()),
          secretExpiresOn = secretSourceDetails.secretExpiresOn,
          lastSuccessfulSync = secretSourceDetails.lastSuccessfulSync,
          lastFailedSync = secretSourceDetails.lastFailedSync,
          errorCode = secretSourceDetails.errorCode,
          failedSyncCount = secretSourceDetails.failedSyncCount
        )
        val dtoWebhookSecretCredentials = DtoWebhookSecretCredentials(
          webhookSecretSourceId = 0L,
          clientId = secretSourceDetails.clientId,
          clientSecret = secretSourceDetails.clientSecret
        )
        (subscriptionChannelRegistry, requestPayload.featureTypes, Some(dtoWebhookSecretSource), Some(dtoWebhookSecretCredentials), creator)
      case None =>
        (subscriptionChannelRegistry, requestPayload.featureTypes, None, None, creator)
    }
  }

  private def isRequestObjectValid(requestObject: SubscriptionChannelRegistry, validations: Seq[SubscriptionChannelRegistry => Boolean]): Boolean = {
    validations.forall(validation => validation(requestObject))
  }

  private def saveSubscriptionChannelRegistry(
                                               requestObject: SubscriptionChannelRegistry,
                                               dtoWebhookSecretSource: Option[DtoWebhookSecretSource],
                                               dtoWebhookSecretCredentials: Option[DtoWebhookSecretCredentials], featureTypes: Set[Int]
                                             ): Future[Either[ErrorResponse, Boolean]] = {
    (dtoWebhookSecretSource, dtoWebhookSecretCredentials) match {
      case (Some(dtoWss), Some(dtoWsc)) =>
        daoSubscriptionChannelRegistry.createSubscriptionChannelRegistryWithWebhookSourceDetails(requestObject, dtoWss, dtoWsc, featureTypes).map{scrId =>
          Right(true)
        }
      case (_,_) => daoSubscriptionChannelRegistry.createSubscriptionChannelRegistry(requestObject, featureTypes).map(Right(_))
    }
  }

  private def validateSubscriptionChannelExists(envId: Long, subTypeId: Long, channelType: Int, featureTypes: Set[Int]): Future[Boolean]  = {
    daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryByEnvIdAndSubTypeIdAndChannelTypeAndFeatureType(envId, subTypeId, channelType, featureTypes).map{ subChannel =>
      subTypeId.toInt match {
        case SubscriptionTypes.Watchlist_Monitoring.id =>
          if(featureTypes.exists(featureId => subChannel.exists(s => s.subscriptionTypeId == subTypeId && s.featureTypeId == featureId))){
            throw ErrorResponseException(ErrorResponseFactory.get(SubscriptionChannelRegistryAlreadyExists))
          }
        case SubscriptionTypes.Document_Verification.id =>
          if(subChannel.exists(s => s.subscriptionTypeId == subTypeId)) throw ErrorResponseException(ErrorResponseFactory.get(SubscriptionChannelRegistryAlreadyExists))
      }
      true
    }
  }

  private def validateAndUpdateSubscriptionChannelRegistry(
                                                            existingSubscriptionChannelRegistry: Option[SubscriptionChannelRegistry],
                                                            potentialConflictingRegistry: Option[SubscriptionChannelRegistry],
                                                            requestObject: SubscriptionChannelRegistry,
                                                            dtoWebhookSecretSource: Option[DtoWebhookSecretSource],
                                                            dtoWebhookSecretCredentials: Option[DtoWebhookSecretCredentials]
                                                          ): Future[Either[ErrorResponse, Boolean]] = {
    existingSubscriptionChannelRegistry match {
      case Some(existingRegistry) =>
        if (!(potentialConflictingRegistry forall (_.id == requestObject.id)) ||
          (existingRegistry.status == SubscriptionChannelRegistryStatus.DISABLED.id &&
            requestObject.status == SubscriptionChannelRegistryStatus.ACTIVE.id))
          Future.successful(Left(ErrorResponseFactory.get(SubscriptionChannelRegistryUpdateFailed)))
        else
          (dtoWebhookSecretSource, dtoWebhookSecretCredentials) match {
            case (Some(dtoWss), Some(dtoWsc)) =>
              daoSubscriptionChannelRegistry.getSubscriptionChannelWebhookSourceDetails(requestObject.copy(createdAt = existingRegistry.createdAt.withZone(DateTimeZone.UTC), updatedAt = clock.now()).id).flatMap {
                case Some(wss) =>
                  val newFailureCount = (dtoWss.failedSyncCount, wss._1.failedSyncCount) match {
                    case (Some(count), Some(prevCount)) => Some(prevCount+1)
                    case (Some(count), None) => Some(count)
                    case _ => None
                  }
                  daoSubscriptionChannelRegistry.updateSubscriptionChannelRegistryWithWebhookSourceDetails(requestObject,
                    dtoWss.copy(id = wss._1.id, failedSyncCount = newFailureCount),
                    dtoWsc.copy(webhookSecretSourceId = wss._1.id)
                  ).map(Right(_))
                case None =>
                  daoSubscriptionChannelRegistry.updateSubscriptionChannelRegistryCreateWebhookSourceDetails(requestObject,
                    dtoWss,
                    dtoWsc
                  ).map(Right(_))
              }
            case (_, _) => daoSubscriptionChannelRegistry.updateSubscriptionChannelRegistry(
              requestObject.copy(createdAt = existingRegistry.createdAt.withZone(DateTimeZone.UTC), updatedAt = clock.now())
            ).map(Right(_))
          }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(SubscriptionChannelRegistryUpdateFailed)))
    }
  }

  private def getDocumentVerificationSubscriptionTypeId(): Long = {
    implicit val strategy = RetryStrategy.times(3)
    implicit val decider = Decider.default[Long]
    Await.result(Retry(daoSubscriptionChannelRegistry.getDocumentVerificationSubscriptionTypeId()), Duration(60, duration.SECONDS))
  }

  private def isSubscriptionProvisionedForAccount(accountId: Long, subscriptionTypeId: Long): Future[Boolean] = {
    if(subscriptionTypeId == SubscriptionTypes.Watchlist_Monitoring.id) {
      daoSubscriptionChannelRegistry.isAccountProvisioned(accountId, subscriptionTypeId).flatMap{
        case true => Future.successful(true)
        case false => v2Validator.validateAccountPermissions(accountId, Set(BusinessUserRoles.WATCHLIST_3_0.id, BusinessUserRoles.WATCHLIST_CASE_MANAGEMENT.id))
      }
    } else {
      daoSubscriptionChannelRegistry.isAccountProvisioned(accountId, subscriptionTypeId)
    }
  }

}
