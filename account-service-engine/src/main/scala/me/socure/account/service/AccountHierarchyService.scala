package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.{AccountTypes, Status}
import me.socure.convertors.AccountConvertors
import me.socure.model.account.{AccountHierarchy, AccountHierarchyInput, AccountInfoV2WithIndustry}
import me.socure.model.dashboardv2.Creator
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.account.{DtoAccount, DtoAccountAssociationHistory, DtoAccountHierarchy, DtoUserAccountAssociation}
import org.joda.time.{DateTime, DateTimeZone}
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountHierarchyService (daoAccount: DaoAccount, daoAccountV2: DaoAccountV2,v2Validator: V2Validator, clock: Clock)(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getDirectCustomerAccountInfo(path: String, uaa: DtoUserAccountAssociation): Future[Right[Nothing, Seq[AccountInfoV2WithIndustry]]] = {
    val accountInfo = if(uaa.isPrimaryUser) daoAccountV2.getAccountInfoV2WithIndustry(path)
    else daoAccountV2.getAccountInfoV2WithIndustry(path, uaa.businessUserId)
    accountInfo map { obj =>
      Right(obj.map { accInfo =>
        AccountInfoV2WithIndustry(accInfo._1.id,
          accInfo._1.accountId,
          accInfo._2.publicId,
          accInfo._2.name,
          accInfo._1.hierarchyPath,
          accInfo._1.accountType,
          accInfo._1.administer,
          accInfo._1.numberOfPrimaryAdmins,
          state = true,
          accInfo._3.description,
          accInfo._2.isActive
        )
      })
    }
  }

  def getPartnerAccountInfo(path: String, uaa: DtoUserAccountAssociation): Future[Right[Nothing, Seq[AccountInfoV2WithIndustry]]] = {
    val accountInfo = if(uaa.isPrimaryUser) daoAccountV2.getAccountInfoV2WithIndustry(path)
    else daoAccountV2.getAccountInfoV2WithIndustry(path, uaa.businessUserId)
    accountInfo map { obj =>
      Right(obj.map { accInfo =>
        AccountInfoV2WithIndustry(accInfo._1.id,
          accInfo._1.accountId,
          accInfo._2.publicId,
          accInfo._2.name,
          accInfo._1.hierarchyPath,
          accInfo._1.accountType,
          accInfo._1.administer,
          accInfo._1.numberOfPrimaryAdmins,
          state = if((accInfo._1.accountType != AccountTypes.SUB_ACCOUNT.id) || accInfo._1.hierarchyPath.equals(path)) true else false,
          accInfo._3.description,
          accInfo._2.isActive)
      })
    }
  }

  def getCascadeOptedPartnerAccountInfo(path: String, uaa: DtoUserAccountAssociation): Future[Right[Nothing, Seq[AccountInfoV2WithIndustry]]] = {
    val accountInfo = if(uaa.isPrimaryUser) daoAccountV2.getAccountInfoV2WithIndustry(path)
                        else daoAccountV2.getAccountInfoV2WithIndustry(path, uaa.businessUserId)
    accountInfo map { obj =>
      val firstLevelSubaccountInfo = obj.filter(_._1.hierarchyPath.count(_ == '/') == 2).map{ f =>
        (f._1.hierarchyPath, f._1.administer)
      }
      Right(obj.map { accInfo =>
        AccountInfoV2WithIndustry(accInfo._1.id,
          accInfo._1.accountId,
          accInfo._2.publicId,
          accInfo._2.name,
          accInfo._1.hierarchyPath,
          accInfo._1.accountType,
          accInfo._1.administer,
          accInfo._1.numberOfPrimaryAdmins,
          state = if((accInfo._1.accountType != AccountTypes.SUB_ACCOUNT.id) || accInfo._1.hierarchyPath.equals(path)) true
                  else {
                      firstLevelSubaccountInfo.find(p => accInfo._1.hierarchyPath.startsWith(p._1)) match {
                        case Some(v) =>v._2
                        case _ => false
                      }
                  },
          accInfo._3.description,
          accInfo._2.isActive)
      })
    }
  }

  def listAccountHierarchy(accountId: Long, businessUserId: Long): Future[Either[ErrorResponse, Seq[AccountInfoV2WithIndustry]]] = {
    val response = for {
      res <-daoAccountV2.getUserAccountAssociation(businessUserId, accountId).flatMap {
        case Some(uaa) =>
          daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
            case Some(ah) =>
                v2Validator.fetchParent(ah) flatMap {
                  case Some(pah) =>
                    if(AccountTypes.isTypeDirect(pah.accountType)) getDirectCustomerAccountInfo(ah.hierarchyPath, uaa)
                    else daoAccountV2.isAccountPermissionProvisioned(pah.accountId, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id) flatMap {
                          case true => getCascadeOptedPartnerAccountInfo(ah.hierarchyPath, uaa)
                          case false => getPartnerAccountInfo(ah.hierarchyPath, uaa)
                      }
                }
              }
        case None =>
          logger.info(s"No hierarchy found for accountId:$accountId and userId:$businessUserId")
          Future.successful(Right(Seq.empty[AccountInfoV2WithIndustry]))
      }
    } yield res

    response recover {
      case ere: ErrorResponseException =>
        logger.info(s"Error occurred while fetching account hierarchy by account, for accountId:$accountId and userId:$businessUserId", ere)
        Left(ere.errorResponse)
      case e: Throwable =>
        logger.info(s"Error occurred while fetching account hierarchy by account, for accountId:$accountId and userId:$businessUserId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError))
    }
  }

  def getAccountHierarchy(id: Long): Future[Either[ErrorResponse, AccountHierarchy]] = {
    daoAccountV2.getAccountHierarchy(id) map {
      case Some(dtoAccountHierarchy) =>
        Right(AccountConvertors.toAccountHierarchy(dtoAccountHierarchy))
      case _ =>
        logger.info(s"Account Hierarchy not found for id $id")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Hierarchy", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound))
    }
  }

  def getAccountHierarchyByAccountId(accountId: Long): Future[Either[ErrorResponse, AccountHierarchy]] = {
    daoAccountV2.getAccountHierarchyByAccountId(accountId) map {
      case Some(dtoAccountHierarchy) =>
        Right(AccountConvertors.toAccountHierarchy(dtoAccountHierarchy))
      case _ =>
        logger.info(s"Account Hierarchy not found for accountId $accountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching account hierarchy by account", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError))
    }
  }

  def getSubAccounts(accountId: Long): Future[Either[ErrorResponse, Set[Long]]] = {
    daoAccountV2.getSubAccountIdsWithParent(accountId) map {
      case ahs if ahs.nonEmpty =>
        val accountIds = ahs.find(_.accountId.equals(accountId)) map {
          case dcOrSa if AccountTypes.isTypeDirect(dcOrSa.accountType) || dcOrSa.accountType.equals(AccountTypes.SUB_ACCOUNT.id)=>
            ahs.filterNot(_.accountId == accountId).map(_.accountId)
          case rsc if AccountTypes.isTypeChannelPartner(rsc.accountType) =>
            ahs.filter(i => (i.hierarchyPath.split("/").length == 2) && i.administer) flatMap { ah =>
                ahs.filter(_.hierarchyPath.startsWith(ah.hierarchyPath)).map(_.accountId)
            }
        } getOrElse Set.empty[Long]
        Right(accountIds)
      case _ =>
        logger.info(s"No sub accounts for $accountId")
        Right(Set.empty[Long])
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching sub accounts for $accountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.SubAccountFetchError))
    }
  }

  @deprecated("Should not allow to update administer flag")
  def updateAccountHierarchy(accountHierarchyInput: AccountHierarchyInput): Future[Either[ErrorResponse, Int]] = {
    val dtoAccountHierarchy = AccountConvertors.toDtoAccountHierarchy(accountHierarchyInput)
    daoAccountV2.updateAccountHierarchy(dtoAccountHierarchy).flatMap {
      case accountHierarchyRes: Int if accountHierarchyRes > 0 && dtoAccountHierarchy.hierarchyStatus == Status.ACTIVE.id =>
        daoAccountV2.activateAccountAssociationHistory(dtoAccountHierarchy.id).map {
          case res: Int if res > 0 => Right(1)
          case _ =>
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountAssociationHistory))
        }
      case accountHierarchyRes: Int if accountHierarchyRes > 0 && dtoAccountHierarchy.hierarchyStatus == Status.INACTIVE.id =>
        daoAccountV2.deActivateAccountAssociationHistory(dtoAccountHierarchy.id,clock).map {
          case res: Int if res > 0 => Right(1)
          case _ =>
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountAssociationHistory))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while update account hierarchy", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    }
  }

  def updateHierarchyStatus(accountId: Long, hierarchyStatus: Int): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.updateAccountHierarchyStatus(accountId, hierarchyStatus, clock) map {
      case n if n>0 => Right(true)
      case _ =>
        logger.info(s"Error occurred while updating the HierarchyStatus")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the HierarchyStatus", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    }
  }

  def updatePrimaryAdminCount(accountId: Long, noOfPrimaryAdmins: Int): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.updatePrimaryAdminCount(accountId, noOfPrimaryAdmins) map {
      case n if n>0 => Right(true)
      case _ =>
        logger.info(s"Error occurred while updating the Primary Admins Count")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the Primary Admins Count", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    }
  }

  def updateAdministerFlag(accountId: Long, administerFlag: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.updateAdministerFlag(accountId, administerFlag) map {
      case n if n>0 => Right(true)
      case _ =>
        logger.info(s"Error occurred while updating the Administer flag")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the Administer flag", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy))
    }
  }

  def insertAccountHierarchy(accountHierarchyInput: AccountHierarchyInput): Future[Either[ErrorResponse, Int]] = {
    val dtoAccountHierarchy = AccountConvertors.toDtoAccountHierarchy(accountHierarchyInput)
    daoAccountV2.insertAccountHierarchy(dtoAccountHierarchy).flatMap {
      case dtoAccountHierarchy: DtoAccountHierarchy =>
        val hierarchyId = Right(AccountConvertors.toAccountHierarchy(dtoAccountHierarchy)).right.get.id
        val dtoAccountAssociationHistory = DtoAccountAssociationHistory(0, hierarchyId, new DateTime(DateTimeZone.UTC), None)
        daoAccountV2.saveAccountAssociationHistory(dtoAccountAssociationHistory).map {
          case res: DtoAccountAssociationHistory =>
            Right(1)
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertAccountAssociationHistory))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertAccountHierarchy)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while inserting account hierarchy", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertAccountHierarchy))
    }
  }

  def validateAccountAccess(accountId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2AccountRequestExtd(accountId, Some(Creator(0L, creatorAccountId))) flatMap { isV2Provisioned =>
      if(isV2Provisioned) {
        (for {
          _ <- v2Validator.validateAccountAccess(accountId, creatorAccountId)
        } yield true).map(Right(_))
      } else {
        (daoAccount.getWithSubAccounts(creatorAccountId) map {
          case obj: Seq[DtoAccount] => obj.exists(_.accountId == accountId)
          case _ => false
        }).map(Right(_))
      }
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Invalid account access", ex)
        Right(false)
      case e: Exception =>
        logger.info("Error occurred while validating account access", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def validateAccountAccess(accountId: Long, creatorAccountId: Long, accountPermissions: Set[Int]): Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2AccountRequestExtd(accountId, Some(Creator(0L, creatorAccountId))) flatMap { isV2Provisioned =>
      if(isV2Provisioned) {
        (for {
          _ <- v2Validator.validateAccountAccess(accountId, creatorAccountId)
          hasPermission <- v2Validator.validateAccountPermissions(accountId, accountPermissions)
        } yield hasPermission).map(Right(_))
      } else {
        (daoAccount.getWithSubAccounts(creatorAccountId) flatMap {
          case obj: Seq[DtoAccount] if obj.exists(_.accountId == accountId) =>
            v2Validator.validateAccountPermissions(creatorAccountId, accountPermissions)
          case _ => Future(false)
        }).map(Right(_))
      }
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Invalid account access", ex)
        Right(false)
      case e: Exception =>
        logger.info("Error occurred while validating account access", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getRootParent(accountId: Long): Future[Long] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) map {
          //        in v2 account management, the account id's hierarchy are stored as 1/2/3, so here if we want root parent of 2 or 3, by below logic we will get 1
          case Some(ah) =>
            val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
            if (accountIds.nonEmpty) accountIds.head else accountId
          case None => accountId
        }
      case _ =>
        daoAccount.getParentAccountId(accountId) map { a =>
          a.flatten match {
            case Some(x) => x
            case _ => accountId
          }
        }
    }
  }

  def getRootParentAccountType(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    v2Validator.getRootParentAccountType(accountId) flatMap {
      case Some(accountType) => Future.successful(Right(accountType))
      case None => daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
        case Some(ah) => Future.successful(Right(ah.accountType))
        case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)))
      }
    }
  }

  def getCompanyId(accountId: Long): Future[Long] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap{
          //        in v2 account management, the account id's hierarchy are stored as 1/2/3, so here if we want root parent of 2 or 3, by below logic we will get 1
          case Some(ah) =>
            val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
            val tmpRootParent = if (accountIds.nonEmpty) accountIds.head else accountId
            val rootParent = daoAccountV2.getAccountHierarchyByAccountId(tmpRootParent) map{
              case Some(tempHierarchy) =>
                if(tempHierarchy.accountType==AccountTypes.RESELLER.id){
                  //go one step above
                  if(accountIds.length>1){
                    accountIds(1)
                  }else{
                    accountId
                  }
                }else{
                  tmpRootParent
                }
              case _ => accountId
            }
            rootParent
          case _ => Future.successful(accountId)
        }
      case _ =>
        daoAccount.getParentAccountId(accountId) map { a =>
          a.flatten match {
            case Some(x) => x
            case _ => accountId
          }
        }
    }
  }

  def isInternal(accountId: Long): Future[Option[Boolean]] = daoAccount.isInternalAccount(accountId)

}
