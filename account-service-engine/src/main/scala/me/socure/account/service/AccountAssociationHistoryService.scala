package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountAssociationHistory
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.account.DtoAccountAssociationHistory
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountAssociationHistoryService (daoAccountV2: DaoAccountV2)(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getAccountAssociationHistory(id: Long): Future[Either[ErrorResponse, AccountAssociationHistory]] = {
    daoAccountV2.getAccountAssociationHistory(id) map {
      case Some(dtoAccountAssociationHistory) =>
        Right(AccountConvertors.toAccountAssociationHistory(dtoAccountAssociationHistory))
      case _ =>
        logger.info(s"Account Association History not found for id $id")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAssociationHistoryNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Association History", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAssociationHistoryNotFound))
    }
  }

  def getAssociationHistoryForAccount(accountId: Long): Future[Either[ErrorResponse, DtoAccountAssociationHistory]] = {
    daoAccountV2.getAssociationHistoryForAccount(accountId) map{
      case aah if aah.nonEmpty => Right(aah.head)
      case _ =>
        logger.info(s"Account Association History not found for account $accountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAssociationHistoryNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Association History for account", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountAssociationHistoryNotFound))
    }
  }
}
