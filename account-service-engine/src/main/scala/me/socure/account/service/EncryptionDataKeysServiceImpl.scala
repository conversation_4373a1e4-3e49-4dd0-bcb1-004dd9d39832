package me.socure.account.service

import java.nio.charset.StandardCharsets
import java.util.Base64

import me.socure.crypto.decryptor.key.StaticKeyBasedDecrypter
import me.socure.crypto.encryptor.key.StaticKeyBasedEncryptor
import me.socure.crypto.key.common.DataWithEncryptionContext

import scala.concurrent.{ExecutionContext, Future}


class EncryptionDataKeysServiceImpl(
                                     keyBasedEncryptor: StaticKeyBasedEncryptor,
                                     keyBasedDecryptor: StaticKeyBasedDecrypter
                                   )(implicit ec: ExecutionContext) extends EncryptionDataKeysService {

  override def encryptMessage(message: String): Future[String] = {
    keyBasedEncryptor.encrypt(
      input = DataWithEncryptionContext(
        data = message.getBytes(StandardCharsets.UTF_8),
        encryptionContext = Map.empty
      )
    ).map(Base64.getEncoder.encodeToString)
  }

  override def decryptMessage(encryptedMessage: String): Future[String] = {
    keyBasedDecryptor.decrypt(
      input = DataWithEncryptionContext(
        data = Base64.getDecoder.decode(encryptedMessage),
        encryptionContext = Map.empty
      )
    ).map(new String(_, StandardCharsets.UTF_8))
  }
}
