package me.socure.account.service

import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, Metadata, SubscriptionChannelRegistryForAccount, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails, WebhookSecretSourceDetails}
import org.json4s.JValue

import scala.concurrent.Future

trait SubscriptionChannelRegistryService {
  def getSubscriptionChannelRegistry(id: Long, creator: Creator): Future[Either[ErrorResponse, DtoSubscriptionChannelRegistry]]

  def createSubscriptionChannelRegistry(dtoSubscriptionChannelRegistryJson: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]

  def createSubscriptionChannelRegistryV2(dtoSubscriptionChannelRegistryJson: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]

  def updateSubscriptionChannelRegistry(id: Long, parsedBody: JValue): Future[Either[ErrorResponse, Boolean]]

  def updateSubscriptionChannelRegistryV2(id: Long, parsedBody: JValue): Future[Either[ErrorResponse, Boolean]]

  def updateSubscriptionChannelState(id: Long, actionName: String, creator: Creator): Future[Either[ErrorResponse, Boolean]]

  def updateSubscriptionChannelStateV2(id: Long, actionName: String, creator: Creator): Future[Either[ErrorResponse, Boolean]]

  def deleteSubscriptionChannelRegistry(id: Long, creator: Creator): Future[Either[ErrorResponse, Boolean]]

  def getSubscriptionChannelRegistries(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]

  def getSubscriptionChannelRegistries(environmentId: Long, subscriptionTypeId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]

  def getSubscriptionChannelRegistriesV2(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]

  def getSubscriptionChannelRegistryWithAccount(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int]): Future[Either[ErrorResponse, SubscriptionChannelRegistryWithAccount]]

  def getSubscriptionTypes(): Future[List[Metadata]]

  def getChannelTypes(): Future[List[Metadata]]

  def getCommunicationModes(): Future[List[Metadata]]

  def getActions(): Future[List[Metadata]]

  def hasActiveChannels(accountId: Long, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]]

  def hasActiveChannelsV2(creator: Creator, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]]

  def getSubscriptionChannelRegistriesForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[SubscriptionChannelRegistryForAccount]]]

  def updateSecretKeyForWatchlistWebhook(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String, featureTypeId: Option[Int]): Future[Either[ErrorResponse, Int]]

  def getSecretKeyRotationDetails(): Future[Either[ErrorResponse, Seq[WebhookSecretKeyRotationDetails]]]

  def updateSecretKeyRotation(updateWebhookSecretKeyRotation: Seq[UpdateWebhookSecretKeyRotation]): Future[Either[ErrorResponse, Boolean]]
}
