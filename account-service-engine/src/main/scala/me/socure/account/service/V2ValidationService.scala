package me.socure.account.service

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.EnvironmentTypes
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class V2ValidationService(accountHierarchyService: AccountHierarchyService, v2Validator: V2Validator, daoBusinessUser: DaoBusinessUser, daoAccountV2: DaoAccountV2)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  def isPermissionAvailable(accountId: Long, permissions: Set[String], creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    accountHierarchyService.validateAccountAccess(accountId, creator.accountId) flatMap {
      case Right(accessible) if accessible =>
        v2Validator.isValidV2AccountRequest(accountId, Some(creator)) flatMap {
          case isV2Provisioned if isV2Provisioned =>
            val res = for {
              _ <- v2Validator.validatePermissions(accountId, creator.userId, permissions.map(_.toInt), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
            } yield Right(true)
            res recover {
              case ex: ErrorResponseException =>
                logger.info(s"Permission(s) ${permissions.mkString(",")} not available", ex)
                Left(ex.errorResponse)
              case e: Exception =>
                logger.info(s"Error occurred while validating permissions", e)
                Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
            }
          case _ =>
            daoAccountV2.isV1PrimaryUser(creator.userId) flatMap {
              case Some(isV1PrimaryUser) =>
                val requiredV1Permissions = AccountConvertors.toDashboardUserRoles(permissions.map(_.toInt))
                logger.info(s"Required Permissions: ${requiredV1Permissions} \n creator.userId: ${creator.userId}")
                daoBusinessUser.getEnvironmentUserRolesByUserId(creator.userId) map { assignedRoles =>
                  val isPermissionAvailable = isV1PrimaryUser || requiredV1Permissions.forall(assignedRoles.map(_.role).contains)
                  Right(isPermissionAvailable)
                }
              case _ =>
                throw new Exception(s"Invalid user id - ${creator.userId}")
            }
        }
      case _ =>
        logger.info(s"Account Id - $accountId is not accessible by ${creator.accountId}")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
  }


  def isValidEnvironmentPermission(envId: Long, permissions: Set[String], creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2EnvironmentRequest(envId,  Some(creator), permissions.map(_.toInt)) flatMap {
      case true => Future.successful(Right(true))
      case _ => Future.successful(Right(false))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not access environment", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not update environment", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def isValidEnvironmentPermissionByEnvName(envName: String, accountId: Long, permissions: Set[String], creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.getEnvironmentId(accountId, envName) flatMap {
      case Some(envId) => isValidEnvironmentPermission(envId, permissions, creator)
      case _ =>  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
  }

  def isValidActiveUserAccountAssociation(accountId: Long, userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.isValidActiveUser(accountId, userId) flatMap {
      case true => Future.successful(Right(true))
      case _ => Future.successful(Right(false))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not access environment", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not update environment", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }
}
