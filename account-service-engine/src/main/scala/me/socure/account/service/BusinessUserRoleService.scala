package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.watchlist.ManageCAAccountsCacheKeyProvider
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.constants.attributes.AccountAttributeName.AccountAttributeName
import me.socure.constants.attributes.AccountAttributeValue.AccountAttributeValue
import me.socure.constants.attributes.{AccountAttributeName, AccountAttributeValue}
import me.socure.constants.{AccountTypes, Resources}
import me.socure.convertors.AccountConvertors
import me.socure.model.account.{BusinessUserRolesLess, BusinessUserRolesWithPermissions}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoComplyWatchlistPreferences}
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import org.slf4j.LoggerFactory
import scalacache.ScalaCache
import slick.driver.JdbcProfile

import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal

/**
  * Created by alexandre on 5/30/16.
  */
class BusinessUserRoleService(
                               val dbProxyWithMetrics : DBProxyWithMetrics,
                               val profile: JdbcProfile,
                               daoBusinessUser: DaoBusinessUser,
                               daoAccountV2: DaoAccountV2,
                               accountInfoCacheInvalidator: AccountInfoCacheInvalidator,
                               subscriptionService: SubscriptionService,
                               daoAccount: DaoAccount,
                               daoComplyWatchlistPreferences: DaoComplyWatchlistPreferences,
                               v2Validator: V2Validator,
                               scalaCache: ScalaCache[_]
                             )(implicit ec: ExecutionContext)
  extends DaoTblAccount
    with DaoTblAccountPermission
    with DaoTblAccountAttribute
    with DaoTblEnvironment
    with DaoTblApiKey
    with DaoTblCAWatchlistPreferences {

  private val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)
  val cawatchlistApi = "/settings/preferences/ca/watchlist/3.0"
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"
  private val ModulesFeatureFlagsForSubAccounts = Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.AccountIntelligence.id) ++
    BusinessUserRoles.getRolesByResource(Resources.FEATURE_FLAGS).map(_.id)

  @deprecated private val DocumentVerificationDecisionRoleIds: Set[Int] = Set(
    BusinessUserRoles.DOC_VER_STRICT.id,
    BusinessUserRoles.DOC_VER_LENIENT.id,
    BusinessUserRoles.DOC_VER_MODERATE.id,
    BusinessUserRoles.DOC_VER_CUS_01.id)

  private val DocumentVerificationV2Permissions: Set[Int] = Set(
    BusinessUserRoles.DV_CALL_CENTER.id,
    BusinessUserRoles.DV_CLEARING_HOUSE.id,
    BusinessUserRoles.DV_DECISION_ENGINE.id,
    BusinessUserRoles.DV_IN_PERSON.id,
    BusinessUserRoles.DV_MOBILE_SDK_ONBOARDING.id,
    BusinessUserRoles.DV_WEB_ONBOARDING.id,
    BusinessUserRoles.USOnlyUncontrolledCapture.id,
    BusinessUserRoles.USOnlyControlledCapture.id
  )
  private val WlmBillingCadencePermissions: Set[Int] = Set(
    BusinessUserRoles.MonthlyCadence.id,
    BusinessUserRoles.QuarterlyCadence.id,
    BusinessUserRoles.AnnualCadence.id
  )

  private val eCBSVPermissions: Set[Int] = Set(
    BusinessUserRoles.ECBSV.id,
    BusinessUserRoles.ECBSV_Production.id,
    BusinessUserRoles.ECBSV_Certification.id,
    BusinessUserRoles.ECBSV_Sandbox.id)

  private val MLAPermissions: Set[Int] = Set(
    BusinessUserRoles.MLA.id,
    BusinessUserRoles.MLA_Production.id,
    BusinessUserRoles.MLA_Certification.id,
    BusinessUserRoles.MLA_Sandbox.id)

  private val PrefillPermissions: Set[Int] = Set(
    BusinessUserRoles.PrefillModule.id,
    BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id,
    BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id,
    BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id,
    BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id
  )

  private val KYCPermissions: Set[Int] = Set(
    BusinessUserRoles.KYC.id,
    BusinessUserRoles.KYCPLUS.id,
    BusinessUserRoles.BestMatchEntityViaDashboard.id
  )

  @deprecated private val DocumentVerificationV2Environments: Set[Int] = Set(
    BusinessUserRoles.AUTHENTIC_ID_V2_CERTIFICATION.id,
    BusinessUserRoles.AUTHENTIC_ID_V2_PRODUCTION.id
  )

  import profile.api._

  //TODO: Note:- Either Permission or Attribute can be added to only one account at a time.

  def isAccountExist(accountId : Long): Future[Boolean] = {
    val query = TblAccounts.filter(_.id === accountId).exists.result
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Select, "isAccountExist")
  }

  def isInternal(accountId : Long): Future[Boolean] = {
    val query = TblAccounts.filter(a => a.id === accountId && a.isInternal).exists.result
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Select, "isInternal")
  }

  //Account Attribute
  def upsertAttributeToAccount(accountId : Long, name : AccountAttributeName, value : AccountAttributeValue) : Future[Either[ErrorResponse, Int]] = {
    isAccountExist(accountId) flatMap {
      case true =>
        updateAccountAttribute(accountId, name.toString, value.id.toString) flatMap {
          case rows if rows > 0  => Future.successful(Right(rows))
          case _ => addAttribute(accountId, name.toString, value.id.toString) map {
            case rows if rows > 0  => Right(rows)
            case rows =>
              logger.info(s"Unable to add/update account attribute $name=$value for account=$accountId. Expected 1 affected row, but found $rows")
              Left(ErrorResponseFactory.get(AccountAttributeAddFailed))
          }
        }
      case false => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def removeAttributeFromAccount(accountId : Long, name : AccountAttributeName) : Future[Either[ErrorResponse, Int]] = {
    removeAttribute(accountId, name.toString) map {
      case rowAffected if rowAffected > 0 => Right(1)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def addAttribute(accountId : Long, name : String, value : String) = {
    val query = TblAccountAttribute += DtoAccountAttribute(accountId, name, value)
    dbProxyWithMetrics.run(query, DBTables.TblAccountAttribute, DBActions.Insert, "addAttribute")
  }

  private def updateAccountAttribute(accountId : Long, name : String, value : String) = {
    val query = TblAccountAttribute.filter(a => a.accountId === accountId && a.name === name).map(_.value).update(value)
    dbProxyWithMetrics.run(query, DBTables.TblAccountAttribute, DBActions.Update, "updateAccountAttribute")
  }

  private def removeAttribute(accountId : Long, name : String) = {
    val query = TblAccountAttribute.filter(a => a.accountId === accountId && a.name === name).delete
    dbProxyWithMetrics.run(query, DBTables.TblAccountAttribute, DBActions.Delete, "removeAttribute")
  }

  def fetchAccountAttributes(accountId : Long): Future[Seq[DtoAccountAttribute]] = {
    val query = TblAccountAttribute.filter(_.accountId === accountId).result
    dbProxyWithMetrics.run(query, DBTables.TblAccountAttribute, DBActions.Select, "fetchAccountAttributes")
  }

  def addDocVerificationCallCenter(accountId: Long): Future[Either[ErrorResponse, Int]] = {

    fetchAccountPermissions(accountId) flatMap { permissions =>

      if (permissions.contains(BusinessUserRoles.DOCUMENT_VERIFICATION.id)) {
        logger.info(s"Document Verification and Decision Logic already applied to this account $accountId")
        addPermission(accountId, BusinessUserRoles.DOC_VER_CALL_CENTER.id).map(_ => Right(1))
      } else {
        logger.info(s"DOC_VER_CALL_CENTER is not added for account $accountId due to parent permission was missing")
        Future.successful(Left(ErrorResponseFactory.get(ParentPermissionMissing)))
      }
    }
  }

  def addWhiteListDashboardPermission(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
    removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(addPermission(accountId, BusinessUserRoles.WHITELIST_DASHBOARD.id)) map {
      case res if res > 0 =>
        Right(1)
      case _ =>
        logger.info("Could not add permission")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case e: Exception =>
        logger.info("Could not add permission", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def removeWhiteListDashboardPermission(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
    removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(removePermissionV2(accountId, Set(BusinessUserRoles.WHITELIST_DASHBOARD.id))) map {
      case Right(res) =>  Right(res)
      case Left(errRes) =>
        logger.info(s"Error occurred while remove permission [${errRes.message}]")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case e: Exception =>
        logger.info("Error occurred while remove permission", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  //Account Permission
  def addPermissionToAccount(accountId : Long, permission : Int, issuedBy: Option[String] = None) : Future[Either[ErrorResponse, Int]] = {
    //TODO: Pattern match should be removed when super-admin adopts new account-service changes
    if(BusinessUserRoles.isKnown(permission)) {
      isValid(accountId, permission) flatMap {
        case true => validateAddPermission(accountId, permission, issuedBy)
        case false =>
          logger.info(s"Unable to add permission to the account: $accountId, permission: $permission")
          Future.successful(Left(ErrorResponseFactory.get(ParentPermissionMissing)))
      } recover {
        case e: ErrorResponseException => Left(e.errorResponse)
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(UnknownRole)))
    }
  }

  private def addPrefillPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    def addValidatedPermission(check: Int, add: Set[Int]): Future[Either[ErrorResponse, Int]] = {
      fetchAccountPermissions(accountId) flatMap { permissions =>
        if (permissions.contains(check)) {
          addPermissionsIgnoreExisting(accountId = accountId,
            permissions = add).map(_ => Right(1))
        }else{
          Future.successful(Left(ErrorResponseFactory.get(PrefillModuleNotEnabled)))
        }
      }
    }
    permission match {
      case BusinessUserRoles.PrefillModule.id => addPermission(accountId, permission).map(_ => Right(1))
      case BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id =>
        addValidatedPermission(BusinessUserRoles.PrefillModule.id, Set(permission))
      case perm if perm == BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id
                || perm == BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id
                || perm == BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id =>
        addValidatedPermission(BusinessUserRoles.PrefillModule.id, Set(BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id, permission))
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(UnknownRole)))
    }
  }

  private def removePrefillPermission(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case BusinessUserRoles.PrefillModule.id => removePermissions(accountId, PrefillPermissions).map(_ => Right(1))
      case BusinessUserRoles.PREFILL_ENHANCED_REPORTING.id =>
        removePermissions(accountId, Set(permission,
          BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id,
          BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id,
          BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id)).map(_ => Right(1))
      case perm if perm == BusinessUserRoles.PREFILL_ENHANCED_DOB_REPORTING.id
        || perm == BusinessUserRoles.PREFILL_ENHANCED_NAME_REPORTING.id
        || perm == BusinessUserRoles.PREFILL_ENHANCED_ADDRESS_REPORTING.id =>
        removePermission(accountId, permission).map(_ => Right(1))
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(UnknownRole)))
    }
  }

  private def togglePermissions(accountId: Long, add: Set[Int], remove: Set[Int]): Future[Either[ErrorResponse, Int]] = {
      removePermissions(accountId, remove) flatMap {
        case rowAffected if rowAffected >= 0 =>
          addPermissionsIgnoreExisting(accountId = accountId, permissions = add).map(_ => Right(1))
        case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      }
  }

  private def addeCBSVPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
        permission match {
          case BusinessUserRoles.ECBSV.id =>
            togglePermissions(accountId,
              Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id),
              Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id))
          case BusinessUserRoles.ECBSV_Sandbox.id =>
            togglePermissions(accountId,
              Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Sandbox.id),
              Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id))
          case BusinessUserRoles.ECBSV_Certification.id =>
            togglePermissions(accountId,
              Set(BusinessUserRoles.ECBSV.id, BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Sandbox.id),
              Set(BusinessUserRoles.ECBSV_Production.id))
          case BusinessUserRoles.ECBSV_Production.id =>
            addPermissionsIgnoreExisting(accountId = accountId,
              permissions = Set(BusinessUserRoles.ECBSV.id,
                BusinessUserRoles.ECBSV_Production.id,
                BusinessUserRoles.ECBSV_Certification.id,
                BusinessUserRoles.ECBSV_Sandbox.id)).map(_ => Right(1))
          case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
        }
  }

  private def removeeCBSVPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case id if id == BusinessUserRoles.ECBSV.id || id == BusinessUserRoles.ECBSV_Sandbox.id =>
        removePermissions(accountId, eCBSVPermissions).map(_ => Right(1))
      case BusinessUserRoles.ECBSV_Certification.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.ECBSV_Certification.id, BusinessUserRoles.ECBSV_Production.id)).map(_ => Right(1))
      case BusinessUserRoles.ECBSV_Production.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.ECBSV_Production.id)).map(_ => Right(1))
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def removeMLAPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case id if id == BusinessUserRoles.MLA.id || id == BusinessUserRoles.MLA_Sandbox.id =>
        removePermissions(accountId, MLAPermissions).map(_ => Right(1))
      case BusinessUserRoles.MLA_Certification.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id)).map(_ => Right(1))
      case BusinessUserRoles.MLA_Production.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.MLA_Production.id)).map(_ => Right(1))
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def removeWatchlistAutoMonitoringPermissions(accountId: Long, permission: Set[Int]): Future[Either[ErrorResponse, Int]] = {
    daoAccountV2.isAccountV2Provisioned(Set(accountId)).flatMap {
      isV2Provisioned => isV2Provisioned match {
          case true => daoAccountV2.getActiveSubAccountIds(accountId).flatMap {
            subAccountIds => daoAccountV2.removePermissionsForSubAccounts(subAccountIds, permission).flatMap {
                removedSubAccountPermissionCount => logger.info(s"removed subaccount permission count: ${removedSubAccountPermissionCount}")
                  removePermissions(accountId, permission).flatMap {
                    removedAccountPermissionCount => logger.info(s"removed account permission count: ${removedAccountPermissionCount}")
                      val accountIds = Set(accountId) ++ subAccountIds
                      daoAccountV2.fetchEnvironmentsByAccounts(accountIds).flatMap {
                        environmentIds => logger.info(s"environmentIds count: ${environmentIds.size}")
                          daoComplyWatchlistPreferences.getListWatchlistAllTiers(environmentIds).flatMap {
                            caWatchlistPreferences => val updatedPreferences = caWatchlistPreferences.map(caWatchlistPreference => caWatchlistPreference.copy(isWatchlistTransactionsAutoMonitored = Some(false)))
                              val cacheKeys = updatedPreferences map (ManageCAAccountsCacheKeyProvider provideForGetCAWatchList _.environmentId)
                              removeCacheKeysWrapped(cawatchlistApi, scalaCache, cacheKeys.toSet)(daoComplyWatchlistPreferences saveSeqWatchlist updatedPreferences) flatMap {
                                case rowsAffected if rowsAffected >= 0 => {
                                  logger.info(s"Rows updated for ca Watchlist Preferences: ${rowsAffected}")
                                  Future.successful(Right(rowsAffected))
                                }
                                case _ => logger.error(s"ca Watchlist Preferences not updated for account $accountId")
                                  metrics.increment("ca.watchlist.preferences.update.error", s"accountId:$accountId")
                                  Future.successful(Left(ErrorResponseFactory.get(UnknownError)))}}}}
              }.recover {
                case e: Exception =>
                  logger.info(s"Error occurred while removing permission $permission from account $accountId", e)
                  metrics.increment("ca.watchlist.preferences.update.error", s"accountId:$accountId")
                  Left(ErrorResponseFactory.get(UnknownError))
              }
          }
          case false => removePermissions(accountId, permission).flatMap {
            removedAccountPermissionCount => logger.info(s"removed account permission count: ${removedAccountPermissionCount}")
              daoAccountV2.fetchEnvironmentsByAccounts(Set(accountId)).flatMap {
                environmentIds => logger.info(s"environmentIds count: ${environmentIds.size}")
                  daoComplyWatchlistPreferences.getListWatchlistAllTiers(environmentIds).flatMap {
                    caWatchlistPreferences => val updatedPreferences = caWatchlistPreferences.map(caWatchlistPreference => caWatchlistPreference.copy(isWatchlistTransactionsAutoMonitored = Some(false)))
                      val cacheKeys = updatedPreferences map (ManageCAAccountsCacheKeyProvider provideForGetCAWatchList _.environmentId)
                      removeCacheKeysWrapped(cawatchlistApi, scalaCache, cacheKeys.toSet)(daoComplyWatchlistPreferences saveSeqWatchlist updatedPreferences) flatMap {
                        case rowsAffected if rowsAffected >= 0 => logger.info(s"Rows updated for ca Watchlist Preferences: ${rowsAffected}")
                          Future.successful(Right(rowsAffected))
                        case _ => logger.error(s"ca Watchlist Preferences not updated for account $accountId")
                          metrics.increment("ca.watchlist.preferences.update.error", s"accountId:$accountId")
                          Future.successful(Left(ErrorResponseFactory.get(UnknownError)))}}}
          }
        }
    }
  }

  private def addMLAPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case BusinessUserRoles.MLA.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id),
          Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id))
      case BusinessUserRoles.MLA_Sandbox.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Sandbox.id),
          Set(BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Production.id))
      case BusinessUserRoles.MLA_Certification.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.MLA.id, BusinessUserRoles.MLA_Certification.id, BusinessUserRoles.MLA_Sandbox.id),
          Set(BusinessUserRoles.MLA_Production.id))
      case BusinessUserRoles.MLA_Production.id =>
        addPermissionsIgnoreExisting(accountId = accountId,
          permissions = Set(BusinessUserRoles.MLA.id,
            BusinessUserRoles.MLA_Production.id,
            BusinessUserRoles.MLA_Certification.id,
            BusinessUserRoles.MLA_Sandbox.id)).map(_ => Right(1))
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def addKYCPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case BusinessUserRoles.KYC.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.KYC.id, BusinessUserRoles.BestMatchEntityViaDashboard.id),
          Set(BusinessUserRoles.KYCPLUS.id))
      case BusinessUserRoles.KYCPLUS.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.KYC.id, BusinessUserRoles.KYCPLUS.id),
          Set.empty)
      case BusinessUserRoles.BestMatchEntityViaDashboard.id =>
        togglePermissions(accountId,
          Set(BusinessUserRoles.KYC.id, BusinessUserRoles.BestMatchEntityViaDashboard.id),
          Set.empty)
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def removeKYCPermissions(accountId: Long, permission: Int): Future[Either[ErrorResponse, Int]] = {
    permission match {
      case id if id == BusinessUserRoles.KYC.id =>
        removePermissions(accountId, KYCPermissions).map(_ => Right(1))
      case BusinessUserRoles.KYCPLUS.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.KYCPLUS.id)).map(_ => Right(1))
      case BusinessUserRoles.BestMatchEntityViaDashboard.id =>
        removePermissions(accountId,
          Set(BusinessUserRoles.BestMatchEntityViaDashboard.id)).map(_ => Right(1))
      case _ => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  private def validateAddPermission(accountId : Long, permission : Int, issuedBy: Option[String] = None): Future[Either[ErrorResponse, Int]] = {
      val addPermissionResult = isAttributePermission(permission) match {
        case true =>
          val attributes = convertAttribute(permission)
          upsertAttributeToAccount(accountId, attributes._1, attributes._2)
        case false =>
          isAccountExist(accountId) flatMap {
            case true =>
              doesAccountHavePermission(accountId, permission) flatMap {
                case Some(rowAffected) => Future.successful(Right(0)) //No action required, when permission already exist
                case _ =>
                  permission match {
                    case BusinessUserRoles.CAPTURE_APP_INTERFACE_V2.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.CAPTURE_APP_EVALUATION_CERTIFICATION.id)).map(_ => Right(1))
                    case BusinessUserRoles.EnableDQL6.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.DQL6Certification.id)).map(_ => Right(1))
                    case BusinessUserRoles.EnableBB.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.BBCertification.id)).map(_ => Right(1))
                    case id if PrefillPermissions.contains(id) =>
                      addPrefillPermissions(accountId, permission)
                    case id if eCBSVPermissions.contains(id) =>
                      addeCBSVPermissions(accountId, permission)
                    case id if MLAPermissions.contains(id) =>
                      addMLAPermissions(accountId, permission)
                    case id if KYCPermissions.contains(id) =>
                      addKYCPermissions(accountId, permission)
                    case id if DocumentVerificationV2Permissions.contains(id) =>
                      isPermissionAvailable(accountId, BusinessUserRoles.DOCUMENT_VERIFICATION.id) flatMap {
                        case Right(true) => addPermission(accountId, permission).map(_ => Right(1))
                        case _ => Future.successful(Left(ErrorResponseFactory.get(DocumentVerificationNotProvisioned)))
                      }
                    case BusinessUserRoles.DOC_VER_CALL_CENTER.id => {
                      addDocVerificationCallCenter(accountId)
                    }
                    case BusinessUserRoles.DOC_VER_QR_CODE.id =>
                      isInternal(accountId) flatMap {
                        case true =>
                          updateRelatedMissingPermissions(accountId)
                        case false =>
                          Future.successful(Left(ErrorResponseFactory.get(NotAnInternalAccount)))
                      }
                    case BusinessUserRoles.UseDataLabellingVendor.id =>
                      isInternal(accountId) flatMap {
                        case true =>
                          addPermission(accountId, permission).map(_ => Right(1))
                        case false =>
                          Future.successful(Left(ErrorResponseFactory.get(NotAnInternalAccount)))
                      }
                    case BusinessUserRoles.MASK_PII.id =>
                      isFirstActivation(accountId) flatMap {
                        case true => addPermission(accountId, permission).map(_ => Right(1))
                        case false => Future.successful(Left(ErrorResponseFactory.get(NotFirstActivation)))
                      }
                    case BusinessUserRoles.BYOK.id =>
                      v2Validator.isParent(accountId) flatMap { // only allow for parent account
                        case true => addPermission(accountId, permission).map(_ => Right(1))
                        case false => Future.successful(Left(ErrorResponseFactory.get(InvalidParentAccount)))
                      }
                    case BusinessUserRoles.DashboardV3.id =>
                      daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap { // only allow for v2 account
                        case true => daoAccountV2.getActiveSubAccountIdsWithParent(accountId).flatMap(ids => {
                          val futures = ids map (id => addPermission(id, permission))
                            Future.sequence(futures.toList) map (_ => Right(1))
                          })
                        case false => Future.successful(Left(ErrorResponseFactory.get(InvalidAccountType)))
                      }
                    case BusinessUserRoles.WATCHLIST_3_0.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.WatchlistFPRReduction.id)).map(_ => Right(1))
                    case BusinessUserRoles.WatchlistFPRReduction.id =>
                      isPermissionAvailable(accountId, BusinessUserRoles.WATCHLIST_3_0.id) flatMap {
                        case Right(true) => addPermission(accountId, permission).map(_ => Right(1))
                        case _ =>
                          logger.info(s"WATCHLIST_3_0 is not enabled for this account $accountId. And so permission: $permission is not enabled")
                          Future.successful(Left(ErrorResponseFactory.get(OperationNotSupported)))
                      }
                    case BusinessUserRoles.WHITELIST_DASHBOARD.id =>
                      addWhiteListDashboardPermission(accountId)
                    case BusinessUserRoles.CaptureAppEnableRedirectOnTerminalError.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.RedirectOnTerminalErrorCertification.id)).map(_ => Right(1))
                    case BusinessUserRoles.CaptureAppEnableGSAHeaderAndFooter.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.GSAHeaderAndFooterCertification.id)).map(_ => Right(1))
                    case BusinessUserRoles.CaptureAppEnableV4UX.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.EnableV4UXCertification.id)).map(_ => Right(1))
                    case BusinessUserRoles.RealTimeImageAlertList.id =>
                      addPermissions(accountId = accountId, permissions = Set(permission, BusinessUserRoles.RealTimeImageAlertListCertification.id)).map(_ => Right(1))
                    case _ => addPermission(accountId, permission, issuedBy).map(_ => Right(1))
                  }
              }
            case false => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
          }
      }

      permission match {
        case BusinessUserRoles.SAML_2_0.id => addPermissionResult.flatMap {
          case Right(affectedRows) if affectedRows > 0 =>
            isPermissionAvailable(accountId = accountId, permission = BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
              case Right(true) =>
                logger.info(s"$accountId is ACCOUNT_MANAGEMENT_V2 enabled account. Users will not be deleted")
                Future.successful(Right(1))
              case _ =>
                daoBusinessUser.cleanUpUsersForAccount(accountId = accountId).map(_ => Right(affectedRows))
            }
          case other => Future.successful(other)
        }

        case a if DocumentVerificationV2Permissions.contains(a) => {
          addPermissionResult.flatMap {
            case Right(affectedRows) => {
              val rolesIdsToRemove = DocumentVerificationV2Permissions - a
              removePermissionV2(accountId = accountId, permissions = rolesIdsToRemove) map {
                case Right(res) => Right(res + affectedRows)
                case l@Left(_) => l
              }
            }
            case l@Left(_) => Future.successful(l)
          }
        }
        case a if WlmBillingCadencePermissions.contains(a) => {
          addPermissionResult.flatMap {
            case Right(affectedRows) => {
              val rolesIdsToRemove = WlmBillingCadencePermissions - a
              removePermissionV2(accountId = accountId, permissions = rolesIdsToRemove) map {
                case Right(res) => Right(res + affectedRows)
                case l@Left(_) => l
              }
            }
            case l@Left(_) => Future.successful(l)
          }
        }

        case BusinessUserRoles.HIGH_PERFORMANCE_SLA.id => addPermissionResult.flatMap {
          case Right(affectedRows) => removeAttribute(accountId, AccountAttributeValue.EXTREME_PERFORMANCE_SLA.toString).map(res => Right(res + affectedRows))
          case l@Left(_) => Future.successful(l)
        }
        case BusinessUserRoles.EXTREME_PERFORMANCE_SLA.id => addPermissionResult.flatMap {
          case Right(affectedRows) => removeAttribute(accountId, AccountAttributeValue.HIGH_PERFORMANCE_SLA.toString).map(res => Right(res + affectedRows))
          case l@Left(_) => Future.successful(l)
        }
        case BusinessUserRoles.BYOK.id => addPermissionResult flatMap {
          case Right(affectedRows) =>
            val subAccountIds = daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap {
              case true =>
                daoAccountV2.getActiveSubAccountIds(accountId)
              case false =>
                daoAccount.getSubAccountIds(accountId)
            }
            subAccountIds flatMap (ids => { // add BYOK permission to all sub-accounts (both V1 and V2)
              val futures = ids map (id => addPermission(id, permission))
              Future.sequence(futures.toList) map (result => Right(result.sum + affectedRows))
            })
          case l@Left(_) => Future.successful(l)
        }
        case other => addPermissionResult
      }
  }

  private def removePermissionV2(accountId: Long, permissions: Set[Int]): Future[Either[ErrorResponse, Int]] = {
    daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap { isV2Provisioned =>
      if(isV2Provisioned) {
        daoAccountV2.getAllSubAccountIds(accountId) flatMap { subAccountIds =>
          daoAccountV2.removePermissionsForSubAccounts(subAccountIds, permissions) flatMap { _ =>
            removePermissions(accountId, permissions) map {
              case rowAffected if rowAffected >= 0 => Right(1)
              case _ => Left(ErrorResponseFactory.get(UnknownError))
            }
          } recover {
            case e: Exception =>
              logger.info(s"Error occurred while removing permission $permissions from account $accountId", e)
              Left(ErrorResponseFactory.get(UnknownError))
          }
        }
      } else {
        removePermissions(accountId, permissions) map {
          case rowAffected if rowAffected > 0 => Right(1)
          case _ => Left(ErrorResponseFactory.get(UnknownError))
        }
      }
    }
  }

  def removePermissionFromAccount(accountId : Long, permission : Int) : Future[Either[ErrorResponse, Int]] = {
    //TODO: Pattern match should be removed when super-admin adopts new account-service changes
    isAttributePermission(permission) match {
      case true =>
        val attributes = convertAttribute(permission)
        removeAttributeFromAccount(accountId, attributes._1)
      case false =>
        permission match {
          case BusinessUserRoles.AUTHENTIC_ID_V2.id =>
            Future.successful(Left(ErrorResponseFactory.get(CannotRemoveAuthenticIDV2)))
          case BusinessUserRoles.AUTHENTIC_ID_V2_CERTIFICATION.id =>
            doesAccountHavePermission(accountId, BusinessUserRoles.AUTHENTIC_ID_V2_PRODUCTION.id) flatMap {
              case Some(_) => Future.successful(Left(ErrorResponseFactory.get(OperationNotSupported)))
              case _ =>
                removePermissionV2(accountId, Set(permission))
            }
          case BusinessUserRoles.AUTHENTIC_ID_V2_PRODUCTION.id =>
            Future.successful(Left(ErrorResponseFactory.get(cannotRemoveAuthenticIDV2Production)))
          case BusinessUserRoles.BYOK.id =>
            Future.successful(Left(ErrorResponseFactory.get(cannotRemoveBYOKProvision)))
          case BusinessUserRoles.WATCHLIST_3_0.id =>
            subscriptionService.unsubscribe(accountId, 1)
            removePermissionV2(accountId, Set(BusinessUserRoles.WATCHLIST_3_0.id, BusinessUserRoles.WatchlistFPRReduction.id))
          case BusinessUserRoles.CAPTURE_APP_INTERFACE_V2.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.CAPTURE_APP_EVALUATION_CERTIFICATION.id, BusinessUserRoles.CAPTURE_APP_EVALUATION_PRODUCTION.id, BusinessUserRoles.CAPTURE_APP_INTERFACE_V2.id))
          case BusinessUserRoles.EnableDQL6.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.DQL6Certification.id, BusinessUserRoles.DQL6Production.id, BusinessUserRoles.EnableDQL6.id))
          case BusinessUserRoles.EnableBB.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.BBCertification.id, BusinessUserRoles.BBProduction.id, BusinessUserRoles.EnableBB.id))
          case BusinessUserRoles.DOCUMENT_VERIFICATION.id =>
            subscriptionService.unsubscribe(accountId, 2)
            removePermissionV2(accountId, Set(permission))
          case id if PrefillPermissions.contains(id) =>
            removePrefillPermission(accountId, permission)
          case id if eCBSVPermissions.contains(id) =>
            removeeCBSVPermissions(accountId, permission)
          case id if MLAPermissions.contains(id) =>
             removeMLAPermissions(accountId, permission)
          case BusinessUserRoles.WatchlistAutoMonitoring.id =>
             removeWatchlistAutoMonitoringPermissions(accountId, Set(permission))
          case BusinessUserRoles.WHITELIST_DASHBOARD.id =>
            removeWhiteListDashboardPermission(accountId)
          case id if KYCPermissions.contains(id) =>
             removeKYCPermissions(accountId, permission)
          case BusinessUserRoles.CaptureAppEnableRedirectOnTerminalError.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.RedirectOnTerminalErrorCertification.id, BusinessUserRoles.RedirectOnTerminalErrorProduction.id, BusinessUserRoles.CaptureAppEnableRedirectOnTerminalError.id))
          case BusinessUserRoles.CaptureAppEnableGSAHeaderAndFooter.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.GSAHeaderAndFooterCertification.id, BusinessUserRoles.GSAHeaderAndFooterProduction.id, BusinessUserRoles.CaptureAppEnableGSAHeaderAndFooter.id))
          case BusinessUserRoles.CaptureAppEnableV4UX.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.EnableV4UXCertification.id, BusinessUserRoles.EnableV4UXProduction.id, BusinessUserRoles.CaptureAppEnableV4UX.id))
          case BusinessUserRoles.RealTimeImageAlertList.id =>
            removePermissionV2(accountId, Set(BusinessUserRoles.RealTimeImageAlertListCertification.id, BusinessUserRoles.RealTimeImageAlertListProduction.id, BusinessUserRoles.RealTimeImageAlertList.id)).map(_ => Right(1))
          case _ =>
            removePermissionV2(accountId, Set(permission))
        }
    }
  }

  def listRoles(): Future[Either[ErrorResponse, List[BusinessUserRolesLess]]] = {
    val roles = for(r <- BusinessUserRoles.values.filter(_.provisionEnabled)) yield BusinessUserRolesLess(r.id, r.label)
    roles match {
      case x if x.nonEmpty => Future.successful(Right(x.toList))
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
  }

  private def doesAccountHavePermission(accountId : Long, permission : Int) = {
    val query = TblAccountPermission.filter(p => p.accountId === accountId && p.permission === permission && p.enabled === true).result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblAccountPermission, DBActions.Select, "doesAccountHavePermission")
  }

  def isPermissionAvailable(accountId : Long, permission : Int): Future[Either[ErrorResponse, Boolean]] = {
    doesAccountHavePermission(accountId, permission) flatMap {
      case Some(a) => Future.successful(Right(true))
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
  }

  private def removePermission(accountId : Long, permission : Int) = {
    val query = TblAccountPermission.filter(p => p.accountId === accountId && p.permission === permission).delete
    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Delete, "removePermission"))
  }

  def removePermissions(accountId : Long, permissions : Set[Int]) = {
    val query = TblAccountPermission.filter(p => p.accountId === accountId && p.permission.inSet(permissions)).delete
    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Delete, "removePermissions"))
  }

  private def addPermission(accountId : Long, permission : Int, issuedBy: Option[String] = None) = {
    val query = TblAccountPermission.insertOrUpdate(DtoAccountPermission(accountId, permission, createdBy = issuedBy))
    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Upsert, "addPermission"))
  }

  private def addPermissions(accountId : Long, permissions : Set[Int]): Future[Option[Int]] = {
    val dtoAccountPermissions = permissions.map { a =>
      DtoAccountPermission(accountId, a)
    }
    val query = TblAccountPermission ++= dtoAccountPermissions
    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Insert, "addPermissions"))
  }

  def switchPermissions(accountId: Long, permissions:Set[Int], switch: Boolean): Future[Either[ErrorResponse, Int]] = {
    def addpermissions0() = {
      permissions.contains(BusinessUserRoles.WHITELIST_DASHBOARD.id) match {
        case true =>
          val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
          removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(addPermissionsIgnoreExisting(accountId, permissions)) map {
          case Some(res) if res > 0 => Right(res)
          case _ =>
            logger.info(s"Add Permissions($permissions) failed, Unknown Error")
            Left(ErrorResponseFactory.get(UnknownError))
        } recover {
          case e: Exception =>
            logger.info(s"Add Permissions($permissions) failed, Unknown Error")
            Left(ErrorResponseFactory.get(UnknownError))
        }
        case false => addPermissionsIgnoreExisting(accountId, permissions) map {
          case Some(n) if n > 0 => Right(n)
          case _ =>
            logger.info(s"Add Permissions($permissions) failed, Unknown Error")
            Left(ErrorResponseFactory.get(UnknownError))
        }
      }

    }
    def removePermissions0() = {
      permissions.contains(BusinessUserRoles.WHITELIST_DASHBOARD.id) match {
        case true => val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
          removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(removePermissions(accountId, permissions)) map {
            case res if res > 0 =>
              Right(res)
            case _ =>
              logger.info(s"Remove Permissions($permissions) failed, Unknown Error")
              Left(ErrorResponseFactory.get(UnknownError))
          } recover {
            case e: Exception =>
              logger.info(s"Remove Permissions($permissions) failed, Unknown Error")
              Left(ErrorResponseFactory.get(UnknownError))
          }
        case false => removePermissions(accountId, permissions) map {
          case n if n > 0 => Right(n)
          case _ =>
            logger.info(s"Remove Permissions($permissions) failed, Unknown Error")
            Left(ErrorResponseFactory.get(UnknownError))
        }
      }
    }
    isAccountExist(accountId) flatMap {
      case true =>
        if(BusinessUserRoles.isKnown(permissions)) {
          if(switch) {
            addpermissions0
          } else{
            removePermissions0
          }
        }else{
          logger.info(s"Add/Remove Permissions($permissions) failed, Invalid Permissions ${permissions.filterNot(BusinessUserRoles.values.map(_.id))}")
          Future.successful(Left(ErrorResponseFactory.get(UnknownRole)))
        }
      case false =>
        logger.info(s"Add/Remove Permissions($permissions) failed, Account($accountId) not found")
        Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def addPermissionsIgnoreExisting(accountId: Long, permissions: Set[Int]): Future[Option[Int]] = {
      val query = TblAccountPermission.filter(f => f.accountId === accountId && f.permission.inSet(permissions) && f.enabled === true).result.flatMap { exists =>
        val toInsert = permissions.filterNot(exists.map(_.permission).toSet)
        val dtoAccountPermissions = toInsert.map { a =>
          DtoAccountPermission(accountId, a)
        }
        TblAccountPermission ++= dtoAccountPermissions}
    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query.transactionally, DBTables.TblAccountPermission, DBActions.Insert, "addPermissionsIgnoreExisting"))
  }

  private def updateRelatedMissingPermissions(accountId: Long) = {
    fetchAccountPermissions(accountId) flatMap{
      case permissions => if (permissions.contains(BusinessUserRoles.DOCUMENT_VERIFICATION.id)) {
        logger.info(s"Document Verification and Decision Logic already applied to this account $accountId")
        addPermission(accountId, BusinessUserRoles.DOC_VER_QR_CODE.id).map(_ => Right(1))
      }else {
        addDVQRCodePermission(accountId).map(_ => Right(1))
      }
    }
  }

  def containsDecisionLogic[A](a:Set[A], b: Set[A]) = b.exists(a.contains(_))

  private def addDVQRCodePermission(accountId: Long): Future[Option[Int]] = {
    addPermissions(accountId,
      Seq(DtoAccountPermission(accountId, BusinessUserRoles.DOC_VER_QR_CODE.id),
        DtoAccountPermission(accountId, BusinessUserRoles.DOCUMENT_VERIFICATION.id),
        DtoAccountPermission(accountId, BusinessUserRoles.DOC_VER_LENIENT.id)))
  }

  private def addPermissions(accountId: Long, permissions: Seq[DtoAccountPermission]) = {
    val query = TblAccountPermission ++= permissions

    invalidateAccountCacheAfter(accountId = accountId)(dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Insert, "addPermissions"))
  }

  private def isFirstActivation(accountId : Long) = {
    val query = TblAccounts.filter(a => a.id === accountId && a.firstActivatedAt.isEmpty).exists
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "isFirstActivation")
  }

  def fetchAccountPermissions(accountId : Long) = {
    val query = TblAccountPermission.filter(ap => ap.accountId === accountId && ap.enabled === true).map(_.permission).result
    dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Select, "fetchAccountPermissions")
  }

  def getParentAccountPermissions(accountId : Long): Future[Either[ErrorResponse, Set[Int]]] = {
    daoAccount.getAccount(accountId) flatMap {
      case Some(account) =>
        for {
          rootAccountId <- v2Validator.getRootParentAccount(account.accountId) map {
            case Some(rp) => rp
            case None => account.parentId.getOrElse(accountId)
          }
          validPermissions <- {
            daoAccount.getAccountPermissions(rootAccountId) map { permissions =>
              BusinessUserRoles.values.map(_.id).intersect(permissions.map(_.permission).toSet)
            }
          }
        } yield Right(validPermissions)
      case _ =>
        logger.info(s"Account($accountId) not found")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    } recover {
      case _  =>
        logger.info(s"Unable to fetch permission for the Account($accountId)")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getAccountPermissions(accountId : Long): Future[Either[ErrorResponse, List[BusinessUserRolesWithPermissions]]] = {
    val permissions = for{
      accId <- daoAccountV2.isAccountV2Provisioned(Set(accountId)).flatMap {
        case true => Future(accountId)
        case false => daoAccount.getParentAccountId(accountId) map { a =>
          a.flatten match {
            case Some(x) => x
            case _ => accountId
          }
        }
      }
      ap <- fetchAccountPermissions(accId)
      aa <- accountAttributesToRoles(accId)
      roles <- daoAccountV2.isParentAccount(accountId) map {
        case true =>
          for (r <- BusinessUserRoles.values.filter(_.provisionEnabled)) yield BusinessUserRolesLess(r.id, r.label)
        case false =>
          for (r <- BusinessUserRoles.values.filter(businessRole => businessRole.provisionEnabled && !WlmBillingCadencePermissions.contains(businessRole.id)))
            yield BusinessUserRolesLess(r.id, r.label)
      }
      permissions0 <- Future(roles.map(l => BusinessUserRolesWithPermissions(l.id, l.label, ap.contains(l.id) || aa.contains(l.id))))
    } yield permissions0

    permissions map  {
      case x if x.nonEmpty => Right(x.toList)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  private def accountAttributesToRoles(accountId: Long): Future[Seq[Int]] = {
    val queryAccountAttributes = TblAccountAttribute.filter(_.accountId === accountId).map(_.value).result
    dbProxyWithMetrics.run(queryAccountAttributes, DBTables.TblAccountAttribute, DBActions.Select, "accountAttributesToRoles")
      .map(accountAttributes => accountAttributes.flatMap { a =>
        AccountConvertors.convertAttributeToRole(AccountAttributeValue(a.toInt))
      })
  }

  private def buildRoles(permissions:Seq[Int], roles: Set[BusinessUserRolesLess]): Set[BusinessUserRolesWithPermissions] = {
   roles map {
      l => BusinessUserRolesWithPermissions(l.id, l.label, permissions.contains(l.id))
    }
  }

  private def convertAttribute(role : Int) : Tuple2[AccountAttributeName, AccountAttributeValue] = {
    role match {
      case BusinessUserRoles.DEFAULT_SLA.id => (AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)
      case BusinessUserRoles.HIGH_PERFORMANCE_SLA.id => (AccountAttributeName.SLA, AccountAttributeValue.HIGH_PERFORMANCE_SLA)
      case BusinessUserRoles.EXTREME_PERFORMANCE_SLA.id => (AccountAttributeName.SLA, AccountAttributeValue.EXTREME_PERFORMANCE_SLA)
      case BusinessUserRoles.PRODUCTION.id => (AccountAttributeName.PRODUCTION, AccountAttributeValue.PRODUCTION)
    }
  }

  private def isAttributePermission(permission: Int) : Boolean = {
    permission match {
      case BusinessUserRoles.DEFAULT_SLA.id | BusinessUserRoles.HIGH_PERFORMANCE_SLA.id | BusinessUserRoles.EXTREME_PERFORMANCE_SLA.id | BusinessUserRoles.PRODUCTION.id => true
      case _ => false
    }
  }

  private def invalidateAccountCache(accountId: Long): Future[Unit] = {
    val accountsFilterBaseQuery = TblAccounts.filter(a => a.id === accountId || a.parentId === accountId)
    val allAccountsQuery = accountsFilterBaseQuery.map(_.id)
    val allEnvironmentsQuery = TblEnvironment.filter(_.accountId in allAccountsQuery).map(_.id)
    val privateApiKeysQuery = TblApiKey.filter(_.environmentId in allEnvironmentsQuery).map(_.apiKey)
    val publicApiKeysQuery = accountsFilterBaseQuery.map(_.publicApiKey)

    val resFuture = for {
      privateApiKeys <- dbProxyWithMetrics.run(privateApiKeysQuery.result, DBTables.TblApiKey, DBActions.Select, "invalidateAccountCache")
      publicApiKeys <- dbProxyWithMetrics.run(publicApiKeysQuery.result, DBTables.TblAccount, DBActions.Select, "invalidateAccountCache")
      res <- accountInfoCacheInvalidator.invalidate(privateApiKeys.toSet ++ publicApiKeys.toSet, accountId)
    } yield res

    resFuture
  }

  private def invalidateAccountCacheSafe(accountId: Long): Future[Unit] = {
    invalidateAccountCache(accountId = accountId).recover {
      case NonFatal(ex) =>
        //we don't want cache invalidation affect the actual process
        logger.info("Unable to invalidate account information cache, AccountId:", ex)
    }
  }

  private def invalidateAccountCacheAfter[T](accountId: Long)(action: => Future[T]): Future[T] = {
    action.flatMap { res =>
      invalidateAccountCacheSafe(accountId = accountId).map(_ => res)
    }
  }

  private def isValid(accountId: Long, permission: Int): Future[Boolean] = {
    if(ModulesFeatureFlagsForSubAccounts.contains(permission)){
      Future.successful(true)
    } else {
      daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap {
        case true =>
          for {
            ahOpt <- daoAccountV2.getAccountHierarchyByAccountId(accountId)
            ap <- ahOpt match {
              case Some(ah) =>
                if ((ah.accountType != AccountTypes.SUB_ACCOUNT.id) || (permission == BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id) && AccountTypes.isTypeChannelPartner(ah.accountType))
                  Future.successful(true)
                else {
                  for {
                    parent <- getParent(ah.hierarchyPath)
                    hasPermission <- daoAccountV2.isAccountPermissionProvisioned(parent.accountId, permission)
                  } yield hasPermission
                }
              case _ =>
                logger.info(s"Account not found: $accountId")
                throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
            }
          } yield ap
        case false => Future.successful(true)
      }
    }
  }

  def getParent(hierarchyPath: String): Future[DtoAccountHierarchy] = {
    val accountIds = hierarchyPath.split("/").map(_.toLong)
    if(accountIds.nonEmpty) {
      daoAccountV2.getAccountHierarchyByAccountId(accountIds.head) map {
        case Some(ah) => ah
        case None =>
          logger.info(s"Account Hierarchy not found for ${hierarchyPath}")
          throw ErrorResponseException(ErrorResponseFactory.get(AccountNotFound))
      }
    }else{
      logger.info(s"Invalid Account Hierarchy - ${hierarchyPath}")
      throw ErrorResponseException(ErrorResponseFactory.get(UnknownError))
    }
  }
}
