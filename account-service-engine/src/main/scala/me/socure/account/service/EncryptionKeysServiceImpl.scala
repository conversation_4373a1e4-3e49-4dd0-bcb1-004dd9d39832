package me.socure.account.service

import java.nio.ByteBuffer
import java.util.Base64
import com.amazonaws.arn.Arn
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.AWSKMSAsyncClientBuilder
import com.amazonaws.services.kms.model.{AWSKMSException, DecryptRequest, EncryptRequest, GenerateDataKeyRequest, GenerateDataKeyResult}
import me.socure.DaoAccount
import me.socure.account.service.EncryptionKeysService._
import me.socure.account.service.common.EncryptionKeysCacheKeyProvider
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CustomerKMSARNParsingError, CustomerKMSDecryptionError, CustomerKMSEncryptionError, CustomerKMSRegionNotSupported, InternalError, UnknownError}
import me.socure.account.util.CacheUtil._
import me.socure.common.clock.Clock
import me.socure.common.kms.KmsService
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.configuration.EncryptionKeysConfig
import me.socure.model.encryption._
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.{DaoEncryptionKeys, DaoEncryptionKeysImpl, DaoServiceEncryptionKeys, DaoServiceEncryptionKeysImpl}
import me.socure.storage.slick.tables.account.DtoEncryptionKey
import me.socure.storage.slick.tables.account.model.DtoServiceEncryptionKey
import me.socure.utils.DBProxyWithMetrics
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache
import slick.driver.JdbcProfile

import scala.collection.JavaConverters._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}


/**
  * Created by jamesanto on 4/11/17.
  */
class EncryptionKeysServiceImpl(
                                 daoEncryptionKeys: DaoEncryptionKeys,
                                 daoServiceEncryptionKeys: DaoServiceEncryptionKeys,
                                 kmsIdsConfig: KmsIdsConfig,
                                 dataKeyLen: DataKeyLen,
                                 serviceKMSDetails: Seq[ServiceKmsDetail],
                                 kmsServices: Map[Regions, KmsService],
                                 unknownAccountEncryptionKeysFetcher: UnknownAccountEncryptionKeysFetcher,
                                 daoAccount: DaoAccount,
                                 clock: Clock,
                                 scalaCache: ScalaCache[_]
                               )(implicit exe: ExecutionContext) extends EncryptionKeysService {

  import EncryptionKeysServiceImpl._

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  private val logger: Logger = LoggerFactory.getLogger(getClass)
  val activeKeysApiName = "/encryption_keys_v2/active_keys"

  private def generateDataKey(accountId: AccountId, region: Regions, kmsId: KmsId): Future[GenerateDataKeyResult] = {
    val encryptionContext = Map(
      EncryptionContextAccountId -> String.valueOf(accountId.value)
    ).asJava
    val generateDataKeyRequest: GenerateDataKeyRequest = new GenerateDataKeyRequest()
      .withKeyId(kmsId.value)
      .withNumberOfBytes(dataKeyLen.value)
      .withEncryptionContext(encryptionContext)
    kmsServices(region)
      .generateDataKey(generateDataKeyRequest)
  }

  private def decryptKey(accountId: AccountId, region: Regions, kmsId: KmsId, encryptedKey: EncryptedKey): Future[DecryptedKey] = {
    val encryptionContext = Map(
      EncryptionContextAccountId -> String.valueOf(accountId.value)
    ).asJava
    kmsServices(region)
      .decrypt(new DecryptRequest()
        .withKeyId(kmsId.value)
        .withCiphertextBlob(ByteBuffer.wrap(encryptedKey.value))
        .withEncryptionContext(encryptionContext))
        .map { r =>
          DecryptedKey(r.getPlaintext.array())
        }
  }

  private def generateEncryptedDataKey(accountId: AccountId, region: Regions, kmsId: KmsId, plainTextBuffer: ByteBuffer): Future[EncryptedKey] = {
    val encryptionContext = Map(
      EncryptionContextAccountId -> String.valueOf(accountId.value)
    ).asJava
    kmsServices(region)
      .encrypt(new EncryptRequest()
        .withKeyId(kmsId.value)
        .withPlaintext(plainTextBuffer)
        .withEncryptionContext(encryptionContext))
      .map(result => {
        val encryptedBytes = new Array[Byte](result.getCiphertextBlob.remaining())
        result.getCiphertextBlob.get(encryptedBytes)
        EncryptedKey(
          value = encryptedBytes)
      })
  }

  private def generateKeys(accountId: AccountId, kmsIdsConfigValue: Map[Regions, KmsId]): Option[Future[Map[Regions, (EncryptedKey, KmsId)]]] = {
    kmsIdsConfigValue
      .headOption
      .map { defaultKmsIdConfig: (Regions, KmsId) =>
        generateDataKey(accountId, defaultKmsIdConfig._1, defaultKmsIdConfig._2)
          .map(result => {
            val plainText = new Array[Byte](result.getPlaintext.remaining())
            result.getPlaintext.get(plainText)
            val encryptedText = new Array[Byte](result.getCiphertextBlob.remaining())
            result.getCiphertextBlob.get(encryptedText)
            GeneratedDataKeys(kmsId = defaultKmsIdConfig._2, plainText = plainText, encryptedText = encryptedText)
          }).flatMap { dataKey =>
          val futures = kmsIdsConfigValue
            .map {
              case (region0, kmsId0) if kmsId0.equals(dataKey.kmsId) =>
                Future.successful(region0 -> (EncryptedKey(
                  value = dataKey.encryptedText
                ), kmsId0))
              case (region, kmsId) =>
                generateEncryptedDataKey(accountId, region, kmsId, ByteBuffer.wrap(dataKey.plainText)).map{ encryptedKey =>
                  region -> (encryptedKey, kmsId)
                }
            }
          Future.sequence(futures).map(keys => keys.toMap)
        }
      }
  }

  private def getDataKey(dtoEncryptionKey: DtoEncryptionKey): Future[ByteBuffer] = {
      val region = getRegion(dtoEncryptionKey.arn)
      decryptKey(AccountId(dtoEncryptionKey.accountId), region, KmsId(dtoEncryptionKey.arn), EncryptedKey(dtoEncryptionKey.encryptionKey))
        .map { decryptedKey =>
          ByteBuffer.wrap(decryptedKey.value)
        }
  }

  private def getDataKeyT(dtoEncryptionKey: DtoEncryptionKey): Future[(Long, ByteBuffer)] = {
    val region = getRegion(dtoEncryptionKey.arn)
    decryptKey(AccountId(dtoEncryptionKey.accountId), region, KmsId(dtoEncryptionKey.arn), EncryptedKey(dtoEncryptionKey.encryptionKey))
      .map { decryptedKey =>
        (dtoEncryptionKey.accountId, ByteBuffer.wrap(decryptedKey.value))
      }
  }

  private def getDataKeyT(dtoEncryptionKey: DtoServiceEncryptionKey): Future[(Long, ByteBuffer)] = {
    val region = getRegion(dtoEncryptionKey.kmsArn)
    decryptKey(AccountId(dtoEncryptionKey.accountId), region, KmsId(dtoEncryptionKey.kmsArn), EncryptedKey(dtoEncryptionKey.encryptionKey))
      .map { decryptedKey =>
        (dtoEncryptionKey.accountId, ByteBuffer.wrap(decryptedKey.value))
      }
  }

  private def getRegion(arn: String): Regions = {
    val region = Regions.fromName(Arn.fromString(arn).getRegion)
    if(kmsServices.contains(region)) region else throw RegionNotSupportedException(arn)
  }

  private def getNewEncryptedCustomerKey(activeKeys: Seq[DtoEncryptionKey], region: Regions, kmsId: KmsId): Future[Set[(AccountId, (Regions, EncryptedKey, KmsId))]] = {
    for {
      datakeysT <- Future.sequence(activeKeys.map(getDataKeyT))
      encryptedKeys <- newEncryptedCustomerKeyGenerator(datakeysT, region, kmsId)
    } yield encryptedKeys
  }

  private def getNewServiceEncryptedCustomerKey(activeKeys: Seq[DtoServiceEncryptionKey], region: Regions, kmsId: KmsId): Future[Set[(AccountId, (Regions, EncryptedKey, KmsId))]] = {
    for {
      datakeysT <- Future.sequence(activeKeys.map(getDataKeyT))
      encryptedKeys <- newEncryptedCustomerKeyGenerator(datakeysT, region, kmsId)
    } yield encryptedKeys
  }

  private def newEncryptedCustomerKeyGenerator(datakeysT: Seq[(Long, ByteBuffer)], region: Regions, kmsId: KmsId): Future[Set[(AccountId, (Regions, EncryptedKey, KmsId))]] = {
    for {
      groupedByAccount <- Future.sequence(datakeysT.groupBy(_._1).map { dk =>
        Future.sequence(dk._2.toSet.map{t2:(Long, ByteBuffer) =>
          generateEncryptedDataKey(accountId = AccountId(dk._1), region, kmsId, t2._2) map { encrtptedKey =>
            AccountId(dk._1) -> (region, encrtptedKey, kmsId)
          }})
      })
    } yield groupedByAccount.foldLeft(Set.empty[(AccountId, (Regions, EncryptedKey, KmsId))])(_ ++ _)
  }

  private def generateCustomerServiceKeysForRegion(parentAccountId: AccountId, subAccountIds: Set[AccountId], region:Regions, kmsId: KmsId): Future[Seq[Boolean]] = {
    val accountIds = subAccountIds + parentAccountId
   daoServiceEncryptionKeys
      .hasEncryptionKeys(parentAccountId)
      .flatMap {
        case true =>
          val res = serviceKMSDetails.map(serviceKMSDetail => {
            val serviceName = serviceKMSDetail.serviceName.value
            daoServiceEncryptionKeys.getKeys(parentAccountId, serviceName, isActive = true) flatMap {
              case activeKeys if activeKeys.nonEmpty =>
                val response = if (activeKeys.exists(_.isInternalKms)) {
                  if(activeKeys.exists(_.isInternalKms == false)) {
                    val keys = activeKeys.map(k => (k.kmsArn, k.isInternalKms)).mkString(",\n")
                    logger.warn(s"Service Name: $serviceName; Both Internal and External Encryption keys found for the Parent Account($parentAccountId). Generate Keys failed, Kms provided: $kmsId. And the keys are \n $keys")
                    throw KeysAreNotMutuallyExclusiveException(arn = kmsId.value, parentAccount = parentAccountId)
                  }else{
                    logger.info(s"Service Name: $serviceName; No Customer specific EDK for region $region exists. Generating new keys using the Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                    logger.info(s"Service Name: $serviceName; Assuming customer provided key is not multi-region at this point in time")
                    val isMultiRegionKey = false
                    val res = for {
                      activeKeys <- daoServiceEncryptionKeys.getKeys(accountIds, serviceName, isActive = true)
                      newKeys <- getNewServiceEncryptedCustomerKey(activeKeys, region, kmsId)
                      encryptionKeys <- daoServiceEncryptionKeys.addCustomerKeys(toCustomerDtoServiceEncryptionKeys(newKeys, serviceKMSDetail.serviceName.value, isMultiRegionKey))
                    } yield encryptionKeys
                    res.recover {
                      case e: Throwable =>
                        logger.error(s"Unable to generate service encryption key for service name: ${serviceKMSDetail.serviceName.value}", e)
                        false
                    }
                    res
                  }
                } else {
                  if (activeKeys.exists(k => getRegion(k.kmsArn).equals(region))) {
                    logger.info(s"Customer specific EDK for region $region EXISTS. Regenerating keys for Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                    val isMultiRegionKey = false
                    for {
                      activeKeys <- daoServiceEncryptionKeys.getKeys(accountIds, serviceName, isActive = true).map(ek => ek.filter(k => getRegion(k.kmsArn).equals(region)))
                      newKeys <- getNewServiceEncryptedCustomerKey(activeKeys, region, kmsId)
                      encryptionKeys <- daoServiceEncryptionKeys.rotateServiceKey(toCustomerDtoServiceEncryptionKeys(newKeys, serviceName, isMultiRegionKey), activeKeys.map(_.kmsArn).toSet, serviceName, clock)
                    } yield encryptionKeys
                  } else {
                    val isMultiRegionKey = false
                    logger.info(s"Service Name: $serviceName; Customer specific EDK for region $region DOES NOT EXIST. Generating keys for Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                    for {
                      activeKeys <- daoServiceEncryptionKeys.getKeys(accountIds, serviceName, isActive = true)
                      newKeys <- getNewServiceEncryptedCustomerKey(activeKeys, region, kmsId)
                      encryptionKeys <- daoServiceEncryptionKeys.add(toCustomerDtoServiceEncryptionKeys(newKeys, serviceName, isMultiRegionKey))
                    } yield encryptionKeys
                  }
                }
                response.map(r => if(r.getOrElse(0)>0) {
                  logger.info(s"Service Name: $serviceName; Added Customer keys for account $parentAccountId and its Sub Accounts $subAccountIds")
                  true
                } else {
                  logger.warn(s"Service Name: $serviceName; Add Customer keys failed for account $parentAccountId and its Sub Accounts $subAccountIds")
                  false
                })
              case _ =>
                logger.warn(s"Service Name: $serviceName; No Active Encryption keys found for the Parent Account($parentAccountId). Generate Keys failed, Kms provided: $kmsId")
                throw NoEncryptionKeyFoundException(accountId = parentAccountId)
            }
          })
          Future.sequence(res)
      }
  }

  private def generateCustomerKeyForRegion(parentAccountId: AccountId, subAccountIds: Set[AccountId], region:Regions, kmsId: KmsId): Future[Boolean] = {
    val accountIds = subAccountIds + parentAccountId
    daoEncryptionKeys
      .hasEncryptionKeys(parentAccountId)
      .flatMap {
        case true =>
          daoEncryptionKeys.getActiveKeys(parentAccountId) flatMap {
            case activeKeys if activeKeys.nonEmpty =>
              val response = if (activeKeys.exists(_.isInternal)) {
                if(activeKeys.exists(_.isInternal == false)) {
                  val keys = activeKeys.map(k => (k.arn, k.isInternal)).mkString(",\n")
                  logger.warn(s"Both Internal and External Encryption keys found for the Parent Account($parentAccountId). Generate Keys failed, Kms provided: $kmsId. And the keys are \n $keys")
                  throw KeysAreNotMutuallyExclusiveException(arn = kmsId.value, parentAccount = parentAccountId)
                }else{
                  logger.info(s"No Customer specific EDK for region $region exists. Generating new keys using the Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                  for {
                    activeKeys <- daoEncryptionKeys.getKeys(accountIds, status = true)
                    newKeys <- getNewEncryptedCustomerKey(activeKeys, region, kmsId)
                    encryptionKeys <- daoEncryptionKeys.addCustomerKeys(toDtoEncryptionKeys(newKeys))
                  } yield encryptionKeys
                }
              } else {
                if (activeKeys.exists(k => getRegion(k.arn).equals(region))) {
                  logger.info(s"Customer specific EDK for region $region EXISTS. Regenerating keys for Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                  for {
                    activeKeys <- daoEncryptionKeys.getKeys(accountIds, status = true).map(ek => ek.filter(k => getRegion(k.arn).equals(region)))
                    newKeys <- getNewEncryptedCustomerKey(activeKeys, region, kmsId)
                    encryptionKeys <- daoEncryptionKeys.rotateKeys(toDtoEncryptionKeys(newKeys), activeKeys.map(_.arn).toSet, clock)
                  } yield encryptionKeys
                } else {
                  logger.info(s"Customer specific EDK for region $region DOES NOT EXIST. Generating keys for Parent Account($parentAccountId) and its SubAccounts($subAccountIds), Kms provided: $kmsId")
                  for {
                    activeKeys <- daoEncryptionKeys.getKeys(accountIds, status = true)
                    newKeys <- getNewEncryptedCustomerKey(activeKeys, region, kmsId)
                    encryptionKeys <- daoEncryptionKeys.add(toDtoEncryptionKeys(newKeys))
                  } yield encryptionKeys
                }
              }
              response.map(r => if(r.getOrElse(0)>0) {
                logger.info(s"Added Customer keys for account $parentAccountId and its Sub Accounts $subAccountIds")
                true
              } else {
                logger.warn(s"Add Customer keys failed for account $parentAccountId and its Sub Accounts $subAccountIds")
                false
              })
            case _ =>
              logger.warn(s"No Active Encryption keys found for the Parent Account($parentAccountId). Generate Keys failed, Kms provided: $kmsId")
              throw NoEncryptionKeyFoundException(accountId = parentAccountId)
          }
      }
  }

  def getActiveCustomerKeys(accountId: AccountId): Future[Seq[KmsArnDetails]] = {
    daoEncryptionKeys.getKeysWithStatusAndType(accountId, isActive = true, isInternal = false) map (list => {
      val groupByArn = list.groupBy(_.arn)
      groupByArn.values.toSeq map (values => getKMSArn(values.head))
    })
  }

  def getServiceAccountKeys(serviceName: String, accountId: AccountId): Future[Map[Regions, EncryptedKey]] = {
    serviceKMSDetails.find(_.serviceName.value == serviceName) match {
      case Some(_) => {
        daoServiceEncryptionKeys.getKeys(accountId, serviceName, isActive = true).map(groupServiceKeyByRegion)
      }
      case None => throw InvalidServiceNameException(serviceName)
    }
  }

  /**
   * We decrypt the external key and re-encrypt with our internal key for safe transport across the network
   * Does not affect BYOK rules since we cannot fetch the key without decrypting with the BYOK KMS first
   * We ignore inactive data keys that are managed by an external KMS
   * @param serviceName The service for which to fetch the keys for
   * @return
   */
  def getAllKeysForService(serviceName: String, regionStr: String): Future[Either[ErrorResponse, EncryptedServiceKeys]] = {
    Try {
      Regions.fromName(regionStr.toLowerCase)
    }
    match {
      case Success(region) => {
        serviceKMSDetails.find(_.serviceName.value == serviceName) match {
          case Some(serviceKmsKeyDetail) => {
            daoServiceEncryptionKeys.getAllKeysForService(serviceName).flatMap(allDataKeys => {
              val allDataKeysForRegionRaw: Seq[DtoServiceEncryptionKey] = allDataKeys.filter(_.region == region)
              val allDataKeysForRegion = allDataKeysForRegionRaw.groupBy(_.accountId).foldLeft[Seq[DtoServiceEncryptionKey]](Seq.empty)((keyAccum, dataKeysWithAccount) => {
                val dataKeys = dataKeysWithAccount._2
                if(dataKeys.count(_.isActive) > 1) {
                  logger.info(s"Duplicate active keys found for service: $serviceName for account ${dataKeys.head.accountId} with keys ${dataKeys.map(_.id)}, checking uniqueness")
                  if(dataKeys.exists(_.isInternalKms == false)) {
                    //Always prefer latest external kms key if both active
                    keyAccum :+ dataKeys.filter(_.isInternalKms == false).maxBy(_.id)
                  }
                  else {
                    if(dataKeys.map(_.encryptionKey).toSet.size > 1) {
                      logger.info(s"Duplicate internal active keys found for service: $serviceName for account ${dataKeys.head.accountId} with keys ${dataKeys.map(_.id)}, different key material")
                      throw KeysAreNotMutuallyExclusiveException(dataKeys.head.kmsArn, AccountId(dataKeysWithAccount._1))
                    }
                    else {
                      //both keys are the same
                      keyAccum :+ dataKeys.minBy(_.id)
                    }
                  }
                }
                else {
                  keyAccum :+ dataKeys.head
                }
              })

              val internalKeys = allDataKeysForRegion
                .filter(_.isInternalKms)
                .filter(_.isActive)
              val internalKeysMap = internalKeys.map(internalKey => {
                internalKey.accountId -> EncryptedKey(internalKey.encryptionKey)
              }).toMap
              val externalKeys = allDataKeysForRegion
                .filter(_.isInternalKms == false)
                .filter(_.isActive)
              logger.info(s"getAllKeysForService -> Number of active internal keys found: ${internalKeys.size}")
              logger.info(s"getAllKeysForService -> Number of active external keys found: ${externalKeys.size}")
              val externalKeysReEncryptedFuture = reEncryptServiceKeyForTransit(serviceKmsKeyDetail, externalKeys, region)
              Future
                .sequence(externalKeysReEncryptedFuture)
                .map(res => {
                  res collect { case Right(r) => r}
                })
                .map(_.toMap)
                .map(externalKeysMap => {
                  externalKeysMap ++ internalKeysMap
                })
            }).map(EncryptedServiceKeys).map(Right(_))
          }
          case None => Future.successful(Left(ErrorResponseFactory.get(400, "Invalid Service Name provided")))
        }
      }
      case Failure(exception) => {
        Future.successful(Left(ErrorResponseFactory.get(code = 400, exception.getMessage)))
      }
    }
  }

  private def reEncryptServiceKeyForTransit(serviceKmsDetail: ServiceKmsDetail, keysToProcess: Seq[DtoServiceEncryptionKey], region: Regions): Seq[Future[Either[ErrorResponse, (Long, EncryptedKey)]]] = {
    val serviceKmsKey = serviceKmsDetail.kmsIds.value(region)
    keysToProcess.map(externalKey => {
      getDataKeyT(externalKey).flatMap(decryptedDataKey => {
        generateEncryptedDataKey(accountId = AccountId(externalKey.accountId), region, serviceKmsKey, decryptedDataKey._2) map { encryptedKey: EncryptedKey =>
          Right(externalKey.accountId -> encryptedKey)
        }
      }) recover {
        case e: AWSKMSException =>
          metrics.increment("servicekey.decryption.failure")
          logger.error(s"Failed to decrypt BYOK key from External KMS for accountId ${externalKey.accountId} with key ${externalKey.kmsArn}", e)
          Left(ErrorResponseFactory.get(e))
        case e: Throwable =>
          metrics.increment("servicekey.unknown.failure")
          logger.error(s"Failed to fetch service data key from external KMS for accountId ${externalKey.accountId} with key ${externalKey.kmsArn}", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
    })
  }

  override def generateCustomerKeys(parentAccountId: AccountId, subAccountIds: Set[AccountId], kmsId: KmsId): Future[Boolean] = {
    try{
      val region = getRegion(kmsId.value)
      val cacheKeys = (subAccountIds + parentAccountId).map(id => EncryptionKeysCacheKeyProvider.provide(id.value))
      for {
        res <- removeCacheKeysWrapped(activeKeysApiName, scalaCache, cacheKeys)(generateCustomerKeyForRegion(parentAccountId, subAccountIds, region, kmsId))
        _ <- generateCustomerServiceKeysForRegion(parentAccountId, subAccountIds, region, kmsId)
      } yield res
    } catch{
      case iae: IllegalArgumentException =>
        logger.info(s"Invalid Arn Provided $kmsId", iae)
        Future.failed(InvalidKMSIdException(parentAccountId, kmsId))
      case e: Throwable =>
        logger.info(s"GenerateCustomer keys failed $kmsId", e)
        Future.failed(e)
    }
  }

  def regenerateInternal(accountId: AccountId): Future[Map[Regions, EncryptedKey]] = {
    daoEncryptionKeys
      .hasEncryptionKeys(accountId, isInternal = true)
      .flatMap {
        case false =>
          throw NoEncryptionKeyFoundException(accountId)
        case true =>
          for {
            keys <- generateKeys(accountId, kmsIdsConfig.value).getOrElse(throw NoKMSIdFoundException(accountId))
            _ <- daoEncryptionKeys.deactivateKeys(accountId, isInternal = true)
            _ <- addKeys(accountId = accountId, keys = keys, isInternal = true)
          } yield keys.map(k=>k._1 -> k._2._1)
      }
  }

  override def regenerate(accountId: AccountId): Future[Map[Regions, EncryptedKey]] = {
    val cacheKeys = Set(EncryptionKeysCacheKeyProvider.provide(accountId.value))
    removeCacheKeysWrapped(activeKeysApiName, scalaCache, cacheKeys)(regenerateInternal(accountId))
  }

  override def getKeys(accountId: AccountId): Future[Map[Regions, EncryptedKey]] = {
    if (accountId.value == 0) {
      unknownAccountEncryptionKeysFetcher.fetch()
    } else {
      daoEncryptionKeys
        .getActiveKeys(accountId)
        .map(groupByRegion)
    }
  }

  override def generate(accountId: AccountId): Future[Boolean] = {
    def generateAndStoreAuditKeys(accountId: AccountId, isInternal: Boolean, regionToKmsKey: Map[Regions, KmsId]): Future[Boolean] = {
      // todo: Assume internal service account is using multiregion kms
      val resultFuture = daoEncryptionKeys
        .hasEncryptionKeys(accountId, isInternal)
        .flatMap {
          case true => throw EncryptionKeysAlreadyExistException(accountId)
          case false =>
            for {
              keys <- generateKeys(accountId, regionToKmsKey).getOrElse(throw NoKMSIdFoundException(accountId))
              _ <- addKeys(accountId = accountId, keys = keys, isInternal)
            } yield keys.nonEmpty
        }
      resultFuture.onFailure {
        case e: Throwable => logger.info("Unable to generate audit encryption keys", e)
      }
      resultFuture
    }

    def generateAndStoreServiceKeys(accountId: AccountId, isInternal: Boolean, serviceKMSDetails: Seq[ServiceKmsDetail]): Future[Seq[Boolean]] = {
      val isMultiRegion = isInternal
      val result = serviceKMSDetails.map(serviceKMSDetail => {
        daoServiceEncryptionKeys
          .hasEncryptionKeys(accountId, serviceKMSDetail.serviceName.value, isInternal)
          .flatMap {
            case true => throw EncryptionKeysAlreadyExistException(accountId)
            case false =>
              val serviceKeyAdditionFuture = for {
                keys <- generateKeys(accountId, serviceKMSDetail.kmsIds.value).getOrElse(throw NoKMSIdFoundException(accountId))
                _ <- addServiceKeys(accountId = accountId, keys = keys, isInternal, isMultiRegionKms = isMultiRegion, serviceKMSDetail.serviceName.value)
              } yield keys.nonEmpty
              serviceKeyAdditionFuture.onFailure {
                case e: Throwable => logger.info("Unable to generate service encryption keys", e)
              }
              serviceKeyAdditionFuture
          }
      })
      Future.sequence(result)
    }

    daoAccount.getAccount(accountId.value) flatMap {
      case Some(account) if account.parentId.isEmpty =>
        for {
          a <- generateAndStoreAuditKeys(accountId, isInternal = true, kmsIdsConfig.value)
          b <- generateAndStoreServiceKeys(accountId, isInternal = true, serviceKMSDetails)
        } yield a && b.forall(_ == true)
      case Some(account) =>
        val parentAccountId = account.parentId.get
        getActiveCustomerKeys(AccountId(parentAccountId)) flatMap( externalKmsKeys => {
          if(externalKmsKeys.isEmpty){
            for {
              a <- generateAndStoreAuditKeys(accountId, isInternal = true, kmsIdsConfig.value)
              b <- generateAndStoreServiceKeys(accountId, isInternal = true, serviceKMSDetails)
            } yield a && b.forall(_ == true)
          } else {
            daoAccount.addPermission(accountId.value, BusinessUserRoles.BYOK.id) flatMap( _ => {
              val externalRegionsToKmsIdMap = externalKmsKeys.map { key => getRegion(key.arn) -> KmsId(key.arn) }.toMap
              val externalServiceKMSDetails = serviceKMSDetails.map(_.copy(kmsIds = KmsIdsConfig(externalRegionsToKmsIdMap)))
              for {
                a <- generateAndStoreAuditKeys(accountId, isInternal = false, externalRegionsToKmsIdMap)
                b <- generateAndStoreServiceKeys(accountId, isInternal = false, externalServiceKMSDetails)
              } yield a && b.forall(_ == true)
            })
          }
        })
    }
  }

  override def hasKeys(accountId: AccountId): Future[Boolean] = {
    if(accountId.value == 0) {
      Future.successful(true)
    } else {
      daoEncryptionKeys.hasEncryptionKeys(accountId)
    }
  }


  private def toDtoEncryptionKeys(keys: Set[(AccountId, (Regions, EncryptedKey, KmsId))]): Iterable[DtoEncryptionKey] = {
    val timestamp = clock.now()
    keys.map {
      case (accountId, keyDetails) =>
        val dtoEncryptionKey = DtoEncryptionKey(
          id = 0,
          accountId = accountId.value,
          encryptionKey = keyDetails._2.value,
          arn = keyDetails._3.value,
          region = keyDetails._1,
          isActive = true,
          isInternal = false,
          createdAt = timestamp,
          updatedAt = timestamp
        )
        dtoEncryptionKey
    }
  }

  private def toCustomerDtoServiceEncryptionKeys(keys: Set[(AccountId, (Regions, EncryptedKey, KmsId))], serviceName: String, isMultiRegion: Boolean): Iterable[DtoServiceEncryptionKey] = {
    val timestamp = clock.now()
    keys.map {
      case (accountId, keyDetails) =>
        val dtoEncryptionKey = DtoServiceEncryptionKey(
          id = 0,
          accountId = accountId.value,
          encryptionKey = keyDetails._2.value,
          kmsArn = keyDetails._3.value,
          region = keyDetails._1,
          isActive = true,
          isInternalKms = false,
          createdAt = timestamp,
          updatedAt = timestamp,
          isMultiRegionKms = isMultiRegion,
          serviceName = serviceName
        )
        dtoEncryptionKey
    }
  }

  private def addKeys(
                       accountId: AccountId,
                       keys: Map[Regions, (EncryptedKey, KmsId)],
                       isInternal: Boolean
                     ): Future[Option[Int]] = {
    val dtoEncryptionKeys = keys.map {
      case (region, key) =>
        val dtoEncryptionKey = DtoEncryptionKey(
          id = 0,
          accountId = accountId.value,
          encryptionKey = key._1.value,
          arn = key._2.value,
          region = region,
          isActive = true,
          isInternal = isInternal,
          createdAt = clock.now(),
          updatedAt = clock.now()
        )
        dtoEncryptionKey
    }
    daoEncryptionKeys.add(dtoEncryptionKeys)
  }

  private def addServiceKeys(
                       accountId: AccountId,
                       keys: Map[Regions, (EncryptedKey, KmsId)],
                       isInternalKms: Boolean,
                       isMultiRegionKms: Boolean,
                       serviceName: String
                     ): Future[Option[Int]] = {
    val dtoEncryptionKeys = keys.map {
      case (region, key) =>
        val dtoEncryptionKey = DtoServiceEncryptionKey(
          id = 0,
          accountId = accountId.value,
          encryptionKey = key._1.value,
          kmsArn = key._2.value,
          region = region,
          isActive = true,
          isInternalKms = isInternalKms,
          createdAt = clock.now(),
          updatedAt = clock.now(),
          serviceName = serviceName,
          isMultiRegionKms = isMultiRegionKms
        )
        dtoEncryptionKey
    }
    daoServiceEncryptionKeys.add(dtoEncryptionKeys)
  }

  private def groupByRegion(keys: Seq[DtoEncryptionKey]): Map[Regions, EncryptedKey] = {
    keys
      .groupBy(_.region)
      .mapValues(ek => ek.map(_.encryptionKey).headOption match {
        case Some(key) => EncryptedKey(key)
        case _ => throw new Exception("Failed to find any encryption key value for a region")
      })
  }

  private def groupServiceKeyByRegion(keys: Seq[DtoServiceEncryptionKey]): Map[Regions, EncryptedKey] = {
    keys
      .groupBy(_.region)
      .mapValues(ek => ek.map(_.encryptionKey).headOption match {
        case Some(key) => EncryptedKey(key)
        case _ => throw new Exception("Failed to find any service encryption key value for a region")
      })
  }

  private def getKMSArn(dtoEncryptionKey: DtoEncryptionKey): KmsArnDetails ={
    KmsArnDetails(dtoEncryptionKey.arn, dtoEncryptionKey.createdAt)
  }

  override def getAllActiveKeys(accountId: AccountId): Future[EncryptedKeyDetails] = {

    if (accountId.value == 0) {
      unknownAccountEncryptionKeysFetcher.fetch() map (regionMap => {
        EncryptedKeyDetails(Map.empty[String, List[String]], regionMap map (item => (kmsIdsConfig.value(item._1).value, List(Base64.getEncoder.encodeToString(item._2.value)))))
      })
    } else {
      daoEncryptionKeys
        .getActiveKeys(accountId).map( keys => {

        val grouping = keys.groupBy(_.isInternal).map( keyType => (keyType._1, keyType._2.groupBy(_.arn)
          .map(arnGroup => (arnGroup._1, arnGroup._2.map(dtoEncryptionKey => Base64.getEncoder.encodeToString(dtoEncryptionKey.encryptionKey)).toList))))

        EncryptedKeyDetails(grouping.getOrElse(false, Map.empty[String, List[String]]), grouping.getOrElse(true, Map.empty[String, List[String]]))
      })
    }
  }

  override def testCustomerKms(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]] = {
    try {
      val region = getRegion(customerKey.kmsArn)
      val kmsId = KmsId(customerKey.kmsArn)
      val generateDataKeyFuture = generateDataKey(customerKey.accountId, region, kmsId) map (result => {
        val plainText = new Array[Byte](result.getPlaintext.remaining())
        result.getPlaintext.get(plainText)
        val encryptedText = new Array[Byte](result.getCiphertextBlob.remaining())
        result.getCiphertextBlob.get(encryptedText)
        (plainText, encryptedText)
      })
      val encryptionFuture = generateEncryptedDataKey(customerKey.accountId, region, kmsId, ByteBuffer.wrap(TestEncryptionByteArray)) map (result => {
        (TestEncryptionByteArray, result.value)
      })
      Future.sequence(List(generateDataKeyFuture, encryptionFuture)) flatMap (encryptionResults => {
        val decryptionFutures = encryptionResults map (encryptionResult => {
          decryptKey(customerKey.accountId, region, kmsId, EncryptedKey(encryptionResult._2)) map (decryptResult => {
            (encryptionResult._1, decryptResult.value)
          })
        })
        Future.sequence(decryptionFutures) map (decryptionResults => {
          val validations = decryptionResults map (decryptionResult => decryptionResult._1.deep == decryptionResult._2.deep)
          Right(validations forall (_ == true))
        }) recover {
          case e =>
            logger.error(s"Error while decrypting data key $e")
            Left(ErrorResponseFactory.get(CustomerKMSDecryptionError))
        }
        }) recover {
        case e =>
          logger.error(s"Error while generating/encrypting data key $e")
          Left(ErrorResponseFactory.get(CustomerKMSEncryptionError))
      }
    }
    catch {
      case iae: IllegalArgumentException =>
        logger.info(s"Invalid Arn Provided ${customerKey.kmsArn}", iae)
        Future.successful(Left(ErrorResponseFactory.get(CustomerKMSARNParsingError)))
      case rns: RegionNotSupportedException =>
        logger.info(s"Region not supported for the given KMS ${customerKey.kmsArn}", rns)
        Future.successful(Left(ErrorResponseFactory.get(CustomerKMSRegionNotSupported)))
      case e: Throwable =>
        logger.info(s"Test customer keys failed ${customerKey.kmsArn}", e)
        Future.successful(Left(ErrorResponseFactory.get(InternalError)))
    }
  }
}

object EncryptionKeysServiceImpl {
  val EncryptionContextAccountId = "socure_account_id"
  val TestEncryptionByteArray = "test_for_encryption".getBytes

  def apply(
             encryptionKeysConfig: EncryptionKeysConfig,
             dbProxy: DBProxyWithMetrics,
             driver: JdbcProfile,
             clock: Clock,
             daoAccount: DaoAccount,
             scalaCache: ScalaCache[_]
           )(implicit exe: ExecutionContext): EncryptionKeysServiceImpl = {
    val daoEncryptionKeys = new DaoEncryptionKeysImpl(
      dbProxy = dbProxy,
      profile = driver
    )

    val daoServiceEncryptionKeys = new DaoServiceEncryptionKeysImpl(
      dbProxy = dbProxy,
      profile = driver
    )

    val awsCredentialsProvider = new DefaultAWSCredentialsProviderChain

    def createKmsService(region: Regions): KmsService = {
      val awsKmsAsync = AWSKMSAsyncClientBuilder
        .standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(region)
        .build()
      new KmsService(awsKmsAsync)
    }
    val serviceKMSIds = encryptionKeysConfig
      .serviceKeys
      .flatMap(_.kmsIds
        .value
        .keySet)
    val auditKMSIds = encryptionKeysConfig
      .kmsIdsConfig
      .value
      .keySet

    val kmsServices = (auditKMSIds ++ serviceKMSIds)
      .map {
        region => region -> createKmsService(region)
      }.toMap

    val encryptionKeysService = new EncryptionKeysServiceImpl(
      daoEncryptionKeys = daoEncryptionKeys,
      daoServiceEncryptionKeys = daoServiceEncryptionKeys,
      kmsIdsConfig = encryptionKeysConfig.kmsIdsConfig,
      dataKeyLen = encryptionKeysConfig.dataKeyLen,
      serviceKMSDetails = encryptionKeysConfig.serviceKeys,
      kmsServices = kmsServices,
      clock = clock,
      daoAccount = daoAccount,
      unknownAccountEncryptionKeysFetcher = MicroServiceConfigUnknownAccountEncryptionKeysFetcher(),
      scalaCache = scalaCache
    )
    encryptionKeysService
  }
}
