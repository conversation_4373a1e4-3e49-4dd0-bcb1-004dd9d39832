package me.socure.account.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.ProductSettingsFields.{DISABLED, ENABLED, getFormattedDvConfigValues}
import me.socure.constants.{DashboardUserPermissions, ProductSettingsFields}
import me.socure.model.account.{DvConfiguration, DvConfigurationRequest, DvConfigurationValueGenerator, VerboseDvConfiguration}
import me.socure.model.dashboardv2.{AuditDetails, Creator, ProductSettingDelta}
import me.socure.model.dv.{DVConfigurationDecision, DVConfigurationDetails, DVConfigurationsForAccount, DVConfiguration => DvConfig}
import me.socure.model.{DvConfigurationResponse, ErrorResponse}
import me.socure.storage.slick.dao.DaoDvConfiguration
import me.socure.storage.slick.tables.account
import me.socure.storage.slick.tables.account.DtoEnvironmentDvConfiguration
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DvConfigurationService(daoDvConfiguration: DaoDvConfiguration, v2Validator: V2Validator, auditDetailsService: AuditDetailsService)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  /**
   * List dv configuration for an environment
   *
   * @param environmentId
   */
  def listDvConfigurationByEnvironment(environmentId: Long): Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
    daoDvConfiguration.listConfigurationByEnvironment(environmentId).map(environmentDvConfiguration =>
    if(environmentDvConfiguration.isEmpty) {
      Right(DvConfigurationValueGenerator.dvConfigurationDefaults())
    }
    else {
      val result = scala.collection.mutable.Map[String,DvConfiguration]()
      val configIds = new ListBuffer[Int]
        environmentDvConfiguration.flatMap(dvConfig => {
          result.put(DvConfig.byId(dvConfig.configId).map(_.name).get, DvConfiguration(
            configId= dvConfig.configId,
            configValue= dvConfig.configValue,
            decision= dvConfig.decision
          ))
          configIds += dvConfig.configId
        })
      (DvConfigurationValueGenerator.defaultConfigIds diff configIds) map { configId =>
        val name = DvConfig.byId(configId).map(_.name).get
        result.put(name, DvConfigurationValueGenerator.getDvConfigByName(name))
      }
      Right(result.toMap)
    })
  }

  def getDvConfigurationForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[DVConfigurationsForAccount]]] = {
    def toDVConfigView(environmentDvConfiguration: Seq[DtoEnvironmentDvConfiguration]): List[DVConfigurationDetails] = {
      val result = scala.collection.mutable.Map[String,VerboseDvConfiguration]()
      val configIds = new ListBuffer[Int]
      environmentDvConfiguration.flatMap(dvConfig => {
        result.put(DvConfig.byId(dvConfig.configId).map(_.name).get, DvConfiguration(
          configId= dvConfig.configId,
          configValue= dvConfig.configValue,
          decision= dvConfig.decision
        ).getVerboseDvConfigurationForDebug)
        configIds += dvConfig.configId
      })
      (DvConfigurationValueGenerator.defaultConfigIds diff configIds) map { configId =>
        val name = DvConfig.byId(configId).map(_.name).get
        result.put(name, DvConfigurationValueGenerator.getDvConfigByName(name).getVerboseDvConfigurationForDebug)
      }
      result.map(v => DVConfigurationDetails(v._1, v._2.configValue, v._2.decision)).toList
    }

    daoDvConfiguration.getDvConfigurationForAccount(accountId) map {
      case dvConfigurations if dvConfigurations.nonEmpty =>
        val environmentTypeDVConfigs = dvConfigurations.groupBy(_._1).map { dvConfiguration =>
          val dvConfigs = dvConfiguration._2.flatMap(_._3)
          if (dvConfigs.isEmpty) {
            (dvConfiguration._1, DvConfigurationValueGenerator.dvConfigurationDefaults().map { v =>
              val verboseDvConfiguration = v._2.getVerboseDvConfigurationForDebug
              DVConfigurationDetails(v._1, verboseDvConfiguration.configValue, verboseDvConfiguration.decision)}.toList)
          } else {
            (dvConfiguration._1, toDVConfigView(dvConfigs))
          }
        }
        Right(environmentTypeDVConfigs.map{ c => DVConfigurationsForAccount(c._1, c._2)}.toSeq)
      case _ =>
        logger.info(s"Could not get DV Configurations for account($accountId)")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not get DV Configurations for account($accountId)", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not get DV Configurations for account($accountId)", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listDvConfigurationByEnvironment(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true => listDvConfigurationByEnvironment(environmentId)
      case _ =>
        logger.info("Could not list dv configuration")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not list dv configuration ", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not list dv configuration", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }



  def retrieveConfiguration(environmentId: Long): Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
    daoDvConfiguration.listConfigurationByEnvironment(environmentId).map(environmentDvConfiguration =>
      if(environmentDvConfiguration.isEmpty) {
        Right(DvConfigurationValueGenerator.dvConfigurationDefaults().toMap)
      }
      else {
        val result: Map[String, DvConfiguration] = environmentDvConfiguration.flatMap(dvConfig => {
          DvConfig.byId(dvConfig.configId).map(_.name).map{ name =>
            name -> DvConfiguration(
              configId = dvConfig.configId,
              configValue = dvConfig.configValue,
              decision = dvConfig.decision
            )
          }
        }).toMap
        Right(result)
      }
    )
  }

  /**
   * Get Dv configuration for environmentId and configId
   *
   * @param environmentId
   */
  def getDvConfiguration(environmentId: Long, configId: Int) : Future[Either[ErrorResponse, DvConfiguration]] = {
    DvConfig.isValid(configId) match {
      case true =>
        daoDvConfiguration.getDvConfiguration(environmentId, configId) map {
          case Some(configuration) =>
            Right(
              DvConfiguration(
                configId= configuration.configId,
                configValue= configuration.configValue,
                decision= configuration.decision
              )
            )
          case _ => Left(ErrorResponseFactory.get(CouldNotFindDvConfiguration))
        }
      case false =>
        Future.successful(Left(ErrorResponseFactory.get(InvalidConfigIdPassed)))
    }
  }

  def formProductSettingsDeltaWithDvConf(accountId: Long,
                                         environmentId: Long,
                                         existingDvConf: Seq[VerboseDvConfiguration],
                                         newDvConf: Seq[VerboseDvConfiguration]): Seq[ProductSettingDelta] = {

    def getEnabledOrDisabledByDecision(value: String): String = {
      value match {
        case DVConfigurationDecision.NoAction.name => DISABLED
        case DVConfigurationDecision.None.name => DISABLED
        case _ => ENABLED
      }
    }
    def removeNoActionForDecision(value: String): String = {
      if(value.equalsIgnoreCase(DVConfigurationDecision.NoAction.name)) "" else value
    }

    def formProductSettingsDeltaMap(
                                  existingDv: Option[VerboseDvConfiguration],
                                  newDv: VerboseDvConfiguration
                                   ): Map[String, (String, String)] = {
      DvConfig.byId(newDv.configId).flatMap { config =>
        ProductSettingsFields.dvFields.get(config.name).map { fieldList =>

          val (existingConfigValue, existingConfigDecision, enabledOrDisabledByValue, enabledOrDisabledByDecision) = existingDv match {
            case Some(dv) => (dv.configValue, dv.decision, getFormattedDvConfigValues(dv.configValue), getEnabledOrDisabledByDecision(dv.decision))
            case None => ("", "", "", "")
          }
          val deltaMap: Map[String, (String, String)] = if (fieldList.size == 1) {
            Map(fieldList.apply(0) -> (enabledOrDisabledByValue, getFormattedDvConfigValues(newDv.configValue))).filter {
              case (_, (oldValue, newValue)) => oldValue != newValue }
          } else if (fieldList.size > 1) {
            val map = if (fieldList.size == 3) {
              Map(fieldList.apply(0) -> (enabledOrDisabledByDecision, getEnabledOrDisabledByDecision(newDv.decision)),
                fieldList.apply(1) -> (existingConfigValue, newDv.configValue),
                fieldList.apply(2) -> (removeNoActionForDecision(existingConfigDecision), removeNoActionForDecision(newDv.decision)))
            } else {
              val (oldConfigValue, newConfigValue) = if (fieldList.apply(0).contains("Under Threshold Of")) {
                val oldConfigValue = ProductSettingsFields.getDdvFieldsUnderThresholdValues(existingConfigValue)
                val newConfigValue = ProductSettingsFields.getDdvFieldsUnderThresholdValues(newDv.configValue)
                (oldConfigValue, newConfigValue)
              } else {
                (existingConfigValue, newDv.configValue)
              }
              Map(fieldList.apply(0) -> (oldConfigValue, newConfigValue),
                fieldList.apply(1) -> (removeNoActionForDecision(existingConfigDecision), removeNoActionForDecision(newDv.decision)))

            }
            map.filter { case (_, (oldValue, newValue)) => oldValue != newValue }
          } else {
            Map.empty
          }
          deltaMap
        }
      }.getOrElse(Map.empty)
    }

    newDvConf.flatMap { newDv =>
      val existingDvOpt = existingDvConf.find(_.configId == newDv.configId)
      existingDvOpt match {
        case Some(existingDv) =>
          if (newDv != existingDv) {
            val productSettingDeltaMap = formProductSettingsDeltaMap(Some(existingDv), newDv)
            Seq(ProductSettingDelta(accountId, Some(environmentId), ProductSettingsFields.DOCUMENT_VERIFICATION, productSettingDeltaMap))
          }
          else Seq.empty
        case _ => {
          val productSettingDeltaMap = formProductSettingsDeltaMap(None, newDv)
          Seq(ProductSettingDelta(accountId, Some(environmentId), ProductSettingsFields.DOCUMENT_VERIFICATION, productSettingDeltaMap))
        }
      }
    }
  }

  def formAuditDetailsWithProductSettingDelta(isSuccess: Boolean, environmentIds: Seq[Long], creator: Option[Creator], errorResponse: Option[ErrorResponse], existingDvConfig: Seq[DtoEnvironmentDvConfiguration], newDvConfig: Seq[DvConfiguration]): Future[AuditDetails] = {


    val newDv = newDvConfig map {
      (dvConfig) => dvConfig.getVerboseDvConfigurationForDebug
    }

    def formProductSettings(accountId: Long): Seq[ProductSettingDelta] = {
      val productSettings = environmentIds.flatMap {
        envId =>
          val existingConfigsByEnvId = existingDvConfig.filter(_.environmentId == envId)
          val defaultConfig = DvConfigurationValueGenerator.defaultConfigIds.diff(existingConfigsByEnvId.map(_.configId)) map {
            configId =>
              val name = DvConfig.byId(configId).map(_.name).get
              val dv = DvConfigurationValueGenerator.getDvConfigByName(name)
              VerboseDvConfiguration(
                dv.configId, dv.configValue, DVConfigurationDecision.byId(dv.decision).map(_.name).getOrElse("")
              )
          }

          val existingConfigs = existingConfigsByEnvId map {
              (dvConfig) =>
                VerboseDvConfiguration(
                  dvConfig.configId, dvConfig.configValue, DVConfigurationDecision.byId(dvConfig.decision).map(_.name).getOrElse("")
                )
            }

          val existingDv = existingConfigs ++ defaultConfig

          formProductSettingsDeltaWithDvConf(accountId, envId, existingDv.sortBy(dv => dv.configId), newDv.sortBy(dv => dv.configId))
      }
      productSettings
    }

    def formAuditDetails(productSettingDelta: Seq[ProductSettingDelta]): Future[AuditDetails] = {
      auditDetailsService.formActionUserInfo(creator) map {
        actionUserInfo => AuditDetails(isSuccess, actionUserInfo, None, errorResponse, productSettingDelta)
      }
    }


    creator match {
      case Some(creator) =>
        formAuditDetails(formProductSettings(creator.accountId))

      case None =>
        formAuditDetails(formProductSettings(0L))

    }
  }


  /**
   * Save document verification configuration
   *
   * @param dvConfigurationSeq
   */
  def saveEnvironmentDvConfiguration(dvConfigurationSeq: Seq[DvConfiguration], environmentId: Long, creator: Option[Creator]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true =>
        for {
          existingDvConfig <- daoDvConfiguration.listConfigurationByEnvironment(environmentId)
          resp <- saveEnvironmentDvConfiguration(dvConfigurationSeq, environmentId)
          auditDetailsResp <- resp match {
            case Right(isSuccess) => formAuditDetailsWithProductSettingDelta(isSuccess, Seq(environmentId), creator, None, existingDvConfig, dvConfigurationSeq) map {
              auditDetails => (auditDetails, Right(isSuccess))
            }
            case Left(error) => logger.info("Could not save dv configuration")
              formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), existingDvConfig, dvConfigurationSeq) map {
                auditDetails => (auditDetails, Left(error))
              }
          }
        } yield auditDetailsResp

      case _ =>
        logger.info("Could not save dv configuration")
        val error = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationSeq) map {
          auditDetails => (auditDetails, Left(error))
        }
    } recoverWith {
      case ex: ErrorResponseException =>
        logger.info("Could not save dv configuration ", ex)
        val error = ex.errorResponse
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationSeq) map {
          auditDetails => (auditDetails, Left(error))
        }
      case e : Throwable =>
        logger.info("Could not save dv configuration", e)
        val error = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationSeq) map {
          auditDetails => (auditDetails, Left(error))
        }
    }
  }

  def listDvConfigurationForAccountsByEnvironment(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, DvConfigurationResponse]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true => daoDvConfiguration.listConfigurationByEnvironmentWithCount(environmentId, creator.map(_.accountId).get).map(environmentDvConfiguration =>
        if (environmentDvConfiguration._1.isEmpty) {
          Right(DvConfigurationValueGenerator.dvConfigurationResponseDefaults())
        }
        else {
          val result = scala.collection.mutable.Map[String, DvConfiguration]()
          val configIds = new ListBuffer[Int]
          environmentDvConfiguration._1.flatMap(dvConfig => {
            result.put(DvConfig.byId(dvConfig.configId).map(_.name).get, DvConfiguration(
              configId = dvConfig.configId,
              configValue = dvConfig.configValue,
              decision = dvConfig.decision
            ))
            configIds += dvConfig.configId
          })
          (DvConfigurationValueGenerator.defaultConfigIds diff configIds) map { configId =>
            val name = DvConfig.byId(configId).map(_.name).get
            result.put(name, DvConfigurationValueGenerator.getDvConfigByName(name))
          }
          val dvConfigurationResponse = DvConfigurationResponse(
            dvConfigurations = result.toMap,
            isForceInherit = environmentDvConfiguration._1(0).isForceInherit,
            byAccountId = environmentDvConfiguration._1(0).byAccountId,
            inheritedToSubAccountsCount = Some(environmentDvConfiguration._2)
          )
          Right(dvConfigurationResponse)
        })
      case _ =>
        logger.info("Could not list dv configuration")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not list dv configuration ", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not list dv configuration", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def saveEnvironmentDvConfigurationForAccounts(dvConfigurationRequest: DvConfigurationRequest, environmentId: Long, creator: Option[Creator]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoDvConfiguration.getEnvironmentIds(dvConfigurationRequest.environmentTypes, dvConfigurationRequest.accountIds) flatMap {
        case environmentIds: Seq[Long] =>

          val dtoConfigurationSeq = environmentIds.flatMap(environmentId => dvConfigurationRequest.dvConfigurations.map(dvConfiguration =>
            account.DtoEnvironmentDvConfiguration(
              environmentId,
              dvConfiguration.configId,
              dvConfiguration.configValue,
              dvConfiguration.decision,
              creator.map(_.accountId),
              dvConfigurationRequest.isForceInherit,
              DateTime.now,
              DateTime.now)))

          daoDvConfiguration.listConfigurationByEnvironmentIds(environmentIds) flatMap {
            currentDvConfig =>
              daoDvConfiguration.saveDvConfiguration(dtoConfigurationSeq) flatMap {
                case count if count > 0 =>
                  formAuditDetailsWithProductSettingDelta(true, environmentIds, creator, None, currentDvConfig, dvConfigurationRequest.dvConfigurations) map {
                    auditDetails => (auditDetails, Right(true))
                  }
                case err =>
                  logger.info("Error occurred while saving dv configuration", err)
                  val error = ErrorResponseFactory.get(CouldNotSaveDvConfiguration)
                  formAuditDetailsWithProductSettingDelta(false, environmentIds, creator, Some(error), currentDvConfig, dvConfigurationRequest.dvConfigurations) map {
                    auditDetails => (auditDetails, Left(error))
                  }
              } recoverWith {
                case t: Throwable =>
                  val error = ErrorResponseFactory.get(CouldNotSaveDvConfiguration)
                  formAuditDetailsWithProductSettingDelta(false, environmentIds, creator, Some(error), currentDvConfig, dvConfigurationRequest.dvConfigurations) map {
                    auditDetails => (auditDetails, Left(error))
                  }
              }
          }

        case _ => logger.info(s"Could not fetch EnvironmentIds while saving dv configuration ")
          val error = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationRequest.dvConfigurations) map {
            auditDetails => (auditDetails, Left(error))
          }
      }
      case _ =>
        logger.info("Could not save dv configuration")
        val error = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationRequest.dvConfigurations) map {
          auditDetails => (auditDetails, Left(error))
        }
    } recoverWith {
      case ex: ErrorResponseException =>
        logger.info("Could not save dv configuration ", ex)
        val error = ex.errorResponse
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationRequest.dvConfigurations) map {
          auditDetails => (auditDetails, Left(error))
        }
      case e: Throwable =>
        logger.info("Could not save dv configuration", e)
        val error = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDelta(false, Seq(environmentId), creator, Some(error), Seq.empty, dvConfigurationRequest.dvConfigurations) map {
          auditDetails => (auditDetails, Left(error))
        }
    }
  }

  def saveEnvironmentDvConfiguration(dvConfigurationSeq: Seq[DvConfiguration], environmentId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val dtoConfigurationSeq = dvConfigurationSeq.map(dvConfiguration => account.DtoEnvironmentDvConfiguration(environmentId, dvConfiguration.configId, dvConfiguration.configValue, dvConfiguration.decision, None, None,  DateTime.now, DateTime.now))
        daoDvConfiguration.saveDvConfiguration(dtoConfigurationSeq) map {
          case count if count > 0 =>
            Right(true)
          case err =>
            logger.info("Error occurred while saving dv configuration", err)
            Left(ErrorResponseFactory.get(CouldNotSaveDvConfiguration))
        } recover {
          case t: Throwable =>
            Left(ErrorResponseFactory.get(CouldNotSaveDvConfiguration))
        }
  }

  /**
   * Update document verification configuration
   *
   * @param dvConfiguration
   */
  def updateDvConfiguration(dvConfiguration: DvConfiguration, environmentId: Long): Unit = {
    val dtoAccountDvConfiguration = DtoEnvironmentDvConfiguration(environmentId, dvConfiguration.configId, dvConfiguration.configValue, dvConfiguration.decision, None, None, DateTime.now, DateTime.now)
    daoDvConfiguration.updateDvConfiguration(dtoAccountDvConfiguration) map {
      case count if count>0 =>
        Right(true)
      case _ =>
        Left(ErrorResponseFactory.get(CouldNotSaveDvConfiguration))
    }
  }

}
