package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, UnableToCreatePublicWebhook, UnableToUpdateML<PERSON>ields, UnknownError}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.mla.MLAInputRequest
import me.socure.storage.slick.dao.DaoMLAFields
import me.socure.storage.slick.tables.DtoAccountMLAField
import me.socure.storage.slick.tables.account.DtoAccount
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class MLAService(
                  daoAccount: DaoAccount,
                  daoMLAFields: DaoMLAFields,
                  clock: Clock
                ) (implicit ec : ExecutionContext) {
  private val logger = LoggerFactory.getLogger(classOf[MLAService])
  def saveMLAFields(request: MLAInputRequest): Future[Either[ErrorResponse, MLAInputRequest]] = {
    daoMLAFields.saveMLAFields(DtoAccountMLAField(
      id = 0,
      accountId = request.accountId.toLong,
      memberNumber = request.memberNumber,
      securityCode = request.securityCode,
      updatedAt = Some(clock.now())
    )) flatMap {
      case 1 =>  Future.successful(Right(request))
      case _ =>  Future.successful(Left(ErrorResponseFactory.get(UnableToUpdateMLAFields)))
    } recover {
      case e =>
        logger.info("Could not save MLA Fields : ", e)
        Left(ErrorResponseFactory.get(UnableToUpdateMLAFields))
    }
  }

  def fetchMLAFields(accountId: Long): Future[DtoAccountMLAField] = {
    daoAccount.getAccount(accountId).flatMap {
      case Some(_) =>
            daoMLAFields.getMLAFields(accountId) flatMap {
              case Some(mLAField) => Future.successful(mLAField)
              case _ =>
                logger.info(s"mla fields not obtained for $accountId")
                getDefaultDtoMLAField(accountId)
            }
      case _ =>
        logger.info(s"mla fields not obtained for $accountId")
        getDefaultDtoMLAField(accountId)
    }
  }

  private def getDefaultDtoMLAField(accountId: Long) = {
    Future.successful(DtoAccountMLAField(
      id = 0,
      accountId = accountId,
      memberNumber = "",
      securityCode = "",
      updatedAt = None
    ))
  }
}
