package me.socure.account.service

import java.sql.SQLException
import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, InvalidEIN, UnableToSaveLookupApiKey, UnableToSaveOTPWorkFlow, UnknownError}
import me.socure.common.clock.Clock
import me.socure.model.{BusinessUserRoles, ErrorResponse, ein}
import me.socure.model.ein.{EIN, EINRequest, EINResponse, LookupApiKey, LookupApiKeyRequest, LookupApiKeyResponse, LookupApiKeyServiceIdRequest, LookupApiKeyServiceIdResponse}
import me.socure.storage.slick.dao.DaoEIN
import me.socure.storage.slick.tables.account.DtoEIN
import org.slf4j.LoggerFactory

import java.util.Base64
import scala.concurrent.{ExecutionContext, Future}

class EINService(
                  daoEIN: DaoEIN,
                  daoAccount: DaoAccount,
                  clock: Clock
                ) (implicit ec : ExecutionContext) {
  private val logger = LoggerFactory.getLogger(classOf[EINService])
  private val pattern = "^[0-9]{9}$";
  private val EINPlaceHolder = "*********"

  private def validateApiKey(apiKey: String): Boolean = {
    Option(apiKey)
      .filter(_.nonEmpty)
      .flatMap(decodeBase64)
      .map(_.split(":"))
      .exists(_.length == 2)
  }

  private def validateServiceId(serviceId: String): Boolean = {
    Option(serviceId)
      .filter(_.nonEmpty)
      .flatMap(decodeBase64)
      .isDefined
  }

  private def decodeBase64(encoded: String): Option[String] = {
    try {
      Some(new String(Base64.getDecoder.decode(encoded)))
    } catch {
      case _: IllegalArgumentException => None
    }
  }

  private def validateEIN(ein: String): Boolean = {
    ein.matches(pattern)
  }

  private def getLatest(accountId: Long, permission: Int, action: String): Future[Option[DtoEIN]] = {
    daoEIN.getDtoEINByPermission(accountId, permission, action).map {
      case list if list.nonEmpty =>
        Some(list.maxBy(_.id))
      case _ => None
    }
  }

  private def getLatest(accountId: Long, permissionIds: Seq[Int], action: String): Future[Option[DtoEIN]] = {
    daoEIN.getDtoEINByPermissionIds(accountId, permissionIds, action).map {
      case list if list.nonEmpty =>
        Some(list.maxBy(_.id))
      case _ => None
    }
  }

  def upsertEIN(request: EINRequest): Future[Either[ErrorResponse, EINResponse]] = {
    if (validateEIN(request.ein)) {
      val dtoEIN = DtoEIN(id = 0, accountId = request.accountId, ein = request.ein, updatedAt = Some(clock.now()), lookupApiKey = None, serviceId = None)
      daoEIN.getEINs(accountId = request.accountId) flatMap {
        case eins0 if eins0.isEmpty => daoEIN.insertEIN(dtoEIN)
        case eins1 if eins1.length == 1 => daoEIN.upsertEIN(dtoEIN)
        case _ => daoEIN.editEIN(dtoEIN)
      } flatMap {
        case 1 => Future.successful(Right(EINResponse(request.accountId, request.ein)))
        case _ =>
          logger.info(s"Unable to save EIN:${request.ein}, for account:${request.accountId}")
          Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      } recover {
        case sqlExp: SQLException => logger.info(s"Unable to save EIN:${request.ein}, for account:${request.accountId}", sqlExp)
          Left(ErrorResponseFactory.get(AccountNotFound))
        case e: Throwable => logger.info(s"Unable to save EIN:${request.ein}, for account:${request.accountId}", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
    } else {
      logger.info(s"Invalid EIN, Unable to save EIN:${request.ein}, for account:${request.accountId}")
      Future.successful(Left(ErrorResponseFactory.get(InvalidEIN)))
    }
  }

  def fetchEIN(accountId: Long): Future[Either[ErrorResponse, Option[EIN]]] = {
    daoAccount.getAccount(accountId).flatMap {
      case Some(_) =>
        for {
          dtoEINOpt <- getLatest(accountId, BusinessUserRoles.ECBSV.id, "getLatestEIN")
        } yield {
          dtoEINOpt match {
            case Some(dtoEIN) => Right(Some(EIN(dtoEIN.ein)))
            case None => Right(None)
          }
        }
      case None =>
        logger.info(s"Unable to fetch EIN for account, invalid account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def fetchLookupKey(accountId: Long): Future[Either[ErrorResponse, Option[LookupApiKey]]] = {
    daoAccount.getAccount(accountId).flatMap {
      case Some(_) =>
        getLatest(accountId, BusinessUserRoles.SimSwapLookup.id, "getLatestLookupApiKey").map {
          case Some(dtoEIN) =>
            Right(dtoEIN.lookupApiKey.map(LookupApiKey))
          case None =>
            Right(None)
        }
      case None =>
        logger.info(s"Unable to fetch LookupApiKey for account, invalid account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def upsertLookupApiKey(request: LookupApiKeyRequest, onlyWhen: Boolean = false): Future[Either[ErrorResponse, LookupApiKeyResponse]] = {
    def handleUpdate(): Future[Either[ErrorResponse, LookupApiKeyResponse]] = {
      val dtoEIN = DtoEIN(
        id = 0,
        accountId = request.accountId,
        ein = EINPlaceHolder,
        updatedAt = Some(clock.now()),
        lookupApiKey = Some(request.lookupApiKey),
        serviceId = None
      )

      daoEIN.getEINs(accountId = request.accountId).flatMap {
        case Nil  => daoEIN.insertEIN(dtoEIN)
        case _    => daoEIN.upsertLookupApiKey(dtoEIN)
      }.map {
        case n if n > 0 => Right(LookupApiKeyResponse(request.accountId, request.lookupApiKey))
        case _ =>
          logger.info(s"Unable to save LookupApiKey:${request.lookupApiKey}, for account:${request.accountId}")
          Left(ErrorResponseFactory.get(UnknownError))
      }.recover {
        case sqlExp: SQLException =>
          logger.info(s"SQL Exception - Unable to save LookupApiKey:${request.lookupApiKey}, for account:${request.accountId}", sqlExp)
          Left(ErrorResponseFactory.get(AccountNotFound))
        case e: Throwable =>
          logger.info(s"Exception - Unable to save LookupApiKey:${request.lookupApiKey}, for account:${request.accountId}", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
    }
    if (validateApiKey(request.lookupApiKey)) {
      if (onlyWhen) {
        daoEIN.isPermissionEnabled(request.accountId, BusinessUserRoles.SimSwapLookup.id, "isPermissionEnabled-LookupApiKey").flatMap {
          case true => handleUpdate()
          case false =>
            logger.info(s"SimSwapLookup is not provisioned for account:${request.accountId}. Unable to save LookupApiKey:${request.lookupApiKey}")
            Future.successful(Left(ErrorResponseFactory.get(UnableToSaveLookupApiKey)))
        }
      } else {
        handleUpdate()
      }
    } else {
      logger.info(s"Invalid ApiKey. Unable to save LookupApiKey:${request.lookupApiKey} for account:${request.accountId}")
      Future.successful(Left(ErrorResponseFactory.get(UnableToSaveLookupApiKey)))
    }
  }

  def fetchLookupKeyServiceSid(accountId: Long): Future[Either[ErrorResponse, Option[LookupApiKeyServiceIdResponse]]] = {
    daoAccount.getAccount(accountId).flatMap {
      case None =>
        logger.info(s"Unable to fetch LookupKeyServiceSid Info for account, invalid account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
      case Some(_) =>
        getLatest(accountId, Seq(BusinessUserRoles.MFAOrchestration.id, BusinessUserRoles.SilentNetworkAuthentication.id), "getLatestLookupKeyServiceSidInformation").map {
          case Some(dtoEIN) =>
            (dtoEIN.lookupApiKey, dtoEIN.serviceId) match {
              case (Some(lookupApiKey), Some(serviceId)) =>
                Right(Some(LookupApiKeyServiceIdResponse(accountId, lookupApiKey, serviceId)))
              case _ =>
                Right(None)
            }
          case None => Right(None)
        }
    }
  }

  def upsertLookupApiKeyAndServiceSid(request: LookupApiKeyServiceIdRequest, onlyWhen: Boolean = false): Future[Either[ErrorResponse, LookupApiKeyServiceIdResponse]] = {
    def handleUpdate(): Future[Either[ErrorResponse, LookupApiKeyServiceIdResponse]] = {
      val dtoEIN = DtoEIN(
        id = 0,
        accountId = request.accountId,
        ein = EINPlaceHolder,
        updatedAt = Some(clock.now()),
        lookupApiKey = Some(request.apiKey),
        serviceId = Some(request.serviceId)
      )

      daoEIN.getEINs(accountId = request.accountId).flatMap {
        case Nil  => daoEIN.insertEIN(dtoEIN)
        case _    => daoEIN.upsertServiceId(dtoEIN)
      }.map {
        case n if n > 0 => Right(LookupApiKeyServiceIdResponse(request.accountId, request.apiKey, request.serviceId))
        case _ =>
          logger.info(s"Unable to save LookupApiKeyServiceSid apiKey:${request.apiKey} and ServiceId: ${request.serviceId}, for account:${request.accountId}")
          Left(ErrorResponseFactory.get(UnknownError))
      }.recover {
        case sqlExp: SQLException =>
          logger.info(s"SQL Exception - Unable to save LookupApiKeyServiceSid apiKey:${request.apiKey} and ServiceId: ${request.serviceId}, for account:${request.accountId}", sqlExp)
          Left(ErrorResponseFactory.get(AccountNotFound))
        case e: Throwable =>
          logger.info(s"Exception - Unable to save LookupApiKeyServiceSid apiKey:${request.apiKey} and ServiceId: ${request.serviceId}, for account:${request.accountId}", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
    }
    if(validateApiKey(request.apiKey) && validateServiceId(request.serviceId)) {
      if (onlyWhen) {
        for {
          isMFAEnabled <- daoEIN.isPermissionEnabled(request.accountId, BusinessUserRoles.MFAOrchestration.id, "isPermissionEnabled-MFAOrchestration")
          isSilentNetworkEnabled <- daoEIN.isPermissionEnabled(request.accountId, BusinessUserRoles.SilentNetworkAuthentication.id, "isPermissionEnabled-SilentNetworkAuthentication")
          result <- if (isMFAEnabled || isSilentNetworkEnabled) {
            handleUpdate()
          } else {
            logger.info(s"MFAOrchestration and SilentNetworkAuthentication are not provisioned for account:${request.accountId}. Unable to save OTPWorkFlow apiKey:${request.apiKey} and ServiceId: ${request.serviceId}")
            Future.successful(Left(ErrorResponseFactory.get(UnableToSaveOTPWorkFlow)))
          }
        } yield result
      } else {
        handleUpdate()
      }
    } else {
      logger.info(s"Invalid ApiKey or ServiceId. Unable to save OTPWorkFlow for account:${request.accountId}, apiKey:${request.apiKey} and ServiceId: ${request.serviceId}")
      Future.successful(Left(ErrorResponseFactory.get(UnableToSaveOTPWorkFlow)))
    }
  }
}
