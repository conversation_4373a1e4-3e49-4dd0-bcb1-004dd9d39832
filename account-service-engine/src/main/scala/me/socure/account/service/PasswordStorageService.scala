package me.socure.account.service

import com.github.tototoshi.slick.MySQLJodaSupport._
import me.socure.account.hash.Argon2
import me.socure.account.utils.Md5Hash
import me.socure.common.clock.Clock
import me.socure.common.publicid.PublicId
import me.socure.salt.client.SaltClient
import me.socure.salt.model.Salt
import me.socure.storage.slick.tables.password.{DaoTblPassword, DtoPassword, HashAlgorithm, PasswordStatus}
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import org.apache.commons.codec.digest.DigestUtils
import org.joda.time.DateTime
import slick.driver.JdbcProfile

import java.security.MessageDigest
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 3/26/17.
  */
class PasswordStorageService(val dbProxyWithMetrics : DBProxyWithMetrics,
                             val profile: Jdbc<PERSON>rofile,
                             clock: Clock,
                             saltClient: SaltClient)(implicit ec: ExecutionContext) extends DaoTblPassword {

  private val maximumHistory = 8

  import profile.api._

  private def verify(entry: DtoPassword, password: String): Future[Boolean] = {
    entry.hashAlgorithm match {
      case HashAlgorithm.V1 =>
        Future.successful(Md5Hash.getHash(password, entry.businessUserid) == entry.password)
      case HashAlgorithm.V2 =>
        saltClient.get(PublicId(entry.saltId.get)).map {
          case Left(e) =>
            throw new Exception(e.message)
          case Right(s) =>
            Argon2.verify(entry.password, password, s.salt)
        }
      case HashAlgorithm.V3 =>
        saltClient.get(PublicId(entry.saltId.get)).map {
          case Left(e) =>
            throw new Exception(e.message)
          case Right(s) =>
            hashV3(password, s) == entry.password
        }
    }
  }

  private def hashV3(password: String, s: Salt) = {
    val md = MessageDigest.getInstance("SHA-256")
    md.update(s.salt.getBytes())
    //converting to Hex String
    md.digest(password.getBytes()).map("%02X" format _).mkString
  }

  def alreadyUsed(id: Long, password: String): Future[Boolean] = {
    val query = TblPasswords.filter(r => r.businessUserId === id).sortBy(_.createdAt.desc).take(maximumHistory)
    dbProxyWithMetrics.run(query.result, DBTables.TblPassword, DBActions.Select, "alreadyUsed").flatMap(list =>
      Future.sequence(list.map(r => verify(r, password))).map(_.contains(true))
    )
  }

  def validate(id: Long, password: String): Future[Boolean] = {
    val query = TblPasswords.filter(r => r.businessUserId === id && r.status === PasswordStatus.Active ).take(1)
    dbProxyWithMetrics.run(query.result, DBTables.TblPassword, DBActions.Select, "validate").flatMap(r => {
      verify(r.head, password).map(valid=>{
        if(valid && r.head.hashAlgorithm != HashAlgorithm.V3){
          updatePassword(r.head.businessUserid,password)
        }
        valid
      })
    })
  }

  def updatePassword(id: Long, password: String): Future[Unit] = {
    saltClient.generate().flatMap {
      case Left(e) => Future.failed(new Exception(e.message))
      case Right(s) =>
        val now = clock.now()
        val revokeCurrent = TblPasswords
          .filter(r => r.businessUserId === id && r.status === PasswordStatus.Active)
          .map(r => (r.status, r.updatedAt))
          .update(PasswordStatus.Deprecated, now)

        val hash = hashV3(password, s)
        val insertNew = TblPasswords += DtoPassword(0, id, Some(s.id.value), hash, PasswordStatus.Active, HashAlgorithm.V3, now, now)
        val transaction = DBIO.seq(
          revokeCurrent,
          insertNew
        )
        dbProxyWithMetrics.run(transaction.transactionally, DBTables.TblPassword, DBActions.Upsert, "updatePassword")
    }
  }

  def createPassword(id: Long, password: String): Future[Boolean] = {
    saltClient.generate().flatMap {
      case Left(e) => Future.failed(new Exception(e.message))
      case Right(s) =>
        val now = clock.now()
        countPasswords(id).flatMap {
          case 0 =>
            val hash = hashV3(password, s)
            val insertNew = TblPasswords += DtoPassword(0, id, Some(s.id.value), hash, PasswordStatus.Active, HashAlgorithm.V3, now, now)
            dbProxyWithMetrics.run(insertNew, DBTables.TblPassword, DBActions.Insert, "createPassword").map(_ == 1)
          case _ =>
            Future.successful(false)
        }
    }
  }

  def countPasswords(id: Long): Future[Int] = {
    val query = TblPasswords.filter(_.businessUserId === id).countDistinct
    dbProxyWithMetrics.run(query.result, DBTables.TblPassword, DBActions.Select, "countPasswords")
  }

  def invalidatePassword(id: Long): Future[Boolean] = {
    val query = TblPasswords.filter(u => u.businessUserId === id && u.status === PasswordStatus.Active).map(_.status).update(PasswordStatus.Deprecated)
    dbProxyWithMetrics.run(query, DBTables.TblPassword, DBActions.Update, "invalidatePassword").map(_ > 0)
  }

  def isPasswordExpired(id: Long, currentDate : DateTime, days : Int): Future[Boolean] = {
    val query = TblPasswords.filter(p => p.businessUserId === id && p.status === PasswordStatus.Active && p.updatedAt <= currentDate.minusDays(days)).countDistinct
    dbProxyWithMetrics.run(query.result, DBTables.TblPassword, DBActions.Select, "isPasswordExpired").map(_ > 0)
  }

  def getPasswordUpdatedAt(id: Long): Future[Option[DateTime]] = {
    val query = TblPasswords.filter(p => p.businessUserId === id && p.status === PasswordStatus.Active).map(_.updatedAt)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblPassword, DBActions.Select, "getPasswordUpdatedAt")
  }

  def deletePassword(id: Long): Future[Boolean] = {
    val query = TblPasswords.filter(_.businessUserId === id).delete
    dbProxyWithMetrics.run(query, DBTables.TblPassword, DBActions.Delete, "deletePassword").map(_ > 0)
  }

  def hasValidPassword(id: Long): Future[Boolean] = {
    val query = TblPasswords.filter(u => u.businessUserId === id && u.status === PasswordStatus.Active).length
    dbProxyWithMetrics.run(query.result, DBTables.TblPassword, DBActions.Select, "hasValidPassword").map(_ > 0)
  }

}
