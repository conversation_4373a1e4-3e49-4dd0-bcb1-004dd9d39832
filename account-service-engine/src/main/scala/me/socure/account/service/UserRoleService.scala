package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ExceptionCodes.NoDataFound
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.{AccountManagementDefaults, AccountTypes, DashboardUserPermissions, EnvironmentTypes, SystemDefiendRolesPermissions, SystemDefinedRoles}
import me.socure.convertors.AccountConvertors
import me.socure.model.{ErrorResponse, UsersAndRoles}
import me.socure.model.account.{DashboardUserPermissionResult, RolePermissionTemplateAssociation, UserRole, UserRoleInput, UserRoleResult}
import me.socure.model.dashboardv2.Creator
import me.socure.storage.slick.dao.{DaoAccountUIConfiguration, DaoAccountV2}
import me.socure.storage.slick.tables.account.{DtoAccountHierarchy, DtoAccountUIConfiguration}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserRoleService(daoAccount: DaoAccount, daoAccountV2: DaoAccountV2, v2Validator: V2Validator, daoUIAccountConfiguration: DaoAccountUIConfiguration)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, UserRole]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(id, accountId)
      res <- daoAccountV2.getUserRole(id) flatMap {
        case Some(dtoUserRole) =>
          v2Validator.validateAccountAccess(dtoUserRole.byAccountId, accountId) map { _ =>
            Right(AccountConvertors.toUserRole(dtoUserRole))
          }
        case _ =>
          logger.info(s"User role not found for id $id")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not view user role", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching user role", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound))
    }
  }

  def getUserRolesByPublicAccountId(publicAccountId: String): Future[Either[ErrorResponse, Seq[UserRoleResult]]] = {

    daoAccount.fetchAccountByPublicAccountId(publicAccountId).flatMap {
      case Some(account) =>
        fetchCustomAndSystemRoles(account.accountId).map(roleSeq => Right(roleSeq))

      case None =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPublicAccountId)))

    }
  }


  def getUserRolesByAccountId(accountId: Long, creator: Creator, filterSystemDefinedRoles: Boolean = false): Future[Either[ErrorResponse, Seq[UserRoleResult]]] = {

    (for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
      _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, permissions = Set(DashboardUserPermissions.USER_ROLES_VIEW.id), envType = EnvironmentTypes.GLOBAL_ENVIRONMENT.id, orPermissions = Set(DashboardUserPermissions.USERS_VIEW.id))
      user <- daoAccountV2.getUserDetailsV2(creator.userId, accountId)
      roles <- fetchRolesList(accountId, filterSystemDefinedRoles, user.exists(user => user._3.matches(AccountManagementDefaults.internalEmailDomains)))
    } yield Right(roles)) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not view user roles by account", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching user role by account", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserRolesFetchError))
    }
  }


  private def fetchRolesList(accountId: Long, filterSystemDefinedRoles: Boolean, isSocureUser:Boolean = false): Future[Seq[UserRoleResult]] = {
    v2Validator.getRootParentAccountType(accountId).flatMap {
      case Some(accountType) if accountType == AccountTypes.DIRECT_EFFECTIV.id =>
        fetchPlatformRoles(accountId, isSocureUser)
      case _ =>
        fetchCustomAndSystemRoles(accountId, filterSystemDefinedRoles)
    }
  }

  private def fetchPlatformRoles(accountId: Long, isSocureUser: Boolean = false): Future[Seq[UserRoleResult]] = {
    val riskOsSupportRoles = List(SystemDefinedRoles.RISKOS_SUPPORT_ADMIN.roleType, SystemDefinedRoles.RISKOS_SUPPORT_VIEWER.roleType)
    daoAccountV2.getSystemDefinedRolesDetailsByAccountId(accountId).map { roleDetails =>
      val validRolePermissionMap = SystemDefiendRolesPermissions
        .getAccountTypeBasedRolePermissionMap(Some(AccountTypes.DIRECT_EFFECTIV.id)).filterNot(role => !isSocureUser && riskOsSupportRoles.contains(role._1))
      SystemDefinedRoles.values
        .filter(role => validRolePermissionMap.contains(role.roleType))
        .map(role => AccountConvertors.toUserSysDefinedRoleResult(role, roleDetails))
    }
  }


  def fetchCustomAndSystemRoles(accountId: Long, filterSystemDefinedRoles: Boolean = false):  Future[Seq[UserRoleResult]] = {
    for{
      customRoles <- daoAccountV2.getUserRolesByAccountId(accountId) map { res =>
        val dtoUserRoleSeq = res._1
        val roleIdVsUsersCountMap = res._2
        if (dtoUserRoleSeq.isEmpty) {
          Seq.empty
        } else {
          dtoUserRoleSeq.map(dtoUserRole => AccountConvertors.toUserRoleResult(dtoUserRole, roleIdVsUsersCountMap))
        }
      }
      sysRoles <- getSystemDefinedRoles(accountId, filterSystemDefinedRoles)
    } yield (sysRoles ++ customRoles)
  }

  def fetchSystemDefinedRoles(accountId: Long): Future[Seq[UserRoleResult]] = {
    for {
      roleDetails <- daoAccountV2.getSystemDefinedRolesDetailsByAccountId(accountId)
    } yield SystemDefinedRoles.values.filter(_.roleType != SystemDefinedRoles.CUSTOMROLE.roleType).map(r => AccountConvertors.toUserSysDefinedRoleResult(r, roleDetails))
  }

  def getSystemDefinedRoles(accountId: Long, filterSystemDefinedRoles: Boolean): Future[Seq[UserRoleResult]] = {
    if(filterSystemDefinedRoles){
      daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
        case Some(ah: DtoAccountHierarchy) =>
          if (ah.accountType == AccountTypes.SUB_ACCOUNT.id) {
            val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
            accountIds.headOption match {
              case Some(parentAccountId: Long) =>
                daoUIAccountConfiguration.getUIAccountConfiguration(parentAccountId) flatMap  {
                  case Some(accountUIConfiguration: DtoAccountUIConfiguration) if accountUIConfiguration.hideSystemDefinedRoles =>
                    Future.successful(Seq.empty[UserRoleResult])
                  case _ => fetchSystemDefinedRoles(accountId)
                }
              case _ =>
                logger.error(s"Parent account ID not found in account hierachy of ${accountId}")
                fetchSystemDefinedRoles(accountId)
            }

          } else {
            fetchSystemDefinedRoles(accountId)
          }
        case _ =>
          logger.error(s"Error while fetching account hierarchy for account ID ${accountId}")
          fetchSystemDefinedRoles(accountId)
      }
    } else {
      fetchSystemDefinedRoles(accountId)
    }

  }

  def insertUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]] = {
    (userRoleInput.name.length >= 4 && !SystemDefinedRoles.values.map(sysDefRole=>sysDefRole.name).contains(userRoleInput.name)) match {
      case true =>
        (for {
          _ <- v2Validator.validatePermissions(userRoleInput.creator.accountId, userRoleInput.creator.userId, Set(DashboardUserPermissions.USER_ROLES_CREATE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
          _ <- v2Validator.validateAccountAccess(userRoleInput.accountId, userRoleInput.creator.accountId)
          res <- daoAccountV2.insertUserRole(userRoleInput) map {
            case res if res =>
              Right(res)
            case _ =>
              logger.info("Could not insert user role")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserRole))
          }
        } yield res) recover {
          case ex: ErrorResponseException =>
            logger.info("Could not insert user role", ex)
            Left(ex.errorResponse)
          case e: Exception =>
            logger.info("Could not insert user role", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserRole))
        }
      case false => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)))
    }

  }

  def updateUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]] = {
    (userRoleInput.name.length >= 4 && !SystemDefinedRoles.values.map(sysDefRole=>sysDefRole.name).contains(userRoleInput.name)) match {
      case true =>
        val userRoleId = userRoleInput.id.getOrElse(throw new Exception("id not provided in the payload"))
        (for {
          _ <- v2Validator.validatePermissions(userRoleInput.creator.accountId, userRoleInput.creator.userId, Set(DashboardUserPermissions.USER_ROLES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
          _ <- v2Validator.validateUserRoleAccess(userRoleId, userRoleInput.accountId)
          _ <- v2Validator.validateUserRoleAccess(userRoleId, userRoleInput.creator.accountId)
          res <- daoAccountV2.updateUserRole(userRoleInput) map {
            case res if res =>
              Right(res)
            case _ =>
              logger.info("Could not update user role")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole))
          }
        } yield res) recover {
          case ex: ErrorResponseException =>
            logger.info("Could not update user role", ex)
            Left(ex.errorResponse)
          case e: Exception =>
            logger.info("Could not update user role", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole))
        }
      case false => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RoleNamePolicy)))
    }

  }

  def deleteUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_DELETE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(id, accountId)
      res <- daoAccountV2.deleteUserRole(id) map {
        case res if res > 0 =>
          Right(res)
        case _ =>
          logger.info("Could not delete user role")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole))
      } recover {
        case e: Exception =>
          logger.info("Could not delete user role", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not delete user role", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not delete user role", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole))
    }
  }

  def getRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, RolePermissionTemplateAssociation]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(userRoleId, accountId)
      res <- daoAccountV2.getRolePermissionTemplateAssociationByRoleId(userRoleId) map {
        case Some(dtoRolePermissionTemplateAssociation) =>
          Right(AccountConvertors.toRolePermissionTemplateAssociation(dtoRolePermissionTemplateAssociation, userId, accountId))
        case _ =>
          logger.info("Could not fetch permission template by role id")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissionTemplate))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch permission template by role id", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not fetch permission template by role id", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissionTemplate))
    }
  }

  def insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation: RolePermissionTemplateAssociation): Future[Either[ErrorResponse, Int]] = {
    val dtoRolePermissionTemplateAssociation = AccountConvertors.toDtoRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)
    (for {
      _ <- v2Validator.validatePermissions(rolePermissionTemplateAssociation.accountId, rolePermissionTemplateAssociation.userId, Set(DashboardUserPermissions.USER_ROLES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(rolePermissionTemplateAssociation.userRoleId, rolePermissionTemplateAssociation.accountId)
      _ <- v2Validator.validatePermissionTemplateAccess(rolePermissionTemplateAssociation.permissionTemplateId, rolePermissionTemplateAssociation.accountId)
      res <- daoAccountV2.insertOrUpdateRolePermissionTemplateAssociation(dtoRolePermissionTemplateAssociation) map {
        case res if res > 0 =>
          Right(res)
        case _ =>
          logger.info("Role permission template association already exist")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpsertRolePermissionTemplateAssociation))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not update role permission template association", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not update role permission template association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpsertRolePermissionTemplateAssociation))
    }
  }

  def deleteRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_DELETE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(userRoleId, accountId)
      res <- daoAccountV2.deleteRolePermissionTemplateAssociation(userRoleId) map {
        case res if res > 0 =>
          Right(res)
        case _ =>
          logger.info("Could not delete role permission template association")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteRolePermissionTemplateAssociation))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not delete role permission template association", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not delete role permission template association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteRolePermissionTemplateAssociation))
    }
  }

  def getDashboardPermissionsByRoleId(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccessToAssociateAccount(userRoleId, accountId)
      res <- daoAccountV2.fetchPermissionsByRoleId(userRoleId) map { rows =>
        val result = rows.flatMap(row => {
          val environmentType: Int = row._1
          val permissionsString: String = row._2
          if (!permissionsString.isEmpty) {
            permissionsString
              .split(",")
              .flatMap(permissionId => DashboardUserPermissions.byId(permissionId.trim.toInt).map(permission => DashboardUserPermissions.toPermissionResult(permission, environmentType)))
          } else {
            None
          }
        })
        Right(result.sortBy(p => (p.environmentType, p.id)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch permissions by role id", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not fetch permissions by role id", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissions))
    }
  }

  def getDashboardPermissionsByRoleTypeID(roleType: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      rootAccountType <- v2Validator.getRootParentAccountType(accountId)
      res <- SystemDefiendRolesPermissions.sysDefinedRolesFuture(rootAccountType, roleType) map {permissions =>
        permissions.flatMap(permission => permission._2.map(permissionId => DashboardUserPermissions.toPermissionResult(DashboardUserPermissions.byId(permissionId).get, permission._1))).toSeq
      }
    } yield Right(res)) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch permissions by role Type : $roleType for user ID : $userId", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Could not fetch permissions by role Type : $roleType for user ID : $userId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissions))
    }
  }

  def deleteUserRoleWithPermissionTemplate(roleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.USER_ROLES_DELETE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validateUserRoleAccess(roleId, accountId)
      isRoleAssociated <- daoAccountV2.isUserRoleUnassociated(roleId)
      mappedPermissionTemplateId <- daoAccountV2.getRolePermissionTemplateAssociationByRoleId(roleId).map{
        case Some(perm) => perm.permissionTemplateId
        case None => 0
      }
      isPermissionTemplateUnusedInOtherRoles <- {
        if(mappedPermissionTemplateId > 0) {
          daoAccountV2.getRolePermissionTemplateAssociationByTemplateId(mappedPermissionTemplateId).map(rpta => rpta.filterNot(_.userRoleId == roleId).isEmpty)
        } else Future.successful(false)
      }
      res <- {
        if(!isRoleAssociated) {
          daoAccountV2.deleteUserRoleWithPermissionTemplate(roleId, mappedPermissionTemplateId, isPermissionTemplateUnusedInOtherRoles).map{
            case res if res > 0 =>
              Right(true)
            case _ =>
              logger.info("Could not delete user role")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole))
          }
        } else {
          logger.info("Could not delete role because it is associated to a user")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole)))
        }
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not delete user role", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not delete user role", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole))
    }
  }

  def getUsersAndRolesTotalRecordCount(accountIds: Array[Long], excludeUserStatuses: Option[Set[Int]]): Future[Either[ErrorResponse, Int]] = {
    logger.info(s"Getting users and roles total counts for accounts ${accountIds.mkString(",")}")
    if (accountIds.isEmpty) {
      Future.successful(Left(ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)))
    } else {
      daoAccountV2.getUsersAndRolesTotalRecordCount(accountIds.toSet, excludeUserStatuses) map (totalCount => Right(totalCount))
    }
  }

  def getUsersAndRoles(accountIds: Array[Long], excludeUserStatuses: Option[Set[Int]], start: Option[Int], size: Option[Int]): Future[Either[ErrorResponse, Seq[UsersAndRoles]]] = {
    logger.info(s"Getting users and roles for accounts ${accountIds.mkString(",")} and provided excluded user statuses $excludeUserStatuses")
    if (accountIds.isEmpty) {
      Future.successful(Left(ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)))
    } else {
      daoAccountV2.getUsersAndRoles(accountIds.toSet, excludeUserStatuses, start, size) map (res => Right(res))
    }
  }

}
