package me.socure.account.service

import me.socure.account.service.common.RateLimitCacheKeyProvider
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.util.CacheUtil._
import me.socure.account.validator.RateLimitingValidator
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.RateLimiterPublicAPI.RateLimiterPublicAPI
import me.socure.model.account.{DeleteRateLimitingInput, RateLimiterPublicAPI, SaveRateLimitingInput, UpdateRateLimitingInput}
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}
import me.socure.storage.slick.dao.DaoRateLimit
import me.socure.storage.slick.tables.account.DtoRateLimit
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class RateLimitingService(daoRateLimit: DaoRateLimit, scalaCache: ScalaCache[_])(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val rateLimitPolicyApiName = "/api/1.0/rate-limiting/policies_v2"

  def getRateLimits(accountId: Long, environmentTypeId: Long, api: String): Future[Either[ErrorResponse, Seq[RateLimitingConfig]]] = {
    daoRateLimit.getRateLimits(accountId, environmentTypeId, api) map { rateLimitingConfigs =>
      Right(AccountConvertors.toRateLimitingConfigs(rateLimitingConfigs))
    } recover {
      case e: Exception =>
        logger.info("Could not list rate limits", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotListRateLimits))
    }
  }

  def getRateLimits(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]] = {
    daoRateLimit.getRateLimits(accountId, environmentTypeId, apiIds) map {
      case rateLimitingConfigs if rateLimitingConfigs.nonEmpty =>
        val responseMap = AccountConvertors.toRateLimitingConfigsV2(rateLimitingConfigs)
        Right((apiIds map (id => id -> responseMap.getOrElse(id, Seq.empty[RateLimitingConfig]))).toMap)
      case _ => Right((apiIds map (_ -> Seq.empty[RateLimitingConfig])).toMap)
    } recover {
      case e: Exception =>
        logger.info("Could not list rate limits", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotListRateLimits))
    }
  }

  def getRateLimits: Future[Either[ErrorResponse, Seq[RateLimitingEntry]]] = {
    daoRateLimit.getRateLimits map { result =>
      Right(AccountConvertors.toRateLimitingEntries(dtoRateLimits = result._1, accountIdNames = result._2))
    } recover {
      case e: Exception =>
        logger.info("Could not list rate limits", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotListRateLimits))
    }
  }

  def getRateLimits(accountId: Long): Future[Either[ErrorResponse, Seq[RateLimitingEntry]]] = {
    daoRateLimit.getRateLimits(accountId) map { result =>
      Right(AccountConvertors.toRateLimitingEntries(dtoRateLimits = result._1, accountIdNames = result._2))
    } recover {
      case e: Exception =>
        logger.info(s"Could not list rate limits for given accountid $accountId ", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotListRateLimits))
    }
  }

  def getRateLimitingApis: Future[Seq[RateLimiterPublicAPI]] = {
    Future.successful(RateLimiterPublicAPI.getAll)
  }

  def saveRateLimits(saveRateLimitingInput: SaveRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val dtoRateLimit: DtoRateLimit = AccountConvertors.toDtoRateLimit(saveRateLimitingInput)
    if (RateLimitingValidator.validateRateLimit(dtoRateLimit)) {
      daoRateLimit.saveRateLimit(dtoRateLimit) map {
        case dtoRateLimit if dtoRateLimit.id > 0 =>
          Right(true)
        case _ =>
          logger.info("Could not save rate limits")
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits))
      } recover {
        case e: Exception =>
          logger.info("Error occurred while saving rate limits", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits))
      }
    } else {
      logger.info(s"Validation error while saving rate limit - windowInMillis: ${saveRateLimitingInput.windowInMillis} , limit: ${saveRateLimitingInput.limit}")
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits)))
    }
  }

  def saveRateLimitCollection(saveRateLimitingInputs: Seq[SaveRateLimitingInput], forceValidation: Boolean=true): Future[Either[ErrorResponse, Boolean]] = {
    val dtoRateLimits = saveRateLimitingInputs map AccountConvertors.toDtoRateLimit
    val inputsValid = if(forceValidation) dtoRateLimits map RateLimitingValidator.validateRateLimit else Seq(true)
    if (!inputsValid.contains(false)) {
      daoRateLimit.saveRateLimits(dtoRateLimits) map {
        case Some(saved) if saved > 0 =>
          Right(true)
        case _ =>
          logger.info("Could not save rate limits")
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits))
      } recover {
        case e: Exception =>
          logger.info("Error occurred while saving rate limits", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits))
      }
    } else {
      logger.info(s"Validation error while saving rate limit $saveRateLimitingInputs")
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveRateLimits)))
    }
  }

  def updateRateLimits(updateRateLimitingInput: UpdateRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val dtoRateLimit: DtoRateLimit = AccountConvertors.toDtoRateLimit(updateRateLimitingInput)
    if (RateLimitingValidator.validateRateLimit(dtoRateLimit)) {
      val cacheKeys = RateLimitCacheKeyProvider.provide(updateRateLimitingInput.accountId, updateRateLimitingInput.environmentTypeId, updateRateLimitingInput.api)

      removeCacheKeysWrapped(rateLimitPolicyApiName, scalaCache, Set(cacheKeys))(daoRateLimit updateRateLimit dtoRateLimit) map {
        case res if res > 0 =>
          Right(true)
        case _ =>
          logger.info("Could not update rate limits")
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateRateLimits))
      } recover {
        case e: Exception =>
          logger.info("Error occurred while updating rate limits", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateRateLimits))
      }
    } else {
      logger.info(s"Validation error while updating rate limit - windowInMillis: ${updateRateLimitingInput.windowInMillis} , limit: ${updateRateLimitingInput.limit}")
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateRateLimits)))
    }
  }

  def upsertRateLimits(updateRateLimitingInputs: Seq[UpdateRateLimitingInput]): Future[Either[ErrorResponse, Boolean]] = {
    val dtoRateLimits: Seq[DtoRateLimit] = AccountConvertors.toDtoRateLimits(updateRateLimitingInputs)
    if (RateLimitingValidator.validateRateLimits(dtoRateLimits)) {
      val cacheKeys = updateRateLimitingInputs map (input => RateLimitCacheKeyProvider.provide(input.accountId, input.environmentTypeId, input.api))

      removeCacheKeysWrapped(rateLimitPolicyApiName, scalaCache, cacheKeys.toSet)(daoRateLimit upsertRateLimit dtoRateLimits) map {
        case res if res == dtoRateLimits.size =>
          Right(true)
        case _ =>
          logger.info("Could not upsert rate limits")
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpsertRateLimits))
      } recover {
        case e: Exception =>
          logger.info("Error occurred while updating rate limits", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpsertRateLimits))
      }
    } else {
      logger.info(s"Validation error while upserting rate limit ")
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpsertRateLimits)))
    }
  }

  def deleteRateLimit(deleteRateLimitingInput: DeleteRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val cacheKeys = RateLimitCacheKeyProvider.provide(deleteRateLimitingInput.accountId, deleteRateLimitingInput.environmentTypeId, deleteRateLimitingInput.api)

    removeCacheKeysWrapped(rateLimitPolicyApiName, scalaCache, Set(cacheKeys))(daoRateLimit
      deleteRateLimit(deleteRateLimitingInput.accountId, deleteRateLimitingInput.environmentTypeId, deleteRateLimitingInput.api, deleteRateLimitingInput.windowInMillis)) map {
      case true => Right(true)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotDeleteRateLimits))
    }
  }

  def deleteRateLimits(deleteRateLimitingInputs: Seq[DeleteRateLimitingInput]): Future[Either[ErrorResponse, Boolean]] = {
    val dtoRateLimits: Seq[DtoRateLimit] = AccountConvertors.toDtoRateLimitsFromDeleteInput(deleteRateLimitingInputs)

    daoRateLimit.doesAllRowsExists(dtoRateLimits) flatMap {
      case true =>
        val cacheKeys = deleteRateLimitingInputs map (input => RateLimitCacheKeyProvider.provide(input.accountId, input.environmentTypeId, input.api))
        removeCacheKeysWrapped(rateLimitPolicyApiName, scalaCache, cacheKeys.toSet)(daoRateLimit deleteRateLimits dtoRateLimits) map {
          case true => Right(true)
          case _ =>
            logger.info("Could not delete rate limits")
            Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotDeleteRateLimits))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotDeleteRateLimits)))
    }
  }
}
