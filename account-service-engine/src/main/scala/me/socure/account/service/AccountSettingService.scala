package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ExceptionCodes.{ExceptionCodes, _}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.configuration.ApiKeyRenewalConfig
import me.socure.constants.{KeyProviders, Resources}
import me.socure.convertors.AccountConvertors
import me.socure.dashboard.NewAccountSettings
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.{Creator, EnvironmentNameAndId}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}


/**
  * Created by gopal on 03/05/16.
  */
class AccountSettingService(daoAccount : DaoAccount, apiKeyRenewalConfig: ApiKeyRenewalConfig, v2Validator: V2Validator, clock: Clock)(implicit ec : ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  private def optToFuture[A](opt: Option[(A)]): Future[A] = {
    opt match{
      case Some(a) => Future.successful(a)
      case _ => Future.failed(throw new Exception("Empty option conversion failure"))
    }
  }

  def getNewAccountSetting(accountId : Long) : Future[Either[ErrorResponse, NewAccountSettings]] = {
    val renewalTimeInHours = apiKeyRenewalConfig.renewalTimeInHours
    daoAccount.getAccount(accountId) flatMap  {
      case Some(account) =>

        val settings = for {
          environmentDetails <- daoAccount.getProductionEnvironmentWithApiKeysForAccount(accountId)
          environmentObj <- optToFuture(environmentDetails.keys.headOption)
          socialKeys <- daoAccount.getSocialAppKeys(environmentObj.id)
          invidiualCache <- daoAccount.getEnvironmentCacheIndividual(environmentObj.id)
          accountCache <- daoAccount.getAccountCache(environmentObj.id)
        } yield (account,
                 environmentObj,
                 environmentDetails.values.headOption.getOrElse(throw new Exception("Failed to retrieve any APIKey value")),
                 socialKeys,
                 invidiualCache,
                 accountCache)

        settings map {
          case (ff, environment, apiKeys, keys, invidiualCache, accountcache) =>
            Right(
              NewAccountSettings(
                ff.accountId,
                account.name,
                List[NewEnvironment] {
                  AccountConvertors.getSettingsEnvironment(
                    ff,
                    environment,
                    apiKeys,
                    keys,
                    invidiualCache,
                    accountcache,
                    renewalTimeInHours,
                    clock
                  )
                }))
          case _ => Left(ErrorResponseFactory.get(UnknownError))
        }

      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }
  def updateDomain(accoutId : Long, domains: List[String]) : Future[Either[ErrorResponse, String]] = {

    daoAccount.updateDomain(accoutId, domains) map { count =>
      getUpsertStatus(count, "Domain updated Successfully", ExceptionCodes.DomainUpdateFailed)
    }
  }

  def deleteInvidiualCache(invidiualCacheId : Long) : Future[Either[ErrorResponse, String]] = {
    daoAccount.removeAccountInvidiualCache(invidiualCacheId) map {
      case count if count > 0 => Right("Invidual Cache removed successfully")
      case count if count == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.IndividualCacheIdNotFound))
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.DeleteIndividualCacheFaild))
    }
  }

  def deleteInvidiualCache(accountId : Long, identifier : String) : Future[Either[ErrorResponse, String]] = {
    daoAccount.getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        daoAccount.removeAccountInvidiualCache(env.id, identifier) map {
          case count if count > 0 => Right("Invidual Cache removed successfully")
          case count if count == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.IndividualCacheIdNotFound))
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.DeleteIndividualCacheFaild))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def deleteAccountCache(accountId : Long) : Future[Either[ErrorResponse, String]] = {
    daoAccount.getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        daoAccount.removeEnvironmentCache(env.id) map { count =>
          getUpsertStatus(count, "Account Cache removed successfully", ExceptionCodes.DeleteAccountCacheFailed)
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def upsertAccountCache(accountCache : AccountOverallCache) : Future[Either[ErrorResponse, String]] = {
    daoAccount.upsert(AccountConvertors.getDtoAccountCache(accountCache), accountCache.accountId) map { count =>
      getUpsertStatus(count, "Account Cache update successful", ExceptionCodes.UpdateAccountCacheFailed)
    }
  }

  def upsertIndividualCache(accountIndividualCache: AccountIndividualCache) : Future[Either[ErrorResponse, String]] = {
    daoAccount.upsert(AccountConvertors.getDtoInvdividualCache(accountIndividualCache), accountIndividualCache.accountId) map { count =>
      getUpsertStatus(count, "Account Invidiual Cache update successful", ExceptionCodes.UpdateAccountIndividualCacheFailed)
    }
  }

  def removeSocialNetworkkeys(id : Long) = {
    daoAccount.removeAppNetworkKeys(id) map {
      case count if count > 0 => Right("social network keys removed successfully")
      case count if count == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.SocialKeyIdNotFound))
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.DeleteSocialNetworkKeyFailed))
    }
  }

  def removeSocialNetworkkeys(accountId : Long, provider : String) = {
    daoAccount.getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        daoAccount.removeEnvironmentAppNetworkKeys(accountId, KeyProviders.withName(provider).id) map {
          case count if count > 0 => Right("social network keys removed successfully")
          case count if count == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.SocialKeyIdNotFound))
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.DeleteSocialNetworkKeyFailed))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  def upsertSocialNetworkKey(socialNetworkAppKeys: SocialNetworkAppKeys) = {
    daoAccount.upsert(AccountConvertors.getDtoAccountKeys(socialNetworkAppKeys), socialNetworkAppKeys.accountId) map {
      case count if count > 0 => Right("social network keys added/updated successfully")
      case count if count == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      case _ =>  Left(ErrorResponseFactory.get(ExceptionCodes.UpdateSocialNetworkKeyFailed))
    }
  }

  def getAllEnvironmentWithAccountDetails : Future[Either[ErrorResponse, Vector[EnvironmentWithAccount]]] = {
    daoAccount.getAllEnvironmentWithAccount map (Right(_))
  }

  def getEnvironmentByEmail(email : String) : Future[Either[ErrorResponse, Vector[EnvironmentNameAndId]]] = {
    daoAccount.getEnvironmentInfo(email) map {
      case list if list.nonEmpty => Right(list)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  private def getUpsertStatus(result : Int, successCode : String, errorCode : ExceptionCodes) : Either[ErrorResponse, String] = {
    result match {
      case c if c > 0 => Right(successCode)
      case c if c == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      case _ => Left(ErrorResponseFactory.get(errorCode))
    }
  }

  def getModulesByAccountId(accountId: Long, creatorOpt: Option[Creator],forceValidate: Boolean): Future[Either[ErrorResponse, Seq[BusinessUserRolesLess]]] = {

    val modules = BusinessUserRoles.getRolesByResource(Resources.IDPLUS)

    def getModules(): Future[Either[ErrorResponse, Seq[BusinessUserRolesLess]]] = {
      daoAccount.getAccountPermissions(accountId) map { dtoAccountPermissionSeq =>
        val permissions = dtoAccountPermissionSeq.map(_.permission)
        val dashboardModules = modules.filter(module => permissions.contains(module.id))
          .map(module => BusinessUserRolesLess(id = module.id, label = module.label)).toSeq.sortBy(_.id)
        Right(dashboardModules)
      }
    }

    forceValidate match {
      case false => getModules()
      case true =>

        creatorOpt match {
          case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CreatorDetailsNotAvailable)))
          case Some(creator) =>

            v2Validator.isValidV2AccountRequest(accountId, Some(creator)) flatMap {
              case false => if (accountId == creator.accountId) {
                getModules()
              } else {
                (for {
                  _ <- v2Validator.isValidSubAccount(creator.accountId, Set(accountId))
                  res <- getModules()
                } yield res) recover {
                  case ere: ErrorResponseException =>
                    logger.info(s"Error occurred while fetching modules for account $accountId", ere)
                    Left(ere.errorResponse)
                  case e: Exception =>
                    logger.info(s"Failed to fetch permissions for account id $accountId", e)
                    Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
                }
              }
              case true =>
                (for {
                  _ <- v2Validator.validateUserAccountAssociation(creator.userId, creator.accountId)
                  _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
                  res <- {
                    daoAccount.getAccountPermissions(accountId) map { dtoAccountPermissionSeq =>
                      val permissions = dtoAccountPermissionSeq.map(_.permission)
                      val dashboardModules = modules.filter(module => permissions.contains(module.id))
                        .map(module => BusinessUserRolesLess(id = module.id, label = module.label)).toSeq.sortBy(_.id)
                      Right(dashboardModules)
                    }
                  }
                } yield res) recover {
                  case ere: ErrorResponseException =>
                    logger.info(s"Error occurred while fetching modules for account $accountId", ere)
                    Left(ere.errorResponse)
                  case e: Exception =>
                    logger.info(s"Failed to fetch permissions for account id $accountId", e)
                    Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
                }
            }
        }
    }
  }
}
