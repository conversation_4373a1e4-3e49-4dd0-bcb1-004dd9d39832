package me.socure.account.service

import javax.crypto.SecretKey

import me.socure.crypto.decryptor.key.StaticKeyBasedDecryptorFactory
import me.socure.crypto.encryptor.key.StaticKeyBasedEncryptorFactory

import scala.concurrent.ExecutionContext


object EncryptionDataKeysServiceFactory {

  def get(secretKey: SecretKey, keyId: String)(implicit ec: ExecutionContext): EncryptionDataKeysServiceImpl = {
    val encryptor = StaticKeyBasedEncryptorFactory.createAesGcmNoPadding(
      secretKey = secretKey,
      keyId = keyId
    )
    val decryptor = StaticKeyBasedDecryptorFactory.createAesGcmNoPadding(
      secretKey = secretKey,
      keyId = keyId
    )
    new EncryptionDataKeysServiceImpl(
      keyBasedEncryptor = encryptor,
      keyBasedDecryptor = decryptor
    )
  }
}
