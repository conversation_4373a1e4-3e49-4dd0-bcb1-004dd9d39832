package me.socure.account.service

import com.amazonaws.services.kms.model.{DecryptRequest, EncryptRequest, GenerateDataKeyPairWithoutPlaintextRequest}
import me.socure.account.service.common.AccountPayloadKeysConstant._
import me.socure.account.service.common.{AccountPayloadCacheKeyProvider, PayloadKeysAlgo}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.Clock
import me.socure.common.kms.KmsService
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountPayloadKey, MergePayloadKeysRequest}
import me.socure.model.encryption.KmsIdsConfig
import me.socure.storage.slick.dao.DaoAccountPayloadKeys
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.util.Base64

import com.amazonaws.regions.Regions

import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

/**
  * Created by abhishek on 11/15/2021.
  */
class AccountPayloadKeysService(daoService : DaoAccountPayloadKeys,
                                kmsServices: Map[Regions, KmsService],
                                kmsKey: KmsIdsConfig,
                                multiRegionKmsIdsConfig: KmsIdsConfig,
                                scalaCache: ScalaCache[_],
                                clock: Clock)(implicit ec : ExecutionContext) {

  import AccountPayloadKeysService._

  def getPayloadKeys(accountId: Option[Long]): Future[Either[ErrorResponse, Seq[AccountPayloadKey]]] = {
    logger.info("Getting active account payload keys list")
    val getKeysFuture = accountId match {
      case Some(accId) => daoService.getKeysforAccount(accId)
      case None => daoService.getAllPayloadKeys
    }
    getKeysFuture.flatMap {
      case list if list.nonEmpty => {
        val decryptFutures = list.map(pl => {
          decryptPayloadKeys(pl.privateKey, pl.wrappingKeyRef, pl.accountId, pl.environmentTypeId).map(dk => {
            AccountConvertors.toAccountPayloadKey(pl, dk)
          })
        })
        Future.sequence(decryptFutures)
      }.map(seq => Right(seq)
      ).recover {
        case e: Throwable =>
          logger.info(s"Get payload keys error: ${e.getClass.getName} ${e.getMessage}")
          Left(ErrorResponseFactory.get(PayloadKeysDecryptionError))
      }

      case _ => Future.successful(Left(ErrorResponseFactory.get(RecordsNotFound)))
    }
  }

  @deprecated("deprecated in favour of createPayloadKeysV2")
  def createPayloadKeys(accountId: Long, environmentId: Long, keySize: Long, customerPublicKey: String, isRotation: Boolean): Future[Either[ErrorResponse, String]] = {
    logger.info(s"Creating new account payload keys for accountId: $accountId and environmentId: $environmentId")
    val keyId = kmsKey.value.headOption.map(_._2.value).getOrElse(throw new Exception("Invalid state of KMS configuration"))
    val algo = if(keySize == RSA_4096_SIZE) PayloadKeysAlgo.RSA_4096 else PayloadKeysAlgo.RSA_2048
    val generateDataKeyPairWithoutPlaintextRequest = new GenerateDataKeyPairWithoutPlaintextRequest()
                                      .withKeyId(keyId)
                                      .withKeyPairSpec(algo)
    val kmsService = kmsServices(Regions.US_EAST_1)
    val result = kmsService.generateDataKeyPairWithoutPlaintext(generateDataKeyPairWithoutPlaintextRequest)

    result.flatMap(r => {
      daoService.createKeys(AccountConvertors
        .getAccountPayloadKeysDto(
          accountId,
          environmentId,
          customerPublicKey,
          keyId,
          Base64.getEncoder.encodeToString(r.getPublicKey.array()),
          Base64.getEncoder.encodeToString(r.getPrivateKeyCiphertextBlob.array()),
          clock
        ), isRotation, clock).map {
        case Left(_) =>
          logger.info(s"Error while saving payload keys for accountId: $accountId and environmentId: $environmentId")
          Left(ErrorResponseFactory.get(CreatePayloadKeysError))

        case Right(key) =>
          // Invalidating to ensure cache contains all possible active payload keys (including newly created) for given account
          invalidateCache(accountId)
          Right(key.publicKey)
      }
    }).recover {
      case e: Throwable =>
        logger.info(s"Error while generating payload keys for accountId: $accountId and environmentId: $environmentId, ${e.getClass.getName} ${e.getMessage}")
        Left(ErrorResponseFactory.get(CreatePayloadKeysError))
    }
  }

  def createPayloadKeysV2(accountId: Long, environmentId: Long, keySize: Long, customerPublicKey: String, isRotation: Boolean): Future[Either[ErrorResponse, String]] = {
    logger.info(s"Creating new account payload keys for accountId: $accountId and environmentId: $environmentId")
    val keyIdWithArn = getMultiRegionKmsKeys.headOption.getOrElse(throw new Exception("Invalid state of KMS configuration"))
    val algo = if(keySize == RSA_4096_SIZE) PayloadKeysAlgo.RSA_4096 else PayloadKeysAlgo.RSA_2048
    val generateDataKeyPairWithoutPlaintextRequest = new GenerateDataKeyPairWithoutPlaintextRequest()
      .withKeyId(keyIdWithArn)
      .withKeyPairSpec(algo)
    val keyId = getKeyIdOrAliasFromKey(keyIdWithArn)
    val region = getRegionFromWrappingKey(keyIdWithArn)
    val kmsService = kmsServices(region)
    val result = kmsService.generateDataKeyPairWithoutPlaintext(generateDataKeyPairWithoutPlaintextRequest)

    result.flatMap(r => {
      daoService.createKeys(AccountConvertors
        .getAccountPayloadKeysDto(
          accountId,
          environmentId,
          customerPublicKey,
          keyId,
          Base64.getEncoder.encodeToString(r.getPublicKey.array()),
          Base64.getEncoder.encodeToString(r.getPrivateKeyCiphertextBlob.array()),
          clock
        ), isRotation, clock).map {
        case Left(_) =>
          logger.info(s"Error while saving payload keys for accountId: $accountId and environmentId: $environmentId")
          Left(ErrorResponseFactory.get(CreatePayloadKeysError))

        case Right(key) =>
          // Invalidating to ensure cache contains all possible active payload keys (including newly created) for given account
          invalidateCache(accountId)
          Right(key.publicKey)
      }
    }).recover {
      case e: Throwable =>
        logger.info(s"Error while generating payload keys for accountId: $accountId and environmentId: $environmentId, ${e.getClass.getName} ${e.getMessage}")
        Left(ErrorResponseFactory.get(CreatePayloadKeysError))
    }
  }

  /*
    Clones the existing latest active record by re-encrypting the private key using multiregion kms keys.
    If the existing active key is already using multiregion kms keys, no database changes happen.
    Even if we had to clone, no changes to the last active record, only new record would be inserted.
   */
  def clonePayloadKeys(accountId: Long, environmentTypeId: Long): Future[Either[ErrorResponse, String]] = {
    //Fetching active keys for given accountId and environmentTypeId
    daoService.getKeysforAccountAndEnvironment(accountId, environmentTypeId).flatMap { payloadKeys =>
      if (payloadKeys.isEmpty) {
        //No active keys found for the given account and environment.
        //Hence nothing to clone
        Future(Left(ErrorResponseFactory.get(NoActivePayloadKeysForCloningError)))
      } else {
        if (payloadKeys.exists(payloadKey => isMultiRegionKmsKey(payloadKey.wrappingKeyRef))) {
          //Already MultiRegionKeys found in one of the active keys. No need for cloning again.
          Future(Left(ErrorResponseFactory.get(CloningPayloadKeysNotRequiredError)))
        } else {
          val latestActivePayloadKey = payloadKeys.head
          val multiRegionWrappingKey = getMultiRegionKmsKeys.head.split("/")(1) //key/alias without fullARN
          val result = for {
            decryptedPrivateKey <- decryptPayloadKeys(
              latestActivePayloadKey.privateKey,
              latestActivePayloadKey.wrappingKeyRef,
              accountId,
              environmentTypeId
            )
            encryptedPrivateKey <- encryptPayloadKeys(
              decryptedPrivateKey,
              latestActivePayloadKey.wrappingKeyRef,
              accountId,
              environmentTypeId,
              useMultiRegionKmsKeys = true
            )
            result <- daoService.insertPayloadKeys(
              latestActivePayloadKey.copy(
                id = 0,
                privateKey = encryptedPrivateKey,
                wrappingKeyRef = multiRegionWrappingKey,
                createdAt = clock.now(),
                updatedAt = clock.now()
              )
            )
          } yield result
          result.map {
            case Left(error) => Left(error)
            case Right(dtoAccountPayloadKeys) =>
              // Invalidating to ensure cache contains all possible active payload keys (including newly created) for given account
              invalidateCache(accountId)
              Right(dtoAccountPayloadKeys.publicKey)
          }.recover {
            case exception: Exception =>
              logger.error(s"Exception while cloning payload keys for accountId $accountId and envTypeId $environmentTypeId", exception)
              Left(ErrorResponseFactory.get(CloningPayloadKeysError))
          }
        }
      }
    }.recover {
      case exception: Exception =>
        logger.error(s"Exception while cloning payload keys for accountId $accountId and envTypeId $environmentTypeId", exception)
        Left(ErrorResponseFactory.get(CloningPayloadKeysError))
    }
  }

  def mergePayloadKeys(mergePayloadKeysRequest: MergePayloadKeysRequest): Future[Either[ErrorResponse, Seq[AccountPayloadKey]]] = {
    daoService.mergePayloadKeys(mergePayloadKeysRequest.accountId, mergePayloadKeysRequest.environmentId, mergePayloadKeysRequest.accountEnvList, clock).flatMap {
      case Left(e) =>
        logger.info("Error while merging payload keys")
        Future.successful(Left(e))

      case Right(payloadKeys) => {
        invalidateCache(mergePayloadKeysRequest.accountId)
        val decryptFutures = payloadKeys.map(pl => {
          decryptPayloadKeys(pl.privateKey, pl.wrappingKeyRef, pl.accountId, pl.environmentTypeId).map(dk => {
            AccountConvertors.toAccountPayloadKey(pl, dk)
          })
        })
        Future.sequence(decryptFutures)
      }.map(seq => Right(seq)
      ).recover {
        case e: Throwable =>
          logger.info(s"Merge payload keys error: ${e.getClass.getName} ${e.getMessage}")
          Left(ErrorResponseFactory.get(PayloadKeysDecryptionError))
      }

    }
  }

  @deprecated("deprecated in favour of updatePayloadKeysV2")
  def updatePayloadKeys(accountId: Long,
                        environmentId: Long,
                        customerPublicKey: Option[String],
                        isSocureKeysUpdate: Boolean,
                        keySize: Long): Future[Either[ErrorResponse, AccountPayloadKey]] = {
    val updateFuture = if(isSocureKeysUpdate) {
      val kmsService = kmsServices(Regions.US_EAST_1)
      //Get symmetric key value from payload encryption config map(region -> kms key) which will be used to generate payload key pair
      val keyId = kmsKey.value.headOption.map(_._2.value).getOrElse(throw new Exception("Invalid state of KMS configuration"))
      val algo = if(keySize == RSA_4096_SIZE) PayloadKeysAlgo.RSA_4096 else PayloadKeysAlgo.RSA_2048
      val generateDataKeyPairWithoutPlaintextRequest = new GenerateDataKeyPairWithoutPlaintextRequest()
        .withKeyId(keyId)
        .withKeyPairSpec(algo)
      val result = kmsService.generateDataKeyPairWithoutPlaintext(generateDataKeyPairWithoutPlaintextRequest)
      result.flatMap(r => {
        val publicKey = Base64.getEncoder.encodeToString(r.getPublicKey.array())
        val privateKey = Base64.getEncoder.encodeToString(r.getPrivateKeyCiphertextBlob.array())
        daoService.updatePayloadKeys(accountId, environmentId, customerPublicKey, Some(publicKey), Some(privateKey), clock)
      }).recover {
        case e: Throwable =>
          logger.info(s"Error while updating payload keys for accountId: $accountId and environmentId: $environmentId, ${e.getClass.getName} ${e.getMessage}")
          Left(ErrorResponseFactory.get(UpdatePayloadKeysError))
      }
    } else {
      if(customerPublicKey.isDefined) {
        daoService.updatePayloadKeys(accountId, environmentId, customerPublicKey, None, None, clock)
      } else {
        Future.successful(Left(ErrorResponseFactory.get(CustomerPublicKeyNotDefinedError)))
      }
    }

    updateFuture.flatMap {
      case Right(payloadKey) =>
        // Invalidating to ensure cache gets updated values of payload keys for given account
        invalidateCache(accountId)
        decryptPayloadKeys(payloadKey.privateKey, payloadKey.wrappingKeyRef, payloadKey.accountId, payloadKey.environmentTypeId).map(dk =>
          Right(AccountConvertors.toAccountPayloadKey(payloadKey, dk))
        ).recover {
          case e: Throwable =>
            logger.info(s"Error while decrypting payload key for accountId $accountId and environmentId $environmentId: ${e.getClass.getName} ${e.getMessage}")
            Left(ErrorResponseFactory.get(PayloadKeysDecryptionError))
        }
      case _ =>
        logger.info(s"Error while updating payload keys for accountId: $accountId and environmentId: $environmentId")
        Future.successful(Left(ErrorResponseFactory.get(UpdatePayloadKeysError)))
    }
  }


  def updatePayloadKeysV2(accountId: Long,
                        environmentId: Long,
                        customerPublicKey: Option[String],
                        isSocureKeysUpdate: Boolean,
                        keySize: Long): Future[Either[ErrorResponse, AccountPayloadKey]] = {
    val updateFuture = if(isSocureKeysUpdate) {
      val keyIdWithArn = getMultiRegionKmsKeys.headOption.getOrElse(throw new Exception("Invalid state of KMS configuration"))
      val keyId = getKeyIdOrAliasFromKey(keyIdWithArn)
      val region = getRegionFromWrappingKey(keyIdWithArn)
      val kmsService = kmsServices(region)
      val algo = if(keySize == RSA_4096_SIZE) PayloadKeysAlgo.RSA_4096 else PayloadKeysAlgo.RSA_2048
      val generateDataKeyPairWithoutPlaintextRequest = new GenerateDataKeyPairWithoutPlaintextRequest()
        .withKeyId(keyIdWithArn)
        .withKeyPairSpec(algo)
      val result = kmsService.generateDataKeyPairWithoutPlaintext(generateDataKeyPairWithoutPlaintextRequest)
      result.flatMap(r => {
        val publicKey = Base64.getEncoder.encodeToString(r.getPublicKey.array())
        val privateKey = Base64.getEncoder.encodeToString(r.getPrivateKeyCiphertextBlob.array())
        daoService.updatePayloadKeysV2(accountId, environmentId, customerPublicKey, Some(publicKey), Some(privateKey), Some(keyId), clock)
      }).recover {
        case e: Throwable =>
          logger.info(s"Error while updating payload keys for accountId: $accountId and environmentId: $environmentId, ${e.getClass.getName} ${e.getMessage}")
          Left(ErrorResponseFactory.get(UpdatePayloadKeysError))
      }
    } else {
      if(customerPublicKey.isDefined) {
        daoService.updatePayloadKeysV2(accountId, environmentId, customerPublicKey, None, None, None, clock)
      } else {
        Future.successful(Left(ErrorResponseFactory.get(CustomerPublicKeyNotDefinedError)))
      }
    }

    updateFuture.flatMap {
      case Right(payloadKey) =>
        // Invalidating to ensure cache gets updated values of payload keys for given account
        invalidateCache(accountId)
        decryptPayloadKeys(payloadKey.privateKey, payloadKey.wrappingKeyRef, payloadKey.accountId, payloadKey.environmentTypeId).map(dk =>
          Right(AccountConvertors.toAccountPayloadKey(payloadKey, dk))
        ).recover {
          case e: Throwable =>
            logger.info(s"Error while decrypting payload key for accountId $accountId and environmentId $environmentId: ${e.getClass.getName} ${e.getMessage}")
            Left(ErrorResponseFactory.get(PayloadKeysDecryptionError))
        }
      case _ =>
        logger.info(s"Error while updating payload keys for accountId: $accountId and environmentId: $environmentId")
        Future.successful(Left(ErrorResponseFactory.get(UpdatePayloadKeysError)))
    }
  }

  //Deactivates all active payload keys for given accountId and environmentId
  def deletePayloadKeys(accountId: Long, environmentId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoService.deactivateKey(accountId, environmentId, clock).map {
      case i =>
        // Invalidating to ensure cache is refreshed for deleted payload keys
        invalidateCache(accountId)
        Right(i == 1)
      case _ =>
        logger.info(s"Error while deleting payload keys for accountId: $accountId and environmentId: $environmentId")
        Left(ErrorResponseFactory.get(DeletePayloadKeysError))
    }
  }

  //AccountId and EnvironmentId are being used here only for logging
  def encryptPayloadKeys(plaintextKey : String, wrappingKeyRef: String, accountId: Long, environmentId: Long, useMultiRegionKmsKeys: Boolean) : Future[String] = {

    def encryptHelper(keys: Seq[String]): Future[String] = {
      if (keys.isEmpty) {
        logger.error(s"Payload key encryption failed with all possible keys for accountId $accountId environmentId $environmentId with wrapping key $wrappingKeyRef")
        Future.failed(new Exception(PayloadKeysDecryptionError.description))
      } else {
        val currentKey = keys.head
        val region = getRegionFromWrappingKey(currentKey)
        val kmsService = kmsServices(region)

        val encryptReq = new EncryptRequest()
                            .withKeyId(currentKey)
                            .withPlaintext(ByteBuffer.wrap(Base64.getDecoder.decode(plaintextKey.getBytes(StandardCharsets.UTF_8))))

        kmsService.encrypt(encryptReq).map(r => Base64.getEncoder.encodeToString(r.getCiphertextBlob.array()))
          .recoverWith {
            case NonFatal(ex) =>
              if (keys.tail.nonEmpty)
                logger.warn(s"Exception while encrypting payload key for accountId $accountId environmentId $environmentId with wrapping key $currentKey", ex)
              else
                logger.error(s"Exception while encrypting payload key for accountId $accountId environmentId $environmentId with wrapping key $currentKey", ex)
              encryptHelper(keys.tail)
          }
      }
    }
    if (useMultiRegionKmsKeys) encryptHelper(getMultiRegionKmsKeys)
    else encryptHelper(Seq(wrappingKeyRef))
  }

  //AccountId and EnvironmentId are being used here only for logging
  def decryptPayloadKeys(encryptedKey : String, wrappingKeyRef: String, accountId: Long, environmentId: Long) : Future[String] = {

    def decryptHelper(keys: Seq[String]): Future[String] = {
      if (keys.isEmpty) {
        logger.error(s"Payload key decryption failed with all possible keys for accountId $accountId environmentId $environmentId with wrapping key $wrappingKeyRef")
        Future.failed(new Exception(PayloadKeysDecryptionError.description))
      } else {
        val currentKey = keys.head
        val region = getRegionFromWrappingKey(currentKey)
        val kmsService = kmsServices(region)

        val decryptReq = new DecryptRequest().withKeyId(currentKey)
          .withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(encryptedKey.getBytes(StandardCharsets.UTF_8))))

        kmsService.decrypt(decryptReq).map(r => Base64.getEncoder.encodeToString(r.getPlaintext.array()))
          .recoverWith {
            case NonFatal(ex) =>
              if (keys.tail.nonEmpty)
                logger.warn(s"Exception while decrypting payload key for accountId $accountId environmentId $environmentId with wrapping key $currentKey", ex)
              else
                logger.error(s"Exception while decrypting payload key for accountId $accountId environmentId $environmentId with wrapping key $currentKey", ex)
              decryptHelper(keys.tail)
          }
      }
    }

    decryptHelper(getResolvedWrappingKeys(wrappingKeyRef))
  }

  private def invalidateCache(accountId: Long): Future[Unit] = {
    val cacheKey = AccountPayloadCacheKeyProvider.provide(accountId)
    scalaCache.cache.remove(cacheKey)
  }

  private def isMultiRegionKmsKey(wrappingKey: String): Boolean =
    multiRegionKmsIdsConfig.value.values.exists(kmsId => kmsId.value.endsWith(wrappingKey))

  private def getMultiRegionKmsKeys: Seq[String] =
    multiRegionKmsIdsConfig.value.values.map(kmsId => kmsId.value).toSeq.sorted

  private def getResolvedWrappingKeys(wrappingKey: String): Seq[String] = {
    if (isMultiRegionKmsKey(wrappingKey)) {
      getMultiRegionKmsKeys
    } else Seq(wrappingKey)
  }
}

object AccountPayloadKeysService {

  private val logger : Logger = LoggerFactory.getLogger(getClass)
  val kmsArnPrefix = "arn:aws" //prefix applies to both commercial and govcloud
  val kmsArnRegionIndex = 3
  val kmsArnDelimiter = ":"

  /*
    Fetching region name from the wrapping key which may get persisted in one of the below formats,
      1. keyId (UUID) alone  Ex - 086a5d39-acdb-4c19-b5bf-a172428b6424
      2. Full ARN alias Ex - arn:aws:kms:us-east-1:************:alias/socure-account-service-payload-encryption-stage
      3. Full ARN key Ex - arn:aws:kms:us-east-1:************:key/086a5d39-acdb-4c19-b5bf-a172428b6424
   */
  def getRegionFromWrappingKey(key: String): Regions = {
    if (key.startsWith(kmsArnPrefix)) {
      val parts = key.split(kmsArnDelimiter)
      if (parts.size > kmsArnRegionIndex)
        Regions.fromName(parts(kmsArnRegionIndex))
      else
        Regions.US_EAST_1
    } else
      Regions.US_EAST_1
  }

  /*
    This method returns the keyId or alias from the given kms key which can be of any formats like below
      1. keyId (UUID) alone  Ex - 086a5d39-acdb-4c19-b5bf-a172428b6424
      2. Full ARN alias Ex - arn:aws:kms:us-east-1:************:alias/socure-account-service-payload-encryption-stage
      3. Full ARN key Ex - arn:aws:kms:us-east-1:************:key/086a5d39-acdb-4c19-b5bf-a172428b6424
   */
  def getKeyIdOrAliasFromKey(key: String): String = {
    if (key.startsWith(kmsArnPrefix)) {
      key.split(":").last.replace("alias/", "").replace("key/", "")
    } else key
  }

}