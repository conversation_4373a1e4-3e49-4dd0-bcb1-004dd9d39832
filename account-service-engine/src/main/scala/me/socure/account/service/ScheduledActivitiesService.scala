package me.socure.account.service

import me.socure.account.superadmin.InactiveUserService
import me.socure.common.clock.{Clock, RealClock}
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.configuration.ScheduledActivityConfig
import me.socure.constants.UserMagicTokenConstants
import me.socure.mail.service.MailNotificationService
import me.socure.model.ErrorResponse
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.user.MagicLinkAuditService
import org.joda.time.{DateTime, Days}
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by ma<PERSON><PERSON><PERSON> on 03/04/2017.
  */
class ScheduledActivitiesService(daoBusiness: DaoBusinessUser,
                                 clock: Clock,
                                 config : ScheduledActivityConfig,
                                 passwordService : PasswordService,
                                 magicLinkAuditService: MagicLinkAuditService,
                                 inactiveUserService: InactiveUserService)(implicit ec : ExecutionContext) extends RealClock {

  private val metrics = JavaMetricsFactory.get("accountservice." + this.getClass.getSimpleName)
  val logger = LoggerFactory.getLogger(getClass)
  def lockInactiveBusinessUser : Future[Either[ErrorResponse, String]] = {
    daoBusiness.lockInactiveBusinessUser(clock.now(), config.minInactiveDays).map {_ =>
      Right("Task is successfully completed")
    }
  }

  /**
    * It locks primary business user who has not logged in for more than 20 days after the date of registration
    * @return
    */
  def lockPrimaryUserNotLoggedIn : Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.lockBUNotLoggedInDays(clock.now(), config.registrationWaitDays).map(_ => Right(true))
  }

  def invalidateActivationCode : Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.getExpiredActivationCodes(clock.now(), config.activationCodeTTL) flatMap { ids =>
      if(ids.nonEmpty)
        daoBusiness.invalidateActivationCodes(ids.toSet).map(_ => Right(true))
      else
        Future.successful(Right(true))
    }
  }

  def invalidateOldPasswordResetTokens: Future[Either[ErrorResponse, Boolean]] = {
    daoBusiness.getExpiredPasswordTokens(clock.now(), config.passwordResetTokenTTL) flatMap { ids =>
      if(ids.nonEmpty)
        daoBusiness.invalidatePasswordResetTokenByTimeToLive(ids.toSet).map(_ => Right(true))
      else
        Future.successful(Right(true))
    }
  }

  private def handlePasswordByAge(seqPasswordDetails: Seq[(DateTime, Long, String)],
                                  currentDate: DateTime) = {
    val seq = seqPasswordDetails.map{
      case(ud, bu, e) => {
        val days = config.passwordExpiryDays-Days.daysBetween(ud.toLocalDate, currentDate.toLocalDate).getDays
        days match {
          case 10 | 5 | 1 => inactiveUserService.getPasswordResetLinkAsActivationDetails(List(e)).map {
              case Right(activationDetails) =>
                activationDetails.find(a => a.email == e) match {
                  case Some(activationDetail) =>
                    logger.info(s"Sending password reset notification to $e")
                    Future.successful(None)
                    Future.successful(Right(true))
                  case _ => logger.info(s"User with Email id not found")
                }
              case Left(e)=> logger.info(s"Error occurred while generating the activation code$e")
            }
          case 0 => {
            logger.info(s"Invalidating password for $e")
            passwordService.invalidatePassword(bu)
          }
          case _ => {
            logger.info(s"No action for $e :days left are $days and password updated at $ud with current $currentDate")
            Future.successful(None)
          }
        }
      }
    }
    Future.sequence(seq)
  }

  def passwordResetNotification : Future[Either[ErrorResponse, Boolean]] = {
    val currentDate = clock.now()
    val remainderDate = currentDate.minusDays(config.passwordExpiryDays-10)

    val futureResult = for{
      s <- daoBusiness.fetchPasswordsWithinExpirySpan(remainderDate)
      _ <- handlePasswordByAge(s, currentDate)
    } yield{
        Right(true)
    }
    futureResult recover {
        case e: Throwable => {
          logger.info("Password Reset Notification failed ", e)
          Right(false)
        }
    }
  }

  def deleteExpiredMagicTokensWithAudit: Future[Either[ErrorResponse, Boolean]] = {
    for {
     dtoMagicTokens <- daoBusiness.deleteExpiredMagicTokens(clock.now(), UserMagicTokenConstants.expiryTime)
      _ <- magicLinkAuditService.bulkSaveMagicLinkDeleteAudit(dtoMagicTokens, clock.now())
     dtoDocumentTokens <- daoBusiness.deleteExpiredDocumentTokens(clock.now(), UserMagicTokenConstants.expiryTimeForDocumentLink)
     _ <- magicLinkAuditService.bulkSaveDocumentLinkDeleteAudit(dtoDocumentTokens, clock.now())
    } yield Right(true)
  }

}
