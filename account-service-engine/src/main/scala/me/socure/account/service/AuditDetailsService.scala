package me.socure.account.service

import me.socure.account.validator.V2Validator
import me.socure.constants.EnvironmentTypes
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2._
import me.socure.storage.slick.dao.DaoAccountV2

import scala.concurrent.{ExecutionContext, Future}

class AuditDetailsService(daoAccountV2: DaoAccountV2, v2Validator: V2Validator)(implicit ec: ExecutionContext) {


  def formProductSettingsDelta(accountId: Option[Long], environmentId: Option[Long], productSettingType: String, productSettingDeltaMap: Map[String, (String, String)]): Seq[ProductSettingDelta] = {
    val accId = accountId.getOrElse(0L)
    if (productSettingDeltaMap.nonEmpty) {
      Seq(ProductSettingDelta(accId, environmentId, productSettingType, productSettingDeltaMap))
    }
    else Seq.empty
  }

  def formActionUserInfo(creator: Option[Creator]): Future[ActionUserInfo] = {

    creator match {
      case Some(creator) =>
        val futureCreatorUserDetails = daoAccountV2.getUserDetailsV2(creator.userId, creator.accountId)
        val futureCustomRolePermissions = daoAccountV2.fetchPermissions(creator.accountId, creator.userId, EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
        val actionUserInfo: Future[ActionUserInfo] = for {
          creatorUserDetails <- futureCreatorUserDetails
          rootAccountType <- v2Validator.getRootParentAccountType(creator.accountId)
          systemRolePermissions <- daoAccountV2.fetchSystemRolesPermissions(creator.accountId, creator.userId, EnvironmentTypes.GLOBAL_ENVIRONMENT.id, rootAccountType)
          customRolePermissions <- futureCustomRolePermissions
        } yield {

          val (firstName, lastName, email, contactNumber) = creatorUserDetails.getOrElse("Anonymous", "", "", "")
          val userDetails = UserDetails(creator.userId, firstName, lastName, email, contactNumber)
          ActionUserInfo(userDetails, systemRolePermissions, customRolePermissions)

        }
        actionUserInfo
      case None => Future.successful(ActionUserInfo(UserDetails(0, "", "", "", ""), Set.empty, Seq.empty))
    }
  }

  def formAuditDetails(isSuccess: Boolean, creator: Option[Creator], accountDelta: Option[AccountDelta], errorResponse: Option[ErrorResponse], productSettingDelta: Seq[ProductSettingDelta]): Future[AuditDetails] = {
    formActionUserInfo(creator) map {
      actionUserInfo => AuditDetails(isSuccess, actionUserInfo, None, errorResponse, productSettingDelta)
    }
  }
}
