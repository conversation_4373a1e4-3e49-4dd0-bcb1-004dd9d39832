package me.socure.account.service

import me.socure.constants.DashboardUserPermissions
import me.socure.convertors.AccountConvertors
import me.socure.model.account.{CurrentAndNewPermissions, CurrentPermission, NewPermission, PermissionConversionResponse}
import me.socure.model.user.DashboardUserRole

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DashboardUserPermissionService(implicit ec: ExecutionContext) {
  def getCurrentAndNewPermissions: Future[PermissionConversionResponse] = {
    val legacyPermissions = DashboardUserRole.getLegacyPermissions
    val permissionConversionResponse = PermissionConversionResponse(
      permissions = legacyPermissions map { legacyPermission =>
        CurrentAndNewPermissions(
          currentPermission = CurrentPermission (
              id = legacyPermission.id,
            name = legacyPermission
          ),
          newPermissions = AccountConvertors.toDashboardPermissions(Set(legacyPermission.id)) map { permissionId =>
            val dashboardUserPermission = DashboardUserPermissions.byId(permissionId).get
            NewPermission(
              dashboardUserPermission.id,
              dashboardUserPermission.name
            )
          }
        )
      }
    )
    Future.successful(permissionConversionResponse)
  }
}
