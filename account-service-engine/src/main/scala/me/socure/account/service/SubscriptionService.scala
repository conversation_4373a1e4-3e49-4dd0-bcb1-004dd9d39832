package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes.{EnvironmentNotFound, ExceptionCodes, UnknownError}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.subscription.{ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.account.validator.V2Validator
import me.socure.constants.SubscriptionTypes
import me.socure.model.ErrorResponse
import me.socure.model.account._
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account.DtoSubscriptionType
import me.socure.storage.slick.tables.account.mappers.SubscriptionTypeMapper
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class SubscriptionService(daoSubscriptions: DaoSubscriptions,
                          daoSubscriptionType: DaoSubscriptionType,
                          daoSubscriptionStatus: DaoSubscriptionStatus,
                          daoEnvironment: DaoEnvironment,
                          daoSubscriptionChannelRegistry: DaoSubscriptionChannelRegistry,
                          daoAccountV2: DaoAccountV2,
                          v2Validator: V2Validator
                         )(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  private def getUpsertStatus(result : Int, successCode : String, errorCode : ExceptionCodes) : Either[ErrorResponse, String] = {
    result match {
      case c if c > 0 => Right(successCode)
      case c if c == 0 => Left(ErrorResponseFactory.get(ExceptionCodes.InvalidInputFormat))
      case _ => Left(ErrorResponseFactory.get(errorCode))
    }
  }

  def listSubscriptionTypes: Future[Either[ErrorResponse, Seq[DtoSubscriptionType]]] = {
    daoSubscriptionType.fetchSubscriptionTypes().map {
      case subscriptionTypes : Seq[SubscriptionType] =>
        Right(subscriptionTypes.map(SubscriptionTypeMapper.toDto))
      case e: Exception =>
        logger.info("Couldn't list subscription types", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  /**
   * Updates the Subscription Status
   *
   * @param environmentId
   * @param subscriptionTypes
   * @param subscriptionStatus 1 -> enable, 2-> suspend, 3-> disable, 4-> delete
   * @return
   */
  def updateSubscriptionStatus(environmentId: Long, subscriptionTypes : Set[Long], subscriptionStatus: Int): Future[Either[ErrorResponse, String]] = {
    subscriptionStatus match {
      case -1 =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidInputFormat)))
      case SubscriptionStatuses.DELETE.id =>
        daoSubscriptionStatus.deleteSubscriptionType(environmentId, subscriptionTypes) map {
          count => getUpsertStatus(count, "Subscription types deleted Successfully", ExceptionCodes.InvalidInputFormat)
        }
      case _ =>
        daoSubscriptionStatus.updateSubscriptionStatus(environmentId, subscriptionTypes, subscriptionStatus)map {
          count => getUpsertStatus(count, "Subscription Status updated Successfully", ExceptionCodes.InvalidInputFormat)
        }
    }
  }

  def listSubscriptions(accountId: Long): Future[Either[ErrorResponse, Seq[Long]]] = {
    daoSubscriptions.list(accountId).map {
      case subscriptions : Seq[Subscriptions] =>
        Right(subscriptions.map(_.subscriptionTypeId))
      case e: Exception =>
        logger.info(s"Couldn't list Subscriptions for the account ${accountId}", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def subscribe(accountId: Long, subscriptionTypeId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoSubscriptions.isSubscriptionTypeEnabled(accountId, subscriptionTypeId) flatMap  {
      case false =>
        daoSubscriptions.subscribe(accountId, subscriptionTypeId).map {
          case n if n >= 1 =>
            daoEnvironment.getEnvironmentsByAccountId(accountId).map {
              case env if env.nonEmpty =>
                val environmentIds = env.map(_.id)
                daoSubscriptionChannelRegistry.suspendSubscriptionChannelRegistries(environmentIds, subscriptionTypeId)
              case _ =>
                logger.info("Error occurred while resetting subscription status")
                Left(ErrorResponseFactory.get(EnvironmentNotFound))
            }
            Right(true)
          case _ => logger.info(s"Couldn't Subscribe - $subscriptionTypeId for the account $accountId")
            Left(ErrorResponseFactory.get(UnknownError))
        } recover {
          case t : Exception =>
            logger.info(s"Exception Couldn't Subscribe - $subscriptionTypeId for the account $accountId", t)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case true =>
        logger.info(s"Already Subscribed - $subscriptionTypeId for the account $accountId")
        Future.successful(Right(true))
    }
  }

  def unsubscribe(accountId: Long, subscriptionTypeId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoSubscriptions.isSubscriptionTypeEnabled(accountId, subscriptionTypeId) flatMap {
      case true =>
        daoSubscriptions.unsubscribe(accountId, subscriptionTypeId).map {
          case n if n >= 1 =>
            daoEnvironment.getEnvironmentsByAccountId(accountId).map {
              case env if env.nonEmpty =>
                val environmentIds = env.map(_.id)
                daoSubscriptionChannelRegistry.disableSubscriptionChannelRegistries(environmentIds, subscriptionTypeId)
              case _ =>
                logger.info("Error occurred while resetting subscription status")
                Left(ErrorResponseFactory.get(EnvironmentNotFound))
            }
            Right(true)
          case _ => logger.info(s"Couldn't Unsubscribe - $subscriptionTypeId for the account $accountId")
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case false =>
        for{
          a <- daoSubscriptions.accountExists(accountId)
          st <- daoSubscriptionType.isValidSubscriptionTypeId(subscriptionTypeId)
        } yield (a, st) match {
          case (true, true) =>
            logger.info(s"Unsubscribe failed: Could not find subscription - $subscriptionTypeId for the account $accountId")
            Right(true)
          case (_, _) =>
            logger.info(s"Couldn't Unsubscribe - $subscriptionTypeId for the account $accountId")
            Left(ErrorResponseFactory.get(UnknownError))
        }
    }
  }

  def listSubscriptionTypesWithProvision(accountId : Long): Future[Either[ErrorResponse, Seq[SubscriptionsProvision]]] = {
    daoSubscriptionType.fechSubscriptionTypesWithProvision(accountId) map {
      case res => Right(res)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getEnvSubscriptionRegistryDetails(envId: Long): Future[Either[ErrorResponse, Seq[WebhookNotificationPreference]]]= {
    daoSubscriptionChannelRegistry.getSubscriptionChannelRegistriesByEnvironmentId(environmentId = envId) map {
      scr =>
        val updatedModel = scr.map{
        row => WebhookNotificationPreference(
          communicationSource = row._1._1.communicationSource,
          status = SubscriptionChannelRegistryStatus.byId( row._1._1.status).map(_.name).getOrElse(throw new Exception("Invalid WlAndDv channel status")),
          subscriptionType = if (row._1._1.subscriptionTypeId == 1L) SubscriptionTypes.Watchlist_Monitoring.name else SubscriptionTypes.Document_Verification.name,
          featureType = row._1._1.featureTypeId,
          channelType = ChannelType.byId(row._1._1.channelType).map(_.name).getOrElse(throw new Exception("Invalid WlAndDv channel type")),
          communicationMode = CommunicationMode.byId(row._1._1.communicationMode).map(_.name).getOrElse(throw new Exception("Invalid WlAndDv comm mode"))
        )
      }
      Right(updatedModel)
    } recover {
      case e: Throwable =>
        logger.info(s"Could not fetch subscriptionChannelRegistry info $e")
        Left(ErrorResponseFactory.get(ExceptionCodes.InternalError))
    }
  }

}
