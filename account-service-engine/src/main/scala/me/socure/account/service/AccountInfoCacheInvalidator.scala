package me.socure.account.service

import me.socure.account.service.common.{AccInfoCacheKeyProvider, CacheKeyProvider}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

class AccountInfoCacheInvalidator(
                                   accInfoCacheKeyProvider: AccInfoCacheKeyProvider,
                                   cacheKeyProvider: CacheKeyProvider,
                                   scalaCache: ScalaCache[_]
                                 )(implicit ec: ExecutionContext) {

  def getRawCachedKeys(accountId: Long): Set[String] = {
    Set(
      accInfoCacheKeyProvider.getAccountPermissionsKey(accountId),
      accInfoCacheKeyProvider.keyForGetAccountDetailsById(accountId)
    )
  }

  def invalidate(apiKeys: Set[String], accountId: Long): Future[Unit] = {
    val f1: Set[Future[Unit]] = apiKeys.map { apiKey =>
      val cacheKey = cacheKeyProvider.provide(
        accInfoCacheKeyProvider.provide(apiKey)
      )
      scalaCache.cache.remove(cacheKey)
    }
    val rawCachedKeys: Set[String] = getRawCachedKeys(accountId)
    val f2: Set[Future[Unit]] = rawCachedKeys.map(k => {
      scalaCache.cache.remove(k)
    })
    Future.sequence(f1 ++ f2)
      .map(_ => ())
  }

}
