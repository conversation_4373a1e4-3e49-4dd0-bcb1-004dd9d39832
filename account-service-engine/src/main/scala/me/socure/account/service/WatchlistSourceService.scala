package me.socure.account.service

import me.socure.util.ProductSettingsDeltaUtil.{formProductSettingsDeltaMapForField, getImplicits}
import me.socure.account.service.common.exceptions.ExceptionCodes.{UnableToExcludeWatchlistSource, UnableToIncludeWatchlistSource, UnknownError}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.watchlist.ManageCAAccountsCacheKeyProvider
import me.socure.account.service.common.watchlist.source.{WatchlistCategories, WatchlistSubCategories}
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.constants.DashboardUserPermissions
import me.socure.constants.ProductSettingsFields.WATCHLIST_SOURCES
import me.socure.model._
import me.socure.model.dashboardv2.{AuditDetails, Creator, ProductSettingDelta}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoWatchlistSource}
import me.socure.storage.slick.tables.account.mappers.WatchlistSourceMapper
import me.socure.storage.slick.tables.account.{DtoFilteredWatchlistSource, DtoWatchlistSource}
import org.apache.commons.csv.{CSVFormat, CSVParser, CSVRecord}
import org.apache.commons.io.IOUtils
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import java.io.InputStream
import java.nio.charset.StandardCharsets
import scala.collection.JavaConverters._
import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}


class WatchlistSourceService(daoWatchlistSource: DaoWatchlistSource,
                             daoAccountV2: DaoAccountV2,
                             clock: Clock,
                             v2Validator: V2Validator,
                             scalaCache: ScalaCache[_],
                             auditDetailsService: AuditDetailsService)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts.ca." + getClass.getSimpleName)
  val cawatchlistSourceApi = "/settings/preferences/ca/watchlist/sources/included"

  def list: Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    daoWatchlistSource.list().map {
      case sources: Seq[DtoWatchlistSource] =>
        Right(sources.map(WatchlistSourceMapper.toModel))
    } recover {
      case e: Exception =>
        logger.info("Couldn't list the source", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listByCategory(category: Int): Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    daoWatchlistSource.listByCategory(category).map {
      case sources: Seq[DtoWatchlistSource] if sources.nonEmpty =>
        Right(sources.map(WatchlistSourceMapper.toModel))
      case es: Seq[DtoWatchlistSource] if es.isEmpty =>
        logger.warn("Couldn't list the source by Category, invalid category => $category")
        Right(Seq.empty)
    } recover {
      case e: Exception =>
        logger.info("Couldn't list the source by Category", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listBySubCategory(subCategory: Int): Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    daoWatchlistSource.listBySubCategory(subCategory).map {
      case sources: Seq[DtoWatchlistSource] =>
        Right(sources.map(WatchlistSourceMapper.toModel))
      case _ =>
        logger.warn("Couldn't list the source by Sub Category, invalid category => $subCategory")
        Right(Seq.empty)
    } recover {
      case e: Exception =>
        logger.info("Couldn't list the source by Sub-Category", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }


  private def formProductSettingsDelta(accountId: Option[Long], environmentIds: Seq[Long], existingSources: Seq[(DtoFilteredWatchlistSource, DtoWatchlistSource)], watchlistSourceForEnvironment: WatchlistSourceForEnvironment): Future[Seq[ProductSettingDelta]] = {

    implicit val (_, _, implicit_3) = getImplicits
    Future.successful(environmentIds.flatMap {
      envId =>
        val watchListOldValues = existingSources.filter(wlSource => wlSource._1.environmentId == envId).map(wl => wl._1.sourceId.toString).toSet
        val watchlistNewValues = watchlistSourceForEnvironment.sourceIds.map(wl=>wl.toString)
        auditDetailsService.formProductSettingsDelta(accountId, Some(envId), "Watchlist",
          formProductSettingsDeltaMapForField(WATCHLIST_SOURCES, watchListOldValues, watchlistNewValues))
    })
  }
  private def formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess: Boolean,
                                                                 creator: Option[Creator],
                                                                 errorResponse: Option[ErrorResponse],
                                                                 existingSources: Seq[(DtoFilteredWatchlistSource, DtoWatchlistSource)],
                                                                 newSources: WatchlistSourceForEnvironment,
                                                                 environmentIds: Seq[Long]): Future[AuditDetails] = {


    formProductSettingsDelta(creator.map(_.accountId), environmentIds, existingSources, newSources) flatMap {
      product => auditDetailsService.formAuditDetails(isSuccess, creator, None, errorResponse, product)
    }
  }

  def includeWatchlistSource(includeWatchlistSource: WatchlistSourceForEnvironment): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    if(includeWatchlistSource.sourceIds.isEmpty){
      logger.info(s"Exclude watchlist sources for an environment ${includeWatchlistSource.environmentId}")
      v2Validator.isValidV2EnvironmentRequest(includeWatchlistSource.environmentId, includeWatchlistSource.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true =>
          daoWatchlistSource.listWatchListSourcesByEnvironmentIds(Seq(includeWatchlistSource.environmentId)) flatMap {
            case existingSources =>
              val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource includeWatchlistSource.environmentId
              removeCacheKeysWrapped(cawatchlistSourceApi, scalaCache, Set(cacheKey))(daoWatchlistSource excludeWatchlistSource includeWatchlistSource.environmentId) flatMap {
                case i if i >= 0 =>
                  formAuditDetailsWithProductSettingDeltaForAccounts(true, includeWatchlistSource.creator, None, existingSources, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
                    auditDetails => (auditDetails, Right(true))
                  }
                case _ =>
                  val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
                  formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), existingSources, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
                    auditDetails => (auditDetails, Left(errorResponse))
                  }
              }
          }
        case _ => logger.info("Could not Exclude Watchlist Socure")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Exclude Watchlist Source", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Exception =>
          logger.info(s"Problem in exclude watchlist source for an environment ${includeWatchlistSource.environmentId}", e)
          val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e : Throwable =>
          logger.info("Could not Exclude Watchlist Source", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    } else {
      v2Validator.isValidV2EnvironmentRequest(includeWatchlistSource.environmentId, includeWatchlistSource.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true => val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource includeWatchlistSource.environmentId
          daoWatchlistSource.listWatchListSourcesByEnvironmentIds(Seq(includeWatchlistSource.environmentId)) flatMap {
            case existingSources =>
              removeCacheKeysWrapped(cawatchlistSourceApi, scalaCache, Set(cacheKey))(daoWatchlistSource includeWatchlistSource(includeWatchlistSource.environmentId, includeWatchlistSource.sourceIds)) flatMap {
                case Some(i) if i >= 1 =>
                  formAuditDetailsWithProductSettingDeltaForAccounts(true, includeWatchlistSource.creator, None, existingSources, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
                    auditDetails => (auditDetails, Right(true))
                  }

                case _ =>
                  val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
                  formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), existingSources, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
                    auditDetails => (auditDetails, Left(errorResponse))
                  }
              }
          }
        case _ => logger.info("Could not Include Watchlist Source")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Include Watchlist Source", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Exception =>
          logger.info(s"Problem in include watchlist source for an environment ${includeWatchlistSource.environmentId}, ${includeWatchlistSource.sourceIds}", e)
          val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e : Throwable =>
          logger.info("Could not Include Watchlist Source", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, includeWatchlistSource.creator, Some(errorResponse), Seq.empty, includeWatchlistSource, Seq(includeWatchlistSource.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    }
  }

  def includeWatchlistSource(watchlistSourceRequest: WatchlistSourceRequest): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    if (watchlistSourceRequest.watchlistSourceForEnvironment.sourceIds.isEmpty) {
      logger.info(s"Exclude watchlist sources for an environment ${watchlistSourceRequest.watchlistSourceForEnvironment.environmentId}")
      v2Validator.isValidV2EnvironmentRequest(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true => daoWatchlistSource.getEnvironmentIds(watchlistSourceRequest.environmentTypes, watchlistSourceRequest.accountIds) flatMap {
          case environmentIds: Seq[Long] =>
            daoWatchlistSource.listWatchListSourcesByEnvironmentIds(environmentIds) flatMap {
              case existingSources =>
                val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource watchlistSourceRequest.watchlistSourceForEnvironment.environmentId
                removeCacheKeysWrapped(cawatchlistSourceApi, scalaCache, Set(cacheKey))(daoWatchlistSource excludeWatchlistSource environmentIds) flatMap {
                  case i if i >= 0 =>
                    formAuditDetailsWithProductSettingDeltaForAccounts(true, watchlistSourceRequest.watchlistSourceForEnvironment.creator, None, existingSources, watchlistSourceRequest.watchlistSourceForEnvironment, environmentIds) map {
                      auditDetails => (auditDetails, Right(true))
                    }
                  case _ =>
                    val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
                    formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), existingSources, watchlistSourceRequest.watchlistSourceForEnvironment, environmentIds) map {
                      auditDetails => (auditDetails, Left(errorResponse))
                    }

                }

            }

          case _ => logger.info("Could not Save CAWatchlist preference")
            val errorResponse = ErrorResponseFactory.get(UnknownError)
            formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }

        }
        case _ => logger.info("Could not Exclude Watchlist Socure")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Exclude Watchlist Source", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }

        case e: Exception =>
          logger.info(s"Problem in exclude watchlist source for an environment ${watchlistSourceRequest.watchlistSourceForEnvironment.environmentId}", e)
          val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Throwable =>
          logger.info("Could not Exclude Watchlist Source", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    } else {
      v2Validator.isValidV2EnvironmentRequest(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true => daoWatchlistSource.getEnvironmentIds(watchlistSourceRequest.environmentTypes, watchlistSourceRequest.accountIds) flatMap {
          case environmentIds: Seq[Long] => val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource watchlistSourceRequest.watchlistSourceForEnvironment.environmentId
            daoWatchlistSource.listWatchListSourcesByEnvironmentIds(environmentIds) flatMap {
              case existingSources =>
                removeCacheKeysWrapped(cawatchlistSourceApi, scalaCache, Set(cacheKey))(daoWatchlistSource includeWatchlistSources(environmentIds, watchlistSourceRequest.watchlistSourceForEnvironment.sourceIds)) flatMap {
                  case Some(res) if res > 0 =>
                    formAuditDetailsWithProductSettingDeltaForAccounts(true, watchlistSourceRequest.watchlistSourceForEnvironment.creator, None, existingSources, watchlistSourceRequest.watchlistSourceForEnvironment, environmentIds) map {
                      auditDetails => (auditDetails, Right(true))
                    }
              case _ => logger.info("Could not Save CAWatchlist preference")
                val errorResponse = ErrorResponseFactory.get(UnableToExcludeWatchlistSource)
                formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), existingSources, watchlistSourceRequest.watchlistSourceForEnvironment, environmentIds) map {
                  auditDetails => (auditDetails, Left(errorResponse))
                }
                }
            }
          case _ => logger.info("Could not Save CAWatchlist preference")
            val errorResponse = ErrorResponseFactory.get(UnknownError)
            formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }
        }
        case _ => logger.info("Could not Include Watchlist Source")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Include Watchlist Source", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Exception =>
          logger.info(s"Problem in include watchlist source for an environment ${watchlistSourceRequest.watchlistSourceForEnvironment.environmentId}, ${watchlistSourceRequest.watchlistSourceForEnvironment.sourceIds}", e)
          val errorResponse = ErrorResponseFactory.get(UnableToIncludeWatchlistSource)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Throwable =>
          logger.info("Could not Include Watchlist Source", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false, watchlistSourceRequest.watchlistSourceForEnvironment.creator, Some(errorResponse), Seq.empty, watchlistSourceRequest.watchlistSourceForEnvironment, Seq(watchlistSourceRequest.watchlistSourceForEnvironment.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    }
  }

  def includedWatchlistSources(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true =>
        daoWatchlistSource.listIncludedWatchlistSources(environmentId).map {
          case sources: Seq[DtoWatchlistSource] =>
            Right(sources.map(WatchlistSourceMapper.toModel))
          case _ =>
            logger.info(s"Couldn't list the included source for environment $environmentId")
            Right(Seq.empty)
        }
      case _ =>
        logger.info("Could not list the included source")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not list the included source", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not list the included source", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def includedWatchlistSources(environmentId: Long): Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    daoWatchlistSource.listIncludedWatchlistSources(environmentId).map {
      case sources: Seq[DtoWatchlistSource] =>
        Right(sources.map(WatchlistSourceMapper.toModel))
      case _ =>
        logger.info(s"Couldn't list the included source for environment $environmentId")
        Right(Seq.empty)
    }
  }

  def includedWatchlistSourcesNames(environmentId: Long): Future[Either[ErrorResponse, Seq[String]]] = {
    daoWatchlistSource.listIncludedWatchlistSources(environmentId).map {
      case sources: Seq[DtoWatchlistSource] =>
        Right(sources.map(_.name))
      case _ =>
        logger.warn("Couldn't list the included source for environment $environmentId")
        Right(Seq.empty)
    } recover {
      case e: Exception =>
        logger.info("Couldn't list the included sources for environment $environmentId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listWatchlistCategories(): Future[Vector[WatchlistCategory]] = {
    Future.successful(WatchlistCategories.values.map(a => WatchlistCategory(a.id, a.name)))
  }

  def listWatchlistSubCategories(): Future[Vector[WatchlistSubCategory]] = {
    Future.successful(WatchlistSubCategories.values.map(a => WatchlistSubCategory(a.id, a.name)))
  }

  /*
  Maintian two list for storing valid and invalid records.
  Parse CSV contents, Convert each row to the DTO object. If the given data is valid, add it to valid list and upsert using the DAO class
  DTO Conversion throws exception for invalid category and sub category values
  If category or subcategory is invalid, catch the exception add it to invalid list , track the invalid data using metrics and return the invalid list back to client
  */
  def importWatchlistSources(sources: String): Future[Either[ErrorResponse,List[WatchlistSource]]] = {
    val csvRecordsList : Seq[CSVRecord] = parseCSVContentFromSources(sources)
    val validList : ListBuffer[DtoWatchlistSource] = ListBuffer()
    val invalidList : ListBuffer[WatchlistSource] = ListBuffer()
    csvRecordsList.foreach(row => {
      val watchlistSource = WatchlistSource(0, row.get(0), row.get(1), row.get(2), Some(row.get(3)), DateTime.now())
      try{
        validList += WatchlistSourceMapper.toDto(watchlistSource)
      }catch { //handle exceptions for invalid category and sub categories
        case ex: Exception => {
          handleCSVException(ex,watchlistSource)
          invalidList += watchlistSource
        }
      }
    })
    daoWatchlistSource.importWatchlistSources(validList.toList).map {
      case 0 =>
        Right(invalidList.toList)
    } recover {
      case ex : Exception => {
        logger.info("Error while importing watchlist sources",ex)
        Left(ErrorResponseFactory.get(ex))
      }
    }
  }

  def parseCSVContentFromSources(sources: String) : Seq[CSVRecord]= {
    val inputStream: InputStream = IOUtils.toInputStream(sources, StandardCharsets.UTF_8)
    val csvParser = CSVParser.parse(inputStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withFirstRecordAsHeader())
    val csvRecords: Seq[CSVRecord] = csvParser.getRecords.asScala
    csvRecords
  }

  def handleCSVException(ex: Exception, watchlistSource: WatchlistSource): Unit ={
    ex.getMessage match {
      case ExceptionCodes.InvalidWatchlistCategory.description => {
        metrics.increment("import.watchlist.sources.csv.invalid_category",watchlistSource.category)
      }
      case ExceptionCodes.InvalidWatchlistSubCategory.description => {
        metrics.increment("import.watchlist.sources.csv.invalid_sub_category",watchlistSource.subCategory)
      }
      case _ => throw ex
    }
  }


}
