package me.socure.account.service

import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.util.ProductSettingsDeltaUtil.{formProductSettingsDeltaMapForField, getImplicits}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.common.clock.Clock
import me.socure.constants.ProductSettingsFields.DASHBOARD_ALLOWED_DOMAINS
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountDashboardDomain, AccountDomain}
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.storage.slick.dao.DaoDashboardDomain
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.slf4j.LoggerFactory
import scalacache.ScalaCache

import java.sql.BatchUpdateException
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 10/04/2017.
  */
class DashboardDomainService(daoDashboardDomain: DaoDashboardDomain, daoBusinessUser: DaoBusinessUser, clock: Clock, scalaCache: ScalaCache[_], auditDetailsService: AuditDetailsService, dashboardAccountService2: DashboardAccountServiceV2)(implicit ec: ExecutionContext) {

  private val logger = LoggerFactory.getLogger(getClass)
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"


  def formAuditDetails(isSuccess: Boolean, accountId: Option[Long], environmentId: Option[Long], creator: Option[Creator], errorResponse: Option[ErrorResponse], existingDomain: List[String], newDomains: List[String]): Future[AuditDetails] = {
    implicit val (_, _, implicit_3) =  getImplicits
    val productSettingDeltaMap = formProductSettingsDeltaMapForField(DASHBOARD_ALLOWED_DOMAINS, existingDomain.toSet, newDomains.toSet)
    val productSettingDelta = auditDetailsService.formProductSettingsDelta(accountId, environmentId, "General", productSettingDeltaMap)
    auditDetailsService.formAuditDetails(isSuccess, creator, None, errorResponse, productSettingDelta)
  }

  def upsertDashboardDomain(accountId: Long, domain: List[String]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val accountDomain: AccountDomain = AccountDomain(accountId, domain)
    daoDashboardDomain.isAccountWhitelisted(accountId) flatMap {
      case true =>
        daoDashboardDomain.getDomainDetailsForAccount(accountId).flatMap {
          case domains =>
            val existingDomains: List[String] = domains.map(_.allowedDomain.getOrElse("").split(",").toList).getOrElse(List.empty)
            val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
            removeCacheKeysWrapped(getDomainByAccountIdApiName, scalaCache, Set(cacheKey))(daoDashboardDomain updateDomain accountDomain) flatMap {
              case res if res > 0 =>
                formAuditDetails(true, Some(accountId), None, None, None, existingDomains, domain) map {
                  auditDetails => (auditDetails, Right(true))
                }
              case _ =>
                logger.info("Could not update dashboard domain")
                val error = ErrorResponseFactory.get(ExceptionCodes.DomainUpdateFailed)
                formAuditDetails(false, Some(accountId), None, None, Some(error), existingDomains, domain) map {
                  auditDetails => (auditDetails, Left(error))
                }
            } recoverWith {
              case e: Exception =>
                logger.info("Error occurred while update dashboard domain", e)
                val error = ErrorResponseFactory.get(ExceptionCodes.DomainUpdateFailed)
                formAuditDetails(false, Some(accountId), None, None, Some(error), existingDomains, domain) map {
                  auditDetails => (auditDetails, Left(error))
                }
            }
        }
      case _ => daoDashboardDomain.saveDomain(Seq(AccountConvertors.getDtoDashboardDomain(AccountDomain(accountId, domain), clock))).flatMap {
        case row if row.nonEmpty =>
          formAuditDetails(true, Some(accountId), None, None, None, List.empty, domain) map {
            auditDetails => (auditDetails, Right(true))
          }
        case _ =>
          logger.info("Could not insert dashboard domain list")
          val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
          formAuditDetails(false, Some(accountId), None, None, Some(error), List.empty, domain) map {
            auditDetails => (auditDetails, Left(error))
          }
      } recoverWith {
        case t: BatchUpdateException =>
          val error = ErrorResponseFactory.get(t)
          formAuditDetails(false, Some(accountId), None, None, Some(error), List.empty, domain) map {
            auditDetails => (auditDetails, Left(error))
          }
      }
    }
  }


  def getWhitelistedDomainForAccount(accountId : Long) : Future[Either[ErrorResponse, String]] = {
    daoDashboardDomain.getDomainDetailsForAccount(accountId).map {
      case Some(row) => Right(row.allowedDomain.getOrElse(""))
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound))
    }
  }

  def getWhitelistedDomainForAccountByEmail(email : String) : Future[Either[ErrorResponse, String]] = {
    daoBusinessUser.getUserAccount(email).flatMap {
      case Some(id) => daoDashboardDomain.getDomainDetailsForAccount(id).map {
        case Some(d) => Right(d.allowedDomain.getOrElse(""))
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound))
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def getWhitelistedDomainPermissionForAccountByEmail(email : String) : Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    daoBusinessUser.getUserAccount(email).flatMap {
      case Some(id) => getWhitelistedDomainPermissionForAccountById(id = id)
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def getAssociatedAccountDashboardDomainListByEmailId(email: String): Future[Either[ErrorResponse, Seq[AccountDashboardDomain]]] = {
    daoBusinessUser.getIdByEmail(email).flatMap {
      case Some(userId) =>
        dashboardAccountService2.getAssociatedAccounts(userId).flatMap {
          case Right(accountList) if accountList.nonEmpty =>
            val accountIds = accountList.map(_.id)
            daoDashboardDomain.getAccountDashboardDomains(accountIds).map(Right(_))
          case Right(_) =>
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)))
          case Left(error) =>
            Future.successful(Left(error))
        }
      case None =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
    }
  }


  def getWhitelistedDomainPermissionForAccountById(id: Long) : Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    daoDashboardDomain.isDomainWhiteListEnabled(id) flatMap  {
      case true =>
        daoDashboardDomain.getDomainDetailsForAccount(id) map  {
          case Some(wd) => Right(AccountDashboardDomain(accountId = id, domainWhiteEnabled = true, whiteListedDomain = wd.allowedDomain))
          case _ =>   Right(AccountDashboardDomain(accountId = id, domainWhiteEnabled = true, whiteListedDomain = None))
        }
      case _ =>
        Future.successful(Right(AccountDashboardDomain(accountId = id, domainWhiteEnabled = false, whiteListedDomain = None)))
    }
  }

}
