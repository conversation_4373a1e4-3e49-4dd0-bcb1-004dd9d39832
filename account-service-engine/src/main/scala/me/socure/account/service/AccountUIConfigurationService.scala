package me.socure.account.service

import me.socure.account.service.common.AccountUIConfigurationCacheKeyProvider
import me.socure.util.ProductSettingsDeltaUtil.{formProductSettingsDeltaMapForField, getImplicits}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.util.CacheUtil.removeCacheKeysWrapped
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.AccountTypes
import me.socure.constants.ProductSettingsFields.{GENERAL, GENERAL_AUTO_SIGNOUT, GENERAL_IDLE_SIGNOUT}
import me.socure.model.dashboardv2.{AuditDetails, Creator, ProductSettingDelta}
import me.socure.model.{AccountUIConfiguration, AccountUIConfigurationRequest, AccountsUIConfigurationRequest, ErrorResponse}
import me.socure.storage.slick.dao.DaoAccountUIConfiguration
import me.socure.storage.slick.tables.account.DtoAccountUIConfiguration
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

class AccountUIConfigurationService(daoUIAccountConfiguration: DaoAccountUIConfiguration, v2Validator: V2Validator, scalaCache: ScalaCache[_], auditDetailsService: AuditDetailsService)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val getUIAccountConfigurationApiName = "/ui/configuration"

  def getUIAccountConfiguration(accountId: Long, creator: Creator): Future[Either[ErrorResponse, AccountUIConfiguration]] = {
    (for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
      res <- daoUIAccountConfiguration.getUIAccountConfiguration(accountId) map {
        case Some(dtoUIAccountConfiguration: DtoAccountUIConfiguration) => Right(AccountUIConfiguration(Some(dtoUIAccountConfiguration.accountId), Some(dtoUIAccountConfiguration.autoTimeoutInMinutes), Some(dtoUIAccountConfiguration.idleTimeoutInMinutes), dtoUIAccountConfiguration.byAccountId, dtoUIAccountConfiguration.isForceInherit, dtoUIAccountConfiguration.hideSystemDefinedRoles))
        case _ => Right(AccountUIConfiguration())
      } recover {
        case e: Exception =>
          logger.info(s"Could not fetch Account UI Configuration for account $accountId, create $creator", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFetchUIAccountConfiguration))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Account UI Configuration for account $accountId, create $creator", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching account hierarchy by account $creator", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError))
    }
  }

  def formProductSettingsDeltaWithUiConf(accountId: Long, uIAccountConfigurationRequest: AccountUIConfigurationRequest, accountUIConfiguration: Option[AccountUIConfiguration]): Seq[ProductSettingDelta] = {
    implicit val (_, implicits_2, _) = getImplicits

    val autoTimeout = formProductSettingsDeltaMapForField(GENERAL_AUTO_SIGNOUT, accountUIConfiguration.map(_.autoTimeoutInMinutes.map(_.toLong)).flatten.getOrElse(0L), uIAccountConfigurationRequest.autoTimeoutInMinutes.toLong)
    val idleTimeout = formProductSettingsDeltaMapForField(GENERAL_IDLE_SIGNOUT, accountUIConfiguration.map(_.idleTimeoutInMinutes.map(_.toLong)).flatten.getOrElse(0L), uIAccountConfigurationRequest.idleTimeoutInMinutes.toLong)
    Seq(ProductSettingDelta(accountId, None, GENERAL, (autoTimeout++ idleTimeout)))
  }

  def formAuditDetailsWithProductSettingDelta(isSuccess: Boolean, creator: Creator, errorResponse: Option[ErrorResponse], uIAccountConfigurationRequest: AccountUIConfigurationRequest, accountUIConfiguration: Option[AccountUIConfiguration]): Future[AuditDetails] = {
    val productSettings = formProductSettingsDeltaWithUiConf(uIAccountConfigurationRequest.accountId, uIAccountConfigurationRequest, accountUIConfiguration)
    auditDetailsService.formAuditDetails(isSuccess, Some(creator), None, errorResponse, productSettings)
  }

  def saveUIAccountConfiguration(uIAccountConfigurationRequest: AccountUIConfigurationRequest, creator: Creator): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    (for {
      _ <- v2Validator.validateAccountAccess(uIAccountConfigurationRequest.accountId, creator.accountId)
      res <- getUIAccountConfiguration(uIAccountConfigurationRequest.accountId, creator) flatMap {
        case Right(configuration: AccountUIConfiguration) if configuration.accountId.isDefined =>
          val cacheKey = AccountUIConfigurationCacheKeyProvider.provide(uIAccountConfigurationRequest.accountId)
          removeCacheKeysWrapped(getUIAccountConfigurationApiName, scalaCache, Set(cacheKey))(daoUIAccountConfiguration updateUIAccountConfiguration(uIAccountConfigurationRequest.accountId, uIAccountConfigurationRequest.autoTimeoutInMinutes.toShort, uIAccountConfigurationRequest.idleTimeoutInMinutes.toShort, uIAccountConfigurationRequest.hideSystemDefinedRoles)) flatMap {
            case res if res > 0 =>
              formAuditDetailsWithProductSettingDelta(true, creator, None, uIAccountConfigurationRequest, Some(configuration)) map {
                auditDetails => (auditDetails, Right(true))
              }

            case _ =>
              logger.info(s"Could not update Account UI Configuration for account ${uIAccountConfigurationRequest.accountId}, create $creator")
              val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
              formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, Some(configuration)) map {
                auditDetails => (auditDetails, Left(errorResponse))
              }


          } recoverWith {
            case e: Exception =>
              logger.info(s"Could not save Account UI Configuration for account ${uIAccountConfigurationRequest.accountId}, create $creator", e)
              val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
              formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, Some(configuration)) map {
                auditDetails => (auditDetails, Left(errorResponse))
              }

          }
        case _ => daoUIAccountConfiguration.saveUIAccountConfiguration(DtoAccountUIConfiguration(0, uIAccountConfigurationRequest.accountId, uIAccountConfigurationRequest.autoTimeoutInMinutes.toShort, uIAccountConfigurationRequest.idleTimeoutInMinutes.toShort,
          DateTime.now(), DateTime.now(), None, None, uIAccountConfigurationRequest.hideSystemDefinedRoles)) flatMap {
          case dtoSessionTimeout if dtoSessionTimeout.id > 0 =>
            formAuditDetailsWithProductSettingDelta(true, creator, None, uIAccountConfigurationRequest, None) map {
              auditDetails => (auditDetails, Right(true))
            }

          case _ =>
            logger.info(s"Could not save Account UI Configuration for account ${uIAccountConfigurationRequest.accountId}, create $creator")
            val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
            formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, None) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }
        }
      } recoverWith {
        case e: Exception =>
          logger.info(s"Could not save Account UI Configuration for account ${uIAccountConfigurationRequest.accountId}, create $creator", e)
          val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
          formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, None) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    } yield res) recoverWith {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Account UI Configuration for account ${uIAccountConfigurationRequest.accountId}, create $creator", ex)
        val errorResponse = ex.errorResponse

        formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, None) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
      case e: Exception =>
        logger.info(s"Error occurred while fetching account hierarchy by account $creator", e)
        val errorResponse = ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError)
        formAuditDetailsWithProductSettingDelta(false, creator, Some(errorResponse), uIAccountConfigurationRequest, None) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    }
  }

  def formProductSettingDeltaForAccounts(creator: Creator, currentUiConfigurations: Seq[DtoAccountUIConfiguration], uIAccountConfigurationRequest: AccountsUIConfigurationRequest): Seq[ProductSettingDelta] = {
    uIAccountConfigurationRequest.accountIds.map {
      accountId =>
        val previousConf: Option[AccountUIConfiguration] = currentUiConfigurations.find(currentUiConf => currentUiConf.accountId == accountId) match {
          case Some(dtoUIAccountConfiguration) => Some(AccountUIConfiguration(Some(dtoUIAccountConfiguration.accountId), Some(dtoUIAccountConfiguration.autoTimeoutInMinutes), Some(dtoUIAccountConfiguration.idleTimeoutInMinutes), None, dtoUIAccountConfiguration.isForceInherit, dtoUIAccountConfiguration.hideSystemDefinedRoles))
          case None => None
        }
        val newConf = AccountUIConfigurationRequest(accountId, uIAccountConfigurationRequest.autoTimeoutInMinutes, uIAccountConfigurationRequest.idleTimeoutInMinutes, uIAccountConfigurationRequest.hideSystemDefinedRoles)
        formProductSettingsDeltaWithUiConf(accountId, newConf, previousConf)
    }.flatten
  }

  def formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess: Boolean, creator: Creator, errorResponse: Option[ErrorResponse], currentUiConfigurations: Seq[DtoAccountUIConfiguration], uIAccountConfigurationRequest: AccountsUIConfigurationRequest): Future[AuditDetails] = {
    val productSettings = formProductSettingDeltaForAccounts(creator, currentUiConfigurations, uIAccountConfigurationRequest)
    auditDetailsService.formAuditDetails(isSuccess, Some(creator), None, errorResponse, productSettings)
  }

  def validateAndSaveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest: AccountsUIConfigurationRequest, creator: Creator, autoTimeout: Int,  sessionIdleTimeout: Int  ): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    def validateTimeouts(autoTimeoutLimit: Int, idleTimeoutLimit: Int): Either[ErrorResponse, Unit] = {
      if (uIAccountConfigurationRequest.autoTimeoutInMinutes < uIAccountConfigurationRequest.idleTimeoutInMinutes)
        Left(ErrorResponseFactory.get(ExceptionCodes.IdleTimeoutGreaterThanAutoTimeout))
      else if (uIAccountConfigurationRequest.autoTimeoutInMinutes < 1 || uIAccountConfigurationRequest.autoTimeoutInMinutes > autoTimeoutLimit)
        Left(ErrorResponseFactory.get(ExceptionCodes.AutoTimeoutExceeded))
      else if (uIAccountConfigurationRequest.idleTimeoutInMinutes < 1 || uIAccountConfigurationRequest.idleTimeoutInMinutes > idleTimeoutLimit)
        Left(ErrorResponseFactory.get(ExceptionCodes.IdleTimeoutExceeded))
      else
        Right(())
    }

    v2Validator.getRootParentAccountType(creator.accountId).flatMap {
      case Some(accountType) =>
        val (autoTimeoutLimit, idleTimeoutLimit) = accountType match {
          case AccountTypes.DIRECT_EFFECTIV.id => (1440, 480)
          case _ => (autoTimeout, sessionIdleTimeout)
        }
        validateTimeouts(autoTimeoutLimit, idleTimeoutLimit) match {
          case Left(errorResponse) =>
            formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), Seq.empty, uIAccountConfigurationRequest) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }
          case Right(_) =>
            saveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest, creator)
        }
      case None =>
        val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), Seq.empty, uIAccountConfigurationRequest) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    }
  }


  def saveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest: AccountsUIConfigurationRequest, creator: Creator): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    (for {
      _ <- v2Validator.validateAccountAccess(uIAccountConfigurationRequest.accountIds, creator.accountId, creator.userId, None)
      currentUiConfigurations <- daoUIAccountConfiguration.getUIAccountsConfiguration(uIAccountConfigurationRequest.accountIds)
      res <- {
        val cacheKeys = uIAccountConfigurationRequest.accountIds map (input => AccountUIConfigurationCacheKeyProvider.provide(input))
        removeCacheKeysWrapped(getUIAccountConfigurationApiName, scalaCache, cacheKeys.toSet)(daoUIAccountConfiguration saveUIAccountsConfiguration uIAccountConfigurationRequest.copy(byAccountId = Some(creator.accountId))
          flatMap {
          case Some(res) if res >= 0 =>
            formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = true, creator, None, currentUiConfigurations, uIAccountConfigurationRequest) map {
              auditDetails => (auditDetails, Right(true))
            }

          case _ =>
            logger.info(s"Could not save Account UI Configuration for account ${creator.accountId}, create $creator")
            val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
            formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), currentUiConfigurations, uIAccountConfigurationRequest) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }
        } recoverWith {
          case e: Exception =>
            logger.info(s"Could not save Account UI Configuration for account ${creator.accountId}, create $creator", e)
            val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
            formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), currentUiConfigurations, uIAccountConfigurationRequest) map {
              auditDetails => (auditDetails, Left(errorResponse))
            }
        })
      } recoverWith {
        case e: Exception =>
          logger.info(s"Could not save Account UI Configuration for account ${creator.accountId}, create $creator", e)
          val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), currentUiConfigurations, uIAccountConfigurationRequest) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    } yield res) recoverWith {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Account UI Configuration for account ${creator.accountId}, create $creator", ex)
        val errorResponse = ex.errorResponse
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), Seq.empty, uIAccountConfigurationRequest) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
      case e: Exception =>
        logger.info(s"Error occurred while fetching account hierarchy by account $creator", e)
        val errorResponse = ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError)
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, Some(errorResponse), Seq.empty, uIAccountConfigurationRequest) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    }
  }

}
