package me.socure.account.service

import java.io.ByteArrayInputStream
import java.nio.charset.StandardCharsets
import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.xml.schema.validator.XmlSchemaValidator
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.model.superadmin.AccountIdpMetadata
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoIdpMetadata}
import me.socure.storage.slick.tables.account.DtoIdpMetadata
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class IdpMetadataService (daoIdpMetadata: DaoIdpMetadata,
                          daoAccount: DaoAccount,
                          daoAccountV2: DaoAccountV2,
                          xmlSchemaValidator: XmlSchemaValidator,
                          accountInfoService: AccountInfoService,
                          clock: Clock)(implicit ec : ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def insertIdpMetadata(accountId: Long, metadata: String): Future[Either[ErrorResponse, Boolean]] = {

    def checkForSAMLProvisioning(): Future[Either[ErrorResponse, Unit]] = {
      daoAccount.getActiveAccountInfo(accountId) flatMap  {
        case Some(accountInfo)  =>
          accountInfoService.hasRole(accountId = accountId, role = BusinessUserRoles.SAML_2_0.id) map {
            case true =>
              Right()
            case _ =>
              logger.info(s"SAML not provisioned for Account: $accountId")
              Left(ErrorResponseFactory.get(SAMLNotEnabled))
          }
        case None =>
          Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
      }
    }

    def validateAndInsert(): Future[Either[ErrorResponse, Boolean]] = {
      xmlSchemaValidator.validateWithFeedback(new ByteArrayInputStream(metadata.getBytes(StandardCharsets.UTF_8))) match {
        case Right(_) =>
          saveMetadata(accountId, metadata)
        case Left(error) =>
          val message = try {
            val entityId = scala.xml.XML.loadString(metadata).attributes("entityID").text
            s"Schema Mapping Error: [Entity: $entityId] $error"
          } catch {
            case ex: Exception =>
              logger.error("Error while parsing the SAML metadata", ex)
              "Invalid SAML metadata: " + ex.getMessage
          }
          Future.successful(Left(ErrorResponseFactory.get(InvalidIdpMetadata.id, message)))
      }
    }

    for {
      provisioningCheck <- checkForSAMLProvisioning()
      result <- provisioningCheck match {
        case Right(_) =>
          validateAndInsert()
        case Left(err) =>
          Future.successful(Left(err))
      }
    } yield result
  }

  private def saveMetadata(accountId: Long, metadata: String): Future[Either[ErrorResponse, Boolean]] = {
    val entityId = scala.xml.XML.loadString(metadata).attributes("entityID").text
    for {
      metadataAlreadyExists <- daoIdpMetadata.doesIdpMetadataExistForEntityId(entityId = entityId)
      result <- {
        if(metadataAlreadyExists) {
          Future.successful(Left(ErrorResponseFactory.get(DuplicateIdpMetadata)))
        } else {
            daoIdpMetadata
              .save(DtoIdpMetadata(id = 0, accountId = accountId, entityId = entityId, metadata = metadata, createdAt = clock.now()))
              .map(c => Right(c > 0))
        }
      }
    } yield result
  }

  def deleteIdpMetadata(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoIdpMetadata.deleteIdpMetadata(accountId).map {
      case a:Int if a > 0 =>
        Right(true)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listIdpMetadata(): Future[Either[ErrorResponse, Seq[AccountIdpMetadata]]] = {
    daoIdpMetadata.listIdpMetadata().map { list =>
      Right(list.map {
        case (idpMetadata, account) =>
          AccountIdpMetadata(accountId = account.accountId, accountName = account.name, createdAt = idpMetadata.createdAt)
      })
    }
  }

  def getIdpMetadata(entityId: String): Future[Either[ErrorResponse, IdpMetadata]] = {
    daoIdpMetadata.getAccountIdpMetadata(entityId) flatMap {
      case Some(accountId) =>
        daoIdpMetadata.getIdpMetadata(entityId) map {
          case Some(emd) => Right(IdpMetadata(accountId, emd))
          case _ => Left(ErrorResponseFactory.get(NoIdpMetadataFound))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(NoIdpMetadataFound)))
    }
  }

  def getIdpMetadata(accountId: Long): Future[Either[ErrorResponse, IdpMetadata]] = {
    // we have to get the immediate parent idp metadata of this account
    daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
      case Some(hierarchy) =>
        val accountIds = hierarchy.hierarchyPath.split("/").map(_.toLong).toList.reverse
        getIdpMetadata(accountIds)
      case _ => getIdpMetadata(List(accountId))
    }
  }

  private def getIdpMetadata(accountIds: List[Long]): Future[Either[ErrorResponse, IdpMetadata]] = {
    if(accountIds.isEmpty)
      Future.successful(Left(ErrorResponseFactory.get(NoIdpMetadataFound)))
    else {
      daoIdpMetadata.getIdpMetadata(accountIds.head) flatMap {
        case Some(emd) => Future.successful(Right(IdpMetadata(accountIds.head, emd)))
        case _ => getIdpMetadata(accountIds.tail)
      }
    }
  }
}
