package me.socure.account.service

import me.socure.account.service.common.Constant
import me.socure.util.ProductSettingsDeltaUtil.{formProductSettingsDeltaMapForField, getImplicits}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CAWlLimitOutOfRange, CAWlMatchScoreNotInRange, InvalidHistoricalRange, UnknownError, WlMatchScoreOutOfRange}
import me.socure.account.service.common.watchlist.{HistoricalRange, ManageCAAccountsCacheKeyProvider}
import me.socure.account.util.CacheUtil._
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.constants.ProductSettingsFields._
import me.socure.constants.{DashboardUserPermissions, ProductSettingsFields}
import me.socure.model._
import me.socure.model.account._
import me.socure.model.account.watchlist.{CAWatchlistPreferenceForAccount, CAWatchlistPreferenceView}
import me.socure.model.dashboardv2.{AuditDetails, Creator, ProductSettingDelta}
import me.socure.model.kyc.KycNationalIdMatchLogic.KycNationalIdMatchLogicEnum
import me.socure.model.kyc._
import me.socure.service.constants.{Countries, DobMatchLogic}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoComplyWatchlistPreferences, DaoKycPreferences, DaoWatchlistPreferences}
import me.socure.storage.slick.tables.account.mappers.{CAWatchlistPreferenceMapper, KycPreferencesMapper, ProductSettingTracesMapper, WatchlistPreferenceMapper}
import me.socure.storage.slick.tables.account.{DtoAccountKycPreference, DtoCAAccountWatchlistPreference, DtoProductSettingChangeRequest, DtoProductSettingTraces}
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

class AccountPreferencesService(daoAccount : DaoKycPreferences,
                                daoAccountV2: DaoAccountV2,
                                watchlistPreference: DaoWatchlistPreferences,
                                watchlistPreferenceMapper: WatchlistPreferenceMapper,
                                daoComplyWatchlistPreferences: DaoComplyWatchlistPreferences,
                                caWatchlistPreferenceMapper: CAWatchlistPreferenceMapper,
                                v2Validator: V2Validator,
                                scalaCache: ScalaCache[_],
                                auditDetailsService: AuditDetailsService)(implicit ec: ExecutionContext) {
  val logger : Logger = LoggerFactory.getLogger(this.getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass)
  val cawatchlistApi = "/settings/preferences/ca/watchlist/3.0"

  private val defaultWatchlistPreference = WatchlistPreference(
    environmentId = 0,
    exactDoB = true,
    dobAndName = false,
    categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP),
    matchScore = 90
  )

  private val defaultKycPreferences: KycPreferences = KycPreferences(
    exactDob = Option(true),
    dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
    ssnExactMatch = false,
    nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy)
  )

  private val defaultKycPreferencesResponse: KycPreferencesResponse = KycPreferencesResponse(
    kycPreferences = KycPreferences(
      exactDob = Option(true),
      dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
      ssnExactMatch = false,
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy)
    )
  )

  private val caDefaultWatchlistPreference = CAWatchlistPreference(
    environmentId = 0,
    exactDoB = Some(true),
    dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
    dobAndName = false,
    monitoring = false,
    matchingThresholds = 0.5,
    limit = 10,
    screeningCategories = Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP),
    watchlistScreeningCategories = Option(Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP)),
    country = Option(Set.empty),
    historicalRange = None
  )

  private val caDefaultWatchlistPreferenceTiers = CAWatchlistPreferences(
    standard = null,
    plus = null,
    premier = null
  )


  private val wlMatchScoreMin = 70
  private val wlMatchScoreMax = 100

  private val caWLMatchScoreSet = Set(0,0.1,0.2,0.3,0.4,0.5,0.6,0.7,0.8,0.9,1)

  private val caWLLimitMin = 1
  private val caWLLimitMax = 100

  def getKycPreferenceForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[KycPreferencesForAccount]]] = {
    daoAccount.getKycPreferencesForAccount(accountId = accountId) map {
      case kycPreferences if kycPreferences.nonEmpty =>
        val toModelList = kycPreferences.map{ kp => (kp._1, kp._2.map(KycPreferencesMapper.toModel).getOrElse(defaultKycPreferences))}
        Right(
          toModelList
            .map(
              p => KycPreferencesForAccount(
                p._1,
                KycPreferencesView(
                  p._2.exactDob,
                  p._2.dobMatchLogic,
                  p._2.ssnExactMatch,
                  p._2.nationalIdMatchLogic.getOrElse(KycNationalIdMatchLogic.exact).toString,
                  p._2.addressMatchLogic.getOrElse(KycAddressMatchLogic.default).toString
                )
              )
            )
        )
      case _ =>
        logger.info(s"Could not get kyc preference for account($accountId)")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not get kyc preference for account($accountId)", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not get kyc preference for account($accountId)", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getKycPreference(environmentId: Long): Future[Either[ErrorResponse, KycPreferences]] = {
    daoAccount.getKyc(environmentId = environmentId) map {
      case Some(dobResult) =>
        Right(KycPreferencesMapper.toModel(dobResult))
      case None =>
        metrics.increment ("defaultKycPreferences")
        Right(defaultKycPreferences)
    }
  }



  def getKycPreference(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, KycPreferences]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true =>
        getKycPreference(environmentId)
      case _ =>
        logger.info("Could not get kyc preference")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get kyc preference", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not get kyc preference", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def saveKycPreference(environmentId: Long, kycPreferences: KycPreferences): Future[(AuditDetails, Either[ErrorResponse, KycPreferences])] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, kycPreferences.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true =>

        daoAccount.getKyc(environmentId = environmentId) flatMap {
          currentKyc =>
            daoAccount.deleteKyc(environmentId = environmentId)
            val kycPreferencesForAccounts: KycPreferencesForAccounts = KycPreferencesMapper.toDto(Seq(environmentId), KycPreferencesRequest(kycPreferences, Seq.empty, Seq.empty, None))
            daoAccount.saveKyc(KycPreferencesMapper.toDto(environmentId = environmentId, kycPreferences)) flatMap {
              insertedRows =>
                if (insertedRows == 1) formAuditDetailsWithProductSettingDeltaForAccounts(true, kycPreferences.creator, None, currentKyc.map(kyc => Seq(kyc)).getOrElse(Seq.empty), Some(kycPreferencesForAccounts), Seq.empty, None, Seq(environmentId)) map {
                  auditDetails => (auditDetails, Right(kycPreferences))
                } else {
                  val errorResponse = ErrorResponse(UnknownError.id, UnknownError.description)
                  formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, kycPreferences.creator, Some(errorResponse), currentKyc.map(kyc => Seq(kyc)).getOrElse(Seq.empty), Some(kycPreferencesForAccounts), Seq.empty, None, Seq(environmentId)) map {
                    auditDetails => (auditDetails, Left(errorResponse))
                  }
                }
            }

        }

      case _ =>
        logger.info("Could not save kyc preference")
        val errorResponse = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq(environmentId)) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    } recoverWith {
      case ex: ErrorResponseException =>
        logger.info("Could not save kyc preference", ex)
        val errorResponse = ex.errorResponse
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq(environmentId)) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }

      case e: Throwable =>
        logger.info("Could not save kyc preference", e)
        val errorResponse = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq(environmentId)) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    }
  }

  def getKycPreferenceForAccounts(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, KycPreferencesResponse]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true =>
        daoAccount.getKycWithCount(environmentId = environmentId, creator.map(_.accountId).get) map {
          case dobResult if dobResult._1.nonEmpty =>
            Right(KycPreferencesMapper.toModel(dobResult._1.get, dobResult._2))
          case _ =>
            metrics.increment("defaultKycPreferences")
            Right(defaultKycPreferencesResponse)
        }
      case _ =>
        logger.info("Could not get kyc preference")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get kyc preference", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not get kyc preference", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def formProductSettingsDeltaWithKycPreference(accountId: Option[Long], environmentId: Option[Long], oldKycPreference: Option[KycPreferences], newKycPreference: KycPreferences): Seq[ProductSettingDelta] = {
    implicit val (implicit_1, _, _) = getImplicits

    def getNationalIdMatchLogicDisplayName(valueOpt: Option[KycNationalIdMatchLogicEnum]): String = {
      valueOpt match {
        case Some(value) => value.toString.capitalize
        case _ => ""
      }
    }

    val (oldDob, oldSsn, oldAddress) = oldKycPreference.map(
      kyc => (
        kyc.dobMatchLogic.map(dobLogic => ProductSettingsFields.getFormattedDateRange(dobLogic)).getOrElse(""),
        getNationalIdMatchLogicDisplayName(kyc.nationalIdMatchLogic),
        kyc.addressMatchLogic.map(_.toString).getOrElse(""))
    ).getOrElse("", "", "")

    val (newDob, newSsn, newAddress) = (
      newKycPreference.dobMatchLogic.map(dob => ProductSettingsFields.getFormattedDateRange(dob)).getOrElse(""),
      getNationalIdMatchLogicDisplayName(newKycPreference.nationalIdMatchLogic),
      newKycPreference.addressMatchLogic.map(_.toString).getOrElse("")
    )

    val productSettingsDeltaMap: Map[String, (String, String)] =
      formProductSettingsDeltaMapForField(KYC_DOB, oldDob, newDob)(implicit_1) ++
        formProductSettingsDeltaMapForField(KYC_SSN, oldSsn, newSsn)(implicit_1) ++
        formProductSettingsDeltaMapForField(KYC_ADDRESS, oldAddress, newAddress)(implicit_1)



    auditDetailsService.formProductSettingsDelta(accountId, environmentId, KYC, productSettingsDeltaMap)
  }

  def formProductSettingsDeltaWithWatchlist(accountId: Option[Long], environmentId: Option[Long], oldWatchlist: Option[DtoCAAccountWatchlistPreference], newWatchlist: CAWatchlistPreference): Seq[ProductSettingDelta] = {
    implicit val implicit_1 = getImplicits._1
    implicit val implicit_3 = getImplicits._3
    def getEnabledOrDisabled(boolean: Boolean): String = {
      boolean match {
        case true => "Enabled"
        case false => "Disabled"
      }
    }

    def getCountryName(countryCode: String): String = {
      Countries.byCode2(countryCode) match {
        case Some(countryName) =>
          countryName.name
        case None => ProductSettingsFields.getCountryName(countryCode)
      }
    }

    def formatStringForCategories(category: String): String = {
      category.replaceAll("-", " ").split("\\s+").map(_.capitalize).mkString(" ")
    }

    val productSettingsDeltaMap: Map[String, (String, String)] =
      formProductSettingsDeltaMapForField(WATCHLIST_DOB_NAME, oldWatchlist.map(watchlist => getEnabledOrDisabled(watchlist.dobAndName)).getOrElse(""), getEnabledOrDisabled(newWatchlist.dobAndName))(implicit_1) ++
        formProductSettingsDeltaMapForField(WATCHLIST_NAME_FUZZY, oldWatchlist.map(watchlist => (watchlist.matchingThresholds * 100).toString).getOrElse(""), (newWatchlist.matchingThresholds * 100).toString)(implicit_1) ++
        formProductSettingsDeltaMapForField(WATCHLIST_AUTO_MONITOR, oldWatchlist.map(watchlist => watchlist.isWatchlistTransactionsAutoMonitored.map(monitor => getEnabledOrDisabled(monitor))).flatten.getOrElse(""), newWatchlist.isWatchlistTransactionsAutoMonitored.map(monitor => getEnabledOrDisabled(monitor)).getOrElse(""))(implicit_1) ++
        formProductSettingsDeltaMapForField(WATCHLIST_ENTITIES_LIMIT, oldWatchlist.map(watchlist => watchlist.limit.toString).getOrElse(""), newWatchlist.limit.toString)(implicit_1) ++
        formProductSettingsDeltaMapForField(WATCHLIST_SUPPRESS_PEPS, oldWatchlist.map(watchlist => watchlist.suppressPepsWithoutURL.map(peps => getEnabledOrDisabled(peps))).flatten.getOrElse(""), newWatchlist.suppressPepsWithoutURL.map(peps => getEnabledOrDisabled(peps)).getOrElse(""))(implicit_1) ++
        formProductSettingsDeltaMapForField(WATCHLIST_DOB_TOLERANCE, oldWatchlist.map(watchlist => watchlist.dobMatchLogic.map(tolerance => getFormattedDateRange(tolerance))).flatten.getOrElse(""), newWatchlist.dobMatchLogic.map(tolerance => getFormattedDateRange(tolerance)).getOrElse("")) (implicit_1)++
        formProductSettingsDeltaMapForField(WATCHLIST_SOURCE_COUNTRIES, oldWatchlist.map(watchlist => watchlist.country.map(countryCode => countryCode.split(",").toSet.filter(_.nonEmpty).map(country => getCountryName(country.trim)))).flatten.getOrElse(Set.empty), newWatchlist.country.map(countryCode => countryCode.split(",").toSet.filter(_.nonEmpty).map(country => getCountryName(country.trim))).getOrElse(Set.empty))(implicit_3) ++
        formProductSettingsDeltaMapForField(WATCHLIST_CATEGORIES, oldWatchlist.map(watchlist => watchlist.screeningCategories.map(screening => screening.split(",").map(category => formatStringForCategories(category)).toSet)).flatten.getOrElse(Set.empty), newWatchlist.screeningCategories.map(category => formatStringForCategories(category)))(implicit_3) ++
        formProductSettingsDeltaMapForField(WATCHLIST_HISTORICAL_RANGE, oldWatchlist.map(watchlist => watchlist.historicalRange).flatten.getOrElse(""), newWatchlist.historicalRange.getOrElse(""))(implicit_1)


    auditDetailsService.formProductSettingsDelta(accountId, environmentId, "Watchlist", productSettingsDeltaMap)
  }

  private def formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess: Boolean,
                                                                 creator: Option[Creator],
                                                                 errorResponse: Option[ErrorResponse],
                                                                 dtoAccountKycPreference: Seq[DtoAccountKycPreference],
                                                                 kycPreferencesRequest: Option[KycPreferencesForAccounts],
                                                                 currentWatchListSeq: Seq[DtoCAAccountWatchlistPreference],
                                                                 watchlistPreference: Option[CAWatchlistPreference], environmentIds: Seq[Long]): Future[AuditDetails] = {


    def formProductSettingDeltaWithWatchlistForAccounts(accountId: Option[Long], currentWatchlistSeq: Seq[DtoCAAccountWatchlistPreference],
                                                        watchlistPreferenceRequest: CAWatchlistPreference, environmentIds: Seq[Long]): Seq[ProductSettingDelta] = {
      environmentIds.flatMap {
        envId =>
          val previousPreference: Option[DtoCAAccountWatchlistPreference] = currentWatchlistSeq.find(currentWatchlist => currentWatchlist.environmentId == envId) match {
            case Some(dtoAccountKycPreference) => Some(dtoAccountKycPreference)
            case None => None
          }

          formProductSettingsDeltaWithWatchlist(accountId, Some(envId), oldWatchlist = previousPreference, newWatchlist = watchlistPreferenceRequest)
      }
    }

    def formProductSettingDeltaWithKycForAccounts(accountId: Option[Long], dtoAccountKycPreferences: Seq[DtoAccountKycPreference], kycPreferencesRequest: KycPreferencesForAccounts): Seq[ProductSettingDelta] = {
      kycPreferencesRequest.environmentIds.flatMap {
        envId =>
          val previousPreference: Option[KycPreferences] = dtoAccountKycPreferences.find(currentKyc => currentKyc.environmentId == envId) match {
            case Some(dtoAccountKycPreference) => Some(
              KycPreferences(
                dtoAccountKycPreference.exactDob,
                dtoAccountKycPreference.dobMatchLogic,
                dtoAccountKycPreference.ssnExactMatch,
                dtoAccountKycPreference.nationalIdMatchLogic,
                None,
                dtoAccountKycPreference.addressMatchLogic
              )
            )
            case None =>
              //default value
              Some(defaultKycPreferences)

          }
          val newPreference = KycPreferences(kycPreferencesRequest.exactDob, kycPreferencesRequest.dobMatchLogic, kycPreferencesRequest.ssnExactMatch, kycPreferencesRequest.nationalIdMatchLogic, None, kycPreferencesRequest.addressMatchLogic)

          formProductSettingsDeltaWithKycPreference(accountId, Some(envId), previousPreference, newPreference)
      }
    }


    def formAuditDetails(accountId: Option[Long]): Future[AuditDetails] = {
      val productSettingsKYC = kycPreferencesRequest match {
        case Some(kycPreferencesRequest) =>
          formProductSettingDeltaWithKycForAccounts(accountId, dtoAccountKycPreference, kycPreferencesRequest)
        case None => Seq.empty
      }
      val productSettingsWatchlist = watchlistPreference match {
        case Some(watchlistPreference) => formProductSettingDeltaWithWatchlistForAccounts(accountId, currentWatchListSeq, watchlistPreference, environmentIds)
        case None => Seq.empty
      }

      auditDetailsService.formAuditDetails(isSuccess, creator, None, errorResponse, productSettingsKYC ++ productSettingsWatchlist)

    }

    formAuditDetails(creator.map(_.accountId))
  }

  def saveKycPreferenceForAccounts(environmentId: Long, kycPreferencesRequest: KycPreferencesRequest): Future[(AuditDetails, Either[ErrorResponse, KycPreferencesRequest])] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, kycPreferencesRequest.kycPreferences.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoAccountV2.getEnvironmentIds(kycPreferencesRequest.environmentTypes, kycPreferencesRequest.accountIds) flatMap {
        case environmentIds: Seq[Long] =>
          daoAccount.getKycForEnvironmentIds(environmentIds).flatMap {
            kycPreferences =>
              val kycPreferencesForAccounts: KycPreferencesForAccounts = KycPreferencesMapper.toDto(environmentIds, kycPreferencesRequest)
              daoAccount.saveKycForAccounts(kycPreferencesForAccounts) flatMap {

                case Some(res) if res > 0 =>
                  formAuditDetailsWithProductSettingDeltaForAccounts(true,
                    kycPreferencesRequest.kycPreferences.creator, None, kycPreferences,
                    Some(kycPreferencesForAccounts), Seq.empty, None, environmentIds) map {
                    auditDetails =>
                      (auditDetails, Right(kycPreferencesRequest))
                  }
                case _ => logger.info(s"Could not save kyc preference")
                  val errorResponse = ErrorResponse(UnknownError.id, UnknownError.description)
                  formAuditDetailsWithProductSettingDeltaForAccounts(false,
                    kycPreferencesRequest.kycPreferences.creator, Some(errorResponse),
                    kycPreferences, Some(kycPreferencesForAccounts), Seq.empty, None, environmentIds) map {
                    auditDetails => (auditDetails, Left(errorResponse))
                  }
              }
          }

        case _ => logger.info(s"Could not fetch EnvironmentIds")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(false,
            kycPreferencesRequest.kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq.empty) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }

      }
      case _ =>
        logger.info("Could not save kyc preference")
        val errorResponse = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDeltaForAccounts(false,
          kycPreferencesRequest.kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq.empty) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    } recoverWith {
      case ex: ErrorResponseException =>
        logger.info("Could not save kyc preference", ex)
        formAuditDetailsWithProductSettingDeltaForAccounts(false,
          kycPreferencesRequest.kycPreferences.creator, Some(ex.errorResponse), Seq.empty, None, Seq.empty, None, Seq.empty) map {
          auditDetails => (auditDetails, Left(ex.errorResponse))
        }
      case e: Throwable =>
        logger.info("Could not save kyc preference", e)
        val errorResponse = ErrorResponseFactory.get(UnknownError)
        formAuditDetailsWithProductSettingDeltaForAccounts(false,
          kycPreferencesRequest.kycPreferences.creator, Some(errorResponse), Seq.empty, None, Seq.empty, None, Seq.empty) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }
    }
  }

  def getProductSettingTraces(productSettingTracesFilter: ProductSettingTracesFilter): Future[Either[ErrorResponse, ProductSettingTracesResponse]] = {
    daoAccount.getProductSettingTracesFilter(productSettingTracesFilter) flatMap  {
      case (dbResults, totalCount, filteredCount) =>
        if (productSettingTracesFilter.page.isDefined && productSettingTracesFilter.size.isDefined) {
          val page = productSettingTracesFilter.page.get
          val size = productSettingTracesFilter.size.get
          val filterDaoResult = dbResults.slice((page - 1) * size, (page - 1) * size + size)
          val productSettingTracesList = filterDaoResult.map(sr => ProductSettingTracesMapper.toProductSettingTracesModel(sr._1, sr._2))
          Future.successful(Right(ProductSettingTracesResponse(productSettingTraces = productSettingTracesList, totalCount = totalCount, filteredCount = filteredCount)))
        } else {
          val productSettingTracesList = dbResults.map(sr => ProductSettingTracesMapper.toProductSettingTracesModel(sr._1, sr._2))
          Future.successful(Right(ProductSettingTracesResponse(productSettingTraces = productSettingTracesList, totalCount = totalCount, filteredCount = filteredCount)))
        }
      case _ =>
        logger.info("Could not fetch product setting request list")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  def getProductSettingTracesById(id: Long): Future[Either[ErrorResponse, ProductSettingTraces]] = {
    daoAccount.getProductSettingTracesById(id) flatMap   {
        case Some(dobResult) => Future.successful(Right(ProductSettingTracesMapper.toProductSettingTracesModel(dobResult._1, dobResult._2)))
        case _ =>
          logger.info("Could not fetch product setting request")
          Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      }
  }

  def getProductSettingAuditLogByPstId(id: Long): Future[Either[ErrorResponse, ProductSettingAuditResponse]] = {
    daoAccount.getProductSettingAuditLog(id) map {
      case dtoResult =>  Right(ProductSettingAuditResponse(audits = dtoResult.map(al => ProductSettingTracesMapper.toProductSettingAuditLog(al)) ))
      case _ => logger.info("Could not fetch product setting request")
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def saveAndUpdateProductSettingTraces(productSettingTraces: ProductSettingTraces): Future[Either[ErrorResponse, ProductSettingTraces]] = {
    if(productSettingTraces.state == ProductSettingState.ACTIVE.id){
      daoAccount.archivedProductSettingRequest(productSettingTraces.programId, productSettingTraces.createdBy).flatMap {
        case res if res >= 0 => saveProductSettingTraces(productSettingTraces)
        case _=>
          logger.info(s"Could not save product setting change request")
          Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      }
    } else {
      saveProductSettingTraces(productSettingTraces)
    }
  }

  def saveProductSettingTraces(productSettingTraces: ProductSettingTraces): Future[Either[ErrorResponse, ProductSettingTraces]] = {
    daoAccount.saveProductSettingTraces(ProductSettingTracesMapper.toDtoProductSettingTraces(productSettingTraces)).flatMap  {
      case dtoProductSettingTraces: DtoProductSettingTraces =>
        if(productSettingTraces.changeRequest.isDefined){
          val dtoProductSettingChangeRequest = ProductSettingTracesMapper.toDtoProductSettingChangeRequest(productSettingTraces.changeRequest.get)
          val dtoProductSettingAuditLog = ProductSettingTracesMapper.toDtoProductSettingAuditLog(dtoProductSettingTraces.id, dtoProductSettingTraces.state, dtoProductSettingTraces.lastUpdatedBy)
          daoAccount.saveProductSettingChangeRequestAndAudit(dtoProductSettingChangeRequest, dtoProductSettingAuditLog).flatMap {
            case dtoProductSettingChangeRequest: DtoProductSettingChangeRequest => Future.successful(Right(ProductSettingTracesMapper.toProductSettingTracesModel(dtoProductSettingTraces, Some(dtoProductSettingChangeRequest))))
            case e => logger.info(s"Could not save product setting change request ${e}")
              Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
          }
        } else {
          daoAccount.saveProductSettingAuditLog(ProductSettingTracesMapper.toDtoProductSettingAuditLog(dtoProductSettingTraces.id, dtoProductSettingTraces.state, dtoProductSettingTraces.lastUpdatedBy)).flatMap {
            case res if res > 0 => Future.successful(Right(ProductSettingTracesMapper.toProductSettingTracesModel(dtoProductSettingTraces)))
            case e =>
              logger.info(s"Could not save audit log for setting request ${e}")
              Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
          }
        }
      case e =>
        logger.info(s"Could not save product setting traces ${e}")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  def deleteKycPreference(environmentId: Long): Future[Either[ErrorResponse, Boolean]] = {
    for {
      numDeletedRows <- daoAccount.deleteKyc(environmentId = environmentId)
    } yield Right(numDeletedRows == 1)
  }

  def getWatchlistPreference(environmentId: Long): Future[Either[ErrorResponse, WatchlistPreference]] = {
    watchlistPreference.getWatchlist(environmentId).map {
      case Some(preference) => Right(watchlistPreferenceMapper.toModel(preference))
      case None => Right(defaultWatchlistPreference.copy(environmentId = environmentId))
    }
  }

  def getWatchlistPreference(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, WatchlistPreference]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true =>
        getWatchlistPreference(environmentId)
      case _ =>
        logger.info("Could not get watchlist preference")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get watchlist preference", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not get watchlist preference", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def saveWatchlistPreference(preference: WatchlistPreference): Future[Either[ErrorResponse, WatchlistPreference]] = {
    if(preference.matchScore < wlMatchScoreMin || preference.matchScore > wlMatchScoreMax) {
      Future.successful(Left(ErrorResponseFactory.get(WlMatchScoreOutOfRange)))
    } else {
      val dto = watchlistPreferenceMapper.toDto(preference)
      v2Validator.isValidV2EnvironmentRequest(preference.environmentId, preference.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true => watchlistPreference.saveWatchlist(dto).map {
          case 1 => Right(preference)
          case _ => Left(ErrorResponseFactory.get(UnknownError))
        }
        case _ => logger.info("Could not Save watchlist preference")
          Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      } recover {
        case ex: ErrorResponseException =>
          logger.info("Could not Save watchlist preference", ex)
          Left(ex.errorResponse)
        case e : Throwable =>
          logger.info("Could not Save watchlist preference", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
    }
  }

  def getCAWatchListPreferenceForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[CAWatchlistPreferenceForAccount]]] = {
    daoComplyWatchlistPreferences.getWatchlistPreferencesForAccount(accountId).map {
      case caWatchlistPreferences if caWatchlistPreferences.nonEmpty =>
        val toModel = caWatchlistPreferences.map{wlp => (wlp._1, toCaWatchlistPreference(wlp._3, wlp._2))}
        Right(toModel.map(v => CAWatchlistPreferenceForAccount(v._1,
                                  CAWatchlistPreferenceView(isCategoryDefault = v._2.isCategoryDefault,
                                    environmentId = v._2.environmentId,
                                    exactDoB = v._2.exactDoB,
                                    dobMatchLogic = v._2.dobMatchLogic,
                                    dobAndName = v._2.dobAndName,
                                    monitoring = v._2.monitoring,
                                    matchingThresholds = v._2.matchingThresholds,
                                    limit = v._2.limit,
                                    screeningCategories = v._2.screeningCategories,
                                    watchlistScreeningCategories = v._2.watchlistScreeningCategories,
                                    country = v._2.country,
                                    historicalRange = v._2.historicalRange,
                                    isWatchlistTransactionsAutoMonitored = v._2.isWatchlistTransactionsAutoMonitored))))
      case _ =>
        logger.info(s"Could not get ca watchlist preference for account($accountId)")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not get ca watchlist preference for account($accountId)", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not get watchlist preference for account($accountId)", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def toCaWatchlistPreference(preference: Option[DtoCAAccountWatchlistPreference], envId: Long): CAWatchlistPreference = {
    preference match {
      case Some(pref) => caWatchlistPreferenceMapper.toModel(pref)
      case None => caDefaultCAWatchlistPreference(envId).plus
    }
  }

  def getCAWatchListPreference(environmentId: Long, needCustomWatchlistCategory: Boolean = true): Future[Either[ErrorResponse, CAWatchlistPreference]] = {
    daoComplyWatchlistPreferences.getWatchlistAllTiers(environmentId).map {
      case Some(preference) => Right(caWatchlistPreferenceMapper.toModel(preference, needCustomWatchlistCategory))
      case None => Right(caDefaultWatchlistPreference.copy(environmentId = environmentId))
    }
  }

  def getCAWatchListPreference(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, CAWatchlistPreference]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true =>
        getCAWatchListPreference(environmentId)
      case _ =>
        logger.info("Could not get ca watchlist preference")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get ca watchlist preference", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not get watchlist preference", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def caDefaultCAWatchlistPreference(environmentId: Long) = {
    caDefaultWatchlistPreferenceTiers.copy(
      standard = CAWatchlistPreference(
        isCategoryDefault = Some(true),
        environmentId = environmentId,
        exactDoB = Some(Constant.WL_DEFAULT_EXACT_DOB),
        dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
        dobAndName = Constant.WL_DEFAULT_DOB_AND_NAME,
        monitoring = Constant.WL_DEFAULT_MONITORING,
        matchingThresholds = Constant.WL_DEFAULT_FUZZINESS,
        limit = Constant.WL_DEFAULT_LIMIT,
        screeningCategories = Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY),
        watchlistScreeningCategories = Option(Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY)),
        country = Option(Set.empty),
        historicalRange = None
      ),
      plus = CAWatchlistPreference(
        isCategoryDefault = Some(true),
        environmentId = environmentId,
        exactDoB = Some(Constant.WL_DEFAULT_EXACT_DOB),
        dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
        dobAndName = Constant.WL_DEFAULT_DOB_AND_NAME,
        monitoring = Constant.WL_DEFAULT_MONITORING,
        matchingThresholds = Constant.WL_DEFAULT_FUZZINESS,
        limit = Constant.WL_DEFAULT_LIMIT,
        screeningCategories = Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP),
        watchlistScreeningCategories = Option(Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP)),
        country = Option(Set.empty),
        historicalRange = None
      ),
      premier = CAWatchlistPreference(
        isCategoryDefault = Some(true),
        environmentId = environmentId,
        exactDoB = Some(Constant.WL_DEFAULT_EXACT_DOB),
        dobMatchLogic = Some(DobMatchLogic.FUZZY_MATCH.toString),
        dobAndName = Constant.WL_DEFAULT_DOB_AND_NAME,
        monitoring = Constant.WL_DEFAULT_MONITORING,
        matchingThresholds = Constant.WL_DEFAULT_FUZZINESS,
        limit = Constant.WL_DEFAULT_LIMIT,
        screeningCategories = Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP, Constant.ADVERSE_MEDIA),
        watchlistScreeningCategories = Option(Set(Constant.WARNING, Constant.SANCTION, Constant.FITNESS_PROBITY, Constant.PEP, Constant.ADVERSE_MEDIA)),
        country = Option(Set.empty),
        historicalRange = None
      )
    )
  }

  def getCAWatchListPreferences(environmentId: Long, needCustomWatchlistCategory: Boolean = true): Future[Either[ErrorResponse, CAWatchlistPreferences]] = {
    daoComplyWatchlistPreferences.getWatchlistAllTiers(environmentId).map {
      case Some(preference) => Right(caWatchlistPreferenceMapper.toCAModel(preference, needCustomWatchlistCategory))
      case None => Right(caDefaultCAWatchlistPreference(environmentId))
    }
  }

  def getCAWatchListPreferences(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, CAWatchlistPreferences]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id), Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id)) flatMap {
      case true =>
        getCAWatchListPreferences(environmentId)
      case _ =>
        logger.info("Could not get ca watchlist preferences")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get ca watchlist preferences", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not get watchlist preferences", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getCAWatchListPreferencesForAccounts(environmentId: Long, creator: Option[Creator]): Future[Either[ErrorResponse, CAWatchlistPreferencesResponse]] = {
    v2Validator.isValidV2EnvironmentRequest(environmentId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id), Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id)) flatMap {
      case true =>
        daoComplyWatchlistPreferences.getWatchlistAllTiersWithCount(environmentId, creator.get.accountId) map ( dtoResult =>
          if (dtoResult._1.isEmpty) {
            Right(CAWatchlistPreferencesResponse(caDefaultCAWatchlistPreference(environmentId).plus))
          }
          else {
            val cAWatchlistPreferences = caWatchlistPreferenceMapper.toCAModel(dtoResult._1.get)
            Right(CAWatchlistPreferencesResponse(
              cAWatchlistPreferences.plus,
              byAccountId = dtoResult._1.get.byAccountId,
              isForceInherit = dtoResult._1.get.isForceInherit,
              inheritedToSubAccountsCount =  Some(dtoResult._2)
            ))
          }
          )
      case _ =>
        logger.info("Could not get ca watchlist preferences")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not get ca watchlist preferences", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not get watchlist preferences", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def saveCAWatchlistPreference(preference: CAWatchlistPreference): Future[(AuditDetails, Either[ErrorResponse, CAWatchlistPreference])] = {
    if(!caWLMatchScoreSet.contains(preference.matchingThresholds)) {
      Future.successful(Left(ErrorResponseFactory.get(CAWlMatchScoreNotInRange)))
      val errorResponse = ErrorResponseFactory.get(CAWlMatchScoreNotInRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else if(preference.limit < caWLLimitMin || preference.limit > caWLLimitMax) {
      val errorResponse = ErrorResponseFactory.get(CAWlLimitOutOfRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else if(preference.historicalRange.isDefined && HistoricalRange.byName(preference.historicalRange.get).isEmpty) {
      val errorResponse = ErrorResponseFactory.get(InvalidHistoricalRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else {
      val dto = caWatchlistPreferenceMapper.toDto(preference)
      v2Validator.isValidV2EnvironmentRequest(preference.environmentId, preference.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true =>
          daoComplyWatchlistPreferences.getWatchlistAllTiers(preference.environmentId) flatMap {
            watchlist =>
              val cacheKeys = Set(ManageCAAccountsCacheKeyProvider provideForGetCAWatchList preference.environmentId)
              val currentWatchlist = watchlist match {
                case Some(currentWatchlist) => Seq(currentWatchlist)
                case None => Seq.empty
              }
              removeCacheKeysWrapped(cawatchlistApi, scalaCache, cacheKeys)(daoComplyWatchlistPreferences saveWatchlist dto) flatMap {
                case 1 =>

                  formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
                    None, Seq.empty, None, currentWatchlist, Some(preference), Seq(preference.environmentId)) map {
                    auditDetails => (auditDetails, Right(caWatchlistPreferenceMapper.toModel(dto)))
                  }
                case _ =>
                  val errorResponse = ErrorResponseFactory.get(UnknownError)
                  formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
                    Some(errorResponse), Seq.empty, None, currentWatchlist, Some(preference), Seq(preference.environmentId)) map {
                    auditDetails => (auditDetails, Left(errorResponse))
                  }
              }

          }

        case _ => logger.info("Could not Save CAWatchlist preference")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Save CAWatchlist preference", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e : Throwable =>
          logger.info("Could not Save CAWatchlist preference", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference), Seq(preference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    }
  }

  def saveCAWatchlistPreference(preference: CAWatchlistPreferenceRequest): Future[(AuditDetails, Either[ErrorResponse, CAWatchlistPreference])] = {
    if (!caWLMatchScoreSet.contains(preference.cAWatchlistPreference.matchingThresholds)) {
      val errorResponse = ErrorResponseFactory.get(CAWlMatchScoreNotInRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else if (preference.cAWatchlistPreference.limit < caWLLimitMin || preference.cAWatchlistPreference.limit > caWLLimitMax) {
      val errorResponse = ErrorResponseFactory.get(CAWlLimitOutOfRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else if (preference.cAWatchlistPreference.historicalRange.isDefined && HistoricalRange.byName(preference.cAWatchlistPreference.historicalRange.get).isEmpty) {
      val errorResponse = ErrorResponseFactory.get(InvalidHistoricalRange)
      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
        Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
        auditDetails => (auditDetails, Left(errorResponse))
      }
    } else {
      val dto = caWatchlistPreferenceMapper.toDto(preference.cAWatchlistPreference)
      val dtoCAAccountWatchlistPreference = dto.copy(byAccountId = preference.cAWatchlistPreference.creator.map(_.accountId), isForceInherit = preference.isForceInherit)
      v2Validator.isValidV2EnvironmentRequest(preference.cAWatchlistPreference.environmentId, preference.cAWatchlistPreference.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
        case true =>
          daoComplyWatchlistPreferences.getEnvironmentIds(preference.environmentTypes, preference.accountIds) flatMap {
            case environmentIds: Seq[Long] =>
              daoComplyWatchlistPreferences.getListWatchlistAllTiers(environmentIds) flatMap {
                currentWatchListSeq =>
                  val cacheKeys = Set(ManageCAAccountsCacheKeyProvider provideForGetCAWatchList preference.cAWatchlistPreference.environmentId)
                  removeCacheKeysWrapped(cawatchlistApi, scalaCache, cacheKeys)(daoComplyWatchlistPreferences saveWatchlist(dtoCAAccountWatchlistPreference, environmentIds)) flatMap {
                    case Some(res) if res > 0 =>
                      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = true, preference.cAWatchlistPreference.creator,
                        None, Seq.empty, None, currentWatchListSeq, Some(preference.cAWatchlistPreference), environmentIds) map {
                        auditDetails => (auditDetails, Right(caWatchlistPreferenceMapper.toModel(dtoCAAccountWatchlistPreference)))
                      }

                    case _ => logger.info("Could not Save CAWatchlist preference")
                      val errorResponse = ErrorResponseFactory.get(UnknownError)
                      formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
                        Some(errorResponse), Seq.empty, None, currentWatchListSeq, Some(preference.cAWatchlistPreference), environmentIds) map {
                        auditDetails => (auditDetails, Left(errorResponse))
                      }
                  }
              }

            case _ => logger.info("Could not Save CAWatchlist preference")
              val errorResponse = ErrorResponseFactory.get(UnknownError)
              formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
                Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
                auditDetails => (auditDetails, Left(errorResponse))
              }
          }
        case _ => logger.info("Could not Save CAWatchlist preference")
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not Save CAWatchlist preference", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }

        case e: Throwable =>
          logger.info("Could not Save CAWatchlist preference", e)
          val errorResponse = ErrorResponseFactory.get(UnknownError)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, preference.cAWatchlistPreference.creator,
            Some(errorResponse), Seq.empty, None, Seq.empty, Some(preference.cAWatchlistPreference), Seq(preference.cAWatchlistPreference.environmentId)) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
    }
  }

  def getHistoricalRange(): Future[Set[String]] ={
    Future.successful(HistoricalRange.values.map(_.name).toSet)
  }

  def saveKycPreference(kycPreferencesRequest: KycPreferencesRequest): Future[Either[ErrorResponse, KycPreferencesRequest]] = {
    daoAccountV2.getEnvironmentIds(kycPreferencesRequest.environmentTypes, kycPreferencesRequest.accountIds) flatMap {
      case environmentIds: Seq[Long] => daoAccount.saveKycForAccounts(KycPreferencesMapper.toDto(environmentIds, kycPreferencesRequest)) map {
        case Some(res) if res > 0 => Right(kycPreferencesRequest)
        case _ => logger.info(s"Could not save kyc preference")
          Left(ErrorResponse(UnknownError.id, UnknownError.description))
      }
      case _ => logger.info(s"Could not fetch EnvironmentIds")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }
}
