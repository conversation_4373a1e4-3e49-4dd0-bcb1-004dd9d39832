package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.utils.UUIDUtilities
import me.socure.constants.AccountManagementDefaults
import me.socure.model.BusinessUserRoles
import me.socure.model.account.UserAccountAssociationStatuses
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.account.DtoAccount
import me.socure.storage.slick.tables.user.DaoBusinessUser
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class BusinessUserCommonService (daoBusinessUser: DaoBusinessUser,
                                 daoAccount: DaoAccount,
                                 daoAccountV2: DaoAccountV2)(implicit ec : ExecutionContext) {

  val LOGGER: Logger = LoggerFactory.getLogger(this.getClass)

  def accountsHaveNonFunctionalUsers(accountIds: Set[Long]): Future[Boolean] = {
    val usersFuture = Future.sequence(accountIds.map { accountId =>
      daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
        case true =>
          daoAccountV2.accountHasNonFunctionEmailsV2(accountId)
        case false =>
          daoAccountV2.accountHasNonFunctionEmailsV1(accountId)
      } map {
        case businessUsers if businessUsers.isEmpty => false
        case _ => true
      }
    })
    usersFuture.map(users => users.contains(true))
  }

  def accountHasNonfunctionalUsers(publicId: String): Future[Boolean] = {
    daoAccount.fetchAccountByPublicAccountId(publicId) flatMap  {
      case Some(account) =>
        daoAccountV2.isAccountPermissionProvisioned(account.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
          case true =>
            daoAccountV2.accountHasNonFunctionEmailsV2(account.accountId)
          case false =>
            daoAccountV2.accountHasNonFunctionEmailsV1(account.accountId)
        } map {
          case businessUsers if businessUsers.isEmpty => false
          case _ => true
        }
      case None =>
        Future.successful(false)
    }
  }

  def handleIdleEmail(email: String, associatedAccounts: Set[Long]): Future[Boolean] = {
    if(associatedAccounts.nonEmpty){
      daoBusinessUser.getUser(email) flatMap {
        case Some(bu) =>
          daoAccountV2.isAccountPermissionProvisioned(associatedAccounts.head, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
            case true =>
              val uuid = UUIDUtilities.getRandomUUID
              val prefixedEmail = s"${AccountManagementDefaults.PrefixForNonFunctionalEmail}${uuid}_$email"
              daoAccountV2.updateEmailAndUserAccountAssociationStatus(email, prefixedEmail, bu.id, associatedAccounts) flatMap {
                case i if i == 1 =>
                  LOGGER.info(s"Updated Email with the prefix:$prefixedEmail")
                  Future.successful(true)
                case _ =>
                  LOGGER.info(s"Update failed to prefix the email associated with inactive/deleted account $associatedAccounts")
                  Future.successful(false)
              }
            case false =>
              daoBusinessUser.cleanUpUsers(bu.id) flatMap {
                case i if i == 1 =>
                  LOGGER.info(s"Deleted user with email associated with inactive/deleted account $associatedAccounts")
                  Future.successful(true)
                case _ =>
                  LOGGER.info(s"Failed to delete the user with the email, associated with inactive/deleted account $associatedAccounts")
                  Future.successful(false)
              }
          }
        case _ =>
          LOGGER.info(s"User not found, not deleted or prefixed")
          Future.successful(true)
      }
    } else {
      LOGGER.info(s"Associated accounts for Email $email empty, so email is not prefixed/deleted")
      Future.successful(true)
    }
  }

  def checkNApplyUserEmailAvailability(email: String): Future[Boolean] = {
    if(email.trim.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail)){
      LOGGER.info(s"InvalidEmail, Email cannot start with idle. $email")
      Future.failed(new Exception("Invalid Email, cannot start with idle"))
    } else {
      checkUserEmailAvailability(email) flatMap {
        case (true, true, associatedAccounts) =>
          handleIdleEmail(email, associatedAccounts)
        case (_, false, _) =>
          Future.successful(false)
        case (false, true, _) =>
          Future.successful(true)
      }
    }
  }

  def checkUserEmailAvailability(email: String): Future[(Boolean, Boolean, Set[Long])] = { //Tuple2(emailAlreadyExists, canBeReused, associatedAccounts)
    def checkV1(associatedAccounts: Option[Set[Long]] = Some(Set.empty)): Future[(Boolean, Boolean, Set[Long])] = {
      val associatedAccounts0 = associatedAccounts.getOrElse(Set.empty)
      daoBusinessUser.getUser(email) flatMap {
        case Some(userInfo) =>
          daoAccount.getAccount(userInfo.accountId) map {
            case Some(dtoAccount) =>
              dtoAccount match {
                case acc if !acc.isActive || acc.isDeleted =>
                  LOGGER.info(s"Email already exists, but associated with inactive/deleted account(${acc.accountId})")
                  (true, true, Set(acc.accountId).++(associatedAccounts0))
                case acc0 =>
                  LOGGER.info(s"Email already exists, and associated with account(${acc0.accountId})")
                  (true, false, Set(acc0.accountId).++(associatedAccounts0))
              }
            case None =>
              LOGGER.info(s"Email already exists, but not associated with any account")
              (true, true, Set.empty[Long])
          }
        case None =>
          LOGGER.info(s"Email does not exist")
          Future.successful(false, true, Set.empty[Long])
      }
    }

    def checkAccountStatus(associatedAccounts: Set[Long]): Future[(Boolean, Boolean, Set[Long])] = {
          daoAccount.getAccounts(associatedAccounts) map {
            case accounts0 if accounts0.nonEmpty =>
              val activeAndNonDeletedAccounts = accounts0.filter(f => f.isActive && !f.isDeleted)
              val allAccountIds = accounts0.map(_.accountId)
              val accountIds = activeAndNonDeletedAccounts.map(_.accountId)
              if(activeAndNonDeletedAccounts.nonEmpty) {
                LOGGER.info(s"Email already exists, and associated with account(${accountIds.mkString(",")})")
                (true, false, accountIds.toSet)
              } else {
                LOGGER.info(s"Email already exists, but associated with inactive/deleted account(${allAccountIds.mkString(",")})")
                (true, true, allAccountIds.toSet)
              }
            case _ =>
              LOGGER.info(s"Email already exists, but not associated with any account")
              (true, true, Set.empty[Long])
          }
    }

    if(email.trim.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail)){
      LOGGER.info(s"InvalidEmail, Email cannot start with idle. $email")
      Future.failed(new Exception("Invalid Email, cannot start with idle"))
    } else {
    daoAccountV2.getAllUserAccountAssociationsByEmail(email) flatMap {
      case userAccountAssociations if userAccountAssociations.nonEmpty =>
        userAccountAssociations.filter(uaa => uaa.status == UserAccountAssociationStatuses.ACTIVE.id || uaa.status == UserAccountAssociationStatuses.LOCKED.id) match {
          case l0 if l0.nonEmpty =>
            LOGGER.info(s"Email already exists, and associated with V2 accounts(${l0.map(_.accountId).mkString(",")}), Lets check status in accounts table")
            checkAccountStatus(l0.map(_.accountId).toSet)
          case _ =>
            LOGGER.info(s"Email already exists, and associated with V2 inactive/deleted accounts(${userAccountAssociations.map(_.accountId).mkString})")
            Future.successful(true, true, userAccountAssociations.map(_.accountId).toSet)
        }
      case _ =>
        LOGGER.info(s"Email does not exists, or not associated with V2 account, Checking V1 accounts")
        checkV1()
    }
  }
}
}
