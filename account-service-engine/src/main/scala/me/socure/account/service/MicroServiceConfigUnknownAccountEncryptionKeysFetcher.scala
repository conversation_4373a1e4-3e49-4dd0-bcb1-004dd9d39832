package me.socure.account.service

import java.nio.file.{Path, Paths}

import com.amazonaws.regions.Regions
import me.socure.common.environment.{AppNameResolver, EnvironmentResolver}
import me.socure.common.resource.{EnvironmentResourceLoaderFactory, ResourceLoader}
import me.socure.constants.JsonFormats
import me.socure.model.encryption.EncryptedKey
import org.json4s._
import org.json4s.jackson.JsonMethods._

import scala.concurrent.Future

/**
  * Created by jamesanto on 5/3/17.
  */
class MicroServiceConfigUnknownAccountEncryptionKeysFetcher(resourceLoader: ResourceLoader) extends UnknownAccountEncryptionKeysFetcher {

  import MicroServiceConfigUnknownAccountEncryptionKeysFetcher.resourceName

  private implicit val jsonFormats = JsonFormats.formats

  override def fetch(): Future[Map[Regions, EncryptedKey]] = {
    val resource = resourceLoader.load(resourceName)
    val keys = parse(resource).extract[Map[Regions, EncryptedKey]]
    Future.successful(keys)
  }
}

object MicroServiceConfigUnknownAccountEncryptionKeysFetcher {
  val resourceName: Path = Paths.get("unknown_account_keys.json")

  def apply(): MicroServiceConfigUnknownAccountEncryptionKeysFetcher = {
    val appName = AppNameResolver.resolve
    val environment = EnvironmentResolver.resolve()
    val resourceLoader = EnvironmentResourceLoaderFactory.create(
      environment = environment,
      appName = appName,
      basePath = Paths.get("encryption_keys")
    )
    new MicroServiceConfigUnknownAccountEncryptionKeysFetcher(
      resourceLoader = resourceLoader
    )
  }
}
