package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.model.ErrorResponse
import me.socure.model.pgp.{PgpDecAndSigKeys, PgpPrivateKey, PgpPublicKey}

import scala.concurrent.{ExecutionContext, Future}

class PgpDecAndSigKeysService(accountPgpKeysService: AccountPgpKeysService, pgpSignaturePublicKeyService: PgpSignaturePublicKeyService)(implicit exe: ExecutionContext) {

  private val noPGPSignaturePublicKeyFoundCode = ExceptionCodes.NoPGPSignaturePublicKeyFound.id

  def getPgpDecAndSigKeys(accountId: Long): Future[Either[ErrorResponse, PgpDecAndSigKeys]] = {
    for {
      privateKey <- accountPgpKeysService.getAccountPgpPrivateKey(accountId)
      publicKey <- pgpSignaturePublicKeyService.getPgpSignaturePublicKey(accountId)
    } yield {
      (privateKey, publicKey) match {
        case (Right(privateKey), Right(publicKey)) =>
          Right(PgpDecAndSigKeys(
            decryptionPrivateKey = privateKey.value,
            signatureVerificationPublicKey = Some(publicKey.value),
            hasSubkey = privateKey.hasSubkey
          ))
        case (Right(privateKey), Left(ErrorResponse(`noPGPSignaturePublicKeyFoundCode`, _))) =>
          Right(PgpDecAndSigKeys(
            decryptionPrivateKey = privateKey.value,
            signatureVerificationPublicKey = None,
            hasSubkey = privateKey.hasSubkey
          ))
        case (Right(_), Left(error)) => Left(error)
        case (Left(error), _) => Left(error)
      }
    }
  }

  def getAllPgpDecAndSigKeys(accountId: Long): Future[Either[ErrorResponse, Seq[PgpDecAndSigKeys]]] = {
    for {
      privateKeys <- accountPgpKeysService.getAllAccountPgpPrivateKey(accountId)
      publicKey <- pgpSignaturePublicKeyService.getPgpSignaturePublicKey(accountId)
    } yield {
      (privateKeys, publicKey) match {
        case (Right(privKeys), Right(pubKey)) =>
          Right(createPgpDecAndSigKeys(privKeys, Some(pubKey)))

        case (Right(privKeys), Left(ErrorResponse(`noPGPSignaturePublicKeyFoundCode`, _))) =>
          Right(createPgpDecAndSigKeys(privKeys, None))

        case (Left(error), _) => Left(error)
        case (_, Left(error)) if error.code != noPGPSignaturePublicKeyFoundCode => Left(error)
      }
    }
  }

  private def createPgpDecAndSigKeys(privateKeys: Seq[PgpPrivateKey], publicKey: Option[PgpPublicKey]): Seq[PgpDecAndSigKeys] = {
    privateKeys.map { privateKey =>
      PgpDecAndSigKeys(
        decryptionPrivateKey = privateKey.value,
        signatureVerificationPublicKey = publicKey.map(_.value),
        hasSubkey = privateKey.hasSubkey
      )
    }
  }


}
