package me.socure.account.service

import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.validator.{PermissionValidator, V2Validator}
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.{DashboardUserPermissions, EnvironmentTypes, TemplateTypes}
import me.socure.convertors.AccountConvertors
import me.socure.model.{ErrorResponse, PermissionTemplate}
import me.socure.model.account.{PermissionTemplateMapping, PermissionTemplateMappingInput}
import me.socure.storage.slick.dao.DaoAccountV2
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class PermissionTemplateService(daoAccountV2: DaoAccountV2,
                                v2Validator: V2Validator,
                                clock: Clock)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getPermissionTemplateMappingsByTemplateId(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[PermissionTemplateMapping]]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.TEMPLATES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validatePermissionTemplateAccess(permissionTemplateId, accountId)
      res <- daoAccountV2.getPermissionTemplateMappingsByTemplateId(permissionTemplateId) map { dtoPermissionTemplateMappingSeq =>
        Right(dtoPermissionTemplateMappingSeq.map(AccountConvertors.toPermissionTemplateMapping))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not view permission template mapping", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching permission template mappings", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateMappingsFetchError))
    }
  }

  def insertPermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(permissionTemplateMappingInput.creator.accountId, permissionTemplateMappingInput.creator.userId, Set(DashboardUserPermissions.TEMPLATES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validatePermissionTemplateAccess(permissionTemplateMappingInput.permissionTemplateId, permissionTemplateMappingInput.creator.accountId)
      res <- if (PermissionValidator.validateIds(permissionTemplateMappingInput.permissions)) {
        if (EnvironmentTypes.isValid(permissionTemplateMappingInput.environmentTypeId)) {
          val dtoPermissionTemplateMapping = AccountConvertors.toDtoPermissionTemplateMapping(permissionTemplateMappingInput)
          logger.info(s"Inserting permissions ${dtoPermissionTemplateMapping.permissions} for template - ${permissionTemplateMappingInput.permissionTemplateId} and environment type ${permissionTemplateMappingInput.environmentTypeId}")
          daoAccountV2.insertPermissionTemplateMapping(dtoPermissionTemplateMapping) map {
            case res if res > 0 =>
              Right(res)
            case _ =>
              logger.info("Could not insert permission template mapping")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertPermissionTemplateMapping))
          }
        } else {
          logger.info(s"Invalid environment type - ${permissionTemplateMappingInput.environmentTypeId}")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided)))
        }
      } else {
        logger.info("Invalid Permission id provided")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissionIdProvided)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not insert permission template mapping", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not insert permission template mapping", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertPermissionTemplateMapping))
    }
  }

  def updatePermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(permissionTemplateMappingInput.creator.accountId, permissionTemplateMappingInput.creator.userId, Set(DashboardUserPermissions.TEMPLATES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validatePermissionTemplateAccess(permissionTemplateMappingInput.permissionTemplateId, permissionTemplateMappingInput.creator.accountId)
      res <- if (PermissionValidator.validateIds(permissionTemplateMappingInput.permissions)) {
        if (EnvironmentTypes.isValid(permissionTemplateMappingInput.environmentTypeId)) {
          val dtoPermissionTemplateMapping = AccountConvertors.toDtoPermissionTemplateMapping(permissionTemplateMappingInput)
          daoAccountV2.updatePermissionTemplateMapping(dtoPermissionTemplateMapping) map {
            case res if res > 0 =>
              Right(res)
            case _ =>
              logger.info(s"Could not update permission template mapping with id - ${dtoPermissionTemplateMapping.id}")
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdatePermissionTemplateMapping))
          } recover {
            case e: Exception =>
              logger.info("Could not update permission template mapping", e)
              Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdatePermissionTemplateMapping))
          }
        } else {
          logger.info(s"Invalid environment type - ${permissionTemplateMappingInput.environmentTypeId}")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided)))
        }
      } else {
        logger.info("Invalid Permission id provided")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissionIdProvided)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not update permission template mapping", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not update permission template mapping - Unknown error", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }

  }

  def deletePermissionTemplateMapping(permissionTemplateId: Long, environmentTypeId: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.TEMPLATES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      _ <- v2Validator.validatePermissionTemplateAccess(permissionTemplateId, accountId)
      res <- if (EnvironmentTypes.isValid(environmentTypeId)) {
        daoAccountV2.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId) map {
          case res if res > 0 =>
            Right(res)
          case _ =>
            logger.info("Could not delete permission template mapping")
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeletePermissionTemplateMapping))
        }
      } else {
        logger.info(s"Invalid environment type - $environmentTypeId")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not delete permission template mapping", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not delete permission template mapping", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToDeletePermissionTemplateMapping))
    }
  }

  def insertPermissionTemplate(permissionTemplate: PermissionTemplate): Future[Either[ErrorResponse, Int]] = {
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(permissionTemplate.updatedBy)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.TEMPLATES_CREATE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- if (TemplateTypes.isValid(permissionTemplate.templateType)) {
        daoAccountV2.insertPermissionTemplate(AccountConvertors.toDtoPermissionTemplate(permissionTemplate, clock)) map {
          case res if res > 0 => Right(res)
          case _ =>
            logger.info("Could not insert permission template")
            Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateCreateFailed))
        }
      } else {
        logger.info(s"Error occurred while inserting Permission Template - ${permissionTemplate.name} - invalid template type ${permissionTemplate.templateType}" )
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateCreateFailed)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not insert permission template", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while inserting Permission Template - ${permissionTemplate.name}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateCreateFailed))
    }
  }


    def getPermissionTemplate(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, PermissionTemplate]] = {
      (for{
        _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.TEMPLATES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
        res <- daoAccountV2.getPermissionTemplate(permissionTemplateId) map {
          case Some(permissionTemplate) => Right(AccountConvertors.toPermissionTemplate(permissionTemplate))
          case _ =>
            logger.info(s"No Permission Template matched for $permissionTemplateId")
            Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateFetchFailed))
        }
      } yield res) recover {
        case ex: ErrorResponseException =>
          logger.info(s"Could not fetch permission template with id $permissionTemplateId", ex)
          Left(ex.errorResponse)
        case e: Exception =>
          logger.info(s"Error occurred while fetching the Permission Template for $permissionTemplateId", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateFetchFailed))
      }
    }

  def updatePermissionTemplate(permissionTemplate: PermissionTemplate): Future[Either[ErrorResponse, Int]] = {
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(permissionTemplate.updatedBy)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.TEMPLATES_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- if (TemplateTypes.isValid(permissionTemplate.templateType)) {
        daoAccountV2.updatePermissionTemplate(AccountConvertors.toDtoPermissionTemplate(permissionTemplate, clock)) map {
          case res if res > 0 => Right(res)
          case _ =>
            logger.info(s"Could not update permission template with id - ${permissionTemplate.id}")
            Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed))
        }
      } else {
        logger.info(s"Error occurred while updating Permission Template - ${permissionTemplate.name} - invalid template type ${permissionTemplate.templateType}" )
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed)))
      }
    } yield res)  recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not update permission template", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while updating Permission Template - ${permissionTemplate.name}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed))
    }
  }

  def deletePermissionTemplate(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    (for {
      _ <- v2Validator.validatePermissions(accountId, userId, Set(DashboardUserPermissions.TEMPLATES_DELETE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- daoAccountV2.deletePermissionTemplate(permissionTemplateId) map {
        case res if res > 0 =>
          Right(res)
        case _ =>
          logger.info(s"Could not delete permission template with id - $permissionTemplateId")
          Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateDeleteFailed))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not delete permission template with id - $permissionTemplateId", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not delete permission template with id - $permissionTemplateId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateDeleteFailed))
    }
  }

  def getPermissionTemplates(userAccountAssociationId: Long): Future[Either[ErrorResponse, Seq[PermissionTemplate]]] = {
    (for {
      uaa <- daoAccountV2.getUserAccountAssociationById(userAccountAssociationId)
      _ <- v2Validator.validatePermissions(uaa.accountId, uaa.businessUserId, Set(DashboardUserPermissions.TEMPLATES_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- daoAccountV2.getPermissionTemplates(userAccountAssociationId) map {
        case permissionTemplates => Right(permissionTemplates.map(AccountConvertors.toPermissionTemplate))
        case _ =>
          logger.info(s"No Permission Template(s) for userAccount - $userAccountAssociationId")
          Right(Seq.empty[PermissionTemplate])
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch permission templates for user account association- $userAccountAssociationId", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching the Permission Template for userAccount - $userAccountAssociationId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateFetchFailed))
    }
  }
}
