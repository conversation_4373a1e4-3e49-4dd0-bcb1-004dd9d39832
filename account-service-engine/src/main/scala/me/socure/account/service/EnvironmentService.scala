package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.validator.V2Validator
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.EnvironmentData
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class EnvironmentService(
                          daoEnvironment: DaoEnvironment,
                          v2Validator: V2Validator,
                          daoAccount: DaoAccount
                        )(implicit val ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(this.getClass)

  def getEnvirontmentById(id: Long): Future[Either[ErrorResponse, EnvironmentData]] = {
    val etlEnvironmentDto = daoEnvironment.getEnvironment(id).map(list => list.map(AccountConvertors.getEtlEnvironment)).map(_.headOption)
    etlEnvironmentDto map {
      case Some(dto) =>
        val environmentData = EnvironmentData(dto.id, dto.apiKey,
          dto.secretKey, dto.accessToken,
          dto.accessTokenSecret, dto.domain,
          dto.accountId, dto.environmentType
        )
        Right(environmentData)
    }
  }

  def getEnvironmentDataById(environmentId: Long): Future[Either[ErrorResponse, Option[EnvironmentData]]] = {
    daoEnvironment
      .getEnvironment(environmentId)
      .map(_.headOption.map(AccountConvertors.getEtlEnvironment))
      .flatMap {
        case Some(dto) =>
          val accountPublicIdF = daoAccount.fetchPublicIdByAccountId(dto.accountId)
          val rootAccTypeIdF = v2Validator
            .getRootParentAccountType(dto.accountId)

          for {
            accountPublicId <- accountPublicIdF
            rootAccTypeId <- rootAccTypeIdF
          } yield {
            val environmentData = EnvironmentData(
              dto.id, dto.apiKey,
              dto.secretKey, dto.accessToken,
              dto.accessTokenSecret, dto.domain,
              dto.accountId, dto.environmentType,
              accountTypeId = rootAccTypeId,
              accountPublicId = accountPublicId
            )
            Right(Some(environmentData))
          }

        case None =>
          logger.info(s"No environment data found with environment id=$environmentId")
          Future.successful(Right(None))
      }
  }
}
