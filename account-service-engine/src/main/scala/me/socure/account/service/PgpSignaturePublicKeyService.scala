package me.socure.account.service


import java.io.ByteArrayInputStream
import java.nio.ByteBuffer
import java.nio.charset.StandardCharsets
import java.util.Base64

import com.amazonaws.services.kms.model.{DecryptRequest, EncryptRequest}
import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.Clock
import me.socure.common.kms.KmsService
import me.socure.common.pgp.PgpKeyUtil
import me.socure.model.encryption.KmsIdsConfig
import me.socure.model.pgp.PgpPublicKey
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.storage.slick.dao.DaoPgpSignaturePublicKeys
import me.socure.storage.slick.tables.account.{DtoAccount, DtoPgpSignaturePublicKey}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class PgpSignaturePublicKeyService(daoPgpSignaturePublicKeys: DaoPgpSignaturePublicKeys, daoAccount: DaoAccount, kmsService: KmsService, kmsKey : KmsIdsConfig, clock: Clock)(implicit ec : ExecutionContext) {
  val logger : Logger = LoggerFactory.getLogger(getClass)

  def insertPgpSignaturePublicKey(accountId: Long, publicKey: String) = {
    daoAccount.isParentAccount(accountId).flatMap {
      case true =>
        daoPgpSignaturePublicKeys.doesPgpSignaturePublicKeyExist(accountId).flatMap {
          case false =>
            try {
              val decodedCert = Base64.getDecoder.decode(publicKey)
              val fis = new ByteArrayInputStream(decodedCert)
              PgpKeyUtil.readPublicKey(fis)
              encryptPgpSignaturePublicKey(decodedCert).flatMap { es =>
                daoPgpSignaturePublicKeys.save(DtoPgpSignaturePublicKey(id = 0, accountId = accountId, publicKey = es, deleted = false, createdAt = clock.now())).map{
                  case ar if ar > 0 => Right(true)
                  case _ => Left(ErrorResponseFactory.get(UnknownError))
                }
              }
            } catch {
              case e: Exception =>
                logger.info("Error while reading public key", e)
                Future.successful(Left(ErrorResponseFactory.get(InvlidPGPSignaturePublicKey)))
            }
          case true => Future.successful(Left(ErrorResponseFactory.get(PGPSignaturePublicKeyExists)))
        }
        case false => Future.successful(Left(ErrorResponseFactory.get(ParentAccountNotFound)))
      }
  }

  def deletePgpPublicKey(accountId: Long) = {
    daoPgpSignaturePublicKeys.deletePgpSignaturePublicKey(accountId).map{
      case a if a > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAccountsWithSignaturePublicKeys() = {
    daoPgpSignaturePublicKeys.listPgpSignaturePublicKeys().map{
      case list:Seq[(DtoPgpSignaturePublicKey, DtoAccount)] if list.nonEmpty =>
        val accountPgpPublicKey = list.map{
          case(pgpPublicKey, account) =>
            AccountPgpInfo(accountId = account.accountId, publicAccountId = account.publicId, accountName = account.name, createdAt = pgpPublicKey.createdAt)
        }
        Right(accountPgpPublicKey)
      case _ => Left(ErrorResponseFactory.get(NoPGPSignaturePublicKeyFound))
    }
  }

  def getPgpSignaturePublicKey(accountId: Long) = {
    daoPgpSignaturePublicKeys.getPgpSignaturePublicKey(accountId).flatMap{
      case Some(s) => decryptPgpSignaturePublicKey(s.publicKey).map(kk => Right(PgpPublicKey(Base64.getEncoder.encodeToString(kk))))
      case _ => Future.successful(Left(ErrorResponseFactory.get(NoPGPSignaturePublicKeyFound)))
    }
  }

  private def encryptPgpSignaturePublicKey(publicKey : Array[Byte]) : Future[String] = {
    val kmsKeyId = kmsKey.value.headOption.map(_._2.value).getOrElse(throw new Exception("Failed to find any key in KMS configuration"))
    val req = new EncryptRequest().withKeyId(kmsKeyId).withPlaintext(ByteBuffer.wrap(publicKey))
    kmsService.encrypt(req).map(r => Base64.getEncoder.encodeToString(r.getCiphertextBlob.array()))
  }

  private def decryptPgpSignaturePublicKey(publicKey : String): Future[Array[Byte]] = {
    val req = new DecryptRequest().withCiphertextBlob(ByteBuffer.wrap(Base64.getDecoder.decode(publicKey.getBytes(StandardCharsets.UTF_8))))
    kmsService.decrypt(req).map(r => r.getPlaintext.array())
  }
}
