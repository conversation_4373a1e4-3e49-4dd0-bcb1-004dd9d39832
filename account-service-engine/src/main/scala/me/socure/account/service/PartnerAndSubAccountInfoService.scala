package me.socure.account.service

import me.socure.account.onboarding.OnboardingResponse
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.{AccountManagementDefaults, AccountTypes, DashboardUserPermissions, EnvironmentConstants, EnvironmentTypes, JsonFormats, MigrationStatus, Status, SystemDefinedRoles}
import me.socure.convertors.AccountConvertors
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.account.{AccountIdName, AccountUserInfo, MergeAccountDetailsInput, MigratedAccount, MigrationAccountDetails, MigrationAccountDetailsInput, MigrationSubAccountDetailsInput, PartnerAndSubAccountInfo, PartnerAndSubAccountUserInfo, SubAccountMigrationDetails, UserAccountAssociationStatuses}
import me.socure.model.dashboardv2.SubAccountV2
import me.socure.model.sponsor.bank.SponsorBankProgram
import me.socure.model.user.{BusinessUserWithRolesV2, DashboardUserRole, UserInfo}
import me.socure.model.user.DashboardUserRole.getGlobalPermissions
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.account.{DtoAccount, DtoAccountAttribute, DtoAccountHierarchy, DtoAccountMigrationAudit, DtoAccountPermission, DtoEnvironment, DtoSystemRolesMaxCount, DtoUserAccountAssociation, DtoUserAccountRoleAssociation, DtoUserRole}
import me.socure.storage.slick.tables.user.DtoBusinessUser
import me.socure.storage.slick.tables.user.role.DtoBusinessUserEnvironmentRole
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
/**
 * <AUTHOR> Kumar
 */
class PartnerAndSubAccountInfoService(daoAccountV2: DaoAccountV2,
                                      v2Validator: V2Validator,
                                      sponsorBankService: SponsorBankService,
                                      clock: Clock)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  protected implicit def jsonFormats: Formats = JsonFormats.formats

  def fetchAccountDetails(accountId: Long): Future[Either[ErrorResponse, MigrationAccountDetails]] = {

    daoAccountV2.fetchAccountAndSubAccounts(accountId) flatMap {
      case resp: (Option[DtoAccount],Seq[DtoAccount]) =>
        resp._1 match {
          case Some(account) =>
            getBusinessUserWithRoles(Set(accountId)) flatMap {
              res: (Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]]) =>
                val accountUserEnvMap = res._1
                val userRoleMap = res._2
                val modules = accountUserEnvMap(accountId)._3.map(permission => BusinessUserRoles.byId(permission.permission) match {
                  case Some(per) => per.label
                  case None => "-"
                })
                val users = accountUserEnvMap(accountId)._1.filter(_.accountNonLocked).map(user => {
                  val role = userRoleMap(user.id).map {case (envTypeId, rolesIds) =>
                    val envName = EnvironmentTypes.byEnvironmentTypeId(envTypeId).get.name
                    val roleNames = rolesIds.map( roleId => DashboardUserRole.byId(roleId).get.label)
                    (envName, roleNames)
                  }
                  if(role.contains(EnvironmentTypes.GLOBAL_ENVIRONMENT.name)) {
                    val commonRoles = role(EnvironmentTypes.GLOBAL_ENVIRONMENT.name)
                    BusinessUserWithRolesV2(user.id, user.firstName, user.lastName, user.email, user.contactNumber, user.isPrimaryUser, commonRoles.toSeq, role - EnvironmentTypes.GLOBAL_ENVIRONMENT.name)
                  } else {
                    BusinessUserWithRolesV2(user.id, user.firstName, user.lastName, user.email, user.contactNumber, user.isPrimaryUser, Seq.empty, role)
                  }
                })
                fetchSubAccountMigrationAccountDetails(resp._2) map {
                  subAccounts => Right(MigrationAccountDetails(accountId = account.accountId, accountName = account.name , users = users, modules = modules, subAccounts = subAccounts))
                }
            }
          case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    } recover {
      case e : Throwable =>
        logger.info(s"Error occurred while fetching account details", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def migrateAccount(migrationAccountDetails: MigrationAccountDetailsInput, startTime: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val accountId = migrationAccountDetails.accountId
    val accountType = migrationAccountDetails.accountType
    val subAccounts = migrationAccountDetails.subAccounts
    val associateAllUsersToSubAccount = migrationAccountDetails.associateAllUsersToSubAccount
    val parentAccountIdOpt = migrationAccountDetails.parentAccountId

    validateAccountMigrationData(accountId, accountType, subAccounts, parentAccountIdOpt, associateAllUsersToSubAccount) flatMap {
      _ =>
        val accountIds = parentAccountIdOpt match {
          case None => (accountId +: subAccounts.map(_.accountId)).toSet
          case Some(parentAccountId) => (parentAccountId +: accountId +: subAccounts.map(_.accountId)).toSet
        }
        getBusinessUserWithRoles(accountIds) flatMap {
          resp: (Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]]) =>
            val accountUserEnvMap = resp._1
            val userRoleMap: Map[Long, Map[Int, Set[Int]]] = resp._2
            daoAccountV2.getAccountAttributes(parentAccountIdOpt.getOrElse(accountId)) flatMap {
              accountAttributes:Seq[DtoAccountAttribute] =>
                val users =  parentAccountIdOpt match {
                  case None => accountUserEnvMap(accountId)._1
                  case Some(parentAccountId) => if(accountType == AccountTypes.SUB_ACCOUNT.id && associateAllUsersToSubAccount) accountUserEnvMap(accountId)._1 ++ accountUserEnvMap(parentAccountId)._1 else accountUserEnvMap(accountId)._1
                }
                users.find(_.isPrimaryUser) match {
                  case Some(primaryUser) =>
                    if(accountType == AccountTypes.SUB_ACCOUNT.id) {
                      parentAccountIdOpt match {
                        case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)))
                        case Some(parentAccountId) =>
                          val accountPermissions = (Seq(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) ++ accountUserEnvMap(parentAccountId)._3.map(_.permission)).diff(accountUserEnvMap(accountId)._3.map(_.permission))
                          val dtoAccountPermission = accountPermissions.distinct.map(permission => DtoAccountPermission(accountId, permission))
                          val dtoAccountAttributes = accountAttributes.map(accountAttributes => DtoAccountAttribute(accountId, accountAttributes.name, accountAttributes.value))
                          val dtoAccountHierarchy = DtoAccountHierarchy(0, accountId, s"$parentAccountId/$accountId/", accountType, Status.ACTIVE.id, administer = migrationAccountDetails.administer.getOrElse(false), AccountManagementDefaults.PrimaryAdmins)
                          daoAccountV2.getAccountHierarchyByAccountId(parentAccountId) flatMap {
                            case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)))
                            case Some(hierarchy) =>
                              val dtoPrimaryUserAccountAssociations = users.filter(_.isPrimaryUser).map(user => {
                                DtoUserAccountAssociation(0, user.id, accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
                              })
                              val (dtoUserAccountAssociations, dtoUserRoles, permissions) = users.filter(!_.isPrimaryUser).map(user => {
                                val userEnvRole = userRoleMap(user.id)
                                val assignAllPermission = migrationAccountDetails.userIdsWithAllPermission.contains(user.id)
                                val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(userEnvRole.flatMap(_._2).toSet).map(_.id)
                                val dtoUserAccountAssociation = DtoUserAccountAssociation(0, user.id, accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
                                val dtoUserRoles = DtoUserRole(0, s"Role_${user.id}", description = None, user.id, accountId, clock.now)
                                val permission = (if (assignAllPermission) AccountManagementDefaults.userPermissions(accountType) else userEnvRole).map(envPermission =>
                                  envPermission._1 match {
                                    case EnvironmentTypes.GLOBAL_ENVIRONMENT.id =>
                                      val permissions = if (assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2) ++ provisionPermissions
                                      (envPermission._1, permissions.mkString(","))
                                    case _ =>
                                      val permissions = if (assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2).diff(provisionPermissions)
                                      (envPermission._1, permissions.mkString(","))
                                  }
                                )
                                (dtoUserAccountAssociation, dtoUserRoles, permission)
                              }).unzip3
                              val (dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq) = prepareSubAccountData(parentAccountId, hierarchy.accountType, s"$parentAccountId/$accountId/", primaryUser, subAccounts, accountUserEnvMap, userRoleMap, accountAttributes, isPromoteSubAccount = false, Some(accountId), associateAllUsersToSubAccount)
                              daoAccountV2.migrateAccount(accountId, accountType, startTime, dtoAccountAttributes ++ dtoAccountAttributeSeq, dtoAccountPermission ++ dtoAccountPermissionSeq, dtoAccountHierarchy +: dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociations ++ dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociations ++ dtoUserAccountAssociationSeq, dtoUserRoles ++ dtoUserRolesSeq, permissions ++ permissionSeq, migrationAccountDetails.subAccounts, migrationAccountDetails.initiatedBy, isSubAccountMigration = false, isSubAccountPromote = false, None, parentAccountIdOpt, migrationAccountDetails.administer, clock).map(_ => Right(true))
                          }
                        case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)))
                      }
                    } else {
                      val accountPermissions = if(AccountTypes.isTypeChannelPartner(accountType)) Seq(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id).diff(accountUserEnvMap(accountId)._3.map(_.permission))
                      else Seq(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id).diff(accountUserEnvMap(accountId)._3.map(_.permission))
                      val dtoAccountPermission =  accountPermissions.distinct.map(permission => DtoAccountPermission(accountId, permission))
                      val dtoAccountHierarchy = DtoAccountHierarchy(0, accountId, s"$accountId/", accountType, Status.ACTIVE.id, administer = true, AccountManagementDefaults.PrimaryAdmins)
                      val dtoPrimaryUserAccountAssociations = users.filter(_.isPrimaryUser).map(user => {
                        DtoUserAccountAssociation(0, user.id, accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
                      })
                      val (dtoUserAccountAssociations, dtoUserRoles, permissions) = users.filter(!_.isPrimaryUser).map(user => {
                        val userEnvRole = userRoleMap(user.id)
                        val assignAllPermission = migrationAccountDetails.userIdsWithAllPermission.contains(user.id)
                        val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(userEnvRole.flatMap(_._2).toSet).map(_.id)
                        val dtoUserAccountAssociation = DtoUserAccountAssociation(0, user.id, accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
                        val dtoUserRoles = DtoUserRole(0, s"Role_${user.id}", description = None, user.id, accountId, clock.now)
                        val permission = (if (assignAllPermission) AccountManagementDefaults.userPermissions(accountType) else userEnvRole).map(envPermission =>
                          envPermission._1 match {
                            case EnvironmentTypes.GLOBAL_ENVIRONMENT.id =>
                              val permissions = if (assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2) ++ provisionPermissions
                              (envPermission._1, permissions.mkString(","))
                            case _ =>
                              val permissions = if (assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2).diff(provisionPermissions)
                              if(accountType == AccountTypes.AGGREGATOR.id) {
                                (envPermission._1, permissions.filterNot(_.equals(DashboardUserPermissions.TRANSACTIONS_CREATE.id)).mkString(","))
                              } else {
                                (envPermission._1, permissions.mkString(","))
                              }
                          }
                        )
                        (dtoUserAccountAssociation, dtoUserRoles, permission)
                      }).unzip3
                      val (dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq) = prepareSubAccountData(accountId, accountType, s"$accountId/", primaryUser, subAccounts, accountUserEnvMap, userRoleMap, accountAttributes, isPromoteSubAccount = false, None, associateAllUsersToSubAccount)
                      daoAccountV2.migrateAccount(accountId, accountType, startTime, dtoAccountAttributeSeq, dtoAccountPermission ++ dtoAccountPermissionSeq, dtoAccountHierarchy +: dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociations ++ dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociations ++ dtoUserAccountAssociationSeq, dtoUserRoles ++ dtoUserRolesSeq, permissions ++ permissionSeq, migrationAccountDetails.subAccounts, migrationAccountDetails.initiatedBy, isSubAccountMigration = false, isSubAccountPromote = false, None, None, None, clock).map(_ => Right(true))
                    }
                  case None => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PrimaryUserNotFound)))
                }
            }
        }
      } recover {
        case ex: ErrorResponseException =>
          logger.info(s"Error occurred while migrating account", ex)
          updateMigrationAudit(accountId, accountType, ex.getMessage, migrationAccountDetails.initiatedBy, startTime)
          Left(ex.errorResponse)
        case e : Throwable =>
          logger.info(s"Error occurred while migrating account", e)
          updateMigrationAudit(accountId, accountType, e.getMessage, migrationAccountDetails.initiatedBy, startTime)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }

  }

  def mergeAccount(mergeAccountDetails: MergeAccountDetailsInput, startTime: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val accountId = mergeAccountDetails.accountId
    val subAccounts = mergeAccountDetails.subAccounts
    val associateAllUsersToSubAccount = mergeAccountDetails.associateAllUsersToSubAccount
    val mergeAccountId = mergeAccountDetails.mergeAccountId
    daoAccountV2.getAccountHierarchyByAccountId(mergeAccountId) flatMap {
      case Some(hierarchy) =>
        validateMergeAccount(accountId, hierarchy.accountType, subAccounts, mergeAccountId, associateAllUsersToSubAccount)flatMap {
          _ =>
            daoAccountV2.fetchPrimaryUser(mergeAccountId) flatMap {
              case Some(primaryUser) =>
                getBusinessUserWithRoles((mergeAccountId +: accountId +: subAccounts.map(_.accountId)).toSet) flatMap {
                  resp: (Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]]) =>
                    val accountUserEnvMap = resp._1
                    val userRoleMap: Map[Long, Map[Int, Set[Int]]] = resp._2
                    val users = accountUserEnvMap(accountId)._1
                    val dtoPrimaryUserAccountAssociations = users.filter(_.isPrimaryUser).map(user => {
                      DtoUserAccountAssociation(0, user.id, accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
                    })
                    val (dtoUserAccountAssociations, dtoUserRoles, permissions) = users.filter(!_.isPrimaryUser).map(user => {
                      val userEnvRole = userRoleMap(user.id)
                      val assignAllPermission = mergeAccountDetails.userIdsWithAllPermission.contains(user.id)
                      val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(userEnvRole.flatMap(_._2).toSet).map(_.id)
                      val dtoUserAccountAssociation = DtoUserAccountAssociation(0, user.id, mergeAccountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, false, None, None, clock.now)
                      val dtoUserRoles = DtoUserRole(0, s"Role_${user.id}", description = None, user.id, mergeAccountId, clock.now)
                      val permission = (if (user.isPrimaryUser || assignAllPermission) AccountManagementDefaults.userPermissions(hierarchy.accountType) else userEnvRole).map(envPermission =>
                        envPermission._1 match {
                          case EnvironmentTypes.GLOBAL_ENVIRONMENT.id =>
                            val permissions = if (user.isPrimaryUser || assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2) ++ provisionPermissions
                            (envPermission._1, permissions.mkString(","))
                          case _ =>
                            val permissions = if (user.isPrimaryUser || assignAllPermission) envPermission._2 else AccountConvertors.toDashboardPermissions(envPermission._2).diff(provisionPermissions)
                            if(hierarchy.accountType == AccountTypes.AGGREGATOR.id) {
                              (envPermission._1, permissions.filterNot(_.equals(DashboardUserPermissions.TRANSACTIONS_CREATE.id)).mkString(","))
                            } else {
                              (envPermission._1, permissions.mkString(","))
                            }
                        }
                      )
                      (dtoUserAccountAssociation, dtoUserRoles, permission)
                    }).unzip3
                    daoAccountV2.getAccountAttributes(mergeAccountId) flatMap {
                      accountAttributes:Seq[DtoAccountAttribute] =>
                        val dtoAccountPermission = DtoAccountPermission(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)
                        val (dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq) = prepareSubAccountData(mergeAccountId, hierarchy.accountType, hierarchy.hierarchyPath, primaryUser, subAccounts, accountUserEnvMap, userRoleMap, accountAttributes, isPromoteSubAccount = false, Some(accountId), associateAllUsersToSubAccount)
                        daoAccountV2.migrateAccount(accountId, hierarchy.accountType, startTime, dtoAccountAttributeSeq, dtoAccountPermission +: dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociations ++ dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociations ++ dtoUserAccountAssociationSeq, dtoUserRoles ++ dtoUserRolesSeq, permissions ++ permissionSeq, mergeAccountDetails.subAccounts, mergeAccountDetails.initiatedBy, isSubAccountMigration = false, isSubAccountPromote = false, Some(mergeAccountId),  None, None, clock).map(_ => Right(true))
                    }
                }

              case _ =>  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PrimaryUserNotFound)))
            }
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound)))
    }recover {
      case ex: ErrorResponseException =>
        logger.info(s"Error occurred while migrating account", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Error occurred while migrating account", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def migrateSubAccount(migrationAccountDetails: MigrationSubAccountDetailsInput, startTime: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val subAccounts = migrationAccountDetails.subAccounts
    val parentId = migrationAccountDetails.accountId
    val associateAllUsersToSubAccount = migrationAccountDetails.associateAllUsersToSubAccount
    daoAccountV2.getAccountHierarchyByAccountId(parentId) flatMap {
      case Some(hierarchy) =>
        validateSubAccountMigrationData(parentId, hierarchy.accountType, subAccounts, associateAllUsersToSubAccount) flatMap {
          _ =>
            daoAccountV2.fetchPrimaryUser(parentId) flatMap {
              case Some(primaryUser) =>
                getBusinessUserWithRoles((parentId +: subAccounts.map(_.accountId)).toSet) flatMap {
                  resp: (Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]]) =>
                    val accountUserEnvMap = resp._1
                    val userRoleMap: Map[Long, Map[Int, Set[Int]]] = resp._2
                    daoAccountV2.getAccountAttributes(parentId) flatMap {
                      accountAttributes:Seq[DtoAccountAttribute] =>
                        val (dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq) = prepareSubAccountData(parentId, hierarchy.accountType, hierarchy.hierarchyPath, primaryUser, subAccounts, accountUserEnvMap, userRoleMap, accountAttributes, isPromoteSubAccount = false, None, associateAllUsersToSubAccount)
                        daoAccountV2.migrateAccount(migrationAccountDetails.accountId, AccountTypes.SUB_ACCOUNT.id, startTime, dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq, migrationAccountDetails.subAccounts, migrationAccountDetails.initiatedBy,isSubAccountMigration = true, isSubAccountPromote = false, None, None, None, clock).map(_ => Right(true))
                    }
                }
              case _ =>  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PrimaryUserNotFound)))
            }
        }
      case _ =>
        updateMigrationAudit(parentId, AccountTypes.SUB_ACCOUNT.id, ExceptionCodes.AccountHierarchyNotFound.description, migrationAccountDetails.initiatedBy, startTime)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Error occurred while migrating account", ex)
        updateMigrationAudit(parentId, AccountTypes.SUB_ACCOUNT.id, ex.getMessage, migrationAccountDetails.initiatedBy, startTime)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Error occurred while migrating account", e)
        updateMigrationAudit(parentId, AccountTypes.SUB_ACCOUNT.id, e.getMessage, migrationAccountDetails.initiatedBy, startTime)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def promoteSubAccount(migrationAccountDetails: MigrationAccountDetailsInput, startTime: DateTime) : Future[Either[ErrorResponse, Boolean]] = {
    val accountType = migrationAccountDetails.accountType
    val subAccounts = migrationAccountDetails.subAccounts
    val parentId = migrationAccountDetails.accountId
    validateSubAccountMigrationData(parentId, accountType, subAccounts, associateAllUsersToSubAccount = false) flatMap {
      _ =>
        daoAccountV2.fetchPrimaryUser(parentId) flatMap {
          case Some(primaryUser) =>
            getBusinessUserWithRoles((parentId +: subAccounts.map(_.accountId)).toSet) flatMap {
              resp: (Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]]) =>
                val accountUserEnvMap = resp._1
                val userRoleMap: Map[Long, Map[Int, Set[Int]]] = resp._2
                daoAccountV2.getAccountAttributes(parentId) flatMap {
                  accountAttributes: Seq[DtoAccountAttribute] =>
                    val (dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq) = prepareSubAccountData(parentId, accountType, "", primaryUser, subAccounts, accountUserEnvMap, userRoleMap, accountAttributes, isPromoteSubAccount = true, None, associateAllUsersToSubAccount = false)
                    daoAccountV2.migrateAccount(migrationAccountDetails.accountId ,accountType, startTime, dtoAccountAttributeSeq, dtoAccountPermissionSeq, dtoAccountHierarchySeq, dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRolesSeq, permissionSeq, migrationAccountDetails.subAccounts, migrationAccountDetails.initiatedBy, isSubAccountMigration = true, isSubAccountPromote = true, None, None, None, clock).map(_ => Right(true))
                }
            }
          case _ =>  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PrimaryUserNotFound)))
        }
    }recover {
      case ex: ErrorResponseException =>
        logger.info(s"Error occurred while migrating account", ex)
        updateMigrationAudit(parentId, accountType, ex.getMessage, migrationAccountDetails.initiatedBy, startTime)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Error occurred while migrating account", e)
        updateMigrationAudit(parentId, accountType, e.getMessage, migrationAccountDetails.initiatedBy, startTime)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def validateAccountMigrationData(accountId: Long, accountType: Int, subAccounts: Seq[SubAccountMigrationDetails], parentAccountId: Option[Long], associateAllUsersToSubAccount: Boolean): Future[Unit] = {
    val subAccountIds = subAccounts.map(subAccount => subAccount.accountId)
    val accountIds = accountId +: subAccountIds
    val usersIds = subAccounts.filter(_.userId.nonEmpty).map(subAccount => subAccount.userId.get)
    for{
      _ <- v2Validator.isValidMigrationInput(accountType, subAccounts, parentAccountId, associateAllUsersToSubAccount)
      _ <- v2Validator.isPrimaryAccount(accountId)
      _ <- v2Validator.isAccountNotV2Provisioned(accountIds.toSet)
      _ <- v2Validator.isValidSubAccount(accountId, subAccountIds.toSet)
      _ <- v2Validator.isValidUser(accountIds.toSet, usersIds.toSet)
    } yield ()
  }

  def validateSubAccountMigrationData(accountId: Long, accountType: Int, subAccounts: Seq[SubAccountMigrationDetails], associateAllUsersToSubAccount: Boolean): Future[Unit] = {
    val subAccountIds = subAccounts.map(subAccount => subAccount.accountId)
    val accountIds = accountId +: subAccountIds
    val usersIds = subAccounts.filter(_.userId.nonEmpty).map(subAccount => subAccount.userId.get)
    for{
      _ <- v2Validator.isValidMigrationInput(accountType, subAccounts, None, associateAllUsersToSubAccount)
      _ <- v2Validator.isPrimaryAccount(accountId)
      _ <- v2Validator.isAccountV2Provisioned(Set(accountId))
      _ <- v2Validator.isAccountNotV2Provisioned(subAccountIds.toSet)
      _ <- v2Validator.isValidSubAccount(accountId, subAccountIds.toSet)
      _ <- v2Validator.isValidUser(accountIds.toSet, usersIds.toSet)
    } yield ()
  }

  def validateMergeAccount(accountId: Long, accountType: Int, subAccounts: Seq[SubAccountMigrationDetails], mergeAccountId: Long, associateAllUsersToSubAccount: Boolean): Future[Unit] = {
    val subAccountIds = subAccounts.map(subAccount => subAccount.accountId)
    val accountIds = accountId +: subAccountIds
    val usersIds = subAccounts.filter(_.userId.nonEmpty).map(subAccount => subAccount.userId.get)
    for{
      _ <- v2Validator.isValidMigrationInput(accountType, subAccounts, Some(mergeAccountId), associateAllUsersToSubAccount)
      _ <- v2Validator.isPrimaryAccount(accountId)
      _ <- v2Validator.isAccountNotV2Provisioned(accountIds.toSet)
      _ <- v2Validator.isValidSubAccount(accountId, subAccountIds.toSet)
      _ <- v2Validator.isValidUser(accountIds.toSet, usersIds.toSet)
    } yield ()
  }

  def fetchAccountDetailsLite(accountId: Long): Future[Either[ErrorResponse, OnboardingResponse]] = {
      daoAccountV2.fetchAccountDetailsLite(accountId) map { accountDetailsLite =>
        val businessUserWithUserAccountAssociationSeq = accountDetailsLite._1
        val dtoAccount = accountDetailsLite._3
        val subaccounts = accountDetailsLite._4.map(tup => SubAccountV2(tup._1._2.accountId, tup._1._2.name, tup._2.description, tup._1._2.isActive, Some(tup._1._1.hierarchyPath)))
        val accountHierarchy = accountDetailsLite._2
        Right(
          OnboardingResponse(
            accountId = accountId,
            name = dtoAccount.name,
            publicId = dtoAccount.publicId,
            accountType = AccountTypes.byId(accountHierarchy.accountType) match {
              case Some(hierarchy) => hierarchy.name
              case None => "-"
            },
            users = businessUserWithUserAccountAssociationSeq.map(businessUserWithUserAccountAssociation => UserInfo(
              businessUserWithUserAccountAssociation._2.id,
              businessUserWithUserAccountAssociation._2.firstName,
                businessUserWithUserAccountAssociation._2.lastName,
                businessUserWithUserAccountAssociation._2.email)
          ).toSet
        ))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching account info", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def fetchAccountDetailsV2(accountId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountInfo]] = {
    def getSponsorBankDetails(): Future[Option[AccountIdName]] = {
        sponsorBankService.getSponsorBank(accountId) map {
          case Right(sponsorBankDetails) =>
            Some(sponsorBankDetails)
          case Left(error) =>
            logger.info(s"No Sponsor Bank Details found for $accountId", error)
            None
        }
    }

    def getLinkedPrograms(): Future[Seq[SponsorBankProgram]] = {
      sponsorBankService.getLinkedPrograms(accountId) map {
        case Right(linkedPrograms) if linkedPrograms.nonEmpty =>
          linkedPrograms
        case Right(_) =>
          logger.info(s"No Programs linked with SponsorBank $accountId")
          Seq.empty[SponsorBankProgram]
        case Left(error) =>
          logger.info(s"Unable to fetch linked programs for Sponsor Bank $accountId", error)
          Seq.empty[SponsorBankProgram]
      }
    }

    val partnerSubAccountInfoResponse = for{
      accountDetails <- daoAccountV2.fetchAccountDetails(accountId)
      sponsorBankDetails <- if(!accountDetails._8.isSponsorBank) {
              getSponsorBankDetails()
            } else {
              Future.successful(None)
            }
      linkedPrograms <- if(accountDetails._8.isSponsorBank) {
            getLinkedPrograms()
          } else {
              Future.successful(Seq.empty[SponsorBankProgram])
          }
    } yield  (accountDetails, sponsorBankDetails, linkedPrograms)
    partnerSubAccountInfoResponse.map { response =>
      val accountDetails = response._1
      val sponsorBankDetails = response._2
      val linkedPrograms = response._3

      val defaultRoles = SystemDefinedRoles.getAll.filter(_.roleType != SystemDefinedRoles.CUSTOMROLE.roleType).map(AccountConvertors.toUserRole)
      val roles = defaultRoles ++ accountDetails._5.map(AccountConvertors.toUserRole).toSet

      val businessUserWithUserAccountAssociationSeq = accountDetails._1
      val dtoUserAccountAssociationSeq = businessUserWithUserAccountAssociationSeq.map(businessUserWithUserAccountAssociation => businessUserWithUserAccountAssociation._1)
      val userAccountRoleAssociationSeq = accountDetails._7
      val userVsRoleMap = dtoUserAccountAssociationSeq.map(dtoUserAccountAssociation => dtoUserAccountAssociation.businessUserId -> userAccountRoleAssociationSeq.filter(_.userAccountAssociationId == dtoUserAccountAssociation.id)).toMap
      val dtoAccount = accountDetails._8
      val subaccounts = accountDetails._9.map(tup => SubAccountV2(tup._1._2.accountId, tup._1._2.name, tup._2.description, tup._1._2.isActive, Some(tup._1._1.hierarchyPath)))
      val subAccountsNotMigratedSeq = accountDetails._10
      val usersAccountSeq = accountDetails._11
      val subAccountsNotMigrated = subAccountsNotMigratedSeq.map(subAccount => AccountUserInfo(subAccount._1.accountId, subAccount._1.name, subAccount._2.description, usersAccountSeq.filter(_.accountId == subAccount._1.accountId).map(u => UserInfo(u.id, u.firstName, u.lastName, u.email))))
      val isSponsorBank = accountDetails._8.isSponsorBank
      Right(
        PartnerAndSubAccountInfo(
          accountId = accountId,
          name = dtoAccount.name,
          firstActivatedAt = dtoAccount.firstActivatedAt.map(new DateTime(_, DateTimeZone.UTC).toString),
          publicId = dtoAccount.publicId,
          hierarchyPath = accountDetails._2.hierarchyPath,
          administer = accountDetails._2.administer,
          isSponsorBank = isSponsorBank,
          accountTypeId = accountDetails._2.accountType,
          accountType = AccountTypes.byId(accountDetails._2.accountType) match {
            case Some(hierarchy) => hierarchy.name
            case None => "-"
          },

          users = businessUserWithUserAccountAssociationSeq.map(businessUserWithUserAccountAssociation => AccountConvertors.toBusinessUserWithUserAccountAssociation(
            businessUserWithUserAccountAssociation._2,
            businessUserWithUserAccountAssociation._1,
            userVsRoleMap(businessUserWithUserAccountAssociation._1.businessUserId).filter(_.userRoleId.isDefined).map(_.userRoleId.get).toSet,
            userVsRoleMap(businessUserWithUserAccountAssociation._1.businessUserId).map(_.roleType).toSet
          )).toSet,

          roles = roles,
          permissionTemplates = AccountConvertors.toPermissionTemplateInfo(accountDetails._3, accountDetails._4, accountDetails._5, accountDetails._6, dtoUserAccountAssociationSeq),
          subAccounts = subaccounts,
          subAccountsNotMigrated = subAccountsNotMigrated,
          sponsorBankDetails = sponsorBankDetails,
          linkedPrograms = linkedPrograms
        )
      )
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching account info", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def fetchUserDetails(userId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]] = {
    daoAccountV2.doesUserAccountAssociationExist(userId) flatMap { userAccountAssociationExists =>
      if(userAccountAssociationExists) {
        logger.info(s"User account association exists for $userId")
        daoAccountV2.fetchUserDetails(userId) map { resp =>
          Right(AccountConvertors.toPartnerAndSubAccountUserInfo(userId, resp._1, resp._2, resp._3, resp._4, resp._5, resp._6))
        }
      } else {
        logger.info(s"Business user doesn't exist with id - $userId")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser)))
      }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching business user", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def fetchUserDetailsByEmail(email: String): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]] = {
    daoAccountV2.getUserIdByEmail(email) flatMap {
      case Some(userId) =>
        daoAccountV2.doesUserAccountAssociationExist(userId) flatMap { userAccountAssociationExists =>
          if (userAccountAssociationExists) {
            logger.info(s"User account association exists for $userId")
            daoAccountV2.fetchUserDetails(userId) map { resp =>
              Right(AccountConvertors.toPartnerAndSubAccountUserInfo(userId, resp._1, resp._2, resp._3, resp._4, resp._5, resp._6))
            }
          } else {
            logger.info(s"Business user doesn't exist with id - $userId")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser)))
          }
        } recover {
          case e: Exception =>
            logger.info(s"Error occurred while fetching business user", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
        }
      case _ =>
        logger.info(s"Business user doesn't exist with email - $email")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser)))
    }
  }


  def getBusinessUserWithRoles(accountIds: Set[Long]): Future[(Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], Map[Long, Map[Int, Set[Int]]])] = {

    daoAccountV2.fetchUserEnvironmentRoles(accountIds) map {
      resp: (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoBusinessUserEnvironmentRole], Seq[DtoAccountPermission]) =>
        val businessUsers = resp._1
        val environments = resp._2

        val accountUserEnvMap = accountIds.map(accountId => {
          val accountBusinessUsers = businessUsers.filter(_.accountId == accountId)
          val accountEnv = environments.filter(_.accountId == accountId)
          val modules = resp._4.filter(_.accountId == accountId)
          accountId -> (accountBusinessUsers, accountEnv, modules)
        }).toMap
        val userRoleMap = businessUsers.map(user => {
          val roles = resp._3.filter(_.businessUserId == user.id)
          val commonRoles : Seq[Int] = roles.filter(r =>  getGlobalPermissions.map(_.id).contains(r.role)).map(d => DashboardUserRole(d.role).id)
          val envRoles = accountUserEnvMap(user.accountId)._2.map{ a:DtoEnvironment =>
            (EnvironmentConstants(a.environmentType.toInt).id,
              roles.filter{ _.environmentId.contains(a.id)}.
                filterNot(er => getGlobalPermissions.map(_.id).contains(er.role)).
                map(d => DashboardUserRole(d.role).id).toSet)
          }
          user.id -> (envRoles :+ (EnvironmentTypes.GLOBAL_ENVIRONMENT.id, commonRoles.toSet)).toMap
        }).toMap
        (accountUserEnvMap, userRoleMap)
    }
  }

  def fetchSubAccountMigrationAccountDetails(subAccounts: Seq[DtoAccount]) : Future[Seq[MigrationAccountDetails]] = {
    val subAccountsInfo = subAccounts.map( subAccount =>
      fetchAccountDetails(subAccount.accountId) map {
        case Right(accountDetails) => accountDetails
        case _ => throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
    )
    Future.sequence(subAccountsInfo)
  }

  def prepareSubAccountData(accountId: Long, accountType: Int, hierarchyPath: String, primaryUser: DtoBusinessUser, subAccounts: Seq[SubAccountMigrationDetails], accountUserEnvMap: Map[Long, (Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoAccountPermission])], userRoleMap: Map[Long, Map[Int, Set[Int]]], accountAttributes: Seq[DtoAccountAttribute], isPromoteSubAccount: Boolean, parentId: Option[Long], associateAllUsersToSubAccount: Boolean): (Seq[DtoAccountAttribute], Seq[DtoAccountPermission], Seq[DtoAccountHierarchy], Seq[DtoUserAccountAssociation], Seq[DtoUserAccountAssociation], Seq[DtoUserRole], Seq[Map[Int, String]]) = {
    val parentModules = (Seq(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) ++ accountUserEnvMap(accountId)._3.map(_.permission)).distinct
    val result = subAccounts.map{ subAccount =>
      val subAccountModules = parentModules diff accountUserEnvMap(subAccount.accountId)._3.map(_.permission)
      val dtoAccountPermissions: Seq[DtoAccountPermission] = subAccountModules.map(permission => DtoAccountPermission(subAccount.accountId, permission))
      val dtoAccountAttributes = accountAttributes.map(accountAttributes => DtoAccountAttribute(subAccount.accountId, accountAttributes.name, accountAttributes.value))
      val dtoAccountHierarchy = accountType match {
        case AccountTypes.DIRECT_CUSTOMER.id =>
          if(isPromoteSubAccount) {
            DtoAccountHierarchy(0, subAccount.accountId, s"$hierarchyPath${subAccount.accountId}/", accountType, Status.ACTIVE.id, administer = true, AccountManagementDefaults.PrimaryAdmins)
          } else {
            DtoAccountHierarchy(0, subAccount.accountId, s"$hierarchyPath${subAccount.accountId}/", AccountTypes.SUB_ACCOUNT.id, Status.ACTIVE.id, administer = true, AccountManagementDefaults.PrimaryAdmins)
          }
        case _ =>
          if(isPromoteSubAccount) {
            DtoAccountHierarchy(0, subAccount.accountId, s"$hierarchyPath${subAccount.accountId}/", accountType, Status.ACTIVE.id, subAccount.administer.getOrElse(false), AccountManagementDefaults.PrimaryAdmins)
          } else {
            DtoAccountHierarchy(0, subAccount.accountId, s"$hierarchyPath${subAccount.accountId}/", AccountTypes.SUB_ACCOUNT.id, Status.ACTIVE.id, subAccount.administer.getOrElse(false), AccountManagementDefaults.PrimaryAdmins)
          }
      }
      val dtoPrimaryUserAccountAssociation = subAccount.userId match {
        case Some(primaryUserId) =>
          DtoUserAccountAssociation(0, primaryUserId, subAccount.accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = true, None, None, clock.now)
        case None =>
          DtoUserAccountAssociation(0, primaryUser.id, subAccount.accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = true, None, None, clock.now)
      }

      val users = if(associateAllUsersToSubAccount) {
        parentId match {
          case None => accountUserEnvMap(subAccount.accountId)._1.filterNot(_.id == subAccount.userId.getOrElse(-1)) ++ accountUserEnvMap(accountId)._1.filterNot(_.id == primaryUser.id)
          case Some(parentId) => accountUserEnvMap(subAccount.accountId)._1.filterNot(_.id == subAccount.userId.getOrElse(-1)) ++ accountUserEnvMap(accountId)._1.filterNot(_.id == primaryUser.id) ++ accountUserEnvMap(parentId)._1.filterNot(_.id == primaryUser.id)
        }
      } else accountUserEnvMap(subAccount.accountId)._1.filterNot(_.id == subAccount.userId.getOrElse(-1))
      val dtoPrimaryUserAccountAssociationSeq = users.filter(_.isPrimaryUser).map{ user =>
        DtoUserAccountAssociation(0, user.id, subAccount.accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
      }
      val (dtoUserAccountAssociationSeq, dtoUserRoleSeq, permissionSeq) = users.filter(!_.isPrimaryUser).map{ user =>
        val userEnvRole = userRoleMap(user.id)
        val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(userEnvRole.flatMap(_._2).toSet).map(_.id)
        val dtoUserAccountAssociation = DtoUserAccountAssociation(0, user.id, subAccount.accountId, if(user.accountNonLocked) UserAccountAssociationStatuses.ACTIVE.id else UserAccountAssociationStatuses.LOCKED.id, user.isPrimaryUser, None, None, clock.now)
        val dtoUserRoles = DtoUserRole(0, s"Role_${user.id}", description = None, user.id, subAccount.accountId, clock.now)
        val permission = userEnvRole.map(envPermission =>
          envPermission._1 match {
            case EnvironmentTypes.GLOBAL_ENVIRONMENT.id =>
              val permissions = AccountConvertors.toDashboardPermissions(envPermission._2) ++ provisionPermissions
              (envPermission._1, permissions.mkString(","))
            case _ =>
              val permissions = AccountConvertors.toDashboardPermissions(envPermission._2).diff(provisionPermissions)
              (envPermission._1, permissions.mkString(","))
          }
        )
        (dtoUserAccountAssociation, dtoUserRoles, permission)
      }.unzip3
      (dtoAccountAttributes, dtoAccountPermissions, dtoAccountHierarchy, dtoPrimaryUserAccountAssociation +: dtoPrimaryUserAccountAssociationSeq, dtoUserAccountAssociationSeq, dtoUserRoleSeq, permissionSeq )
    }
    result.foldLeft((Seq.empty[DtoAccountAttribute], Seq.empty[DtoAccountPermission], Seq.empty[DtoAccountHierarchy], Seq.empty[DtoUserAccountAssociation], Seq.empty[DtoUserAccountAssociation], Seq.empty[DtoUserRole],  Seq.empty[Map[Int, String]])) { (res, cur) ⇒
      (res._1 ++ cur._1, res._2 ++ cur._2, res._3 :+ cur._3, res._4 ++ cur._4, res._5 ++ cur._5, res._6  ++ cur._6, res._7 ++ cur._7)
    }
  }

  def updateMigrationAudit(accountId: Long, accountType: Int, additionalInfo: String, initiatedBy: String, startTime: DateTime): Future[DtoAccountMigrationAudit] = {
    val processingTime = DateTime.now().getMillis - startTime.getMillis
    val dtoAccountMigrationAudit = DtoAccountMigrationAudit(0, accountId, accountType, Some(additionalInfo), MigrationStatus.FAILED.id, initiatedBy, startTime, processingTime)
    daoAccountV2.insertAccountMigrationAudit(dtoAccountMigrationAudit)
  }

  def getMigratedParentAccounts(): Future[Either[ErrorResponse, List[MigratedAccount]]] = {
    daoAccountV2.getMigratedAccounts() map {
      case list if list.nonEmpty =>
        Right(list.map { d =>
          MigratedAccount(id = d._2.accountId,
            name = d._2.name,
            accountType = d._1.accountType,
            hierarchyPath = d._1.hierarchyPath)
        }.toList)
      case list0 if list0.isEmpty => Right(List.empty)
    } recover {
      case t: Throwable =>
        logger.info("Unable to fetch migrated accounts", t)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def associateUserAccountRole(userId: Long, accountId: Long, userRoleId: Option[Long], roleType: Int, updatedBy: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    {for{
      _ <- daoAccountV2.getUser(userId) map { users =>
          if (users.nonEmpty && users.head.email.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail)) {
            logger.info(s"Non functional user cannot be associated User: ${users.head.email}, associated with Account: $accountId and Role $userRoleId")
            throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NonFunctionalMail))
          }
          if(users.nonEmpty && !users.head.email.matches(AccountManagementDefaults.internalEmailDomains)) { //RiskOS support roles validation for non-socure users
            val riskOsSupportRoles = List(SystemDefinedRoles.RISKOS_SUPPORT_ADMIN.roleType, SystemDefinedRoles.RISKOS_SUPPORT_VIEWER.roleType)
            if (riskOsSupportRoles.contains(roleType)) {
              logger.info(s"Risk OS Support role cannot be associated with User: ${users.head.email}, associated with Account: $accountId and Role $userRoleId")
              throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
            }
          }
      }
      _ <- daoAccountV2.getUserAccountRoleAssociation(userId, accountId, roleType, userRoleId) map { associations: Seq[DtoUserAccountRoleAssociation] =>
        if (associations.nonEmpty) {
          logger.info(s"UserAccountRoleAssociation exists - User: $userId, is associated with Account: $accountId and Role $userRoleId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.UserAccountRoleAssociationExists))
        }
      }
      accountHierarchy <- daoAccountV2.getAccountHierarchyByAccountId(accountId) map {
        case None =>
          logger.info(s"Account: $accountId not valid (did not find in the accountHierarchy)")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
        case Some(ah) => ah
      }
      parentAccountHierarchy <- v2Validator.fetchParent(accountHierarchy) map  {
        case None =>
          logger.info(s"Unable to get the parent account for $accountId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.AccessForbidden))
        case Some(pah) => pah
      }
      result <- isValidUserAndApplicableRole(parentAccountHierarchy.accountId, accountId, roleType, userRoleId) flatMap  {
        case Right(true) =>
          daoAccountV2.getAllUserAccountAssociation(userId, accountId).flatMap {
            case Some(uaa) =>
              val dtoUserAccountAssociation = uaa.copy(status = UserAccountAssociationStatuses.ACTIVE.id)
              daoAccountV2.updateUserAccountAssociationWithRole(dtoUserAccountAssociation, roleType, userRoleId)
            case None =>
              val dtoUserAccountAssociation = DtoUserAccountAssociation(
                id = 0,
                businessUserId = userId,
                accountId = accountId,
                status = UserAccountAssociationStatuses.ACTIVE.id,
                isPrimaryUser = false, // TODO: Remove isPrimaryUser attribute from User Account Association as it is no longer valid with System Roles
                revoked = None,
                updatedBy = updatedBy,
                updatedAt = clock.now
              )
              daoAccountV2.insertUserAccountAssociationWithRole(dtoUserAccountAssociation, roleType, userRoleId)
          }
        case Left(errorResponse: ErrorResponse) =>
          logger.info(s"Unable to get the roles for account(parent): ${parentAccountHierarchy.accountId}, User: $userId, UserRole: $userRoleId")
          throw ErrorResponseException(errorResponse)
        case _ =>
          logger.info(s"Unable to get the roles for account(parent): ${parentAccountHierarchy.accountId}, User: $userId, UserRole: $userRoleId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound))
      }
    } yield result match {
      case n:Boolean => Right(n)
      case n:Long if n > 0 => Right(true)
      case _ => Right(false)
    }} recover {
        case ex: ErrorResponseException =>
          logger.info("Could not associate User with Account", ex)
          Left(ex.errorResponse)
        case e : Throwable =>
          logger.info("Could not associate User with Account", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
  }

  private def isValidUserAndApplicableRole(parentAccountId: Long, accountId: Long, roleType: Int, userRoleId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    if (!SystemDefinedRoles.isValidRoleType(roleType)) {
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleType)))
    } else if (roleType != SystemDefinedRoles.CUSTOMROLE.roleType) {
      canApplySystemRole(accountId, roleType)
    } else if (userRoleId.isDefined){
      daoAccountV2.isValidUserRole(parentAccountId, roleType, userRoleId.get) map {
        case true => Right(true)
        case false => Left(ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound))
      }
    } else {
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidCustomRole)))
    }
  }

  private def canApplySystemRole(accountId: Long, roleType: Int): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.getAccountUsersWithSystemRole(accountId, roleType) flatMap {
      existingUserWithRole =>
        daoAccountV2.getSystemRoleMaxCount(roleType) map {
          case Some(dtoSystemRolesMaxCount: DtoSystemRolesMaxCount) =>
            if (existingUserWithRole < dtoSystemRolesMaxCount.maxCount) {
              Right(true)
            } else {
              Left(ErrorResponseFactory.get(ExceptionCodes.RoleLimitReached))
            }
          case None => Right(true)
        }
    }
  }

  def updateAccountType(accountId: Long, accountType: Int): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.getAccountHierarchyByAccountId(accountId).flatMap {
      case Some(dtoAccountHierarchy) =>
        dtoAccountHierarchy.accountType match {
          case AccountTypes.PROSPECT.id => convertProspectToDirectCustomer(accountId, accountType, dtoAccountHierarchy)
          case _ =>
            accountType match {
              case AccountTypes.RESELLER.id =>
                if (dtoAccountHierarchy.accountType == AccountTypes.SUB_ACCOUNT.id) {
                  logger.info(s"Error occurred while updating the Account Type, can not update account type of subAccount. accountId: $accountId accountType: $accountType")
                  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountType)))
                } else if (dtoAccountHierarchy.accountType == accountType) {
                  logger.info(s"Error occurred while updating the Account Type, account is already of accountType $accountType. accountId: $accountId accountType: $accountType")
                  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountType)))
                } else {
                  daoAccountV2.updateAccountHierarchy(dtoAccountHierarchy.copy(accountType = AccountTypes.RESELLER.id)).map {
                    case n if n > 0 => Right(true)
                    case _ =>
                      logger.info(s"Error occurred while updating the Account Type. accountId: $accountId accountType: $accountType")
                      Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountType))
                  }
                }
              case _ =>
                logger.info(s"Error occurred while updating the Account Type, invalid account type. accountId: $accountId accountType: $accountType")
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType)))
            }
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the Account Type accountId: $accountId accountType: $accountType", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound))
    }
  }

  private def convertProspectToDirectCustomer(accountId:Long, accountType:Int, dtoAccountHierarchy: DtoAccountHierarchy):Future[Either[ErrorResponse,Boolean]] = {
    daoAccountV2.getProspectDetails(accountId).flatMap {
      case prospectDetails =>
        val (subAccounts,accountAssociationIds,associatedRoles) = prospectDetails
        val isValidProspect = subAccounts.size.equals(1) && accountAssociationIds.size.equals(1) && associatedRoles.size.equals(1) && associatedRoles.head.equals(SystemDefinedRoles.PROSPECT.roleType)
        if (isValidProspect) {
          daoAccountV2.upgradeProspectToDirectCustomer(accountId, SystemDefinedRoles.ACCOUNTOWNER.roleType, Option.empty, accountAssociationIds.head, dtoAccountHierarchy.copy(accountType = AccountTypes.DIRECT_CUSTOMER.id)).flatMap {
            case true => Future.successful(Right(true))
            case _ =>
              logger.info(s"Error occurred while updating the prospect role association to account owner. accountId: $accountId accountType: $accountType")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole)))
          }
        } else {
          if (subAccounts.size > 1) {
            logger.info(s"Invalid account provided, account has sub-accounts. accountId: $accountId accountType: $accountType")
          } else if (accountAssociationIds.size > 1) {
            logger.info(s"Invalid account provided, account has multiple accounts associated. accountId: $accountId accountType: $accountType")
          } else if (!associatedRoles.size.equals(1) && !associatedRoles.head.equals(SystemDefinedRoles.PROSPECT.roleType)) {
            logger.info(s"Invalid account provided, account has multiple roles associated. accountId: $accountId accountType: $accountType")
          }
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType)))
        }
      case _ =>
        logger.info(s"Error occurred while updating the Account Type accountId: $accountId accountType: $accountType")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType)))
    }recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the Account Type accountId: $accountId accountType: $accountType", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountType))
    }
  }
  def updateAdminister(accountId: Long, administer: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.getAccountHierarchyByAccountId(accountId).flatMap {
      case Some(dtoAccountHierarchy) =>
        daoAccountV2.updateAccountHierarchy(dtoAccountHierarchy.copy(administer = administer)).map {
          case n if n > 0 => Right(true)
          case _ =>
            logger.info(s"Error occurred while updating the Administer accountId: $accountId administer: $administer")
            Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAdminister))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating the Administer accountId: $accountId administer: $administer", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAdminister))
    }
  }

  def updateIsSponsorBank(accountId: Long, isSponsorBank: Boolean, initiatedBy: String): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountV2.updateIsSponsorBank(accountId, isSponsorBank).flatMap {
      case n if n > 0 =>
        if(!isSponsorBank){
          sponsorBankService.unlinkSponsorBankPrograms(accountId, initiatedBy) map {
            case Right(true) => Right(true)
            case Right(false) => Right(false)
            case Left(err) =>
              logger.info("IsSponsorBank status updated. But Unlink Programs Failed. ", err)
              Left(err)
          }
        } else {
          Future.successful(Right(true))
        }
      case _ =>
        logger.info(s"Error occurred while updating accountId: $accountId Sponsor Bank Option: $isSponsorBank")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateIsSponsorBank)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating accountId: $accountId Sponsor Bank Option: $isSponsorBank", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateIsSponsorBank))
    }
  }

  def swapUserRoles(accountId: Long, userId: Long, swappingUserId: Long): Future[Either[ErrorResponse, Boolean]]  = {
    {for{
      userAccountAssociationId <- daoAccountV2.getAllUserAccountAssociation(userId, accountId) map {
        case None =>
          logger.info(s"UserAccountAssociation does not exists - User: $userId, is not associated with Account: $accountId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
        case Some(userAccountAssociation) => userAccountAssociation.id
      }
      swappingUserAccountAssociationId <- daoAccountV2.getAllUserAccountAssociation(swappingUserId, accountId) map {
        case None =>
          logger.info(s"UserAccountAssociation does not exists - User: $userId, is not associated with Account: $accountId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.UserAccountAssociationsFetchError))
        case Some(userAccountAssociation) => userAccountAssociation.id
      }
      (deletedEntries, updatedRolesforUser, updatedRolesforSwapUser) <- daoAccountV2.swapUserRoles(userAccountAssociationId, swappingUserAccountAssociationId)
    } yield if (deletedEntries == updatedRolesforUser + updatedRolesforSwapUser) {
      Right(true)
    } else {
      Right(false)
    }} recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not swap User roles of users $userId and $swappingUserId of account $accountId", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not swap User roles of users $userId and $swappingUserId of account $accountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

}
