package me.socure.account.service

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.Clock
import me.socure.common.publicid.PublicId
import me.socure.model.ErrorResponse
import me.socure.model.account.{PublicWebhook, PublicWebhookWithPublicAccountId, PublicWebhooksWithPublicAccountId, SubscriptionStatuses}
import me.socure.storage.slick.dao.{DaoPublicWebhook, DaoSubscriptionStatus}
import me.socure.storage.slick.tables.account.{DtoPublicWebhook, DtoPublicWebhookWithName}
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class PublicWebhookService(daoPublicWebhook: DaoPublicWebhook,
                           daoSubscriptionStatus: DaoSubscriptionStatus,
                           clock: Clock)(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def listPublicWebhooks: Future[Either[ErrorResponse, Seq[PublicWebhook]]] = {
    daoPublicWebhook.listPublicWebhooks().map(webhookList => {
      Right(
        webhookList.map(webhook => {
          PublicWebhook(
            None,
            None,
            webhook.endpoint,
            webhook.certificate,
            webhook.environmentId,
            webhook.createdAt,
            webhook.updatedAt
          )
        })
      )
    })
  }

  def insertPublicWebhook(environmentId: Long, endpoint: String, encodedCertificate: String, subscriptionType: Long): Future[Either[ErrorResponse, Boolean]] = {
    val certificateObject = DtoPublicWebhook(id = 0, environmentId = environmentId, endpoint = endpoint, certificate = encodedCertificate, updatedAt = DateTime.now(), createdAt = DateTime.now())
    daoPublicWebhook.getPublicWebhooksByEnvironment(environmentId, Some(subscriptionType)) flatMap {
      case (a,_) =>
        if(a.size < 1) {
          daoPublicWebhook.savePublicWebhook(certificateObject, subscriptionType, subscriptionStatus = 1).map { webhook : DtoPublicWebhook=>
            if(webhook.id > 0) {
              Right(true)
            } else {
              Left(ErrorResponseFactory.get(UnableToCreatePublicWebhook))
            }
          } recover {
            case e =>
              logger.info("Could not create public webhook : ", e)
              Left(ErrorResponseFactory.get(UnableToCreatePublicWebhook))
          }
        } else {
          logger.info(s"Webhook already exist for environmentId: $environmentId, subscriptionType: $subscriptionType")
          Future.successful(Left(ErrorResponseFactory.get(WebhookAlreadyExist)))
        }
      case _ =>
        logger.info(s"Some error occurred while checking if webhook exist for environmentId: $environmentId, subscriptionType: $subscriptionType")
        Future.successful(Left(ErrorResponseFactory.get(UnableToCreatePublicWebhook)))
    }
  }

  def updatePublicWebhook(environmentId: Long, endpoint: String, encodedCertificate: String, subscriptionType: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoPublicWebhook.getPublicWebhook(environmentId, subscriptionType).flatMap {
      case Some(webhookResult) =>
        val updatedPublicWebhook = DtoPublicWebhook(id = webhookResult.id, environmentId = environmentId, endpoint = endpoint, certificate = encodedCertificate, updatedAt = DateTime.now(), createdAt = DateTime.now())
        daoPublicWebhook.updatePublicWebhook(updatedPublicWebhook, Set(subscriptionType), subscriptionStatus = 1) map  {
          case a:Int if a > 0 =>
            Right(true)
          case _ => Left(ErrorResponseFactory.get(UnableToUpdatePublicWebhook))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(NoPublicWebhookFound)))
    } recover {
      case e =>
        logger.info(s"Could not update public webhook for environmentId:$environmentId, $subscriptionType", e)
        Left(ErrorResponseFactory.get(UnableToUpdatePublicWebhook))
    }
  }

  @deprecated("Use getPublicWebhook(environmentId, subscriptionType")
  def getPublicWebhook(environmentId: Long): Future[Either[ErrorResponse, PublicWebhook]] = {
    daoPublicWebhook.getPublicWebhook(environmentId) map {
      case Some(webhookResult) => Right(
        PublicWebhook(
          Some(webhookResult.subscriptionType),
          Some(SubscriptionStatuses.byId(webhookResult.subscriptionStatus).getOrElse(SubscriptionStatuses.ENABLE).label),
          webhookResult.endpoint,
          webhookResult.certificate,
          webhookResult.environmentId,
          webhookResult.createdAt,
          webhookResult.updatedAt
        )
      )
      case _ => Left(ErrorResponseFactory.get(NoPublicWebhookFound))
    }
  }

  def getPublicWebhook(environmentId: Long, subscriptionType: Long): Future[Either[ErrorResponse, PublicWebhook]] = {
    daoPublicWebhook.getPublicWebhook(environmentId, subscriptionType) map {
      case Some(webhookResult) => Right(
        PublicWebhook(
          Some(webhookResult.subscriptionType),
          Some(SubscriptionStatuses.byId(webhookResult.subscriptionStatus).getOrElse(SubscriptionStatuses.ENABLE).label),
          webhookResult.endpoint,
          webhookResult.certificate,
          webhookResult.environmentId,
          webhookResult.createdAt,
          webhookResult.updatedAt
        )
      )
      case _ => Left(ErrorResponseFactory.get(NoPublicWebhookFound))
    }
  }

  def getPublicWebhookByAccount(accountId: Long, envTypeId: Long, subscriptionType: Long): Future[Either[ErrorResponse, PublicWebhookWithPublicAccountId]] = {
    daoPublicWebhook.getAccountDetails(accountId) flatMap {
      case Some(ad) =>
        val parentId = ad.parentId.getOrElse(accountId)
//        evaluate permission at parent level
        daoPublicWebhook.isAccountProvisioned(parentId, subscriptionType) flatMap { p =>
          val provisioned = if(p._2) "Yes" else "No"
//          get secret key of child
          daoPublicWebhook.isAccountProvisioned(accountId, subscriptionType) flatMap { child =>
            daoPublicWebhook.getPublicWebhookByAccount(accountId, envTypeId, subscriptionType) map {
              case Some(values) =>
                val pwh = PublicWebhook(
                  Some(values._3.name),
                  Some(SubscriptionStatuses.byId(values._2.subscriptionStatus).getOrElse(SubscriptionStatuses.ENABLE).label),
                  values._1.endpoint,
                  values._1.certificate,
                  values._1.environmentId,
                  values._1.createdAt,
                  values._1.updatedAt
                )
                Right(PublicWebhookWithPublicAccountId(Some(pwh), PublicId(ad.publicId), provisioned, child._1))
              case _ => None
                Right(PublicWebhookWithPublicAccountId(None, PublicId(ad.publicId), provisioned, child._1))
            }
          }
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    } recover {
      case e =>
        logger.info(s"Problem in fetching webhooks for accountId, environmentType and the subscription type $accountId, $envTypeId, $subscriptionType", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getPublicWebhooks(envId: Long, subscriptionType: Option[Long]): Future[Either[ErrorResponse, PublicWebhooksWithPublicAccountId]] = {
    daoPublicWebhook.getPublicWebhooksByEnvironment(envId, subscriptionType) map {
      case webhookResults : (Seq[DtoPublicWebhookWithName], Option[String]) if webhookResults._1.nonEmpty =>
        val publicWebhooks = webhookResults._1.map{ webhookResult =>
          PublicWebhook(
            Some(webhookResult.subscriptionType),
            Some(SubscriptionStatuses.byId(webhookResult.subscriptionStatus).getOrElse(SubscriptionStatuses.ENABLE).label),
            webhookResult.endpoint,
            webhookResult.certificate,
            webhookResult.environmentId,
            webhookResult.createdAt,
            webhookResult.updatedAt
          )
        }
        Right(PublicWebhooksWithPublicAccountId(publicWebhooks, PublicId(webhookResults._2.getOrElse(throw new Exception(NoPublicWebhookFound.description)))))
      case _ =>
        logger.info(s"Problem in fetching webhooks for environment and/or the subscrition type $envId")
        Left(ErrorResponseFactory.get(NoPublicWebhookFound))
    }
  }

  def deletePublicWebhook(environmentId: Long, subscriptionType: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoSubscriptionStatus.deleteSubscriptionType(environmentId, Set(subscriptionType)).map {
      case a:Int if a > 0 =>
        Right(true)
      case _ =>
        logger.info(s"Could not delete public webhook(mark subscription type as deleted) for environmentId:$environmentId, $subscriptionType")
        Left(ErrorResponseFactory.get(UnableToDeletePublicWebhook))
    }
  }
}
