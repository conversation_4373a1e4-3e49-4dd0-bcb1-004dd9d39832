package me.socure.account.service

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{SaiPreferencesNotFound, SaiPreferencesUpsertFailed}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.sai.{SAIPreferences, SAIPreferencesUpsertRequest}
import me.socure.storage.slick.dao.DaoSaiPreferences
import me.socure.storage.slick.tables.account.DtoSaiPreferences
import org.slf4j.LoggerFactory

import scala.concurrent.duration.DurationInt
import scala.concurrent.{Await, ExecutionContext, Future}

class SaiPreferencesService(
                               daoSaiPreferences: DaoSaiPreferences,
                               daoAccount: DaoAccount,
                               clock: Clock
                           )(implicit ec: ExecutionContext) {
    private val logger = LoggerFactory.getLogger(classOf[SaiPreferencesService])

    def saveSaiPreferences(request: SAIPreferencesUpsertRequest): Future[Either[ErrorResponse, Unit]] = {
        if(!isRequestValid(request)) {
            Future.successful(Left(ErrorResponse(400, "Mandatory fields for SAIPreference upsert are missing/empty.")))
        }
        else {
            daoSaiPreferences
                .saveSaiPreferences(
                    DtoSaiPreferences(
                        accountId = request.accountId.toLong,
                        memo = request.memo,
                        depositorName = request.depositorName,
                        physicalAddress = request.physicalAddress,
                        state = request.state,
                        city = request.city,
                        zip = request.zip,
                        country = request.country,
                        triceUltimateSendingPartyId = None,
                        createdBy = request.initiatedBy,
                        updatedBy = request.initiatedBy,
                        createdAt = Some(clock.now()),
                        updatedAt = Some(clock.now())
                        )) flatMap {
                case 1 => Future.successful(Right())
                case _ => Future.successful(Left(ErrorResponseFactory.get(SaiPreferencesUpsertFailed)))
            } recover {
                case e =>
                    logger.info("Could not save SAI Preferences due to : ", e)
                    Left(ErrorResponseFactory.get(SaiPreferencesUpsertFailed))
            }
        }
    }

    def fetchSaiPreferences(accountId: Long): Future[Either[ErrorResponse, Option[SAIPreferences]]] = {
        daoAccount.getAccount(accountId).flatMap {
            case Some(_) =>
                daoSaiPreferences.getSaiPreferences(accountId) flatMap {
                    case Some(dtoSaiPreferences) => {
                        val saiPreferences = SAIPreferences(
                            memo = dtoSaiPreferences.memo,
                            triceUltimateSendingPartyId = dtoSaiPreferences.triceUltimateSendingPartyId,
                            depositorName = dtoSaiPreferences.depositorName,
                            physicalAddress = dtoSaiPreferences.physicalAddress,
                            city = dtoSaiPreferences.city,
                            state = dtoSaiPreferences.state,
                            zip = dtoSaiPreferences.zip,
                            country = dtoSaiPreferences.country
                            )
                        Future.successful(Right(Some(saiPreferences)))
                    }
                    case _ =>
                        logger.info(s"sai preferences not obtained for $accountId")
                        Future.successful(Left(ErrorResponseFactory.get(SaiPreferencesNotFound)))
                }
            case _ =>
                logger.info(s"sai preferences not obtained for $accountId")
                Future.successful(Left(ErrorResponseFactory.get(SaiPreferencesNotFound)))
        }
    }

    /**
     * Checks if mandatory fields for Upsert is missing/empty
     * @param request
     * @return
     */
    private def isRequestValid(request: SAIPreferencesUpsertRequest): Boolean = {
        Option(request.accountId).forall(_.trim.nonEmpty) &&
            request.depositorName.forall(_.trim.nonEmpty) &&
            request.physicalAddress.forall(_.trim.nonEmpty) &&
            request.state.forall(_.trim.nonEmpty) &&
            request.city.forall(_.trim.nonEmpty) &&
            request.zip.forall(_.trim.nonEmpty) &&
            request.country.forall(_.trim.nonEmpty) &&
            request.initiatedBy.forall(_.trim.nonEmpty)
    }
}