package me.socure.account.service

import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.account.validator.V2Validator
import me.socure.common.exception.ErrorResponseException
import me.socure.model.ErrorResponse
import me.socure.model.account.SubAccountCreationRequest
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class ConfigureAccountService(v2Validator: V2Validator,
                              dashboardAccountServiceV2: DashboardAccountServiceV2
                             )(implicit ec: ExecutionContext) {

  private val logger: Logger = LoggerFactory.getLogger(getClass)

  def createSubAccount(subAccountCreationRequest: SubAccountCreationRequest): Future[Either[ErrorResponse, Boolean]] = {
    (for {
      _ <- v2Validator.validateSubAccountCreationRequest(subAccountCreationRequest)
      res <- dashboardAccountServiceV2.createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest)
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Error occurred while validating account access", {ex.errorResponse})
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Error occurred while validating account access", e)
        Left(ErrorResponseFactory.get(PublicExceptionCodes.UnknownError))
    }
  }
}
