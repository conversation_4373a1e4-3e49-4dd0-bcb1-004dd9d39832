package me.socure.account.service

import com.amazonaws.regions.Regions
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, CustomerKeyDetails, EncryptedKey, EncryptedKeyDetails, EncryptedServiceKeys, KmsArnDetails, KmsId}

import scala.concurrent.Future

/**
  * Created by jamesanto on 4/11/17.
  */
trait EncryptionKeysService {

  def generate(accountId: AccountId): Future[Boolean]

  def generateCustomerKeys(parentAccountId: AccountId, subAccountIds: Set[AccountId], kmsId: KmsId): Future[Boolean]

  def regenerate(accountId: AccountId): Future[Map[Regions, EncryptedKey]]

  def getKeys(accountId: AccountId): Future[Map[Regions, EncryptedKey]]

  def hasKeys(accountId: AccountId): Future[Boolean]

  def getActiveCustomerKeys(accountId: AccountId): Future[Seq[KmsArnDetails]]

  def getAllActiveKeys(accountId: AccountId): Future[EncryptedKeyDetails]

  def getServiceAccountKeys(serviceName: String, accountId: AccountId): Future[Map[Regions, EncryptedKey]]

  def getAllKeysForService(serviceName: String, regionStr: String): Future[Either[ErrorResponse, EncryptedServiceKeys]]

  def testCustomerKms(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]]

}

object EncryptionKeysService {

  case class EncryptionKeysAlreadyExistException(accountId: AccountId) extends Exception(s"Encryption keys already exist for $accountId")

  case class NoEncryptionKeyFoundException(accountId: AccountId) extends Exception(s"No encryption key found for $accountId")

  case class NoKMSIdFoundException(accountId: AccountId) extends Exception(s"No KMSId found for region $accountId")

  case class InvalidKMSIdException(accountId: AccountId, kmsId: KmsId) extends Exception(s"Invalid KMSId: $kmsId provided for account $accountId")

  case class InvalidServiceNameException(serviceName: String) extends Exception(s"Invalid Service Name: $serviceName provided")

  case class RegionNotSupportedException(arn: String) extends Exception(s"Region not supported for Arn: $arn")

  case class UpsertCustomerKeysFailedException(arn: String, parentAccount: AccountId) extends Exception(s"Add/Update Customer Keys failed for ParentAccount($parentAccount) Arn: $arn")

  case class KeysAreNotMutuallyExclusiveException(arn: String, parentAccount: AccountId) extends Exception(s"Both Internal and External key found. Generation of Customer Keys failed for ParentAccount($parentAccount) Arn: $arn")

}



