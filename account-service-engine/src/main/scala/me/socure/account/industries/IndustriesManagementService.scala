package me.socure.account.industries

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.account.AccountDetails
import me.socure.model.{ErrorResponse, Industry}
import me.socure.storage.slick.dao.DaoIndustries
import me.socure.storage.slick.tables.industry.DtoIndustry
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 8/31/16.
  */
class IndustriesManagementService(daoIndustries: DaoIndustries)(implicit val ec : ExecutionContext) {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  def getIndustriesList : Future[Either[ErrorResponse, Vector[Industry]]] = {
    daoIndustries.getIndustriesList map {
      case list if list.nonEmpty => Right(list.map(i => Industry(i.sector, i.description)).toVector)
      case _ => Right(Vector.empty[Industry])
    } recover {
      case e : Throwable =>
        logger.info("Could not get industries list", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def upsertIndustry(industry: Industry) = {
    def upsertIndustry0(isDeleted: Boolean) = {
      daoIndustries.upsert(new DtoIndustry(sector = industry.sector, description = industry.description, isDeleted)) map {
        case affectedRows if (affectedRows == 1) => Right(true)
        case _ => Left(ErrorResponseFactory.get(UnknownError))
      }
    }
    daoIndustries.getBySector(industry.sector) flatMap {
      case Some(ind) => upsertIndustry0(ind.isDeleted)
      case _=> upsertIndustry0(false)
    }
  }

  def deleteIndustry(sector: String) : Future[Either[ErrorResponse, Boolean]] = getAccountByIndustry(sector) flatMap {
    case Left(msg) =>
      daoIndustries.delete(sector) map { _ =>
        Right(true)
      }
    case _ =>
      Future.successful(Left(ErrorResponseFactory.get(AccountsAssociatedWithIndustry)))
  }

  def getAccountByIndustry(industrySector : String) : Future[Either[ErrorResponse, Seq[AccountDetails]]] = {

    daoIndustries.getAccountNameEmailByIndustry(industrySector) map {
      case accounts if accounts.nonEmpty => {
        val details =  accounts map { account =>
          AccountDetails(account._1, account._2, account._3)
        }
        Right(details)
      }
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getIndustryBySector(sector : String) : Future[Either[ErrorResponse, Industry]] = {
    daoIndustries.getBySector(sector) map {
      case Some(industry) => Right(Industry(industry.sector, industry.description))
      case _ => Left(ErrorResponseFactory.get(IndustryNotFound))
    }
  }

}
