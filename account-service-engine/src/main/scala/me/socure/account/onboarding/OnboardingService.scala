package me.socure.account.onboarding

import me.socure.account.automation.AccountAutomationService
import me.socure.account.dashboardv2.{DashboardAccountServiceV2, DashboardUserServiceV2}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{BusinessUserService, PartnerAndSubAccountInfoService, ProductService}
import me.socure.account.superadmin.ManageAccountsService
import me.socure.constants.{AccountManagementDefaults, SystemDefinedRoles}
import me.socure.model.account.ProductAccountTypes.ProductAccountType
import me.socure.model.account.automation.{ProductConfiguration, UpdateAccountProvisioningDetails}
import me.socure.model.account.{ProductAccountTypes, SubAccount}
import me.socure.model.dashboardv2.{AccountWithRolesInput, CreateBusinessUserInput, Creator, RolesInputDetails, SubAccountV2}
import me.socure.model.user.BusinessUserWithUserAccountAssociation
import me.socure.model.{ErrorResponse, UpdateProduct}
import me.socure.storage.slick.dao.DaoAccountUIConfiguration
import me.socure.storage.slick.tables.account.DtoAccountUIConfiguration
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, Future}

class OnboardingService(
                         businessUserService: BusinessUserService,
                         dashboardAccountServiceV2: DashboardAccountServiceV2,
                         dashboardUserServiceV2: DashboardUserServiceV2,
                         manageAccountsService: ManageAccountsService,
                         productService: ProductService,
                         accountAutomationService: AccountAutomationService,
                         daoAccountUIConfiguration: DaoAccountUIConfiguration,
                         partnerAndSubAccountInfoService: PartnerAndSubAccountInfoService
                       )(implicit ec: ExecutionContext) {

  private val logger: Logger = LoggerFactory.getLogger(this.getClass)
  private val idleTimeout = 120
  private val autoSignout = 1440

  def registration(onboardingRequest: OnboardingRequest): Future[Either[ErrorResponse, OnboardingResponse]] = {

    def createSubAccountFlow(userId: Long, accountId: Long, subAccountRequest: SubAccountRequest): Future[Either[ErrorResponse, Boolean]] = {
      val subAccountInfo = SubAccount(subAccountRequest.companyName, subAccountRequest.industry)
      val subAccountAction = if (subAccountRequest.email == onboardingRequest.accountInfo.email) {
        logger.info("Sub-account owner is the same as parent account owner. NO User Creation")
        createSubAccount(userId, accountId, subAccountInfo, userId)
      } else {
        createUser(OnboardingUserRequest(
          subAccountRequest.email,
          subAccountRequest.firstName,
          subAccountRequest.lastName,
          subAccountRequest.contactNumber,
          accountId,
          userId)
        ).flatMap {
          case Right((subUserId, _)) =>
            logger.info(s"Sub-account user created with ID $subUserId.")
            createSubAccount(userId, accountId, subAccountInfo, subUserId)
          case Left(error) =>
            logger.info(s"Sub-account user creation FAILED.")
            Future.successful(Left(error))
        }
      }

      subAccountAction.flatMap {
        case Right(subAccountId) =>
          provision(ProvisionRequest(
            subAccountId,
            subAccountRequest.productConfiguration,
            subAccountRequest.modulesToAdd,
            subAccountRequest.modulesToRemove
          ))
        case Left(error) =>
          Future.successful(Left(error))
      }
    }

    for {
      registrationResult <- businessUserService.registerV2(
        onboardingRequest.accountInfo,
        isActive = onboardingRequest.isActive.getOrElse(false),
        isDashboardV3 = true,
        isInternalOpt = onboardingRequest.isInternal
      )
      accountInfoResponse <- registrationResult match {
        case Right(true) =>
          getUserId(onboardingRequest.accountInfo.email).flatMap {
            case Right((userId, accountId)) =>
              logger.info(s"Registration succeeded for account ID $accountId.")
              setTimeout(accountId, autoSignout, idleTimeout).map { timeoutConfig =>
                logger.info(s"Timeout settings configured successfully for account ID $accountId.")
                Right((userId, accountId))
              }.recover {
                case ex =>
                  logger.error(s"Failed to set timeout settings for account ID $accountId: ${ex.getMessage}")
                  Left(ErrorResponseFactory.get(ExceptionCodes.TimeoutConfigurationFailed))
              }
              Future.successful(Right((userId, accountId)))
            case _ =>
              logger.info(s"Failed to retrieve user details for email: ${onboardingRequest.accountInfo.email}.")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed)))
          }
        case Left(err) =>
          logger.info(s"Registration failed for email: ${onboardingRequest.accountInfo.email}. Error - ${err.message}")
          Future.successful(Left(err))
        case _ =>
          logger.info("Registration failed.")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed)))
      }
      provisionResult <- accountInfoResponse match {
        case Right((userId, accountId)) =>
          provision(
            ProvisionRequest(
              accountId,
              onboardingRequest.productConfiguration,
              onboardingRequest.modulesToAdd,
              onboardingRequest.modulesToRemove
            )
          ).map(_ => Right((userId, accountId)))
        case Left(err) =>
          Future.successful(Left(err))
      }
      accountInformation <- accountInfoResponse match {
        case Right((_ , accountId)) => partnerAndSubAccountInfoService.fetchAccountDetailsLite(accountId) flatMap {
          case Right(account) =>
            logger.info(s"Account information fetched for account ID $accountId.")
            Future.successful(Right(account))
          case Left(error) =>
            logger.info(s"Failed to fetch account information for account ID $accountId.")
            Future.successful(Left(error))
        }
        case Left(err) =>
          Future.successful(Left(err))
      }
    } yield accountInformation

  }

  def getUserId(email: String): Future[Either[ErrorResponse, (Long, Long)]] =
    businessUserService.getByEmail(email).map {
      case Some(user) => Right((user.id, user.accountId))
      case None =>
        logger.info(s"User not found for email: $email.")
        Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
    }

  def createUser(onboardingUserRequest: OnboardingUserRequest
                ): Future[Either[ErrorResponse, (Long, Long)]] = {
    val createBusinessUserInput = CreateBusinessUserInput(
      onboardingUserRequest.parentAccountId,
      onboardingUserRequest.email,
      onboardingUserRequest.firstName,
      onboardingUserRequest.lastName,
      onboardingUserRequest.contactNumber,
      accountsWithRoles = Seq(AccountWithRolesInput(onboardingUserRequest.parentAccountId, Seq(RolesInputDetails(SystemDefinedRoles.ADMINISTRATOR.roleType, None)))),
      creator = Creator(onboardingUserRequest.parentUserId, onboardingUserRequest.parentAccountId)
    )

    dashboardUserServiceV2
      .createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput)
      .flatMap {
        case (_, Right(activationDetails)) => getUserId(activationDetails.email)
        case _ =>
          logger.info(s"Failed to create business user for email: $onboardingUserRequest.email.")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotCreated)))
      }
  }

  def createSubAccount(parentUserId: Long,
                        parentAccountId: Long,
                        subAccountInfo: SubAccount,
                        primaryUserId: Long
                      ): Future[Either[ErrorResponse, Long]] =
    dashboardAccountServiceV2
      .createSubAcountWithMinDetails(parentAccountId, subAccountInfo, Some(parentUserId), Some(parentAccountId), Some(primaryUserId))
      .map {
        case Right(info) =>
          logger.info(s"Sub-account created with ID: ${info._2}.")
          Right(info._2)
        case Left(err) =>
          logger.info("Failed to create sub-account.")
          Left(err)
      }

  def updateProducts(accountId: Long,
                      addBusinessUserRoleIds: Set[Int],
                      removeBusinessUserRoleIds: Set[Int],
                      productConfiguration: ProductConfiguration
                    ): Future[Either[ErrorResponse, Boolean]] = {
    val productUpdates = for {
      productsToAdd <- getUpdateProduct(addBusinessUserRoleIds)
      productsToRemove <- getUpdateProduct(removeBusinessUserRoleIds, provision=false)
    } yield {
      val updateProducts = productsToAdd ++ productsToRemove
      UpdateAccountProvisioningDetails(
        bundleReference = "",
        products = updateProducts,
        productConfiguration = productConfiguration,
        initiatedBy = AccountManagementDefaults.defaultInitiatedBy
      )
    }

    productUpdates.flatMap { updateDetails =>
      accountAutomationService.saveAccountAutomation(accountId, updateDetails, ignoreDecision=true)
    }
  }

  def getUpdateProduct(businessUserRoleIds: Set[Int],
                       provision: Boolean = true,
                       accountType: ProductAccountType = ProductAccountTypes.DIRECT_CUSTOMER
                     ): Future[Seq[UpdateProduct]] =
    productService.getProductByBusinessUserRoleId(businessUserRoleIds, accountType).map {
      case products if products.nonEmpty =>
        products.map(p => UpdateProduct(p.id, provisioned = provision, enabled = provision))
      case _ =>
        logger.info(s"Failed to retrieve products for BusinessUserRoleIds: ${businessUserRoleIds.mkString(",")}.")
        Seq.empty[UpdateProduct]
    }

  def createSubAccount(onboardingSubAccountRequest: OnboardingSubAccountRequest): Future[Either[ErrorResponse, OnboardingResponse]] = {
    val parentAccountId = onboardingSubAccountRequest.parentAccountId
    val parentUserId = onboardingSubAccountRequest.parentUserId
    val primaryAdmin = onboardingSubAccountRequest.primaryAdmin
    val subAccountInfo = SubAccount(onboardingSubAccountRequest.companyName, onboardingSubAccountRequest.industry)

    createSubAccount(parentUserId, parentAccountId, subAccountInfo, primaryAdmin).flatMap {
      case Right(subAccountId) =>
        logger.info(s"Created a Sub-Account Id: $subAccountId, for $parentAccountId.")
        provision(ProvisionRequest(
          subAccountId,
          onboardingSubAccountRequest.productConfiguration,
          onboardingSubAccountRequest.modulesToAdd,
          onboardingSubAccountRequest.modulesToRemove
        )).flatMap {
          case Right(true) =>
            logger.info(s"Successfully Updated Product Configuration for Sub-Account $subAccountId.")
            partnerAndSubAccountInfoService.fetchAccountDetailsLite(subAccountId) flatMap {
              case Right(account) =>
                logger.info(s"Account information fetched for account ID $subAccountId.")
                Future.successful(Right(account))
              case Left(error) =>
                logger.info(s"Failed to fetch account information for account ID $subAccountId.")
                Future.successful(Left(error))
            }
          case _ =>
            logger.info(s"Failed to Update Product Configuration for Sub-Account $subAccountId.")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountProvisioningNotUpdated)))
        }
      case Left(error) =>
        logger.info(s"Failed to Create a Sub-Account, for $parentAccountId.")
        Future.successful(Left(error))
    }
  }

  def provision(provisionRequest: ProvisionRequest): Future[Either[ErrorResponse, Boolean]] = {
        updateProducts(
          provisionRequest.accountId,
          provisionRequest.modulesToAdd,
          provisionRequest.modulesToRemove,
          provisionRequest.productConfiguration
        ).map {
          case Right(true) =>
            logger.info(s"Successfully Updated Product Configuration for Account ${provisionRequest.accountId}.")
            Right(true)
          case _ =>
            logger.info(s"Failed to Update Product Configuration for Sub-Account ${provisionRequest.accountId}.")
            Left(ErrorResponseFactory.get(ExceptionCodes.AccountProvisioningNotUpdated))
        }
  }

  def setTimeout(accountId: Long, autoTimeout: Int, idleTimeout: Int): Future[Int] = {
    daoAccountUIConfiguration.updateUIAccountConfiguration(
      accountId,
      autoTimeoutInMinutes = autoTimeout.toShort,
      idleTimeoutInMinutes = idleTimeout.toShort
      )
  }

  def sendWelcomeEmail(welcomeEmailRequest: WelcomeEmailRequest): Future[Either[ErrorResponse, Boolean]] = {
    manageAccountsService.sendOnboardMail(welcomeEmailRequest.email)
  }
}
