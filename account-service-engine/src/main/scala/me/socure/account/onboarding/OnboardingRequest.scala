package me.socure.account.onboarding

import me.socure.model.account.automation.ProductConfiguration
import me.socure.model.dashboardv2.SubAccountV2
import me.socure.model.user.{BusinessUserWithUserAccountAssociation, UserFormV2, UserInfo}

case class OnboardingRequest(accountInfo: UserFormV2,
                             subAccountInfo: Option[SubAccountRequest] = None,
                             useCases: String,
                             bundleReference: Option[String] = None,
                             productConfiguration: ProductConfiguration = ProductConfiguration(None, None, None, None),
                             modulesToAdd: Set[Int] = Set.empty[Int],
                             modulesToRemove: Set[Int] = Set.empty[Int],
                             isActive: Option[Boolean] = None,
                             isInternal: Option[Boolean] = None)

case class SubAccountRequest(email: String,
                             firstName: String,
                             lastName: String,
                             contactNumber: String,
                             companyName: String,
                             industry: String,
                             productConfiguration: ProductConfiguration = ProductConfiguration(None, None, None, None),
                             modulesToAdd: Set[Int] = Set.empty[Int],
                             modulesToRemove: Set[Int] = Set.empty[Int]
                             )


case class OnboardingSubAccountRequest(parentAccountId: Long,
                             parentUserId: Long,
                             primaryAdmin: Long,
                             contactNumber: String,
                             companyName: String,
                             industry: String,
                             productConfiguration: ProductConfiguration = ProductConfiguration(None, None, None, None),
                             modulesToAdd: Set[Int] = Set.empty[Int],
                             modulesToRemove: Set[Int] = Set.empty[Int]
                            )

case class ProvisionRequest(accountId: Long,
                            productConfiguration: ProductConfiguration = ProductConfiguration(None, None, None, None),
                            modulesToAdd: Set[Int] = Set.empty[Int],
                            modulesToRemove: Set[Int] = Set.empty[Int])

case class WelcomeEmailRequest(email: String)

case class OnboardingUserRequest(email: String,
                                 firstName: String,
                                 lastName: String,
                                 contactNumber: String,
                                 parentAccountId: Long,
                                 parentUserId: Long)

case class OnboardingResponse(accountId: Long,
                              accountType: String,
                              name: String,
                              publicId: String,
                              users: Set[UserInfo],
                              message: Option[String] = None
                             )