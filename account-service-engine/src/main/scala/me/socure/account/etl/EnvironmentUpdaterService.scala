package me.socure.account.etl

import me.socure.convertors.AccountConvertors
import me.socure.model.etl.DtoEtlEnvironment
import me.socure.storage.slick.dao.DaoEnvironment
import me.socure.storage.slick.tables.account.DtoEnvironmentType
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON> on 11/3/16.
  */
class EnvironmentUpdaterService(daoEnvironment : DaoEnvironment)(implicit val ec : ExecutionContext) {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  def getEnvironmentTable : Future[Seq[DtoEtlEnvironment]] = {
    daoEnvironment.getEntireEnvironmentTable.map(list => list.map(AccountConvertors.getEtlEnvironment))
  }

  def getEnvironmentTypeTable : Future[Seq[DtoEnvironmentType]] = {
    daoEnvironment.getEntireEnvironmentTypeTable
  }

}
