package me.socure.account.platform

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.model.ErrorResponse
import me.socure.model.account.platform.resource.mapping.{AccountPlatformResourceMapping, AccountPlatformResourceMappingStatuses, DtoAccountPlatformResourceMapping, ResourceTypes}
import me.socure.storage.slick.dao.DaoAccountPlatformResourceMapping
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountPlatformResourceMappingService(daoAccountPlatformResourceMapping: DaoAccountPlatformResourceMapping,
                                            clock: Clock )(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getAccountPlatformResourceMapping(accountId: Long): Future[Either[ErrorResponse, Seq[DtoAccountPlatformResourceMapping]]] = {
    daoAccountPlatformResourceMapping.getAccountPlatformResourceMapping(accountId) map {
      case dtoAccountPlatformResourceMappings: Seq[DtoAccountPlatformResourceMapping] if dtoAccountPlatformResourceMappings.nonEmpty =>
        Right(dtoAccountPlatformResourceMappings)
      case _ =>
        logger.info(s"Account Platform Resource Mappings not found for Account $accountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountPlatformResourceMappingNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Platform Resource Mappings", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountPlatformResourceMappingNotFound))
    }
  }

  def saveAccountPlatformResourceMapping(accountId: Long, accountPlatformResourceMapping: AccountPlatformResourceMapping): Future[Either[ErrorResponse, Boolean]] = {
    daoAccountPlatformResourceMapping.saveAccountPlatformResourceMapping(
      DtoAccountPlatformResourceMapping(id = 0,
        accountId = accountId,
        envTypeId = accountPlatformResourceMapping.envType,
        resourceId = accountPlatformResourceMapping.resourceId,
        resourceType = ResourceTypes.byResourceTypeName(accountPlatformResourceMapping.resourceType).getOrElse(ResourceTypes.WorkFlow),
        status = AccountPlatformResourceMappingStatuses.byAccountPlatformResourceMappingStatusId(accountPlatformResourceMapping.status).getOrElse(AccountPlatformResourceMappingStatuses.InActive),
        createdBy = accountPlatformResourceMapping.createdBy,
        createdAt = clock.now,
        updatedBy = None,
        updatedAt = None)) map {
      case i: Int if i > 0 =>
        Right(true)
      case _ =>
        logger.info(s"Error occurred while saving Account Platform Resource Mapping")
        Right(false)
    }
  } recover {
    case e: Exception =>
      logger.info(s"Error occurred while saving Account Platform Resource Mapping", e)
      Left(ErrorResponseFactory.get(ExceptionCodes.AccountPlatformResourceMappingNotSaved))
  }
}

