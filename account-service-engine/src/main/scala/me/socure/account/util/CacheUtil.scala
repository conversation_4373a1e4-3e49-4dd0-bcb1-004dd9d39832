package me.socure.account.util

import me.socure.account.client.GenericClient.getBaseTags
import me.socure.common.data.core.provider._
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import org.slf4j.{Logger, LoggerFactory}
import scalacache.ScalaCache

import scala.concurrent.{ExecutionContext, Future}

object CacheUtil {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  private val cacheMetrics: Metrics = JavaMetricsFactory.get(MetricTags.memcachedPrefix)
  private val cacheMetricsErrorAspect = "error.count"

  private def removeCache(apiName: String, scalaCache: ScalaCache[_], cacheKeys: Set[String], additionalTags: MetricTags)(implicit ec: ExecutionContext): Future[Unit] = {

    val seqFuture = Future sequence(cacheKeys map scalaCache.cache.remove)

    val futureResponse = seqFuture.map(_ => ())
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = additionalTags
      )()

    futureResponse
  }

  def removeCacheKeysWrapped[T](apiName: String, scalaCache: ScalaCache[_], cacheKeys: Set[String], additionalTags: Set[String] = Set.empty[String])(action: => Future[T])(implicit ec: ExecutionContext): Future[T] = {
    action flatMap { res => {
      val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:reomve"))
      removeCache(apiName, scalaCache, cacheKeys, basetags) recover {
        case ex =>
          val errorTags : MetricTags = MetricTags(exceptionClass = Some(ex.getClass.getSimpleName))
          val tags = basetags.getTags ++ errorTags.getTags
          cacheMetrics.increment(cacheMetricsErrorAspect, tags.toSeq:_*)
          logger.error(s"Unable to remove cache for keys $cacheKeys", ex)
          ()
      } map (_ => res)
    }
    }
  }

}
