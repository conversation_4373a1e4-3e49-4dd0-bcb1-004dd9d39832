package me.socure.account.prospect

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.account.ProspectDomainCategoryTypes
import me.socure.model.prospect.{ProspectExclusionDetail, ProspectExclusionInput, ProspectInclusionDetail, ProspectInclusionInput}
import me.socure.storage.slick.dao.DaoProspect
import me.socure.storage.slick.tables.user.prospects.{DtoProspectBlackListedDomains, DtoProspectInclusionListDomains}
import org.joda.time.DateTime
import org.json4s.Formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class ProspectService(daoProspect: DaoProspect)(implicit ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  implicit def jsonFormats: Formats = JsonFormats.formats

  private val DEFAULT_LIMIT_SIZE = 10

  def getExclusionList(start: Option[Int], size: Option[Int], search: String): Future[Either[ErrorResponse, Seq[ProspectExclusionDetail]]] = {
    daoProspect.getExclusionList(start.getOrElse(0), size.getOrElse(DEFAULT_LIMIT_SIZE), search) map {
      case details => Right(details.map(dto => getExclusionDetail(dto)))
      case _ =>
        logger.info(s"No Exclusion list found")
        Left(ErrorResponseFactory.get(ExceptionCodes.NoDataFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Exclusion list", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getExclusionListTotalCount: Future[Either[ErrorResponse, Int]] = {
    daoProspect.getExclusionListTotalCount map {
      case count => Right(count)
      case _ =>
        logger.info(s"No Exclusion list found")
        Left(ErrorResponseFactory.get(ExceptionCodes.NoDataFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Exclusion list count", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def insertOrUpdateExclusionList(input: ProspectExclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    if (input.emailDomain.isEmpty) {
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.EmailDomainNotEmpty)))
    } else if (input.id.nonEmpty) {
      daoProspect.getExclusionDetail(input.id.get).flatMap {
        case Some(exclusionDto) =>
          updateExclusionList(exclusionDto, input)
        case _ =>
          insertExclusionList(input)
      } recover {
        case e: Exception =>
          logger.info(s"Error occurred while insertOrUpdateExclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
    } else {
      insertExclusionList(input)
    }
  }

  private def insertExclusionList(input: ProspectExclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect
      .insertExclusionDetail(toNewDtoExclusionDetail(input))
      .map { _ => Right(true) }
      .recover {
        case e: Exception =>
          logger.info(s"Error occurred while inserting ExclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
  }

  private def updateExclusionList(exclusionDto: DtoProspectBlackListedDomains, input: ProspectExclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect
      .updateExclusionDetail(exclusionDto.copy(emailDomain = input.emailDomain).copy(updatedBy = input.actionBy).copy(updatedAt = DateTime.now()))
      .map { numOfRows => if (numOfRows > 0) Right(true) else Right(false) }
      .recover {
        case e: Exception =>
          logger.info(s"Error occurred while updating ExclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
  }

  def deleteExclusionDetail(id: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect.deleteExclusionDetail(id).map(numDeletedRows => Right(numDeletedRows == 1))
  }

  def getInclusionList(start: Option[Int], size: Option[Int], search: String): Future[Either[ErrorResponse, Seq[ProspectInclusionDetail]]] = {
    daoProspect.getInclusionList(start.getOrElse(0), size.getOrElse(DEFAULT_LIMIT_SIZE), search) map {
      case details => Right(details.map(dto => getInclusionDetail(dto)))
      case _ =>
        logger.info(s"No Inclusion list found")
        Left(ErrorResponseFactory.get(ExceptionCodes.NoDataFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Inclusion list", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getInclusionListTotalCount: Future[Either[ErrorResponse, Int]] = {
    daoProspect.getInclusionListTotalCount map {
      case count => Right(count)
      case _ =>
        logger.info(s"No Inclusion list found")
        Left(ErrorResponseFactory.get(ExceptionCodes.NoDataFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Inclusion list count", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def insertOrUpdateInclusionList(input: ProspectInclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    if (input.emailDomain.isEmpty) {
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.EmailDomainNotEmpty)))
    } else if (input.id.nonEmpty) {
      daoProspect.getInclusionDetail(input.id.get).flatMap {
        case Some(inclusionDto) =>
          updateInclusionList(inclusionDto, input)
        case _ =>
          insertInclusionList(input)
      } recover {
        case e: Exception =>
          logger.info(s"Error occurred while insertOrUpdateInclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
    } else {
      insertInclusionList(input)
    }
  }

  private def insertInclusionList(input: ProspectInclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect
      .insertInclusionDetail(toNewDtoInclusionDetail(input))
      .map { _ => Right(true) }
      .recover {
        case e: Exception =>
          logger.info(s"Error occurred while inserting InclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
  }

  private def updateInclusionList(inclusionDto: DtoProspectInclusionListDomains, input: ProspectInclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect
      .updateInclusionDetail(inclusionDto.copy(emailDomain = input.emailDomain).copy(updatedBy = input.actionBy).copy(updatedAt = DateTime.now()))
      .map { numOfRows => if (numOfRows > 0) Right(true) else Right(false) }
      .recover {
        case e: Exception =>
          logger.info(s"Error occurred while updating InclusionList ", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
  }

  def deleteInclusionDetail(id: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoProspect.deleteInclusionDetail(id).map(numDeletedRows => Right(numDeletedRows == 1))
  }

  private def getExclusionDetail(dto: DtoProspectBlackListedDomains): ProspectExclusionDetail = {
    ProspectExclusionDetail(dto.id, dto.emailDomain,dto.createdBy,dto.createdAt,dto.updatedBy,dto.updatedAt)
  }

  private def toNewDtoExclusionDetail(input: ProspectExclusionInput): DtoProspectBlackListedDomains = {
    DtoProspectBlackListedDomains(
      id = 0,
      category = ProspectDomainCategoryTypes.KNOWN_BUSINESS_DOMAIN,
      emailDomain = input.emailDomain,
      createdBy = input.actionBy,
      createdAt = DateTime.now(),
      updatedBy = input.actionBy,
      updatedAt = DateTime.now(),
      isDeleted = false
    )
  }

  private def getInclusionDetail(dto: DtoProspectInclusionListDomains): ProspectInclusionDetail = {
    ProspectInclusionDetail(dto.id, dto.emailDomain,dto.createdBy,dto.createdAt,dto.updatedBy,dto.updatedAt)
  }

  private def toNewDtoInclusionDetail(input: ProspectInclusionInput): DtoProspectInclusionListDomains = {
    DtoProspectInclusionListDomains(
      id = 0,
      emailDomain = input.emailDomain,
      createdBy = input.actionBy,
      createdAt = DateTime.now(),
      updatedBy = input.actionBy,
      updatedAt = DateTime.now(),
      isDeleted = false
    )
  }

}
