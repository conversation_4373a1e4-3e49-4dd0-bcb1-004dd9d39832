package me.socure.account.data.retention

import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountDataRetentionScheduleNotSaved, AccountNotFound, InvalidAccountDataRetentionSchedule, UnknownError}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.constants.Routine
import me.socure.model.ErrorResponse
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, AccountDataRetentionScheduleWithHierarchy, DtoAccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.storage.slick.dao.{DaoAccountDataRetentionSchedule, DaoAccountV2}
import org.slf4j.{Logger, LoggerFactory}

import java.sql.SQLException
import scala.concurrent.{ExecutionContext, Future}

class AccountDataRetentionScheduleService(daoAccountDataRetentionSchedule: DaoAccountDataRetentionSchedule,
                                          daoAccountV2: DaoAccountV2,
                                          clock: Clock )(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def getAccountDataRetentionSchedule(): Future[Either[ErrorResponse, Seq[AccountDataRetentionSchedule]]] = {
    daoAccountDataRetentionSchedule.getAccountDataRetentionSchedule() map {
      case dtoAccountDataRetentionSchedule: Seq[DtoAccountDataRetentionSchedule] if dtoAccountDataRetentionSchedule.nonEmpty =>
        Right(dtoAccountDataRetentionSchedule.map{d => AccountDataRetentionSchedule(d.accountId, d.cadence, d.routine)})
      case _ =>
        logger.info(s"Account Data Retention Schedule not found")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Data Retention Schedule", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound))
    }
  }

  def getAccountDataRetentionSchedule(accountId: Long): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]] = {
    daoAccountDataRetentionSchedule.getAccountDataRetentionScheduleIfEnabled(accountId) map {
      case dtoAccountDataRetentionSchedule: Seq[DtoAccountDataRetentionSchedule] if dtoAccountDataRetentionSchedule.nonEmpty =>
        Right(dtoAccountDataRetentionSchedule.head)
      case _ =>
        logger.info(s"Account Data Retention Schedule not found for Account $accountId")
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Data Retention Schedule", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound))
    }
  }

  def upsertAccountDataRetentionSchedule(accountId: Long, accountDataRetentionSchedule: UpdateAccountDataRetentionSchedule): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]] = {
      if (validateAccountDataRetentionSchedule(accountDataRetentionSchedule.cadence, accountDataRetentionSchedule.routine)) {
        val dtoAccountDataRetentionSchedule = DtoAccountDataRetentionSchedule(id = 0,
          accountId = accountId,
          cadence = accountDataRetentionSchedule.cadence,
          routine = accountDataRetentionSchedule.routine,
          updatedBy = accountDataRetentionSchedule.initiatedBy,
          updatedAt = clock.now())
          daoAccountDataRetentionSchedule.getAccountDataRetentionSchedule(accountId) flatMap {
            case adrs0 if adrs0.isEmpty =>
              daoAccountDataRetentionSchedule.saveAccountDataRetentionSchedule(dtoAccountDataRetentionSchedule)
            case adrs1 if adrs1.length == 1 =>
              daoAccountDataRetentionSchedule.updateAccountDataRetentionSchedule(dtoAccountDataRetentionSchedule.copy(id=adrs1.head.id)) map {
                case i if i>=1 => dtoAccountDataRetentionSchedule
                case _ => Left(ErrorResponseFactory.get(AccountDataRetentionScheduleNotSaved))
              }
            case _ =>
              daoAccountDataRetentionSchedule.upsertAccountDataRetentionSchedule(dtoAccountDataRetentionSchedule)
          } flatMap {
          case dtoAdrs: DtoAccountDataRetentionSchedule  => Future.successful(Right(dtoAdrs))
          case _ =>
            logger.info(s"Unable to save Account Data Retention Schedule:${accountDataRetentionSchedule}, for account:${accountId}")
            Future.successful(Left(ErrorResponseFactory.get(AccountDataRetentionScheduleNotSaved)))
        } recover {
          case sqlExp: SQLException => logger.info(s"Unable to save Account Data Retention Schedule:${accountDataRetentionSchedule}, for account:${accountId}", sqlExp)
            Left(ErrorResponseFactory.get(AccountNotFound))
          case e: Throwable => logger.info(s"Unable to save Account Data Retention Schedule:${accountDataRetentionSchedule}, for account:${accountId}", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      } else {
        logger.info(s"Invalid Cadence/Routine Or Data Retention Policy is not Enabled, Unable to save Account Data Retention Schedule:${accountDataRetentionSchedule}, for account:${accountId}")
        Future.successful(Left(ErrorResponseFactory.get(InvalidAccountDataRetentionSchedule)))
      }
  }

  def getAccountDataRetentionScheduleWithHierarchy(accountId: Long): Future[Either[ErrorResponse, AccountDataRetentionScheduleWithHierarchy]] = {
    daoAccountDataRetentionSchedule.getAccountDataRetentionScheduleIfEnabled(accountId) flatMap {
      case dtoAccountDataRetentionSchedule: Seq[DtoAccountDataRetentionSchedule] if dtoAccountDataRetentionSchedule.nonEmpty =>
        val adrs = dtoAccountDataRetentionSchedule.head
        daoAccountV2.getSubAccounts(accountId) map {
          case l if l.nonEmpty =>
            Right(AccountDataRetentionScheduleWithHierarchy(accountId, adrs.cadence, adrs.routine, l.map(_.accountId).toSet))
          case _ =>
            logger.info(s"No Sub accounts found for $accountId")
            Right(AccountDataRetentionScheduleWithHierarchy(accountId, adrs.cadence, adrs.routine, Set.empty[Long]))
        }
      case _ =>
        logger.info(s"Account Data Retention Schedule not found for Account $accountId")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Account Data Retention Schedule", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound))
    }
  }

  def validateAccountDataRetentionSchedule(cadence: Int, routine: String): Boolean = {
    val r0 = Routine.byRoutineName(routine)
    r0.isDefined && cadence <= r0.get.max
  }

}
