package me.socure.account.sponsorbank

import me.socure.account.audit.AccountAuditService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.constants.{JsonFormats, SystemDefinedRoles}
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}
import me.socure.storage.slick.dao.DaoSponsorBankProgram
import me.socure.storage.slick.tables.account.DtoSponsorBankProgram
import me.socure.storage.slick.tables.account.audit.DtoAccountAudit
import org.joda.time.DateTime
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success}

class SponsorBankService(daoSponsorBankProgram: DaoSponsorBankProgram,
                         accountAuditService: AccountAuditService,
                         clock: Clock )(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  implicit def jsonFormats: Formats = JsonFormats.formats

  def getNonSponsorBankPrograms(): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    daoSponsorBankProgram.getNonSponsorBankPrograms map {
      case accounts =>
        Right(accounts.map(a => AccountIdName(a._1, a._2)))
      case _ =>
        logger.info(s"No Non Sponsor Bank Programs found")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))

    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Non Sponsor Bank", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getSponsorBank(programId: Long): Future[Either[ErrorResponse, AccountIdName]] = {
    daoSponsorBankProgram.getSponsorBankForProgram(programId) map {
      case sponsorBankProgramDtls if sponsorBankProgramDtls.nonEmpty =>
        Right(AccountIdName(sponsorBankProgramDtls.head._1, sponsorBankProgramDtls.head._2))
      case _ =>
        logger.info(s"No Sponsor Bank found for Program: $programId")
        Left(ErrorResponseFactory.get(ExceptionCodes.NoSponsorBankLinked))

    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Sponsor Bank for Program: $programId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.NoSponsorBankLinked))
    }
  }

  def getLinkedPrograms(sponsorBankId: Long): Future[Either[ErrorResponse, Seq[SponsorBankProgram]]] = {
    daoSponsorBankProgram.getPrograms(sponsorBankId) map {
      case sponsorBankProgramDtls =>
        Right(sponsorBankProgramDtls.map(dtls => SponsorBankProgram(dtls._1.programId, dtls._2, dtls._1.createdBy, dtls._1.createdAt)))
      case _ =>
        logger.info(s"No Sponsor Bank Programs found for Account $sponsorBankId")
        Left(ErrorResponseFactory.get(ExceptionCodes.SponsorBankProgramNotFound))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching Sponsor Bank Programs for Account: $sponsorBankId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.SponsorBankProgramNotFound))
    }
  }

  def linkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Future[Either[ErrorResponse, Boolean]] = {
    val startTime = clock.now
    val sponsorBankId =  sponsorBankProgramLinkRequest.sponsorBankId
    val programId = sponsorBankProgramLinkRequest.programId
    val initiatedBy = sponsorBankProgramLinkRequest.initiatedBy
    val result = daoSponsorBankProgram.getSponsorBankProgram(sponsorBankId, programId) flatMap {
      case sponsorBankPrograms: Seq[DtoSponsorBankProgram] if sponsorBankPrograms.nonEmpty =>
        logger.info(s"Program $programId is already linked with the SponsorBank $sponsorBankId")
        Future.successful(Right(false))
      case _ =>
        daoSponsorBankProgram.isValidSponsorBankProgram(sponsorBankId, programId) flatMap {
          case true => daoSponsorBankProgram.getUserIdRoleTypeMaps(sponsorBankId, Set(SystemDefinedRoles.BSA_OFFICER.roleType, SystemDefinedRoles.COMPLIANCE_ANALYST_ROLE.roleType)) flatMap {
            case userIdRoleMap=>
              val dtoSponsorBankProgram = DtoSponsorBankProgram(0,
                accountId = sponsorBankId,
                programId = programId,
                createdBy = initiatedBy,
                createdAt = clock.now())
              daoSponsorBankProgram.saveSponsorBankProgram(dtoSponsorBankProgram, userIdRoleMap) map {
                case i if i > 0 => Right(true)
                case _ =>
                  logger.info(s"Failed to link Program: $programId with Sponsor Bank: $sponsorBankId")
                  Left(ErrorResponseFactory.get(ExceptionCodes.UnableToLinkSponsorBankProgram))
              }
            case _=> logger.info(s"Unable to fetch the userID RoleType Map for $programId with Sponsor Bank: $sponsorBankId")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToLinkSponsorBankProgram)))
          }
          case _ =>
            logger.info(s"Failed to link  Program: $programId with Sponsor Bank: $sponsorBankId")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnableToLinkSponsorBankProgram)))
        }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while linking  Program: $programId with Sponsor Bank: $sponsorBankId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToLinkSponsorBankProgram))
    }
    audit(result, sponsorBankProgramLinkRequest, startTime, Some("Link Program"))
    result
  }

  def unlinkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Future[Either[ErrorResponse, Boolean]] = {
    val startTime = clock.now
    val sponsorBankId =  sponsorBankProgramLinkRequest.sponsorBankId
    val programId = sponsorBankProgramLinkRequest.programId
      val result = daoSponsorBankProgram.deleteSponsorBankProgramLink(sponsorBankId, programId)  map {
        case i if i > 0 => Right(true)
        case _ =>
          logger.info(s"Failed to unlink Program: $programId from Sponsor Bank: $sponsorBankId")
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUnLinkSponsorBankProgram))
      } recover {
        case e: Exception =>
          logger.info(s"Error occurred while removing the link Program: $programId from  Sponsor Bank : $sponsorBankId", e)
          Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUnLinkSponsorBankProgram))
      }
    audit(result, sponsorBankProgramLinkRequest, startTime, Some("UnLink Program"))
    result
  }

  def unlinkSponsorBankPrograms(sponsorBankId: Long, initiatedBy: String): Future[Either[ErrorResponse, Boolean]] = {
    val startTime = clock.now
    val result = daoSponsorBankProgram.deleteSponsorBankProgramLinks(sponsorBankId)  map {
      case i if i >= 0 => Right(true)
      case _ =>
        logger.info(s"Failed to unlink Programs from Sponsor Bank: $sponsorBankId")
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUnLinkSponsorBankProgram))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while removing the link Programs from  Sponsor Bank : $sponsorBankId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToUnLinkSponsorBankProgram))
    }
    audit(result, SponsorBankProgramLinkRequest(sponsorBankId, 0, initiatedBy), startTime, Some("UnLink Programs"))
    result
  }

  def audit(result: Future[Either[ErrorResponse, Boolean]], sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest, startTime: DateTime, action: Option[String] = None) = {
    result onComplete {
      case Success(r) if r.isRight =>
        saveAudit(sponsorBankProgramLinkRequest,
          "Sponsor Bank Program Updated",
          "200",
          startTime,
          action
        )
      case Success(l)  =>
        saveAudit(sponsorBankProgramLinkRequest,
          l.left.get.message,
          "400",
          startTime,
          action
        )
      case Failure(t) =>
        saveAudit(sponsorBankProgramLinkRequest,
          t.toString,
          "400",
          startTime,
          action
        )
    }
  }

  def saveAudit(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest, response: String, status: String, startTime: DateTime, action: Option[String], client: String = "SuperAdmin"): Future[Either[ErrorResponse, DtoAccountAudit]] = {
    accountAuditService.saveAccountAudit(accountId = sponsorBankProgramLinkRequest.sponsorBankId,
      client = client,
      component = "Sponsor Bank Program",
      action = action,
      componentReference = "0",
      payload = Serialization.write(sponsorBankProgramLinkRequest),
      response = response,
      status = status,
      processingTime = clock.now.getMillis - startTime.getMillis,
      createdBy = sponsorBankProgramLinkRequest.initiatedBy)
  }

}

