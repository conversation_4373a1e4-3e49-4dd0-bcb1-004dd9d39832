package me.socure.account.saml

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.superadmin.AccountInfoService
import me.socure.account.validator.V2Validator
import me.socure.constants.{AccountManagementDefaults, AccountTypes}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class SamlValidator(accountInfoService: AccountInfoService, daoBusinessUser: DaoBusinessUser, daoAccountV2: DaoAccountV2, v2Validator: V2Validator)(implicit exe: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  def accountIdsHaveNoSaml(accountIds: Set[Long]): Future[Either[ErrorResponse, Unit]] = {
    accountInfoService.anyHasRole(accountIds = accountIds, role = BusinessUserRoles.SAML_2_0.id).flatMap {
      case true => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts)))
      case false => Future.successful(Right(()))
    }
  }

  def accountIdsHaveNoSamlBoolean(accountIds: Set[Long]): Future[Boolean] = {
    accountInfoService.anyHasRole(accountIds = accountIds, role = BusinessUserRoles.SAML_2_0.id)
  }

  private def isAllSAMLAccountPresent(accountIds: Seq[Long]) : Future[Boolean] = {
    if(accountIds.isEmpty)
      Future.successful(true)
    else {
      accountInfoService.anyHasRole(accountIds = Set(accountIds.head),BusinessUserRoles.SAML_2_0.id) flatMap { present => {
        if(!present) {
          Future.successful(false)
        } else {
          isAllSAMLAccountPresent(accountIds.tail)
        }
      }
      }
    }
  }

  def usersHaveNoSaml(emails: Set[String]): Future[Either[ErrorResponse, Unit]] = {
    daoBusinessUser.getUser(emails.toList).flatMap {
      case businessUsers if businessUsers.nonEmpty && businessUsers.size == emails.size =>
        val allUsersSamlFuture = businessUsers map (businessUser => daoAccountV2.isAccountV2Provisioned(Set(businessUser.accountId)) flatMap { v2 =>
          if (!v2) {
            accountIdsHaveNoSamlBoolean(Set(businessUser.accountId)) map (saml => (false, saml))// if SAML enabled, return true
          } else {
            daoAccountV2.getAllAssociatedAccounts(businessUser.id) flatMap { associatedAccounts =>
              if(associatedAccounts.isEmpty) {
                Future.successful((true, false))
              } else {
                v2Validator.getRootParentAccountTypes(associatedAccounts.map(_.id)).flatMap {
                  case rootAccountTypes if (businessUser.email.matches(AccountManagementDefaults.internalEmailDomains) && rootAccountTypes.exists(_._2 == AccountTypes.DIRECT_EFFECTIV.id)) =>
                    Future.successful((false, false))
                  case _ =>
                    isAllSAMLAccountPresent(associatedAccounts map (_.id)) map (saml => (false, saml)) // if SAML enabled for all account, return true
                }
              }
            }
          }
        })
        Future.sequence(allUsersSamlFuture) map { results =>
          if(results.exists(_._1)){ // no associations exists in V2 account for atleast 1 email address, we should consider it as user not exist/deleted
            Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound))
          } else if (results.exists(_._2)) { // if atleast one e-mail address has only SAML account, return error
            Left(ErrorResponseFactory.get(ExceptionCodes.NotSupportedForSAMLEnabledAccounts))
          } else {
            Right(())
          }
        }
      case _ =>
        logger.info("UAT: User Not Found")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.BusinessUserNotFound)))
    }
  }

  def userHaveAllSaml(businessUser: DtoBusinessUser): Future[Boolean] = {
    daoAccountV2.isAccountV2Provisioned(Set(businessUser.accountId)) flatMap { v2 =>
      if (!v2) {
        accountIdsHaveNoSamlBoolean(Set(businessUser.accountId)) // if SAML enabled, return true
      } else {
        daoAccountV2.getAllAssociatedAccounts(businessUser.id) flatMap { associatedAccounts =>
          if(associatedAccounts.isEmpty){
            Future.successful(false) // if there is no associated account, return false
          } else {
            isAllSAMLAccountPresent(associatedAccounts map (_.id)) // if SAML enabled for all account, return true
          }
        }
      }
    }
  }

  def whenAccountIdsHaveNoSaml[T](accountIds: Set[Long])(action: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]] = {
    accountIdsHaveNoSaml(accountIds = accountIds).flatMap {
      case Right(_) => action
      case Left(error) => Future.successful(Left(error))
    }
  }

  def whenUsersHaveNoSaml[T](emails: Set[String])(action: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]] = {
    usersHaveNoSaml(emails = emails).flatMap {
      case Right(_) => action
      case Left(error) => Future.successful(Left(error))
    }
  }

  def accountIdHasNoSaml(accountId: Long): Future[Either[ErrorResponse, Unit]] = accountIdsHaveNoSaml(accountIds = Set(accountId))
  def emailHasNoSaml(email: String): Future[Either[ErrorResponse, Unit]] = usersHaveNoSaml(emails = Set(email))
  def whenAccountIdHasNoSaml[T](accountId: Long)(action: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]] = whenAccountIdsHaveNoSaml(accountIds = Set(accountId))(action)
  def whenUserHasNoSaml[T](email: String)(action: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]] = whenUsersHaveNoSaml(emails = Set(email))(action)
}
