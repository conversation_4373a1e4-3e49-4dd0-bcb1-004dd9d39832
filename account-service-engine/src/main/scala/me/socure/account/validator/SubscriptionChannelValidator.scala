package me.socure.account.validator

import me.socure.DaoAccount
import me.socure.common.exception.ErrorResponseException
import me.socure.model.ErrorResponse
import me.socure.storage.slick.dao.{DaoEnvironment, DaoSubscriptionChannelRegistry, DaoSubscriptions}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class SubscriptionChannelValidator(daoEnvironment: DaoEnvironment ,
                                   daoAccount : DaoAccount,
                                   daoSubscriptions: DaoSubscriptions,
                                   daoSubscriptionChannelRegistry : DaoSubscriptionChannelRegistry)(implicit val ec : ExecutionContext) {

   val logger: Logger = LoggerFactory.getLogger(this.getClass)

   private val SecretKeyMaxSize = 4096


   def doesAccountSubscriptionTypeProvisioned(accountId: Long , subscriptionTypeId : Long) : Future[Boolean] = {
      daoAccount.getAccount(accountId) flatMap  {
         case Some(acc) if  acc.parentId.isDefined =>  isSubscriptionEnabled(acc.parentId.get , subscriptionTypeId)
         case _ => isSubscriptionEnabled(accountId, subscriptionTypeId)
      }
   }

   def isSubscriptionEnabled(accId:Long , subscriptionTypeId:Long) : Future[Boolean]={
      daoSubscriptions.isSubscriptionTypeEnabled(accId, subscriptionTypeId) map {
         case true => true
         case _ => logger.error(s"Subscription TYpe $subscriptionTypeId is not provisioned to accountId $accId");
            throw ErrorResponseException(ErrorResponse(121, s"subscription channel type  ${subscriptionTypeId} is not provisioned "))
      }
   }

   def doesEnvBelongToAccount(accountId: Long, envId: Long): Future[Boolean] = {
      daoEnvironment.getEnvironment(envId) map {
         case env if !env.filter(e => e.accountId == accountId).isEmpty => true
         case _ => throw ErrorResponseException(ErrorResponse(121,s"env $envId does not belong to account $accountId"))
      }
   }

   def doesChannelBelongsToAccount(channelId : Long, accountId : Long) : Future[Boolean] = {
      daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(channelId) flatMap {
         case Some(channel) => doesEnvBelongToAccount(accountId,channel.environmentId)
         case _ => throw ErrorResponseException(ErrorResponse(121,s"channel  $channelId does not exists"))
      }
   }

   def doesChannelBelongsToEnv(channelId :Long , envId :Long) : Future[Boolean] = {
      daoSubscriptionChannelRegistry.getSubscriptionChannelRegistryById(channelId) map {
         case Some(channel) if channel.environmentId == envId => true
         case _ => throw ErrorResponseException(ErrorResponse(121,s"channel  $channelId does belong to env $envId"))
      }
   }

   def createSubscriptioChannelValidations(accountId: Long , envId: Long , subscriptionTypeId: Long) : Future[Boolean] ={
      doesEnvBelongToAccount(accountId, envId) flatMap  {
         case true => doesAccountSubscriptionTypeProvisioned(accountId,subscriptionTypeId)
      }
   }

   def updateSubscriptionChannelValidations(accountId: Long , envId: Long , subscriptionTypeId: Long , channelId : Long) : Future[Boolean] = {
      doesEnvBelongToAccount(accountId, envId) flatMap {
         case true => doesAccountSubscriptionTypeProvisioned(accountId , subscriptionTypeId ) flatMap {
            case true => doesChannelBelongsToEnv(channelId,envId)
         }
      }
   }

   def isSecretKeyValid(secretKey: String): Boolean = {
      secretKey.length <= SecretKeyMaxSize
   }

}
