package me.socure.account.validator

import me.socure.model.account.RateLimiterPublicAPI
import me.socure.storage.slick.tables.account.DtoRateLimit

object RateLimitingValidator {
  def validateRateLimit(dtoRateLimit: DtoRateLimit): Boolean = {
    RateLimiterPublicAPI.isValid(dtoRateLimit.api) &&
    dtoRateLimit.windowInMillis >= 1000 &&
    dtoRateLimit.windowInMillis <= (1000 * 60 * 60 * 24) &&
    dtoRateLimit.limit >= 0 &&
    dtoRateLimit.limit <= (10 * 60 * 60 * 24 * 2)
  }

  def isAllDigits(x: String) = x forall Character.isDigit

  def validateRateLimits(dtoRateLimits: Seq[DtoRateLimit]) : Boolean = {
    dtoRateLimits.filter(rl => validateRateLimit(rl)== false).isEmpty
  }
}