package me.socure.account.validator

import me.socure.constants.AccountManagementDefaults
import me.socure.model.BusinessUserRoles
import org.apache.commons.lang3.math.NumberUtils
import org.slf4j.{Logger, LoggerFactory}

object V2Helper {
  private val logger : Logger = LoggerFactory.getLogger(getClass)

  def getFeatureFlags(modules: Option[Set[Int]], isAnalyticsFlagNeeded: Boolean): Set[Int] = {
    var generalFeatureFlags = AccountManagementDefaults.moduleFeatureFlagsMapping.getOrElse(AccountManagementDefaults.GeneralFlags, Set.empty[Int])
    if(isAnalyticsFlagNeeded){
      generalFeatureFlags += BusinessUserRoles.CustomerFacingAnalytics.id
      logger.info(s"Adding CustomerFacingAnalytics feature flag to GeneralFlags: ${generalFeatureFlags}")
    }
    modules match {
      case Some(ap) => ap.flatMap(AccountManagementDefaults.moduleFeatureFlagsMapping.get).foldLeft(Set.empty[Int])(_ ++ _) ++ generalFeatureFlags
      case _ => generalFeatureFlags
    }
  }

  def getEcbsvAndMlaFlags = {
    AccountManagementDefaults.moduleFeatureFlagsMapping.getOrElse(AccountManagementDefaults.MlaFlags, Set.empty[Int]) ++ AccountManagementDefaults.moduleFeatureFlagsMapping.getOrElse(AccountManagementDefaults.EcbsvFlags, Set.empty[Int])
  }

  def getRootParent(hierarchyPath: String) = {
    hierarchyPath.split("/") match {
        case l0 if l0.nonEmpty && NumberUtils.isNumber(l0.head) => Some(l0.head.toLong)
        case _ =>
          logger.info(s"Unable to get Parent, Invalid hierarchyPath, $hierarchyPath")
          None
    }
  }
}
