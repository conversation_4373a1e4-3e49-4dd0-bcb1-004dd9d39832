package me.socure.account.validator

import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.common.exception.ErrorResponseException
import me.socure.constants.{AccountManagementDefaults, AccountTypes, DashboardUserPermissions, Resources, SystemDefinedRoles}
import me.socure.convertors.AccountConvertors
import me.socure.model.BusinessUserRoles
import me.socure.model.account.ApiKeyStatus.ApiKeyStatus
import me.socure.model.account.{AccountIdName, SubAccountCreationRequest, SubAccountMigrationDetails}
import me.socure.model.dashboardv2.{AccountWithRolesInput, Creator, RolesInputDetails}
import me.socure.storage.slick.dao.DaoAccountV2
import me.socure.storage.slick.tables.account.{DtoAccount, DtoAccountHierarchy, DtoEnvironment, DtoUserAccountAssociation}
import org.apache.commons.validator.routines.EmailValidator
import org.slf4j.{Logger, LoggerFactory}

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class V2Validator(daoAccountV2: DaoAccountV2)(implicit val ec : ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(this.getClass)

  def userExists(email: String): Future[Unit] = {
    daoAccountV2.userExists(email) map {
      case true =>
        logger.info(s"Duplicate email id")
        throw ErrorResponseException(ErrorResponseFactory.get(UnknownError))
      case _ => ()
    }
  }

  def isPrimaryAccount(accountId: Long): Future[Unit] = {
    daoAccountV2.isParentAccount(accountId) map {
      case false =>
        logger.info(s"Given account is not primary")
        throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
      case _ => ()
    }
  }

 def isValidV2AccountRequest(accountId: Long, creator: Option[Creator]): Future[Boolean] = {

   daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.SAML_2_0.id) flatMap {
     case true => daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.NEW_SAML.id) flatMap {
       case false => Future.successful(false)
       case true => isAccountV2Provisioned(accountId, creator) // SAML 2.0 plus NEW SAML is essentially a V2 account management with authorisations on our end
     }
     case false => isAccountV2Provisioned(accountId, creator)
  }
 }

  def isAccountV2Provisioned(accountId: Long, creator: Option[Creator]): Future[Boolean] = {
    daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap { v2Provisioned =>
      (creator, v2Provisioned) match {
        case (_, false) => Future.successful(false)
        case (Some(c), true) =>
          for {
            _ <- isAccountV2Provisioned(Set(c.accountId))
          } yield true
        case (None, true) =>
          logger.info(s"Creator details are missing")
          throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))
      }
    }
  }

  //TODO redefine this method like rename and avoid unwanted checks
  def isValidV2AccountRequestExtd(accountId: Long, creator: Option[Creator]): Future[Boolean] = {
    //TODO if we are fixing SAML for V3 dashboard, we need to reconsider this SAML flag check
/*    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.SAML_2_0.id) flatMap {
      case true =>
        Future.successful(false)
      case false =>*/
    daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap { v2Provisioned =>
      (creator, v2Provisioned) match {
        case (Some(c), false) =>
          daoAccountV2.isAccountV2Provisioned(Set(c.accountId)) flatMap  {
            case false =>
              if(accountId == c.accountId) {
                Future.successful(false)
              } else {
                for{
                  _ <- isValidSubAccount(c.accountId, Set(accountId))
                } yield false
              }
            case true =>
              logger.info(s"Invalid account access")
              throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
          }
        case (None, false) =>
          Future.successful(false)
        case (Some(c), true) =>
          for {
            _ <- isAccountV2Provisioned(Set(c.accountId))
          } yield true
        case (None, true) =>
          logger.info(s"Creator details are missing")
          throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))
      }
    }
 // }
  }

  def isValidV2EnvironmentRequest(environmentId: Long, creatorOpt: Option[Creator], permissions: Set[Int], orPermissions: Set[Int] = Set.empty): Future[Boolean] = {
    creatorOpt match {
      case Some(creator) =>
        daoAccountV2.getEnvironmentByEnvironmentId(environmentId) flatMap {
          case Some(env) =>
            isValidV2AccountRequest(env.accountId, creatorOpt) flatMap { isV2Provisioned =>
              if (isV2Provisioned) {
                for {
                  _ <- validateUserAccountAssociation(creator.userId, creator.accountId)
                  _ <- validateAccountAccess(env.accountId, creator.accountId)
                  _ <- validatePermissions(creator.accountId, creator.userId, permissions, envType = env.environmentType.toInt, orPermissions)
                } yield true
              } else {
                val requiredV1Permissions = AccountConvertors.toDashboardUserRoles(permissions)
                daoAccountV2.isV1PrimaryUser(creator.userId) flatMap {
                  case Some(isV1PrimaryUser) =>
                    daoAccountV2.isParentAccountEnvironment(creator.userId, environmentId) flatMap {
                      case true =>
                        isPermissionAvailable(creator, environmentId, isV1PrimaryUser, requiredV1Permissions) map {
                          case true => true
                          case _ => throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
                        }
                      case false =>
                        isPermissionAvailableForSubAccountEnvironment(creator, environmentId, isV1PrimaryUser, requiredV1Permissions) map {
                          case true => true
                          case _ => throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
                        }
                    }
                  case _ =>
                    throw new Exception(s"Invalid user id - ${creator.userId}")
                }
              }
            }
          case None =>
            logger.info(s"Environment is not right")
            throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
        }
      case None =>
        daoAccountV2.getEnvironmentByEnvironmentId(environmentId) flatMap {
          case Some(env) =>
            isValidV2AccountRequest(env.accountId, None) flatMap {
              case false => Future.successful(true)
              case true =>
                logger.info(s"Creator details are missing")
                throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))
            }
          case None =>
            logger.info(s"Environment is not right")
            throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
        }
    }
  }

  private def isPermissionAvailable(creator: Creator, environmentId: Long, isV1PrimaryUser: Boolean, requiredV1Permissions: Set[Int]) = {
    daoAccountV2.getEnvironmentRolesV1(creator.userId, environmentId) map { assignedRoles =>
      isV1PrimaryUser || requiredV1Permissions.forall(assignedRoles.contains)
    }
  }

  private def isPermissionAvailableForSubAccountEnvironment(creator: Creator, environmentId: Long, isV1PrimaryUser: Boolean, requiredV1Permissions: Set[Int]) = {
    daoAccountV2.getSubAccountEnvironments(creator.userId, environmentId) flatMap { dtoEnvironments =>
      if(dtoEnvironments.map(_.id).contains(environmentId)) {
        daoAccountV2.getEnvironmentType(environmentId) flatMap  {
          case Some(environmentType) =>
            daoAccountV2.getEnvironmentByUserId(creator.userId, environmentType) flatMap {
              case Some(dtoEnvironment) =>
                isPermissionAvailable(creator, dtoEnvironment.id, isV1PrimaryUser, requiredV1Permissions)
              case _ =>
                throw new Exception(s"Environment type doesn't exist in parent account")
            }
          case _ =>
            throw new Exception(s"Invalid environment id - $environmentId")
        }
      } else {
        Future.successful(false)
      }
    }
  }


  def isValidV2EnvironmentRequest(environmentType: Int, accountId: Long, creator: Option[Creator], permissions: Set[Int]): Future[Boolean] = {
    daoAccountV2.getEnvironment(environmentType, accountId) flatMap  {
      case Some(env) =>
        for {
          _ <- isValidV2AccountRequest(env.accountId, creator)
          res <-
            creator match {
              case Some(c) =>
                for {
                  _ <- validateAccountAccess(env.accountId, c.accountId)
                  _ <- validatePermissions(c.accountId, c.userId, permissions, envType = env.environmentType.toInt)
                } yield true
              case None => Future.successful(true)
            }
        } yield res
      case None =>
        logger.info(s"Environment is not right")
        throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def isValidV2EnvironmentRequest(environmentType: Int, creator: Creator, permissions: Set[Int]): Future[Boolean] = {
    daoAccountV2.getEnvironment(environmentType, creator.accountId) flatMap {
        case Some(e:DtoEnvironment) =>
          for {
            _ <- isValidUser(creator.accountId, Some(creator.userId))
            _ <- validatePermissions(creator.accountId, creator.userId, permissions, envType = e.environmentType.toInt)
          } yield true
        case _ =>
          logger.info(s"Environment is not right")
          throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
      }
  }

  def isValidV2EnvironmentRequestOrPermissions(environmentType: Int, creator: Creator, permissions: Set[Int], orPermissions: Set[Int]): Future[Boolean] = {
    daoAccountV2.getEnvironment(environmentType, creator.accountId) flatMap {
      case Some(e:DtoEnvironment) =>
        for {
          _ <- isValidUser(creator.accountId, Some(creator.userId))
          _ <- validatePermissions(creator.accountId, creator.userId, permissions, envType = e.environmentType.toInt, orPermissions)
        } yield true
      case _ =>
        logger.info(s"Environment is not right")
        throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def isValidV2SocialIdRequest(socialId: Long, creator: Option[Creator], permissions: Set[Int]): Future[Boolean] = {
    daoAccountV2.getEnvironmentBySocialId(socialId) flatMap  {
      case Some(env) =>
        for {
          isValidV2Account <- isValidV2AccountRequestExtd(env.accountId, creator)
          res <-
            (creator, isValidV2Account) match {
              case (Some(c), true) =>
                for {
                  _ <- validateAccountAccess(env.accountId, c.accountId)
                  _ <- validatePermissions(c.accountId, c.userId, permissions, envType = env.environmentType.toInt)
                } yield true
              case (Some(_), false) => Future.successful(true)
              case (None, false) => Future.successful(true)
              case (_, _) => Future.successful(false)
            }
        } yield res
      case None =>
        logger.info(s"Social Id is not right")
        throw ErrorResponseException(ErrorResponseFactory.get(SocialKeyIdNotFound))
    }
  }

  def isValidV2ApiKeyRequest(apiKey: String, creator: Option[Creator], permissions: Set[Int], statusSet: Set[ApiKeyStatus]): Future[Boolean] = {
    if(permissions.nonEmpty) {
      daoAccountV2.getAccountHierarchyWithEnvironment(apiKey, statusSet) flatMap {
        case Some(info) =>
          for {
            _ <- isValidV2AccountRequest(info._1.accountId, creator)
            res <-
              creator match {
                case Some(c) =>
                  for {
                    root <- validateAccountAccess(info._1.accountId, c.accountId)
                    _ <- validatePermissions(c.accountId, c.userId, permissions, envType = info._2.environmentType.toInt, accountId = info._1.accountId, rootAccount = root)
                  } yield true
                case None => Future.successful(true)
              }
          } yield res
        case None =>
          logger.info(s"Environment is not right")
          throw ErrorResponseException(ErrorResponseFactory.get(EnvironmentNotFound))
      }
    }else{
      logger.info(s"Invalid permissions")
      throw ErrorResponseException(ErrorResponseFactory.get(InvalidPermissions))
    }
  }

  def getEnvironments( creator: Option[Creator], permissionChkForTransaction: Boolean):Future[Seq[Int]] = {
    if(permissionChkForTransaction){
      getEnvironments(Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id), creator)
    } else {
      getEnvironments(Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id), creator)
    }
  }

  def getEnvironments(permissions: Set[Int], creator: Option[Creator]): Future[Seq[Int]] = {
    creator match {
      case Some(c) =>
        (for {
          customRolePermissions <- daoAccountV2.fetchPermissions(c.accountId, c.userId)
          rootAccountType <- getRootParentAccountType(c.accountId)
          systemRolePermissions <- daoAccountV2.fetchSystemRolesPermissions(c.accountId, c.userId, rootAccountType)
        } yield customRolePermissions ++ systemRolePermissions) map { environmentPermissions =>
            environmentPermissions.flatMap { ep =>
              val permissions0 = ep._2.split(",").toSet.filter(_.nonEmpty)
              if (permissions0.map { a: String => Try{a.trim.toInt} match {
                case Failure(ex) =>
                  logger.info(s"Error while parsing the permissions, Permissions: $ep", ex)
                  throw ErrorResponseException(ErrorResponseFactory.get(InvalidPermissions))
                case Success(n) => n
              }}.exists(permissions.contains)) Some(ep._1) else None
            }
        }
      case _ =>
        logger.info(s"Creator details are missing")
        throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))
    }
  }

  def isAccountV2Provisioned(accountIds: Set[Long]): Future[Unit] = {
    daoAccountV2.isAccountV2Provisioned(accountIds) map {
      case false =>
        logger.info(s"Account V2 not provisioned for any/all of these ${accountIds.mkString(",")} ")
        throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
      case _ =>
        ()
    }
  }

  def isAccountNotV2Provisioned(accountIds: Set[Long]): Future[Unit] = {
    daoAccountV2.isAccountV2Provisioned(accountIds) map {
      case true =>
        logger.info(s"Account V2 is already provisioned for any/all of these ${accountIds.mkString(",")} ")
        throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
      case false =>
        ()
    }
  }

  def fetchParent(ah: DtoAccountHierarchy): Future[Option[DtoAccountHierarchy]] = {
    val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
    if (accountIds.nonEmpty) {
      val accountId = accountIds.head
      if (accountId == ah.accountId) {
        Future.successful(Some(ah))
      } else {
        daoAccountV2.getAccountHierarchyByAccountId(accountId)
      }
    }else Future.successful(None)
  }

  def fetchParent(ahs: Seq[DtoAccountHierarchy]): Future[Map[Long, Int]] = {
    val accountHierarchyIds = ahs.map(ah => (ah, ah.hierarchyPath.split("/").map(_.toLong)))
    val parentAccountTypeMap = scala.collection.mutable.Map[Long, Int]()
    val accountParentAccountMap = scala.collection.mutable.Map[Long, Long]()
    if (accountHierarchyIds.nonEmpty) {
      accountHierarchyIds.foreach { ahAccountIds =>
        val parentAccountId = ahAccountIds._2.head
        if (parentAccountId == ahAccountIds._1.accountId) {
          parentAccountTypeMap.put(ahAccountIds._1.accountId, ahAccountIds._1.accountType)
        } else {
          accountParentAccountMap.put(ahAccountIds._1.accountId, parentAccountId)
        }
      }
      daoAccountV2.getAccountHierarchyPaths(accountParentAccountMap.values.toSet).map{ parentAhList =>
        accountParentAccountMap.keySet.foreach { accountId =>
          val parentAh = parentAhList.find(_.accountId == accountParentAccountMap(accountId)).head
          parentAccountTypeMap.put(accountId, parentAh.accountType)
        }
      }
      Future.successful(parentAccountTypeMap.toMap)
    } else Future.successful(Map.empty)
  }

  def isValidSubAccount(parentAccountId: Long, subAccounts: Set[Long]): Future[Unit] = {
    daoAccountV2.isValidSubAccount(parentAccountId, subAccounts) map {
      case false =>
        logger.info(s"SubAccount Not valid ${subAccounts.mkString(",")} ")
        throw ErrorResponseException(ErrorResponseFactory.get(InvalidSubAccount))
      case _ =>
        ()
    }
  }

  def isValidUser(accountId: Long, userId: Option[Long]): Future[Unit] = {
    userId match {
      case Some(u) => daoAccountV2.isValidUser(accountId, u) flatMap {
        case false =>
          validateIsAccountOwnerInParentHierarchy(u, accountId).map {
            case true =>
              ()
            case false =>
              logger.info(s"User account association doesn't exist for $userId and $accountId")
              throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
          }
        case true => Future.successful(())
      }
      case _ => Future.successful()
    }
  }

  def isValidBusinessUser(accountId: Long, userId: Long): Future[Boolean] = {
    daoAccountV2.isValidBusinessUser(accountId, userId)
  }

  def isValidUser(accountIds: Set[Long], users: Set[Long]): Future[Unit] = {
    daoAccountV2.isValidUser(accountIds, users) map {
      case false =>
        logger.info(s"user Not valid ")
        throw ErrorResponseException(ErrorResponseFactory.get(BusinessUserNotFound))
      case _ =>
        ()
    }
  }

  def isValidMigrationInput(accountType: Int, subAccounts: Seq[SubAccountMigrationDetails], parentAccountId: Option[Long], associateAllUsersToSubAccount: Boolean): Future[Unit] = {
    if(associateAllUsersToSubAccount) {
      subAccounts.foreach(subAccount =>
        if(subAccount.userId.isDefined) throw ErrorResponseException(ErrorResponseFactory.get(InvalidInputFormat))
      )
    }
    accountType match {
      case AccountTypes.DIRECT_CUSTOMER.id => Future.successful(Future.successful(()))
      case AccountTypes.RESELLER.id | AccountTypes.AGGREGATOR.id =>
        subAccounts.foreach(subAccount =>
          subAccount.administer match {
            case Some(administer) =>
              if(!administer && subAccount.userId.isEmpty && !associateAllUsersToSubAccount) throw ErrorResponseException(ErrorResponseFactory.get(BusinessUserNotFound))
            case None => throw ErrorResponseException(ErrorResponseFactory.get(InvalidInputFormat))
          }
        )
        Future.successful(Future.successful(()))
      case AccountTypes.SUB_ACCOUNT.id =>
        parentAccountId match {
          case None => throw ErrorResponseException(ErrorResponseFactory.get(InvalidInputFormat))
          case Some(parentAccountId) => isAccountV2Provisioned(Set(parentAccountId))
        }
      case _ => throw ErrorResponseException(ErrorResponseFactory.get(InvalidAccountType))
    }
  }

  def validateAccountAccess(accountId: Long, creatorId: Long, ignoreAdministerFlag: Boolean = false): Future[DtoAccountHierarchy] = {
    daoAccountV2.getAccountHierarchyByAccountId(accountId: Long) flatMap {
      case Some(ah) =>
        val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
        if(accountIds.nonEmpty && (accountId == creatorId || accountIds.contains(creatorId))) {
          daoAccountV2.getAccountHierarchyByAccountId(accountIds.head) flatMap {
            case Some(ah0) =>
              if(accountId == creatorId)  Future.successful(ah0)
                else{ validateAdmin(ah0, ah, ignoreAdministerFlag) map {
                  case true => ah0
                  case _ =>
                    logger.info("Creator and associate account validation failed")
                    throw ErrorResponseException(ErrorResponseFactory.get(AdminsterNotProvisioned))
                }
              }
            case _ =>
              logger.info(s"Account Hierarchy not found for ${accountIds.head}")
              throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
          }
        }else{
          logger.info(s"Invalid account association: $accountId for creator $creatorId")
           throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
        }
      case _ =>
        logger.info(s"Account Hierarchy not found for $accountId")
         throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))
    }
  }

  def validateAccountAccess(accountIds: Seq[Long], creatorId: Long): Future[Seq[DtoAccountHierarchy]] = {
    Future.sequence(accountIds.map(accountId => validateAccountAccess(accountId, creatorId)))
  }

  def validateAccountAccess(accountIds: Seq[Long], creatorId: Long, creatorUserId: Long, userId: Option[Long]): Future[Seq[DtoAccountHierarchy]] = {
    daoAccountV2.getAssociatedAccounts(creatorUserId, accountIds, userId) flatMap { associatedAccounts =>
      associatedAccounts.isEmpty match {
        case true =>  Future.sequence(accountIds.map(accountId => validateAccountAccess(accountId, creatorId)))
        case false =>  Future.sequence(accountIds.map(accountId =>  validateAccountAccess(accountId, creatorId, associatedAccounts.map(_.id).contains(accountId))))
      }
    }
  }

  def validateRiskOsSupportRoles(creatorUserId: Long, accountId: Long, inputBusinessUserEmail: String, accountWithRolesInput: Seq[AccountWithRolesInput]): Future[Boolean] = {
    daoAccountV2.getUserDetailsV2(creatorUserId, accountId).flatMap {
      case Some(userDetails) if userDetails._3.matches(AccountManagementDefaults.internalEmailDomains) && inputBusinessUserEmail.matches(AccountManagementDefaults.internalEmailDomains) => // validating creator & input user is socure
        Future.successful(true)
      case _ if inputBusinessUserEmail.matches(AccountManagementDefaults.internalEmailDomains) => // validating input user is socure user and creator is non-socure user
        Future.successful(true)
      case _ => // validating support roles for non-socure users
        val riskOsSupportRoles = Set(SystemDefinedRoles.RISKOS_SUPPORT_ADMIN.roleType, SystemDefinedRoles.RISKOS_SUPPORT_VIEWER.roleType)
        if (accountWithRolesInput.exists(_.roles.exists(role => riskOsSupportRoles.contains(role.roleType)))) {
          logger.info(s"Unauthorized access to provision RiskOS support roles for user $creatorUserId")
          Future.failed(ErrorResponseException(ErrorResponseFactory.get(AccessForbidden)))
        } else {
          Future.successful(true)
        }
    }
  }

  def validateAdmin(parent: DtoAccountHierarchy, account: DtoAccountHierarchy, ignoreAdministerFlag: Boolean): Future[Boolean] = {
    def handleChannelPartner: Future[Boolean] = {
      if (ignoreAdministerFlag) {
        Future.successful(true)
      } else {
        daoAccountV2.isAccountPermissionProvisioned(parent.accountId, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id).flatMap {
          case true =>
            isSubAccountAdministerProvisioned(account)
          case false =>
            logger.info("Administer Sub Accounts not enabled for the partner account")
            Future.failed(ErrorResponseException(ErrorResponseFactory.get(AdministerSubAccountsNotProvisioned)))
        }
      }
    }
    parent.accountType match {
      case t if AccountTypes.isTypeDirect(t) =>
        Future.successful(true)
      case t if AccountTypes.isTypeChannelPartner(t) =>
        handleChannelPartner
      case AccountTypes.SUB_ACCOUNT.id =>
        logger.info("The first level parent cannot be a sub account. Something wrong")
        Future.failed(ErrorResponseException(ErrorResponseFactory.get(InvalidParentAccountType)))
      case _ =>
        logger.info("Invalid parent account type")
        Future.failed(ErrorResponseException(ErrorResponseFactory.get(InvalidParentAccountType)))
    }
  }


  def validateAdministerAccount(parent: DtoAccountHierarchy, account: DtoAccountHierarchy, ignoreAdministerFlag: Boolean): Future[Boolean] = {
    parent.accountType match {
      case n if AccountTypes.isTypeDirect(parent.accountType) => Future.successful(true)
      case n if AccountTypes.isTypeChannelPartner(n) && ignoreAdministerFlag => Future.successful(true)
      case n if AccountTypes.isTypeChannelPartner(n) =>
        daoAccountV2.isAccountPermissionProvisioned(parent.accountId, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id) flatMap {
          case true =>
            isSubAccountAdministerProvisioned(account)
          case false =>
            logger.info("Administer Sub Accounts not enabled for the partner account")
            Future.successful(false)
        }
      case AccountTypes.SUB_ACCOUNT.id =>
        logger.info("The first level parent cannot be a sub account. Something wrong")
        Future.successful(false)
      case _ =>
        logger.info("Invalid parent account type")
        Future.successful(false)
    }
  }

  def isSubAccountAdministerProvisioned(accountHierarchy: DtoAccountHierarchy): Future[Boolean] = {
    val accountIds = accountHierarchy.hierarchyPath.split("/")
    accountIds.length match {
      case n if n > 1 => daoAccountV2.isAdministerProvisioned(accountIds(1).toLong)
      case n if n == 1 => Future.successful(true)
      case _ => Future.successful(false)
    }
  }

  def validatePrimaryAdminRateLimit(accountId: Long): Future[Unit] = {
    primaryAdminRateLimitExceeded(accountId) map {
      case true => logger.info(s"Cannot add user as primary admin")
        throw ErrorResponseException(ErrorResponseFactory.get(PrimaryAdminLimitExceeded))
      case _ => ()
    }
  }

  def primaryAdminRateLimitExceeded(accountId: Long): Future[Boolean] = {
    (for {
      primaryAdminCount <- daoAccountV2.getPrimaryAdminsCount(accountId)
      accountHierarchy <- daoAccountV2.getAccountHierarchyByAccountId(accountId)
    } yield (primaryAdminCount, accountHierarchy)) map {
      case (c, Some(ah:DtoAccountHierarchy)) =>
          if (ah.numberOfPrimaryAdmins <= c) true else false
      case(_, _) => false
    }
  }

  def validateIsAccountOwnerInParentHierarchy(businessUserId: Long, accountId: Long): Future[Boolean] ={
    daoAccountV2.getAccountHierarchyByAccountId(accountId: Long) flatMap {
      case Some(ah) =>
        val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
        if(accountIds.nonEmpty) {
          daoAccountV2.getAccountHierarchyByAccountId(accountIds.head) flatMap {
            case Some(ah0) =>
              validateAdministerAccount(ah0, ah, false) flatMap  {
                case true => daoAccountV2.isAccountOwnerInParentAccountHierarchy(businessUserId, accountId, ah.hierarchyPath)
                case _ => Future.successful(false)
              }
            case _ => logger.info(s"Account Hierarchy not found for ${accountIds.head}")
              Future.successful(false)
          }
        } else {
          logger.info(s"Account Hierarchy not found for ${accountId}")
          Future.successful(false)
        }
      case _ => logger.info(s"Account Hierarchy not found for ${accountId}")
        Future.successful(false)
    }
  }

  def validateHasAccountOwnerInParentHierarchy(businessUserId: Long, accountId: Long): Future[Seq[DtoUserAccountAssociation]] ={
    daoAccountV2.getAccountHierarchyByAccountId(accountId: Long) flatMap {
      case Some(ah) =>
        val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
        if(accountIds.nonEmpty) {
          daoAccountV2.getAccountHierarchyByAccountId(accountIds.head) flatMap {
            case Some(ah0) =>
              validateAdministerAccount(ah0, ah, false) flatMap  {
                case true => daoAccountV2.hasAccountOwnerInParentAccountHierarchy(businessUserId, accountId, ah.hierarchyPath)
                case _ => Future.successful(Seq.empty[DtoUserAccountAssociation])
              }
            case _ => logger.info(s"Account Hierarchy not found for ${accountIds.head}")
              Future.successful(Seq.empty[DtoUserAccountAssociation])
          }
        } else {
          logger.info(s"Account Hierarchy not found for ${accountId}")
          Future.successful(Seq.empty[DtoUserAccountAssociation])
        }
      case _ => logger.info(s"Account Hierarchy not found for ${accountId}")
        Future.successful(Seq.empty[DtoUserAccountAssociation])
    }
  }

  def filterAdministerProvisionedAccounts(accountIdNameSeq: Seq[AccountIdName], rootAccHierarchy: DtoAccountHierarchy): Future[Seq[AccountIdName]]={
    rootAccHierarchy.accountType match {
      case n if AccountTypes.isTypeDirect(rootAccHierarchy.accountType) =>
        Future.successful(accountIdNameSeq)
      case n if AccountTypes.isTypeChannelPartner(rootAccHierarchy.accountType) =>
        daoAccountV2.isAccountPermissionProvisioned(rootAccHierarchy.accountId, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id) flatMap {
          case true =>
            daoAccountV2.getAccountHierarchyPaths(accountIdNameSeq.map(_.id).toSet).flatMap { paths =>
              val secondMostParentAccIds = paths.filter(ah => ah.hierarchyPath.split("/").length>1).map(ah => ah.hierarchyPath.split("/")(1).toLong)
              daoAccountV2.getAccountHierarchyPaths(secondMostParentAccIds.toSet).map { secondMostParentAccHierarchy =>
                accountIdNameSeq.filter(acc => isAdministerProvisionedAccount(paths.find(_.accountId == acc.id), secondMostParentAccHierarchy))
              }
            }
          case false =>
            logger.info("Administer Sub Accounts not enabled for the partner account")
            Future.successful(Seq.empty[AccountIdName])
        }
      case _ =>
        Future.successful(Seq.empty[AccountIdName])
    }
  }

  def isAdministerProvisionedAccount(accountHierarchy: Option[DtoAccountHierarchy], secondMostParentAccHierarchy: Seq[DtoAccountHierarchy]): Boolean = {
    accountHierarchy match {
      case Some(ah) =>
        val accountIds = ah.hierarchyPath.split("/")
        accountIds.length match {
          case n if n > 1 => secondMostParentAccHierarchy.find(_.accountId == accountIds(1).toLong) match {
            case Some(sah) => sah.administer
            case None => false
          }
          case n if n == 1 => true
          case _ => false
        }
      case _ => false
    }
  }

  def getAccountOwnerAssociatedAccounts(userId: Long, ignoreAccountIds: Seq[AccountIdName]): Future[Seq[AccountIdName]] = {
    daoAccountV2.getUserAccountOwnerAssociations(userId).flatMap { accountIds =>
      if(accountIds.nonEmpty) {
        getRootParentAccountHierarchy(accountIds.head).flatMap {
          case Some(rootAccHierarchy) =>
            val futureCriteriaAccountPathsAndIds = if(accountIds.contains(rootAccHierarchy.accountId)) {
              Future.successful(Set(rootAccHierarchy.hierarchyPath), Set(rootAccHierarchy.accountId))
            } else {
              daoAccountV2.getAccountHierarchyPaths(accountIds).map{ paths =>
                (filterAccountsToGetAccountOwnerAssociatedAccounts(paths.map(_.hierarchyPath)), accountIds)
              }
            }
            for {
              accountPathsAndIds <- futureCriteriaAccountPathsAndIds
              administerProvisionedAccounts <- daoAccountV2.getAccountOwnerAssociatedAccounts(accountPathsAndIds._2, ignoreAccountIds, accountPathsAndIds._1).flatMap { filteredAccounts =>
                filterAdministerProvisionedAccounts(filteredAccounts, rootAccHierarchy)
              }
            } yield administerProvisionedAccounts
          case None => Future.successful(Seq.empty)
        }
      } else {
        Future.successful(Seq.empty)
      }
    }
  }

  def filterAccountsToGetAccountOwnerAssociatedAccounts(hierarchyPaths: Seq[String]): Set[String] = {
    val sortedHierarchyPaths = hierarchyPaths.sorted
    val result: mutable.Set[String] = mutable.Set.empty[String]
    var prevPath = sortedHierarchyPaths(0)
    for(iterator <- 1 until sortedHierarchyPaths.length){
      val nextPath = sortedHierarchyPaths(iterator)
      if(!nextPath.startsWith(prevPath)){
        result.add(prevPath)
        prevPath = nextPath
      }
    }
    result.add(prevPath)
    result.toSet
  }

  def validatePermissions(accountId: Long, businessUserId: Long, permissions: Set[Int], envType: Int, orPermissions: Set[Int]=Set.empty): Future[Unit] = {
    validateIsAccountOwnerInParentHierarchy(businessUserId, accountId) flatMap {
      case false =>
        (for {
          customRolePermissions <- daoAccountV2.fetchPermissions(accountId, businessUserId, envType)
          rootAccountType <- getRootParentAccountType(accountId)
          sytemRolePermissions <- daoAccountV2.fetchSystemRolesPermissions(accountId, businessUserId, envType, rootAccountType)
        } yield (sytemRolePermissions, customRolePermissions)) map {
          case (systemPermissins, customPermissions) =>
            customPermissions.map { p =>
              if (p.isEmpty) {
                Set.empty[Int]
              } else {
                p.split(",").toSet.map { a: String => a.trim.toInt }
              }
            }.toSet.flatten ++ systemPermissins
        } map { p => (permissions.subsetOf(p) || (orPermissions.nonEmpty && p.exists(orPermissions.contains)))} map {
          case false =>
            val er = permissions.mkString(",")
            logger.info(s"Invalid role provisioning, may be the parent does not have the provisioning permissions - $er")
            throw ErrorResponseException(ErrorResponseFactory.get(InvalidRoleProvisioning))
          case _ =>
            ()
        }
      case true =>
        Future.successful(())
    }
  }

  def skipOrValidatePermissions(accountId: Long, businessUserId: Long, permissions: Set[Int], envType: Int, skipPermissionChk: Boolean): Future[Unit] = {
    if(!skipPermissionChk){
      validatePermissions(accountId, businessUserId, permissions, envType)
    } else {
      Future.successful(())
    }
  }

  def validatePermissions(creatorAccountId: Long, businessUserId: Long, permissions: Set[Int], envType: Int, accountId: Long, rootAccount: DtoAccountHierarchy): Future[Unit] = {
    if((accountId != creatorAccountId) && rootAccount.accountType == AccountTypes.AGGREGATOR.id && permissions.contains(DashboardUserPermissions.TRANSACTIONS_CREATE.id)){
      logger.info(s"Reseller Partner Parent cannot create Transactions for their sub accounts")
      throw ErrorResponseException(ErrorResponseFactory.get(InvalidPermissions))
    }
    validateIsAccountOwnerInParentHierarchy(businessUserId, accountId) flatMap {
      case false =>
        (for {
          customRolePermissions <- daoAccountV2.fetchPermissions(accountId, businessUserId, envType)
          rootAccountType <- getRootParentAccountType(accountId)
          sytemRolePermissions <- daoAccountV2.fetchSystemRolesPermissions(accountId, businessUserId, envType, rootAccountType)
        } yield (sytemRolePermissions, customRolePermissions)) map {
          case (systemPermissins, customPermissions) =>
            customPermissions.map { p =>
              if (p.isEmpty) {
                Set.empty[Int]
              } else {
                p.split(",").toSet.map { a: String => a.trim.toInt }
              }
            }.toSet.flatten ++ filterSystemRolesPermissionsBasedOnAccountType(creatorAccountId, rootAccount, systemPermissins)
        } map (permissions.subsetOf(_)) map {
          case false =>
            val er = permissions.mkString(",")
            logger.info(s"Invalid role provisioning, may be the parent does not have the provisioning permissions - $er")
            throw ErrorResponseException(ErrorResponseFactory.get(InvalidRoleProvisioning))
          case _ =>
            ()
        }
      case true =>
        Future.successful(())
    }
  }

  private def filterSystemRolesPermissionsBasedOnAccountType(creatorAccountId: Long, rootAccount: DtoAccountHierarchy, systemRolePermissions: Set[Int]): Set[Int] = {
    if(rootAccount.accountType == AccountTypes.AGGREGATOR.id && rootAccount.hierarchyPath.startsWith(creatorAccountId.toString)) {
      systemRolePermissions.filterNot(_ == DashboardUserPermissions.TRANSACTIONS_CREATE.id)
    } else systemRolePermissions
  }

  def validatePermissionTemplateAccess(permissionTemplateId: Long, creatorAccountId: Long): Future[Unit] = {
    daoAccountV2.getAccountIdByPermissionTemplateId(permissionTemplateId) map {
      case Some(accountId) =>
        validateAccountAccess(accountId, creatorAccountId)
      case None =>
        logger.info("Invalid permission template id")
        throw ErrorResponseException(ErrorResponseFactory.get(InvalidPermissionTemplateId))
    }
  }

  def validateUserRoleAccess(userRoleId: Long, creatorAccountId: Long): Future[DtoAccountHierarchy] = {
    daoAccountV2.getUserRole(userRoleId) flatMap {
      case Some(dtoUserRole) =>
        validateAccountAccess(dtoUserRole.byAccountId, creatorAccountId)
      case None =>
        logger.info("Invalid user role id")
        throw ErrorResponseException(ErrorResponseFactory.get(InvalidUserRoleId))
    }
  }

  def validateUserRoleAccessToAssociateAccount(userRoleId: Long, creatorAccountId: Long): Future[Boolean] = {
        daoAccountV2.isAccountAssociatedToRoleId(creatorAccountId, 0, userRoleId) flatMap (isAssociated =>
          isAssociated match {
            case true => Future.successful(true)
            case _=> logger.info("Role id doesnt exist for the custom role")
              throw ErrorResponseException(ErrorResponseFactory.get(InvalidUserRoleId))
          })
  }

  def validateUserRoleAccess(userRoleIds: Seq[Long], creatorAccountId: Long): Future[Seq[DtoAccountHierarchy]] = {
    daoAccountV2.getUserRoles(userRoleIds) flatMap  { dtoUserRoleSeq =>
      Future.sequence(dtoUserRoleSeq.map(dtoUserRole => daoAccountV2.isSubAccount(dtoUserRole.byAccountId, creatorAccountId) flatMap ( isSubAccount =>
        isSubAccount match {
          //Eg Case - Propagating Customrole from a Parent Account to a SubAccount Since Role ID belongs to a parent account ID and Creator ID is a subaccount, inverting the parameters to validate proper access
          case true => validateAccountAccess(creatorAccountId, dtoUserRole.byAccountId)
          case false => validateAccountAccess(dtoUserRole.byAccountId, creatorAccountId)
        }
      )))
    }
  }

  def validateUserRoleDetails(roles: Seq[RolesInputDetails]): Future[Unit] = {
    roles foreach { r =>
      if(SystemDefinedRoles.isValidRoleType(r.roleType)){
        if(r.roleType == SystemDefinedRoles.CUSTOMROLE.roleType && r.roleId.isEmpty){
          logger.info(s"Role id doesnt exist for the custom role")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.InvalidCustomRole))
        }
      } else {
        logger.info(s"Invalid role type found ${r.roleType}")
        throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.InvalidRoleType))
      }
    }
    Future.successful(())
  }

  def validateUserAccountAssociation(userId: Long, accountId: Long): Future[Unit] = {
    daoAccountV2.getUserAccountAssociation(userId, accountId) flatMap  {
      case Some(_) =>
        Future.successful(())
      case _ =>
        validateIsAccountOwnerInParentHierarchy(userId, accountId).map {
          case true =>
            ()
          case false =>
            logger.info(s"User account association doesn't exist for $userId and $accountId")
            throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
        }
    }
  }

  def validateUserAccountAssociation(userId: Long, accountIds: Seq[Long], creatorAccountId: Long): Future[Unit] = {
    daoAccountV2.getUserAccountAssociation(userId, accountIds) flatMap  {
      case Some(_) =>
        Future.successful()
      case _ => daoAccountV2.getUser(userId) flatMap {
        case users if users.nonEmpty =>
          daoAccountV2.getAccountHierarchyByAccountId(creatorAccountId) map {
            case Some(ah) =>
              val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
              if (accountIds.nonEmpty && accountIds.contains(users.head.accountId)) {
                ()
              } else {
                logger.info(s"User account and creator $creatorAccountId are in different Hierarchy")
                throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
              }
            case None =>
              logger.info(s"Unable to get account Hierarchy for $creatorAccountId")
              throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
          }
        case _ =>
          logger.info(s"Unable to get User details for $userId")
          throw ErrorResponseException(ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation))
      }
    }
  }

  def isAdministrable(accountId: Long): Future[Boolean] = {
    daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
      case Some(ah) =>
        fetchParent(ah) flatMap  {
          case Some(parentAccountHierarchy) =>
            if(AccountTypes.isTypeDirect(parentAccountHierarchy.accountType)) Future.successful(true)
            else {
              isFirstLevelSubAccountAdministrable(parentAccountHierarchy.hierarchyPath)
            }
        }
      case _ => Future.successful(false)
    }
  }

  def isFirstLevelSubAccountAdministrable(parentAccountHierarchyPath: String): Future[Boolean] = {
    daoAccountV2.getAccountInfoV2(parentAccountHierarchyPath).map(_.find(_._1.hierarchyPath.split("/").length == 2).head._1.administer)
  }

  def validateAccountPermissions(accountId: Long, accountPermissions: Set[Int]): Future[Boolean] = {
    daoAccountV2.getAccountPermissions(accountId).map { ap =>
      accountPermissions.subsetOf(ap.map(_.permission).toSet)
    }
  }

  def validateAccountPermissionProvisioned(accountId: Long, premissionId: Int): Future[Boolean] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId,premissionId)
  }

  def validateSubAccountCreationRequest(subAccountCreationRequest: SubAccountCreationRequest): Future[Unit] = {
    daoAccountV2.getBySector(subAccountCreationRequest.industry) flatMap {
      case Some(_) =>
        if (subAccountCreationRequest.accountName.trim.isEmpty)
          throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.AccountNameRequired))
        else {
          daoAccountV2.doesAccountNameExist(subAccountCreationRequest.accountName.trim) flatMap {
            case false =>
              val subAccountModulesProvided = subAccountCreationRequest.modules.getOrElse(Set.empty[String]).map(BusinessUserRoles.byPublicId)
              val permissions = subAccountModulesProvided.flatten.map(_.id)
              val validModules = subAccountModulesProvided.forall(_.isDefined) && BusinessUserRoles.isValidRoles(Resources.IDPLUS, permissions)
              if (!validModules)
                throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.ModulesIncorrect))
              else {
                // user validation
                subAccountCreationRequest.user match {
                  case Some(user) =>
                    val valid = EmailValidator.getInstance().isValid(user.email)
                    if (!valid)
                      throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.IncorrectEmailId))
                    else {
                      daoAccountV2.getUserIdByEmail(user.email) flatMap {
                        case Some(_) => validateAccountPermissions(subAccountCreationRequest)
                        case None =>
                          if (user.firstName.isEmpty || user.firstName.get.trim.isEmpty)
                            throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.UserFirstNameRequired))
                          else if (user.lastName.isEmpty || user.lastName.get.trim.isEmpty)
                            throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.UserLastNameRequired))
                          else if (user.contactNumber.isEmpty || user.contactNumber.get.trim.isEmpty)
                            throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.UserContactNumberRequired))
                          else
                            validateAccountPermissions(subAccountCreationRequest)
                      }
                    }
                  case None =>
                    validateAccountPermissions(subAccountCreationRequest)
                }
              }
            case _ => throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.AccountNameAlreadyExists))
          }
        }
      case None =>
        if (subAccountCreationRequest.industry.trim.isEmpty)
          throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.IndustryRequired))
        else
          throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.IncorrectIndustry))
    }
  }

  def getRootParentAccount(accountId: Long): Future[Option[Long]] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
          case Some(accountHierarchy) => fetchParent(accountHierarchy) map {
            case Some(rph) => Some(rph.accountId)
            case _ => None
          }
          case _ => Future.successful(None)
        }
      case _ => Future.successful(None)
    }
  }

  def getRootParentAccountType(accountId: Long): Future[Option[Int]] = {
    getRootParentAccountHierarchy(accountId).map {
      case Some(accountHierarchy) => Some(accountHierarchy.accountType)
      case _ => None
    }
  }

  def getRootParentAccountHierarchy(accountId: Long): Future[Option[DtoAccountHierarchy]] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap {
          case Some(accountHierarchy) => fetchParent(accountHierarchy) map {
            case Some(rph) => Some(rph)
            case _ => None
          }
          case _ => Future.successful(None)
        }
      case _ => Future.successful(None)
    }
  }

  def getRootParentAccountDetail(accountId: Long): Future[Option[(DtoAccount, DtoAccountHierarchy)]] = {
    getRootParentAccount(accountId).flatMap {
      case Some(rootAccId) => daoAccountV2.getAccountWithHierarchy(rootAccId)
      case _ => Future.successful(None)
    }
  }

  def getRootParentAccountTypes(accountIds: Seq[Long]): Future[Map[Long, Int]] = {
    daoAccountV2.getAccountHierarchyPaths(accountIds.toSet) flatMap { ahs =>
        fetchParent(ahs)
    }
  }

  private def validateAccountPermissions(subAccountCreationRequest: SubAccountCreationRequest): Future[Unit] = {
    daoAccountV2.getParentAccountWithPermissionsByApiKey(subAccountCreationRequest.apiKey) map { permissions => {
      if (permissions.nonEmpty) {
        val permissionIds = permissions.map(_.permission)
        if (!permissionIds.contains(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
          logger.info(s"Account not V2 provisioned, given api key ${subAccountCreationRequest.apiKey}")
          throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.AccountNotV2Provisioned))
        } else if (!permissionIds.contains(BusinessUserRoles.ALLOW_SUBACCOUNTS.id)) {
          logger.info(s"Account does not have sub account provisioning, given api key ${subAccountCreationRequest.apiKey}")
          throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.PermissionMissing))
        }
      } else {
        logger.info(s"Account not provisioned with valid permissions, given api key ${subAccountCreationRequest.apiKey}")
        throw ErrorResponseException(ErrorResponseFactory.get(PublicExceptionCodes.NonPrimaryAccountAPIKey))
      }
    }
    }
  }

  def isParent(accountId: Long): Future[Boolean] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) map {
          case Some(accountHierarchy) if accountHierarchy.accountType != AccountTypes.SUB_ACCOUNT.id => true
          case _ => false
        }
      case false =>
        daoAccountV2.isParentAccount(accountId)
    }
  }
}

