package me.socure.account.dashboard

import me.socure.DaoAccount
import me.socure.account.SubAccountRevealer
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccessCredentials, AccountDetails, AccountInfo}
import me.socure.model.user.{DashboardUser, UserInfoWithAccountId}
import me.socure.storage.slick.tables.account.DtoAccount
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/21/16.
  */
class DashboardUserService(val daoAccount: DaoAccount,
                           daoBusinessUser: DaoBusinessUser)
                          (implicit val ec : ExecutionContext) extends SubAccountRevealer {//this trait will have a new implementation later


  def getAccountCrdentialsByApiKey(socurekey : String) : Future[Either[ErrorResponse, AccessCredentials]] = {

    daoAccount.getAccountCredsByApiKey(socurekey) map {
      case Some(u) => Right(AccountConvertors.getAccessCredentials(u._2, u._1.apiKey))
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def findUserByEmail(email: String) : Future[Either[ErrorResponse, DashboardUser]] = {

    daoBusinessUser.getDashboardUser(email) flatMap {
      case bu +: _ =>
        val futurePermission = daoAccount.getAccountPermissions(bu.accountId)
        val futureRole = daoBusinessUser.getBusinessUserRoles(bu.id)
        for{
          role <- futureRole
          permission <- futurePermission
          user <- Future.successful(Right(bu.copy(roles = (bu.roles ++ role ++ permission.map(_.permission)).toSet)))
        } yield user
      case _ => Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    }
  }

  def checkToSAgreementByUser(email: String): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.getUser(email) flatMap {
      case Some(bu) =>
        Future.successful(Right(bu.isAgreedTermsOfService))
      case _ => Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    }
  }

  def getPrimaryUser(delegatedAdminEmail: String) : Future[Either[ErrorResponse, DashboardUser]] = {

    daoBusinessUser.getPrimaryAdminUser(delegatedAdminEmail) flatMap {
      case bu +: _ =>
        val futureRoles = daoBusinessUser.getBusinessUserRoles(bu.id)
        val futurePermission = daoAccount.getAccountPermissions(bu.accountId)
        for {
          roles <- futureRoles
          permission <- futurePermission
          user <- Future.successful(Right(bu.copy(roles = (bu.roles ++ roles ++ permission.map(_.permission)).toSet)))
        } yield user
      case _ => Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    }
  }

  def getAccountIdByEmail(email : String) : Future[Either[ErrorResponse, Long]] = {

    daoBusinessUser.getAccountIdByEmail(email) map {
      case Some(id)  => Right(id)
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }
  }

  def getUserInfoWithAccountIdByEmail(email : String) : Future[Either[ErrorResponse, UserInfoWithAccountId]] = {
    daoBusinessUser.getUserInfoWithAccountIdByEmail(email) map {
      case Some(dtoBusinessUser)  => Right(UserInfoWithAccountId(dtoBusinessUser.id, dtoBusinessUser.firstName, dtoBusinessUser.lastName, dtoBusinessUser.email, dtoBusinessUser.accountId))
      case _ => Left(ErrorResponseFactory.get(BusinessUserNotFound))
    }
  }

  def findSubAccountsForAccountId(id: Long): Future[List[AccountInfo]] = {
    daoAccount.getWithSubAccounts(id) map {
      case obj: Seq[DtoAccount] => obj.map { a => AccountInfo(a.accountId, a.name, a.parentId) }.toList
      case _ => List.empty
    }
  }
}
