package me.socure.account.dashboardv2

import me.socure.DaoAccount
import me.socure.account.automation.AccountAutomationService
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.account.service._
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.account.validator.{<PERSON><PERSON><PERSON><PERSON><PERSON>, V2Validator}
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.common.metrics.models.MetricTags.isInternal
import me.socure.configuration.ApiKeyRenewalConfig
import me.socure.configuration.{AnalyticsConfig, ApiKeyRenewalConfig}
import me.socure.constants.ProductSettingsFields.GENERAL
import me.socure.constants._
import me.socure.convertors.{AccountConvertors, BusinessUserConvertors}
import me.socure.mail.service.MailNotificationService
import me.socure.model._
import me.socure.model.account._
import me.socure.model.dashboardv2._
import me.socure.model.encryption.AccountId
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{DashboardUserRole, SubAccountFormV2, UserForm, UserFormWithNoPassword}
import me.socure.password.PasswordConstraintCheck
import me.socure.storage.slick.dao.{DaoAccountUIConfiguration, DaoAccountV2, DaoEnvironment, DaoPublicApiKey}
import me.socure.storage.slick.tables.account.{ApiKey => _, _}
import me.socure.storage.slick.tables.industry.DtoIndustry
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoActivationToken, DtoBusinessUser}
import org.jasypt.encryption.StringEncryptor
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.StandardCharsets
import java.util.Base64
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
import scala.util.{Failure, Success, Try}

/**
 * Created by gopal on 04/08/16.
 */
class DashboardAccountServiceV2 (daoAccount: DaoAccount,
                                 daoAccountV2: DaoAccountV2,
                                 daoBusinessUser: DaoBusinessUser,
                                 passwordService: PasswordStorageService,
                                 daoEnvironment: DaoEnvironment,
                                 clock: Clock,
                                 encryptionKeysService: EncryptionKeysService,
                                 apiKeyRenewalConfig: ApiKeyRenewalConfig,
                                 customerSuccessMailId: Option[String],
                                 mailNotificationService: MailNotificationService,
                                 daoPublicApiKey: DaoPublicApiKey,
                                 v2Validator: V2Validator,
                                 pbeEncryptor: StringEncryptor,
                                 auditRequestEncipher: AuditRequestEncipher,
                                 rateLimitingService: RateLimitingService,
                                 modelManagementClient: ModelManagementClient,
                                 daoAccountUIConfiguration: DaoAccountUIConfiguration,
                                 businessUserCommonService: BusinessUserCommonService,
                                 dashboardUserService2: DashboardUserServiceV2,
                                 accountPgpKeysService: AccountPgpKeysService,
                                 pgpKeyExpiryDuration: Long,
                                 auditDetailsService: AuditDetailsService,
                                 analyticsConfig: AnalyticsConfig
                                )(implicit val ec : ExecutionContext) {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)
  lazy val deprecationHours = apiKeyRenewalConfig.drepcateDurationInHours
  lazy val renewalTimeInHours = apiKeyRenewalConfig.renewalTimeInHours
  val DefaultRateLimit=15
  val defaultAutoTimeoutInMinutes = 30
  val defaultIdleTimeoutInMinutes = 30

  def listAllAccounts(parentAccountId : Int)  : Future[Either[ErrorResponse,Vector[AccountV2]]] = {
    daoAccount.getSubAccountsWithUserCount(parentAccountId) map {
      case list if list.nonEmpty => Right(list)
      case _ => Left(ErrorResponseFactory.get(SubAccountNotFound))
    }
  }

  def listSubAccounts(parentAccountId : Int)  : Future[Either[ErrorResponse,List[SubAccountV2]]] = {
    daoAccount.getSubAccountsWithIndustry(parentAccountId) map {
      case obj: Seq[(DtoAccount, DtoIndustry)] =>
        Right(obj.map(tup => SubAccountV2(tup._1.accountId, tup._1.name, tup._2.description, tup._1.isActive, Some(s"${parentAccountId}/${tup._1.accountId}/"))).toList)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def listSubAccountsByPublicId(publicParentId: String): Future[Either[ErrorResponse, List[SubAccountV2]]] = {
    daoAccount.getSubAccountsByPublicId(publicParentId).map{
      case obj: Seq[DtoAccount] => Right(obj.map(account => SubAccountV2(account.accountId,account.name,"",account.isActive,None)).toList)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getPrimaryUserByAccountId(accountId: Long): Future[Either[ErrorResponse, DtoBusinessUser]] = {
    daoAccountV2.fetchPrimaryUserOfAnAccount(accountId) map {
      case Some(dashboardUserV2) => Right(dashboardUserV2)
      case _ => Left(ErrorResponseFactory.get(UserNotFound))
    }
  }

  def listSubAccountsV2(parentAccountId : Long, creator: Creator, page: Option[Int], size: Option[Int], skipPermissionChk : Boolean = false)  : Future[Either[ErrorResponse,Seq[SubAccountV2]]] = {
    (for {
      _ <- v2Validator.validateAccountAccess(parentAccountId, creator.accountId)
      _ <- v2Validator.skipOrValidatePermissions(creator.accountId, creator.userId, Set(DashboardUserPermissions.ACCOUNTS_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id, skipPermissionChk)
      res <- daoAccountV2.getAccountHierarchyByAccountId(parentAccountId).flatMap {
        case Some(dtoAccountHierarchy) =>
          val hierarchyPath = Right(AccountConvertors.toAccountHierarchy(dtoAccountHierarchy)).right.get.hierarchyPath
          val isParentPartnerOrReseller = dtoAccountHierarchy.accountType == AccountTypes.RESELLER.id || dtoAccountHierarchy.accountType == AccountTypes.AGGREGATOR.id
          daoAccountV2.getAllAccountInfoV2WithIndustry(hierarchyPath).map {
            case resp: Seq[(DtoAccountHierarchy, DtoAccount, DtoIndustry)] =>
              val count = hierarchyPath.count(_ == '/')
              val subAccounts = resp.map(tup =>
                  if(isParentPartnerOrReseller)
                     SubAccountV2(tup._2.accountId, tup._2.name, tup._3.description, tup._2.isActive, Some(tup._1.hierarchyPath), Some(tup._1.administer))
                   else
                    SubAccountV2(tup._2.accountId, tup._2.name, tup._3.description, tup._2.isActive, Some(tup._1.hierarchyPath))
               ).filter(subAccount => {
                subAccount.hierarchyPath.get.split("/").length == count + 1
              })
              if(page.isDefined && size.isDefined)
                Right(subAccounts.slice((page.get - 1) * size.get, (page.get - 1) * size.get + size.get))
              else
                Right(subAccounts)
            case _ => Left(ErrorResponseFactory.get(ExceptionCodes.SubAccountFetchError))
          }
        case _ =>
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not list sub accounts v2", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching account hierarchy by account", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError))
    }
  }

  def listSubAccountsWithEnvDetails(parentAccountId: Long): Future[Either[ErrorResponse, Set[AccountInfoWithEnvDetails]]] = {
    daoAccount
      .getSubAccountsWithEnvDetails(parentAccountId)
      .map(_
        .groupBy { case (accId, _, _) => accId }
        .map { case (accId, envDetails) =>
          AccountInfoWithEnvDetails(
            id = accId,
            name = envDetails.headOption.map { case (_, accName, _) => accName },
            environments = envDetails.map { case (_, _, envId) =>
              IdAndName(id = envId, name = None)
            }.toSet
          )
        }.toSet
      ).map(Right(_))
  }

  type KeyEnvDetails = (DtoApiKey, String, DtoEnvironment)

  private def getHeadAttributes(seqTuple: Seq[KeyEnvDetails]): KeyEnvDetails = {
    seqTuple match {
      case hd +: _ => hd
      case _ => throw new IllegalArgumentException("Failed to retrieve head from Empty sequence")
    }
  }

  def getApiKeysForAccountAndSubAccounts(accountId: Long) : Future[Either[ErrorResponse, List[AccountApiKeys]]] = {
    daoAccount.getApiKeysForAccountAndSubAccounts(accountId) map {
      case (list:(Seq[(DtoApiKey, String, DtoEnvironment)])) if list.nonEmpty =>
        Right(getApiKeys(list))
      case list if list.isEmpty => Right(List.empty[AccountApiKeys])
      case _ =>  Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getSubAccountApiKeys(list: Seq[(DtoApiKey, String, DtoEnvironment)]): List[SubAccountApiKeys] = {
    val listByEnvId = list.groupBy(_._1.environmentId) map {
      case (k, l) =>
        val headTuple = getHeadAttributes(l)
        SubAccountApiKeys(accountName = headTuple._2,
          environmentId = headTuple._1.environmentId,
          environmentType = EnvironmentConstants(headTuple._3.environmentType.toInt).toString,
          apiKeys = l.map{ e =>
            val timeLeft = ApiKey.timeLeftForRenewal(deprecationHours, renewalTimeInHours, e._3.updatedAt, clock)
            val canBeRenewed = ApiKey.canBeRenewed(renewalTimeInHours = renewalTimeInHours, updatedAt = e._3.updatedAt, availableApiKeys = l.size, clock = clock)
            ApiKeyDetails(apiKey = e._1.apiKey,
              canBeRenewed = canBeRenewed,
              timeLeft = if (l.size > 1 && e._1.status.equals(ApiKeyStatus.ACTIVE)) Some(timeLeft) else None,
              status = e._1.status)
          })
    }
    listByEnvId.toList
  }

  private def getApiKeys(list: Seq[(DtoApiKey, String, DtoEnvironment)]): List[AccountApiKeys] = {
    val deprecationHours = apiKeyRenewalConfig.drepcateDurationInHours
    val renewalTimeInHours = apiKeyRenewalConfig.renewalTimeInHours
    val listByEnvId = list.groupBy(_._1.environmentId) map {
      case (k, l) =>
        val headTuple = getHeadAttributes(l)
        AccountApiKeys(
          accountId = headTuple._3.accountId,
          accountName = headTuple._2,
          environmentId = headTuple._1.environmentId,
          environmentType = EnvironmentConstants(headTuple._3.environmentType.toInt).toString,
          apiKeys = l.map{ e =>
            val timeLeft = ApiKey.timeLeftForRenewal(deprecationHours, renewalTimeInHours, e._3.updatedAt, clock)
            val canBeRenewed = ApiKey.canBeRenewed(renewalTimeInHours = renewalTimeInHours, updatedAt = e._3.updatedAt, availableApiKeys = l.size, clock = clock)
            ApiKeyDetails(apiKey = e._1.apiKey,
              canBeRenewed = canBeRenewed,
              timeLeft = if (l.size > 1 && e._1.status.equals(ApiKeyStatus.ACTIVE)) Some(timeLeft) else None,
              status = e._1.status)
          })
    }
    listByEnvId.toList
  }

  def createSubAcountWithNoPassword(parentId : Long, user: UserFormWithNoPassword) : Future[Either[ErrorResponse, UserActivationDetails]] = {
    businessUserCommonService.checkNApplyUserEmailAvailability(user.email).flatMap {
      case false =>
        logger.info("Email already exists")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.EmailAlreadyExists)))
      case true =>
        val bu = BusinessUserConvertors.getDtoBusinessUser(user)
        val account = AccountConvertors.getAccount(user, pbeEncryptor).copy(parentId = Some(parentId))
        createSubAccount(parentId, bu, account).flatMap {
          case Right(_) =>
            daoBusinessUser.getIdByEmail(user.email) flatMap { a =>
              val buId: Long = a.getOrElse(0)
              daoBusinessUser.createActivationCode(DtoActivationToken(0, buId, None, Some(clock.now()))) map { uuid =>
                val details = UserActivationDetails(
                  firstname = user.firstname,
                  surname = user.lastname,
                  email = user.email,
                  activationCode = uuid
                )
                Right(details)
              }
            }
          case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed)))
        }
    } recover {
      case e: Throwable =>
        logger.info("Registration failed", e)
        Left(ErrorResponse(RegistrationFailed.id, RegistrationFailed.description))
    }
  }

  def createSubAcount(parentId : Long, user: UserForm, isActive: Boolean = false) : Future[Either[ErrorResponse, Boolean]] = {
    businessUserCommonService.checkUserEmailAvailability(user.email) flatMap {
      case (_, false, _) => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      case (emailAlreadyExists, true, associatedAccounts) =>
        PasswordConstraintCheck.validatePasswordConstraint(user.email, user.firstname, user.lastname, user.password) match {
          case Right(true) =>
            daoAccount.doesAccountNameExist(user.companyname) flatMap {
              case true =>
                logger.info(ExceptionCodes.AccountAlreadyExists.description)
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
              case false =>
                daoAccount.fetchAccountInfoAndPermission(parentId).flatMap { info =>
                  info.headOption match {
                    case Some((pa, accountPermissions)) =>
                      val manageAccountsV2 = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id))
                      val isValidPermissions = user.modules.getOrElse(Set.empty[Int]).subsetOf(accountPermissions.map(_.permission).toSet)
                      val canCreateSubAccounts = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ALLOW_SUBACCOUNTS.id))
                      val canCreateUser = accountPermissions.exists(_.permission.equals(BusinessUserRoles.SUB_ACCOUNTS_CAN_CREATE_USER.id))
                      if (!manageAccountsV2) {
                        if (!isValidPermissions) {
                          logger.info(ExceptionCodes.InvalidPermissions.description)
                          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
                        } else if (canCreateSubAccounts && canCreateUser) {
                          if (pa.parentId.isEmpty) {
                            val bu = BusinessUserConvertors.getDtoBusinessUser(user).copy(isPrimaryUser = false)
                            val account = AccountConvertors.getAccount(user, pbeEncryptor, isActive).copy(parentId = Some(parentId))
                            emailAlreadyExists match {
                              case true =>
                                businessUserCommonService.handleIdleEmail(user.email, associatedAccounts) flatMap {
                                  case true => createSubAccount(parentId, bu, account, Some(user.password), user.modules)
                                  case false =>
                                    logger.info(s"Unable to Create User, updating the prefixed email failed")
                                    Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
                                }
                              case false => createSubAccount(parentId, bu, account, Some(user.password), user.modules)
                            }
                          } else {
                            logger.info(s"Can create a sub-account for sub-account in v1 - $parentId")
                            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                          }
                        } else {
                          logger.info(s"Permissions missing to create sub-account with user")
                          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                        }
                      } else {
                        logger.info(s"Parent account should be v1 - $parentId")
                        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                      }

                    case _ =>
                      logger.info(s"Parent Account not found - $parentId")
                      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                  }
                }
            }
          case Right(false) => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy)))
          case Left(x) => Future.successful(Left(x))
        }
    } recover {
      case e: Throwable =>
        logger.info("Sub account creation failed", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def createSubAccountV2(parentId : Long, user: SubAccountFormV2, isActive: Boolean = false) : Future[Either[ErrorResponse, Boolean]] = {
    PasswordConstraintCheck.validatePasswordConstraint(user.email, user.firstname, user.lastname, user.password) match {
      case Right(true) =>
        daoAccount.doesAccountNameExist(user.companyname) flatMap {
          case true =>
            logger.info(ExceptionCodes.AccountAlreadyExists.description)
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
          case false =>
            daoAccount.fetchAccountInfoAndPermission(parentId).flatMap { info =>
              info.headOption match {
                case Some((pa, accountPermissions)) =>
                  val manageAccountsV2 = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id))
                  val isValidPermissions = user.modules.getOrElse(Set.empty[Int]).subsetOf(accountPermissions.map(_.permission).toSet)
                  val canCreateSubAccounts = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ALLOW_SUBACCOUNTS.id))
                  val canCreateUser = accountPermissions.exists(_.permission.equals(BusinessUserRoles.SUB_ACCOUNTS_CAN_CREATE_USER.id))
                  if(!isValidPermissions) {
                    logger.info(ExceptionCodes.InvalidPermissions.description)
                    Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
                  } else if(manageAccountsV2 && canCreateSubAccounts && canCreateUser) {
                    daoAccountV2.getAccountHierarchyByAccountId(parentId).flatMap {
                      case Some(dtoAccountHierarchy) =>
                        createSubAccountV2(dtoAccountHierarchy, user, isActive)
                      case _ => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountNotFound)))
                    }
                  } else {
                    logger.info(s"Permissions missing to create sub-account with user")
                    Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                  }
                case _ =>
                  logger.info(s"Parent Account not found - $parentId")
                  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.ParentAccountNotFound)))
              }
            }
        }
      case Right(false) => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy)))
      case Left(x) => Future.successful(Left(x))
    }
  }

  private def generateEncryptionKeys(accountId: Long) = {
    val encryptionKeysFuture = encryptionKeysService.generate(AccountId(accountId))
    encryptionKeysFuture.onFailure {
      case NonFatal(e) => logger.info("Unable to generate encryption keys", e)
    }
    encryptionKeysFuture
  }

  private def copyParentModels(parentAccountId: Long, accountId: Long): Future[Boolean] = {
    modelManagementClient.copyAccountModels(parentAccountId.toString, accountId.toString).map{
      case Right(status) if status =>
        logger.info(s"Copied Parent Account $parentAccountId Models to Sub account $accountId")
        true
      case Right(_) =>
        logger.info(s"Unable to copy Parent Account $parentAccountId Models to Sub account $accountId")
        false
      case Left(e) =>
        logger.info(s"Unable to copy Parent Account $parentAccountId Models to Sub account $accountId", e)
        false
    }
  }

  def propagateUsersToSubAccount(parentAccountID: Long, creator: Option[Creator], primaryUserID: Long, subAccountID: Long) : Future[Either[ErrorResponse, Boolean]] = {
    dashboardUserService2.listAllUsers(parentAccountID, creator, false) flatMap {
      case Left(errorResponse) => Future.successful(Left(errorResponse))
      case Right(dashboardUsersList: Vector[DashboardUserV2]) =>
        if (dashboardUsersList.nonEmpty) {
          val usersListWithoutPrimaryUser = dashboardUsersList.filter( user => !user.id.equals(primaryUserID))
          val subAccountUsersToPropagate = usersListWithoutPrimaryUser.flatMap(user => {
            user.accountsWithRoles match {
              case Some(accountRoles) =>
               val parentAccountRoleOpt = accountRoles.find(accountRole => accountRole.accountDetails.id.equals(parentAccountID))
                parentAccountRoleOpt  match {
                  case Some(parentAccountRole) =>
                    val subAccountRoles =  parentAccountRole.roles.filter(roles => roles.roleType != SystemDefinedRoles.ACCOUNTOWNER.roleType).map(roles => {
                      RolesInputDetails(roles.roleType, roles.id)
                    })
                    if(subAccountRoles.nonEmpty){
                      Some(UserWithAccountRoles(user.id, subAccountRoles))
                    } else {
                      None
                    }
                  case None => None
                }
              case None => None
            }
            })
          if (subAccountUsersToPropagate.nonEmpty) {
            daoAccountV2.createSubAccountUsersOnCreation(
              accountId = subAccountID,
              userList = subAccountUsersToPropagate
            ).map(_ => {
              Right(true)
            })
          } else {
            Future.successful(Right(true))
          }
        }
        else {
          Future.successful(Right(true))
        }
    }
  }

  def createSubAcountWithMinDetails(parentId: Long, subAccount: SubAccount, userId: Option[Long], accountId: Option[Long], primaryUserIdOpt: Option[Long]): Future[Either[ErrorResponse, (DtoAccount, Long)]] = {
    daoAccount.fetchAccountInfoAndPermission(parentId).flatMap { info =>
      info.headOption match {
        case Some((pa, accountPermissions)) =>
          val manageAccountsV2 = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id))
          val isValidPermissions = subAccount.modules.getOrElse(Set.empty[Int]).subsetOf(accountPermissions.map(_.permission).toSet)
          val canCreateSubAccounts = accountPermissions.exists(_.permission.equals(BusinessUserRoles.ALLOW_SUBACCOUNTS.id))
          if (manageAccountsV2) {
            val primaryUserId = primaryUserIdOpt.getOrElse(throw new Exception("primary_user_id not found"))
            if (!isValidPermissions) {
              logger.info(ExceptionCodes.InvalidPermissions.description)
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
            } else if (!canCreateSubAccounts) {
              logger.info(s"Permissions missing to create sub-account")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
            } else {
              (userId, accountId) match {
                case (Some(u), Some(a)) => for {
                  _ <- v2Validator.validateAccountAccess(parentId, a)
                  _ <- v2Validator.validatePermissions(a, u, Set(DashboardUserPermissions.ACCOUNTS_CREATE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
                  _ <- v2Validator.isValidUser(a, primaryUserIdOpt)
                  res <- createSubAccountV2(pa, subAccount, accountPermissions.map(_.permission).toSet, primaryUserId, Creator(u, a), existingRoles = false)
                  finalResult <- res match {
                    case Right(subAccountID: Long) =>
                      subAccount.propogateUsers match {
                        case Some(createSubAccountUsers) =>
                          if (createSubAccountUsers) {
                            propagateUsersToSubAccount(pa.accountId, Option(Creator(u, a)), primaryUserId, subAccountID).map(_ => Right((pa, subAccountID)))
                          } else {
                            Future.successful(Right((pa, subAccountID)))
                          }
                        case None => Future.successful(Right((pa, subAccountID)))
                      }
                    case Left(errorResponse) => Future.successful(Left(errorResponse))
                  }
                } yield finalResult
                case _ =>
                  logger.info("Creator details not provided in the v2 request")
                  Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CreatorDetailsNotAvailable)))
              }
            }
          } else {
            if (pa.parentId.getOrElse(0) == 0) {
              if (canCreateSubAccounts) {
                createSubAccount(pa, subAccount).map {
                  case Right(subAccountId: Long) => Right((pa, subAccountId))
                  case Left(error) => Left(error)
                }
              } else {
                logger.info(s"Permissions missing to create sub-account")
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
              }
            } else {
              logger.info(s"Not a valid Parent Account - $parentId")
              Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.ParentAccountNotFound)))
            }
          }

        case _ =>
          logger.info(s"Parent Account not found - $parentId")
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.ParentAccountNotFound)))
      }
    }
  }

  private def createSubAccount(pa: DtoAccount, subAccount: SubAccount): Future[Either[ErrorResponse, Long]] = {
    daoAccount.doesAccountNameExist(subAccount.companyname) flatMap {
      case false =>
        val account = AccountConvertors.getAccount(pa.accountId, pa.isInternal, subAccount, pbeEncryptor)
        for {
          savedAccount <- daoAccount.saveAccount(account)
          savedEnvironment <- addSubAccountEnvironments(pa, savedAccount, clock)
          apiKeys = savedEnvironment.map(e => {
            AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)
          })
          apikeyStatus <- daoEnvironment.saveApiKeys(apiKeys, clock).map(_ == 1)
          _ <- {
            val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
            daoPublicApiKey.saveOrUpdate(publicApiKeys).map(_.getOrElse(0) != 0)
          }
          encryptionKeys <- generateEncryptionKeys(savedAccount.accountId)
          _ <- copyParentModels(pa.accountId, savedAccount.accountId)
        } yield {
          apikeyStatus match {
            case true =>
              customerSuccessMailId.map { id =>
                if (!pa.isInternal) mailNotificationService.sendSubAccountCreatedNotification(pa.accountId, pa.name, savedAccount.accountId, savedAccount.name, id)
              }
              Right(savedAccount.accountId) // Returning savedAccountId instead of boolean
            case _ =>
              Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
          }
        }

      case _ =>
        logger.info(ExceptionCodes.AccountAlreadyExists.description)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)))
    }
  }

  private def createSubAccountV2(pa: DtoAccount, subAccount: SubAccount, parentAccountPermissions: Set[Int], primaryUserId: Long, creator: Creator, existingRoles: Boolean): Future[Either[ErrorResponse, Long]] = {
    daoAccount.doesAccountNameExist(subAccount.companyname) flatMap {
      case false =>
        val account = AccountConvertors.getAccount(pa.accountId, pa.isInternal, subAccount, pbeEncryptor)
        val featureFlags = V2Helper.getFeatureFlags(subAccount.modules, analyticsConfig.isHistoricDataImported)
        val accountPermissions = parentAccountPermissions intersect featureFlags ++ subAccount.modules.getOrElse(Set.empty)
        for {
          savedAccount <- daoAccount.saveAccount(account)
          savedEnvironment <- addSubAccountEnvironments(pa, savedAccount, clock)
          apiKeys = savedEnvironment.map(e => {
            AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)
          })
          apikeyStatus <- daoEnvironment.saveApiKeys(apiKeys, clock).map(_ == 1)
          _ <- {
            val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
            daoPublicApiKey.saveOrUpdate(publicApiKeys).map(_.getOrElse(0) != 0)
          }
          ah <- daoAccountV2.getAccountHierarchyByAccountId(pa.accountId)
          _ <- {
            val parentAccountHierarchy = ah.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(ParentAccountNotFound)))
            val hierarchyPathPrefix = parentAccountHierarchy.hierarchyPath
            val permissions = AccountManagementDefaults.userPermissions(AccountTypes.SUB_ACCOUNT.id)
            val dtoAccountHierarchy = DtoAccountHierarchy(0, savedAccount.accountId, s"$hierarchyPathPrefix${savedAccount.accountId}/", AccountTypes.SUB_ACCOUNT.id, Status.ACTIVE.id, subAccount.administer.getOrElse(false), parentAccountHierarchy.numberOfPrimaryAdmins)
            if (existingRoles) {
              daoAccountV2.addSubAccountAdditionalDetails(
                dtoAccountHierarchy = dtoAccountHierarchy,
                accountId = savedAccount.accountId,
                accountPermissions = accountPermissions,
                primaryUserId = primaryUserId,
                userPermissions = permissions,
                creator = creator,
                clock = clock
              )
            } else {
              daoAccountV2.addSubAccountAdditionalDetailsWithNewRole(
                dtoAccountHierarchy = dtoAccountHierarchy,
                accountId = savedAccount.accountId,
                accountPermissions = accountPermissions,
                primaryUserId = primaryUserId,
                userPermissions = permissions,
                creator = creator,
                clock = clock
              )
            }
          }
          _ <- generateEncryptionKeys(savedAccount.accountId)
          _ <- copyParentModels(pa.accountId, savedAccount.accountId)
          user <- daoBusinessUser.getUser(creator.userId)
          rootAccountHierarchy <- {
            val rootAccountId = ah.get.hierarchyPath.split("/").map(_.toLong).head
            daoAccountV2.getAccountHierarchyByAccountId(rootAccountId)
          }
          _ <- {
            val adminDashboard = SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
              RateLimiterPublicAPI.ADMIN_DASHBOARD.publicId, RateLimitingWindowSizes.SECOND.value, RateLimiterPublicAPI.ADMIN_DASHBOARD.getDefaultRateLimit.getOrElse(DefaultRateLimit),
              if (user.isDefined) user.get.email else RateLimitingEntryCreators.SERVICE.value)

            val rateLimitingCollections = if (rootAccountHierarchy.isDefined && rootAccountHierarchy.get.accountType == AccountTypes.DIRECT_CUSTOMER.id) {

              val emailAuthScoreApiProd = SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
                RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId, RateLimitingWindowSizes.SECOND.value, 0, if (user.isDefined) user.get.email else RateLimitingEntryCreators.SERVICE.value)
              val emailAuthScoreApiCert = SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id,
                RateLimiterPublicAPI.EMAIL_AUTH_SCORE.publicId, RateLimitingWindowSizes.SECOND.value, 0, if (user.isDefined) user.get.email else RateLimitingEntryCreators.SERVICE.value)

              Seq(emailAuthScoreApiProd, emailAuthScoreApiCert, adminDashboard)

            } else {
              Seq(adminDashboard)
            }
            rateLimitingService.saveRateLimitCollection(rateLimitingCollections)
          }
          _ <- daoAccountUIConfiguration.saveUIAccountConfiguration(DtoAccountUIConfiguration(0, savedAccount.accountId, defaultAutoTimeoutInMinutes.toShort, defaultIdleTimeoutInMinutes.toShort, DateTime.now(), DateTime.now()))
          _ <- if (accountPermissions.contains(BusinessUserRoles.CUSTOMER_FILE_UPLOAD.id)) {
            accountPgpKeysService.createPgpKeys(savedAccount.accountId, Some(pgpKeyExpiryDuration)) map (_ => true) // this function add pgp keys if active pgp key doesn't exist for the account
          } else {
            Future.successful(true)
          }
        } yield {
          apikeyStatus match {
            case true =>
              customerSuccessMailId.map { id =>
                if (!pa.isInternal) mailNotificationService.sendSubAccountCreatedNotification(pa.accountId, pa.name, savedAccount.accountId, savedAccount.name, id)
              }
              Right(savedAccount.accountId)
            case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
          }
        }
      case _ =>
        logger.info(ExceptionCodes.AccountAlreadyExists.description)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountAlreadyExists)))
    }
  }

  def addSubAccountEnvironments(parentAccount: DtoAccount, subAccount: DtoAccount, clock: Clock): Future[Seq[Long]] = {

    daoAccount.fetchDomainsByAccountId(parentAccount.accountId).flatMap { parentAccountEnvs =>
      val parentAccountEnvsAsMap: Map[Long, DtoEnvironment] = parentAccountEnvs.map { env =>
        env.environmentType -> env
      }.toMap
      val subEnvironments: Seq[DtoEnvironment] = AccountConvertors.getDtoEnvironment(subAccount.accountId, clock).map { subAccountEnv =>
        parentAccountEnvsAsMap.get(subAccountEnv.environmentType) match {
          case Some(parentAccountEnv) =>
            subAccountEnv.copy(domain = parentAccountEnv.domain)
          case None => subAccountEnv
        }
      }
      daoBusinessUser.saveEnvironmentReturnIds(subEnvironments)
    }
  }

  private def createSubAccount(parentId : Long,
                               bu: DtoBusinessUser,
                               account: DtoAccount,
                               password:Option[String]=None,
                               modules: Option[Set[Int]]=None) : Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.saveAccount(account).flatMap(savedAccount => {
      daoBusinessUser.saveEnvironmentReturnIds(AccountConvertors.getDtoEnvironment(savedAccount.accountId, clock)) flatMap( savedEnvironment =>{
        val apiKeys = savedEnvironment.map( e => {AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)})
        //saveApiKeys - inserts keys to tbl_api_key and updates time in tbl_environment, transactionally, and so, the response will be 1 if updated
        val apikeyStatus = daoEnvironment.saveApiKeys(apiKeys, clock).map(count => {
          count==1
        })

        val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
        val publicApiKeysResult: Future[Boolean] = daoPublicApiKey.saveOrUpdate(publicApiKeys).map(_ != 0)

        val encryptionKeysFuture = encryptionKeysService.generate(AccountId(savedAccount.accountId))
        encryptionKeysFuture.onFailure {
          case NonFatal(e) => logger.info("Unable to generate encryption keys", e)
        }
        val associateParentModelsFuture = copyParentModels(parentId, savedAccount.accountId)
        val adminDashboardRateLimit = rateLimitingService.saveRateLimits(SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
          RateLimiterPublicAPI.ADMIN_DASHBOARD.publicId, RateLimitingWindowSizes.SECOND.value, RateLimiterPublicAPI.ADMIN_DASHBOARD.getDefaultRateLimit.getOrElse(DefaultRateLimit), bu.email))

        adminDashboardRateLimit.onFailure {
          case e: Throwable => logger.info(s"Unable to generate admin dashboard rate limits for account ${savedAccount.accountId}", e)
        }

        val daoUIAccountConfigurationFuture = daoAccountUIConfiguration.saveUIAccountConfiguration(DtoAccountUIConfiguration(0, savedAccount.accountId, defaultAutoTimeoutInMinutes.toShort, defaultIdleTimeoutInMinutes.toShort, DateTime.now(), DateTime.now()))

        daoUIAccountConfigurationFuture.onFailure {
          case e: Throwable => logger.info(s"Unable to save default UI Account Configuration for account ${savedAccount.accountId}", e)
        }

        val userStatus = daoBusinessUser.saveUser(bu.copy(accountId = savedAccount.accountId)) flatMap { savedUser =>
          val futureUpdatePassword = password.filter(_.trim.nonEmpty).
            map(passwordService.createPassword(savedUser.id, _)).
            getOrElse(Future.successful(true))

          val futureRole = savedEnvironment.map(envId=> daoBusinessUser.addEnvironmentUserRoles(envId = Some(envId), userId = savedUser.id, roles = Set(DashboardUserRole.LIST_TRANSACTION.id)))
          Future.sequence(Seq(futureUpdatePassword,  Future.sequence(futureRole)))
        }
        val futureAccountPermissions = daoAccountV2.addAccountPermissions(savedAccount.accountId, modules.getOrElse(Set.empty))
        Future.sequence(Seq(apikeyStatus, userStatus, encryptionKeysFuture, associateParentModelsFuture, publicApiKeysResult, futureAccountPermissions)).map(allstatus => Right(!allstatus.contains(false)))          })
    }) recover {
      case e: Throwable =>
        logger.info("Sub Account creation failed", e)
        Left(ErrorResponse(RegistrationFailed.id, UnknownError.description))
    }
  }

  def createSubAccountV2(parent : DtoAccountHierarchy, user: SubAccountFormV2, isActive: Boolean) : Future[Either[ErrorResponse, Boolean]] = {
    businessUserCommonService.checkNApplyUserEmailAvailability(user.email).flatMap {
      case false => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
      case true =>
        val bu = BusinessUserConvertors.getDtoBusinessUser(user)
        val account = AccountConvertors.getSubAccountV2(user, pbeEncryptor, isActive)
        daoAccount.saveAccount(account).flatMap(savedAccount => {
          daoBusinessUser.saveEnvironmentReturnIds(AccountConvertors.getDtoEnvironment(savedAccount.accountId, clock)) flatMap( savedEnvironment =>{
            val apiKeys = savedEnvironment.map( e => {AccountConvertors.getDtoApiKey(e, ApiKeyStatus.ACTIVE, clock)})
            //saveApiKeys - inserts keys to tbl_api_key and updates time in tbl_environment, transactionally, and so, the response will be 1 if updated
            val apikeyStatus = daoEnvironment.saveApiKeys(apiKeys, clock).map(count => {
              count==1
            })

            val encryptionKeysFuture = encryptionKeysService.generate(AccountId(savedAccount.accountId))
            encryptionKeysFuture.onFailure {
              case NonFatal(e) => logger.info("Unable to generate encryption keys", e)
            }

            val publicApiKeys: Seq[DtoPublicApiKey] = savedEnvironment.map(AccountConvertors.getPublicDtoApiKey(_, ApiKeyStatus.ACTIVE, clock))
            val publicApiKeysResult: Future[Boolean] = daoPublicApiKey.saveOrUpdate(publicApiKeys).map(!_.contains(0))
            val accountHirearchy = AccountHierarchyInput(id = None,
              accountId = savedAccount.accountId,
              hierarchyPath = s"${parent.hierarchyPath}${savedAccount.accountId}/",
              accountType = AccountTypes.SUB_ACCOUNT.id,
              hierarchyStatus = Status.ACTIVE.id,
              administer = user.administer.getOrElse(true),
              numberOfPrimaryAdmins = user.numberOfPrimaryAdmins)

            val defaultPermissions = AccountManagementDefaults.userPermissions(AccountTypes.SUB_ACCOUNT.id).map{ dp =>
              (dp._1, dp._2.mkString(","))
            }

            val userResult = daoBusinessUser.saveUser(bu.copy(accountId = savedAccount.accountId)) flatMap { savedUser =>
              val futureUpdatePassword = passwordService.createPassword(savedUser.id, user.password)
              val futureRole = daoBusinessUser.addEnvironmentUserRoles(userId = savedUser.id, roles = Set(BusinessUserRoles.ADMIN.id))
              val futureAccountAdditionalDetails = daoAccountV2.addAccountAdditionalDetails(savedAccount,
                savedUser.id,
                AccountConvertors.toDtoAccountHierarchy(accountHirearchy),
                defaultPermissions.toSet,
                isDashboardV3 = false,
                SystemDefinedRoles.ACCOUNTOWNER.roleType,
                clock)
              Future.sequence(Seq(futureRole, futureUpdatePassword, futureAccountAdditionalDetails)).map(_ => true)
            }
            val associateParentModelsFuture = copyParentModels(parent.accountId, savedAccount.accountId)
            val adminDashboardRateLimit = rateLimitingService.saveRateLimits(SaveRateLimitingInput(savedAccount.accountId, EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
              RateLimiterPublicAPI.ADMIN_DASHBOARD.publicId, RateLimitingWindowSizes.SECOND.value, RateLimiterPublicAPI.ADMIN_DASHBOARD.getDefaultRateLimit.getOrElse(DefaultRateLimit), bu.email))

            adminDashboardRateLimit.onFailure {
              case e: Throwable => logger.info(s"Unable to generate admin dashboard rate limits for account ${savedAccount.accountId}", e)
            }

            val daoUIAccountConfigurationFuture = daoAccountUIConfiguration.saveUIAccountConfiguration(DtoAccountUIConfiguration(0, savedAccount.accountId, defaultAutoTimeoutInMinutes.toShort, defaultIdleTimeoutInMinutes.toShort, DateTime.now(), DateTime.now()))

            daoUIAccountConfigurationFuture.onFailure {
              case e: Throwable => logger.info(s"Unable to save default UI Account Configuration for account ${savedAccount.accountId}", e)
            }

            val futureAccountPermissions = daoAccountV2.addAccountPermissions(savedAccount.accountId, user.modules.getOrElse(Set.empty))
            Future.sequence(Seq(apikeyStatus, userResult, encryptionKeysFuture, associateParentModelsFuture, publicApiKeysResult, futureAccountPermissions, adminDashboardRateLimit)).map(a => {
              Right(!a.contains(false))
            })
          })
        })
    } recover {
      case e: Throwable =>
        logger.info("Registration failed", e)
        Left(ErrorResponse(RegistrationFailed.id, RegistrationFailed.description))
    }
  }

  private def isBusinessUserInfoDefined(values: Option[String]*): Boolean = {
    values.exists(value => value.isDefined)
  }

  //TODO: For matching Account update details except primary email. and updare password only if it's not empty
  def updateSubAccount(email : String, user: UserForm) : Future[Either[ErrorResponse, Boolean]] = {

    daoBusinessUser.updateUserInformation(email, BusinessUserConvertors.getDeleagedUserFromUserForm(user)) map {
      case rows if rows > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(SubAccountNotFound))
    }
  }

  def toggleAccountActivateStatus(accountId : Long, isActive : Boolean, creator: Option[Creator]) : Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2AccountRequest(accountId, creator) flatMap {
      case true =>
        toggleAccountStatusV2(accountId, isActive, creator.getOrElse(throw new Exception("Invalid Creator details"))) flatMap {
          case Right(_) =>
            toogleAccountStatusV1(accountId, isActive)
          case Left(err) =>
            Future.successful(Left(err))
        }
      case false => toogleAccountStatusV1(accountId, isActive)
    }  recover {
      case ex: ErrorResponseException =>
        logger.info("Could not change activation status", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not change activation status", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def toogleAccountStatusV1(accountId: Long, status: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.toggleAccountActiveStatus(accountId, status) map {
      case rows if rows > 0 => Right(true)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  private def toggleAccountStatusV2(accountId: Long, status: Boolean, creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    val status0 = if(status) Status.ACTIVE.id else Status.INACTIVE.id
    for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId, ignoreAdministerFlag = true)
      res <- if(!status) {
        daoAccountV2.getActiveSubAccounts(accountId) flatMap {
          case l if l.isEmpty =>
            daoAccountV2.updateAccountHierarchyStatus(accountId, status0, clock) map {
              case rows if rows > 0 => Right(true)
              case _ =>
                logger.info("Account status cannot be changed")
                Left(ErrorResponseFactory.get(UpdateAccountStatusFailed))
            }
          case x =>
            logger.info("The account has active sub accounts, the status cannot be changed")
            Future.successful(Left(ErrorResponseFactory.get(UpdateAccountStatusFailedHasActiveSubAccounts)))
        }
      }else{
        daoAccountV2.updateAccountHierarchyStatus(accountId, status0, clock) map {
          case rows if rows > 0 => Right(true)
          case _ =>
            logger.info("Update Account status failed")
            Left(ErrorResponseFactory.get(UpdateAccountStatusFailed))
        }
      }
    } yield res
  }

  def getAllAccounts(accountId : Long) : Future[Either[ErrorResponse, Vector[AccountIdName]]] = {
    daoAccount.getAllAccountsDetails(accountId) map {
      case list if list.nonEmpty => Right(list)
      case _ =>  Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getAccountsByName(accountNames: String, parentAccountId : Long): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    val parsedAccountNames = Try {
      Some(accountNames.split(",").map(_.trim).toSeq)
    } match {
      case Success(Some(value)) => value
      case _ =>
        Seq.empty[String]
    }
    daoAccountV2.getAccountsByName(parsedAccountNames, parentAccountId) map {
      case accounts => Right(accounts)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAssociatedAccounts(userId : Long): Future[Either[ErrorResponse,Seq[AccountIdName]]]= {
    getAssociatedAccountsSeq(userId) map  {
      case accounts if accounts.nonEmpty => Right(accounts)
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAssociatedAccountsSeq(userId : Long): Future[Seq[AccountIdName]] = {
    val accountsFuture = daoAccountV2.getAssociatedAccounts(userId)
    for {
      accounts <- accountsFuture
      accountOwnerAccessAccounts <- v2Validator.getAccountOwnerAssociatedAccounts(userId, accounts)
    } yield accounts ++ accountOwnerAccessAccounts
  }


  def deprecateApiKeys() : Future[Either[ErrorResponse, String]] = {
    daoEnvironment.deprecateApiKeys(clock, apiKeyRenewalConfig.drepcateDurationInHours) map {
      case list if list.count(_>0)>0 =>
        Right("Api keys status updated")
      case list if list.count(_>0)==0   =>
        Right("No Api keys updated")
      case _ =>   Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getAccountInfoForEnvironment(environmentId: Long) : Future[Either[ErrorResponse, AccountInfo]]  = {
    daoAccount.getAccountInfoForEnvironment(environmentId = environmentId) map {
      case Some(a:DtoAccount) => Right(AccountInfo(id = a.accountId, name = a.name, parentId = a.parentId))
      case _ =>   Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def validateAccessPermission(apiKey: String, creator: Creator, permissions: String, status: Option[String] = None): Future[Either[ErrorResponse, Boolean]] = {
    Try{permissions.split(",").map(_.trim.toInt).toSet} match {
      case Success(newPermissions) =>
        val statusSet = status match {
          case Some(status) if !status.isEmpty => status.split(",").map(_.trim.toInt).toSet.flatMap(ApiKeyStatus.byId)
          case None => Set(ApiKeyStatus.ACTIVE, ApiKeyStatus.NEW)
        }
        v2Validator.isValidV2ApiKeyRequest(apiKey, Some(creator), newPermissions, statusSet) map {
          case true => Right(true)
          case false =>
            logger.info("Validate access permission with apikey failed")
            Left(ErrorResponseFactory.get(InvalidPermissions))
        } recover {
          case ex: ErrorResponseException =>
            logger.info("Validate access permission with apikey failed", ex)
            Left(ex.errorResponse)
          case e: Exception =>
            logger.info(s"Error occurred while validating access permission using apikey", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
        }
      case Failure(exception) =>
        logger.info(s"Invalid permissions: $permissions", exception)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
    }
  }

  def validateAccessPermission(environmentType: Int, creator: Creator, permissions: String): Future[Either[ErrorResponse, Boolean]] = {
    Try{permissions.split(",").map(_.trim.toInt).toSet} match {
      case Success(newPermissions) =>
        v2Validator.isValidV2EnvironmentRequest(environmentType, creator, newPermissions) map {
          case true => Right(true)
          case false =>
            logger.info("Validate access permission environment type failed")
            Left(ErrorResponseFactory.get(InvalidPermissions))
        } recover {
          case ex: ErrorResponseException =>
            logger.info("Validate access permission with environment type failed", ex)
            Left(ex.errorResponse)
          case e: Exception =>
            logger.info(s"Error occurred while validating access permission using apikey", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
        }
      case Failure(exception) =>
        logger.info(s"Invalid permissions: $permissions", exception)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
    }
  }

  def validateAccessWithOrPermission(environmentType: Int, creator: Creator, permissions: String, orPermissions: Option[String]): Future[Either[ErrorResponse, Boolean]] = {
    Try{permissions.split(",").map(_.trim.toInt).toSet} match {
      case Success(newPermissions) =>
        val orPermissionsSet = orPermissions match {
          case Some(orPermStr) => orPermStr.split(",").map(_.trim.toInt).toSet
          case None => Set.empty[Int]
        }
        v2Validator.isValidV2EnvironmentRequestOrPermissions(environmentType, creator, newPermissions, orPermissionsSet) map {
          case true => Right(true)
          case false =>
            logger.info("Validate access permission environment type failed")
            Left(ErrorResponseFactory.get(InvalidPermissions))
        } recover {
          case ex: ErrorResponseException =>
            logger.info("Validate access permission with environment type failed", ex)
            Left(ex.errorResponse)
          case e: Exception =>
            logger.info(s"Error occurred while validating access permission using apikey", e)
            Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
        }
      case Failure(exception) =>
        logger.info(s"Invalid permissions: $permissions", exception)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPermissions)))
    }
  }

  def fetchAccountPermissions(accountId: Long): Future[Either[ErrorResponse, Seq[DtoAccountPermission]]] ={
    daoAccount.fetchAccountInfoAndPermission(accountId).map { info =>
      info.headOption match {
        case Some((_, accountPermissions)) => Right(accountPermissions)
        case _ => Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  def getAccountHierarchy(accountId: Long): Future[Either[ErrorResponse, DtoAccountHierarchy]] ={
    daoAccountV2.getAccountHierarchyByAccountId(accountId).map { info =>
      info.headOption match {
        case Some(accountHierachy) => Right(accountHierachy)
        case _ => Left(ErrorResponseFactory.get(UnknownError))
      }
    }
  }

  def getApiKeysForSubAccountsV1(parentId: Long) : Future[Either[ErrorResponse, List[SubAccountApiKeys]]] = {
    daoAccount.getApiKeysForSubAccounts(parentId) map {
      case (list:(Seq[(DtoApiKey, String, DtoEnvironment)])) if list.nonEmpty =>
        Right(getSubAccountApiKeys(list))
      case list if list.isEmpty => Right(List.empty[SubAccountApiKeys])
      case _ =>  Left(ErrorResponseFactory.get(SubAccountNotFound))
    }
  }

  private def getApiKeysForSubAccountsV2(accountId: Long, root: DtoAccountHierarchy, uaaOpt: Option[DtoUserAccountAssociation]): Future[Either[ErrorResponse, List[SubAccountApiKeys]]] = {
    uaaOpt match {
      case Some(uaa) =>
        val apiKeysInfo = if(uaa.isPrimaryUser) daoAccountV2.getApiKeysForSubAccount(accountId)
        else daoAccountV2.getApiKeysForSubAccount(accountId, uaa.businessUserId)
        apiKeysInfo.flatMap { list =>
          root.accountType match {
            case t if AccountTypes.isTypeDirect(t) =>
              Future.successful(Right(getApiKeys(accountId, list, None)))
            case t if AccountTypes.isTypeChannelPartner(t) =>
              daoAccountV2.isAccountPermissionProvisioned(root.accountId, BusinessUserRoles.ADMINISTER_SUB_ACCOUNTS.id).map {
                case true =>
                  val firstLevelSubAccountInfo = list
                    .filter(_._4.hierarchyPath.count(_ == '/') == 2)
                    .map(f => (f._4.hierarchyPath, f._4.administer))
                  Right(getApiKeys(accountId, list, Some(firstLevelSubAccountInfo)))
                case false =>
                  Right(getApiKeys(accountId, list, Some(Seq.empty)))
              }

            case _ =>
              logger.info(s"Invalid account type accountId:${root.accountId}, accountType:${root.accountType}")
              Future.successful(Left(ErrorResponseFactory.get(InvalidAccountType)))
          }
        }
      case None =>
        logger.info(s"Invalid user account association, accountId:${root.accountId}, accountType:${root.accountType}")
        Future.successful(Left(ErrorResponseFactory.get(InvalidAccountType)))
    }
  }

  private def getApiKeys(accountId: Long, list: Seq[(DtoAccount, DtoEnvironment, DtoApiKey, DtoAccountHierarchy)], adminsterDetails: Option[Seq[(String, Boolean)]]): List[SubAccountApiKeys] = {
    if (list.nonEmpty) {
      list.find(_._4.accountId == accountId).map { sa =>
        val cnt = sa._4.hierarchyPath.count(_ == '/')
        list.filter (_._4.hierarchyPath.count(_ == '/') == cnt + 1)
      } match {
        case Some(firstLevelSubAccounts) =>
          val data = firstLevelSubAccounts.groupBy(_._1).flatMap { info =>
            adminsterDetails match {
              case Some(ad) =>
                info._2.flatMap { details =>
                  if ((details._4.accountType != AccountTypes.SUB_ACCOUNT.id) || details._4.accountId.equals(accountId))
                    Some(details._3, details._1.name, details._2)
                  else
                    ad.find(p => details._4.hierarchyPath.startsWith(p._1)) match {
                      case Some(v) if v._2 => Some(details._3, details._1.name, details._2)
                      case _ => None
                    }
                }
              case None => info._2.map { details => (details._3, details._1.name, details._2) }
            }
          }
          getSubAccountApiKeys(data.toSeq)
        case None => List.empty[SubAccountApiKeys]
      }
    }
    else List.empty[SubAccountApiKeys]
  }

  def getApiKeysForSubAccounts(accountId: Long, creatorOpt: Option[Creator]): Future[Either[ErrorResponse, List[SubAccountApiKeys]]] = {
    v2Validator.isValidV2AccountRequest(accountId, creatorOpt) flatMap {
      case true =>
        val creator = creatorOpt.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable)))
        for {
          root <- v2Validator.validateAccountAccess(accountId, creator.accountId)
          uaaOpt <- daoAccountV2.getUserAccountAssociation(creator.userId, creator.accountId)
          res <- getApiKeysForSubAccountsV2(accountId, root, uaaOpt)
        } yield res
      case false => getApiKeysForSubAccountsV1(accountId)
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not list apikeys for subaccounts account:$accountId, creator: $creatorOpt", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error occurred while fetching apikeys for subaccounts account:$accountId, creator: $creatorOpt", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyFetchFailed))
    }
  }

  def getParentsSubAccounts(accountId:Long): Future[Either[ErrorResponse, Set[Long]]] = {
    def getSubAccounts(accountId: Long): Future[Set[Long]] = {
      daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
        case true =>
          daoAccountV2.getSubAccounts(accountId).map(_.map(_.accountId)).map(_.toSet)
        case false =>
          daoAccount.getSubAccountIds(accountId)
      }
    }

    daoAccount.isActiveParentAccount(accountId) flatMap {
      case true =>
        getSubAccounts(accountId).map(Right.apply)
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidParentAccount)))
    }
  }

  def getAccountModules(accountId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, Set[String]]] = {
    (for {
      _ <- v2Validator.validateAccountAccess(accountId, creatorAccountId)
      res <- daoAccountV2.getAccountPermissions(accountId) map { permissions: Seq[DtoAccountPermission] =>
        Right(permissions.flatMap(permission => BusinessUserRoles.byId(permission.permission).map(_.publicId)).toSet)
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not list modules for $accountId, with creatorAccountId $creatorAccountId", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while listing the modules for $accountId, with creatorAccountId $creatorAccountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    }
  }

  def getDefaultModules(accountId: Long, creator: Creator): Future[Either[ErrorResponse, Set[Int]]] = {
    (for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
      res <- daoAccountV2.getDefaultModules(accountId) map { dtoDefaultModules =>
        val modules0 = dtoDefaultModules.map(_.modules.split(",").map(_.trim).map(_.toInt)).toSet.flatten
        val validModules = BusinessUserRoles.getPermissionsOfTheResource(Resources.IDPLUS, modules0)
        val diffModules = modules0 diff validModules
        if (diffModules.nonEmpty) {
          logger.info(s"Invalid default Modules:($diffModules), for $accountId")
        }
        Right(validModules)
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not list default modules for $accountId, with creator $creator", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while listing the default modules for $accountId, with creator $creator", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchDefaultModules))
    }
  }

  def getDefaultModulesForAccounts(accountId: Long, creator: Option[Creator],forceValidate: Boolean): Future[Either[ErrorResponse, ModulesResponse]] = {
    (for {
      _ <-  if (forceValidate) v2Validator.validateAccountAccess(accountId, creator.get.accountId) else Future.successful()
      res <- daoAccountV2.getDefaultModulesWithCount(accountId) map { moduleResult =>
        val modules0 = moduleResult._1.map(_.modules.split(",").map(_.trim).map(_.toInt)).toSet.flatten
        val byAccountId = moduleResult._1.flatMap(_.byAccountId)
        val isForceInherit = moduleResult._1.flatMap(_.isForceInherit)
        val validModules = BusinessUserRoles.getPermissionsOfTheResource(Resources.IDPLUS, modules0)
        val diffModules = modules0 diff validModules
        val inheritedToSubAccountsCount = moduleResult._2
        if(diffModules.nonEmpty) {
          logger.info(s"Invalid default Modules:($diffModules), for $accountId")
        }
        Right(ModulesResponse(validModules, byAccountId, isForceInherit, Some(inheritedToSubAccountsCount) ))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not list default modules for $accountId, with creator $creator", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while listing the default modules for $accountId, with creator $creator", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchDefaultModules))
    }
  }

  def formProductSettingsDeltaWithModules(accountId: Long, existingModules: Set[Int], modules: Set[Int]): Seq[ProductSettingDelta] = {
    def getEnabledOrDisabled(boolean: Boolean): String = {
      boolean match {
        case true => "Enabled"
        case false => "Disabled"
      }
    }

    def getStandardModuleName(module: String): String={
      module match {
        case s if s.contains("Prefill") => "Prefill"
        case s if s.contains("SYNTHETIC") => "Synthetic"
        case s if s.contains("WATCHLIST_3_0") => "Global Watchlist"
        case s if s.contains("Module Decisioning") => "Decision"
        case _ => module // Default case if no match is found
      }
    }


    val productSettingDelta: Seq[ProductSettingDelta] = {
      if (existingModules == modules) {
        Seq.empty
      } else {
        val deltaMap = (existingModules ++ modules).flatMap { id =>
          val existing = existingModules.contains(id)
          val modified = modules.contains(id)
          if (existing != modified) {
            BusinessUserRoles.byId(id).map { role =>
              (getStandardModuleName(role.label), (getEnabledOrDisabled(existing), getEnabledOrDisabled(modified)))
            }
          } else {
            None
          }
        }.toMap

        Seq(ProductSettingDelta(accountId, None, GENERAL, deltaMap))
      }
    }
    productSettingDelta
  }


  def formAuditDetailsWithProductSettingDelta(isSuccess: Boolean, creator: Creator, accountId: Long, errorResponse: Option[ErrorResponse], modules: Set[Int], existingModules: Set[Int]): Future[AuditDetails] = {
    val productSettings = formProductSettingsDeltaWithModules(accountId, existingModules, modules)
    auditDetailsService.formAuditDetails(isSuccess, Some(creator), None, errorResponse, productSettings)
  }

  def saveDefaultModules(accountId: Long, modules: Set[Int], creator: Creator): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {

    def saveModules(): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
      daoAccountV2.getDefaultModules(accountId).flatMap {
        case Some(existingModules) =>
          daoAccountV2.updateDefaultModules(accountId, modules.mkString(","), clock) flatMap {
            case i if i > 0 =>
              val existingModulesSet: Set[Int] = existingModules.modules.split(",").map(each => each.trim.toInt).toSet
              formAuditDetailsWithProductSettingDelta(isSuccess = true, creator, accountId, None, modules, existingModulesSet) map {
                auditDetails => (auditDetails, Right(true))
              }

            case _ =>
              val existingModulesSet: Set[Int] = existingModules.modules.split(",").map(each => each.toInt).toSet
              formAuditDetailsWithProductSettingDelta(isSuccess = false, creator, accountId, None, modules, existingModulesSet) map {
                auditDetails => (auditDetails, Right(false))
              }
          }

        case None => daoAccountV2.insertDefaultModules(DtoDefaultModules(0, accountId, modules.mkString(","), clock.now, clock.now, None, Some(false))) flatMap {
          case i if i > 0 =>
            formAuditDetailsWithProductSettingDelta(isSuccess = true, creator, accountId, None, modules, Set.empty) map {
              auditDetails => (auditDetails, Right(true))
            }
          case _ =>
            formAuditDetailsWithProductSettingDelta(isSuccess = false, creator, accountId, None, modules, Set.empty) map {
              auditDetails => (auditDetails, Right(false))
            }
        }
      }
    }
      (for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
      res <- {
        if(modules.isEmpty){
          daoAccountV2.getDefaultModules(accountId).flatMap {
            case Some(existingModules) => daoAccountV2.clearDefaultModules(Seq(accountId)).flatMap {
              case i if i > 0 =>
                val existingModulesSet: Set[Int] = existingModules.modules.split(",").map(each => each.toInt).toSet
                formAuditDetailsWithProductSettingDelta(isSuccess = true, creator, accountId, None, modules, existingModulesSet) map {
                  auditDetails => (auditDetails, Right(true))
                }

              case _ =>
                val existingModulesSet: Set[Int] = existingModules.modules.split(",").map(each => each.toInt).toSet
                formAuditDetailsWithProductSettingDelta(isSuccess = false, creator, accountId, None, modules, existingModulesSet) map {
                  auditDetails => (auditDetails, Right(false))
                }
            }
            case None =>
              daoAccountV2.clearDefaultModules(Seq(accountId)).flatMap {
                case i if i > 0 =>
                  formAuditDetailsWithProductSettingDelta(isSuccess = true, creator, accountId, None, modules, Set.empty) map {
                    auditDetails => (auditDetails, Right(true))
                  }
                case _ => formAuditDetailsWithProductSettingDelta(isSuccess = false, creator, accountId, None, modules, Set.empty) map {
                  auditDetails => (auditDetails, Right(false))
                }
              }
          }
        }else if(BusinessUserRoles.isValidRoles(Resources.IDPLUS, modules)) {
          if(accountId == creator.accountId) {
            saveModules
          }else {
            daoAccountV2.getAccountPermissions(creator.accountId).flatMap {
              case ap if ap.nonEmpty && modules.subsetOf(ap.map(_.permission).toSet) =>
                saveModules
              case _ =>
                throw ErrorResponseException(ErrorResponseFactory.get(InvalidModules))
            }
          }
        } else
          throw ErrorResponseException(ErrorResponseFactory.get(InvalidModules))
      }
      } yield res) recoverWith {
        case ex: ErrorResponseException =>
          logger.info(s"Could not save default modules for $accountId, with creator $creator, modules $modules", ex)
          val errorResponse = ex.errorResponse
          formAuditDetailsWithProductSettingDelta(false, creator, accountId, Some(errorResponse), modules, Set.empty) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case e: Exception =>
          logger.info(s"Error while saving the default modules for $accountId, with creator $creator, modules $modules", e)
          val errorResponse = ErrorResponseFactory.get(ExceptionCodes.UnableToFetchDefaultModules)
          formAuditDetailsWithProductSettingDelta(false, creator, accountId, Some(errorResponse), modules, Set.empty) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
      }
  }


  def formProductSettingsDeltaWithModulesForAccounts(accountIds: Seq[Long], currentDefaultModulesForAccounts: Seq[DtoDefaultModules], modules: Set[Int]): Seq[ProductSettingDelta] = {

    accountIds.map {
      accountId =>
        currentDefaultModulesForAccounts.find(modules => modules.accountId == accountId) match {
          case Some(defaultModules) =>
            val existingModulesSet: Set[Int] = defaultModules.modules.split(",").map(each => each.trim.toInt).toSet
            formProductSettingsDeltaWithModules(accountId, existingModulesSet, modules)
          case None =>
            formProductSettingsDeltaWithModules(accountId, Set.empty, modules)
        }


    }.flatten
  }

  def formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess: Boolean, creator: Creator, accountIds: Seq[Long], errorResponse: Option[ErrorResponse], modules: Set[Int], currentDefaultModulesForAccounts: Seq[DtoDefaultModules]): Future[AuditDetails] = {
    val productSettings: Seq[ProductSettingDelta] = formProductSettingsDeltaWithModulesForAccounts(accountIds, currentDefaultModulesForAccounts, modules)
    auditDetailsService.formActionUserInfo(Some(creator)) map {
      actionUserInfo => AuditDetails(isSuccess, actionUserInfo, None, errorResponse, productSettings)
    }
  }

  def saveDefaultModulesForAccounts(accountIds: Seq[Long], modules: Set[Int], isForceInherit: Boolean, creator: Creator): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    def saveModules(currentDefaultModulesForAccounts: Seq[DtoDefaultModules]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
      daoAccountV2.insertDefaultModulesForAccounts(DefaultAccountsModules(accountIds, modules.mkString(","), clock.now, clock.now, Some(creator.accountId), Some(isForceInherit))) flatMap {
        case Some(res) if res >= 0 =>
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = true, creator, accountIds, None, modules, currentDefaultModulesForAccounts) map {
            auditDetails => (auditDetails, Right(true))
          }
        case _ =>
          logger.info(s"Could not save modules for accounts, create $creator")
          val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
          formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, accountIds, Some(errorResponse), modules, currentDefaultModulesForAccounts) map {
            auditDetails => (auditDetails, Left(errorResponse))
          }

      }
    }
    (for {
      _ <- v2Validator.validateAccountAccess(accountIds, creator.accountId, creator.userId, None)
      currentDefaultModulesForAccounts <- daoAccountV2.getDefaultModulesForAccounts(accountIds)
      res <- {

        if (modules.isEmpty) {
          daoAccountV2.clearDefaultModules(accountIds).flatMap {
            case i if i > 0 =>
              formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = true, creator, accountIds, None, modules, currentDefaultModulesForAccounts) map {
                auditDetails => (auditDetails, Right(true))
              }
            case _ =>
              formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, accountIds, None, modules, currentDefaultModulesForAccounts) map {
                auditDetails => (auditDetails, Right(false))
              }
          }
        } else if (BusinessUserRoles.isValidRoles(Resources.IDPLUS, modules)) {
          if (accountIds == creator.accountId) {
            saveModules(currentDefaultModulesForAccounts)
          } else {
            daoAccountV2.getAccountPermissions(creator.accountId).flatMap { ap =>
              val basePermissions = ap.map(_.permission).toSet
              val additionalPermissions =
                if (basePermissions.contains(BusinessUserRoles.WATCHLIST_3_0.id)) {
                  Set(
                    BusinessUserRoles.WlStandardTier.id,
                    BusinessUserRoles.WlPlusTier.id,
                    BusinessUserRoles.WlPremierTier.id
                  )
                } else {
                  Set.empty[Int]
                }
              val updatedPermissionsSet = basePermissions ++ additionalPermissions
              ap match {
                case ap if ap.nonEmpty && modules.subsetOf(updatedPermissionsSet) =>
                  saveModules(currentDefaultModulesForAccounts)
                case _ =>
                  throw ErrorResponseException(ErrorResponseFactory.get(InvalidModules))
              }
            }
          }
        } else
          throw ErrorResponseException(ErrorResponseFactory.get(InvalidModules))
      }
    } yield res) recoverWith {
      case ex: ErrorResponseException =>
        logger.info(s"Could not save default modules for $accountIds, with creator $creator, modules $modules", ex)
        val errorResponse = ex.errorResponse
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, accountIds, Some(errorResponse), modules, Seq.empty) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }

      case e: Exception =>
        logger.info(s"Error while saving the default modules for $accountIds, with creator $creator, modules $modules", e)
        val errorResponse = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration)
        formAuditDetailsWithProductSettingDeltaForAccounts(isSuccess = false, creator, accountIds, Some(errorResponse), modules, Seq.empty) map {
          auditDetails => (auditDetails, Left(errorResponse))
        }

    }
  }

  def clearDefaultModules(accountId: Long, creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    (for {
      _ <- v2Validator.validateAccountAccess(accountId, creator.accountId)
      _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, Set(DashboardUserPermissions.ACCOUNTS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- daoAccountV2.clearDefaultModules(Seq(accountId)).map {
        case i if i > 0 => Right(true)
        case _ => Right(false)
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not clear default modules for $accountId, with creator $creator", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Error while removing the default modules for $accountId, with creator $creator", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToFetchDefaultModules))
    }
  }

  private def validateRequestAction(action: Int): Future[Unit] = Future(if(!RequestActions.contains(action)) throw ErrorResponseException(ErrorResponseFactory.get(InvalidRequestAction)))

  private def validateRequestSource(source: Int): Future[Unit] = Future(if(!RequestSources.contains(source)) throw ErrorResponseException(ErrorResponseFactory.get(InvalidRequestSource)))

  def getDtoApiAudit(apiAudit: ApiAudit): Future[DtoApiAudit] = {
    Future.successful(
      DtoApiAudit(
        id = 0L,
        accountId = apiAudit.accountId,
        source = apiAudit.source,
        action = apiAudit.action,
        created_by = apiAudit.created_by,
        created_at = apiAudit.created_at,
        request = auditRequestEncipher.encrypt(apiAudit.request),
        response = Base64.getEncoder.encodeToString(apiAudit.response.getBytes(StandardCharsets.UTF_8)),
        responseCode = apiAudit.responseCode,
        processingTime = apiAudit.processingTime
      )
    )
  }

  def saveApiAudit(apiAudit: ApiAudit): Future[Either[ErrorResponse, Boolean]] = {
    (for {
      _ <- validateRequestAction(apiAudit.action)
      _ <- validateRequestSource(apiAudit.source)
      dtoApiAudit <- getDtoApiAudit(apiAudit)
      res <- daoAccountV2.insertApiAudit(dtoApiAudit) map {
        case i if i > 0 => Right(true)
        case _ => Right(false)
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not save API audit. Account Id: ${apiAudit.accountId}, source: ${apiAudit.source}, action: ${apiAudit.action}", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info(s"Could not save API audit. Account Id: ${apiAudit.accountId}, source: ${apiAudit.source}, action: ${apiAudit.action}", e)
        Left(ErrorResponseFactory.get(CouldNotSaveApiAudit))
    }
  }
  def createSubAccountWithMinDetailsWithoutCreator(subAccountCreationRequest: SubAccountCreationRequest): Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.getAccountIdByApiKey(subAccountCreationRequest.apiKey) flatMap {
      case Some(parentId) =>
        daoAccount.fetchAccountInfoAndPermission(parentId).flatMap { info =>
          val (pa, accountPermissions) = info.head
          val subAccountModulesProvided = subAccountCreationRequest.modules match {
            case Some(modules) =>
              modules.flatMap(BusinessUserRoles.byPublicId).map(_.id)
            // if modules field is not present in the request, add all modules of parent to sub account
            case None => BusinessUserRoles.getPermissionsOfTheResource(Resources.IDPLUS, accountPermissions.map(_.permission).toSet)
          }
          val isValidPermissions = subAccountModulesProvided.subsetOf(accountPermissions.map(_.permission).toSet)
          if (!isValidPermissions) {
            logger.info(s"Requested Modules for the sub account $subAccountModulesProvided is not a subset of parent account ${accountPermissions.map(_.permission).toSet}")
            Future.successful(Left(ErrorResponseFactory.get(PublicExceptionCodes.ModulesIncorrect)))
          } else {
            val primaryUserIdsFuture = subAccountCreationRequest.user match { // user given in the request, check if exists, if not create one for the details provided
              case Some(user) =>
                daoBusinessUser.getIdByEmail(user.email) flatMap {
                  case Some(userId) => daoAccountV2.getUserAccountAssociation(userId, parentId) map {
                    case Some(_) => Right(Seq(userId))
                    case _ => Left(ErrorResponseFactory.get(PublicExceptionCodes.UserNotAssociatedToParentAccount)) // no association between given email and the parent account
                  }
                  case None =>
                    daoAccountV2.createBusinessUserWithActivationToken(user, parentId) flatMap { info => {
                      mailNotificationService.sendSetPasswordEmail(user.firstName.get, user.lastName.get, user.email, info._2, accountId = parentId) map {
                        case Right(sent) if sent => Right(Seq(info._1))
                        case _ =>
                          logger.info(s"Error while sending set password mail to $subAccountCreationRequest")
                          Right(Seq(info._1))
                      }
                    }
                    }
                }
              case None => // user not given in the request, get all active primary users of parent account and add to sub account
                daoAccountV2.getPrimaryUsersOfAnAccount(parentId) map { userIds => {
                  if (userIds.nonEmpty) {
                    Right(userIds)
                  } else {
                    Left(ErrorResponseFactory.get(PublicExceptionCodes.NoPrimaryUsersPresentForParentAccount)) // no primary users exist for parent account
                  }
                }
                }
            }
            primaryUserIdsFuture flatMap {
              case Right(primaryUsers: Seq[Long]) =>
                // create the sub account by using existing createSubAccountV2 API by passing one primary user we have in the list (primaryUsers.head)
                createSubAccountV2(pa, SubAccount(subAccountCreationRequest.accountName, subAccountCreationRequest.industry,
                  subAccountCreationRequest.administer, Some(subAccountModulesProvided)),
                  accountPermissions.map(_.permission).toSet, primaryUsers.head, Creator(0, parentId), existingRoles = false) flatMap {
                  case Right(accountId: Long) if primaryUsers.tail.nonEmpty =>
                    //                      adding other parent account primary users to the created sub account
                    val permissions = AccountManagementDefaults.userPermissions(AccountTypes.SUB_ACCOUNT.id)
                    val userAssociationFutures = primaryUsers.tail map (userId => daoAccountV2.addPrimaryUserAccountAssociationsWithNewRole(accountId, userId, permissions, Creator(0, parentId), clock))
                    Future.sequence(userAssociationFutures) map (results => Right(!results.contains(false)))
                  case Right(_) if primaryUsers.tail.isEmpty => Future.successful(Right(true)) // only 1 primary user is there for addition
                  case Left(errorResponse) => Future.successful(Left(errorResponse))
                }
              case Left(errorResponse) => Future.successful(Left(errorResponse))
            }
          }
        }
      case _ => logger.info(s"Parent Account not found - ${subAccountCreationRequest.apiKey}")
        Future.successful(Left(ErrorResponseFactory.get(PublicExceptionCodes.NonPrimaryAccountAPIKey)))
    }
  }

  def getApiKeysForAccountByEnvironmentType(accountId: Long, envTypeId: Long) : Future[Either[ErrorResponse, List[AccountApiKeys]]] = {
    daoAccount.getApiKeysForAccountByEnvironmentType(accountId, envTypeId) map {
      case (list:(Seq[(DtoApiKey, String, DtoEnvironment)])) if list.nonEmpty =>
        Right(getApiKeys(list))
      case list if list.isEmpty => Right(List.empty[AccountApiKeys])
      case _ =>  Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getSponsorBankIdsByProgramId(programId: Long): Future[Either[ErrorResponse, Long]] = {
    daoAccount.getSponsorBankIdsByProgramId(programId) map {
      case sponsorBankIds if sponsorBankIds.nonEmpty => Right(sponsorBankIds.head)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

  def getSponsorBankPrograms(sponsorBankId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    daoAccount.getProgramAccountIdName(sponsorBankId) map {
      case programIds if programIds.nonEmpty => Right(programIds)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
  }

}