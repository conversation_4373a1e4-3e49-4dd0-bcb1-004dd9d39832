package me.socure.account.dashboardv2

import me.socure.account.service.common.exceptions.ExceptionCodes.{IDMApiKeyCannotBeDeprecated, IDMApiKeyCannotBeUpdated, IDMApiKeyNotInserted}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.Clock
import me.socure.mapping.ApiKeyMapper
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.model.account.{ApiKeyInfo, ApiKeyResp, ApiKeyStatus, ApiKeys, IdmAccountInfoResponse, IdmApiKey}
import me.socure.model.dashboardv2.EnvironmentNameAndId
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment, DaoIDMApiKey}
import me.socure.storage.slick.tables.account.{DtoAccount, DtoAccountHierarchy, DtoApiKey, DtoIdm<PERSON><PERSON><PERSON><PERSON>, DtoPublicApi<PERSON><PERSON>}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class IDMService(
                  daoIDMApiKey: DaoIDMApiKey,
                  daoAccountV2: DaoAccountV2,
                  daoEnvironment: DaoEnvironment,
                  clock: Clock
                )(implicit ec : ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def fetchApiKey(accountId: Long): Future[Either[ErrorResponse, Option[ApiKeyInfo]]] = {
    daoIDMApiKey.fetchActiveKeyByAccountId(accountId).map {
      case Some(dtoIdmApiKey) => Right(Some(ApiKeyInfo(dtoIdmApiKey.id, dtoIdmApiKey.status, dtoIdmApiKey.apiKey, dtoIdmApiKey.label, dtoIdmApiKey.lastUsedAt)))
      case _ => Right(None)
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while fetching idm key for account $accountId", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.IDMKeyFetchFailed))
    }
  }

  def updateLabel(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    daoIDMApiKey.updateLabel(idmApiKey.apiKey, idmApiKey.accountId, idmApiKey.label).map {
      case affectedRows if affectedRows==1 => Right(true)
      case _ => Left(ErrorResponseFactory.get(IDMApiKeyCannotBeUpdated))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while updating idm key label for account ${idmApiKey.accountId}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyCannotBeUpdated))
    }
  }

  def deprecateApiKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    daoIDMApiKey.updateStatus(idmApiKey.apiKey, idmApiKey.accountId, ApiKeyStatus.DEPRECATED).map {
      case affectedRows if affectedRows==1 => Right(true)
      case _ => Left(ErrorResponseFactory.get(IDMApiKeyCannotBeDeprecated))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while deprecating idm key for account ${idmApiKey.accountId}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyCannotBeDeprecated))
    }
  }

  def generateApiKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    daoIDMApiKey.fetchActiveKeyByAccountId(idmApiKey.accountId) flatMap  {
      case Some(activeKey) => daoIDMApiKey.deprecateAndGenerateKey(activeKey.id, ApiKeyMapper.toDTO(idmApiKey, clock)).map {
        case affectedRows if affectedRows==1 => Right(true)
        case _ => Left(ErrorResponseFactory.get(IDMApiKeyNotInserted))
      }
      case _ => daoIDMApiKey.saveKey(ApiKeyMapper.toDTO(idmApiKey, clock)).map {
        case affectedRows if affectedRows==1 => Right(true)
        case _ => Left(ErrorResponseFactory.get(IDMApiKeyNotInserted))
      }
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while generating idm key for account ${idmApiKey.accountId}", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyNotInserted))
    }
  }

  def fetchIdmAccountInformation(apiKey: String): Future[Either[ErrorResponse, IdmAccountInfoResponse]] = {
    for {
      dtoIdmApiKeyOpt <- daoIDMApiKey.fetchActiveApiKeyDetails(apiKey)
      resp <- fetchIdmAccountInformation(dtoIdmApiKeyOpt)
    } yield resp
  }

  private def fetchIdmAccountInformation(dtoIdmApiKeyOpt: Option[DtoIdmApiKey]): Future[Either[ErrorResponse, IdmAccountInfoResponse]] = {
    dtoIdmApiKeyOpt match {
      case Some(dtoIdmApiKey) =>
        daoAccountV2.isAccountPermissionProvisioned(dtoIdmApiKey.accountId, BusinessUserRoles.IDM.id) flatMap {
          case res if res =>
            fetchIdmAccountInformation(dtoIdmApiKey.accountId)
          case _ =>
            logger.info(s"IDM feature not enabled for the account ${dtoIdmApiKey.accountId}")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.IDMNotEnabled)))
        }
      case _ =>
        logger.info(s"Could not find an active API key for the given input")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyNotFound)))
    }
  }

  private def fetchIdmAccountInformation(accountId: Long): Future[Either[ErrorResponse, IdmAccountInfoResponse]] = {
    val accountInfoFuture: Future[Seq[(DtoAccountHierarchy, DtoAccount)]] = daoAccountV2.getAccountInfoV2(accountId.toString)
    val apiKeysTupleFuture: Future[(Seq[EnvironmentNameAndId], Seq[DtoApiKey], Seq[DtoPublicApiKey])] = daoEnvironment.getApiKeysByAccountId(accountId)
    val dbResult: Future[(Seq[(DtoAccountHierarchy, DtoAccount)], (Seq[EnvironmentNameAndId], Seq[DtoApiKey], Seq[DtoPublicApiKey]))] = for {
      accountInfo <- accountInfoFuture
      apiKeysTuple <- apiKeysTupleFuture
    } yield(accountInfo, apiKeysTuple)

    dbResult map { res =>
      val dtoAccount: DtoAccount = res._1.head._2
      val environmentIdVsNameMap: Map[Long, String] = res._2._1.map(e => e.id -> e.name).toMap
      val idPlusKeys: Seq[ApiKeyResp] = getIdPlusKeys(res._2._2, environmentIdVsNameMap)
      val sdkKeys: Seq[ApiKeyResp] = getSdkKeys(res._2._3, environmentIdVsNameMap)

      val idmAccountInfoRespose: IdmAccountInfoResponse = IdmAccountInfoResponse(
        active = dtoAccount.isActive,
        accountId = dtoAccount.publicId,
        accountName = dtoAccount.name,
        apiKeys = ApiKeys(
          idplusKeys = idPlusKeys,
          sdkKeys = sdkKeys
        )
      )
      Right(idmAccountInfoRespose)
    } recoverWith {
      case _ : Exception =>
        logger.info(s"Could not fetch idm account information")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
  }

  private def getIdPlusKeys(dtoApiKeySeq: Seq[DtoApiKey], environmentIdVsNameMap: Map[Long, String]): Seq[ApiKeyResp] = {
    dtoApiKeySeq map { dtoApiKey =>
      ApiKeyResp(
        id = dtoApiKey.id,
        environmentId = dtoApiKey.environmentId,
        environment = environmentIdVsNameMap(dtoApiKey.environmentId),
        apiKey = dtoApiKey.apiKey,
        status = dtoApiKey.status.toString,
        createdAt = dtoApiKey.createdAt,
        updatedAt = dtoApiKey.updatedAt
      )
    }
  }

  private def getSdkKeys(dtoPublicApiKeySeq: Seq[DtoPublicApiKey], environmentIdVsNameMap: Map[Long, String]): Seq[ApiKeyResp] = {
    dtoPublicApiKeySeq map { dtoPublicApiKey =>
      ApiKeyResp(
        id = dtoPublicApiKey.id,
        environmentId = dtoPublicApiKey.environmentId,
        environment = environmentIdVsNameMap(dtoPublicApiKey.environmentId),
        apiKey = dtoPublicApiKey.apiKey,
        status = dtoPublicApiKey.status.toString,
        createdAt = dtoPublicApiKey.createdAt,
        updatedAt = dtoPublicApiKey.updatedAt
      )
    }
  }
}