package me.socure.account.dashboardv2

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{AuditDetailsService, BusinessUserCommonService, PasswordService}
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.constants._
import me.socure.convertors.{AccountConvertors, BusinessUserConvertors}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account.{AccountIdName, UserAccountAssociationStatuses, UserRole}
import me.socure.model.dashboardv2._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{DashboardUserRole, UpdateQuicksightUserStatus}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment}
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoActivationToken, DtoBusinessUser, DtoUserDetailsByPermission}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by gopal on 04/08/16.
 */
class DashboardUserServiceV2(daoAccount: DaoAccount,
                             daoAccountV2: DaoAccountV2,
                             daoBusinessUser: DaoBusinessUser,
                             passwordService: PasswordService,
                             daoEnvironment: DaoEnvironment,
                             clock: Clock,
                             v2Validator: V2Validator,
                             businessUserCommonService: BusinessUserCommonService,
                             auditDetailsService: AuditDetailsService)(implicit val ec: ExecutionContext) {

  val logger: Logger = LoggerFactory.getLogger(this.getClass)
  val AdminRoles = Set(DashboardUserRole.ADMIN.id, DashboardUserRole.SUPER_ADMIN.id, DashboardUserRole.DELEGATED_ADMIN.id, DashboardUserRole.USER.id)
  val SubAccountUserRoles = Set(DashboardUserRole.LIST_TRANSACTION.id)

  def listAllUsers(accountId: Long, creator: Option[Creator], showSubAccount: Boolean = false): Future[Either[ErrorResponse, Vector[DashboardUserV2]]] = {
    def users(): Future[Either[ErrorResponse, Vector[DashboardUserV2]]] = {
      daoBusinessUser.getBusinessUserListByAccountId(accountId, showSubAccount) map {
        case list if list.nonEmpty => Right(list.filter(u => AccountManagementDefaults.validateEmailUserName(u.email)))
        case list if list.isEmpty => Right(Vector.empty[DashboardUserV2])
        case _ => Left(ErrorResponseFactory.get(UnknownError))
      }
    }

    v2Validator.isValidV2AccountRequest(accountId, creator) flatMap {
      case false =>
        creator match {
          case Some(c) =>
            daoAccountV2.isValidSubAccount(c.accountId, Set(accountId)) flatMap {
              case true => users()
              case false => logger.info("Could not list users")
                Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
            }
          case None => users()
        }
      case true =>
        val creatorVal = creator.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable)))
        for {
          _ <- v2Validator.validateAccountAccess(accountId, creatorVal.accountId)
          user <- daoBusinessUser.getUser(creatorVal.userId)
          res <- getUsers(accountId) flatMap {
            case Right(dashboardUserV2Seq) =>
              val result = Future.sequence {
                dashboardUserV2Seq map { dashboardUserV2 =>
                  getUserWithRolesAndAssociations(dashboardUserV2.id, creatorVal) map {
                    case Right(dashboardUserWithAssociations) =>
                      dashboardUserV2.copy(accountsWithRoles = Some(dashboardUserWithAssociations.accountsWithRoles), isPrimryAdmin = isAccountOwnerPrimaryAdmin(dashboardUserWithAssociations.accountsWithRoles.find(_.accountDetails.id == accountId)))
                    case _ =>
                      dashboardUserV2.copy(accountsWithRoles = Some(Seq.empty))
                  }
                }
              }
              val isSocureUser = user.exists(_.email.matches(AccountManagementDefaults.internalEmailDomains))
              val riskOsSupportRoles = List(SystemDefinedRoles.RISKOS_SUPPORT_ADMIN.roleType, SystemDefinedRoles.RISKOS_SUPPORT_VIEWER.roleType)
              result.map(_.filterNot(u => !isSocureUser && u.accountsWithRoles.exists(_.exists(_.roles.exists(r => riskOsSupportRoles.contains(r.roleType)))))).map(Right(_))
            case _ =>
              logger.info("Could not list users")
              Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
          }
        } yield res
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not list users", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not list user Unknown error", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def isAccountOwnerPrimaryAdmin(accWithRoles: Option[AccountWithRoles]): Boolean = {
    accWithRoles match {
      case Some(accRoles) => accRoles.roles.filter(_.roleType == SystemDefinedRoles.ACCOUNTOWNER.roleType).nonEmpty
      case None => false
    }
  }

  def getUsers(accountId: Long): Future[Right[Nothing, Vector[DashboardUserV2]]] = {
    daoAccountV2.getUsers(accountId) map {
      case list if list.nonEmpty =>
        Right(list.filter(u => AccountManagementDefaults.validateEmailUserName(u._2.email)).map(u => DashboardUserV2(u._2.id,
          u._2.firstName,
          u._2.lastName,
          u._2.email,
          u._3.name,
          u._2.contactNumber,
          false,
          u._1.status == UserAccountAssociationStatuses.LOCKED.id)).toVector)
      case list if list.isEmpty => Right(Vector.empty[DashboardUserV2])
    }
  }

  def listAllUserIds(accountId: Long, showSubAccount: Boolean = false): Future[Either[ErrorResponse, Vector[Long]]] = {
    daoBusinessUser.getBusinessUserIdList(accountId, showSubAccount) map {
      case list if list.nonEmpty => Right(list)
      case list if list.isEmpty => Right(Vector.empty[Long])
      case _ => Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def createUser(userForm: BusinessUserForm): Future[Either[ErrorResponse, UserActivationDetails]] = {
    businessUserCommonService.checkUserEmailAvailability(userForm.userInfo.email) flatMap {
      case (_, false, _) => Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
      case (emailAlreadyExists, true, associatedAccounts) =>
        v2Validator.isValidV2AccountRequest(userForm.accountId, userForm.creator) flatMap {
          case true => createUserV2(userForm, emailAlreadyExists, associatedAccounts)
          case false =>
            if (userForm.environmentRoles.isEmpty || userForm.environmentRoles.map(_.roles.size).sum < 1) Future.successful(Left(ErrorResponseFactory.get(RolesNotFound)))
            else validateAndCreateUser(userForm, emailAlreadyExists, associatedAccounts)
        }
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not create user", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not create user Unknown error", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def validateAndCreateUser(userForm: BusinessUserForm, emailAlreadyExists: Boolean, associatedAccounts: Set[Long]): Future[Either[ErrorResponse, UserActivationDetails]] = {
    if (containsAdminRoles(userForm.environmentRoles.map(_.roles), AdminRoles)) {
      val roles = userForm.environmentRoles.mkString(",")
      logger.info(s"Admin roles cannot be assigned to the non-primary users - $roles")
      Future.successful(Left(ErrorResponseFactory.get(InvalidRoleProvisioning)))
    }
    else {
      daoAccount.getActiveAccountInfo(userForm.accountId) flatMap {
        case Some(accountInfo) =>
          if (accountInfo.parentId.isEmpty) {
            createUserWithActivationDetails(userForm, emailAlreadyExists, associatedAccounts)
          } else {
            daoAccount.getAccountPermissions(accountInfo.parentId.getOrElse(throw new Exception("Invalid Parent Account Info"))) flatMap {
              case roles if roles.exists(_.permission == BusinessUserRoles.SUB_ACCOUNTS_CAN_CREATE_USER.id) =>
                if (subAccountRoles(userForm.environmentRoles.map(_.roles), SubAccountUserRoles)) {
                  createUserWithActivationDetails(userForm, emailAlreadyExists, associatedAccounts)
                } else {
                  logger.info(s"Invalid role provision for sub-account users")
                  Future.successful(Left(ErrorResponseFactory.get(InvalidRoleProvisioning)))
                }
              case _ =>
                logger.info(s"Sub accounts cannot create users")
                Future.successful(Left(ErrorResponseFactory.get(OperationNotSupported)))
            }
          }
        case None =>
          logger.info(s"Invalid Account association (may be inactive/deleted/invalid)")
          Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
      }
    }
  }

  private def createUserV2(userForm: BusinessUserForm, emailAlreadyExists: Boolean, associatedAccounts: Set[Long]): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val creator = userForm.creator.getOrElse(throw new Exception("Invalid Creator details"))
    val isPrimaryAdmin = userForm.isPrimaryAdmin.getOrElse(false)

    if (isPrimaryAdmin) {
      for {
        root <- v2Validator.validateAccountAccess(userForm.accountId, creator.accountId)
        _ <- v2Validator.validatePrimaryAdminRateLimit(creator.accountId)
        response <- createUserWithAdditionalDetails(userForm.userInfo,
          userForm.accountId,
          isPrimaryUser = true,
          AccountManagementDefaults.userPermissions(root.accountType).filter(_._1 != EnvironmentTypes.GLOBAL_ENVIRONMENT.id).map { dp => (dp._1, dp._2.mkString(",")) },
          AccountManagementDefaults.userPermissions(root.accountType).filter(_._1 == EnvironmentTypes.GLOBAL_ENVIRONMENT.id).values.mkString(","),
          emailAlreadyExists,
          associatedAccounts: Set[Long],
          userForm.creator)
      } yield response
    } else {
      val envRoles = userForm.environmentRoles.flatMap(_.roles).toSet
      val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(envRoles).map(_.id)
      val permissions = Set(DashboardUserPermissions.USERS_CREATE.id) ++ provisionPermissions
      for {
        _ <- v2Validator.validateAccountAccess(userForm.accountId, creator.accountId)
        _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, permissions, EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
        response <- {
          val environmentRoles = userForm.environmentRoles
          val byDomainType = environmentRoles.flatMap(_.roles.map { r =>
            AccountManagementDefaults.legacyRoleDomainMapping.get(r).map(_.group).map(gps => (r, gps))
          }).flatten
          val globalV2Permissions = byDomainType.filter(_._2 == Groups.GLOBAL.id).map(_._1).toSet
          val esV2Permissions = environmentRoles.map(r => r.environmentId.toInt -> AccountConvertors.toDashboardPermissions(r.roles.filterNot(globalV2Permissions)).filterNot(provisionPermissions).mkString(",")).toMap

          createUserWithAdditionalDetails(userForm.userInfo,
            userForm.accountId,
            isPrimaryUser = false,
            esV2Permissions,
            (AccountConvertors.toDashboardPermissions(globalV2Permissions) ++ provisionPermissions).mkString(","),
            emailAlreadyExists,
            associatedAccounts,
            userForm.creator)
        }
      } yield response
    }
  }

  private def createUserWithAdditionalDetails(userInfo: DelegatedUserForm,
                                              accountId: Long,
                                              isPrimaryUser: Boolean,
                                              esV2Permissions: Map[Int, String],
                                              globalV2Permissions: String,
                                              emailAlreadyExists: Boolean,
                                              associatedAccounts: Set[Long],
                                              creator: Option[Creator]): Future[Either[ErrorResponse, UserActivationDetails]] = {
    def createUser0(creator: Creator) = {
      val dtoBusinessUser = BusinessUserConvertors.getDtoBusinessUser(userInfo).copy(accountId = accountId, isPrimaryUser = isPrimaryUser, activationCode = None)
      daoAccountV2.createUserWithAdditionalDetails(dtoBusinessUser, esV2Permissions, globalV2Permissions, creator) map { uuid =>
        Right(
          UserActivationDetails(
            firstname = dtoBusinessUser.firstName,
            surname = dtoBusinessUser.lastName,
            email = dtoBusinessUser.email,
            activationCode = Some(uuid)
          )
        )
      }
    }

    creator match {
      case Some(creator) =>
        emailAlreadyExists match {
          case true =>
            businessUserCommonService.handleIdleEmail(userInfo.email, associatedAccounts) flatMap {
              case true => createUser0(creator)
              case false =>
                logger.info(s"Unable to Create User, updating the prefixed email failed")
                Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
            }
          case false => createUser0(creator)
        }
      case _ =>
        logger.info(s"Insufficient data - Creator details missing")
        Future.successful(Left(ErrorResponseFactory.get(CreatorDetailsNotAvailable)))
    }
  }

  private def createUserWithActivationDetails(userForm: BusinessUserForm, emailAlreadyExists: Boolean, associatedAccounts: Set[Long]): Future[Either[ErrorResponse, UserActivationDetails]] = {
    def createUserWithActivationDetails0() = {
      val bu = BusinessUserConvertors.getDtoBusinessUser(userForm.userInfo).copy(accountId = userForm.accountId, isPrimaryUser = false, activationCode = None)
      daoBusinessUser.saveUser(bu) flatMap { savedUser =>
        daoBusinessUser.createActivationCode(DtoActivationToken(0, savedUser.id, None, Some(clock.now()))) flatMap { uuid =>
          val futureRole = userForm.environmentRoles.map(e => daoBusinessUser.addEnvironmentUserRoles(Some(e.environmentId), savedUser.id, e.roles))
          val details = UserActivationDetails(
            firstname = savedUser.firstName,
            surname = savedUser.lastName,
            email = savedUser.email,
            activationCode = uuid
          )
          Future.sequence(futureRole).map(_ => Right(details))
        }
      }
    }

    emailAlreadyExists match {
      case true =>
        businessUserCommonService.handleIdleEmail(userForm.userInfo.email, associatedAccounts) flatMap {
          case true => createUserWithActivationDetails0()
          case false =>
            logger.info(s"Unable to Create User, updating the prefixed email failed")
            Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
        }
      case false => createUserWithActivationDetails0()
    }

  }

  def updateUser(userId: Long, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2AccountRequest(userForm.accountId, userForm.creator) flatMap {
      case true =>
        updateUserInfoV2(userId, userForm) recover {
          case ex: ErrorResponseException =>
            logger.info("Could not update user", ex)
            Left(ex.errorResponse)
          case e: Throwable =>
            logger.info("Could not update user", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case false => updateUserInfo(userId, userForm)
    }
  }

  private def updateUserInfo(userId: Long, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.getUser(userId) flatMap {
      case Some(u) =>
        daoAccount.getActiveAccountInfo(userForm.accountId) flatMap {
          case Some(accountInfo) =>
            if (accountInfo.parentId.isEmpty) {
              updateUserInformation(userId, u, userForm)
            } else if (subAccountRoles(userForm.environmentRoles.map(_.roles), SubAccountUserRoles)) {
              updateUserInformation(userId, u, userForm)
            } else {
              logger.info(s"Invalid role provision for sub-account users")
              Future.successful(Left(ErrorResponseFactory.get(InvalidRoleProvisioning)))
            }
          case None =>
            logger.info(s"Invalid Account association (may be inactive/deleted/invalid)")
            Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def updateUserInfoV2(userId: Long, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]] = {
    val creator = userForm.creator.getOrElse(throw new Exception("Invalid Creator details"))
    val envRoles = userForm.environmentRoles.flatMap(_.roles).toSet
    val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(envRoles).map(_.id)
    val permissions = Set(DashboardUserPermissions.USERS_MODIFY.id) ++ provisionPermissions
    for {
      _ <- v2Validator.isAccountV2Provisioned(Set(userForm.accountId, creator.accountId))
      _ <- v2Validator.validateAccountAccess(userForm.accountId, creator.accountId)
      _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, permissions, EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      response <- validateUserAccountAssociationAndUpdateUser(userId, userForm)
    } yield response
  }

  private def formUserInfoDelta(createBusinessUserInput: CreateBusinessUserInput, userDetails: Option[UserDetails]): Option[UserInfoDelta] = {

    val userInfoDelta: Option[UserInfoDelta] = userDetails.fold {
      val added = Map("email" -> createBusinessUserInput.email, "firstName" -> createBusinessUserInput.firstName,
        "lastName" -> createBusinessUserInput.lastName, "contactNumber" -> createBusinessUserInput.contactNumber).collect {
        case (field, value) if value.nonEmpty => field -> value
      }

      Some(UserInfoDelta(Some(added), None))
    } {
      existingUser =>

        val added = Map.newBuilder[String, String]
        val updated = Map.newBuilder[String, String]

        if (createBusinessUserInput.email != existingUser.email && existingUser.firstName.isEmpty()) {
          added += ("email") -> createBusinessUserInput.email
        }

        def addOrUpdateField(field: String, inputField: String, existingField: String): Unit = {
          if (inputField != existingField) {
            if (existingField.isEmpty()) added += field -> inputField
            else updated += field -> inputField
          }
        }

        addOrUpdateField("firstName", existingUser.firstName, createBusinessUserInput.firstName)
        addOrUpdateField("lastName", existingUser.lastName, createBusinessUserInput.lastName)
        addOrUpdateField("contactNumber", existingUser.contactNumber, createBusinessUserInput.contactNumber)

        Some(UserInfoDelta((if (added.result().nonEmpty) Some(added.result()) else None), if (updated.result().nonEmpty) Some(updated.result()) else None))
    }

    userInfoDelta match {
      case Some(delta) if delta.added.nonEmpty || delta.updated.nonEmpty => Some(delta)
      case _ => None
    }
  }

  private def formAccountDelta(userEmail: String, userInfoDelta: Option[UserInfoDelta], accountWithRolesInput: Seq[AccountWithRolesInput], accountWithRoles: Seq[AccountWithRoles]): Option[AccountDelta] = {

    val inputAccountIDs = accountWithRolesInput.map(_.accountId).toSet
    val existingAccountIDs = accountWithRoles.map(_.accountDetails.id).toSet


    def isRoleUpdated(rolesInputDetails: Seq[RolesInputDetails], userRole: Seq[UserRole]): Boolean = {
      val inputRoleTypes = rolesInputDetails.map(_.roleType).toSet
      val existingRoleTypes = userRole.map(_.roleType).toSet

      if (inputRoleTypes == existingRoleTypes) false else true
    }

    val added = accountWithRolesInput.filterNot(account => existingAccountIDs.contains(account.accountId))

    val updated = accountWithRolesInput.filter(account =>
      existingAccountIDs.contains(account.accountId) &&
        isRoleUpdated(account.roles, accountWithRoles.find(existingAccount =>
          existingAccount.accountDetails.id == account.accountId).fold(Seq.empty[UserRole])(account => account.roles)))

    val removed = accountWithRoles.filterNot(account => inputAccountIDs.contains(account.accountDetails.id))

    if (added.isEmpty && updated.isEmpty && removed.isEmpty && userInfoDelta.isEmpty)
      None
    else {
      Some(AccountDelta(userEmail, userInfoDelta, if (added.nonEmpty) Some(added) else None, if (updated.nonEmpty) Some(updated) else None, if (removed.nonEmpty) Some(removed) else None))
    }

  }

  private def generateAuditDetails(createBusinessUserInput: CreateBusinessUserInput, dashboardUserWithAssociations: Option[DashboardUserWithAssociations], isSuccess: Boolean, errorResponse: Option[ErrorResponse]): Future[AuditDetails] = {

    val accountDelta = dashboardUserWithAssociations match {
      case Some(dashboardUser) =>
        val userInfoDelta: Option[UserInfoDelta] = formUserInfoDelta(createBusinessUserInput, Some(dashboardUser.userDetails))
        val accountDelta: Option[AccountDelta] = formAccountDelta(dashboardUser.userDetails.email, userInfoDelta, createBusinessUserInput.accountsWithRoles, dashboardUser.accountsWithRoles)
        accountDelta
      case None =>
        val userInfoDelta: Option[UserInfoDelta] = formUserInfoDelta(createBusinessUserInput, None)
        val accountDelta: Option[AccountDelta] = formAccountDelta(createBusinessUserInput.email, userInfoDelta, createBusinessUserInput.accountsWithRoles, Seq[AccountWithRoles]())
        accountDelta
    }

    val actionUserInfo: Future[ActionUserInfo] = auditDetailsService.formActionUserInfo(Some(createBusinessUserInput.creator))
    for {
      actionUser <- actionUserInfo
    } yield AuditDetails(isSuccess, actionUser, accountDelta, errorResponse, Seq.empty)
  }

  private def updateErrorResponseInAudit(auditDetails: AuditDetails, errorResponse: ErrorResponse): AuditDetails = {

    AuditDetails(false, auditDetails.actionUserInfo, auditDetails.accountDelta, Some(errorResponse), Seq.empty)
  }

  private def formAuditDetailsWithErrorResponse(createBusinessUserInput: CreateBusinessUserInput, errorResponse: ErrorResponse): Future[AuditDetails] = {

    val actionUserInfo: Future[ActionUserInfo] = auditDetailsService.formActionUserInfo(Some(createBusinessUserInput.creator))
    for {
      actionUser <- actionUserInfo
    } yield AuditDetails(false, actionUser, None, Some(errorResponse), Seq.empty)
  }

  def createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput: CreateBusinessUserInput): Future[(AuditDetails, Either[ErrorResponse, UserActivationDetails])] = {
    def createBusinessUserWithAssociation0() = {
      daoAccountV2.createBusinessUserWithAssociations(createBusinessUserInput) flatMap  { res =>
        daoAccountV2.updateSponsorBankRolesForPrograms(res._2, createBusinessUserInput.accountsWithRoles) map { updateResponse =>
        Right(
          UserActivationDetails(
            firstname = createBusinessUserInput.firstName,
            surname = createBusinessUserInput.lastName,
            email = createBusinessUserInput.email,
              activationCode = Some(res._1)
          )
        )
        }
      }
    }

    def combineRoles(baseRoles: Seq[RolesInputDetails], toBeAdded: Seq[RolesInputDetails]): Seq[RolesInputDetails] = {
      val baseRolesTuple = baseRoles.map(role2 => (role2.roleType, role2.roleId))
      baseRoles ++ toBeAdded.filterNot(role1 => {
        baseRolesTuple.contains((role1.roleType, role1.roleId))
      })

    }

    def updateBusinessUserWithAssociation0(userId: Long, userWithRolesAndAssociation: Future[Either[ErrorResponse, DashboardUserWithAssociations]]): Future[(AuditDetails, Either[ErrorResponse, UserActivationDetails])] = {

      userWithRolesAndAssociation flatMap {
        case Left(errorResponse: ErrorResponse) =>
          logger.info(s"Could not update user $userId, error $errorResponse")

          formAuditDetailsWithErrorResponse(createBusinessUserInput, errorResponse).map {
            auditDetails => (auditDetails, Left(errorResponse))
          }
        case Right(dashboardUserWithAssociations: DashboardUserWithAssociations) =>

          val existingAssociations = dashboardUserWithAssociations.accountsWithRoles map (accountAssociation => AccountWithRolesInput(accountAssociation.accountDetails.id,
            accountAssociation.roles.map(role => RolesInputDetails(role.roleType, role.id))))

          val mergedAssociations = existingAssociations.map(association => {
            val matchedAssociation = createBusinessUserInput.accountsWithRoles.find(_.accountId == association.accountId)
            if (matchedAssociation.isEmpty)
              association
            else {
              val combinedRoles = combineRoles(association.roles, matchedAssociation.get.roles)
              association.copy(roles = combinedRoles)
            }
          })

          val existingAccountIds = mergedAssociations.map(_.accountId)
          val withNewAssociations = mergedAssociations ++ createBusinessUserInput.accountsWithRoles.filterNot(input => existingAccountIds.contains(input.accountId))
          val updateBusinessUserInput = UpdateBusinessUserInput(userId, createBusinessUserInput.firstName, createBusinessUserInput.lastName,
            createBusinessUserInput.contactNumber, withNewAssociations, createBusinessUserInput.creator)


          generateAuditDetails(createBusinessUserInput, Some(dashboardUserWithAssociations), true, None).flatMap {
            auditDetails =>
              updateBusinessUserWithAssociations(updateBusinessUserInput, false) map {
                case Left(errorResponse) =>
                  (updateErrorResponseInAudit(auditDetails, errorResponse), Left(errorResponse))
                case Right(updated) if updated =>
                  (auditDetails, Right(UserActivationDetails(
                    firstname = createBusinessUserInput.firstName,
                    surname = createBusinessUserInput.lastName,
                    email = createBusinessUserInput.email,
                    activationCode = None
                  )))
                case _ =>
                  val errorResponse = ErrorResponseFactory.get(UnknownError)
                  logger.info(s"Could not update user ${userId}")
                  (updateErrorResponseInAudit(auditDetails, errorResponse), Left(errorResponse))
              }
          }
      }
    }


    businessUserCommonService.checkUserEmailAvailability(createBusinessUserInput.email).flatMap {
      case (_, false, _) =>

        //        active and non deleted accounts exists
        //        if V1 fail it, if V2 update the user

        daoBusinessUser.getUser(createBusinessUserInput.email) flatMap {
          case Some(userInfo) =>
            logger.info("userInfo " + userInfo)
            daoAccountV2.isAccountV2Provisioned(Set(userInfo.accountId)) flatMap {
              case false =>
                logger.info(s"Unable to update user ${userInfo.id}, associated to active V1 account ${userInfo.accountId}")
                formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(UnknownError)).map { auditDetails => (auditDetails, Left(ErrorResponseFactory.get(UnknownError))) }

              case true =>
                v2Validator.validateRiskOsSupportRoles(createBusinessUserInput.creator.userId, createBusinessUserInput.creator.accountId, createBusinessUserInput.email, createBusinessUserInput.accountsWithRoles).flatMap {
                  case true =>
                    logger.info(s"Updating user ${userInfo.id}")
                    val userWithRolesAndAssociationsResponse = getUserWithRolesAndAssociations(userInfo.id, createBusinessUserInput.creator)
                    daoAccountV2.isSubAccount(createBusinessUserInput.accountId, userInfo.accountId) flatMap {
                      case true => updateBusinessUserWithAssociation0(userInfo.id, userWithRolesAndAssociationsResponse)
                      case false =>
                        val updateBusinessUserInput = UpdateBusinessUserInput(userInfo.id, createBusinessUserInput.firstName, createBusinessUserInput.lastName,
                          createBusinessUserInput.contactNumber, createBusinessUserInput.accountsWithRoles, createBusinessUserInput.creator)


                        val futureAuditDetails: Future[AuditDetails] = userWithRolesAndAssociationsResponse flatMap {
                          case Right(dashboardUserWithAssociations: DashboardUserWithAssociations) =>
                            generateAuditDetails(createBusinessUserInput, Some(dashboardUserWithAssociations), true, None)

                          case Left(errorResponse) =>
                            formAuditDetailsWithErrorResponse(createBusinessUserInput, errorResponse)
                          case _ =>
                            Left(ErrorResponseFactory.get(UnknownError))
                            formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(UnknownError))
                        }

                        futureAuditDetails.flatMap {
                          auditDetails =>
                            updateBusinessUserWithAssociations(updateBusinessUserInput, false) map {
                              case Left(errorResponse) =>
                                (updateErrorResponseInAudit(auditDetails, errorResponse), Left(errorResponse))

                              case Right(updated) if updated =>
                                (auditDetails, Right(UserActivationDetails(
                                  firstname = createBusinessUserInput.firstName,
                                  surname = createBusinessUserInput.lastName,
                                  email = createBusinessUserInput.email,
                                  activationCode = None
                                )))
                              case _ =>
                                logger.info(s"Could not update user ${userInfo.id}")
                                (auditDetails, Left(ErrorResponseFactory.get(UnknownError)))
                            }

                        }
                    }
                  case _ =>
                    logger.info(s"Unable to update user ${userInfo.id}, associated to active V1 account ${userInfo.accountId}")
                    formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(AccessForbidden)).map { auditDetails => (auditDetails, Left(ErrorResponseFactory.get(AccessForbidden))) }
                }
            }
          case None =>
            logger.info("User doesn't exists") // invalid case
            formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(UnknownError)).map { auditDetails => (auditDetails, Left(ErrorResponseFactory.get(UnknownError))) }
        }
      case (emailAlreadyExists, true, associatedAccounts) =>
        val accountIds = createBusinessUserInput.accountsWithRoles.map(_.accountId) :+ createBusinessUserInput.accountId
        val customRoleIds = createBusinessUserInput.accountsWithRoles.flatMap(_.roles.filter(_.roleType == SystemDefinedRoles.CUSTOMROLE.roleType).map(_.roleId.get))
        val creator = createBusinessUserInput.creator
        generateAuditDetails(createBusinessUserInput, None, true, None).flatMap {
          auditDetails =>
            (for {

              _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, Set(DashboardUserPermissions.USERS_CREATE.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
              _ <- v2Validator.validateUserRoleDetails(createBusinessUserInput.accountsWithRoles.flatMap(_.roles))
              _ <- v2Validator.validateUserRoleAccess(customRoleIds, creator.accountId)
              _ <- v2Validator.validateAccountAccess(accountIds, creator.accountId, creator.userId, None)
              _ <- v2Validator.validateRiskOsSupportRoles(creator.userId,creator.accountId, createBusinessUserInput.email, createBusinessUserInput.accountsWithRoles)
              response <- {
                emailAlreadyExists match {
                  case true =>
                    val createBusinessUserWithExistingEmail: Future[(AuditDetails, Either[ErrorResponse, UserActivationDetails])] = businessUserCommonService.handleIdleEmail(createBusinessUserInput.email, associatedAccounts) flatMap {
                      case true => createBusinessUserWithAssociation0().map(resp => (auditDetails, resp))
                      case false =>
                        logger.info(s"Unable to Create User, updating the prefixed email failed")
                        Future.successful(updateErrorResponseInAudit(auditDetails, ErrorResponseFactory.get(ExceptionCodes.UnknownError)), Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
                    }
                    createBusinessUserWithExistingEmail
                  case false => createBusinessUserWithAssociation0().map(resp => (auditDetails, resp))
                }
              }
            } yield response) recover {
              case ex: ErrorResponseException =>
                logger.info("Could not insert user account association", ex)
                (updateErrorResponseInAudit(auditDetails, ex.errorResponse), Left(ex.errorResponse))
              case e: Exception =>
                logger.info("Could not insert user account association", e)
                (updateErrorResponseInAudit(auditDetails, ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation)), Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation)))
            }
        }
    } recoverWith {
      case e: Throwable =>
        logger.info("Create Business User With Association failed", e)

        formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(UnknownError)).map {
          auditDetails => (auditDetails, Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
        }
    }
  }

  private def isNonSAMLAccountPresent(accountIds: Seq[Long]): Future[Boolean] = {
    if (accountIds.isEmpty)
      Future.successful(false)
    else {
      daoAccount.anyHasRole(accountIds = Set(accountIds.head),
        businessUserRole = BusinessUserRoles.SAML_2_0.id, accountAttributeValueOpt = None) flatMap { present => {
        if (!present) {
          Future.successful(true)
        } else {
          isNonSAMLAccountPresent(accountIds.tail)
        }
      }
      }
    }
  }

  def updateBusinessUserWithAssociations(updateBusinessUserInput: UpdateBusinessUserInput, mergeAssociations: Boolean = true): Future[Either[ErrorResponse, Boolean]] = {
    def combineRoles(baseRoles: Seq[RolesInputDetails], toBeAdded: Seq[RolesInputDetails]): Seq[RolesInputDetails] = {
      val baseRolesTuple = baseRoles.map(role2 => (role2.roleType, role2.roleId))
      baseRoles ++ toBeAdded.filterNot(role1 => {
        baseRolesTuple.contains((role1.roleType, role1.roleId))
      })
    }

    if (mergeAssociations) {
      getUserWithRolesAndAssociations(updateBusinessUserInput.id, updateBusinessUserInput.creator) flatMap {
        case Left(errorResponse: ErrorResponse) =>
          logger.info(s"Could not update user ${updateBusinessUserInput.id}, error $errorResponse")
          Future.successful(Left(errorResponse))
        case Right(dashboardUserWithAssociations: DashboardUserWithAssociations) =>

          val existingAssociations = dashboardUserWithAssociations.accountsWithRoles map (accountAssociation => AccountWithRolesInput(accountAssociation.accountDetails.id,
            accountAssociation.roles.filter(role => role.roleType == SystemDefinedRoles.ACCOUNTOWNER.roleType).map(role => RolesInputDetails(role.roleType, role.id))))

          val mergedAssociations = existingAssociations.map(association => {
            val matchedAssociation = updateBusinessUserInput.accountsWithRoles.find(_.accountId == association.accountId)
            if (matchedAssociation.isEmpty)
              association
            else {
              val combinedRoles = combineRoles(association.roles, matchedAssociation.get.roles)
                association.copy(roles = combinedRoles)
            }
          })

          val existingAccountIds = mergedAssociations.map(_.accountId)
          val newAssociations = updateBusinessUserInput.accountsWithRoles.filterNot(input => existingAccountIds.contains(input.accountId))
          daoAccountV2.getAssociatedAccounts(updateBusinessUserInput.creator.userId).flatMap { associatedAccounts =>
            v2Validator.getAccountOwnerAssociatedAccounts(updateBusinessUserInput.creator.userId, associatedAccounts).flatMap { accountOwnerAccessAccounts =>
              val combinedAccounts = associatedAccounts ++ accountOwnerAccessAccounts
              // Check if all newAssociations' accountIds are valid
              val areNewAssociationsValid = newAssociations.forall(assoc => combinedAccounts.map(_.id).contains(assoc.accountId))
              if (areNewAssociationsValid) {
                val withNewAssociations = mergedAssociations ++ newAssociations
                val updateBusinessUserInputMerged = UpdateBusinessUserInput(updateBusinessUserInput.id, updateBusinessUserInput.firstName, updateBusinessUserInput.lastName,
                  updateBusinessUserInput.contactNumber, withNewAssociations.filter(association => association.roles.nonEmpty), updateBusinessUserInput.creator)
                updateBusinessUser(updateBusinessUserInputMerged)
              } else {
                logger.info(s"Could not update user ${updateBusinessUserInput.id}, ${InvalidAccountRoleAssociation.description}")
                Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidAccountRoleAssociation)))
              }
            }
          }
      }
    } else {
      updateBusinessUser(updateBusinessUserInput)
    }
  }

  def updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput: UpdateBusinessUserInput): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {

    val createBusinessUserInput = CreateBusinessUserInput(updateBusinessUserInput.creator.accountId, "", updateBusinessUserInput.firstName, updateBusinessUserInput.lastName, updateBusinessUserInput.contactNumber, updateBusinessUserInput.accountsWithRoles, updateBusinessUserInput.creator)

    getUserWithRolesAndAssociations(updateBusinessUserInput.id, createBusinessUserInput.creator) flatMap {
      case Left(errorResponse: ErrorResponse) =>
        updateBusinessUserWithAssociations(updateBusinessUserInput).flatMap {
          case Right(response: Boolean) =>
            formAuditDetailsWithErrorResponse(createBusinessUserInput, errorResponse).map {
              audit => (audit, Right(response))
            }
          case Left(errorResponseForUpdate: ErrorResponse) =>
            formAuditDetailsWithErrorResponse(createBusinessUserInput, errorResponseForUpdate).map {
              audit => (audit, Left(errorResponseForUpdate))
            }
        }

      case Right(dashboardUserWithAssociations: DashboardUserWithAssociations) =>
        v2Validator.validateRiskOsSupportRoles(createBusinessUserInput.creator.userId, createBusinessUserInput.accountId, inputBusinessUserEmail = dashboardUserWithAssociations.userDetails.email, updateBusinessUserInput.accountsWithRoles) flatMap {
          case true =>
            updateBusinessUserWithAssociations(updateBusinessUserInput).flatMap {
              case Right(response: Boolean) =>

                generateAuditDetails(createBusinessUserInput, Some(dashboardUserWithAssociations), true, None).map {
                  audit => (audit, Right(response))
                }

              case Left(errorResponseForUpdate: ErrorResponse) =>
                formAuditDetailsWithErrorResponse(createBusinessUserInput, errorResponseForUpdate).map {
                  audit => (audit, Left(errorResponseForUpdate))
                }
            }
          case _ =>
            logger.info(s"Unable to update user ${updateBusinessUserInput.id}, associated to active V1 account ${updateBusinessUserInput.creator}")
            formAuditDetailsWithErrorResponse(createBusinessUserInput, ErrorResponseFactory.get(AccessForbidden)).map {
              audit => (audit, Left(ErrorResponseFactory.get(AccessForbidden)))
            }
        }
    }
  }


  def updateBusinessUser(updateBusinessUserInput: UpdateBusinessUserInput): Future[Either[ErrorResponse, Boolean]] = {
    val accountIds = updateBusinessUserInput.accountsWithRoles.map(_.accountId)
    val customRoleIds = updateBusinessUserInput.accountsWithRoles.flatMap(_.roles.filter(_.roleType == SystemDefinedRoles.CUSTOMROLE.roleType).map(_.roleId.get))
    val creator = updateBusinessUserInput.creator
    (for {
      _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, Set(DashboardUserPermissions.USERS_MODIFY.id, DashboardUserPermissions.USER_ROLES_VIEW.id, DashboardUserPermissions.USER_ROLES_PROVISION.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id, Set(DashboardUserPermissions.USERS_MODIFY.id, DashboardUserPermissions.USERS_VIEW.id, DashboardUserPermissions.USER_ROLES_PROVISION.id))
      _ <- v2Validator.validateUserRoleDetails(updateBusinessUserInput.accountsWithRoles.flatMap(_.roles))
      _ <- v2Validator.validateUserRoleAccess(customRoleIds, creator.accountId)
      _ <- v2Validator.validateAccountAccess(accountIds, creator.accountId, creator.userId, Some(updateBusinessUserInput.id))
      _ <- v2Validator.validateUserAccountAssociation(updateBusinessUserInput.id, creator.accountId +: accountIds, creator.accountId)
      accountIds <- daoAccountV2.getActiveSubAccountIdsWithParent(creator.accountId)
      //Filter here based on Creator account id and its children sub account id's
      res <- daoAccountV2.getAssociationsByUserIdFilteredByAccountIds(updateBusinessUserInput.id, accountIds) flatMap { availableAccountAssociations =>
        val availableAccountIdVsAssociationIdMap = availableAccountAssociations.map(uaa => uaa.accountId -> uaa.id).toMap
        daoAccountV2.updateBusinessUserWithAssociations(updateBusinessUserInput, availableAccountIdVsAssociationIdMap) flatMap { res =>
          daoAccountV2.updateSponsorBankRolesForPrograms(updateBusinessUserInput.id, updateBusinessUserInput.accountsWithRoles) flatMap { updateResponse =>
          // check if user doesn't have password and he has at least one non saml account now, then trigger set password email with domain based on creator account v3 or not

          passwordService.hasValidPassword(updateBusinessUserInput.id) flatMap { valid => {
            if (valid) Future.successful(Right(res))
            else {
              daoAccountV2.getAssociatedAccounts(updateBusinessUserInput.id) flatMap { associatedAccounts =>

                isNonSAMLAccountPresent(associatedAccounts.map(_.id)) flatMap { nonSAMLAccountPresentFuture => {
                  if (!nonSAMLAccountPresentFuture) Future.successful(Right(res)) // all SAML
                  else {
                    daoBusinessUser.createActivationCode(DtoActivationToken(0, updateBusinessUserInput.id, None, Some(clock.now()))) flatMap { activationCode => {
                      if (activationCode.isEmpty) {
                        logger.error(s"Error while creating activation code for user ${updateBusinessUserInput.id} during non saml account association")
                        Future.successful(Right(res))
                      } else {
                        daoBusinessUser.getUser(updateBusinessUserInput.id) flatMap { user => {
                          if (user.isDefined) {
                            daoAccount.anyHasRole(accountIds = Set(creator.accountId), businessUserRole = BusinessUserRoles.DashboardV3.id, accountAttributeValueOpt = None) flatMap {
                              v3 => {
                                Future.successful(Right(res))
                              }
                            }
                          } else {
                            logger.error(s"User doesn't exists ${updateBusinessUserInput.id} during non saml account association")
                            Future.successful(Right(res))
                          }
                        }
                        }
                      }
                    }
                    }
                  }
                }
                }
              }
            }
          }
          }
        }
        }
      } recover {
        case err =>
          logger.info(s"Error occurred while updating user - ${updateBusinessUserInput.id}", err)
          Left(ErrorResponseFactory.get(CouldNotUpdateBusinessUser))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not insert user account association", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not insert user account association", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation))
    }
  }

  private def validateUserAccountAssociationAndUpdateUser(userId: Long, userForm: BusinessUserForm) = {
    daoBusinessUser.getUser(userId) flatMap {
      case Some(u) =>
        daoAccountV2.getUserAccountAssociation(userId, userForm.accountId) flatMap {
          case Some(dtoUserAccountAssociation) =>
            updateUserInformationV2(dtoUserAccountAssociation.id, u, userForm, dtoUserAccountAssociation.isPrimaryUser)
          case None =>
            logger.info(s"Invalid User Account association (may be inactive/deleted/invalid)")
            Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    } recover {
      case err =>
        logger.info(s"Error occurred while fetching user with userid - $userId", err)
        Left(ErrorResponseFactory.get(UserNotFound))
    }
  }

  private def updateUserInformation(userId: Long, u: DtoBusinessUser, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]] = {
    if (userForm.environmentRoles.isEmpty) Future.successful(Left(ErrorResponseFactory.get(RolesNotFound)))
    else if (!u.isPrimaryUser && containsAdminRoles(userForm.environmentRoles.map(_.roles), AdminRoles)) Future.successful(Left(ErrorResponseFactory.get(InvalidRoleProvisioning)))
    else {
      val futureUpdateInfo = daoBusinessUser.updateUserInformation(u.email, BusinessUserConvertors.convertDelegtedUser(userForm.userInfo))
      val futureUpdateRoles = if (!u.isPrimaryUser) {
        daoBusinessUser.deleteAllUserEnvironmentRoles(userId) map { _ =>
          userForm.environmentRoles.map(e => daoBusinessUser.addEnvironmentUserRoles(Some(e.environmentId), userId, e.roles))
        }
      } else Future.successful(None)
      Future.sequence(Seq(futureUpdateInfo, futureUpdateRoles)).map(_ => Right(true))
    } recover {
      case e: Throwable =>
        logger.info("Could not update user Information", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def updateUserInformationV2(userAccountAssociationId: Long, u: DtoBusinessUser, userForm: BusinessUserForm, isPrimaryUser: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    if (isPrimaryUser) daoAccountV2.updateBusinessUserV2(u, userForm.userInfo).map(Right(_))
    else if (userForm.environmentRoles.isEmpty) Future.successful(Left(ErrorResponseFactory.get(RolesNotFound)))
    else {
      daoBusinessUser.getAllEnvironmentByAccountId(userForm.accountId) flatMap {
        environments =>
          val environmentRoles = userForm.environmentRoles
          val provisionPermissions = AccountManagementDefaults.getProvisioningPermission(environmentRoles.flatMap(_.roles).toSet).map(_.id)
          val byDomainType = environmentRoles.flatMap(_.roles.map { r =>
            AccountManagementDefaults.legacyRoleDomainMapping.get(r).map(_.group).map(gps => (r, gps))
          }).flatten
          val globalV2Permissions = byDomainType.filter(_._2 == Groups.GLOBAL.id).map(_._1).toSet
          val esV2Permissions = environmentRoles.map(r => environments.find(e => e.id == r.environmentId).getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(UnknownError))).environmentType.toInt -> AccountConvertors.toDashboardPermissions(r.roles.filterNot(globalV2Permissions)).filterNot(provisionPermissions).mkString(",")).toMap
          val environmentTypeVsPermissionsMap = esV2Permissions + (EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> (AccountConvertors.toDashboardPermissions(globalV2Permissions) ++ provisionPermissions).mkString(","))
          daoAccountV2.updateUserV2(userAccountAssociationId, u, environmentTypeVsPermissionsMap, userForm.creator.get, userForm.userInfo).map(Right(_))
      }
    } recover {
      case e: Throwable =>
        logger.info("Could not update user Information", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def toggleBusinessUserLock(userId: Long, isLocked: Boolean, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, Boolean]] = {
    accountWithCreator match {
      case Some(ac) =>
        v2Validator.isValidV2AccountRequest(ac.accountId, Some(ac.creator)) flatMap {
          case true => toggleBusinessUserLockV2(userId, isLocked, ac)
          case false => v2Validator.isValidBusinessUser(ac.accountId, userId) flatMap {
            case true => toggleBusinessUserLockV1(userId, isLocked)
            case false => Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
          }
        }
      case None => toggleBusinessUserLockV1(userId, isLocked)
    }
  }

  private def sendUnlockEmail(userId: Long): Unit = {
    daoBusinessUser.getUser(userId) map {
      case Some(u) => daoAccount.anyHasRole(accountIds = Set(u.accountId), businessUserRole = BusinessUserRoles.DashboardV3.id, accountAttributeValueOpt = None) map {
        case isV3 =>
        case _ => logger.error(s"Error while fetching dashboard v3 permission for user ${u.accountId}")
      }
      case None => logger.info(s"Couldn't fetch user details")
    }
  }

  private def toggleBusinessUserLockV1(userId: Long, isLocked: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.toggleLockStatus(userId, isLocked) map {
      case row if row > 0 => if (!isLocked) sendUnlockEmail(userId)
        Right(true)
      case _ => Left(ErrorResponseFactory.get(UserNotFound))
    }
  }

  private def toggleBusinessUserLockV2(userId: Long, lock: Boolean, accountWithCreator: AccountWithCreator): Future[Either[ErrorResponse, Boolean]] = {
    val response = for {
      _ <- v2Validator.validateAccountAccess(accountWithCreator.accountId, accountWithCreator.creator.accountId)
      _ <- v2Validator.validatePermissions(accountWithCreator.creator.accountId, accountWithCreator.creator.userId, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      uas <- daoAccountV2.getUserAccountAssociation(accountWithCreator.creator.userId, accountWithCreator.creator.accountId)
      res <- {
        val userAccountAssociationId = uas.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))).id
        if (lock) daoAccountV2.lockUserAccountAssociation(userId, accountWithCreator.accountId, userAccountAssociationId)
        else daoAccountV2.activateUserAccountAssociation(userId, accountWithCreator.accountId, userAccountAssociationId)
      }
    } yield res
    response map {
      case x if x > 0 => if (!lock) sendUnlockEmail(userId)
        Right(true)
      case _ => Left(ErrorResponseFactory.get(UpdateUserAccountStatusFailed))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not change lock status", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not change lock status", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def deleteBusinessUser(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    def getAuditDetailsWithResponse(ac: Option[AccountWithCreator], isSuccess: Boolean, errorResponse: Option[ErrorResponse]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
      isSuccess match {
        case true =>
          formAuditDetailsForDeleteUser(userId, ac, true, errorResponse).map {
            auditDetails => (auditDetails, Right(true))
          }
        case false => formAuditDetailsForDeleteUser(userId, ac, false, errorResponse).map {
          auditDetails => (auditDetails, Left(errorResponse.get))
        }
      }
    }

    accountWithCreator match {
      case Some(ac) =>
        v2Validator.isValidV2AccountRequest(ac.accountId, Some(ac.creator)) flatMap {
          case true => deleteBusinessUserV2(userId, ac) flatMap {
            case Left(error) =>
              getAuditDetailsWithResponse(Some(ac), false, Some(error))

            case Right(isSuccess) => isSuccess match {
              case false =>
                getAuditDetailsWithResponse(Some(ac), false, Some(ErrorResponseFactory.get(UnknownError)))

              case true =>
                getAuditDetailsWithResponse(Some(ac), true, None)
            }

          }
          case false => v2Validator.isValidBusinessUser(ac.accountId, userId) flatMap {
            case true => deleteBusinessUserV1(userId) flatMap {
              case Left(error) =>
                getAuditDetailsWithResponse(Some(ac), false, Some(error))

              case Right(isSuccess) => isSuccess match {
                case false => getAuditDetailsWithResponse(Some(ac), false, Some(ErrorResponseFactory.get(UnknownError)))

                case true =>
                  getAuditDetailsWithResponse(Some(ac), true, None)
              }

            }
            case false =>
              getAuditDetailsWithResponse(Some(ac), false, Some(ErrorResponseFactory.get(AccessForbidden)))


          }
        }
      case None => deleteBusinessUserV1(userId) flatMap {
        case Left(error) =>
          getAuditDetailsWithResponse(None, false, Some(error))

        case Right(isSuccess) => isSuccess match {
          case false =>
            getAuditDetailsWithResponse(None, false, Some(ErrorResponseFactory.get(UnknownError)))

          case true =>
            getAuditDetailsWithResponse(None, true, None)
        }
      }
    }
  }

  def deleteBusinessUserV1(userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.getUser(userId) flatMap {
      case Some(u) =>
        if (!u.isPrimaryUser) {
          val badLoginCount = daoBusinessUser.deleteBadLoginDetails(userId)
          val badLoginAttempt = daoBusinessUser.deleteBadLoginAttemptInfo(userId)
          val envRoles = daoBusinessUser.deleteAllUserEnvironmentRoles(userId)
          val passwords = passwordService.deleteAllPassword(userId)
          Future.sequence(Seq(badLoginAttempt, badLoginCount, envRoles, passwords)).flatMap(_ => daoBusinessUser.deleteUser(userId).map(_ => Right(true)))
        } else {
          Future.successful(Left(ErrorResponseFactory.get(PrimaryBusinessUserDeletion)))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def deleteBusinessUserV2(userId: Long, accountWithCreator: AccountWithCreator): Future[Either[ErrorResponse, Boolean]] = {
    val response = for {
      _ <- v2Validator.validateAccountAccess(accountWithCreator.accountId, accountWithCreator.creator.accountId)
      _ <- v2Validator.validatePermissions(accountWithCreator.creator.accountId, accountWithCreator.creator.userId, Set(DashboardUserPermissions.USERS_MODIFY.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      uas <- daoAccountV2.getUserAccountAssociation(userId, accountWithCreator.accountId)
      _ <- daoAccountV2.deleteUserAccountRoleAssociation(uas.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))).id)
      res <- daoAccountV2 deleteUserAccountAssociation(userId, accountWithCreator.accountId, uas.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))).id)
    } yield res
    response map {
      case x if x > 0 => Right(true)
      case _ =>
        logger.info("Could not change status to DELETED")
        Left(ErrorResponseFactory.get(UpdateUserAccountStatusFailed))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not change status to DELETED ", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not change status to DELETED", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def formAuditDetailsForDeleteUser(userId: Long, accountWithCreator: Option[AccountWithCreator], isSuccess: Boolean, errorResponse: Option[ErrorResponse]) = {

    accountWithCreator match {
      case None =>
        val userDetails = UserDetails(0L, "", "", "", "")
        val actionUserInfo: ActionUserInfo = ActionUserInfo(userDetails, Set.empty[Int], Seq.empty)
        val removedAccount: Option[Seq[AccountWithRoles]] = Some(Seq.empty)
        Future.successful(AuditDetails(isSuccess, actionUserInfo, Some(AccountDelta(userId.toString, None, None, None,
          removedAccount, true)), errorResponse, Seq.empty))
      case Some(accountWithCreator) =>

        val resp = for {
          accountInfo <- daoAccount.getAccount(accountWithCreator.accountId)
          userDetails <- daoAccountV2.getUserDetailsV2(userId, accountWithCreator.accountId)
          actionUserInfo <- auditDetailsService.formActionUserInfo(Some(accountWithCreator.creator))
        } yield (accountInfo, userDetails, actionUserInfo)

        resp.map {
          case data =>
            val accountName: String = data._1 match {
              case Some(account) => account.name
              case None => ""
            }

            val userEmail: String = data._2 match {
              case Some(user) => user._3
              case None => userId.toString
            }

            val actionUserInfo: ActionUserInfo = data._3

            val removedAccount: Option[Seq[AccountWithRoles]] = Some(Seq(AccountWithRoles(AccountIdName(accountWithCreator.accountId, accountName), Seq.empty)))


            AuditDetails(isSuccess, actionUserInfo, Some(AccountDelta(userEmail, None, None, None, removedAccount, true)), errorResponse, Seq.empty)
        }

    }

  }

  def getUserWithRoles(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, DashboardUserWithRoles]] = {
    accountWithCreator match {
      case Some(ac) =>
        v2Validator.isValidV2AccountRequest(ac.accountId, Some(ac.creator)) flatMap {
          case true =>
            getUserWithRolesV2(userId, ac)
          case false =>
            v2Validator.isValidBusinessUser(ac.accountId, userId) flatMap {
              case true =>
                if (ac.accountId == ac.creator.accountId) {
                  getUserWithRolesV1(userId)
                } else {
                  daoAccountV2.isValidSubAccount(ac.creator.accountId, Set(ac.accountId)) flatMap {
                    case true => getUserWithRolesV1(userId)
                    case false => Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
                  }
                }
              case false =>
                Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
            }
        }
      case None => getUserWithRolesV1(userId)
    }
  }

  def getUserWithRolesAndAssociations(userId: Long, creator: Creator): Future[Either[ErrorResponse, DashboardUserWithAssociations]] = {
    def getRoleName(roleDetails: (Int, Option[Long]), customRoleIdVsNameMap: Map[Long, (String, Long)]): String = {
      roleDetails._1 match {
        case SystemDefinedRoles.CUSTOMROLE.roleType =>
          roleDetails._2.flatMap(role => customRoleIdVsNameMap.get(role).flatMap(cr => Some(cr._1))).getOrElse("")
        case _ =>
          SystemDefinedRoles.byRoleType(roleDetails._1).get.name
      }
    }

    def getByAccountId(roleDetails: (Int, Option[Long]), customRoleIdVsNameMap: Map[Long, (String, Long)]): Option[Long] = {
      roleDetails._1 match {
        case SystemDefinedRoles.CUSTOMROLE.roleType =>
          roleDetails._2.flatMap(role => customRoleIdVsNameMap.get(role).flatMap(cr => Some(cr._2)))
        case _ => None
      }
    }

    (for {
      _ <- v2Validator.isAccountV2Provisioned(Set(creator.accountId))
      _ <- v2Validator.validatePermissions(creator.accountId, creator.userId, Set(DashboardUserPermissions.USERS_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      res <- daoAccountV2.getUserDetailsV2(userId, creator.accountId) flatMap {
        case Some(userDetails) =>
          daoAccountV2.getAccountIdVsAssociationId(userId, creator.accountId) flatMap { accountIdVsAssociationIdMap =>
            if (accountIdVsAssociationIdMap.nonEmpty) {
              daoAccountV2.getAssociationIdVsRoles(accountIdVsAssociationIdMap.values.toSet) flatMap { associationIdVsRoles =>
                daoAccountV2.getRoleIdVsRoleNameAndByAccountId(associationIdVsRoles.mapValues(_.filter(_._1 == SystemDefinedRoles.CUSTOMROLE.roleType).map(_._2.get)).values.flatten.toSet) flatMap { customRoleIdVsNameAccountIDMap =>
                  daoAccountV2.getAccountIdVsName(accountIds = accountIdVsAssociationIdMap.keySet) map { accountIdVsNameMap =>
                    val accountIdVsRolesMap = accountIdVsAssociationIdMap.map(entry => entry._1 -> associationIdVsRoles.get(entry._2).map(roleDetails => roleDetails.map(roleDetail => (UserRole(roleDetail._2, getRoleName(roleDetail, customRoleIdVsNameAccountIDMap), None, roleDetail._1, getByAccountId(roleDetail, customRoleIdVsNameAccountIDMap))))).getOrElse(Seq.empty))
                    val result = AccountConvertors.toDashboardUserWithAssociations(
                      UserDetails(userId, userDetails._1, userDetails._2, userDetails._3, userDetails._4),
                      accountIdVsNameMap,
                      accountIdVsRolesMap
                    )
                    Right(result)
                  }
                }
              }
            } else {
              Future.successful(Left(ErrorResponseFactory.get(InvalidUserAccountAssociation)))
            }
          }
        case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user details with roles", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not fetch user details with roles", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
    }
  }

  def getUserWithRolesV1(userId: Long): Future[Either[ErrorResponse, DashboardUserWithRoles]] = {
    daoBusinessUser.getUser(userId) flatMap {
      case Some(u) => daoAccount.getAccount(u.accountId) flatMap {
        case Some(a) =>
          daoEnvironment.getEnvironmentsByAccountId(a.accountId) flatMap { env =>
            daoBusinessUser.getEnvironmentUserRolesByUserId(userId) map {
              case roles if roles.nonEmpty =>
                val commonRoles: Set[Int] = roles.filter(r => DashboardUserRole.commonRoles.contains(r.role)).map(_.role).toSet
                val envRoles = env.map(_.id).
                  map(envId =>
                    EnvironmentRoles(envId,
                      roles.filter {
                        _.environmentId.contains(envId)
                      }.filterNot(er => DashboardUserRole.commonRoles.contains(er.role)).map(_.role).toSet
                    )
                  )
                val userInfo = DashboardUserV2(u.id, u.firstName, u.lastName, u.email, a.name, u.contactNumber, u.isPrimaryUser, !u.accountNonLocked)
                Right(DashboardUserWithRoles(userInfo, commonRoles, envRoles))
              case _ => Left(ErrorResponseFactory.get(RolesNotFound))
            }
          }
        case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
      }
      case _ => Future.successful(Left(ErrorResponseFactory.get(UserNotFound)))
    }
  }

  def getUserWithRolesV2(userId: Long, accountWithCreator: AccountWithCreator): Future[Either[ErrorResponse, DashboardUserWithRoles]] = {
    val response = for {
      _ <- v2Validator.isValidUser(accountWithCreator.accountId, Some(userId))
      _ <- v2Validator.validateAccountAccess(accountWithCreator.accountId, accountWithCreator.creator.accountId)
      _ <- v2Validator.validatePermissions(accountWithCreator.creator.accountId, accountWithCreator.creator.userId, Set(DashboardUserPermissions.USERS_VIEW.id), EnvironmentTypes.GLOBAL_ENVIRONMENT.id)
      rootAccountType <- v2Validator.getRootParentAccountType(accountWithCreator.accountId)
      user <- daoBusinessUser.getUser(userId)
      accountEnv <- daoAccountV2.getAccountEnvironments(accountWithCreator.accountId)
      userAccountAssociation <- daoAccountV2.getUserAccountAssociation(userId, accountWithCreator.accountId)
      customRoles <- daoAccountV2.getDashboardUserPermissions(userAccountAssociation.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))).id)
      systemRoles <- daoAccountV2.getDashboardUserSystemPermissions(userAccountAssociation.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(AccessForbidden))).id, rootAccountType)
    } yield (user, accountEnv, userAccountAssociation, customRoles, systemRoles)
    response map {
      case (userOpt, accountEnv, userAccountAssociation, customRoles, systemRoles) =>
        userOpt match {
          case Some(user) =>
            val userInfo = DashboardUserV2(user.id, user.firstName, user.lastName, user.email, accountEnv._1.name, user.contactNumber, userAccountAssociation.get.isPrimaryUser, !user.accountNonLocked)
            val commonRoles = AccountConvertors.toDashboardUserRoles(customRoles.getOrElse(EnvironmentTypes.GLOBAL_ENVIRONMENT.id, Set.empty[Int]) ++ systemRoles.getOrElse(EnvironmentTypes.GLOBAL_ENVIRONMENT.id, Set.empty[Int]))
            val envRoles = accountEnv._2.map(env => EnvironmentRoles(env.id, AccountConvertors.toDashboardUserRoles(customRoles.getOrElse(env.environmentType.toInt, Set.empty[Int]) ++ customRoles.getOrElse(env.environmentType.toInt, Set.empty[Int]))))
            Right(DashboardUserWithRoles(userInfo, commonRoles, envRoles))
          case None => Left(ErrorResponseFactory.get(UserNotFound))
        }
      case _ =>
        logger.info("Could not fetch user with roles")
        Left(ErrorResponseFactory.get(UnknownError))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user with roles ", ex)
        Left(ex.errorResponse)
      case e: Throwable =>
        logger.info("Could not fetch user with roles", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getUserIdByUsername(username: String): Future[Either[ErrorResponse, Long]] = {
    daoBusinessUser.getIdByEmail(username) map {
      case Some(userId) if userId != 0 => Right(userId)
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    }
  }


  private def containsAdminRoles[A](a: List[Set[A]], b: Set[A]): Boolean = {
    a.exists(e =>
      b.exists(e.contains))
  }

  private def subAccountRoles[A](a: List[Set[A]], b: Set[A]): Boolean = {
    !a.exists { e =>
      e.diff(b).nonEmpty
    }
  }

  def getPublicAccountIdByUserName(username: String): Future[Either[ErrorResponse, String]] = {
    daoBusinessUser.getPublicAccountIdByUserName(username) map {
      case Some(publicAccountId) if publicAccountId.trim.nonEmpty => Right(publicAccountId)
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    }
  }

  def getUsersFilteredByPermissions(permissionFilterReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]] = {
    val crit = permissionFilterReq.permissionCriteria
    val accountIds = permissionFilterReq.accountIds
    val envIds = permissionFilterReq.environmentIds.getOrElse(Seq.empty[Int])
    if (envIds.isEmpty) {
      envIds.map(EnvironmentConstants.values.map(_.id))
    }
    val pageNo = permissionFilterReq.page
    val size = permissionFilterReq.size
    try {
      val checkPermissions = permissionFilterReq.permissions.map(p => DashboardUserPermissions.byName(p) match {
        case Some(perm) => perm.id
        case _ => logger.info("Invalid permission check string")
          throw new Exception("Invalid permission check string")
      })
      if (checkPermissions.nonEmpty && accountIds.nonEmpty) {
        logger.info(s"Get users filtered for permissions: ${checkPermissions.mkString(",")}; for accountIds: ${accountIds.mkString(",")}; Criteria: ${crit}")
        val validUsersList = (for {
          validEnvGroupedUsersCustomPermList <- daoAccountV2.fetchUsersAndCustomRolePermissions(accountIds, envIds)
          validEnvGroupedUsersSystemPermList <- daoAccountV2.fetchUsersAndSystemRolesPermissions(accountIds)
          rootAccountTypesMap <- v2Validator.getRootParentAccountTypes(accountIds)
        } yield (validEnvGroupedUsersCustomPermList, validEnvGroupedUsersSystemPermList, rootAccountTypesMap)).map {
          case (validEnvGroupedUsersCustomPermList, validUsersSystemPermList, rootAccTypes) =>
            validUsersSystemPermList.flatMap { p =>
              envIds.map(env =>
                DtoUserDetailsByPermission(
                  id = p.id,
                  firstName = p.firstName,
                  lastName = p.lastName,
                  email = p.email,
                  accountId = p.accountId,
                  envType = env,
                  permissions = Some(SystemDefiendRolesPermissions.sysDefinedRolesPermissions(rootAccTypes.get(p.accountId), p.roleType, env))
                )
              )
            } ++ validEnvGroupedUsersCustomPermList
        }

        val validEnvGroupedUsersList = validUsersList.map(res => res.filter(r => isValidUserForPermission(permissions = r.permissions, criteria = crit, checkPermissions = checkPermissions)).map(r => addMatchedPermissions(userByPermissionObj = r, criteria = crit, checkPermissions = checkPermissions))).map(_.groupBy(_.envType))

        val accountEnvGroupedUsers = validEnvGroupedUsersList.map { u =>
          u.keys.map(env => env -> u.get(env).map(_.groupBy(_.accountId))).toMap
        }

        accountEnvGroupedUsers.map(users => AccountConvertors.toUserDetailsbyPermission(users, envIds).sortBy(u => u.id)).map {
          case resp =>
            val totalSize = resp.length
            if ((pageNo.isDefined && pageNo.get > 0) && (size.isDefined && size.get > 0)) {
              logger.info(s"Get users pageNo : ${pageNo}, size: ${size}")
              val startIndex = (pageNo.get - 1) * size.get
              val endIndex = startIndex + size.get
              Right(AccountConvertors.toUsersByPermissionFilterResponse(resp.slice(startIndex, endIndex), pageNo, size, totalSize))
            } else {
              Right(AccountConvertors.toUsersByPermissionFilterResponse(resp, pageNo, size, totalSize))
            }
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
        }
      } else {
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
    } catch {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user details by permission filter", ex)
        Future.successful(Left(ex.errorResponse))
      case e: Exception =>
        logger.info("Could not fetch user details by permission filter", e)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
    }
  }

  private def isValidUserForPermission(permissions: Option[String], criteria: Int, checkPermissions: Seq[Int]): Boolean = {
    permissions match {
      case Some(p) if p.nonEmpty =>
        val permissionsSet = p.split(",").toSet.map { a: String => a.trim.toInt }
        criteria match {
          case c: Int if c == FilterMatchCriteria.All.id => checkPermissions.toSet.subsetOf(permissionsSet)
          case c: Int if c == FilterMatchCriteria.Any.id => checkPermissions.exists(permission => permissionsSet.contains(permission))
          case _ => logger.info("Invalid filter match criteria type")
            throw new Exception("Invalid filter match criteria type")
        }
      case _ => false
    }
  }

  private def addMatchedPermissions(userByPermissionObj: DtoUserDetailsByPermission, criteria: Int, checkPermissions: Seq[Int]): DtoUserDetailsByPermission = {
    if (criteria == FilterMatchCriteria.Any.id) {
      userByPermissionObj.permissions match {
        case Some(p) if p.nonEmpty =>
          val permissionsSet = p.split(",").toSet.map { a: String => a.trim.toInt }
          val matchedPermissions = checkPermissions.filter(chkPerm => permissionsSet.contains(chkPerm)).map(matchedPerm => DashboardUserPermissions.byId(matchedPerm).get.name)
          userByPermissionObj.copy(matchedPermissions = Some(matchedPermissions))
        case _ => userByPermissionObj
      }
    } else {
      userByPermissionObj
    }
  }

  private def addMatchedPermissionsCaseMgmt(userByPermissionObj: DtoUserDetailsByPermission, criteria: Int, checkPermissions: Seq[Int]): DtoUserDetailsByPermission = {
    if (criteria == FilterMatchCriteria.Any.id) {
      userByPermissionObj.permissions match {
        case Some(p) if p.nonEmpty =>
          val permissionsSet = p.split(",").toSet.map { a: String => a.trim.toInt }
          val matchedPermissions = checkPermissions.filter(chkPerm => permissionsSet.contains(chkPerm)).map(matchedPerm => CaseManagementPermissions.getPermissionNameById(matchedPerm).get)
          userByPermissionObj.copy(matchedPermissions = Some(matchedPermissions))
        case _ => userByPermissionObj
      }
    } else {
      userByPermissionObj
    }
  }

  def getUserBasicDetails(userDetailsReq: GetUserDetailsRequest): Future[Either[ErrorResponse, Seq[UserDetails]]] = {
    try {
      val userIds = userDetailsReq.userIds
      if (userIds.nonEmpty) {
        daoBusinessUser.getUsersByIds(userIds).map {
          case users if users.nonEmpty => Right(users.map { u =>
            UserDetails(
              id = u.id,
              firstName = u.firstName,
              lastName = u.lastName,
              email = u.email,
              contactNumber = u.contactNumber
            )
          })
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
        }
      } else {
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
    } catch {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user details", ex)
        Future.successful(Left(ex.errorResponse))
      case e: Exception =>
        logger.info("Could not fetch user details", e)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
    }
  }

  def getUsersFilteredByPermissionsForCasemgmt(permissionFilterReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]] = {
    val crit = permissionFilterReq.permissionCriteria
    val accountIds = permissionFilterReq.accountIds
    val envIds = permissionFilterReq.environmentIds.getOrElse(Seq.empty[Int])
    if (envIds.isEmpty) {
      envIds.map(EnvironmentConstants.values.map(_.id))
    }
    val pageNo = permissionFilterReq.page
    val size = permissionFilterReq.size
    try {
      val checkPermissions = permissionFilterReq.permissions.map(p => CaseManagementPermissions.getPermissionIdByName(p) match {
        case Some(permId) => permId
        case _ => logger.info("Invalid permission check string")
          throw new Exception("Invalid permission check string")
      })
      if (checkPermissions.nonEmpty && accountIds.nonEmpty) {
        logger.info(s"Get users filtered for permissions: ${checkPermissions.mkString(",")}; for accountIds: ${accountIds.mkString(",")}; Criteria: ${crit}")
        val validUsersList = (for {
          validEnvGroupedUsersCustomPermList <- daoAccountV2.fetchUsersAndCustomRolePermissions(accountIds, envIds)
          validEnvGroupedUsersSystemPermList <- daoAccountV2.fetchUsersAndSystemRolesPermissions(accountIds)
          rootAccountTypesMap <- v2Validator.getRootParentAccountTypes(accountIds)
        } yield (validEnvGroupedUsersCustomPermList, validEnvGroupedUsersSystemPermList, rootAccountTypesMap)).map {
          case (validEnvGroupedUsersCustomPermList, validUsersSystemPermList, rootAccTypesMap) =>
            validUsersSystemPermList.flatMap { p =>
              envIds.map(env =>
                DtoUserDetailsByPermission(
                  id = p.id,
                  firstName = p.firstName,
                  lastName = p.lastName,
                  email = p.email,
                  accountId = p.accountId,
                  envType = env,
                  permissions = Some(SystemDefiendRolesPermissions.sysDefinedRolesPermissions(rootAccTypesMap.get(p.accountId), p.roleType, env))
                )
              )
            } ++ validEnvGroupedUsersCustomPermList
        }

        val validEnvGroupedUsersList = validUsersList.map(res => res.filter(r => isValidUserForPermission(permissions = r.permissions, criteria = crit, checkPermissions = checkPermissions)).map(r => addMatchedPermissionsCaseMgmt(userByPermissionObj = r, criteria = crit, checkPermissions = checkPermissions))).map(_.groupBy(_.envType))

        val accountEnvGroupedUsers = validEnvGroupedUsersList.map { u =>
          u.keys.map(env => env -> u.get(env).map(_.groupBy(_.accountId))).toMap
        }

        accountEnvGroupedUsers.map(users => AccountConvertors.toUserDetailsbyPermissionForCaseMgmt(users, envIds).sortBy(u => u.id)).map {
          case resp =>
            val totalSize = resp.length
            if ((pageNo.isDefined && pageNo.get > 0) && (size.isDefined && size.get > 0)) {
              logger.info(s"Get users pageNo : ${pageNo}, size: ${size}")
              val startIndex = (pageNo.get - 1) * size.get
              val endIndex = startIndex + size.get
              Right(AccountConvertors.toUsersByPermissionFilterResponse(resp.slice(startIndex, endIndex), pageNo, size, totalSize))
            } else {
              Right(AccountConvertors.toUsersByPermissionFilterResponse(resp, pageNo, size, totalSize))
            }
          case _ => Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
        }
      } else {
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
      }
    } catch {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user details by permission filter", ex)
        Future.successful(Left(ex.errorResponse))
      case e: Exception =>
        logger.info("Could not fetch user details by permission filter", e)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
    }
  }

  def isQuicksightUser(userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.getQuicksightUserStatus(userId) map {
      case Some(quicksightUserStatus) => Right(quicksightUserStatus)
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    }
  }

  def updateQuicksightUserStatus(updateQuicksightUserStatus: UpdateQuicksightUserStatus): Future[Either[ErrorResponse, Boolean]] = {
    daoBusinessUser.updateQuicksightUserStatus(updateQuicksightUserStatus.userId, updateQuicksightUserStatus.status) map {
      case updateStatus => Right(updateStatus)
      case _ => Left(ErrorResponse(UserNotFound.id, UserNotFound.description))
    } recover {
      case e: Exception =>
        logger.info("Could not update quicksight user status", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
    }
  }

  def getUserWithRolesAndAssociationsWithoutCreator(user: DashboardUserV2, sponsorBankId: Long): Future[Either[ErrorResponse, DashboardUserV2]] = {


    (for {
      res <- daoAccountV2.getAccountIdVsAssociationId(user.id, sponsorBankId) flatMap { accountIdVsAssociationIdMap =>
        val parentAccountAssociation = accountIdVsAssociationIdMap.filter(_._1 == sponsorBankId)
        if (parentAccountAssociation.nonEmpty) {
          daoAccountV2.getAssociationIdVsRoles(parentAccountAssociation.values.toSet) flatMap { associationIdVsRoles =>
            val bsaAssociationRoles = associationIdVsRoles.map(role => role._2.filter(details => details._1 == SystemDefinedRoles.BSA_OFFICER.roleType))
            val bsaAssociationRolesSeq = bsaAssociationRoles.toSeq.flatten
            if (bsaAssociationRolesSeq.nonEmpty) {
              Future.successful(Right(user))
            } else {
              Future.successful(Left(ErrorResponseFactory.get(InvalidUserAccountAssociation)))
            }
          }
        } else {
          Future.successful(Left(ErrorResponseFactory.get(InvalidUserAccountAssociation)))
        }
      }
    } yield res) recover {
      case ex: ErrorResponseException =>
        logger.info("Could not fetch user details with roles", ex)
        Left(ex.errorResponse)
      case e: Exception =>
        logger.info("Could not fetch user details with roles", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound))
    }
  }

  def getSponsorBankUsers(sponsorBankAccountId: Long): Future[Either[ErrorResponse, Vector[Option[DashboardUserV2]]]] = {
    getUsers(sponsorBankAccountId) flatMap {
      case Right(dashboardUserV2Seq) =>
        val result = Future.sequence {
          dashboardUserV2Seq map { dashboardUserV2 =>
            getUserWithRolesAndAssociationsWithoutCreator(dashboardUserV2, sponsorBankAccountId) map {
              case Right(user) =>
                Some(user)
              case _ =>
                None
            }
          }
        }
        result.map(Right(_))
      case _ =>
        logger.info("Could not list users")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }

  }

  def updateUserTOS(userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    daoAccount.userExistsInTOSTable(userId) flatMap{
      case false => daoAccount.updateTOSAgreement(userId) map{
        case result if result > 0 => Right(true)
        case _ => Left(ErrorResponse(ExceptionCodes.UnknownError.id, ExceptionCodes.UnknownError.description))
      }
      case true => Future.successful(Left(ErrorResponse(ExceptionCodes.EmailAlreadyExists.id, ExceptionCodes.EmailAlreadyExists.description)))
    }
  }

}