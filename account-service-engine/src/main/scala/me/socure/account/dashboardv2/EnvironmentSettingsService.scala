package me.socure.account.dashboardv2

import me.socure.DaoAccount
import me.socure.account.service.AuditDetailsService
import me.socure.util.ProductSettingsDeltaUtil.{formProductSettingsDeltaMapForField, getImplicits}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.utils.GenerateKeys
import me.socure.account.validator.V2Validator
import me.socure.common.clock.Clock
import me.socure.common.exception.ErrorResponseException
import me.socure.common.ipvalidator.IPAddressValidator
import me.socure.configuration.ApiKeyRenewalConfig
import me.socure.constants.ProductSettingsFields.API_ALLOWED_DOMAINS
import me.socure.constants.{DashboardUserPermissions, EnvironmentConstants, KeyProviders}
import me.socure.convertors.AccountConvertors
import me.socure.dashboard.NewAccountSettings
import me.socure.mapping.ApiKeyMapper
import me.socure.model.ErrorResponse
import me.socure.model.account.ApiKeyStatus.ApiKeyStatus
import me.socure.model.account._
import me.socure.model.dashboardv2._
import me.socure.storage.slick.dao.{DaoAccountV2, DaoEnvironment}
import me.socure.storage.slick.tables.account.{DtoAccount, DtoApiKey, DtoEnvironment, DtoPublicApiKey}
import me.socure.storage.slick.tables.accountsocialkeys.DtoEnvironmentSocialKeys
import org.joda.time.{DateTime, Minutes}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 8/25/16.
  */
class EnvironmentSettingsService(
                                  val daoEnvironment: DaoEnvironment,
                                  daoAccount: DaoAccount,
                                  daoAccountV2: DaoAccountV2,
                                  clock: Clock,
                                  apiKeyRenewalConfig: ApiKeyRenewalConfig,
                                  v2Validator: V2Validator,
                                  auditDetailsService: AuditDetailsService)(implicit ec: ExecutionContext) {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  def getEnvironmentSettingsWithApiKeys(accountId : Long, creator: Option[Creator], permissionChkForTransaction : Boolean = false)  : Future[Either[ErrorResponse, NewAccountSettings]] = {
    daoAccount.getAccount(accountId) flatMap {
      case Some(account) =>
        v2Validator.isValidV2AccountRequestExtd(accountId, creator) flatMap {
          case true =>
            for {
              _ <- v2Validator.validateAccountAccess(accountId, creator.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))).accountId)
              envs <- v2Validator.getEnvironments(creator, permissionChkForTransaction)
              env <- getEnvironments(account, envs.toSet)
            } yield Right(env)
          case false => getEnvironments(account).map(Right.apply)
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Exception while getting environments for Account:$accountId, Creator: $creator", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Unable to get environments for Account:$accountId, Creator: $creator", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getEnvironmentSettingsWithApiKeysDev(accountId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, NewAccountSettings]] = {
    daoAccount.getAccount(accountId) flatMap {
      case Some(account) =>
        v2Validator.isValidV2AccountRequestExtd(accountId, creator) flatMap {
          case true =>
            for {
              _ <- v2Validator.validateAccountAccess(accountId, creator.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))).accountId)
              envs <- v2Validator.getEnvironments(Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id), creator)
              env <- getEnvironments(account, envs.toSet)
            } yield Right(env)
          case false => getEnvironments(account).map(Right.apply)
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Exception while getting environments for Account:$accountId, Creator: $creator", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Unable to get environments for Account:$accountId, Creator: $creator", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  private def getEnvironments(account: DtoAccount): Future[NewAccountSettings] = {
    daoEnvironment.getEnvironmentsWithApiKeysByAccountId(account.accountId) flatMap {
      case envs if envs.nonEmpty =>
        daoEnvironment.getSocialNetworkKeysByEnvId(envs.map(_._1.id).toSet)
          .map(keys => getEnvironmentDetails(account, envs, keys))
    }
  }

  private def getEnvironments(account: DtoAccount, environmentTypes: Set[Int]): Future[NewAccountSettings] = {
    daoEnvironment.getEnvironmentsWithApiKeysByAccountId(account.accountId) flatMap {
      case envs if envs.nonEmpty =>
        val provisionedEnvs = envs.filter(e => environmentTypes.contains(e._1.environmentType.toInt))
        daoEnvironment.getSocialNetworkKeysByEnvId(provisionedEnvs.map(_._1.id).toSet)
          .map(keys => getEnvironmentDetails(account, provisionedEnvs, keys))
    }
  }

  def formAuditDetails(isSuccess: Boolean, accountId: Option[Long], environmentId: Option[Long], creator: Option[Creator], errorResponse: Option[ErrorResponse], existingDomain: List[String], newDomains: List[String]): Future[AuditDetails] = {
    implicit val (_, _, implicit_3) = getImplicits
    val productSettingDeltaMap = formProductSettingsDeltaMapForField(API_ALLOWED_DOMAINS, existingDomain.toSet, newDomains.toSet)
    val productSettingDelta = auditDetailsService.formProductSettingsDelta(accountId, environmentId, "General", productSettingDeltaMap)
    auditDetailsService.formAuditDetails(isSuccess, creator, None, errorResponse, productSettingDelta)
  }

  def updateDomain(updateDomain: EnvironmentDomain): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    if(IPAddressValidator.validateWithoutIpv6(updateDomain.domain.toSet)) {
      v2Validator.isValidV2EnvironmentRequest(updateDomain.id, updateDomain.creator, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id)) flatMap {
        case true =>
          daoEnvironment.getEnvironment(updateDomain.id) flatMap {
            case env =>
              val existingDomain: List[String] = if (env.nonEmpty) env.head.domain.map(_.split(",").toList).getOrElse(List.empty) else List.empty[String]
              daoEnvironment.updateDomain(updateDomain.id, updateDomain.domain) flatMap {
                case row if row > 0 =>
                  formAuditDetails(true, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, None, existingDomain, updateDomain.domain) map {
                    audit => (audit, Right(true))
                  }
                case _ =>
                  val error = ErrorResponseFactory.get(EnvironmentNotFound)
                  formAuditDetails(false, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, Some(error), existingDomain, updateDomain.domain) map {
                    audit => (audit, Left(error))
                  }

              }
          }

        case _ => logger.info("Could not update domain")

          val error = ErrorResponseFactory.get(UnknownError)
          formAuditDetails(false, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, Some(error), List.empty, updateDomain.domain) map {
            audit => (audit, Left(error))
          }
      } recoverWith {
        case ex: ErrorResponseException =>
          logger.info("Could not update domain", ex)
          val error = ex.errorResponse
          formAuditDetails(false, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, Some(error), List.empty, updateDomain.domain) map {
            audit => (audit, Left(error))
          }
        case e : Throwable =>
          logger.info("Could not update domian", e)
          val error = ErrorResponseFactory.get(UnknownError)
          formAuditDetails(false, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, Some(error), List.empty, updateDomain.domain) map {
            audit => (audit, Left(error))
          }
      }
    } else {
      val error = ErrorResponseFactory.get(DomainNotValid)
      formAuditDetails(false, updateDomain.creator.map(_.accountId), Some(updateDomain.id), updateDomain.creator, Some(error), List.empty, updateDomain.domain) map {
        audit => (audit, Left(error))
      }
    }
  }

  def deletedSocialNetworkKeys(socialId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Boolean]] = {
    v2Validator.isValidV2SocialIdRequest(socialId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoEnvironment.deleteSocialNetworkKeys(socialId) map {
        case row if row > 0 => Right(true)
        case _ => Left(ErrorResponseFactory.get(SocialKeyIdNotFound))
      }
      case _ =>
        logger.info("Could not delete social network keys")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not delete social network keys", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not delete social network keys", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def upsertSocialNetworkKeys(socialNetworkAppKeys : SocialNetworkAppKeys) : Future[Either[ErrorResponse, Long]] = {
    v2Validator.isValidV2EnvironmentRequest(socialNetworkAppKeys.environment, socialNetworkAppKeys.creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoEnvironment.upsertSocialNetworkKeys(AccountConvertors.getDtoAccountKeys(socialNetworkAppKeys)) map {
        case socialkeyId if socialkeyId > 0 => Right(socialkeyId)
        case _ => Left(ErrorResponseFactory.get(EnvironmentNotFound))
      }
      case _ => logger.info("Could not upsert social key")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not upsert social network keys", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not upsert social key", e)
        Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def getAccountEnvironmentList(accountId : Long) : Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
    daoEnvironment.getEnvironmentsByAccountId(accountId) map {
      case env if env.nonEmpty => Right(env.map(e => EnvironmentNameAndId(e.id, EnvironmentConstants(e.environmentType.toInt).toString)))
      case _ => Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def getEnvironmentListByAccountIds(accountIds: Seq[Long], envTypeIds: Seq[Long]): Future[Either[ErrorResponse, Map[Long, Seq[Long]]]] = {
    daoEnvironment.getEnvironmentsByAccountIds(accountIds, envTypeIds) map {
      case env if env.nonEmpty =>
        Right(env.groupBy(_.accountId).map { case (k, v) => k -> v.map(_.id) })
      case _ => Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def getAccountEnvironmentV1(accountId : Long, creator: Creator, role: Int) : Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
    val envs = if(accountId == creator.accountId) daoEnvironment.getEnvironmentsWithRoleByAccountId(accountId, creator.userId, role)
    else daoEnvironment.getEnvironmentsForSubAccountWithRoleByAccountId(accountId, creator.accountId, creator.userId, role)
    envs map {
      case env if env.nonEmpty =>
        Right(env.map(e => EnvironmentNameAndId(e.id, EnvironmentConstants(e.environmentType.toInt).toString)))
      case _ => Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  def getEnvironments(accountId : Long, legacyRole: Int, creator: Option[Creator]): Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
     val permissions = AccountConvertors.toDashboardPermissions(Set(legacyRole))
      v2Validator.isValidV2AccountRequestExtd(accountId, creator) flatMap {
        case true =>
          for {
            _ <- v2Validator.validateAccountAccess(accountId, creator.getOrElse(throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))).accountId)
            envTypes <- v2Validator.getEnvironments(permissions, creator)
            envs <- daoAccountV2.getEnvironments(accountId, envTypes)
          } yield Right(envs.map(e => EnvironmentNameAndId(e.id, EnvironmentConstants(e.environmentType.toInt).toString)))
        case false =>
          creator match {
            case Some(c) =>
              daoAccountV2.isV1PrimaryUser(c.userId) flatMap {
                case Some(isPrimary) =>
                  if(isPrimary) getAccountEnvironmentList(accountId)
                  else getAccountEnvironmentV1(accountId, c, legacyRole)
                case None => getAccountEnvironmentV1(accountId,c, legacyRole)
              }
            case None =>
              logger.info(s"Creator details are missing")
              throw ErrorResponseException(ErrorResponseFactory.get(CreatorDetailsNotAvailable))
          }
      } recover {
        case ex: ErrorResponseException =>
          logger.info(s"Exception while getting environments for Account:$accountId, Creator: $creator", ex)
          Left(ex.errorResponse)
        case e : Throwable =>
          logger.info(s"Unable to get environments for Account:$accountId, Creator: $creator", e)
          Left(ErrorResponseFactory.get(UnknownError))
      }
  }

  def getEnvironmentWithDomains(accountId : Long) : Future[Either[ErrorResponse, Seq[EnvironmentWithDomains]]] = {
    daoEnvironment.getEnvironmentsByAccountId(accountId) map {
      case envs if envs.nonEmpty => Right(envs.collect {
        case e if !e.domain.forall(_.isEmpty) =>  EnvironmentWithDomains(e.id, EnvironmentConstants(e.environmentType.toInt).toString, e.domain.get)
      })
      case _ => Left(ErrorResponseFactory.get(EnvironmentNotFound))
    }
  }

  private def getEnvironmentDetails(account: DtoAccount,
                                    environmentWithApiKeys: Map[DtoEnvironment, (Seq[DtoApiKey], Seq[DtoPublicApiKey])],
                                    socialKeys : Seq[DtoEnvironmentSocialKeys]): NewAccountSettings = {
    val deprecationHours = apiKeyRenewalConfig.drepcateDurationInHours
    val renewalTimeInHours = apiKeyRenewalConfig.renewalTimeInHours
    val envList = environmentWithApiKeys map { e =>
      val timeLeft = ApiKey.timeLeftForRenewal(deprecationHours, renewalTimeInHours, e._1.updatedAt, clock)
      val canRenewApiKeyFlag = ApiKey.canBeRenewed(renewalTimeInHours = renewalTimeInHours,
        updatedAt = e._1.updatedAt, availableApiKeys = e._2._1.size, clock = clock)

      val apiKeys = e._2._1.map(k => {
        if(e._2._1.size>1 && k.status.equals(ApiKeyStatus.ACTIVE)) ApiKeyMapper.toViewWithTimeLeft(k, timeLeft)
          else ApiKeyMapper.toView(k)
      }).toList

      val publicApiKeys = e._2._2.map(k => {
        if(e._2._2.size>1 && k.status.equals(ApiKeyStatus.ACTIVE)) ApiKeyMapper.toViewWithTimeLeft(k, timeLeft)
        else ApiKeyMapper.toView(k)
      }).toList

      val timeRemainingtoRenew = e._1.updatedAt match {
        case Some(updateTime) =>  Some((renewalTimeInHours*60) - Minutes.minutesBetween(updateTime, clock.now()).getMinutes)
        case None => None
      }
      NewEnvironment(
        id = e._1.id,
        name = EnvironmentConstants(e._1.environmentType.toInt).toString,
        domain = e._1.domain.fold(Set.empty[String])(d => d.split(",").toSet),
        accessCredentials = NewAccessCredentials(apiKeys, publicApiKeys, e._1.secretKey,e._1.accessToken, e._1.accessTokenSecret, ""),
        socialAccounts = getSocialKeys(e._1.accountId, e._1.id, socialKeys.filter(_.environmentId == e._1.id)).toSeq,
        invidiualCache = Seq.empty,
        overallCache = None,
        canRenewApiKey =  Some(canRenewApiKeyFlag),
        timeLeftToRenew = timeRemainingtoRenew
      )
    }
    NewAccountSettings(accountId = account.accountId, name = account.name, environments = envList.toList)
  }

  private def getSocialKeys(accountId : Long, envId : Long, social : Seq[DtoEnvironmentSocialKeys]) : Set[SocialNetworkAppKeys] = {
    KeyProviders.values map { keys =>
      social.exists(_.network == keys.id) match {
        case true =>
          val filteredNetwork = social.filter(_.network == keys.id) match {
            case hd +: _ => hd
            case _ => throw new Exception("Failed to retrieve matching filtered social network")
          }
          SocialNetworkAppKeys(filteredNetwork.id, keys.toString, filteredNetwork.applicationKey.getOrElse(""), filteredNetwork.applicationSecret.getOrElse(""), filteredNetwork.environmentId, accountId)
        case false => SocialNetworkAppKeys(id = 0, provider = keys.toString, appkey = "", appsecret = "", environment = envId, accountId = accountId)
      }
    }
  }

  def generateApiKey(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Int]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoEnvironment.canAddNewApiKey(envId, clock, apiKeyRenewalConfig.renewalTimeInHours) flatMap {
        case s if(s) =>
          insertApiKey(envId)
        case _ => Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
      }
      case _ => logger.info("Could not Generate Api Key")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not Generate Api Key", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not Generate Api Key", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def generateApiKeyDev(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Int]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id)) flatMap {
      case true => daoEnvironment.canAddNewApiKey(envId, clock, apiKeyRenewalConfig.renewalTimeInHours) flatMap {
        case s if(s) =>
          insertApiKey(envId)
        case _ => Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
      }
      case _ => logger.info("Could not Generate Api Key")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not Generate Api Key", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not Generate Api Key", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def generatePublicApiKey(envId : Long) : Future[Either[ErrorResponse, Long]] = {
    daoEnvironment.canAddNewPublicApiKey(envId, clock, apiKeyRenewalConfig.renewalTimeInHours) flatMap {
      case s if(s) =>
        insertPublicApiKey(envId)
      case _ => Future.successful(Left(ErrorResponseFactory.get(PublicApiKeyCannotBeRenewed)))
    }
  }

  def generateApiKey(envId : Long, staticApiKey : String) : Future[Either[ErrorResponse, Int]] = {
    val renewalTimeInHours = apiKeyRenewalConfig.renewalTimeInHours
    daoEnvironment.canAddNewApiKey(envId, clock, renewalTimeInHours) flatMap {
      case s if(s) =>
        insertApiKey(envId, staticApiKey)
      case _ => Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    }
  }

  private def insertApiKey(envId : Long, apiKeyStatus: ApiKeyStatus = ApiKeyStatus.NEW) : Future[Either[ErrorResponse, Int]] = {
    val ts = clock.now()
    val apiKey = ApiKey(0, envId, GenerateKeys.getApiKey, apiKeyStatus, ts, ts, None)
    daoEnvironment.insertApiKey(ApiKeyMapper.toDTO(apiKey), clock) map {
      case row if(row>=1) => Right(row)
      case _ => Left(ErrorResponseFactory.get(ApiKeyNotInserted))
    } recover {
      case e : Throwable =>
        logger.info("Could not insert api key", e)
        Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed))
    }
  }

  private def insertPublicApiKey(envId : Long, apiKeyStatus: ApiKeyStatus = ApiKeyStatus.NEW) : Future[Either[ErrorResponse, Long]] = {
    val ts: DateTime = clock.now()

    val publicApiKey: PublicApiKey = PublicApiKey(0, envId, GenerateKeys.getApiKey, apiKeyStatus, ts, ts, None)
    daoEnvironment.insertPublicApiKey(ApiKeyMapper.toDTO(publicApiKey), clock) map {
      case row if (row >= 1L) => Right(row)
      case _ => Left(ErrorResponseFactory.get(PublicApiKeyNotInserted))
    } recover {
      case e : Throwable =>
        logger.info("Could not insert public api key", e)
        Left(ErrorResponseFactory.get(PublicApiKeyCannotBeRenewed))
    }
  }

  def insertApiKey(envId : Long, staticApiKey: String) : Future[Either[ErrorResponse, Int]] = {
    val ts = clock.now()
    val apiKey = ApiKey(0, envId, GenerateKeys.getApiKey(staticApiKey), ApiKeyStatus.NEW, ts, ts, None)
    daoEnvironment.insertApiKey(ApiKeyMapper.toDTO(apiKey), clock) map {
      case row if(row>=1) => Right(row)
      case _ => Left(ErrorResponseFactory.get(ApiKeyNotInserted))
    } recover {
      case e : Throwable =>
        logger.info("Could not insert api key", e)
        Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed))
    }
  }

  def changeApiKeyStatus(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[Int]]]= {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
      case true => daoEnvironment.changeApiKeyStatus(envId, clock) map {
        case row: Seq[Int] => Right(row)
        case _ => Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged))
      }
      case _ => logger.info("Could not change Api Key status")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not change Api Key status", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not change Api Key status", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def changeApiKeyStatusDev(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[Int]]]= {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id)) flatMap {
      case true => daoEnvironment.changeApiKeyStatus(envId, clock) map {
        case row: Seq[Int] => Right(row)
        case _ => Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged))
      }
      case _ => logger.info("Could not change Api Key status")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info("Could not change Api Key status", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info("Could not change Api Key status", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def changePublicApiKeyStatus(envId : Long) : Future[Either[ErrorResponse, Seq[Int]]]= {
    daoEnvironment.changePublicApiKeyStatus(envId, clock) map {
      case row: Seq[Int] => Right(row)
      case _ => Left(ErrorResponseFactory.get(PublicApiKeyStatusNotchanged))
    }
  }

  def getSocialAppKeyIds(accountId : Long) : Future[Either[ErrorResponse, Seq[Long]]] = {
    daoEnvironment.getEnvironmentsByAccountId(accountId) flatMap {
      case env if env.nonEmpty =>
        daoEnvironment.getSocialNetworkKeysByEnvId(env.map(_.id).toSet) map {
          case row if row.nonEmpty => Right(row.map(_.id))
          case _ => Left(ErrorResponseFactory.get(SocialKeyIdNotFound))
        }
      case _ => Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  def generateMissingPublicApiKeys(): Future[Either[ErrorResponse, Boolean]] = {
    daoEnvironment.getEnvironmentsMissingPublicApiKeys() flatMap {
      case envs if envs.nonEmpty =>
        Future.sequence(envs.map{ env =>
          generatePublicApiKey(env.id) map {
            case Right(n) if n > 0 =>
              logger.info(s"PublicApiKey is generated for the Environment: ${env.id}")
            case _ =>
              logger.info(s"PublicApiKey is not generated for the Environment: ${env.id}")
          }
        })
        Future.successful(Right(true))
      case env if env.isEmpty =>
        logger.info("Did not find any public Apikeys missing for the environments")
        Future.successful(Right(true))
      case _ =>
        logger.info("Could not generate missing public apikeys")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    }
  }

  def getApiKeys(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id)) flatMap {
      case true => daoAccount.fetchApiKeys(envId) map {
        case apikeys if apikeys.nonEmpty => Right(apikeys.map(apikey => ApiKeyInfo(apikey.id, apikey.status, apikey.apiKey, apikey.label, apikey.lastUsedAt)))
        case _ =>
          logger.info(s"No active Apikey found for environment $envId")
          Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyFetchFailed))
      }
      case _ => logger.info(s"Could not fetch Api Key for environment $envId")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Api Key for environment $envId", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not fetch Api Key for environment $envId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getApiKeysDev(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id)) flatMap {
      case true => daoAccount.fetchApiKeys(envId) map {
        case apikeys if apikeys.nonEmpty => Right(apikeys.map(apikey => ApiKeyInfo(apikey.id, apikey.status, apikey.apiKey, apikey.label, apikey.lastUsedAt)))
        case _ =>
          logger.info(s"No active Apikey found for environment $envId")
          Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyFetchFailed))
      }
      case _ => logger.info(s"Could not fetch Api Key for environment $envId")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Api Key for environment $envId", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not fetch Api Key for environment $envId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getPublicApiKeys(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.SETTINGS_VIEW.id)) flatMap {
      case true => daoEnvironment.getNonDeprecatedPublicApiKeys(envId) map {
        case apikeys if apikeys.nonEmpty => Right(apikeys.map(apikey => ApiKeyInfo(apikey.id, apikey.status, apikey.apiKey, apikey.label, apikey.lastUsedAt)))
        case _ =>
          logger.info(s"No active Public Apikey found for environment $envId")
          Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyFetchFailed))
      }
      case _ => logger.info(s"Could not fetch Public Api Key for environment $envId")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Public Api Key for environment $envId", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not fetch Public Api Key for environment $envId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def getPublicApiKeysDev(envId : Long, creator: Option[Creator]) : Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    v2Validator.isValidV2EnvironmentRequest(envId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id)) flatMap {
      case true => daoEnvironment.getNonDeprecatedPublicApiKeys(envId) map {
        case apikeys if apikeys.nonEmpty => Right(apikeys.map(apikey => ApiKeyInfo(apikey.id, apikey.status, apikey.apiKey, apikey.label, apikey.lastUsedAt)))
        case _ =>
          logger.info(s"No active Public Apikey found for environment $envId")
          Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyFetchFailed))
      }
      case _ => logger.info(s"Could not fetch Public Api Key for environment $envId")
        Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    } recover {
      case ex: ErrorResponseException =>
        logger.info(s"Could not fetch Public Api Key for environment $envId", ex)
        Left(ex.errorResponse)
      case e : Throwable =>
        logger.info(s"Could not fetch Public Api Key for environment $envId", e)
        Left(ErrorResponseFactory.get(UnknownError))
    }
  }

  def updateApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creator: Option[Creator]): Future[Either[ErrorResponse, Boolean]] = {
    daoEnvironment.fetchApiKeyById(apiKeyUpdateRequest.id) flatMap {
      case Some(dtoApiKey: DtoApiKey) =>
        v2Validator.isValidV2EnvironmentRequest(dtoApiKey.environmentId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
          case true =>
            daoEnvironment.updateApiKey(apiKeyUpdateRequest.id, apiKeyUpdateRequest.label, clock) map {
              case rows if rows > 0 => Right(true)
              case _ => Left(ErrorResponseFactory.get(ApiKeyFetchFailed))
            }
        } recover {
          case ex: ErrorResponseException =>
            logger.info(s"Could not fetch Api Key for environment ${dtoApiKey.environmentId}", ex)
            Left(ex.errorResponse)
          case e: Throwable =>
            logger.info(s"Could not fetch Api Key for environment ${dtoApiKey.environmentId}", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ApiKeyFetchFailed)))
    }
  }

  def updateApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creator: Option[Creator]): Future[Either[ErrorResponse, Boolean]] = {
    daoEnvironment.fetchApiKeyById(apiKeyUpdateRequest.id) flatMap {
      case Some(dtoApiKey: DtoApiKey) =>
        v2Validator.isValidV2EnvironmentRequest(dtoApiKey.environmentId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id)) flatMap {
          case true =>
            daoEnvironment.updateApiKey(apiKeyUpdateRequest.id, apiKeyUpdateRequest.label, clock) map {
              case rows if rows > 0 => Right(true)
              case _ => Left(ErrorResponseFactory.get(ApiKeyFetchFailed))
            }
        } recover {
          case ex: ErrorResponseException =>
            logger.info(s"Could not fetch Api Key for environment ${dtoApiKey.environmentId}", ex)
            Left(ex.errorResponse)
          case e: Throwable =>
            logger.info(s"Could not fetch Api Key for environment ${dtoApiKey.environmentId}", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ApiKeyFetchFailed)))
    }
  }

  def updatePublicApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creator: Option[Creator]): Future[Either[ErrorResponse, Boolean]] = {
    daoEnvironment.fetchPublicApiKeyById(apiKeyUpdateRequest.id) flatMap {
      case Some(dtoPublicApiKey: DtoPublicApiKey) =>
        v2Validator.isValidV2EnvironmentRequest(dtoPublicApiKey.environmentId, creator, Set(DashboardUserPermissions.SETTINGS_MODIFY.id)) flatMap {
          case true =>
            daoEnvironment.updatePublicApiKey(apiKeyUpdateRequest.id, apiKeyUpdateRequest.label, clock) map {
              case rows if rows > 0 => Right(true)
              case _ => Left(ErrorResponseFactory.get(ApiKeyFetchFailed))
            }
        } recover {
          case ex: ErrorResponseException =>
            logger.info(s"Could not fetch Api Key for environment ${dtoPublicApiKey.environmentId}", ex)
            Left(ex.errorResponse)
          case e: Throwable =>
            logger.info(s"Could not fetch Api Key for environment ${dtoPublicApiKey.environmentId}", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ApiKeyFetchFailed)))
    }
  }

  def updatePublicApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creator: Option[Creator]): Future[Either[ErrorResponse, Boolean]] = {
    daoEnvironment.fetchPublicApiKeyById(apiKeyUpdateRequest.id) flatMap {
      case Some(dtoPublicApiKey: DtoPublicApiKey) =>
        v2Validator.isValidV2EnvironmentRequest(dtoPublicApiKey.environmentId, creator, Set(DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id)) flatMap {
          case true =>
            daoEnvironment.updatePublicApiKey(apiKeyUpdateRequest.id, apiKeyUpdateRequest.label, clock) map {
              case rows if rows > 0 => Right(true)
              case _ => Left(ErrorResponseFactory.get(ApiKeyFetchFailed))
            }
        } recover {
          case ex: ErrorResponseException =>
            logger.info(s"Could not fetch Api Key for environment ${dtoPublicApiKey.environmentId}", ex)
            Left(ex.errorResponse)
          case e: Throwable =>
            logger.info(s"Could not fetch Api Key for environment ${dtoPublicApiKey.environmentId}", e)
            Left(ErrorResponseFactory.get(UnknownError))
        }
      case _ =>
        Future.successful(Left(ErrorResponseFactory.get(ApiKeyFetchFailed)))
    }
  }
}
