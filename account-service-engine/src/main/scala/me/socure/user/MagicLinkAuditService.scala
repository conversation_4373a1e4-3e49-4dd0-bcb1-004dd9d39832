package me.socure.user

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.ErrorResponse
import me.socure.storage.slick.dao.DaoMagicLinkAudit
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoDocumentLinkToken, DtoMagicToken}
import me.socure.storage.slick.tables.user.audit.{DtoDocumentTokenAudit, DtoMagicTokenAudit}
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class MagicLinkAuditService(daoMagicLink: DaoMagicLinkAudit,
                          daoBu: DaoBusinessUser)(implicit ec: ExecutionContext) {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  def saveMagicLinkAudit(userId: Long,
                       token: String,
                       createdAt: Option[DateTime] = None,
                       count: Option[Int] = None,
                       validatedAt: Option[DateTime] = None,
                       isExpired: Boolean = false,
                       deletedAt: Option[DateTime] = None,
                       userAgent: Option[String] = None): Future[Either[ErrorResponse, Int]] = {
    daoBu.getUser(userId) flatMap {
      case Some(userInfo) =>
        val dtoMagicTokenAudit = DtoMagicTokenAudit(0,
          userInfo.id,
          token,
          createdAt,
          count,
          validatedAt,
          isExpired,
          deletedAt,
          userAgent)
        daoMagicLink.saveMagicLinkAudit(dtoMagicTokenAudit) map {
          case res =>
            Right(res)
          case _ =>
            logger.info(s"Error occurred while saving Magic link audit")
            Left(ErrorResponseFactory.get(ExceptionCodes.MagicLinkAuditNotSaved))
        }
      case _ =>
        logger.info(s"User Id: $userId not found, magic link audit not saved")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UserNotFound)))
    } recover {
      case e: Exception =>
        logger.info(s"Error occurred while saving magic link audit Audit", e)
        Left(ErrorResponseFactory.get(ExceptionCodes.MagicLinkAuditNotSaved))
    }
  }

  def saveDocumentLinkAudit(
                             email: String,
                             token: String,
                             createdAt: Option[DateTime] = None,
                             count: Option[Int] = None,
                             validatedAt: Option[DateTime] = None,
                             isExpired: Boolean = false,
                             deletedAt: Option[DateTime] = None,
                             userAgent: Option[String] = None
                           ): Future[Either[ErrorResponse, Int]] = {

    val auditDto = DtoDocumentTokenAudit(
      id = 0,
      email = email,
      token = token,
      createdAt = createdAt,
      count = count,
      validatedAt = validatedAt,
      isExpired = isExpired,
      deletedAt = deletedAt,
      userAgent = userAgent
    )

    daoMagicLink.saveDocumentLinkAudit(auditDto)
      .map(result => Right(result))
      .recover {
        case ex: Exception =>
          logger.info("Error occurred while saving document link audit", ex)
          Left(ErrorResponseFactory.get(ExceptionCodes.DocumentLinkAuditNotSaved))
      }
  }

  def bulkSaveMagicLinkDeleteAudit(dtomagicTokens: Seq[(Long, String)], deletedAt: DateTime): Future[Either[ErrorResponse, Int]] = {
    daoBu.getUsersByIds(dtomagicTokens.map(_._1)).flatMap{ filteredUsers =>
      val filteredUserIds = filteredUsers.map(_.id)
      val filteredMagicTokens = dtomagicTokens.filter(token => filteredUserIds.contains(token._1))
      val dtoMagicTokenAudits = filteredMagicTokens.map{ filteredToken =>
        DtoMagicTokenAudit(id = 0, businessUserId = filteredToken._1, token = filteredToken._2, isExpired = true, deletedAt = Some(deletedAt))
      }
      daoMagicLink.bulkSaveMagicLinkAudit(dtoMagicTokenAudits) map {
        case Some(res) =>
          Right(res)
        case _ =>
          logger.info(s"Error occurred while saving Magic link audit")
          Left(ErrorResponseFactory.get(ExceptionCodes.MagicLinkAuditNotSaved))
      }
    }
  }

  def bulkSaveDocumentLinkDeleteAudit(dtoDocumentLinkTokens: Seq[(String, String)], deletedAt: DateTime): Future[Either[ErrorResponse, Int]] = {
    val audits = dtoDocumentLinkTokens.map { case (email, token) =>
      DtoDocumentTokenAudit(
        id = 0,
        email = email,
        token = token,
        isExpired = true,
        deletedAt = Some(deletedAt)
      )
    }
    daoMagicLink.bulkSaveDocumentLinkAudit(audits).map {
      case Some(result) => Right(result)
      case None =>
        logger.info("Error occurred while saving document link audit")
        Left(ErrorResponseFactory.get(ExceptionCodes.DocumentLinkAuditNotSaved))
    }
  }

}
