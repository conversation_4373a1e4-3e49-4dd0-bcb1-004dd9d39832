--
-- Table structure for table `tbl_account_metadata`
-- This table is created for the Socure Account Intelligence team
-- to store account ID and child ID as a map
--
DROP TABLE IF EXISTS `tbl_account_metadata`;

CREATE TABLE `tbl_account_metadata` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `account_id` BIGINT NOT NULL,
  `child_id` VARCHAR(20) NOT NULL,
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` VARCHAR(255) NOT NULL,
  `updated_by` VARCHAR(255) NOT NULL,
  PRIMARY KEY (`id`),
  <PERSON><PERSON>Y `account_metadata_account_id_idx` (`account_id`),
  UNIQUE KEY `unq_account_child_id` (`account_id`, `child_id`),
  CONSTRAINT `account_metadata_account_fk` FOREIGN KEY (`account_id`) REFERENCES `tbl_account` (`id`) ON DELETE CASCADE ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;