<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-service-basic</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>account-service</artifactId>
        <version>${revision}</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-convertors</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-storage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-k8s-mysql</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-value-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-schema</artifactId>
            <version>${project.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-json_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-core_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-ext_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-jackson_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-common-servlet</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>            
        </plugins>
    </build>
</project>
