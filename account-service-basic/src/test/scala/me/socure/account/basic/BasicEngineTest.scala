package me.socure.account.basic

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FunSuite, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

/**
  * Created by alexandre on 8/22/16.
  */
class BasicEngineTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  implicit val ec = ExecutionContext.global

  val mysqlService: MysqlService = MysqlService("basic-engine")
  val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty
  var engine: BasicEngine = null

  private def buildDataSource() = {
    val endpoint = mysqlService.endpoint()
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }


  private def prepareSchema(dataSource: DataSource) = {
    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas("socure")
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildEngine(dataSource: DataSource) = {
    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)

    new BasicEngine(dbProxyWithMetrics, slick.driver.MySQLDriver)
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)
    val sqlExecutor = new SQLExecutor(dataSource)
    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('industry sector', 'industry description')")
    sqlExecutor.execute(s"INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES (1, 'accountName', 'industry sector', false, true, NULL, true, '${PublicIdGenerator.account().value}', 'publicApiKey1', 'externalId1')")
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES (1, 'Production')")
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES(1, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 1, 1)")
    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(0, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(1, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(2, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(3, 1, 'api-key2', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")

    engine = buildEngine(dataSource)
  }

  override def afterAll(): Unit = {
    dataSources.foreach(_.close())
    mysqlService.stop()
  }

  test("should fetch credentials properly") {
    val actual = engine.getProductionCredentials(1)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, a => a.apiKey shouldBe "88-16ca6193-4149-456b-ae00-00fdad2437c6")
    }
  }
}
