package me.socure.account.basic

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.account.AccessCredentialsValueGenerator
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 8/22/16.
  */
class BasicServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec = ExecutionContext.global

  val engine = mock[BasicEngine]
  val servlet = new BasicServlet(engine)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(engine)
  }

  test("should return account not found") {

    Mockito.when(engine.getProductionCredentials(1)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(AccountNotFound))))
    get("/production_credentials/1") {
      status should equal(400)
    }
  }

  test("should return credentials") {

    val credentials = AccessCredentialsValueGenerator.anAccessCredentials()
    Mockito.when(engine.getProductionCredentials(1)).thenReturn(Future.successful(Right(credentials)))

    get("/production_credentials/1") {
      status should equal(200)
    }
  }
}
