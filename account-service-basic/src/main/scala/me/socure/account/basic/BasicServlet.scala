package me.socure.account.basic

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

/**
  * Created by alexand<PERSON> on 8/22/16.
  */
class BasicServlet(engine: BasicEngine)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  private val logger = LoggerFactory.getLogger(classOf[BasicServlet])

  private val metrics: Metrics = JavaMetricsFactory.get("account-service." + classOf[BasicServlet].getSimpleName)

  get("/production_credentials/:accountId") {

    val apiKey = params("accountId").toLong
    val futureResponse = engine.getProductionCredentials(apiKey)

    ScalatraResponseFactory.get(futureResponse)
  }
}
