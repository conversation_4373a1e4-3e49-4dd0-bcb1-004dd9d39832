package me.socure.account.basic

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.constants.EnvironmentConstants.PRODUCTION_ENVIRONMENT
import me.socure.convertors.AccountConvertors
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccessCredentials, ApiKeyStatus}
import me.socure.storage.slick.tables.account.{DaoTblApiKey, DaoTblEnvironment, DaoTblEnvironmentType}
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import slick.driver.JdbcProfile
import slick.jdbc.JdbcBackend._

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 8/22/16.
  */
class BasicEngine(val dBProxyWithMetrics: DBProxyWithMetrics, val profile: JdbcProfile)(implicit ec: ExecutionContext)
  extends DaoTblEnvironmentType
    with DaoTblEnvironment
    with DaoTblApiKey{

  import profile.api._

  def getProductionCredentials(accountId: Long): Future[Either[ErrorResponse, AccessCredentials]] = {
    val query = TblEnvironment.filter(e => e.accountId === accountId && e.environmentType === PRODUCTION_ENVIRONMENT.id.toLong)
      .joinLeft(TblApiKey).on(_.id === _.environmentId)

    dBProxyWithMetrics.runWithWriter(query.result,Set(DBTables.TblEnvironment,DBTables.TblApiKey),
      DBActions.Select, "getProductionCredentials").map(_.groupBy(_._1).mapValues(_.flatMap(_._2.filter(_.status != ApiKeyStatus.DEPRECATED)))).map {
      _.headOption match {
        case Some(envkeys) => Right(AccountConvertors.getAccessCredentialsWithLatestApiKey(envkeys._1, envkeys._2))
        case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      }
    }
  }

}
