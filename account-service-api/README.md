To build the docker image:

You will first need to create a fat jar for Account service:

    mvn clean package -pl :account-service-api -am -DskipTests

Then to build the image:

    cd ~/path/to/account-service/account-service-api
    docker build -t docker-registry.us-east-vpc.socure.be:5043/account-service

To pull the latest package from the Docker Registry

    docker pull docker-registry.us-east-vpc.socure.be:5043/account-service


## How to run docker-compose
    CONFIG_DECRYPTION_PASSWORD='socure-secret-password' docker-compose up

Note:
* Keep separate directories for account service and salt service containing sql files and set them accordingly in volumes of mysql-migrator
* for building docker-compose for local tests,  replace `        image: ************.dkr.ecr.us-east-1.amazonaws.com/account-service:latest` with `build: .`
