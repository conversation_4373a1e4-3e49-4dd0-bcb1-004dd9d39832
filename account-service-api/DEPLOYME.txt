[Meta]
BeanstalkAppName = socure-account-service
BeanstalkParallelAllowed = yes
BeanstalkParallelNeedsWarmup = no
BeanstalkDowntimeAcceptable = no
IfDeployedRestart = socure-service
UpstreamApps = salt-service
DownstreamApps = admin-dashboard,socure-authentication,socure-account-manager,socure-service,socure-file-transfer
QaSteps = QA socure-account-service

[Notes]
If account service is deployed, id+ needs to be restarted afterward.
This is because of the Java DNS cache. While AWS DNS is correct, the Java app on id+ caches the old IP, and points to the old wrong account-service.
