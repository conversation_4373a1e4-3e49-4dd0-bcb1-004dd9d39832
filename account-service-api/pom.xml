<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <artifactId>account-service-api</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>

    <parent>
        <groupId>me.socure</groupId>
        <artifactId>account-service</artifactId>
        <version>${revision}</version>
    </parent>

    <dependencies>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jmx-service</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-blacklist</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-basic</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-logs</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-storage</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-schema</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-model</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-engine</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-model</artifactId>
            <version>${project.version}</version>
            <classifier>tests</classifier>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-common-servlet</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-notification</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-healthcheck-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>account-service-idplus</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-environment</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-config</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-c3p0-factory</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-jettythreadpool-factory</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-executioncontext-factory</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-sql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws.secretsmanager</groupId>
            <artifactId>aws-secretsmanager-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-core_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-ext_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-native_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.json4s</groupId>
            <artifactId>json4s-jackson_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-auth_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatra</groupId>
            <artifactId>scalatra-scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dispatchhttp</groupId>
            <artifactId>dispatch-core_${sc.ver}</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-webapp</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-servlet</artifactId>
        </dependency>
        <dependency>
            <groupId>org.eclipse.jetty</groupId>
            <artifactId>jetty-server</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-h2-service</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-microservice</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-s3</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-servlet-api</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-db</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-check-impl-http</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-servlet-metrics</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>common-rds</artifactId>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>model-management-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpcore</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>decision-service-client</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>me.socure</groupId>
                    <artifactId>transaction-audit-storage-mysql-schema</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>document-manager-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>me.socure</groupId>
            <artifactId>docv-orchestra-client</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>jib-fips</id>
            <activation>
                <property>
                    <name>build-environment</name>
                    <value>gitlab-ci</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.google.cloud.tools</groupId>
                        <artifactId>jib-maven-plugin</artifactId>
                        <version>3.2.1</version>
                        <configuration>
                            <allowInsecureRegistries>true</allowInsecureRegistries>
                            <from>
                                <image>${env.FROM_IMAGE}</image>
                                <platforms>
                                    <platform>
                                        <architecture>amd64</architecture>
                                        <os>linux</os>
                                    </platform>
                                    <platform>
                                        <architecture>arm64</architecture>
                                        <os>linux</os>
                                    </platform>
                                </platforms>
                            </from>
                            <to>
                                <image>${env.TO_IMAGE}</image>
                                <auth>
                                    <username>${env.REGISTRY_USERNAME}</username>
                                    <password>${env.REGISTRY_PASSWORD}</password>
                                </auth>
                                <tags>
                                    <tag>${env.TO_IMAGE_TAG}</tag>
                                </tags>
                            </to>
                            <container>
                                <mainClass>me.socure.account.Main</mainClass>
                                <ports>
                                    <port>5000</port>
                                </ports>
                            </container>
                        </configuration>
                        <executions>
                            <execution>
                                <id>build-and-push-docker-image</id>
                                <goals>
                                    <goal>build</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>
