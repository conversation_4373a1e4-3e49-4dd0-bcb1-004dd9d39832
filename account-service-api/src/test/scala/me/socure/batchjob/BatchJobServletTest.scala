package me.socure.batchjob

import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import me.socure.model.{Response, ResponseStatus}
import org.mockito.{Matchers, Mockito}
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class BatchJobServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter with FunSuiteLike {
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val accountManagementService: AccountManagementService = mock[AccountManagementService]

  val batchJobServlet: BatchJobServlet = new BatchJobServlet(accountManagementService, hmacVerifier)
  addServlet(batchJobServlet, "/batchjob/*")

  before{
    Mockito.reset(accountManagementService)
  }

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  test("Update account permissions") {
    val upsRequest = List(
      AccountPermissionUpdateRequest(1000, Set(1, 2, 3), true),
      AccountPermissionUpdateRequest(10, Set(1001, 48, 3), true),
      AccountPermissionUpdateRequest(7, Set(1001, 2, 3), true),
      AccountPermissionUpdateRequest(7, Set(1001, 2, 3), false))
    val upsResponse = List(
      AccountPermissionUpdateResponse(1000, Set(1, 2, 3), true, "Success", ""),
      AccountPermissionUpdateResponse(10, Set(1001, 48, 3), true, "Success", ""),
      AccountPermissionUpdateResponse(7, Set(1001, 2, 3), true, "Failure", "Account Not Found"),
      AccountPermissionUpdateResponse(7, Set(1001, 2, 3), false, "Failure", "Unknown role"))
    Mockito.when(accountManagementService.updateAccountPermissions(upsRequest)).thenReturn(Future.successful(upsResponse))
    post(s"/batchjob/account/permission/update",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      ),
      body = Serialization.write(upsRequest)){
      validate[Response[List[AccountPermissionUpdateResponse]]](status, 200, body, Response(ResponseStatus.Ok, upsResponse))
    }
    Mockito.verify(accountManagementService, Mockito.times(1)).updateAccountPermissions(upsRequest)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
