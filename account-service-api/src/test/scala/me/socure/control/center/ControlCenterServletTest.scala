package me.socure.control.center

import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.control.center.model.{ControlCenterSettings, ToggleableSetting}
import me.socure.model.control.center.ControlCenterSettingUpdateRequest
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.{Matchers, Mockito}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future

class ControlCenterServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter with FunSuiteLike {

  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val controlCenterService: ControlCenterService = mock[ControlCenterService]

  val controlCenterServlet: ControlCenterServlet = new ControlCenterServlet(controlCenterService, hmacVerifier)
  addServlet(controlCenterServlet, "/control_center/*")

  before{
    Mockito.reset(controlCenterService)
  }

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  test("Update control center configuration properly") {
    Mockito.when(controlCenterService.updateSetting("test",true)).thenReturn(Future.successful(Right(controlCenterSettings())))
    post(s"/control_center/settings/update",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      ),
      body = Serialization.write(ControlCenterSettingUpdateRequest("test", true))){
      validate[Response[ControlCenterSettings]](status, 200, body, Response(ResponseStatus.Ok, controlCenterSettings()))
    }
    Mockito.verify(controlCenterService).updateSetting("test",true)
  }

  test("Should handle and return error properly") {
    val errorResponse = ErrorResponse(1,"error")
    Mockito.when(controlCenterService.updateSetting("test",true)).thenReturn(Future.successful(Left(errorResponse)))
    post(s"/control_center/settings/update",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      ),
      body = Serialization.write(ControlCenterSettingUpdateRequest("test", true))){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, errorResponse))
    }
    Mockito.verify(controlCenterService).updateSetting("test",true)
  }

  private def controlCenterSettings() = {
    ControlCenterSettings(toggleableSettings = Seq(ToggleableSetting("test",true,"test")))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
