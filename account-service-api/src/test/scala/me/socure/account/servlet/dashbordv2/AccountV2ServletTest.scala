package me.socure.account.servlet.dashbordv2

import me.socure.account.automation.AccountAutomationService
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.constants.EnvironmentTypes
import me.socure.dashboardv2.AccountV2Servlet
import me.socure.model.account._
import me.socure.model.dashboardv2.{AccountInfoWithEnvDetails, AccountV2, Creator, SubAccountV2}
import me.socure.model.user.{SubAccountFormV2, UserForm}
import me.socure.model.{ErrorResponse, IdAndName, Response, ResponseStatus}
import me.socure.storage.slick.tables.account.DtoAccount
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
/**
  * Created by sunderraj on 8/16/16.
  */

class AccountV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter{
  implicit val ec : ExecutionContext = ExecutionContext.global

  val accountService: DashboardAccountServiceV2 = mock[DashboardAccountServiceV2]
  val accountAutomationService = mock[AccountAutomationService]
  val accountServlet: AccountV2Servlet = new AccountV2Servlet(accountService, accountAutomationService)

  addServlet(accountServlet, "/*")

  before{
    reset(accountService)
  }

  val userForm1: UserForm = UserFixture.duplicateUserForm
  val userForm2: UserForm = UserFixture.userForm
  val subAccount: SubAccount = UserFixture.subAccount
  val subAccountFormV2:SubAccountFormV2 = UserFixture.subAccountFormV2
  val dtoAcc = DtoAccount(accountId = 123, name = subAccount.companyname, industrySector = subAccount.industry, isInternal = false, isActive = false, parentId = None, isDeleted = false, firstActivatedAt = None, publicId = "test-123", publicApiKey = "test-key-123", externalId = None)

  test("list sub accounts of account") {
    when(accountService.listAllAccounts(1)) thenReturn Future.successful(Right(Vector(UserFixture.accountDetailsV2)))
    get("/list_accounts/1") {
      validate[Response[Vector[AccountV2]]](status, 200, body, Response(ResponseStatus.Ok, Vector(UserFixture.accountDetailsV2)))
    }
  }

  test("error response for list subaccount of account") {
    when(accountService.listAllAccounts(2)) thenReturn Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
    get("/list_accounts/2") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound)))
    }
  }

  test("list all sub account info of an account") {
    when(accountService.listSubAccounts(1)) thenReturn Future.successful(Right(List(UserFixture.subAccountV2)))
    get("/list_sub_accounts/1") {
      validate[Response[List[SubAccountV2]]](status, 200, body, Response(ResponseStatus.Ok, List(UserFixture.subAccountV2)))
    }
  }

  test("list all sub account with environment info of an account") {
    val expectedResult = Set(
      AccountInfoWithEnvDetails(
        id = 2,
        name = Some("AccountName2"),
        environments = Set(
          IdAndName(id = 4, name = None)
        )
      ),
      AccountInfoWithEnvDetails(
        id = 3,
        name = Some("AccountName3"),
        environments = Set(
          IdAndName(id = 11, name = None),
          IdAndName(id = 12, name = None)
        )
      )
    )
    when(accountService.listSubAccountsWithEnvDetails(1)) thenReturn Future.successful(Right(expectedResult))
    get("/list_sub_accounts_with_env_details/1") {
      validate[Response[Set[AccountInfoWithEnvDetails]]](status, 200, body, Response(ResponseStatus.Ok, expectedResult))
    }
  }

  test("return empty list of an account when not having any subaccounts") {
    when(accountService.listSubAccounts(2)) thenReturn Future.successful(Right(List.empty))
    get("/list_sub_accounts/2") {
      validate[Response[List[SubAccountV2]]](status, 200, body, Response(ResponseStatus.Ok, List.empty))
    }
  }

  test("error response for list all sub account info  of an account") {
    when(accountService.listSubAccounts(22)) thenReturn Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    get("/list_sub_accounts/22") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError)))
    }
  }

  test("create subaccount should create account") {
    when(accountService.createSubAcount(1, userForm1)) thenReturn Future.successful(Right(true))
    post(s"/create_sub_account?parentid=${1}", userForm1.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("create subaccount with isActive true should create account") {
    when(accountService.createSubAcount(1, userForm1, true)) thenReturn Future.successful(Right(true))
    post(s"/create_sub_account?parentid=${1}&isActive=${true}", userForm1.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("create subaccount should return registration failed") {
    when(accountService.createSubAcount(1, userForm1)) thenReturn Future.successful(Left(ErrorResponseFactory.get(RegistrationFailed)))
    post(s"/create_sub_account?parentid=${1}", userForm1.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed)))
    }
  }

  test("create subaccount v2 should create account") {
    when(accountService.createSubAccountV2(1, subAccountFormV2)) thenReturn Future.successful(Right(true))
    post(s"/create_sub_account_v2?parentid=${1}", subAccountFormV2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("create subaccount v2 with isActive true should create account") {
    when(accountService.createSubAccountV2(1, subAccountFormV2, true)) thenReturn Future.successful(Right(true))
    post(s"/create_sub_account_v2?parentid=${1}&isActive=${true}", subAccountFormV2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("create subaccount v2 should return registration failed") {
    when(accountService.createSubAccountV2(1, subAccountFormV2)) thenReturn Future.successful(Left(ErrorResponseFactory.get(RegistrationFailed)))
    post(s"/create_sub_account_v2?parentid=${1}", subAccountFormV2.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed)))
    }
  }

  test("create subaccount with minimum details should create account") {

    when(accountService.createSubAcountWithMinDetails(1, subAccount, None, None, None)) thenReturn Future.successful(Right((dtoAcc, dtoAcc.accountId)))
    when(accountAutomationService.baselineLogicProvisioningForSubaccount(dtoAcc.accountId, 1L)) thenReturn Future.successful(Right(true))
    post("/create_sub_account_with_min_details", Map("parentid" -> "1", "user" -> subAccount.encodeJson())) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(accountService).createSubAcountWithMinDetails(1, subAccount, None, None, None)
  }

  test("create subaccount(V2) with minimum details should create account") {
    val accountInfo = Map("parentid" -> "1", "user" -> subAccount.encodeJson(), "user_id" -> "2", "account_id" -> "1")
    when(accountAutomationService.baselineLogicProvisioningForSubaccount(dtoAcc.accountId, 1L)) thenReturn Future.successful(Right(true))
    when(accountService.createSubAcountWithMinDetails(1, subAccount, Some(2L), Some(1L), None)) thenReturn Future.successful(Right((dtoAcc, dtoAcc.accountId)))
    post("/create_sub_account_with_min_details", accountInfo) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(accountService).createSubAcountWithMinDetails(1, subAccount, Some(2L), Some(1L), None)
  }

  test("create subaccount(V2) with minimum details and primary user should create account") {
    val accountInfo = Map("parentid" -> "1", "user" -> subAccount.encodeJson(), "user_id" -> "2", "account_id" -> "1", "primary_user_id" -> "2")
    when(accountAutomationService.baselineLogicProvisioningForSubaccount(dtoAcc.accountId, 1L)) thenReturn Future.successful(Right(true))
    when(accountService.createSubAcountWithMinDetails(1, subAccount, Some(2L), Some(1L), Some(2L))) thenReturn Future.successful(Right((dtoAcc, dtoAcc.accountId)))
    post("/create_sub_account_with_min_details", accountInfo) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(accountService).createSubAcountWithMinDetails(1, subAccount, Some(2L), Some(1L), Some(2L))
  }

  test("create subaccount with minumin details should return failure message") {
    when(accountService.createSubAcountWithMinDetails(1, subAccount, None, None, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ParentAccountNotFound)))
    post("/create_sub_account_with_min_details", Map("parentid" -> "1", "user" -> subAccount.encodeJson())) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ParentAccountNotFound)))
    }
    verify(accountService).createSubAcountWithMinDetails(1, subAccount, None, None, None)
  }

  test("update subaccount should update account details") {
    when(accountService.updateSubAccount(userForm2.email, userForm2)) thenReturn Future.successful(Right(true))
    post("/update_sub_account", userForm2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("update subaccount should return subaccount not found") {
    when(accountService.updateSubAccount(UserFixture.userForm.email, UserFixture.userForm)) thenReturn Future.successful(Left(ErrorResponseFactory.get(SubAccountNotFound)))
    post("/update_sub_account", userForm2.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound)))
    }
  }

  test("activate account should return true") {
    when(accountService.toggleAccountActivateStatus(1, isActive = true, None)) thenReturn Future.successful(Right(true))
    post("/activate", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("activate account should return account not found") {
    when(accountService.toggleAccountActivateStatus(1, isActive = true, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    post("/activate", Map("account_id" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  test("deactivate account should return true") {
    when(accountService.toggleAccountActivateStatus(1, isActive = false, None)) thenReturn Future.successful(Right(true))
    post("/deactivate", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("deactivate account should return account not found") {
    when(accountService.toggleAccountActivateStatus(1, isActive = false, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    post("/deactivate", Map("account_id" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  test("get account list should return account list") {
    val list = Vector(AccountIdName(1, "Primary Account"))
    when(accountService.getAllAccounts(1)) thenReturn Future.successful(Right(list))
    get("/get_account_list/1") {
      validate[Response[Vector[AccountIdName]]](status, 200, body, Response(ResponseStatus.Ok, list))
    }
  }

  test("get account list should return account not found") {
    when(accountService.getAllAccounts(1)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    get("/get_account_list/1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  test("get apikeys for sub accounts should return empty list for invalid accounts") {
    when(accountService.getApiKeysForSubAccounts(999L, None)) thenReturn Future(Right(List.empty))
    get("/get_apikeys_for_subaccounts/999") {
      validate[Response[List[SubAccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, List.empty[SubAccountApiKeys]))
    }
  }

  test("get apikeys for sub accounts should return list of sub accounts with the apikeys") {
    val subAccountWithApiKeysList = List(
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"),
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, canBeRenewed = true, None)),18,"Development"))
    when(accountService.getApiKeysForSubAccounts(1L, None)) thenReturn Future(Right(subAccountWithApiKeysList))
    get("/get_apikeys_for_subaccounts/1") {
      validate[Response[List[SubAccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, subAccountWithApiKeysList))
    }
  }

  test("get apikeys for sub accounts should return empty list for invalid accounts - V2") {
    when(accountService.getApiKeysForSubAccounts(999L, Some(Creator(1L, 1L)))) thenReturn Future(Right(List.empty))
    get("/get_apikeys_for_subaccounts/999", params = Map("creator_user_id" -> "1", "creator_account_id" -> "1")) {
      validate[Response[List[SubAccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, List.empty[SubAccountApiKeys]))
    }
  }

  test("get apikeys for sub accounts should return list of sub accounts with the apikeys - V2") {
    val subAccountWithApiKeysList = List(
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"),
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, canBeRenewed = true, None)),18,"Development"))
    when(accountService.getApiKeysForSubAccounts(1L, Some(Creator(1L, 1L)))) thenReturn Future(Right(subAccountWithApiKeysList))
    get("/get_apikeys_for_subaccounts/1", params = Map("creator_user_id" -> "1", "creator_account_id" -> "1")) {
      validate[Response[List[SubAccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, subAccountWithApiKeysList))
    }
  }

  test("get_apikeys_for_account_and_subaccounts should return empty list for invalid account") {
    when(accountService.getApiKeysForAccountAndSubAccounts(999L)) thenReturn Future(Right(List.empty[AccountApiKeys]))
    get("/get_apikeys_for_account_and_subaccounts/999") {
      validate[Response[List[AccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, List.empty[AccountApiKeys]))
    }
  }

  test("get_apikeys_for_account_and_subaccounts should return list of apikeys") {
    val accountWithApiKeysList = List(
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"),
      AccountApiKeys(12, "AccountName12",Vector(ApiKeyDetails("1916ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),19,"Production"),
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, canBeRenewed = true, None)),18,"Development"))
    when(accountService.getApiKeysForAccountAndSubAccounts(1L)) thenReturn Future(Right(accountWithApiKeysList))
    get("/get_apikeys_for_account_and_subaccounts/1") {
      validate[Response[List[AccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, accountWithApiKeysList))
    }
  }

  test("get account info for environment should return environment not found") {
    when(accountService.getAccountInfoForEnvironment(999L)) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.EnvironmentNotFound)))
    get("/get_account_info_for_environment/999") {
      status should equal(400)
      body should include(ExceptionCodes.EnvironmentNotFound.id.toString)
    }
  }

  test("get account info for environment should return account information") {
    val accountInfo = AccountInfo(13,"AccountName13",Some(7))
     when(accountService.getAccountInfoForEnvironment(1L)) thenReturn Future(Right(accountInfo))
    get("/get_account_info_for_environment/1") {
      status should equal(200)
      body shouldBe """{"status":"ok","data":{"id":13,"name":"AccountName13","parentId":7}}"""
    }
  }

  test("Get sub accounts - success") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val expected = Seq(SubAccountV2(2,"AccountName2","Banking",status = true,Some("1/2/")), SubAccountV2(3,"AccountName3","Banking",status = true,Some("1/2/3")), SubAccountV2(5,"AccountName5","Banking",status = false,Some("1/2/5/")))
    when(accountService.listSubAccountsV2(accountId, creator, None, None)).thenReturn(Future.successful(Right(expected)))
    get(s"/subaccounts/$accountId",
      params = Map("user_id" -> creator.userId.toString, "account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[SubAccountV2]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).listSubAccountsV2(accountId, creator, None, None)
  }

  test("Get sub accounts, skip permission - success") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val skipPermissionChk = true
    val expected = Seq(SubAccountV2(2,"AccountName2","Banking",status = true,Some("1/2/")), SubAccountV2(3,"AccountName3","Banking",status = true,Some("1/2/3")), SubAccountV2(5,"AccountName5","Banking",status = false,Some("1/2/5/")))
    when(accountService.listSubAccountsV2(accountId, creator, None, None, skipPermissionChk)).thenReturn(Future.successful(Right(expected)))
    get(s"/subaccounts/$accountId",
      params = Map("user_id" -> creator.userId.toString, "account_id" -> creator.accountId.toString, "skip_permission_chk" -> skipPermissionChk.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[SubAccountV2]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).listSubAccountsV2(accountId, creator, None, None, skipPermissionChk)
  }

  test("Get sub accounts, do permission check - failure") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val skipPermissionChk = false
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    when(accountService.listSubAccountsV2(accountId, creator, None, None, skipPermissionChk)).thenReturn(Future.successful(Left(expected)))
    get(s"/subaccounts/$accountId",
      params = Map("user_id" -> creator.userId.toString, "account_id" -> creator.accountId.toString, "skip_permission_chk" -> skipPermissionChk.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).listSubAccountsV2(accountId, creator, None, None, skipPermissionChk)
  }

  test("Get sub accounts - failure") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val expected = ErrorResponseFactory.get(ExceptionCodes.SubAccountFetchError)
    when(accountService.listSubAccountsV2(accountId, creator, None, None)).thenReturn(Future.successful(Left(expected)))
    get(s"/subaccounts/$accountId",
      params = Map("user_id" -> creator.userId.toString, "account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).listSubAccountsV2(accountId, creator, None, None)
  }

  test("Get sub accounts pagination - success") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val expected = Seq(SubAccountV2(2,"AccountName2","Banking",status = true,Some("1/2/")))
    when(accountService.listSubAccountsV2(accountId, creator, Some(1), Some(1))).thenReturn(Future.successful(Right(expected)))
    get(s"/subaccounts/$accountId",
      params = Map("user_id" -> creator.userId.toString, "account_id" -> creator.accountId.toString, "page" -> "1", "size" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[SubAccountV2]]](status, 200, body, Response(ResponseStatus.Ok, expected))
      expected.length shouldBe 1
    }
    verify(accountService).listSubAccountsV2(accountId, creator, Some(1), Some(1))
  }

  test("get associated accounts for user") {
    val userId = 1
   val expected = Seq(AccountIdName(2,"AccountName2"), AccountIdName(3,"AccountName3"), AccountIdName(5,"AccountName5"))

    when(accountService.getAssociatedAccounts(userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/get_associated_accounts_for_user",
      params = Map("user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[AccountIdName]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).getAssociatedAccounts(userId)
  }

  test("Validate the account access by ApiKey") {
    val apiKey = "apikey"
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "14"
    val expected = true
    when(accountService.validateAccessPermission(apiKey, creator, permissions, None)).thenReturn(Future.successful(Right(expected)))
    get(s"/validate/apikey/access",
      params = Map(
        "creator_user_id" -> creator.userId.toString,
        "creator_account_id" -> creator.accountId.toString,
        "apikey" -> apiKey,
        "permissions" -> permissions
      ),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).validateAccessPermission(apiKey, creator, permissions, None)
  }

  test("Validate the account access by ApiKey should fail for invalid data") {
    val apiKey = "invalid apikey"
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "140"
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(accountService.validateAccessPermission(apiKey, creator, permissions, None)).thenReturn(Future.successful(Left(expected)))

    get(s"/validate/apikey/access",
      params = Map(
        "creator_user_id" -> creator.userId.toString,
        "creator_account_id" -> creator.accountId.toString,
        "apikey" -> apiKey,
        "permissions" -> permissions
      ),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).validateAccessPermission(apiKey, creator, permissions, None)
  }

  test("Validate the account access by environment type") {
    val environmentTypeId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "1009"
    val expected = true
    when(accountService.validateAccessPermission(environmentTypeId, creator, permissions)).thenReturn(Future.successful(Right(expected)))
    get(s"/validate/environmenttype/access",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "environment_type_id" -> environmentTypeId.toString, "permissions" -> permissions),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).validateAccessPermission(environmentTypeId, creator, permissions)
  }

  test("Validate the account access by environment type should fail for invalid data") {
    val environmentTypeId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "140"
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(accountService.validateAccessPermission(environmentTypeId, creator, permissions)).thenReturn(Future.successful(Left(expected)))
    get(s"/validate/environmenttype/access",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "environment_type_id" -> environmentTypeId.toString, "permissions" -> permissions),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).validateAccessPermission(environmentTypeId, creator, permissions)
  }

  test("Validate the account access by environment type with or permissions") {
    val environmentTypeId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "1009"
    val orPermissions = Some("1001")
    val expected = true
    when(accountService.validateAccessWithOrPermission(environmentTypeId, creator, permissions, orPermissions)).thenReturn(Future.successful(Right(expected)))
    get(s"/validate/environmenttype/access/permissions",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "environment_type_id" -> environmentTypeId.toString, "permissions" -> permissions, "or_permissions" -> orPermissions.getOrElse("")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).validateAccessWithOrPermission(environmentTypeId, creator, permissions, orPermissions)
  }

  test("Validate the account access by environment type should fail for invalid data with or permissions") {
    val environmentTypeId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val permissions = "140"
    val orPermissions = Some("1001")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(accountService.validateAccessWithOrPermission(environmentTypeId, creator, permissions, orPermissions)).thenReturn(Future.successful(Left(expected)))
    get(s"/validate/environmenttype/access/permissions",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "environment_type_id" -> environmentTypeId.toString, "permissions" -> permissions, "or_permissions" -> orPermissions.getOrElse("")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).validateAccessWithOrPermission(environmentTypeId, creator, permissions, orPermissions)
  }

  test("get account API keys by env Ype should return list of apikeys") {
    val accountWithApiKeysList = List(
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"))
    when(accountService.getApiKeysForAccountByEnvironmentType(1L, EnvironmentTypes.PRODUCTION_ENVIRONMENT.id)) thenReturn Future(Right(accountWithApiKeysList))
    get("/get_apikeys_for_account_by_envType/1/1") {
      validate[Response[List[AccountApiKeys]]](status, 200, body, Response(ResponseStatus.Ok, accountWithApiKeysList))
    }
  }


  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
