package me.socure.account.servlet.mock

import me.socure.model.account.{AccountInfo}

import scala.util.Random

/**
 * Created by joon<PERSON><PERSON> on 7/14/16.
 */
object MockSubAccounts {
  def generateSubAccounts(num:Int): List[AccountInfo] = {
    val random = Random
    (1 to num).map{ p ⇒{
      AccountInfo(id = random.nextLong(),
        name = writeAlph(10),
        parentId = Some(1l)
      )

    }
    }.toList
  }
  private def writeAlph(chars:Int): String= {
    val random = Random
    (1 to chars ).map(random.alphanumeric).mkString
  }
}
