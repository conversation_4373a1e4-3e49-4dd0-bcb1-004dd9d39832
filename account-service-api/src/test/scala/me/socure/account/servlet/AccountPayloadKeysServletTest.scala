package me.socure.account.servlet

import me.socure.account.service.AccountPayloadKeysService
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.account.{AccountEnvironment, AccountPayloadKey, MergePayloadKeysRequest}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, when}
import org.mockito.{Matchers, Mockito}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
 * Created by ab<PERSON><PERSON><PERSON> on 11/15/2021.
 */
class AccountPayloadKeysServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar{

  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val accountPayloadKeysService = mock[AccountPayloadKeysService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]

  private val servlet = new AccountPayloadKeysServlet(accountPayloadKeysService, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(accountPayloadKeysService)
    reset(hmacVerifier)
  }

  test("get_payload_keys should return list of active account payload keys"){
    val list = Seq(AccountPayloadKey(1, 1, 1, "pub_key", "pvt_key", "cust_key", new DateTime("2021-11-06", DateTimeZone.UTC), new DateTime("2021-11-06", DateTimeZone.UTC)))
    val res = Response(ResponseStatus.Ok, list)
    Mockito.doNothing().when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountPayloadKeysService.getPayloadKeys(Some(1))) thenReturn Future.successful(Right(list))

    get(
      uri = "/get_payload_keys?account_id=1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[AccountPayloadKey]]](status, 200, body, res)
    }

    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPayloadKeysService).getPayloadKeys(Some(1))
  }

  test("create_payload_keys should generate data key pair and return public key"){
    val res = Response(ResponseStatus.Ok, data = "public_key")
    val accountId = 1L
    val environmentId = 1L
    val customerPublicKey = "sample-key"
    when(accountPayloadKeysService.createPayloadKeys(accountId, environmentId, 2048, customerPublicKey, false)) thenReturn Future.successful(Right("public_key"))
    post(
      uri = "/create_payload_keys",
      params = Map("account_id" -> "1", "environment_id" -> "1", "customer_public_key" -> "sample-key", "key_size" -> "2048", "is_rotation" -> "false"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      validate[Response[String]](status, 200, body, res)
    }

    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPayloadKeysService).createPayloadKeys(accountId, environmentId, 2048, customerPublicKey, false)
  }

  test("update_payload_keys should update payload key and return public key"){
    val accountPayloadKey = AccountPayloadKey(1, 1, 1, "pub_key", "pvt_key", "cust_key", new DateTime("2021-11-06", DateTimeZone.UTC), new DateTime("2021-11-06", DateTimeZone.UTC))
    val res = Response(ResponseStatus.Ok, accountPayloadKey)
    val accountId = 1L
    val environmentId = 1L
    val customerPublicKey = Some("cust_key")
    when(accountPayloadKeysService.updatePayloadKeys(accountId, environmentId, customerPublicKey, false, 4096)) thenReturn Future.successful(Right(accountPayloadKey))
    put(
      uri = "/update_payload_keys/1/1",
      params = Map("customer_public_key" -> "cust_key"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      validate[Response[AccountPayloadKey]](status, 200, body, res)
    }

    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPayloadKeysService).updatePayloadKeys(accountId, environmentId, customerPublicKey, false, 4096)
  }

  test("merge_payload_keys should copy payload keys and return list of keys copied"){
    val accountPayloadKey = AccountPayloadKey(1, 1, 2, "pub_key", "pvt_key", "cust_key", new DateTime("2021-11-06", DateTimeZone.UTC), new DateTime("2021-11-06", DateTimeZone.UTC))
    val res = Response(ResponseStatus.Ok, data = Seq(accountPayloadKey))
    val mergePayloadKeysRequest = MergePayloadKeysRequest(1, 1, Seq(AccountEnvironment(1, 2)))
    when(accountPayloadKeysService.mergePayloadKeys(mergePayloadKeysRequest)) thenReturn Future.successful(Right(Seq(accountPayloadKey)))

    post(
      uri = "/merge_payload_keys",
      Serialization.write(mergePayloadKeysRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      validate[Response[Seq[AccountPayloadKey]]](status, 200, body, res)
    }

    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPayloadKeysService).mergePayloadKeys(mergePayloadKeysRequest)
  }

  test("delete_payload_keys should deactivate payload key and return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    val accountId = 1L
    val environmentId = 1L
    when(accountPayloadKeysService.deletePayloadKeys(accountId, environmentId)) thenReturn Future.successful(Right(true))
    put(
      uri = "/delete_payload_keys/1/1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      validate[Response[Boolean]](status, 200, body, res)
    }

    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPayloadKeysService).deletePayloadKeys(accountId, environmentId)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
