package me.socure.account.servlet

import me.socure.account.service.AccountSftpUserService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CouldNotAddSFTPUserForAccount, UnableToListSFTPUsers}
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.account.AccountSftpUser
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountSftpUserServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountSftpUserService = mock[AccountSftpUserService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountSftpUserServlet = new AccountSftpUserServlet(service, hmacVerifier)
  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  val headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  test("Should List AccountSftpUsers") {
    val ts = clock.now
    val response = Seq(AccountSftpUser(1, 1, "account1", "User1", ts, ts))
    when(service.listSftpUsers()).thenReturn(Future.successful(Right(response)))
    get("/",
      headers = headers) {
      validate[Response[Seq[AccountSftpUser]]](status, 200, body, Response(ResponseStatus.Ok, response))
    }
    verify(service).listSftpUsers()
  }

  test("Should fail to List AccountSftpUsers") {
    val ts = clock.now
    val response = ErrorResponseFactory.get(UnableToListSFTPUsers)
    when(service.listSftpUsers()).thenReturn(Future.successful(Left(response)))
    get("/",
      headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(response))
    }
    verify(service).listSftpUsers()
  }

  test("Should Create Account SFTP Users"){
    val response = true
    when(service.saveAccountSftpUser(1L, "sftp_user1")) thenReturn Future.successful(Right(response))
    post("/",
      Map("account_id" -> "1", "sftp_user" -> "sftp_user1"),
      headers) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, response))
    }
  }

  test("Should Fail to Create Account SFTP Users"){
    val response = ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount)
    when(service.saveAccountSftpUser(1L, "sftp_user1")) thenReturn Future.successful(Left(response))
    post("/",
      Map("account_id" -> "1", "sftp_user" -> "sftp_user1"),
      headers) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(response))
    }
  }

  private def constructErrorResponse(code : ErrorResponse) : Response[ErrorResponse] = {
    Response(ResponseStatus.Error, code)
  }

  private def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
