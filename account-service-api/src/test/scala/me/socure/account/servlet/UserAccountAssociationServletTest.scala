package me.socure.account.servlet

import me.socure.account.service.UserAccountAssociationService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.constants.EnvironmentConstants
import me.socure.model._
import me.socure.model.account._
import me.socure.model.user.{DashboardEnvironment, DashboardUserRole}
import me.socure.model.user.authorization.{Account, EnvironmentSettings, User, UserAuth}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserAccountAssociationServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: UserAccountAssociationService = mock[UserAccountAssociationService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: UserAccountAssociationServlet = new UserAccountAssociationServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get  user account association by user id - success") {
    val userId = 1
    val userAccountAssociation = UserAccountAssociation(1, userId, 1, 1, false, Some(Set(1)), None)
    val expected = Seq(userAccountAssociation)
    when(service.getUserAccountAssociationsByUserId(userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/",
      params = Map("user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAccountAssociation]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserAccountAssociationsByUserId(userId)
  }

  test("Get  user account association by user id - empty") {
    val userId = 1000
    val expected = Seq.empty
    when(service.getUserAccountAssociationsByUserId(userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/",
      params = Map("user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAccountAssociation]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserAccountAssociationsByUserId(userId)
  }

  test("Get user account association by email - success") {
    val email = "<EMAIL>"
    val userAccountAssociation = UserAccountAssociation(1, 1, 1, 1, false, Some(Set(1)), None)
    val expected = Seq(userAccountAssociation)
    when(service.getUserAccountAssociationsByEmail(email)).thenReturn(Future.successful(Right(expected)))
    post("/email",
      body = Serialization.write(email),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAccountAssociation]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserAccountAssociationsByEmail(email)
  }

  test("Get user account association by email - empty") {
    val email = "<EMAIL>"
    val expected = Seq.empty
    when(service.getUserAccountAssociationsByEmail(email)).thenReturn(Future.successful(Right(expected)))
    post("/email",
      body = Serialization.write(email),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAccountAssociation]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserAccountAssociationsByEmail(email)
  }

  test("Insert user account association - success") {
    val userAccountAssociationInput = UserAccountAssociationInput(None, 3, 2, Some(Set(2)), None, 1)
    when(service.insertUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Right(1)))
    post(s"/",
      body = Serialization.write(userAccountAssociationInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).insertUserAccountAssociation(userAccountAssociationInput)
  }

  test("Insert user account association - failure") {
    val userAccountAssociationInput = UserAccountAssociationInput(None, 3, 2, Some(Set(2)), None, 1)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToInsertUserAccountAssociation)
    when(service.insertUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(userAccountAssociationInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertUserAccountAssociation(userAccountAssociationInput)
  }

  test("Update user account association - success") {
    val userAccountAssociationInput = UserAccountAssociationInput(Some(1), 3, 2, Some(Set(2)), None, 1)
    when(service.updateUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Right(1)))
    put(s"/",
      body = Serialization.write(userAccountAssociationInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).updateUserAccountAssociation(userAccountAssociationInput)
  }

  test("Update user account association - failure") {
    val userAccountAssociationInput = UserAccountAssociationInput(Some(1), 3, 2, Some(Set(2)), None, 1)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserAccountAssociation)
    when(service.updateUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Left(expected)))
    put(s"/",
      body = Serialization.write(userAccountAssociationInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updateUserAccountAssociation(userAccountAssociationInput)
  }

  test("Delete user account association - success") {
    val userId = 1
    val accountId = 1
    val updatedBy = 1
    val userAccountAssociationInput = UserAccountAssociationInput(id = None, userId=userId, accountId=accountId, userRoles = None, revoked = None, updatedBy = updatedBy)
    when(service.deleteUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Right(1)))
    post("/delete",
      body = userAccountAssociationInput.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).deleteUserAccountAssociation(userAccountAssociationInput)
  }

  test("Delete user account association - failure") {
    val userId = 1
    val accountId = 1
    val updatedBy = 1
    val userAccountAssociationInput = UserAccountAssociationInput(id = None, userId=userId, accountId=accountId, userRoles = None, revoked = None, updatedBy = updatedBy)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserAccountAssociation)
    when(service.deleteUserAccountAssociation(userAccountAssociationInput)).thenReturn(Future.successful(Left(expected)))
    post("/delete",
      body = userAccountAssociationInput.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteUserAccountAssociation(userAccountAssociationInput)
  }

  test("Validate user account association - success") {
    val userId = 1
    val accountId = 1
    val user = User(id = 2, firstName = "firstName", lastName = "lastName", email = "email", isLocked = false, isAdmin = false)
    val account = Account(1, "accountName", Set(4,5,6,7), true, false, Set.empty)
    val dashboardEnvRoles = Set(DashboardEnvironment(1, EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString, Set(DashboardUserRole.CREATE_TRANSACTION, DashboardUserRole.SETTINGS)))
    val environment = Seq(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
    val programs = Seq(
      AccountIdName(
        id = 2, name = "program1"
      ),AccountIdName(
        id = 3, name = "program2"
      )

    )
    val expected  = UserAuth(user = user, account = account, accounts = Seq(AccountIdName(account.id, account.name)), dashboardEnvRoles = dashboardEnvRoles, environment = environment.toList, redirectUrl = None, createdTime = DateTime.now().withZone(DateTimeZone.UTC), programs = Some(programs))
    when(service.validateUserAccountAssociation(userId, accountId)).thenReturn(Future.successful(Right(expected)))
    post(s"/validate",
      params = Map("user_id" -> userId.toString, "account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[UserAuth]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).validateUserAccountAssociation(userId, accountId)
  }

  test("Validate user account association - failure") {
    val userId = 1
    val accountId = 1
    val expected  = ErrorResponseFactory.get(ExceptionCodes.NoUserAccountAssociation)
    when(service.validateUserAccountAssociation(userId, accountId)).thenReturn(Future.successful(Left(expected)))
    post(s"/validate",
      params = Map("user_id" -> userId.toString, "account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).validateUserAccountAssociation(userId, accountId)
  }

  test("Insert user account associations - success") {
    val accountId = 1
    val userAssociationInputSeq = Seq(UserAssociationInput(id = None, userId = 4, userRoles = Some(Set(3)), revoked = None), UserAssociationInput(id = None, userId = 5, userRoles = Some(Set(3)), revoked = None))
    val updatedBy = 2
    val expected = Seq(UserAssociationResponse("ok","true"))
    when(service.insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)).thenReturn(Future.successful(expected))
    submit(method = "POST",
      path = s"/bulk",
      queryParams = Map("updated_by" -> updatedBy.toString, "account_id" -> accountId.toString),
      body = userAssociationInputSeq.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAssociationResponse]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)
  }

  test("Insert user account associations - failure") {
    val accountId = 1
    val userAssociationInputSeq = Seq(UserAssociationInput(id = None, userId = 4, userRoles = Some(Set(3)), revoked = None), UserAssociationInput(id = None, userId = 5, userRoles = Some(Set(3)), revoked = None))
    val updatedBy = 2
    val expected = Seq(UserAssociationResponse("error","Unable to insert user account association"), UserAssociationResponse("error","Unable to insert user account association"))
    when(service.insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)).thenReturn(Future.successful(expected))
    submit(method = "POST",
      path = s"/bulk",
      queryParams = Map("updated_by" -> updatedBy.toString, "account_id" -> accountId.toString),
      body = userAssociationInputSeq.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserAssociationResponse]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertUserAccountAssociations(accountId, userAssociationInputSeq, updatedBy)
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
