package me.socure.account.servlet

import me.socure.account.service.ConfigureAccountService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.account.{SubAccountCreationRequest, SubAccountUserRequest}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, when}
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

class ConfigureAccountServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {

  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: ConfigureAccountService = mock[ConfigureAccountService]
  val hmacVerifier = mock[HMACHttpVerifier]
  val servlet: ConfigureAccountServlet = new ConfigureAccountServlet(service, hmacVerifier)
  addServlet(servlet, "/*")

  before {
    reset(service)
    reset(hmacVerifier)
  }
  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))

  test("save sub account using parent account api key - success"){
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
      user = Some(SubAccountUserRequest(Some("first_name"), Some("last_name"), "<EMAIL>", Some("contact"))),
      modules = Some(Set("rid-gnKpF0yue2")))

    when(service.createSubAccount(subAccountCreationRequest)) thenReturn Future.successful(Right(true))
    post(
      uri = "/create",
      Serialization.write(subAccountCreationRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(service).createSubAccount(subAccountCreationRequest)
  }

  test("save sub account using parent account api key - failure"){
    val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
      user = Some(SubAccountUserRequest(Some("first_name"), Some("last_name"), "<EMAIL>", Some("contact"))),
      modules = Some(Set("rid-gnKpF0yue2")))

    when(service.createSubAccount(subAccountCreationRequest)) thenReturn Future.successful(Left(ErrorResponseFactory.get(PublicExceptionCodes.ModulesIncorrect)))
    post(
      uri = "/create",
      Serialization.write(subAccountCreationRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(PublicExceptionCodes.ModulesIncorrect)))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(service).createSubAccount(subAccountCreationRequest)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
