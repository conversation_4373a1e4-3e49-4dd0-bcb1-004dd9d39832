package me.socure.account.servlet

import me.socure.account.service.AccountUIConfigurationService
import me.socure.account.validator.V2Validator
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model._
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountUIConfigurationServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountUIConfigurationService = mock[AccountUIConfigurationService]
  val v2Validator: V2Validator = mock[V2Validator]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountUIConfigurationServlet = new AccountUIConfigurationServlet(service, hmacVerifier, 15,480, v2Validator)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  val headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  test("Get UI configuration - success") {
    val response = AccountUIConfiguration(Some(1L), Some(10), Some(10))
    when(service.getUIAccountConfiguration(1L, Creator(1L, 1L))).thenReturn(Future.successful(Right(response)))
    get("/1",
      Map("creator_user_id" -> "1", "creator_account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountUIConfiguration]](status, 200, body, Response(ResponseStatus.Ok, response))
    }
    verify(service).getUIAccountConfiguration(1L, Creator(1L, 1L))
  }

//  test("Save UI configuration for accounts - success") {
//    val request = AccountsUIConfigurationRequest(Seq(1L), 10, 10)
//    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
//    when(service.validateAndSaveUIAccountConfigurationForAccounts(request, Creator(1L, 1L), 10, 15)).thenReturn(Future.successful(auditDetails, Right(true)))
//    when(v2Validator.getRootParentAccountType(1L)).thenReturn(Future.successful(Some(1)))
//    post("/accounts?creator_user_id=1&creator_account_id=1",
//      body = Serialization.write(request),
//      headers = Map(
//        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
//        "Content-Type" -> "application-json; charset=UTF-8",
//        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
//      )) {
//      validate[Response[(AuditDetails,Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, true)))
//    }
//    verify(service).saveUIAccountConfigurationForAccounts(request, Creator(1L, 1L))
//  }

//  test("Save UI configuration - success") {
//    val request = AccountUIConfigurationRequest(1L, 10, 10)
//    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
//    when(service.saveUIAccountConfiguration(request, Creator(1L, 1L))).thenReturn(Future.successful(auditDetails, Right(true)))
//    when(v2Validator.getRootParentAccountType(1L)).thenReturn(Future.successful(Some(1)))
//    post("?creator_user_id=1&creator_account_id=1",
//      body = Serialization.write(request),
//      headers = Map(
//        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
//        "Content-Type" -> "application-json; charset=UTF-8",
//        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
//      )) {
//      validate[Response[(AuditDetails,Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, true)))
//    }
//    verify(service).saveUIAccountConfiguration(request, Creator(1L, 1L))
//  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
