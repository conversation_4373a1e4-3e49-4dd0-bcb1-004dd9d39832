package me.socure.account.servlet

import me.socure.account.dashboardv2.{DashboardAccountServiceV2, EnvironmentSettingsService}
import me.socure.account.service.{AccountPreferencesService, AccountSettingService, DashboardDomainService, DvConfigurationService, SubscriptionChannelRegistryServiceImpl, WatchlistSourceService}
import me.socure.common.clock.RealClock
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.account.{AccountSettings, BusinessUserRolesLess, CAWatchlistPreference, DvConfiguration, EnvironmentWithDomains, OverallEnvironmentSettings, VerboseDvConfiguration}
import me.socure.model.account.watchlist.{CAWatchlistPreferenceForAccount, CAWatchlistPreferenceView}
import me.socure.model.dv.{DVConfigurationDetails, DVConfigurationsForAccount}
import me.socure.model.kyc.{KycNationalIdMatchLogic, KycPreferences, KycPreferencesForAccount, KycPreferencesView}
import me.socure.model.subscription.{SubscriptionChannelRegistryForAccount, SubscriptionChannelRegistryView}
import me.socure.model.{AccountUIConfiguration, ModulesResponse, Response, ResponseStatus, WatchlistSource}
import me.socure.storage.slick.dao.DaoAccountUIConfiguration
import me.socure.storage.slick.tables.account.DtoAccountUIConfiguration
import me.socure.superadmin.AccountSettingsViewServlet
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.mockito.Mockito.{reset, when}
import org.mockito.{Matchers, Mockito}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountSettingsViewServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {

  implicit val ec : ExecutionContext = ExecutionContext.global

  val accountPreferencesService : AccountPreferencesService = mock[AccountPreferencesService]
  val dvConfigurationService : DvConfigurationService = mock[DvConfigurationService]
  val subscriptionChannelRegistryServiceImpl : SubscriptionChannelRegistryServiceImpl = mock[SubscriptionChannelRegistryServiceImpl]
  val daoAccountUIConfiguration: DaoAccountUIConfiguration = mock[DaoAccountUIConfiguration]
  val environmentSettingsService: EnvironmentSettingsService = mock[EnvironmentSettingsService]
  val accountSettingService: AccountSettingService = mock[AccountSettingService]
  val dashboardDomainService: DashboardDomainService = mock[DashboardDomainService]
  val dashboardAccountServiceV2: DashboardAccountServiceV2 = mock[DashboardAccountServiceV2]
  val watchlistSourceService: WatchlistSourceService = mock[WatchlistSourceService]

  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]

  val servlet: AccountSettingsViewServlet = new AccountSettingsViewServlet(accountPreferencesService,
    subscriptionChannelRegistryServiceImpl, dvConfigurationService,daoAccountUIConfiguration,environmentSettingsService,
    accountSettingService,dashboardDomainService,dashboardAccountServiceV2,watchlistSourceService,hmacVerifier)

  val clock = new RealClock
  addServlet(servlet, "/*")

  before{
    reset(dvConfigurationService)
    reset(accountPreferencesService)
    reset(subscriptionChannelRegistryServiceImpl)
    reset(hmacVerifier)
  }

  test("Should return empty details invalid account id") {
    val expected = AccountSettings(Seq.empty, Seq.empty, Seq.empty, Seq.empty)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountPreferencesService.getKycPreferenceForAccount(123)) thenReturn Future.successful(Right(Seq.empty))
    when(accountPreferencesService.getCAWatchListPreferenceForAccount(123)) thenReturn Future.successful(Right(Seq.empty))
    when(subscriptionChannelRegistryServiceImpl.getSubscriptionChannelRegistriesForAccount(123)) thenReturn Future.successful(Right(Seq.empty))
    when(dvConfigurationService.getDvConfigurationForAccount(123)) thenReturn Future.successful(Right(Seq.empty))
    get(
      uri = "/account/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountSettings]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPreferencesService).getKycPreferenceForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(accountPreferencesService).getCAWatchListPreferenceForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(dvConfigurationService).getDvConfigurationForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(subscriptionChannelRegistryServiceImpl).getSubscriptionChannelRegistriesForAccount(Matchers.any(classOf[Long]))
  }

  test("Should return details valid account id") {
    val expectedSCR = List(
      SubscriptionChannelRegistryForAccount(2,List(SubscriptionChannelRegistryView(4941,2L,"<EMAIL>",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1158,"Primary","Email",clock.now,clock.now))),
      SubscriptionChannelRegistryForAccount(1, List(
        SubscriptionChannelRegistryView(9,1L,"https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1157,"Primary","Webhook",clock.now, clock.now),
        SubscriptionChannelRegistryView(10,1,"https://webhook.site/3d2db530-7b4a-4ce0-b615-a2a13f6bec4b",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","WL",None,Some("Parent Account 1234567"),1157,"Primary","Webhook",clock.now,clock.now))))

    val expectedKYC = List(KycPreferencesForAccount(1,KycPreferencesView(Some(false),None,true, KycNationalIdMatchLogic.fuzzy.toString, "default")),
      KycPreferencesForAccount(2,KycPreferencesView(Some(true),None,true, "fuzzy", "default")))

    val expectedWL = List(
      CAWatchlistPreferenceForAccount(1, CAWatchlistPreferenceView(Some(false),1,Some(true),Some("one_year_radius_yyyy_mm_dd"),false,false,0.5,10,Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,Some(false),Some(false))),
      CAWatchlistPreferenceForAccount(2,CAWatchlistPreferenceView(Some(false),2,Some(true),Some("one_year_radius_yyyy_mm_dd"),false,false,0.5,10,Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,Some(false),Some(false))))

    val expectedDV =
      List(DVConfigurationsForAccount(2,List(DVConfigurationDetails("Minimum Age","test1","Review"),
        DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
        DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
        DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
        DVConfigurationDetails("FTB Matching - Dates","12","Accept"),
        DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
        DVConfigurationDetails("Strategy","lenient","NoAction"),
        DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"))),
        DVConfigurationsForAccount(1,List(DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"),
          DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
          DVConfigurationDetails("FirstNameMatchWithNickNameDB","0","Accept"),
          DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
          DVConfigurationDetails("Strategy","lenient","NoAction"),
          DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
          DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
          DVConfigurationDetails("Minimum Age","18","Reject"),
          DVConfigurationDetails("FTB Matching - Dates","12","Accept"))))
    val expected = AccountSettings(kyc = expectedKYC, wl = expectedWL, webhook = expectedSCR, dv = expectedDV)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountPreferencesService.getKycPreferenceForAccount(123)) thenReturn Future.successful(Right(expectedKYC))
    when(accountPreferencesService.getCAWatchListPreferenceForAccount(123)) thenReturn Future.successful(Right(expectedWL))
    when(dvConfigurationService.getDvConfigurationForAccount(123)) thenReturn Future.successful(Right(expectedDV))
    when(subscriptionChannelRegistryServiceImpl.getSubscriptionChannelRegistriesForAccount(123)) thenReturn Future.successful(Right(expectedSCR))
    get(
      uri = "/account/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountSettings]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPreferencesService).getKycPreferenceForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(accountPreferencesService).getCAWatchListPreferenceForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(dvConfigurationService).getDvConfigurationForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(subscriptionChannelRegistryServiceImpl).getSubscriptionChannelRegistriesForAccount(Matchers.any(classOf[Long]))
  }


  test("Should return details product settings for envId and account id") {

    val accountId = 123
    val environmentId = 22222

    val expectedKYC = Some(KycPreferences(Some(true), None, true, Some(KycNationalIdMatchLogic.exact),None))

    val expectedWL = Some(CAWatchlistPreference(Some(false), 1, Some(true), Some("one_year_radius_yyyy_mm_dd"), false,
      false, 0.5, 10, Set("warning", "sanction", "fitness-probity", "pep"),
      Some(Set("warning", "sanction", "fitness-probity", "pep")), Some(Set()), None, None, Some(false)))

    val expectedDV = Map("Document Expiration Grace - Past" -> DvConfiguration(2,"11",1),
        "FTB Matching - Non Dates" -> DvConfiguration(4,"75",3), "FirstNameMatchWithNickNameDB" -> DvConfiguration(11,"0",4),
      "Document Expiration Grace - Future" -> DvConfiguration(3,"20",4), "Strategy" -> DvConfiguration(8,"lenient",0),
      "IPVsExtractedMatchingNonDates" -> DvConfiguration(10,"70",4), "IPVsExtractedMatchingDates" -> DvConfiguration(9,"10",1),
      "Minimum Age" -> DvConfiguration(1,"18",3), "FTB Matching - Dates"-> DvConfiguration(5,"12",4))

    val verboseDVConfig = expectedDV.map{
      case (key,value) => (key,value.getVerboseDvConfigurationForDebug)
    }

    val newDateTime = clock.now
    val uiConfig = Some(DtoAccountUIConfiguration(22222,123,480,480,newDateTime,newDateTime,Some(30941),Some(false),false))
    val accountUIConfig = Some(AccountUIConfiguration(Some(uiConfig.get.accountId), Some(uiConfig.get.autoTimeoutInMinutes), Some(uiConfig.get.idleTimeoutInMinutes), None, uiConfig.get.isForceInherit, uiConfig.get.hideSystemDefinedRoles))
    val environmentWithDomains = Seq(EnvironmentWithDomains(22222,"Production","0.0.0.0/1,*********/1"), EnvironmentWithDomains(22221,"Development","0.0.0.0/1,*********/1"), EnvironmentWithDomains(22220,"Sandbox","0.0.0.0/1,*********/1"))
    val dashboardDomains = "0.0.0.0/1,*********/1"
    val dashboardDomainsSeq:Seq[String] = Seq("0.0.0.0/1","*********/1")
    val watchlistSource = Seq(WatchlistSource(1,"Adverse Media","Adverse Media","Adverse Media",None,newDateTime,false))
    val modules = Seq(BusinessUserRolesLess(1,"KYC"))
    val modulesResponse = ModulesResponse(modules = Set.empty[Int])

    val expectedResponse = Response(ResponseStatus.Ok, OverallEnvironmentSettings(kyc = expectedKYC, wl = expectedWL, dv = verboseDVConfig,accountUIConfig,
      Seq.empty[String], watchlistSource, environmentWithDomains,dashboardDomainsSeq))

    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountPreferencesService.getKycPreference(environmentId)) thenReturn Future.successful(Right(expectedKYC.get))
    when(accountPreferencesService.getCAWatchListPreference(environmentId)) thenReturn Future.successful(Right(expectedWL.get))
    when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId)) thenReturn Future.successful(Right(expectedDV))
    when(daoAccountUIConfiguration.getUIAccountConfiguration(accountId)) thenReturn Future.successful((uiConfig))
    when(environmentSettingsService.getEnvironmentWithDomains(accountId)) thenReturn Future.successful(Right(environmentWithDomains))
    when(dashboardDomainService.getWhitelistedDomainForAccount(accountId)) thenReturn Future.successful(Right(dashboardDomains))
    when(watchlistSourceService.includedWatchlistSources(22222)) thenReturn Future.successful(Right(watchlistSource))
    when(accountSettingService.getModulesByAccountId(accountId,None,false)) thenReturn Future.successful(Right(modules))
    when(dashboardAccountServiceV2.getDefaultModulesForAccounts(accountId,None,false)) thenReturn Future.successful(Right(modulesResponse))
    get(
      uri = "/account/overall/123",
      params = Map("environmentId" -> environmentId.toString, "account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[OverallEnvironmentSettings]](status, 200, body, expectedResponse)
    }
    Mockito.verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(accountPreferencesService).getKycPreference(Matchers.any(classOf[Long]))
    Mockito.verify(accountPreferencesService).getCAWatchListPreference(Matchers.any(classOf[Long]),Matchers.any(classOf[Boolean]))
    Mockito.verify(dvConfigurationService).listDvConfigurationByEnvironment(Matchers.any(classOf[Long]))
    Mockito.verify(daoAccountUIConfiguration).getUIAccountConfiguration(Matchers.any(classOf[Long]))
    Mockito.verify(environmentSettingsService).getEnvironmentWithDomains(Matchers.any(classOf[Long]))
    Mockito.verify(dashboardDomainService).getWhitelistedDomainForAccount(Matchers.any(classOf[Long]))
    Mockito.verify(watchlistSourceService).includedWatchlistSources(Matchers.any(classOf[Long]))
    Mockito.verify(accountSettingService).getModulesByAccountId(Matchers.any(classOf[Long]),Matchers.eq(None),Matchers.any(classOf[Boolean]))
    Mockito.verify(dashboardAccountServiceV2).getDefaultModulesForAccounts(Matchers.any(classOf[Long]),Matchers.eq(None),Matchers.any(classOf[Boolean]))

  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
