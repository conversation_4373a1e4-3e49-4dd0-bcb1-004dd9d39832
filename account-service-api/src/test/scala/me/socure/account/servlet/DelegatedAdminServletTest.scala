package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.superadmin.DelegatedAdminService
import me.socure.model.superadmin.{AccountName, DelegatedAdmin, DelegatedAdminLegacy}
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.superadmin.DelegatedAdminServlet
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/9/16.
  */
class DelegatedAdminServletTest extends ScalatraFunSuite with MockitoSugar{
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service = mock[DelegatedAdminService]
  val servlet = new DelegatedAdminServlet(service)

  addServlet(servlet, "/*")

  val sampleAccountName = AccountName("firstname", "surname", "email", "company")
  val delegatedAdminLegacy = DelegatedAdminLegacy(1, "firstname", "lastname", "company", "contact", "email", Some(Set("ROLE_ADMIN")))
  val updateUserInfoLegacy = DelegatedAdminLegacy(0, "firstname", "lastname", "company", "contact", "email", None)

  val delegatedAdmin = DelegatedAdmin(1, "firstname", "lastname", "company", "contact", "email", Some(Set(BusinessUserRoles.ADMIN.id)))
  val updateUserInfo = DelegatedAdmin(0, "firstname", "lastname", "company", "contact", "email", None)

  test("should get result with 200 status"){
    val expected = Response(ResponseStatus.Ok, Vector(sampleAccountName))
    when(service.getAccountName) thenReturn Future.successful(Right(Vector(sampleAccountName)))
    get("/get_all_admins") {
      validate[Response[Vector[AccountName]]](status, 200, body, expected)
    }
  }

  test("should get empty result with 200 status"){
    val expected = Response(ResponseStatus.Ok, Vector.empty[AccountName])
    when(service.getAccountName) thenReturn Future.successful(Right(Vector.empty))
    get("/get_all_admins") {
      validate[Response[Vector[AccountName]]](status, 200, body, expected)
    }
  }

  test("should be 400 when get account name"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.getAccountName) thenReturn Future.failed(new Exception("Something went wrong"))
    get("/get_all_admins") {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should get delegated admin and response code 200 - legacy"){
    val expected = Response(ResponseStatus.Ok, Vector(delegatedAdminLegacy))
    when(service.getDelegatedAdminForBusinessUser("<EMAIL>")) thenReturn Future.successful(Right(Vector(delegatedAdmin)))
    get("/delegated_admins/<EMAIL>") {
      validate[Response[Vector[DelegatedAdminLegacy]]](status, 200, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should get empty delegated admin and response code 200 - legacy"){
    val expected = Response(ResponseStatus.Ok, Vector.empty[DelegatedAdminLegacy])
    when(service.getDelegatedAdminForBusinessUser("<EMAIL>")) thenReturn Future.successful(Right(Vector.empty))
    get("/delegated_admins/<EMAIL>") {
      validate[Response[Vector[DelegatedAdminLegacy]]](status, 200, body, expected)
    }
  }

  test("should get delegated admin and response code 200"){
    val expected = Response(ResponseStatus.Ok, Vector(delegatedAdmin))
    when(service.getDelegatedAdminForBusinessUser("<EMAIL>")) thenReturn Future.successful(Right(Vector(delegatedAdmin)))
    get("/delegated_admins_v2/<EMAIL>") {
      validate[Response[Vector[DelegatedAdmin]]](status, 200, body, expected)
    }
  }

  test("should get empty delegated admin and response code 200"){
    val expected = Response(ResponseStatus.Ok, Vector.empty[DelegatedAdmin])
    when(service.getDelegatedAdminForBusinessUser("<EMAIL>")) thenReturn Future.successful(Right(Vector.empty))
    get("/delegated_admins_v2/<EMAIL>") {
      validate[Response[Vector[DelegatedAdmin]]](status, 200, body, expected)
    }
  }

  test("should be 400 when get delegated admin"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.getDelegatedAdminForBusinessUser("<EMAIL>")) thenReturn Future.failed(new Exception("Something went wrong"))
    get("/delegated_admins/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("create delegatedadmin response code should be 200 - legacy"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "password")) thenReturn Future.successful(Right(true))
    post("/create_delegated_admin", Map("adminemail" -> "<EMAIL>", "password" -> "password", "user" -> delegatedAdminLegacy.encodeJson())) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("create delegated admin response should be 400 - legacy"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "password")) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/create_delegated_admin", Map("adminemail" -> "<EMAIL>", "password" -> "password", "user" -> delegatedAdminLegacy.encodeJson())) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("create delegatedadmin response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "password")) thenReturn Future.successful(Right(true))
    post("/create_delegated_admin_v2", Map("adminemail" -> "<EMAIL>", "password" -> "password", "user" -> delegatedAdmin.encodeJson())) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("create delegated admin response should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.createDelegatedAdmin("<EMAIL>", delegatedAdmin, "password")) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/create_delegated_admin_v2", Map("adminemail" -> "<EMAIL>", "password" -> "password", "user" -> delegatedAdmin.encodeJson())) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("delete delegatedadmin response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.deleteDelegatedAdmin("<EMAIL>")) thenReturn Future.successful(Right(true))
    post("/delete_delegated_admin", Map("email" -> "<EMAIL>")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("delete delegated admin response should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.deleteDelegatedAdmin("<EMAIL>")) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/delete_delegated_admin", Map("email" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("update delegatedadmin password response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.resetPassword("<EMAIL>", "passworD1.")) thenReturn Future.successful(Right(true))
    post("/update_password", Map("email" -> "<EMAIL>", "password" -> "passworD1.")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("update delegated admin password response should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.resetPassword("<EMAIL>", "passworD1.")) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/update_password", Map("email" -> "<EMAIL>", "password" -> "passworD1.")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("update delegatedadmin roles response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.updatePermission("<EMAIL>", Set(BusinessUserRoles.USER.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id))) thenReturn Future.successful(Right(true))
    post("/update_roles", Map("email" -> "<EMAIL>", "roles" -> "2,5,7")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("update delegatedadmin roles response should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.updatePermission("<EMAIL>", Set(BusinessUserRoles.USER.id, BusinessUserRoles.SETTINGS.id, BusinessUserRoles.FEEDBACK.id))) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/update_roles", Map("email" -> "<EMAIL>", "roles" -> "2,5,7")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("promote delegatedadmin response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.promoteDelegatedAdmin("<EMAIL>")) thenReturn Future.successful(Right(true))
    post("/promote", Map("email" -> "<EMAIL>")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("promote delegatedadmin roles response should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    when(service.promoteDelegatedAdmin("<EMAIL>")) thenReturn Future.failed(new Exception("Something went wrong"))
    post("/promote", Map("email" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("is user exist response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.isUserExist("<EMAIL>")) thenReturn Future.successful(Right(true))
    get("/is_user_exist/<EMAIL>") {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("is user exist response status should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(BusinessUserNotFound))
    when(service.isUserExist("<EMAIL>")) thenReturn Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    get("/is_user_exist/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("update user information response code should be 200 - legacy"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.updateUserInformation("<EMAIL>", updateUserInfo)) thenReturn Future.successful(Right(true))
    post("/update_user_information", Map("user" -> updateUserInfoLegacy.encodeJson(), "email" -> "<EMAIL>")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("update user information response status should be 400 - legacy"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(BusinessUserNotFound))
    when(service.updateUserInformation("<EMAIL>", updateUserInfo)) thenReturn Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    post("/update_user_information", Map("user" -> updateUserInfoLegacy.encodeJson(), "email" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("update user information response code should be 200"){
    val expected = Response(ResponseStatus.Ok, true)
    when(service.updateUserInformation("<EMAIL>", updateUserInfo)) thenReturn Future.successful(Right(true))
    post("/update_user_information", Map("user" -> updateUserInfo.encodeJson(), "email" -> "<EMAIL>")) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("update user information response status should be 400"){
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(BusinessUserNotFound))
    when(service.updateUserInformation("<EMAIL>", updateUserInfo)) thenReturn Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))
    post("/update_user_information", Map("user" -> updateUserInfo.encodeJson(), "email" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
