package me.socure.account.servlet

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.superadmin.ManageAccountsService
import me.socure.common.random.Random
import me.socure.model.account.ParentAccount
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
/**
  * Created by an<PERSON><PERSON><PERSON><PERSON> on 10/25/17.
  */
class ManageAccountsServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service : ManageAccountsService = mock[ManageAccountsService]
  val servlet : ManageAccountsServlet = new ManageAccountsServlet(service)

  addServlet(servlet, "/*")

  before{
    reset(service)
  }

  val error = ErrorResponseFactory.get(UnknownError)
  val negRes = Response(ResponseStatus.Error, error)

  test("create account should throw RegistrationFailed") {
    val expected = ErrorResponse(RegistrationFailed.id, RegistrationFailed.description)
    Mockito.when(service.createAccountIfNotExists(UserFixture.accountCreationForm)) thenReturn Future.successful(Left(expected))
    post("/create_account", UserFixture.accountCreationForm.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("register user should return Registration successful") {
    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(service.createAccountIfNotExists(UserFixture.accountCreationForm)) thenReturn Future.successful(Right(true))
    post("/create_account", UserFixture.accountCreationForm.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("get parent account should return list of parent accounts") {
    val expected = Response(ResponseStatus.Ok, UserFixture.parentAccounts)
    Mockito.when(service.getParentAccounts()) thenReturn Future.successful(Right(UserFixture.parentAccounts))
    get("/parent_accounts") {
      validate[Response[List[ParentAccount]]](status, 200, body, expected)
    }
  }

  test("activate_accounts should return true for valid account ids"){
    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(service.activateAccounts(List(5l, 6l))) thenReturn Future.successful(Right(true))
    post("/activate_accounts", List(5l, 6l).encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("activate_accounts should return error for in-valid account ids") {
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    when(service.activateAccounts(List(99l))) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    post("/activate_accounts", List(99l).encodeJson()) {
      status shouldBe 400
      validate[Response[ErrorResponse]](status, 400, body, expected)
    }
  }

  test("update AccountName by public id"){
    val publicId = Random.alphaNumeric(10)
    val accountName = Random.alphaNumeric(10)
    when(service.updateAccountNamebyPublicId(publicId, accountName)) thenReturn Future.successful(Right(1))
    post("/update/account-name/by/public-id", Map("publicId" -> publicId, "accountName" -> accountName)){
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
  }

  test("update account name should return AccountNotFound error for an invalid public id") {
    val publicId = Random.alphaNumeric(10)
    val accountName = Random.alphaNumeric(10)
    val error = ErrorResponseFactory.get(AccountNotFound)
    when(service.updateAccountNamebyPublicId(publicId, accountName)) thenReturn Future.successful(Left(error))
    post("/update/account-name/by/public-id", Map("publicId" -> publicId, "accountName" -> accountName)){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  test("update account name should return InavlidInputFormat error for an invalid accountname") {
    val publicId = Random.alphaNumeric(10)
    val error = ErrorResponseFactory.get(InvalidInputFormat)
    when(service.updateAccountNamebyPublicId(publicId, "")) thenReturn Future.successful(Left(error))
    post("/update/account-name/by/public-id", Map("publicId" -> publicId, "accountName" -> "")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.InvalidInputFormat)))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
