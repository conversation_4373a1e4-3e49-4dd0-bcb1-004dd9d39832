package me.socure.account.servlet

import me.socure.account.superadmin.InactiveUserService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{PrimaryAccountUser, PrimaryAccountUserLegacy}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.superadmin.InactiveUserServlet
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/7/16.
  */
class InactiveUserServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec = ExecutionContext.global

  val service = mock[InactiveUserService]
  val servlet = new InactiveUserServlet(service)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(service)
  }

  val users = UserActivationDetails("firstname", "surnmae", "<EMAIL>", Option("activationcode"))
  val expectedErrorRes = Response(ResponseStatus.Error,  ErrorResponseFactory.get(UnknownError))

  test("inactive users page should throw error") {
    Mockito.when(service.getInactivesPrimaryAccountAdmins) thenReturn Future.failed(new Exception("Something went wrong"))
    get("/inactive_users") {
      validate[Response[ErrorResponse]](status, 400, body, expectedErrorRes)
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("inactive users page should return users list - legacy") {
    val users = UserFixture.inActivePrimaryUsersLLegacy
    Mockito.when(service.getInactivesPrimaryAccountAdmins) thenReturn Future.successful(UserFixture.inActivePrimaryUsersV)
    get("/inactive_users") {
      validate[Response[List[PrimaryAccountUserLegacy]]](status, 200, body, Response[List[PrimaryAccountUserLegacy]](ResponseStatus.Ok, users))
    }
  }

  test("inactive users page should return users list") {
    val users = UserFixture.inActivePrimaryUsersL
    Mockito.when(service.getInactivesPrimaryAccountAdmins) thenReturn Future.successful(UserFixture.inActivePrimaryUsersV)
    get("/inactive_users_v2") {
      validate[Response[List[PrimaryAccountUser]]](status, 200, body, Response[List[PrimaryAccountUser]](ResponseStatus.Ok, users))
    }
  }

  test("get activation code should return details") {
    Mockito.when(service.getActivationLink(List("<EMAIL>"))) thenReturn Future.successful(Right(List(users)))
    get("/get_activation_code/<EMAIL>") {
      validate[Response[List[UserActivationDetails]]](status, 200, body, Response[List[UserActivationDetails]](ResponseStatus.Ok, List(users)))
    }
  }

  test("should return details for multiple users") {
    Mockito.when(service.getActivationLink(List("<EMAIL>", "<EMAIL>"))) thenReturn Future.successful(Right(List(users, users)))
    get("/get_activation_code/<EMAIL>,<EMAIL>") {
      validate[Response[List[UserActivationDetails]]](status, 200, body, Response[List[UserActivationDetails]](ResponseStatus.Ok, List(users, users)))
    }
  }

  test("get activation code should throw error") {
    Mockito.when(service.getActivationLink(List("<EMAIL>"))) thenReturn Future.failed(new Exception("Something went wrong"))
    get("/get_activation_code/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 400, body, expectedErrorRes)
    }
  }

  test("get activation code should throw error invalid input") {
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(InternalError))
    get("/get_activation_code/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 500, body, expected)
    }
  }

  test("get_reset_code should return details for a single user") {
    Mockito.when(service.getPasswordResetLinkAsActivationDetails(List("<EMAIL>"))) thenReturn Future.successful(Right(List(users)))
    get("/get_reset_code/<EMAIL>") {
      validate[Response[List[UserActivationDetails]]](status, 200, body, Response[List[UserActivationDetails]](ResponseStatus.Ok, List(users)))
    }
  }

  test("get_reset_code should return details for multiple users") {
    Mockito.when(service.getPasswordResetLinkAsActivationDetails(List("<EMAIL>", "<EMAIL>"))) thenReturn Future.successful(Right(List(users, users)))
    get("/get_reset_code/<EMAIL>,<EMAIL>") {
      validate[Response[List[UserActivationDetails]]](status, 200, body, Response[List[UserActivationDetails]](ResponseStatus.Ok, List(users, users)))
    }
  }

  test("get_reset_code should throw error for an invalid email") {
    Mockito.when(service.getPasswordResetLinkAsActivationDetails(List("<EMAIL>"))) thenReturn Future.failed(new Exception("Something went wrong"))
    get("/get_reset_code/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 400, body, expectedErrorRes)
    }
  }

  test("get_reset_code should throw error on invalid input") {
    val expected = Response(ResponseStatus.Error, ErrorResponseFactory.get(InternalError))
    get("/get_reset_code/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 500, body, expected)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
