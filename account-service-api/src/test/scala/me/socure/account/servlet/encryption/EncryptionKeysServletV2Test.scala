package me.socure.account.servlet.encryption

import com.amazonaws.regions.Regions
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.EncryptionKeysService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.encryption.{AccountId, CustomerKeyDetails, EncryptedKey, EncryptedKeyDetails, EncryptedServiceKeys, KmsArnDetails, KmsId}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito
import org.mockito.Mockito.when
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFreeSpec

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

class EncryptionKeysServletV2Test extends ScalatraFreeSpec with BeforeAndAfter with MockitoSugar {

  implicit val ec: ExecutionContext = ExecutionContext.global

  private val encryptionKeysService = mock[EncryptionKeysService]
  private val dashboardAccountServiceV2 = mock[DashboardAccountServiceV2]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  private val servlet = new EncryptionKeysServletV2(
    encryptionKeysService = encryptionKeysService,
    dashboardAccountServiceV2 = dashboardAccountServiceV2,
    hmacVerifier
  )
  val Service1EncryptionName = "Service1"

  def randBytes(len: Int = 256): Array[Byte] = {
    val bytes = new Array[Byte](256)
    Random.nextBytes(bytes)
    bytes
  }
  val accountId = AccountId(1)
  val accountId1UsEast1EncKeyBytes: Array[Byte] = randBytes()
  val accountId1UsWest1EncKeyBytes: Array[Byte] = randBytes()

  val accountId1UsEast1EncKey: EncryptedKey = EncryptedKey(accountId1UsEast1EncKeyBytes)
  val accountId1UsWest1EncKey: EncryptedKey = EncryptedKey(accountId1UsWest1EncKeyBytes)

  addServlet(servlet, "/*")

  "Generate Customer Keys" - {

    val kmsId = KmsId("KmsId-1")
    val customerKeyDetails = CustomerKeyDetails(AccountId(1), kmsId.value)
    "should generate customer keys properly" in {
      Mockito.reset(encryptionKeysService, dashboardAccountServiceV2)
      Mockito.when(encryptionKeysService.generateCustomerKeys(accountId, subAccountIds = Set.empty[AccountId], kmsId)).thenReturn(Future.successful(true))
      Mockito.when(dashboardAccountServiceV2.getParentsSubAccounts(accountId.value)).thenReturn(Future.successful(Right(Set.empty[Long])))
      Mockito.when(encryptionKeysService.testCustomerKms(customerKeyDetails)).thenReturn(Future.successful(Right(true)))
      val expectedResponse = Response(ResponseStatus.Ok, true)
      post("/customer_key",
        body = Serialization.write(customerKeyDetails),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[Boolean]](200, Response(ResponseStatus.Ok, true))
      }
      Mockito.verify(dashboardAccountServiceV2).getParentsSubAccounts(accountId.value)
      Mockito.verify(encryptionKeysService).testCustomerKms(customerKeyDetails)
      Mockito.verify(encryptionKeysService).generateCustomerKeys(accountId, subAccountIds = Set.empty[AccountId], kmsId)
    }

    "should fail to generate customer keys" in {
      val expected = ErrorResponseFactory.get(ExceptionCodes.CustomerKMSEncryptionError)
      Mockito.reset(encryptionKeysService, dashboardAccountServiceV2)
      Mockito.when(encryptionKeysService.generateCustomerKeys(accountId, subAccountIds = Set.empty[AccountId], kmsId)).thenReturn(Future.successful(true))
      Mockito.when(dashboardAccountServiceV2.getParentsSubAccounts(accountId.value)).thenReturn(Future.successful(Right(Set.empty[Long])))
      Mockito.when(encryptionKeysService.testCustomerKms(customerKeyDetails)).thenReturn(Future.successful(Left(expected)))

      post("/customer_key",
        body = Serialization.write(customerKeyDetails),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[ErrorResponse]](400, Response(ResponseStatus.Error, expected))
      }
      Mockito.verify(dashboardAccountServiceV2, Mockito.never).getParentsSubAccounts(accountId.value)
      Mockito.verify(encryptionKeysService).testCustomerKms(customerKeyDetails)
      Mockito.verify(encryptionKeysService, Mockito.never).generateCustomerKeys(accountId, subAccountIds = Set.empty[AccountId], kmsId)
    }
  }

  "GET external KMS ARN list" - {
    "should return external KMS ARN details when it's available for the account" in {
      val response = List(KmsArnDetails("arn", new DateTime("2017-03-14").withZone(DateTimeZone.UTC)))
      Mockito.when(encryptionKeysService.getActiveCustomerKeys(AccountId(1))).thenReturn(Future.successful(response))
      get(s"/customer_keys/1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[List[KmsArnDetails]]](200, Response(ResponseStatus.Ok, response))
      }
    }

    "should return empty list when external KMS ARN not available for the account" in {
      Mockito.when(encryptionKeysService.getActiveCustomerKeys(AccountId(1))).thenReturn(Future.successful(List.empty))
      get(s"/customer_keys/1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[List[KmsArnDetails]]](200, Response(ResponseStatus.Ok, List.empty))
      }
    }
  }

  "GET all keys for an account" - {
    "should return internal keys alone when external KMS not configured for the account" in {
      val response = EncryptedKeyDetails(Map.empty, Map("internal-arn-1" -> List("internal_data_key")))
      Mockito.when(encryptionKeysService.getAllActiveKeys(AccountId(1))).thenReturn(Future.successful(response))
      get(s"/active_keys/1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[EncryptedKeyDetails]](200, Response(ResponseStatus.Ok, response))
      }
    }

    "should return both internal and customer keys when external KMS is configured for the account" in {
      val response = EncryptedKeyDetails(Map("external-arn-1" -> List("external_data_key")), Map("internal-arn-1" -> List("internal_data_key")))
      Mockito.when(encryptionKeysService.getAllActiveKeys(AccountId(1))).thenReturn(Future.successful(response))
      get(s"/active_keys/1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[EncryptedKeyDetails]](200, Response(ResponseStatus.Ok, response))
      }
    }

    "should return all keys for all accounts for a specific service" in {
      val response = Right(EncryptedServiceKeys(Map(1L ->  accountId1UsEast1EncKey, 2L -> accountId1UsEast1EncKey)))
      Mockito.when(encryptionKeysService.getAllKeysForService(Service1EncryptionName, Regions.US_EAST_1.getName)).thenReturn(Future.successful(response))
      get(s"/service_keys/all/$Service1EncryptionName/us-east-1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[Either[ErrorResponse, EncryptedServiceKeys]]](200, Response(ResponseStatus.Ok, response))
      }
    }

    "should fail when provided with an invalid region" in {
      val response = Left(ErrorResponseFactory.get(code = 400, message = "Invalid region"))
      Mockito.when(encryptionKeysService.getAllKeysForService(Service1EncryptionName, "us-best-1")).thenReturn(Future.successful(response))
      get(s"/service_keys/all/$Service1EncryptionName/us-best-1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[Either[ErrorResponse, EncryptedServiceKeys]]](200, Response(ResponseStatus.Ok, response))
      }
    }

    "should return all service keys for specific account for specific service" in {
      val response = Map(Regions.US_EAST_1 ->  accountId1UsEast1EncKey, Regions.US_WEST_1 -> accountId1UsWest1EncKey)
      Mockito.when(encryptionKeysService.getServiceAccountKeys(Service1EncryptionName, AccountId(1))).thenReturn(Future.successful(response))
      get(s"/service_keys/$Service1EncryptionName/1", headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[Map[Regions, EncryptedKey]]](200, Response(ResponseStatus.Ok, response))
      }
    }
  }

  "Test customer KMS keys" - {

    "should return success if encrypted and decrypted data are same using given KMS" in {
      val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
      when(encryptionKeysService.testCustomerKms(customerKey)).thenReturn(Future.successful(Right(true)))
      post("/customer_kms/test",
        body = Serialization.write(customerKey),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[Boolean]](200, Response(ResponseStatus.Ok, true))
      }
    }

    "should return failure if encrypted and decrypted data are different using given KMS" in {
      val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
      when(encryptionKeysService.testCustomerKms(customerKey)).thenReturn(Future.successful(Right(false)))
      post("/customer_kms/test",
        body = Serialization.write(customerKey),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[Boolean]](200, Response(ResponseStatus.Ok, false))
      }
    }

    "should return error if provided arn/region/access is not proper" in {
      val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
      val error = ErrorResponse(199, "ISE")
      when(encryptionKeysService.testCustomerKms(customerKey)).thenReturn(Future.successful(Left(error)))
      post("/customer_kms/test",
        body = Serialization.write(customerKey),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[ErrorResponse]](400, Response(ResponseStatus.Error, error))
      }
    }

  }

  private def validate[T: Manifest](expectedStatus: Int, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
