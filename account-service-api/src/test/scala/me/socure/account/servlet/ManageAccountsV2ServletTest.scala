package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.account.superadmin.ManageAccountsService
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, when}
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import me.socure.model.account.AccConsentReason

import scala.concurrent.{ExecutionContext, Future}


class ManageAccountsV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service : ManageAccountsService = mock[ManageAccountsService]
  val hmacVerifier = mock[HMACHttpVerifier]
  val servlet : ManageAccountsV2Servlet = new ManageAccountsV2Servlet(service, hmacVerifier)
  addServlet(servlet, "/*")

  before{
    reset(service)
    reset(hmacVerifier)
  }
  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))


  test("update consentId for valid accountId"){
    when(service.updateConsentReason(1,2)) thenReturn Future.successful(Right(1))
    post(
      uri = "/update_consent_reason",
      params = Map("account_id" -> "1", "consent_id" -> "2"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(service).updateConsentReason(1,2)
  }

  test("get consent reason for valid account"){
    when(service.getConsentId(2)) thenReturn Future.successful(Right(1))
    get(
      uri = "/get_consent_reason/2",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(service).getConsentId(2)
  }

  test("update isDeleted flag by public id"){
    val publicId = "public-id"
    when(service.updateIsDeleted(publicId, true)) thenReturn Future.successful(Right(1))
    put(
      uri = s"/publicid/$publicId/delete",
      params = Map("delete" -> "true"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  test("update isDeleted flag should return AccountNotFound error for an invalid public id") {
    val publicId = "public-id"
    val error = ErrorResponseFactory.get(AccountNotFound)
    when(service.updateIsDeleted(publicId, true)) thenReturn Future.successful(Left(error))
    put(
      uri = s"/publicid/$publicId/delete",
      params = Map("delete" -> "true"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  test("update IsActive flag by public id"){
    val publicId = "public-id"
    when(service.updateIsActive(publicId, true)) thenReturn Future.successful(Right(1))
    put(
      uri = s"/publicid/$publicId/activate",
      params = Map("activate" -> "true"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  test("update IsActive flag should return AccountNotFound error for an invalid public id") {
    val publicId = "public-id"
    val error = ErrorResponseFactory.get(AccountNotFound)
    when(service.updateIsActive(publicId, true)) thenReturn Future.successful(Left(error))
    put(
      uri = s"/publicid/$publicId/activate",
      params = Map("activate" -> "true"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  test("Search accounts by parameters") {
    val asr: AccountSearchRequest = AccountSearchRequest(
      id = None,
      publicId = None,
      name = None,
      apiKey = None,
      publicApiKey = None,
      email = None,
      deleted = None,
      active = None,
      internal = None,
      isParent = None,
      permissions = Some(Set(1, 22)),
      pagination = None)
    val asr1 = AccountSearchResponse(1,"publicId1", "AccountName1",true,false,false,None,Some("<EMAIL>"),false, false)
    val asr2 = AccountSearchResponse(2,"publicId2", "AccountName2",true,true,false,None,Some("<EMAIL>"),false, false)
    val expected = Vector(asr1, asr2)
    when(service.searchAccounts(asr)) thenReturn Future.successful(Right(expected))
    post(
      uri = s"/accounts/list",
      toJson(asr),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Vector[AccountSearchResponse]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    Mockito.verify(service).searchAccounts(MMatchers.any(classOf[AccountSearchRequest]))
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  test("Email update should throw error when emailid is not provided") {
    val expected: ErrorResponse = ErrorResponse(ExceptionCodes.InvalidInputFormat.id, ExceptionCodes.InvalidInputFormat.description)
    put(
      uri = "/accounts/users/111/email",
      "",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def toJson(req: AccountSearchRequest): String = {
    Serialization.write(req)
  }

}
