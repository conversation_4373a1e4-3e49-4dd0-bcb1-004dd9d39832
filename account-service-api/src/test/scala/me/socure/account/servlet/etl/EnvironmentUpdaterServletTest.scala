package me.socure.account.servlet.etl

import me.socure.account.etl.EnvironmentUpdaterService
import me.socure.etl.EnvironmentUpdaterServlet
import me.socure.model.etl.DtoEtlEnvironment
import me.socure.model.{Response, ResponseStatus}
import me.socure.storage.slick.tables.account.{DtoEnvironment, DtoEnvironmentType}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON>raj on 11/3/16.
  */
class EnvironmentUpdaterServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter{
  implicit val ec = ExecutionContext.global

  val environmentService = mock[EnvironmentUpdaterService]
  val servlet = new EnvironmentUpdaterServlet(environmentService)

  addServlet(servlet, "/*")

  before{
    reset(environmentService)
  }

  test("get environment type"){
    val list = Seq(DtoEnvironmentType(1, "Production"), DtoEnvironmentType(2, "Development"))
    when(environmentService.getEnvironmentTypeTable) thenReturn Future.successful(list)
    get("/get_environment_type_table"){
      validate[Response[Seq[DtoEnvironmentType]]](status, 200, body, Response(ResponseStatus.Ok, list))
    }
  }

  test("get environment type for empty list"){
    when(environmentService.getEnvironmentTypeTable) thenReturn Future.successful(Seq.empty)
    get("/get_environment_type_table"){
      validate[Response[Seq[DtoEnvironmentType]]](status, 200, body, Response(ResponseStatus.Ok, Seq.empty))
    }
  }

  test("get environment table information"){
    val list = Seq(DtoEtlEnvironment(1, "apiKey", "secretkey", "access_token", "token_secret", "domain", 1, 1))
    when(environmentService.getEnvironmentTable) thenReturn Future.successful(list)
    get("/get_environment_table"){
      validate[Response[Seq[DtoEtlEnvironment]]](status, 200, body, Response(ResponseStatus.Ok, list))
    }
  }

  test("get environment table information for empty list"){
    when(environmentService.getEnvironmentTable) thenReturn Future.successful(Seq.empty)
    get("/get_environment_table"){
      validate[Response[Seq[DtoEnvironment]]](status, 200, body, Response(ResponseStatus.Ok, Seq.empty))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
