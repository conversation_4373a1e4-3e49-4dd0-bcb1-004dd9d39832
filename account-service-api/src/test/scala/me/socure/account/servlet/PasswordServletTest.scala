package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{PasswordCharsConstraint, MissingRequiredParameters}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.user.servelt.PasswordServlet
import me.socure.util.JsonEnrichments._
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.ExecutionContext

/**
  * Created by alexand<PERSON> on 3/26/17.
  */
class PasswordServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter {

  implicit val ec = ExecutionContext.global


  val servlet = new PasswordServlet

  addServlet(servlet, "/*")

  test("validate password should return true for correct password") {
    val params = Map(
      "email" -> "<EMAIL>",
      "firstname" -> "alexandre",
      "lastname" -> "agular",
      "password" -> "al3f(D:.RR"
    )

    post("/syntax_check", params) {
      validate[Response[Boolean]](status, 200,body, Response(ResponseStatus.Ok, true))
    }
  }

  test("validate password should return false for incorrect password") {
    val params = Map(
      "email" -> "<EMAIL>",
      "firstname" -> "alexandre",
      "lastname" -> "agular",
      "password" -> "alf(D:.RR"
    )
    val expected = ErrorResponseFactory.get(PasswordCharsConstraint)
    post("/syntax_check", params) {
      validate[Response[ErrorResponse]](status, 400,body, Response(ResponseStatus.Error, expected))
    }
  }

  test("validate password should return an error when missing parameter") {
    val params = Map(
      "email" -> "<EMAIL>",
      "lastname" -> "agular",
      "password" -> "al3f(D:.RR"
    )

    val expected = ErrorResponse(MissingRequiredParameters.id, MissingRequiredParameters.description)
    post("/syntax_check", params) {
      validate[Response[ErrorResponse]](status, 400,body, Response(ResponseStatus.Error, expected))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
