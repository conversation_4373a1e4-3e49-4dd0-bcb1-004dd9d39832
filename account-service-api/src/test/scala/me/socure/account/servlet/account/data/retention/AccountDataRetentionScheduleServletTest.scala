package me.socure.account.servlet.account.data.retention

import me.socure.account.data.retention.AccountDataRetentionScheduleService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, AccountDataRetentionScheduleWithHierarchy, DtoAccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountDataRetentionScheduleServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountDataRetentionScheduleService = mock[AccountDataRetentionScheduleService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountDataRetentionScheduleServlet = new AccountDataRetentionScheduleServlet(service, hmacVerifier)
  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get Account Data Retention Schedule details by account id - success") {
    val accountId = 1
    val expected =  DtoAccountDataRetentionSchedule(1, 1, 3, "Days", "<EMAIL>", clock.now)
    when(service.getAccountDataRetentionSchedule(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[DtoAccountDataRetentionSchedule]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountDataRetentionSchedule(accountId)
  }

  test("Get Account Data Retention Schedule details by account id - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.getAccountDataRetentionSchedule(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountDataRetentionSchedule(accountId)
  }

  test("Get Account Data Retention Schedule details - success") {
    val accountId = 1
    val expected =  Seq(AccountDataRetentionSchedule(1, 3, "Days"))
    when(service.getAccountDataRetentionSchedule()).thenReturn(Future.successful(Right(expected)))
    get(s"/",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[AccountDataRetentionSchedule]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountDataRetentionSchedule()
  }

  test("Get Account Data Retention Schedule details - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound)
    when(service.getAccountDataRetentionSchedule()).thenReturn(Future.successful(Left(expected)))
    get(s"/",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountDataRetentionSchedule()
  }

  test("Get Account Data Retention Schedule with hierarchy details - success") {
    val accountId = 1
    val expected =  AccountDataRetentionScheduleWithHierarchy(1, 3, "Days", Set(2,3))
    when(service.getAccountDataRetentionScheduleWithHierarchy(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account-hierarchy/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountDataRetentionScheduleWithHierarchy]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountDataRetentionScheduleWithHierarchy(accountId)
  }

  test("Get Account Data Retention Schedule details with hierarchy - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotFound)
    when(service.getAccountDataRetentionScheduleWithHierarchy(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account-hierarchy/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountDataRetentionScheduleWithHierarchy(accountId)
  }

  test("Update Account Data Retention Schedule details - success") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(3, "Days", "<EMAIL>")
    val accountId = 1L
    val expected = DtoAccountDataRetentionSchedule(1, 1, 3, "Days", "<EMAIL>", clock.now)
    when(service.upsertAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule)).thenReturn(Future.successful(Right(expected)))
    post("/account/1",
      body = Serialization.write(updateAccountDataRetentionSchedule),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[DtoAccountDataRetentionSchedule]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).upsertAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule)
  }

  test("Update Account Data Retention Schedule details - failure") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(3, "Days", "<EMAIL>")
    val accountId = 1L
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountDataRetentionScheduleNotSaved)
    when(service.upsertAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule)).thenReturn(Future.successful(Left(expected)))
    post("/account/1",
      body = Serialization.write(updateAccountDataRetentionSchedule),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertAccountDataRetentionSchedule(1, updateAccountDataRetentionSchedule)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
