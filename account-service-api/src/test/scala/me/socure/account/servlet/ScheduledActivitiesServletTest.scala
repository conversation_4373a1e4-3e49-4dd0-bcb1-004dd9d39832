package me.socure.account.servlet

import me.socure.account.service.ScheduledActivitiesService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.scheduler.ScheduledActivitiesServlet
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._

/**
  * Created by an<PERSON><PERSON><PERSON><PERSON> on 9/5/17.
  */
class ScheduledActivitiesServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar{
  implicit val ec = ExecutionContext.global

  val scheduledActivitiesService = mock[ScheduledActivitiesService]
  val servlet = new ScheduledActivitiesServlet(scheduledActivitiesService)

  addServlet(servlet, "/*")

  test("should call passwordResetNotification  and return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(scheduledActivitiesService.passwordResetNotification) thenReturn Future.successful(Right(true))
    post("/password_reset_notification") {
      validate[Response[Boolean]](status, 200,body, Response(ResponseStatus.Ok, true))
    }
  }

  test("passwordResetNotification should return error response"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(scheduledActivitiesService.passwordResetNotification) thenReturn Future.successful(Left(resCode))
    post("/password_reset_notification") {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def constructErrorResponse(code : ErrorResponse) : Response[ErrorResponse] = {
    Response(ResponseStatus.Error, code)
  }
}
