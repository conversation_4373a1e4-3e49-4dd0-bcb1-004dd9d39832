package me.socure.account.servlet

import me.socure.account.service.DvConfigurationService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.account.{DvConfiguration, DvConfigurationValueGenerator}
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.{Matchers, Mockito}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DvConfigurationServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec = ExecutionContext.global

  val dvConfigurationService: DvConfigurationService = mock[DvConfigurationService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val dvConfigurationServlet = new DvConfigurationServlet(dvConfigurationService, hmacVerifier)

  addServlet(dvConfigurationServlet, "/*")

  before {
    Mockito.reset(dvConfigurationService)
  }

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  test("Fetch dv configuration for an account") {
    val environmentId: Long = 1234
    val dvConfiguration = DvConfiguration(configId = 1, configValue = "test", decision = 1)
    val dvConfigurationRes : Map[String, DvConfiguration] = Map("Minimum Age" -> dvConfiguration)
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId, None)).thenReturn(Future.successful(Right(dvConfigurationRes)))
    get(s"/${environmentId}",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Map[String, DvConfiguration]]](status, 200, body, Response(ResponseStatus.Ok, dvConfigurationRes))
    }
    Mockito.verify(dvConfigurationService).listDvConfigurationByEnvironment(environmentId, None)
  }

  test("Fetch dv configuration for an environment with no data available") {
    val environmentId: Long = 1234
    val dvConfigurationResult : Map[String, DvConfiguration] = DvConfigurationValueGenerator.dvConfigurationDefaults()
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId, None)).thenReturn(Future.successful(Right(dvConfigurationResult)))
    get(s"/${environmentId}",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Map[String, DvConfiguration]]](status, 200, body, Response(ResponseStatus.Ok, dvConfigurationResult))
    }
  }

  test("Fetch dv configuration for an environment failure case") {
    val environmentId: Long = 1234
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId, None)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindDvConfiguration))))
    get(s"/${environmentId}",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      status shouldBe 400
    }
  }

  test("Save dv configuration for an environment") {
    val dvConfigurationSeq = Seq(DvConfiguration(1,"test",1), DvConfiguration(2,"test2", 1))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expected: (AuditDetails, Either[ErrorResponse, Boolean]) = (auditDetails, Right(true))
    val resp = Response(ResponseStatus.Ok, expected)
    Mockito.when(dvConfigurationService.saveEnvironmentDvConfiguration(dvConfigurationSeq, 1234, None)).thenReturn(Future.successful(expected))
    put("/1234",
      Serialization.write(dvConfigurationSeq),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
      validate[Response[(AuditDetails, Either[ErrorResponse, Boolean])]](status, 200, body, resp)
    }
  }

  test("Save dv configuration for an environment failure") {
    val dvConfigurationSeq = Seq(DvConfiguration(1,"test",1), DvConfiguration(2,"test2", 1))
    val error = ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveDvConfiguration)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expected = (auditDetails, Left(error))
    val res = Response(ResponseStatus.Ok, data = expected)
    Mockito.when(dvConfigurationService.saveEnvironmentDvConfiguration(dvConfigurationSeq, 1234, None)).thenReturn(Future.successful(expected))
    put("/1234",
      Serialization.write(dvConfigurationSeq),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
  }

  test("Fetch dv configuration v2 for an account") {
    val environmentId: Long = 1234
    val creator = Creator(1,1)
    val dvConfiguration = DvConfiguration(configId = 1, configValue = "test", decision = 1)
    val dvConfigurationRes : Map[String, DvConfiguration] = Map("Minimum Age" -> dvConfiguration)
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId, Some(creator))).thenReturn(Future.successful(Right(dvConfigurationRes)))
    get(s"/${environmentId}",
      Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Map[String, DvConfiguration]]](status, 200, body, Response(ResponseStatus.Ok, dvConfigurationRes))
    }
    Mockito.verify(dvConfigurationService).listDvConfigurationByEnvironment(environmentId, Some(creator))
  }

  test("Fetch dv configuration v2 for an environment with no data available") {
    val environmentId: Long = 1234
    val creator = Creator(1,1)
    val dvConfigurationResult : Map[String, DvConfiguration] = DvConfigurationValueGenerator.dvConfigurationDefaults()
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId, Some(creator))).thenReturn(Future.successful(Right(dvConfigurationResult)))
    get(s"/${environmentId}",
      Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[Map[String, DvConfiguration]]](status, 200, body, Response(ResponseStatus.Ok, dvConfigurationResult))
    }
  }

  test("Fetch dv configuration v2 for an environment failure case") {
    val environmentId: Long = 1234
    val creator = Creator(1,1)
    Mockito.when(dvConfigurationService.listDvConfigurationByEnvironment(environmentId,  Some(creator))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotFindDvConfiguration))))
    get(s"/${environmentId}",
      Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      status shouldBe 400
    }
  }

  test("Save dv configuration v2 for an environment") {
    val dvConfigurationSeq = Seq(DvConfiguration(1,"test",1), DvConfiguration(2,"test2", 1))
    val creator = Creator(1,1)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expected = (auditDetails, Right(true))
    val res: Response[(AuditDetails, Either[ErrorResponse, Boolean])] = Response(ResponseStatus.Ok, data = expected)
    Mockito.when(dvConfigurationService.saveEnvironmentDvConfiguration(dvConfigurationSeq, 1234, Some(creator))).thenReturn(Future.successful(expected))
    put(s"/1234?creator_user_id=${creator.userId.toString}&creator_account_id=${creator.accountId.toString}",
      Serialization.write(dvConfigurationSeq),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
      validate[Response[(AuditDetails, Either[ErrorResponse, Boolean])]](status, 200, body, res)
    }
  }

  test("Save dv configuration v2 for an environment failure") {
    val dvConfigurationSeq = Seq(DvConfiguration(1,"test",1), DvConfiguration(2,"test2", 1))
    val creator = Creator(1,1)
    val error = ErrorResponseFactory.get(ExceptionCodes.CouldNotSaveDvConfiguration)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    val expected = (auditDetails, Left(error))
    val res: Response[(AuditDetails, Either[ErrorResponse, Boolean])] = Response(ResponseStatus.Ok, expected)
    Mockito.when(dvConfigurationService.saveEnvironmentDvConfiguration(dvConfigurationSeq, 1234, None)).thenReturn(Future.successful(expected))
    put(s"/1234?creator_user_id=${creator.userId.toString}&creator_account_id=${creator.accountId.toString}",
      Serialization.write(dvConfigurationSeq),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
