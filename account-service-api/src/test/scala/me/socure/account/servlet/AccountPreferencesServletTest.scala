package me.socure.account.servlet

import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{AccountPreferencesService, BusinessUserRoleService}
import me.socure.model.account.{WatchlistCategories, WatchlistPreference}
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model.kyc.{KycNationalIdMatchLogic, KycPreferences, KycPreferencesRequest}
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}
import scala.util.Random

class AccountPreferencesServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val accountPreferencesService = mock[AccountPreferencesService]
  private val businessUserRoleService = mock[BusinessUserRoleService]

  val servlet = new AccountPreferencesServlet(accountPreferencesService, businessUserRoleService)
  addServlet(servlet, "/*")

  test("get kyc preferences"){
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean()
    )
    val res = Response(ResponseStatus.Ok, data = kycPreference)
    when(accountPreferencesService.getKycPreference(environmentId, None)) thenReturn Future.successful(Right(kycPreference))
    get(s"/kyc/$environmentId", Map("forceValidation" -> "true")) {
      validate[Response[KycPreferences]](status, 200, body, res)
    }
  }

  test("save kyc preferences"){
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean()
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, kycPreference))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveKycPreference(environmentId, kycPreference)) thenReturn Future.successful(auditDetails, Right(kycPreference))
    post(s"/kyc/$environmentId", kycPreference.encodeJson()) {
      validate[Response[(AuditDetails, KycPreferences)]](status, 200, body, res)
    }
  }

  test("save kyc preferences for accounts") {
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean(),
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.exact),
      Some(Creator(1, 1))
    )
    val kycPreferenceRequest = KycPreferencesRequest(
      kycPreference,
      Seq(1),
      Seq(1),
      Some(false)
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, kycPreferenceRequest))
    when(businessUserRoleService.isPermissionAvailable(kycPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveKycPreferenceForAccounts(environmentId, kycPreferenceRequest)) thenReturn Future.successful(auditDetails, Right(kycPreferenceRequest))
    post(s"/kyc/accounts/$environmentId", kycPreferenceRequest.encodeJson()) {
      validate[Response[(AuditDetails, KycPreferencesRequest)]](status, 200, body, res)
    }
  }

  test("delete kyc preferences"){
    val environmentId = math.abs(Random.nextLong())
    val deleteStatus = Random.nextBoolean()
    val res = Response(ResponseStatus.Ok, data = deleteStatus)
    when(accountPreferencesService.deleteKycPreference(environmentId)) thenReturn Future.successful(Right(deleteStatus))
    delete(s"/kyc/$environmentId") {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("save watchlist preference") {
    val accountId = 1
    val watchListPreference = WatchlistPreference(
      environmentId = accountId,
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.PEP)
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveWatchlistPreference(watchListPreference)) thenReturn Future.successful(Right(watchListPreference))
    post("/watchlist", watchListPreference.encodeJson()) {
      validate[Response[WatchlistPreference]](status, 200, body, res)
    }
  }

  test("get watchlist preference") {
    val environmentId = 1
    val watchListPreference = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getWatchlistPreference(environmentId, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist", Map("environment_id" -> environmentId.toString, "forceValidation" -> "true")) {
      validate[Response[WatchlistPreference]](status, 200, body, res)
    }
  }

  test("get kyc preferences v2"){
    val environmentId = math.abs(Random.nextLong())
    val creator = Creator(1, 1)
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean()
    )
    val res = Response(ResponseStatus.Ok, data = kycPreference)
    when(accountPreferencesService.getKycPreference(environmentId, Some(creator))) thenReturn Future.successful(Right(kycPreference))
    get(s"/kyc/$environmentId", Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "forceValidation" -> "true")) {
      validate[Response[KycPreferences]](status, 200, body, res)
    }
  }

  test("get kyc preferences v2 with forceValidation false"){
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean()
    )
    val res = Response(ResponseStatus.Ok, data = kycPreference)
    when(accountPreferencesService.getKycPreference(environmentId)) thenReturn Future.successful(Right(kycPreference))
    get(s"/kyc/$environmentId", Map("forceValidation" -> "false")) {
      validate[Response[KycPreferences]](status, 200, body, res)
    }
  }

  test("get watchlist preference v2") {
    val environmentId = 1
    val creator = Creator(1, 1)
    val watchListPreference = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getWatchlistPreference(environmentId, Some(creator))) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist", Map("environment_id" -> environmentId.toString, "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "forceValidation" -> "true")) {
      validate[Response[WatchlistPreference]](status, 200, body, res)
    }
  }

  test("get watchlist preference v2 with forceValidation false") {
    val environmentId = 1
    val watchListPreference = WatchlistPreference(
      environmentId = environmentId,
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getWatchlistPreference(environmentId)) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist", Map("environment_id" -> environmentId.toString, "forceValidation" -> "false")) {
      validate[Response[WatchlistPreference]](status, 200, body, res)
    }
  }

  test("save kyc preferences v2"){
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean(),
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy),
      Some(Creator(1,1))
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, kycPreference))
    when(businessUserRoleService.isPermissionAvailable(kycPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveKycPreference(environmentId, kycPreference)) thenReturn Future.successful(auditDetails, Right(kycPreference))
    post(s"/kyc/$environmentId", kycPreference.encodeJson()) {
      validate[Response[(AuditDetails, KycPreferences)]](status, 200, body, res)
    }
  }

  test("save kyc preferences v2 for accounts") {
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean(),
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy),
      Some(Creator(1, 1))
    )
    val kycPreferenceRequest = KycPreferencesRequest(
      kycPreference,
      Seq(1),
      Seq(1),
      Some(false)
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, kycPreferenceRequest))
    when(businessUserRoleService.isPermissionAvailable(kycPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveKycPreferenceForAccounts(environmentId, kycPreferenceRequest)) thenReturn Future.successful(auditDetails, Right(kycPreferenceRequest))
    post(s"/kyc/accounts/$environmentId", kycPreferenceRequest.encodeJson()) {
      validate[Response[(AuditDetails, KycPreferencesRequest)]](status, 200, body, res)
    }
  }


  test("save kyc preferences v2 fail"){
    val error = ErrorResponseFactory.get(UnknownError)
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean(),
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy),
      Some(Creator(1,1))
    )
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    when(businessUserRoleService.isPermissionAvailable(kycPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveKycPreference(environmentId, kycPreference)) thenReturn Future.successful(auditDetails, Left(error))
    post(s"/kyc/$environmentId", kycPreference.encodeJson()) {
      validate[Response[(AuditDetails, ErrorResponse)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, error)))
    }
  }

  test("save kyc preferences for accounts v2 fail") {
    val error = ErrorResponseFactory.get(UnknownError)
    val environmentId = math.abs(Random.nextLong())
    val kycPreference = KycPreferences(
      exactDob = Some(Random.nextBoolean()),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = Random.nextBoolean(),
      nationalIdMatchLogic = Some(KycNationalIdMatchLogic.fuzzy),
      Some(Creator(1, 1))
    )
    val kycPreferenceRequest = KycPreferencesRequest(
      kycPreferences = kycPreference,
      Seq(1),
      Seq(1),
      Some(false)
    )
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    when(businessUserRoleService.isPermissionAvailable(kycPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveKycPreferenceForAccounts(environmentId, kycPreferenceRequest)) thenReturn Future.successful(auditDetails, Left(error))
    post(s"/kyc/accounts/$environmentId", kycPreferenceRequest.encodeJson()) {
      validate[Response[(AuditDetails, ErrorResponse)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, error)))
    }
  }

  test("save watchlist preference v2") {
    val watchListPreference = WatchlistPreference(
      environmentId = math.abs(Random.nextLong()),
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.PEP),
      Some(Creator(1,1))
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(businessUserRoleService.isPermissionAvailable(watchListPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveWatchlistPreference(watchListPreference)) thenReturn Future.successful(Right(watchListPreference))
    post("/watchlist", watchListPreference.encodeJson()) {
      validate[Response[WatchlistPreference]](status, 200, body, res)
    }
  }

  test("save watchlist preference v2 fail") {
    val watchListPreference = WatchlistPreference(
      environmentId = math.abs(Random.nextLong()),
      exactDoB = true,
      dobAndName = false,
      matchScore = 52,
      categories = Seq(WatchlistCategories.PEP),
      Some(Creator(1,1))
    )

    val error = ErrorResponseFactory.get(UnknownError)
    when(businessUserRoleService.isPermissionAvailable(watchListPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveWatchlistPreference(watchListPreference)) thenReturn Future.successful(Left(error))
    post("/watchlist", watchListPreference.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
  }

  private def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  def validateTuple[T: Manifest, A: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: (A, T)): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[(A, T)]
    actual shouldBe expected
  }
}
