package me.socure.account.servlet

import java.io.File

import org.scalatest.mock.MockitoSugar

trait ImageFilesTestSupport { self : MockitoSugar =>

  private val LogoFileLocation: String = "/logo.png"
  private val StyleFileLocation: String = "/style.json"

  protected val LogoFile: File = new File(getClass.getResource(LogoFileLocation).toURI)
  protected val StyleFile: File = new File(getClass.getResource(StyleFileLocation).toURI)
}
