package me.socure.account.servlet


import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{AccountPreferencesService, BusinessUserRoleService, WatchlistSourceService}
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model._
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferenceRequest, CAWatchlistPreferences, CAWatchlistPreferencesResponse}
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, verify, when}
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}


class AccountPreferenceCAServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val accountPreferencesService = mock[AccountPreferencesService]
  private val watchlistSourceService = mock[WatchlistSourceService]
  private val businessUserRoleService = mock[BusinessUserRoleService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet : AccountPreferenceCAServlet = new AccountPreferenceCAServlet(accountPreferencesService, watchlistSourceService, businessUserRoleService, hmacVerifier)
  addServlet(servlet, "/*")
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  before{
    reset(accountPreferencesService)
    reset(hmacVerifier)
  }
  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))

  val headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )


  test("get ca watchlist preference") {
    val environmentId = 1
    val watchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreference(environmentId, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist/3.0", Map("environmentId" -> environmentId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreference]](status, 200, body, res)
    }
  }

  test("get ca cawatchlist preference") {
    val environmentId = 1
    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None
    ))

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(environmentId, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> environmentId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, res)
    }
  }

  test("save ca watchlist preference") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful((auditDetails, Right(cawatchListPreference)))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }
  }

  test("get watchlist included sources for an environment") {
    Mockito.reset(watchlistSourceService)
    val environmentId = 1
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val watchlistSource2 = WatchlistSource(
      id = 101,
      name = "Source 2",
      category = "category 2",
      subCategory = "sub category 2",
      location = Some("location 2"),
      createAt = clock.now()
    )
    val watchlistsources = Seq(watchlistSource1, watchlistSource2)

    val res = Response(ResponseStatus.Ok, data = watchlistsources)
    when(watchlistSourceService.includedWatchlistSources(environmentId, None)) thenReturn Future.successful(Right(watchlistsources))
    get("/watchlist/sources/included", Map("environmentId" -> environmentId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).includedWatchlistSources(environmentId, None)
  }

  test("get watchlist included sources for an invalid environment - should return empty list") {
    Mockito.reset(watchlistSourceService)
    val environmentId = 100
    val watchlistsources = Seq.empty[WatchlistSource]

    val res = Response(ResponseStatus.Ok, data = watchlistsources)
    when(watchlistSourceService.includedWatchlistSources(environmentId, None)) thenReturn Future.successful(Right(watchlistsources))
    get("/watchlist/sources/included", Map("environmentId" -> environmentId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).includedWatchlistSources(environmentId, None)
  }

  test("include watchlist sources for an environment") {
    Mockito.reset(watchlistSourceService)
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(100L, Set(12L))

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, true))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(watchlistSourceService.includeWatchlistSource(watchlistSourceForEnvironment)) thenReturn Future.successful(auditDetails, Right(true))
    post("/watchlist/sources/include", watchlistSourceForEnvironment.encodeJson(), headers) {
      validate[Response[(AuditDetails, Boolean)]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).includeWatchlistSource(MMatchers.any(classOf[WatchlistSourceForEnvironment]))
  }

  test("list watchlist sources") {
    Mockito.reset(watchlistSourceService)
    val sources = Seq(WatchlistSource(1, "Source1", "category1", "Sub Category1", None, clock.now))
    val res = Response(ResponseStatus.Ok, data = sources)
    when(watchlistSourceService.list) thenReturn Future.successful(Right(sources))
    get("/watchlist/sources", headers = headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).list
  }

  test("list watchlist sources by category") {
    Mockito.reset(watchlistSourceService)
    val sources = Seq(WatchlistSource(1, "Source1", "category1", "Sub Category1", None, clock.now))
    val res = Response(ResponseStatus.Ok, data = sources)
    when(watchlistSourceService.listByCategory(1)) thenReturn Future.successful(Right(sources))
    get("/watchlist/sources/category/1", headers = headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).listByCategory(MMatchers.any(classOf[Int]))
  }

  test("should fail - watchlist sources by invalid category") {
    Mockito.reset(watchlistSourceService)
    val error = ErrorResponseFactory.get(ExceptionCodes.InvalidWatchlistCategory)
    when(watchlistSourceService.listByCategory(100)) thenReturn Future.successful(Left(error))
    get("/watchlist/sources/category/100", headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body,  Response(ResponseStatus.Error, error))
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).listByCategory(MMatchers.any(classOf[Int]))
  }

  test("get ca watchlist preference v2") {
    val environmentId = 1
    val creator = Creator(1,1)
    val watchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreference(environmentId, Some(creator))) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist/3.0", Map("environmentId" -> environmentId.toString, "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreference]](status, 200, body, res)
    }
  }

  test("get ca watchlist preference v2 with forceValidation false") {
    val environmentId = 1
    val watchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None
    )

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreference(environmentId)) thenReturn Future.successful(Right(watchListPreference))
    get("/watchlist/3.0", Map("environmentId" -> environmentId.toString, "forceValidation" -> "false"), headers) {
      validate[Response[CAWatchlistPreference]](status, 200, body, res)
    }
  }


  test("get ca cawatchlist preference v2") {
    val environmentId = 1
    val creator = Creator(1,1)
    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None
      ))

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(environmentId, Some(creator))) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> environmentId.toString, "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, res)
    }
  }

  test("get ca cawatchlist preference v2 with forceValidation false") {
    val environmentId = 1
    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None
      ))

    val res = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(environmentId)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> environmentId.toString, "forceValidation" -> "false"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, res)
    }
  }

  test("get ca cawatchlist preference v2 with count and inheritedby accountId") {
    val environmentId = 1
    val creator = Creator(1,1)
    val watchListPreference = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None)
    val watchListPreferenceResponse = CAWatchlistPreferencesResponse(watchListPreference, byAccountId = Some(1), isForceInherit = Some(false), inheritedToSubAccountsCount = Some(1))

    val res = Response(ResponseStatus.Ok, data = watchListPreferenceResponse)
    when(accountPreferencesService.getCAWatchListPreferencesForAccounts(environmentId, Some(creator))) thenReturn Future.successful(Right(watchListPreferenceResponse))
    get("/accounts/cawatchlist/3.0", Map("environmentId" -> environmentId.toString, "creator_user_id" -> "1", "creator_account_id"-> "1"), headers) {
      validate[Response[CAWatchlistPreferencesResponse]](status, 200, body, res)
    }
  }

  test("get watchlist included sources for an environment v2") {
    Mockito.reset(watchlistSourceService)
    val environmentId = 1
    val creator = Creator(1,1)
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val watchlistSource2 = WatchlistSource(
      id = 101,
      name = "Source 2",
      category = "category 2",
      subCategory = "sub category 2",
      location = Some("location 2"),
      createAt = clock.now()
    )
    val watchlistsources = Seq(watchlistSource1, watchlistSource2)

    val res = Response(ResponseStatus.Ok, data = watchlistsources)
    when(watchlistSourceService.includedWatchlistSources(environmentId, Some(creator))) thenReturn Future.successful(Right(watchlistsources))
    get("/watchlist/sources/included",  Map("environmentId" -> environmentId.toString, "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString, "forceValidation" -> "true"), headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).includedWatchlistSources(environmentId, Some(creator))
  }

  test("get watchlist included sources for an environment v2 with forceValidation false") {
    Mockito.reset(watchlistSourceService)
    val environmentId = 1
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val watchlistSource2 = WatchlistSource(
      id = 101,
      name = "Source 2",
      category = "category 2",
      subCategory = "sub category 2",
      location = Some("location 2"),
      createAt = clock.now()
    )
    val watchlistsources = Seq(watchlistSource1, watchlistSource2)

    val res = Response(ResponseStatus.Ok, data = watchlistsources)
    when(watchlistSourceService.includedWatchlistSources(environmentId)) thenReturn Future.successful(Right(watchlistsources))
    get("/watchlist/sources/included",  Map("environmentId" -> environmentId.toString,"forceValidation" -> "false"), headers) {
      validate[Response[Seq[WatchlistSource]]](status, 200, body, res)
    }
    Mockito.verify(watchlistSourceService, Mockito.times(1)).includedWatchlistSources(environmentId)
  }

  test("save ca watchlist preference v2") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobAndName = false,
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      creator = Some(Creator(1,1))
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(cawatchListPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }
    verify(accountPreferencesService).saveCAWatchlistPreference(cawatchListPreference)
  }

  test("save ca watchlist preference v2 for multi accounts") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobAndName = false,
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      creator = Some(Creator(1, 1))
    )

    val cawatchListPreferenceRequest = CAWatchlistPreferenceRequest(
      cawatchListPreference,
      accountIds= Seq(1),
      environmentTypes= Seq(1),
      isForceInherit= Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(cawatchListPreference.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreferenceRequest)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/accounts/watchlist/3.0", cawatchListPreferenceRequest.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }
    verify(accountPreferencesService).saveCAWatchlistPreference(cawatchListPreferenceRequest)
  }

  test("include watchlist sources for an environment v2") {
    Mockito.reset(watchlistSourceService)
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(100L, Set(12L),Some(Creator(1,1)))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, true))
    when(businessUserRoleService.isPermissionAvailable(watchlistSourceForEnvironment.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(watchlistSourceService.includeWatchlistSource(watchlistSourceForEnvironment)) thenReturn Future.successful(auditDetails, Right(true))
    post("/watchlist/sources/include", watchlistSourceForEnvironment.encodeJson(), headers) {
      validate[Response[(AuditDetails, Boolean)]](status, 200, body, res)
    }
    verify(watchlistSourceService).includeWatchlistSource(watchlistSourceForEnvironment)
  }

  test("include watchlist sources for an environment v2 with multi accounts") {
    Mockito.reset(watchlistSourceService)
    val watchlistSourceForEnvironment = WatchlistSourceForEnvironment(100L, Set(12L), Some(Creator(1, 1)))
    val watchlistPreferenceRequest = WatchlistSourceRequest(
      watchlistSourceForEnvironment,
      accountIds = Seq(1),
      environmentTypes = Seq(1),
      isForceInherit = Some(false)
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, true))
    when(businessUserRoleService.isPermissionAvailable(watchlistSourceForEnvironment.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(watchlistSourceService.includeWatchlistSource(watchlistPreferenceRequest)) thenReturn Future.successful(auditDetails, Right(true))
    post("/accounts/watchlist/sources/include", watchlistPreferenceRequest.encodeJson(), headers) {
      validate[Response[(AuditDetails, Boolean)]](status, 200, body, res)
    }
    verify(watchlistSourceService).includeWatchlistSource(watchlistPreferenceRequest)
  }

  test("get historical ranges") {
    val historicalRanges = Set("1 year", "All Time")
    val res = Response(ResponseStatus.Ok, data = historicalRanges)
    when(accountPreferencesService.getHistoricalRange()) thenReturn Future.successful(historicalRanges)
    get("/historicalrange", headers = headers) {
      validate[Response[Set[String]]](status, 200, body, res)
    }
  }

  test("save ca watchlist preference with suppressPepsWithoutURL enabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(true),
      isWatchlistTransactionsAutoMonitored = Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }
  }

  test("save ca watchlist preference with suppressPepsWithoutURL disabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(false),
      isWatchlistTransactionsAutoMonitored = Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }
  }

  test("get ca watchlist preference with suppressPepsWithoutURL enabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(true),
      isWatchlistTransactionsAutoMonitored = Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }

    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(true),
        isWatchlistTransactionsAutoMonitored = Some(false)),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(true),
        isWatchlistTransactionsAutoMonitored = Some(false)
      ))

    val fetchRes = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(123, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> "123", "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, fetchRes)
    }
  }

  test("get ca watchlist preference with suppressPepsWithoutURL disabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(false),
      isWatchlistTransactionsAutoMonitored = Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }

    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(false),
        isWatchlistTransactionsAutoMonitored = Some(false)),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(false),
        isWatchlistTransactionsAutoMonitored = Some(false)
      ))

    val fetchRes = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(123, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> "123", "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, fetchRes)
    }
  }

  test("get ca watchlist preference with watchlist auto monitoring enabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(true),
      isWatchlistTransactionsAutoMonitored = Some(true)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }

    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(true)),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(true),
        isWatchlistTransactionsAutoMonitored = Some(true)
      ))

    val fetchRes = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(123, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> "123", "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, fetchRes)
    }
  }

  test("get ca watchlist preference with watchlist auto monitoring disabled") {
    val cawatchListPreference = CAWatchlistPreference(
      environmentId = 123,
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = None,
      country = Option(Set("US")),
      historicalRange = None,
      suppressPepsWithoutURL = Some(false),
      isWatchlistTransactionsAutoMonitored = Some(false)
    )

    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, data = (auditDetails, cawatchListPreference))
    Mockito.reset(accountPreferencesService)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(accountPreferencesService.saveCAWatchlistPreference(cawatchListPreference)) thenReturn Future.successful(auditDetails, Right(cawatchListPreference))
    post("/watchlist/3.0", cawatchListPreference.encodeJson(), headers) {
      validate[Response[(AuditDetails, CAWatchlistPreference)]](status, 200, body, res)
    }

    val watchListPreference = CAWatchlistPreferences(
      standard = null,
      plus = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(false)),
      premier = CAWatchlistPreference(
        environmentId = 123,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.1,
        limit = 10,
        screeningCategories = Set("PEP"),
        watchlistScreeningCategories = None,
        country = Option(Set("US")),
        historicalRange = None,
        suppressPepsWithoutURL = Some(false),
        isWatchlistTransactionsAutoMonitored = Some(false)
      ))

    val fetchRes = Response(ResponseStatus.Ok, data = watchListPreference)
    when(accountPreferencesService.getCAWatchListPreferences(123, None)) thenReturn Future.successful(Right(watchListPreference))
    get("/cawatchlist/3.0", Map("environmentId" -> "123", "forceValidation" -> "true"), headers) {
      validate[Response[CAWatchlistPreferences]](status, 200, body, fetchRes)
    }
  }

  private def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
