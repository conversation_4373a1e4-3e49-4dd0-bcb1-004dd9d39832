package me.socure.account.servlet

import me.socure.account.service.EINService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model._
import me.socure.model.ein.{EIN, EINRequest, EINResponse, LookupApiKeyRequest, LookupApiKeyResponse, LookupApiKeyServiceIdRequest, LookupApiKeyServiceIdResponse}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
class EINServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: EINService = mock[EINService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: EINServlet = new EINServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get EIN - success") {
    val accountId = 1L
    val expected = Some(EIN("*********"))
    when(service.fetchEIN(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/$accountId",
      headers = Map(
      "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
      "Content-Type" -> "application-json; charset=UTF-8",
      "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[Option[EIN]]](status, 200, body, Response(ResponseStatus.Ok, expected))
      }
    verify(service).fetchEIN(accountId)
  }

  test("Get EIN - fail") {
    val accountId = 1L
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.fetchEIN(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).fetchEIN(accountId)
  }

  test("Save EIN - success") {
    val einRequest = EINRequest(1L, "*********")
    val einResponse = EINResponse(1L, "*********")
    when(service.upsertEIN(einRequest)).thenReturn(Future.successful(Right(einResponse)))
    post(s"/",
      body = Serialization.write(einRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[EINResponse]](status, 200, body, Response(ResponseStatus.Ok, einResponse))
    }
    verify(service).upsertEIN(einRequest)
  }

  test("Save EIN - fail") {
    val einRequest = EINRequest(1L, "*********")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.upsertEIN(einRequest)).thenReturn(Future.successful(Left(expected)))
    post(s"/",
      body = Serialization.write(einRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertEIN(einRequest)
  }

  test("Update EIN - success") {
    val einRequest = EINRequest(1L, "*********")
    val einResponse = EINResponse(1L, "*********")
    when(service.upsertEIN(einRequest)).thenReturn(Future.successful(Right(einResponse)))
    put(s"/",
      body = Serialization.write(einRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[EINResponse]](status, 200, body, Response(ResponseStatus.Ok, einResponse))
    }
    verify(service).upsertEIN(einRequest)
  }

  test("Update EIN - fail") {
    val einRequest = EINRequest(1L, "*********")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.upsertEIN(einRequest)).thenReturn(Future.successful(Left(expected)))
    put(s"/",
      body = Serialization.write(einRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertEIN(einRequest)
  }

  test("Update LookupApiKey - success") {
    val lookupApiKeyRequest = LookupApiKeyRequest(1L, "samplekey")
    val lookupApiKeyResponse = LookupApiKeyResponse(1L, "samplekey")
    when(service.upsertLookupApiKey(lookupApiKeyRequest, true)).thenReturn(Future.successful(Right(lookupApiKeyResponse)))
    post(s"/lookup-api-key",
      body = Serialization.write(lookupApiKeyRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[LookupApiKeyResponse]](status, 200, body, Response(ResponseStatus.Ok, lookupApiKeyResponse))
    }
    verify(service).upsertLookupApiKey(lookupApiKeyRequest, onlyWhen = true)
  }

  test("Update LookupApiKey - fail") {
    val lookupApiKeyRequest = LookupApiKeyRequest(1L, "samplekey")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.upsertLookupApiKey(lookupApiKeyRequest, true)).thenReturn(Future.successful(Left(expected)))
    post(s"/lookup-api-key",
      body = Serialization.write(lookupApiKeyRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertLookupApiKey(lookupApiKeyRequest, onlyWhen = true)
  }

  test("Update OTPWorkFlowInfo - success") {
    val otpWorkFlowRequest = LookupApiKeyServiceIdRequest(1L, "samplekey", "serviceId")
    val otpWorkFlowResponse = LookupApiKeyServiceIdResponse(1L, "samplekey", "serviceId")
    when(service.upsertLookupApiKeyAndServiceSid(otpWorkFlowRequest, true)).thenReturn(Future.successful(Right(otpWorkFlowResponse)))
    post(s"/lookup-api-key-servicesid",
      body = Serialization.write(otpWorkFlowRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[LookupApiKeyServiceIdResponse]](status, 200, body, Response(ResponseStatus.Ok, otpWorkFlowResponse))
    }
    verify(service).upsertLookupApiKeyAndServiceSid(otpWorkFlowRequest, onlyWhen = true)
  }

  test("Update OTPWorkFlowInfo - fail") {
    val otpWorkFlowRequest = LookupApiKeyServiceIdRequest(1L, "samplekey", "serviceId")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.upsertLookupApiKeyAndServiceSid(otpWorkFlowRequest, true)).thenReturn(Future.successful(Left(expected)))
    post(s"/lookup-api-key-servicesid",
      body = Serialization.write(otpWorkFlowRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertLookupApiKeyAndServiceSid(otpWorkFlowRequest, onlyWhen = true)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
