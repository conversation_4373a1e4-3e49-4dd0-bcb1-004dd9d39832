package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.superadmin.LeaderboardService
import me.socure.model.account.{AccountDetails, AccountIdName}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.superadmin.LeaderboardServlet
import me.socure.util.JsonEnrichments._
import org.mockito.Matchers
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/17/16.
  */
class LeaderboardServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service : LeaderboardService = mock[LeaderboardService]
  val servlet : LeaderboardServlet = new LeaderboardServlet(service)

  addServlet(servlet, "/*")

  before{
    reset(service)
  }

  val error = ErrorResponseFactory.get(UnknownError)
  val negRes = Response(ResponseStatus.Error, error)

  test("should return internal account ids"){
    val expectedRes = Response(ResponseStatus.Ok, Seq(1l))
    when(service.getInternalAccountIds) thenReturn Future.successful(Right(Seq(1l)))
    get("/get_internal_accounts") {
      validate[Response[Seq[Long]]](status, 200, body, expectedRes)
    }
  }

  test("status code should be 400 for get internal account ids"){
    when(service.getInternalAccountIds) thenReturn Future.successful(Left(error))
    get("/get_internal_accounts"){
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("should return account name") {
    val accountName = Map(1l -> AccountIdName(1l, "Socure"))
    val expectedRes = Response(ResponseStatus.Ok, accountName)
    when(service.getAccountNameList) thenReturn Future.successful(Right(accountName))
    get("/get_account_name"){
      validate[Response[Map[Long, AccountIdName]]](status, 200, body, expectedRes)
    }
  }

  test("status code should be 400 on get account name call"){
    when(service.getAccountNameList) thenReturn Future.successful(Left(error))
    get("/get_account_name"){
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("should return account details") {
    val account = AccountDetails(22,"primaryemail","accountname")
    val expectedRes = Response(ResponseStatus.Ok, account)
    when(service.getAccountByApiKey(Matchers.anyString())) thenReturn Future.successful(Right(account))
    get("/get_account_by_apikey/valid_apikey"){
      validate[Response[AccountDetails]](status, 200, body, expectedRes)
    }
  }

  test("status code should be 400 when get account by apikey") {
    val acc = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, acc)
    when(service.getAccountByApiKey(Matchers.anyString())) thenReturn Future.successful(Left(acc))
    get("/get_account_by_apikey/invalid_apikey"){
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
