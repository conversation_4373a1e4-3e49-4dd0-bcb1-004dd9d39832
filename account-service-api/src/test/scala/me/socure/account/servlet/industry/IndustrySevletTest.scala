package me.socure.account.servlet.industry

import me.socure.account.industries.IndustriesManagementService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.industry.IndustryServlet
import me.socure.model.account.AccountDetails
import me.socure.model.{ErrorResponse, Industry, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 8/31/16.
  */
class IndustrySevletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service = mock[IndustriesManagementService]
  val servlet = new IndustryServlet(service)

  before {
    reset(service)
  }

  addServlet(servlet, "/*")

  val industries = Vector(Industry("22", "Construction"), Industry("23", "Utilities"))

  test("get industry list with status 200") {
    when(service.getIndustriesList) thenReturn Future.successful(Right(industries))
    get("/get_list") {
      validate[Response[Vector[Industry]]](status, 200, body, Response(ResponseStatus.Ok, industries))
    }
  }

  test("get industry empty list with status 200") {
    when(service.getIndustriesList) thenReturn Future.successful(Right(Vector.empty[Industry]))
    get("/get_list") {
      validate[Response[Vector[Industry]]](status, 200, body, Response(ResponseStatus.Ok, Vector.empty[Industry]))
    }
  }

  test("get industry list status should be 400") {
    when(service.getIndustriesList) thenReturn Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    get("/get_list") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError)))
    }
  }

  test("should return account details by industry") {
    val account = AccountDetails(22,"primaryemail","accountname")
    val expectedRes = Response(ResponseStatus.Ok, Seq(account))
    when(service.getAccountByIndustry("valid_sector")) thenReturn Future.successful(Right(Seq(account)))
    get("/get_accounts_by_industry/valid_sector"){
      validate[Response[Seq[AccountDetails]]](status, 200, body, expectedRes)
    }
  }

  test("Account Not found shound be returned for invalid industry code") {
    val acc = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, acc)
    when(service.getAccountByIndustry("invalid_industry")) thenReturn Future.successful(Left(acc))
    get("/get_accounts_by_industry/invalid_industry"){
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("upsert industry should be successful") {
    val industry = Industry("code", "des")
    when(service.upsertIndustry(industry)) thenReturn Future.successful(Right(true))
    post("/upsert_industry", industry.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response[Boolean](data=true, status = ResponseStatus.Ok))
    }
  }

  test("upsert industry should fail") {
    val industry = Industry("code", "des")
    when(service.upsertIndustry(industry)) thenReturn Future.successful(Left(ErrorResponseFactory.get(UnknownError)))
    post("/upsert_industry", industry.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body,  Response(ResponseStatus.Error,ErrorResponseFactory.get(UnknownError)))
    }
  }

  test("delete industry should be successful") {
    when(service.deleteIndustry("code")) thenReturn Future.successful(Right(true))
    post("/delete_industry", Map("sector"->"code")) {
      validate[Response[Boolean]](status, 200, body, Response[Boolean](data=true, status = ResponseStatus.Ok))
    }
  }

  test("delete industry should fail") {
    when(service.deleteIndustry("code")) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountsAssociatedWithIndustry)))
    post("/delete_industry", Map("sector"->"code")) {
      validate[Response[ErrorResponse]](status, 400, body,  Response[ErrorResponse](ResponseStatus.Error,ErrorResponseFactory.get(AccountsAssociatedWithIndustry)))
    }
  }

  test("should return industry") {
    industries.headOption.fold(fail) ( i => {
      val expectedRes = Response(ResponseStatus.Ok, i)
      when(service.getIndustryBySector("valid_sector")) thenReturn Future.successful(Right(i))
      get("/get_industry/valid_sector") {
        validate[Response[Industry]](status, 200, body, expectedRes)
      }
    })
  }

  test("should return industry not found") {
    val acc = ErrorResponseFactory.get(IndustryNotFound)
    val expectedRes = Response(ResponseStatus.Error, acc)
    when(service.getIndustryBySector("invalid_sector")) thenReturn Future.successful(Left(acc))
    get("/get_industry/invalid_sector"){
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }


  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
