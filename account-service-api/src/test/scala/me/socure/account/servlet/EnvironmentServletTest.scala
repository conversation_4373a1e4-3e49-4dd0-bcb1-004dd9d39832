package me.socure.account.servlet

import me.socure.account.service.EnvironmentService
import me.socure.model.account.EnvironmentData
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

class EnvironmentServletTest  extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {
  implicit val ec = ExecutionContext.global

  private val environmentService = mock[EnvironmentService]
  private val servlet = new EnvironmentServlet(environmentService)
  addServlet(servlet, "/*")

  private val envId = 1
  private val envData = EnvironmentData.apply(1, "apiKey", "secretkey", "access_token", "token_secret", "domain", 1, 1)

  before {
    reset(environmentService)
  }

  test("get environment by id"){
    val res = Response(ResponseStatus.Ok, data = envData)
    val expectedFuture: Future[Either[ErrorResponse, EnvironmentData]] = Future.successful(Right(envData))
    when(environmentService.getEnvirontmentById(1)) thenReturn expectedFuture
    get(s"/$envId")
    {
      validate[Response[EnvironmentData]](status, 200, body, res)
    }
  }

  test("get environment V2 by id") {
    val res = Response(ResponseStatus.Ok, data = envData)
    val expectedFuture: Future[Either[ErrorResponse, Option[EnvironmentData]]] = Future.successful(Right(Some(envData)))
    when(environmentService.getEnvironmentDataById(1)) thenReturn expectedFuture
    get(s"/v2/$envId") {
      validate[Response[EnvironmentData]](status, 200, body, res)
    }
  }

  test("get environment V2 by id when empty data") {
    val res = Response[Option[EnvironmentData]](ResponseStatus.Ok, data = None)
    val expectedFuture: Future[Either[ErrorResponse, Option[EnvironmentData]]] = Future.successful(Right(None))
    when(environmentService.getEnvironmentDataById(1)) thenReturn expectedFuture
    get(s"/v2/$envId") {
      validate[Response[Option[EnvironmentData]]](status, 200, body, res)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
