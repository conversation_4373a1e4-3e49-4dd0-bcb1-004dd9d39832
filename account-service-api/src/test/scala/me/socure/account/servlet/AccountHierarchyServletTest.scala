package me.socure.account.servlet

import me.socure.account.service.AccountHierarchyService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model._
import me.socure.model.account.{AccountHierarchy, _}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> <PERSON>
 */

class AccountHierarchyServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountHierarchyService = mock[AccountHierarchyService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountHierarchyServlet = new AccountHierarchyServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get account hierarchy by id - success") {
    val id = 1
    val expected = AccountHierarchy(5,1,"1/",1,1,administer = true,0)
    when(service.getAccountHierarchy(id)).thenReturn(Future.successful(Right(expected)))
    get(s"/$id",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountHierarchy]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountHierarchy(id)
  }

  test("Get account hierarchy by id - failure") {
    val id = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyNotFound)
    when(service.getAccountHierarchy(id)).thenReturn(Future.successful(Left(expected)))
    get(s"/$id",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountHierarchy(id)
  }

  test("Get account hierarchy by account - success") {
    val accountId = 1012
    val expected = AccountHierarchy(5,1,"1/",1,1,administer = true,0)
    when(service.getAccountHierarchyByAccountId(accountId)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountHierarchy]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountHierarchyByAccountId(accountId)
  }


  test("Get account hierarchy by account - failure") {
    val accountId = 1012
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountHierarchyByAccountFetchError)
    when(service.getAccountHierarchyByAccountId(accountId)).thenReturn(Future.successful(Left(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountHierarchyByAccountId(accountId)
  }


  test("Insert account hierarchy - success") {
    val accountHierarchy = AccountHierarchyInput(None,2,"/2",2,1,administer = true,Some(0))
    when(service.insertAccountHierarchy(accountHierarchy)).thenReturn(Future.successful(Right(1)))
    post("/",
      body = Serialization.write(accountHierarchy),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).insertAccountHierarchy(accountHierarchy)
  }

  test("Insert account hierarchy - failure") {
    val accountHierarchy = AccountHierarchyInput(None,2,"/2",2,1,administer = true,Some(0))
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToInsertAccountHierarchy)
    when(service.insertAccountHierarchy(accountHierarchy)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(accountHierarchy),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertAccountHierarchy(accountHierarchy)
  }

  test("Update account hierarchy - success") {
    val accountHierarchy = AccountHierarchyInput(Some(2),2,"/2",2,1,administer = true,Some(0))
    when(service.updateAccountHierarchy(accountHierarchy)).thenReturn(Future.successful(Right(1)))
    put("/",
      body = Serialization.write(accountHierarchy),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).updateAccountHierarchy(accountHierarchy)
  }

  test("Update account hierarchy - failure") {
    val accountHierarchy = AccountHierarchyInput(Some(2),2,"/2",2,1,administer = true,Some(0))
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateAccountHierarchy)
    when(service.updateAccountHierarchy(accountHierarchy)).thenReturn(Future.successful(Left(expected)))
    put("/",
      body = Serialization.write(accountHierarchy),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updateAccountHierarchy(accountHierarchy)
  }

  test("Update administer flag: allow  - success") {
    when(service.updateAdministerFlag(1L, administerFlag = true)).thenReturn(Future.successful(Right(true)))
    put("/administer/allow",
      params = Map("account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateAdministerFlag(1L, administerFlag = true)
  }

  test("Update administer flag: allow - failure") {
    when(service.updateAdministerFlag(100L, administerFlag = true)).thenReturn(Future.successful(Right(false)))
    put("/administer/allow",
      params = Map("account_id" -> "100"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
    verify(service).updateAdministerFlag(100L, administerFlag = true)
  }

  test("Update administer flag: deny  - success") {
    when(service.updateAdministerFlag(1L, administerFlag = false)).thenReturn(Future.successful(Right(true)))
    put("/administer/deny",
      params = Map("account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateAdministerFlag(1L, administerFlag = false)
  }

  test("Update administer flag: deny - failure") {
    when(service.updateAdministerFlag(100L, administerFlag = false)).thenReturn(Future.successful(Right(false)))
    put("/administer/deny",
      params = Map("account_id" -> "100"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
    verify(service).updateAdministerFlag(100L, administerFlag = false)
  }

  test("Update hierarchy status:deactivate  - success") {
    when(service.updateHierarchyStatus(1L, 0)).thenReturn(Future.successful(Right(true)))
    put("/deactivate",
      params = Map("account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateHierarchyStatus(1L, 0)
  }

  test("Update hierarchy status:deactivate  - failure") {
    when(service.updateHierarchyStatus(100L, 0)).thenReturn(Future.successful(Right(false)))
    put("/deactivate",
      params = Map("account_id" -> "100"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
    verify(service).updateHierarchyStatus(100L, 0)
  }

  test("Update hierarchy status:activate  - success") {
    when(service.updateHierarchyStatus(1L, 1)).thenReturn(Future.successful(Right(true)))
    put("/activate",
      params = Map("account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateHierarchyStatus(1L, 1)
  }

  test("Update hierarchy status:activate - failure") {
    when(service.updateHierarchyStatus(100L, 1)).thenReturn(Future.successful(Right(false)))
    put("/activate",
      params = Map("account_id" -> "100"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
    verify(service).updateHierarchyStatus(100L, 1)
  }

  test("Update primary admin count  - success") {
    when(service.updatePrimaryAdminCount(1L, 1)).thenReturn(Future.successful(Right(true)))
    put("/update/admin_count",
      params = Map("account_id" -> "1", "count" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updatePrimaryAdminCount(1L, 1)
  }

  test("Update primary admin count - failure") {
    when(service.updatePrimaryAdminCount(100L, 1)).thenReturn(Future.successful(Right(false)))
    put("/update/admin_count",
      params = Map("account_id" -> "100", "count" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
    verify(service).updatePrimaryAdminCount(100L, 1)
  }

  test("Get account hierarchy - success") {
    val accountId = 1
    val userId = 1
    val expected = List(AccountInfoV2WithIndustry(5,14,"acc-db3y72Tg30","AccountName14","14/",2,administer = true,0, state = false, "industry", true), AccountInfoV2WithIndustry(6,15,"acc-Kf3habgWE6","AccountName15","14/15/",2,administer = true,0, state = false, "industry", true))
    when(service.listAccountHierarchy(accountId, userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/list",
      params = Map("account_id" -> "1", "user_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[List[AccountInfoV2WithIndustry]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).listAccountHierarchy(accountId, userId)
  }

  test("Get account hierarchy  - should return empty list") {
    val accountId = 1
    val userId = 1
    val expected = List.empty[AccountInfoV2WithIndustry]
    when(service.listAccountHierarchy(accountId, userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/list",
      params = Map("account_id" -> "1", "user_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[List[AccountInfoV2WithIndustry]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).listAccountHierarchy(accountId, userId)
  }

  test("Validate account access - success") {
    when(service.validateAccountAccess(2,1)).thenReturn(Future.successful(Right(true)))
    get("/validate",
      params = Map("account_id" -> "2", "creator_account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).validateAccountAccess(2,1)
  }

  test("Validate account access - failure") {
    when(service.validateAccountAccess(2,1)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    get("/validate",
      params = Map("account_id" -> "2", "creator_account_id" -> "1"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.UnknownError)))
    }
    verify(service).validateAccountAccess(2,1)
  }

  test("Validate account access with permissions - success") {
    when(service.validateAccountAccess(2,1, Set(23, 45))).thenReturn(Future.successful(Right(true)))
    get("/validate/access/permissions",
      params = Map("account_id" -> "2", "creator_account_id" -> "1", "permissions" -> "23 ,45"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).validateAccountAccess(2,1, Set(23, 45))
  }

  test("Validate account access with permissions - failure") {
    when(service.validateAccountAccess(2,1, Set(23, 45))).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    get("/validate/access/permissions",
      params = Map("account_id" -> "2", "creator_account_id" -> "1", "permissions" -> "se, 23, 45"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 500
    }
    verify(service, never).validateAccountAccess(2,1, Set(23, 45))
  }

  test("Get root parent - success") {
    when(service.getRootParent(1L)).thenReturn(Future.successful(1L))
    get("/get_root_parent/1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Long]](status,200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).getRootParent(1)
  }

  test("Get Sub Accounts") {
    val expected = Set(3L,4L,5L)
    when(service.getSubAccounts(1L)).thenReturn(Future.successful(Right(expected)))
    get("/subaccounts/1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Set[Long]]](status,200, body, Response(ResponseStatus.Ok, expected))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
