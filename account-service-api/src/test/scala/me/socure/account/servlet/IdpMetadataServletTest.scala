package me.socure.account.servlet

import me.socure.account.service.IdpMetadataService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.model.superadmin.AccountIdpMetadata
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.when
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

class IdpMetadataServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar{

  implicit val ec = ExecutionContext.global

  val idpMetadataService = mock[IdpMetadataService]
  val servlet = new IdpMetadataServlet(idpMetadataService)
  addServlet(servlet, "/*")

  test("list_idp_metadata should list all metadata"){
    val list = List(AccountIdpMetadata(1,"AccountName1",new DateTime("2017-12-06", DateTimeZone.UTC)), AccountIdpMetadata(4,"AccountName4",new DateTime("2017-12-06", DateTimeZone.UTC)))
    val res = Response(ResponseStatus.Ok, data = list)
    when(idpMetadataService.listIdpMetadata()) thenReturn Future.successful(Right(list))
    get("/list_idp_metadata", Map("account_id" -> "1")) {
      validate[Response[List[AccountIdpMetadata]]](status, 200, body, res)
    }
  }

  test("get_idp_metadata should return NoIdpMetadataFound"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.NoIdpMetadataFound)
    when(idpMetadataService.getIdpMetadata("http://entityId")) thenReturn Future.successful(Left(resCode))
    get("/get_idp_metadata", Map("entity_id" -> "http://entityId")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_idp_metadata should return the metadata"){
    val imd = IdpMetadata(1L, "metadata")
    val res = Response(ResponseStatus.Ok, data = imd)
    when(idpMetadataService.getIdpMetadata("http://entityId")) thenReturn Future.successful(Right(imd))
    get("/get_idp_metadata", Map("entity_id" -> "http://entityId")) {
      validate[Response[IdpMetadata]](status, 200, body, res)
    }
  }

  test("get_idp_metadata by account id should return NoIdpMetadataFound"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.NoIdpMetadataFound)
    when(idpMetadataService.getIdpMetadata(1)) thenReturn Future.successful(Left(resCode))
    get("/get_idp_metadata_by_account", Map("account_id" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_idp_metadata by account id should return the metadata"){
    val imd = IdpMetadata(1L, "metadata")
    val res = Response(ResponseStatus.Ok, data = imd)
    when(idpMetadataService.getIdpMetadata(1)) thenReturn Future.successful(Right(imd))
    get("/get_idp_metadata_by_account", Map("account_id" -> "1")) {
      validate[Response[IdpMetadata]](status, 200, body, res)
    }
  }

  test("insert_idp_metadata should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(idpMetadataService.insertIdpMetadata(11L, "somedata")) thenReturn Future.successful(Left(resCode))
    post("/insert_idp_metadata", Map("account_id" -> "11", "metadata" -> "somedata")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("insert_idp_metadata should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(idpMetadataService.insertIdpMetadata(1L, "somedata")) thenReturn Future.successful(Right(true))
    post("/insert_idp_metadata", Map("account_id" -> "1", "metadata" -> "somedata")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("delete_idp_metadata should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(idpMetadataService.deleteIdpMetadata(11L)) thenReturn Future.successful(Left(resCode))
    post("/delete_idp_metadata", Map("account_id" -> "11")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("delete_idp_metadata should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(idpMetadataService.deleteIdpMetadata(1L)) thenReturn Future.successful(Right(true))
    post("/delete_idp_metadata", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def constructErrorResponse(code : ErrorResponse) : Response[ErrorResponse] = {
    Response(ResponseStatus.Error, code)
  }
}
