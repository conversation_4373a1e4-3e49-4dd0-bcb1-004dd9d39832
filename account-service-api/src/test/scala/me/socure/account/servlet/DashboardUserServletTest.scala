package me.socure.account.servlet

import me.socure.account.dashboard.DashboardUserService
import me.socure.account.service.PasswordService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.dashboard.DashboardUserServlet
import me.socure.model.account.{AccessCredentials, AccountInfo}
import me.socure.model.user.{DashboardUser, UserInfoWithAccountId}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s._
import org.json4s.native.JsonMethods._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/22/16.
  */
class DashboardUserServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service = mock[DashboardUserService]
  val passwordService = mock[PasswordService]
  val servlet = new DashboardUserServlet(service, passwordService)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(service, passwordService)
  }

  val dashboardUser = DashboardUser(1,"Gopal","haris", "<EMAIL>", "49a11f5245eefff691abdd71f42c109a", "GSAL","**********", new DateTime(DateTime.parse("2013-09-20").getMillis, DateTimeZone.UTC) ,1, isLocked = false, 1, isPrimaryAdmin = true, Set(1))
  val accessCreds = AccessCredentials("apikey", "secretkey", "accesstoken", "accesssecret", "certificate")
  val accountNotFound = ErrorResponseFactory.get(AccountNotFound)
  val businessUserNotFound = ErrorResponseFactory.get(BusinessUserNotFound)
  val changePasswordInput = Map("email" -> "<EMAIL>", "currentpassword" -> "valid_current_password", "newpassword" -> "new_password")

  test("should return dashboard user"){
    val expectedResponse = Response(ResponseStatus.Ok, dashboardUser)
    when(service.findUserByEmail("<EMAIL>")) thenReturn Future.successful(Right(dashboardUser))
    get("/get_user/<EMAIL>"){
      validate[Response[DashboardUser]](status, 200, body, expectedResponse)
    }
  }

  test("should return user tos") {
    val expectedResponse = Response(ResponseStatus.Ok, false)
    when(service.checkToSAgreementByUser("<EMAIL>")) thenReturn Future.successful(Right(false))
    get("/check_tos_agreement/<EMAIL>") {
      validate[Response[Boolean]](status, 200, body, expectedResponse)
    }
  }

  test("dashboard user should 400 error"){
    val expResult = Response(ResponseStatus.Error, businessUserNotFound)
    when(service.findUserByEmail("<EMAIL>")) thenReturn Future.successful(Left(businessUserNotFound))
    get("/get_user/<EMAIL>"){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("should return access creds of socurekey"){
    val expectedResponse = Response(ResponseStatus.Ok, accessCreds)
    when(service.getAccountCrdentialsByApiKey("valid_socurekey")) thenReturn Future.successful(Right(accessCreds))
    get("/get_account_creds/valid_socurekey"){
      validate[Response[AccessCredentials]](status, 200, body, expectedResponse)
    }
  }

  test("get access creds response status should 400"){
    val expResult = Response(ResponseStatus.Error, accountNotFound)
    when(service.getAccountCrdentialsByApiKey("invalid_socurekey")) thenReturn Future.successful(Left(accountNotFound))
    get("/get_account_creds/invalid_socurekey"){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("should return account id"){
    val expectedResponse = Response(ResponseStatus.Ok, 10l)
    when(service.getAccountIdByEmail("<EMAIL>")) thenReturn Future.successful(Right(10l))
    get("/get_accountid/<EMAIL>"){
      validate[Response[Long]](status, 200, body, expectedResponse)
    }
  }

  test("get account id response status should 400"){
    val expResult = Response(ResponseStatus.Error, accountNotFound)
    when(service.getAccountIdByEmail("<EMAIL>")) thenReturn Future.successful(Left(accountNotFound))
    get("/get_accountid/<EMAIL>"){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("should return user info with account id"){
    val expectedResponse = Response(ResponseStatus.Ok, UserInfoWithAccountId(1,"firstName","lastName","<EMAIL>",101))
    when(service.getUserInfoWithAccountIdByEmail("<EMAIL>")) thenReturn Future.successful(Right( UserInfoWithAccountId(1,"firstName","lastName","<EMAIL>",101)))
    get("/get_userinfo_with_accountid/<EMAIL>"){
      validate[Response[UserInfoWithAccountId]](status, 200, body, expectedResponse)
    }
  }

  test("get user info with account id response status should 400"){
    val expResult = Response(ResponseStatus.Error, businessUserNotFound)
    when(service.getUserInfoWithAccountIdByEmail("<EMAIL>")) thenReturn Future.successful(Left(businessUserNotFound))
    get("/get_userinfo_with_accountid/<EMAIL>"){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("should change password"){
    val expectedResponse = Response(ResponseStatus.Ok, true)
    when(passwordService.changePassword("<EMAIL>", "valid_current_password", "new_password")) thenReturn Future.successful(Right(true))
    post("/change_password", changePasswordInput){
      validate[Response[Boolean]](status, 200, body, expectedResponse)
    }
  }

  test("should say mismatch current password"){
    val expResult = Response(ResponseStatus.Error, ErrorResponseFactory.get(CurrentPasswordMismatch))
    when(passwordService.changePassword("<EMAIL>", "valid_current_password", "new_password")) thenReturn Future.successful(Left(ErrorResponseFactory.get(CurrentPasswordMismatch)))
    post("/change_password", changePasswordInput){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("should say business user not found on password change"){
    val expResult = Response(ResponseStatus.Error, businessUserNotFound)
    when(passwordService.changePassword("<EMAIL>", "valid_current_password", "new_password")) thenReturn Future.successful(Left(businessUserNotFound))
    post("/change_password", changePasswordInput){
      validate[Response[ErrorResponse]](status, 400, body, expResult)
    }
  }

  test("get subaccounts by Id") {
    import me.socure.account.servlet.mock.MockSubAccounts._
    when(service.findSubAccountsForAccountId(3)) thenReturn Future.successful(generateSubAccounts(3))
    get("/subaccounts_for_id/3") {
      val json = parse(body)
      val datas = json \\ "data"
      datas.extract[List[AccountInfo]].size shouldBe 3
    }
  }

  test("miss subaccounts By Id") {
    import me.socure.account.servlet.mock.MockSubAccounts._
    when(service.findSubAccountsForAccountId(3)) thenReturn Future.successful(generateSubAccounts(3))
    get("/subaccounts_for_id/5") {
      val json = parse(body)
      val datas = json \\ "data"
      datas.extractOpt[List[AccountInfo]] shouldBe empty
    }
  }

  private def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
