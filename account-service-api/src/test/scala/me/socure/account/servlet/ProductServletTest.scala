package me.socure.account.servlet

import me.socure.account.service.ProductService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.account.ProductProvisioningTypes
import me.socure.model._
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class ProductServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: ProductService = mock[ProductService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: ProductServlet = new ProductServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  val headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  test("Get account products - success") {
    val response = Seq(AccountProducts(1, "Watchlist-3.0", 65, ProductProvisioningTypes.CASCADE, None, 1, false, false, false, None, None, None, None))
    when(service.getProductsForAccount(1L)).thenReturn(Future.successful(Right(response)))
    get("/account/1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[AccountProducts]]](status, 200, body, Response(ResponseStatus.Ok, response))
    }
    verify(service).getProductsForAccount(1L)
  }

  test("Get account products - failure") {
    val response = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.getProductsForAccount(1L)).thenReturn(Future.successful(Left(response)))
    get("/account/1",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, response))
    }
    verify(service).getProductsForAccount(1L)
  }

  test("Update account products - success") {
    val request = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true)))
    val response = true
    when(service.updateProductsForAccount(request)).thenReturn(Future.successful(Right(response)))
    post("/account",
      body = request.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, response))
    }
    verify(service).updateProductsForAccount(request)
  }

  test("Post account products - failure") {
    val request = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true)))
    val response = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.updateProductsForAccount(request)).thenReturn(Future.successful(Left(response)))
    post("/account",
      body = request.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, response))
    }
    verify(service).updateProductsForAccount(request)
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
