package me.socure.account.servlet

import me.socure.account.service.MLAService
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.mla.MLAInputRequest
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import org.mockito.{Mockito, Matchers => MMatchers}
import me.socure.util.JsonEnrichments._
import scala.concurrent.{ExecutionContext, Future}

class MLAFieldServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val mlaService = mock[MLAService]
  val hmacVerifier = mock[HMACHttpVerifier]

  val servlet = new MLAFieldServlet(mlaService, hmacVerifier)

  addServlet(servlet, "/*")

  before{
    reset(mlaService)
    reset(hmacVerifier)
  }

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))

  test("update fields successfully"){

    val request = MLAInputRequest(
      accountId = "1888779",
      memberNumber = "member_number",
      securityCode = "security_code"
    )

    when(mlaService.saveMLAFields(request)) thenReturn Future.successful(Right(request))

    post("/field", headers = Map(
      "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
      "Content-Type" -> "application-json; charset=UTF-8",
      "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
    ), body = request.encodeJson()) {
      response.body shouldBe "{\"status\":\"ok\",\"data\":{\"accountId\":\"1888779\",\"memberNumber\":\"member_number\",\"securityCode\":\"security_code\"}}"
      response.status shouldBe 200
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
    Mockito.verify(mlaService).saveMLAFields(request)
  }

  test("return 400"){

    val request = MLAInputRequest(
      accountId = null,
      memberNumber = "",
      securityCode = "security_code"
    )

    post("/field", headers = Map(
      "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
      "Content-Type" -> "application-json; charset=UTF-8",
      "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
    ), body = request.encodeJson()) {
      response.body shouldBe "{\"status\":\"error\",\"data\":{\"code\":400,\"message\":\"AccountNumber, SecurityCode and MemberNumber are mandatory fields.\"}}"
      response.status shouldBe 400
    }
    Mockito
      .verify(hmacVerifier).verify(MMatchers.any(classOf[HmacVerificationRequest]))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
