package me.socure.account.servlet.dashbordv2

import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.dashboardv2.EnvironmentSettingsV2Servlet
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class EnvironmentSettingsV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val serviceMock: EnvironmentSettingsService = mock[EnvironmentSettingsService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet = new EnvironmentSettingsV2Servlet(serviceMock, hmacVerifier)

  before {
    reset(serviceMock)
  }

  test("Validate account access with permissions - failure") {
    addServlet(servlet, "/*")
    when(serviceMock.generateMissingPublicApiKeys()).thenReturn(Future.successful(Right(true)))
    post("/generate/missing/apikeys/public",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(serviceMock).generateMissingPublicApiKeys()
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}