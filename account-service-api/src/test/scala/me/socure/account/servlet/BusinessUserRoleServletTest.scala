package me.socure.account.servlet

import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.constants.attributes.{AccountAttributeName, AccountAttributeValue}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.account.{BusinessUserRolesLess, BusinessUserRolesWithPermissions}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 5/30/16.
  */
class BusinessUserRoleServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec = ExecutionContext.global

  val engine = mock[BusinessUserRoleService]
  val servlet = new BusinessUserRoleServlet(engine)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(engine)
  }

  test("add_role should return account not found") {

    post("/add_role_to_users", Map()) {
      status should equal(400)
    }

    post("/add_role_to_users", Map("user_ids" -> "aaa", "role_id" -> "1")) {
      status should equal(400)
    }

    post("/add_role_to_users", Map("user_ids" -> "1", "role_id" -> "aaa")) {
      status should equal(400)
    }
  }

  test("remove_role should return account not found") {

    post("/remove_role_from_users", Map()) {
      status should equal(400)
    }

    post("/remove_role_from_users", Map("user_ids" -> "aaa", "role_id" -> "1")) {
      status should equal(400)
    }

    post("/remove_role_from_users", Map("user_ids" -> "1", "role_id" -> "aaa")) {
      status should equal(400)
    }
  }

  test("add_role should return correct status when an exception is thrown") {
    Mockito.when(engine.addPermissionToAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int], Some(org.mockito.Matchers.any[String]))).thenReturn(Future.failed(new Exception()))

    post("/add_role_to_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":199,"message":""}}"""
    }
  }

  test("remove_role should return correct status when an exception is thrown") {
    Mockito.when(engine.removePermissionFromAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int])).thenReturn(Future.failed(new Exception()))

    post("/remove_role_from_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":199,"message":""}}"""
    }
  }

  test("add_role should return correctly the the service returns left") {
    Mockito.when(engine.addPermissionToAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int], Some(org.mockito.Matchers.any[String]))).thenReturn(Future.successful(Left(ErrorResponse(1, ""))))

    post("/add_role_to_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":1,"message":""}}"""
    }
  }

  test("remove_role should return correctly the the service returns left") {
    Mockito.when(engine.removePermissionFromAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int])).thenReturn(Future.successful(Left(ErrorResponse(1, ""))))

    post("/remove_role_from_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":1,"message":""}}"""
    }
  }

  test("add_role should return correctly the the service returns right") {
    Mockito.when(engine.addPermissionToAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int], Some(org.mockito.Matchers.any[String]))).thenReturn(Future.successful(Right(1)))

    post("/add_role_to_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(200)
      body shouldBe """{"status":"ok","data":1}"""
    }
  }

  test("remove_role should return correctly the the service returns right") {
    Mockito.when(engine.removePermissionFromAccount(org.mockito.Matchers.any[Long], org.mockito.Matchers.any[Int])).thenReturn(Future.successful(Right(1)))

    post("/remove_role_from_users", Map("user_ids" -> "1", "role_id" -> "1")) {
      status should equal(200)
      body shouldBe """{"status":"ok","data":1}"""
    }
  }

  //Account Attribute Test
  test("should add attribute to account"){
    val expectedRes = Response(ResponseStatus.Ok, 1)
    when(engine.upsertAttributeToAccount(1, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) thenReturn Future.successful(Right(1))
    post("/add_attribute", Map("id" -> "1", "att_name" -> AccountAttributeName.SLA.id.toString, "att_value" -> AccountAttributeValue.DEFAULT_SLA.id.toString)) {
      validate[Response[Int]](status, 200, body, expectedRes)
    }
  }

  test("should return account not found"){
    val error = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, error)
    when(engine.upsertAttributeToAccount(2, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) thenReturn Future.successful(Left(error))
    post("/add_attribute", Map("id" -> "2", "att_name" -> AccountAttributeName.SLA.id.toString, "att_value" -> AccountAttributeValue.DEFAULT_SLA.id.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("should return invalid input format"){
    val error = ErrorResponseFactory.get(InvalidInputFormat)
    val expectedRes = Response(ResponseStatus.Error, error)
    post("/add_attribute", Map("id" -> "invalid", "att_name" -> AccountAttributeName.SLA.id.toString, "att_value" -> AccountAttributeValue.DEFAULT_SLA.id.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("should remove attribute to account"){
    val expectedRes = Response(ResponseStatus.Ok, 1)
    when(engine.removeAttributeFromAccount(1, AccountAttributeName.SLA)) thenReturn Future.successful(Right(1))
    post("/remove_attribute", Map("id" -> "1", "att_name" -> AccountAttributeName.SLA.id.toString)) {
      validate[Response[Int]](status, 200, body, expectedRes)
    }
  }

  test("should return account not found on remove attribute"){
    val error = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, error)
    when(engine.removeAttributeFromAccount(2, AccountAttributeName.SLA)) thenReturn Future.successful(Left(error))
    post("/remove_attribute", Map("id" -> "2", "att_name" -> AccountAttributeName.SLA.id.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("should return invalid input format on remove attributes"){
    val error = ErrorResponseFactory.get(InvalidInputFormat)
    val expectedRes = Response(ResponseStatus.Error, error)
    post("/remove_attribute", Map("id" -> "invalid", "att_name" -> AccountAttributeName.SLA.id.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("list_roles should list business user roles"){
    val roles = List(BusinessUserRolesLess(1,"Admin"), BusinessUserRolesLess(2,"SuperAdmin"))
    val expectedRes = Response(ResponseStatus.Ok, roles)
    when(engine.listRoles) thenReturn Future.successful(Right(roles))
    get("/list_roles") {
      validate[Response[List[BusinessUserRolesLess]]](status, 200, body, expectedRes)
    }
  }

  test("list_roles should return unknown error"){
    val error = ErrorResponseFactory.get(UnknownError)
    val expectedRes = Response(ResponseStatus.Error, error)
    when(engine.listRoles) thenReturn Future.successful(Left(error))
    get("/list_roles") {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("account_permissions should list roles provisioned for the account"){
    val roles = List(BusinessUserRolesWithPermissions(38,"KYC Test",false), BusinessUserRolesWithPermissions(22,"Watchlist",true), BusinessUserRolesWithPermissions(19,"SSN",false))
    val expectedRes = Response(ResponseStatus.Ok, roles)
    when(engine.getAccountPermissions(1L)) thenReturn Future.successful(Right(roles))
    get("/account_permissions/1") {
      validate[Response[List[BusinessUserRolesWithPermissions]]](status, 200, body, expectedRes)
    }
  }

  test("account_permissions should return list with roles provisioned set as false for an invalid account"){
    val roles = List(BusinessUserRolesWithPermissions(38,"KYC Test",false), BusinessUserRolesWithPermissions(22,"Watchlist",false), BusinessUserRolesWithPermissions(19,"SSN",false))
    val expectedRes = Response(ResponseStatus.Ok, roles)
    when(engine.getAccountPermissions(100L)) thenReturn Future.successful(Right(roles))
    get("/account_permissions/100") {
      validate[Response[List[BusinessUserRolesWithPermissions]]](status, 200, body, expectedRes)
    }
  }

  test("fetch_parent_account_permission should list roles provisioned for the account"){
    val roles = Set(1,2,3)
    val expectedRes = Response(ResponseStatus.Ok, roles)
    when(engine.getParentAccountPermissions(1L)) thenReturn Future.successful(Right(roles))
    get("/fetch_parent_account_permission/1") {
      validate[Response[Set[Int]]](status, 200, body, expectedRes)
    }
  }

  test("fetch_parent_account_permission should throw error for invalid account"){
    val error = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, error)
    when(engine.getParentAccountPermissions(100L)) thenReturn Future.successful(Left(error))
    get("/fetch_parent_account_permission/100") {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
