package me.socure.account.servlet.account.automation

import me.socure.account.automation.{AccountAutomationService, BundleManagementService}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model._
import me.socure.model.account.ProductProvisioningTypes
import me.socure.model.account.automation.{AccountProvisioningDetails, ProductConfiguration, UpdateAccountProvisioningDetails}
import me.socure.model.bundles.Bundle
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Matchers.{any, anyBoolean, anyLong, anyObject, anyString}
import org.mockito.Mockito
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountAutomationServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountAutomationService = mock[AccountAutomationService](Mockito.withSettings().defaultAnswer(Mockito.RETURNS_SMART_NULLS))
  val bundleManagementService: BundleManagementService = mock[BundleManagementService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountAutomationServlet = new AccountAutomationServlet(service, bundleManagementService, hmacVerifier)
  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, bundleManagementService, hmacVerifier)
  }

  test("Get Bundle Information - success") {
    val expected = Set(Bundle("Bundle 4","Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Watchlist, Synthetic",
      Set(37, 52, 29, 28, 33, 65, 188, 13, 71, 99, 30)))
    when(bundleManagementService.getBundles()).thenReturn(Future.successful(Right(expected)))
    get(s"/bundles",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Set[Bundle]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(bundleManagementService).getBundles()
  }

  test("Get account provisioning details by account id - success") {
    val accountId = 1
    val expected = AccountProvisioningDetails(Some("Bundle One"),
      List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,false,false,false,None,None,None,None)),
      ProductConfiguration(Some("*********"), None, None))
    when(service.getAccountProvisioningDetails(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountProvisioningDetails]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountProvisioningDetails(accountId)
  }

  test("Get account provisioning details by account id - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.getAccountProvisioningDetails(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountProvisioningDetails(accountId)
  }

  test("Update account bundle association by account id - success") {
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    val expected = true
    when(service.saveAccountAutomation(anyLong, anyObject[UpdateAccountProvisioningDetails], anyString, anyBoolean, anyBoolean)).thenReturn(Future.successful(Right(expected)))
    post("/account/1",
      body = Serialization.write(updateAccountProvisioningDetails),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).saveAccountAutomation(anyLong, anyObject[UpdateAccountProvisioningDetails], anyString(), anyBoolean, anyBoolean)
  }

  test("Update account provisioning details by account id - failure") {
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val productConfig = ProductConfiguration(None, None, None)
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountProvisioningNotUpdated)
   when(service.saveAccountAutomation(anyLong, anyObject[UpdateAccountProvisioningDetails], anyString, anyBoolean, anyBoolean)).thenReturn(Future.successful(Left(expected)))
    post("/account/1",
      body = Serialization.write(updateAccountProvisioningDetails),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).saveAccountAutomation(anyLong, anyObject[UpdateAccountProvisioningDetails], anyString, anyBoolean, anyBoolean)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
