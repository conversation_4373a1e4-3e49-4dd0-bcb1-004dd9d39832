package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.superadmin.AccountInfoService
import me.socure.constants.EnvironmentConstants
import me.socure.convertors.LegacyModelConverters
import me.socure.model._
import me.socure.model.account._
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization._
import me.socure.superadmin.AccountInfoServlet
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

/**
  * Created by james on 9/15/16.
  */
class AccountInfoServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service : AccountInfoService = mock[AccountInfoService]
  val servlet : AccountInfoServlet = new AccountInfoServlet(service)

  addServlet(servlet, "/*")

  before{
    reset(service)
  }

  val error = ErrorResponseFactory.get(UnknownError)
  val negRes = Response(ResponseStatus.Error, error)

  private val accountWithEnvironmentDetails = AccountWithEnvironmentDetails(
    account = me.socure.model.user.authorization.Account(
      id = 20001,
      name = "Saml 2.0 Account",
      permission = Set(
        BusinessUserRoles.SAML_2_0.id,
        BusinessUserRoles.ADDRESS_RISK_SCORE.id
      ),
      isActive = true,
      isInternal = false,
      Set.empty
    ),
    environment = Set(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
  )

  private val accountWithEnvironmentDetailsWithPublicId = AccountWithEnvironmentDetailsWithPublicId(
    account = AccountWithPublicId(
      id = 20001,
      publicId = "some-public-id-123",
      name = "Saml 2.0 Account",
      permission = Set(
        BusinessUserRoles.SAML_2_0.id,
        BusinessUserRoles.ADDRESS_RISK_SCORE.id
      ),
      isActive = true,
      isInternal = false,
      subscriptions = Set(Subscription(1, "Watchlist Monitoring")),
      false
    ),
    environment = Set(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
  )

  test("list should return all accounts") {
    val anAccount = PublicAccount("acc-xxxxxxxxxx", "Account Name")
    val expectedRes = Response(ResponseStatus.Ok, Seq(anAccount))
    when(service.list()) thenReturn Future.successful(Right(Seq(anAccount)))
    get("/list"){
      validate[Response[Seq[PublicAccount]]](status, 200, body, expectedRes)
    }
  }

  test("list_account_ids should return all account ids") {
    val accountIds = Seq(1L, 2L, 3L)
    val expectedRes = Response(ResponseStatus.Ok, accountIds)
    when(service.listAccountIds()) thenReturn Future.successful(Right(accountIds))
    get("/list_account_ids"){
      validate[Response[Seq[Long]]](status, 200, body, expectedRes)
    }
  }

  test("list_account_ids should return error") {
    val errorResp = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val expectedRes = Response(ResponseStatus.Error, errorResp)
    when(service.listAccountIds()) thenReturn Future.successful(Left(errorResp))
    get("/list_account_ids"){
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("should return account name") {
    val accountName = Map(1l -> AccountIdName(1l, "Socure"))
    val expectedRes = Response(ResponseStatus.Ok, accountName)
    when(service.getActiveAccountNameList) thenReturn Future.successful(Right(accountName))
    get("/get_all_account_names"){
      validate[Response[Map[Long, AccountIdName]]](status, 200, body, expectedRes)
    }
  }

  test("status code should be 400 on get account name call"){
    when(service.getActiveAccountNameList) thenReturn Future.successful(Left(error))
    get("/get_all_account_names"){
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("should return non internal and active account ids") {
    val accountIds = List(1,2,3,4).map(_.toLong)
    val expectedRes = Response(ResponseStatus.Ok, accountIds)
    when(service.getNonInternalActiveAccountList) thenReturn Future.successful(Right(accountIds))
    get("/get_non_internal_active_accounts"){
      validate[Response[List[Long]]](status, 200, body, expectedRes)
    }
  }

  test("getting internal and active account ids should fail if anyting worng") {
    when(service.getNonInternalActiveAccountList) thenReturn Future.successful(Left(error))
    get("/get_non_internal_active_accounts"){
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("should return true for has_role") {
    when(service.hasRole(ApiKeyString("api_key"), BusinessUserRoles.ADMIN.id)).thenReturn(Future.successful(Some(true)))
    get("/has_role", Map("api_key" -> "api_key", "role" -> "1")){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("should return false for has_role") {
    when(service.hasRole(ApiKeyString("api_key"), BusinessUserRoles.ADMIN.id)).thenReturn(Future.successful(Some(false)))
    get("/has_role", Map("api_key" -> "api_key", "role" -> "1")){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, false))
    }
  }

  test("should return AccountNotFound error for has_role") {
    when(service.hasRole(ApiKeyString("api_key"), BusinessUserRoles.ADMIN.id)).thenReturn(Future.successful(None))
    get("/has_role", Map("api_key" -> "api_key", "role" -> "1")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  test("should return all parent accounts not having primary users in recently created first order") {
    val expected = Seq(AccountIdName(1, "pa1"))
    when(service.getParentAccountsWithoutPrimaryUsers()) thenReturn Future.successful(Right(expected))
    get("/get_parent_accounts_no_prim_users"){
      validate[Response[Seq[AccountIdName]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
  }

  test("should return account public for valid account id") {
    val expected = Some("test-acc-pub-id")
    when(service.fetchPublicIdByAccountId(1)) thenReturn Future.successful(expected)
    get("/publicid/accounts/1") {
      validate[Response[String]](status, 200, body, Response(ResponseStatus.Ok, "test-acc-pub-id"))
    }
  }

  test("should return account public for invalid account id") {
    val expected = None
    when(service.fetchPublicIdByAccountId(1)) thenReturn Future.successful(expected)
    get("/publicid/accounts/1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(
        ResponseStatus.Error, ErrorResponse(AccountNotFound.id, AccountNotFound.description)
      ))
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should return account details by public id - legacy") {
    val expectedResult = AccountWithEnvironmentDetailsLegacy(
      account = AccountLegacy(
        id = 20001,
        name = "Saml 2.0 Account",
        permission = Set(
          "SAML_2_0",
          "ADDRESS_RISK_SCORE"
        ),
        isActive = true,
        isInternal = false
      ),
      environment = Set(
        EnvironmentSettings(
          id = 200011,
          name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
          socureKey = "200011_api_key_new"
        ),
        EnvironmentSettings(
          id = 200012,
          name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
          socureKey = "200012_api_key_active"
        )
      )
    )

    val publicId = PublicIdGenerator.account().value

    when(service.getAccountWithEnvironmentDetailsByPublicId(publicId)) thenReturn Future.successful(Some(accountWithEnvironmentDetails))
    get("/get_account_details_by_public_id", "publicId" -> publicId){
      validate[Response[AccountWithEnvironmentDetailsLegacy]](status, 200, body, Response(ResponseStatus.Ok, expectedResult))
    }
  }

  test("should return account details by public id") {
    val publicId = PublicIdGenerator.account().value

    when(service.getAccountWithEnvironmentDetailsByPublicId(publicId)) thenReturn Future.successful(Some(accountWithEnvironmentDetails))
    get("/get_account_details_by_public_id_v2", "publicId" -> publicId){
      validate[Response[AccountWithEnvironmentDetails]](status, 200, body, Response(ResponseStatus.Ok, accountWithEnvironmentDetails))
    }
  }

  test("should return AccountNotFound when no account found with associated public id") {
    val publicId = PublicIdGenerator.account().value
    when(service.getAccountWithEnvironmentDetailsByPublicId(publicId)) thenReturn Future.successful(None)
    get("/get_account_details_by_public_id", "publicId" -> publicId){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should return account details by id - legacy") {
    val expectedResult = AccountWithEnvironmentDetailsWithPublicIdLegacy(
      account = AccountWithPublicIdLegacy(
        id = 20001,
        publicId = "some-public-id-123",
        name = "Saml 2.0 Account",
        permission = Set(
          "SAML_2_0",
          "ADDRESS_RISK_SCORE"
        ),
        isActive = true,
        isInternal = false
      ),
      environment = Set(
        EnvironmentSettings(
          id = 200011,
          name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
          socureKey = "200011_api_key_new"
        ),
        EnvironmentSettings(
          id = 200012,
          name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
          socureKey = "200012_api_key_active"
        )
      )
    )

    val id = Random.nextLong()

    when(service.getAccountWithEnvironmentDetailsById(id)) thenReturn Future.successful(Some(accountWithEnvironmentDetailsWithPublicId))
    get(s"/get_account_details_by_id/$id"){
      validate[Response[AccountWithEnvironmentDetailsWithPublicIdLegacy]](status, 200, body, Response(ResponseStatus.Ok, expectedResult))
    }
  }

  test("should return account details by id") {
    val id = Random.nextLong()

    when(service.getAccountWithEnvironmentDetailsById(id)) thenReturn Future.successful(Some(accountWithEnvironmentDetailsWithPublicId))
    get(s"/get_account_details_by_id_v2/$id"){
      validate[Response[AccountWithEnvironmentDetailsWithPublicId]](status, 200, body, Response(ResponseStatus.Ok, accountWithEnvironmentDetailsWithPublicId))
    }
  }

  test("should return AccountNotFound when no account found with associated id") {
    val id = Random.nextLong()
    when(service.getAccountWithEnvironmentDetailsById(id)) thenReturn Future.successful(None)
    get(s"/get_account_details_by_id/$id"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should get account id and names by given roles - legacy") {
    val request = AccountIdNamesByRolesRequestLegacy(
      roles = LegacyModelConverters.idsToNames(Random.shuffle(BusinessUserRoles.values.map(_.id)).take(3)),
      onlyParent = Random.nextBoolean()
    )
    val accountIdNames = Set(
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString)
    )

    when(service.getAccountIdNamesWithRoles(
      roles = LegacyModelConverters.namesToIds(request.roles),
      onlyParents = request.onlyParent
    )) thenReturn Future.successful(accountIdNames)

    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = accountIdNames
    )

    post("/get_account_id_names_by_roles", request.encodeJson()){
      validate[Response[Set[AccountIdName]]](status, 200, body, expectedResponse)
    }
  }

  test("should get account id and names by given roles") {
    val request = AccountIdNamesByRolesRequest(
      roles = Random.shuffle(BusinessUserRoles.values.map(_.id)).take(3),
      onlyParent = Random.nextBoolean()
    )
    val accountIdNames = Set(
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString)
    )

    when(service.getAccountIdNamesWithRoles(
      roles = request.roles,
      onlyParents = request.onlyParent
    )) thenReturn Future.successful(accountIdNames)

    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = accountIdNames
    )

    post("/get_account_id_names_by_roles_v2", request.encodeJson()){
      validate[Response[Set[AccountIdName]]](status, 200, body, expectedResponse)
    }
  }

  test("should return account name for given account id") {
    val expected = PublicAccount("publicid", "accountname")
    val id = Random.nextLong()
    when(service.getAccountNameAndPublicId(id)).thenReturn(Future.successful(Some(expected)))
    get(s"/public-account/$id"){
      validate[Response[PublicAccount]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
  }

  test("should return AccountNotFound error for an invalid account id") {
    val id = Random.nextLong()
    when(service.getAccountNameAndPublicId(id)) thenReturn Future.successful(None)
    get(s"/public-account/$id"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  test("list should return all encrypted parent accounts") {
    val account = PublicAccount("acc-xxxxxxxxxx", "Account Name")
    val expectedRes = Response(ResponseStatus.Ok, Seq(account))
    when(service.getEncryptedParentAccounts()) thenReturn Future.successful(Right(Seq(account)))
    get("/get_encrypted_parent_accounts"){
      validate[Response[Seq[PublicAccount]]](status, 200, body, expectedRes)
    }
  }

  test("preferences: should return account preferences for given account id") {
    val expected = AccountPreferences(1L, true, false, false)
    val id = Random.nextLong()
    when(service.getAccountPreferences(id)).thenReturn(Future.successful(Some(expected)))
    get(s"/preferences/$id"){
      validate[Response[AccountPreferences]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
  }

  test("preferences: should return AccountNotFound error for an invalid account id") {
    val id = Random.nextLong()
    when(service.getAccountPreferences(id)) thenReturn Future.successful(None)
    get(s"/preferences/$id"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  test("should return account name with public id") {
    val accountName = Map("public_id" -> PublicAccountIdName(1l, "public_id", "Socure"))
    val expectedRes = Response(ResponseStatus.Ok, accountName)
    when(service.getAllAccountNamesWithPublicId) thenReturn Future.successful(Right(accountName))
    get("/get_all_account_names_with_public_id"){
      validate[Response[Map[String, PublicAccountIdName]]](status, 200, body, expectedRes)
    }
  }

  test("status code should be 400 on get account name with public id call"){
    when(service.getAllAccountNamesWithPublicId) thenReturn Future.successful(Left(error))
    get("/get_all_account_names_with_public_id"){
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("should return accountIdNames for a given with and without permission IDs ") {
    val withPermissionId = Random.nextInt();
    val withOutPermissionId = Random.nextInt();
    val accountIdNames = Seq(AccountIdName(1, "pa1")).toList
    val expectedRes = Response(ResponseStatus.Ok, accountIdNames)
    when(service.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)) thenReturn Future.successful(Right(accountIdNames))
    get(s"/get_accounts_with_and_without_permission?with_permission_id=${withPermissionId}&with_out_permission_id=${withOutPermissionId}")  {
      validate[Response[List[AccountIdName]]](status, 200, body, expectedRes)
    }
  }

  test("status should be 400 when accountIdNames not able to find for a given with and without permission IDs ") {
    val withPermissionId = Random.nextInt();
    val withOutPermissionId = Random.nextInt();

    when(service.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)) thenReturn Future.successful(Left(error))
    get(s"/get_accounts_with_and_without_permission?with_permission_id=${withPermissionId}&with_out_permission_id=${withOutPermissionId}") {
      validate[Response[ErrorResponse]](status, 400, body, negRes)
    }
  }

  test("GET /analytics should return AnalyticsGlobalImportInfo on success") {
    val expectedResponse = Some(AnalyticsGlobalInfoResponse(isHistoricDataImported = true, lastImportedDate = DateTime.parse("2024-11-20T14:48:07.553Z")))
    when(service.getAnalyticsGlobalImportInfo()).thenReturn(Future.successful(Right(expectedResponse)))
    get("/analytics") {

      validate[Response[Option[AnalyticsGlobalInfoResponse]]](status, 200, body, Response(ResponseStatus.Ok,expectedResponse))
    }
  }

  test("GET /analytics should return error on failure") {
    val errorResponse = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.getAnalyticsGlobalImportInfo()).thenReturn(Future.successful(Left(errorResponse)))
    get("/analytics") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,errorResponse))
    }
  }

  test("POST /analytics should return success on valid request") {
    val request = AnalyticsGlobalInfoRequest(DateTime.parse("2024-11-20T15:05:53.436Z"))
    when(service.addAnalyticsGlobalImportInfo(request)).thenReturn(Future.successful(Right(true)))
    post("/analytics", body = request.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("POST /analytics should return error on invalid request") {
    val request = AnalyticsGlobalInfoRequest(DateTime.parse("2024-11-20T15:05:53.436Z"))
    val errorResponse = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.addAnalyticsGlobalImportInfo(request)).thenReturn(Future.successful(Left(errorResponse)))
    post("/analytics", body = request.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, errorResponse))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
