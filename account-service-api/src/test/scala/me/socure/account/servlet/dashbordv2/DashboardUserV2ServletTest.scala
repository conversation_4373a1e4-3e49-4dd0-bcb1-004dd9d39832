package me.socure.account.servlet.dashbordv2

import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{BusinessUserRoleService, PasswordService}
import me.socure.constants.CaseManagementPermissions
import me.socure.dashboardv2.DashboardUserV2Servlet
import me.socure.model.account.{AccountIdName, UserRole}
import me.socure.model.dashboardv2._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.UpdateQuicksightUserStatus
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 8/18/16.
  */
class DashboardUserV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter{
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service: DashboardUserServiceV2 = mock[DashboardUserServiceV2]
  val passwordService: PasswordService = mock[PasswordService]
  val businessUserRoleService: BusinessUserRoleService = mock[BusinessUserRoleService]
  val servlet = new DashboardUserV2Servlet(service, passwordService, businessUserRoleService)

  addServlet(servlet, "/*")

  before {
    reset(service, passwordService, businessUserRoleService)
  }

  val user: DashboardUserV2 = DashboardUserV2(1, "firstname", "lastname", "email","account_name", "contact_number", isPrimryAdmin = true, isLocked = false)
  val userForm: BusinessUserForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(1))), UserFixture.delegatedUserForm)
  val userFormV2: BusinessUserForm = BusinessUserForm(1, List(EnvironmentRoles(1, Set(1))), UserFixture.delegatedUserForm, Some(Creator(1,1)))

  test("should get user list with 200 status"){
    when(service.listAllUsers(1, None)) thenReturn Future.successful(Right(Vector(user)))
    get("/list_users/1"){
      validate[Response[Vector[DashboardUserV2]]](status, 200, body, Response(ResponseStatus.Ok, Vector(user)))
    }
  }


  test("should listuserIds user list with 200 status"){
    when(service.listAllUserIds(1)) thenReturn Future.successful(Right(Vector[Long](10,5)))
    get("/list_userids/1"){
      validate[Response[Vector[Long]]](status, 200, body, Response(ResponseStatus.Ok, Vector[Long](10,5)))
    }
  }

  test("should ask service subaccount's user list as well"){
    when(service.listAllUsers(1, None, showSubAccount = true)) thenReturn Future.successful(Right(Vector(user)))
    get("/list_users/1?showSubAccount=true"){
      validate[Response[Vector[DashboardUserV2]]](status, 200, body, Response(ResponseStatus.Ok, Vector(user)))
    }
  }

  test("should get user list with 400 status"){
    val expectedError = ErrorResponseFactory.get(AccountNotFound)
    when(service.listAllUsers(1, None)) thenReturn Future.successful(Left(expectedError))
    get("/list_users/1"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  /***
    *
    */
  test("create user status should be 200"){
    val details = UserActivationDetails("first", "last", "email", Option("activation"))
    when(service.createUser(userForm)) thenReturn Future.successful(Right(details))
    post("/create_user", userForm.encodeJson()){
      validate[Response[UserActivationDetails]](status, 200, body, Response(ResponseStatus.Ok, details))
    }
    verify(service).createUser(userForm)
  }

  test("create user status should be 400"){
    val error = ErrorResponseFactory.get(UnknownError)
    when(service.createUser(userForm)) thenReturn Future.successful(Left(error))
    post("/create_user", userForm.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    verify(service).createUser(userForm)
  }

  /***
    *
    */
  test("update user inform status should be 200"){
    when(service.updateUser(1, userForm)) thenReturn Future.successful(Right(true))
    when(businessUserRoleService.isPermissionAvailable(userForm.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    post("/update_user", Map("userid" -> "1", "user" -> userForm.encodeJson())){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("update user inform status should be 400"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(businessUserRoleService.isPermissionAvailable(userForm.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.updateUser(1, userForm)) thenReturn Future.successful(Left(expectedError))
    post("/update_user", Map("userid" -> "1", "user" -> userForm.encodeJson())){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  /***
    * Lock User
    */
  test("lock user should return true"){
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.toggleBusinessUserLock(1, isLocked = true, None)) thenReturn Future.successful(Right(true))
    post("/lock_user", Map("user_id" -> "1")){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("lock user should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.toggleBusinessUserLock(1, isLocked = true, None)) thenReturn Future.successful(Left(expectedError))
    post("/lock_user", Map("user_id" -> "1")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  /***
    * Unlock User
    */
  test("unlock user should return true"){
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.toggleBusinessUserLock(1, isLocked = false, None)) thenReturn Future.successful(Right(true))
    post("/unlock_user", Map("user_id" -> "1")){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("unlock user should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.toggleBusinessUserLock(1, isLocked = false, None)) thenReturn Future.successful(Left(expectedError))
    post("/unlock_user", Map("user_id" -> "1")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  /***
    * Delete Business User
    */
  test("delete business user should return true"){
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), Some(AccountDelta("<EMAIL>", None, None, None, Some(Seq(AccountWithRoles(AccountIdName(22, "Account"), Seq.empty))), true)), None, Seq.empty)
    val expectedResponse = Response(ResponseStatus.Ok, (auditDetails,true))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.deleteBusinessUser(1, None)) thenReturn Future.successful(auditDetails,Right(true))
    post("/delete_user", Map("user_id" -> "1")){
      validate[Response[(AuditDetails,Boolean)]](status, 200, body, expectedResponse)
    }
  }

  test("delete business user should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), None, Some(expectedError), Seq.empty)
    val expectedResponse = Response(ResponseStatus.Ok, (auditDetails,expectedError))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.deleteBusinessUser(1, None))  thenReturn Future.successful(auditDetails,Left(expectedError))
    post("/delete_user", Map("user_id" -> "1")){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, expectedResponse)
    }
  }

  test("delete business user should return primary user deletion"){
    val expectedError = ErrorResponseFactory.get(PrimaryBusinessUserDeletion)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), None, Some(expectedError), Seq.empty)
    val expectedResponse = Response(ResponseStatus.Ok, (auditDetails,expectedError))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(service.deleteBusinessUser(1, None))  thenReturn Future.successful(auditDetails,Left(expectedError))
    post("/delete_user", Map("user_id" -> "1")){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, expectedResponse)
    }
  }

  /***
    * Get Business User Roles
    */
  test("get business user role should return roles"){
    val roles = DashboardUserWithRoles(UserFixture.dashboardV2, Set(13), Seq(EnvironmentRoles(1, Set(1,2))))
    when(service.getUserWithRoles(1, None)) thenReturn Future.successful(Right(roles))
    get("/get_user_information/1"){
      validate[Response[DashboardUserWithRoles]](status, 200, body, Response(ResponseStatus.Ok, roles))
    }
  }

  test("get business user role should return roles not found"){
    val expectedError = ErrorResponseFactory.get(RolesNotFound)
    when(service.getUserWithRoles(1, None)) thenReturn Future.successful(Left(expectedError))
    get("/get_user_information/1"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  test("get business user role should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(service.getUserWithRoles(1, None)) thenReturn Future.successful(Left(expectedError))
    get("/get_user_information/1"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
  }

  test("force_reset_password return properly"){
    val details = UserActivationDetails("first", "last", "email", Option("activation"))
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(passwordService.forceResetPassword(1, None)) thenReturn Future.successful(Right(details))
    post("/force_reset_password", Map("user_id" -> "1")){
      validate[Response[UserActivationDetails]](status, 200, body, Response(ResponseStatus.Ok, details))
      verify(passwordService).forceResetPassword(1, None)
    }
  }

  test("force_reset_password should return an error when service fails"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(businessUserRoleService.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(passwordService.forceResetPassword(1, None)) thenReturn Future.successful(Left(expectedError))
    post("/force_reset_password", Map("user_id" -> "1")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
      verify(passwordService).forceResetPassword(1, None)
    }
  }

  test("get user id by username properly"){
    when(service.getUserIdByUsername("test")) thenReturn Future.successful(Right(1L))
    get("/get_user_id/test"){
      validate[Response[Long]](status, 200, body, Response(ResponseStatus.Ok, 1L))
      verify(service).getUserIdByUsername("test")
    }
  }

  test("get user id by username should fail when the user is not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(service.getUserIdByUsername("test")) thenReturn Future.successful(Left(expectedError))
    get("/get_user_id/test"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
      verify(service).getUserIdByUsername("test")
    }
  }

  test("update user v2 should return 200"){
    val userId = 1
    when(service.updateUser(userId, userFormV2)) thenReturn Future.successful(Right(true))
    submit( method = "POST",
      path =  "/update_user",
      queryParams = Map("userid" -> userId.toString, "user" -> userFormV2.encodeJson())
    ){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateUser(userId, userFormV2)
  }

  test("update user v2 should fail"){
    val userId = 1
    val error = ErrorResponseFactory.get(UnknownError)
    when(service.updateUser(userId, userFormV2)) thenReturn Future.successful(Left(error))
    submit( method = "POST",
      path =  "/update_user",
      queryParams = Map("userid" -> userId.toString, "user" -> userFormV2.encodeJson())
    ) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    verify(service).updateUser(userId, userFormV2)
  }

  test("lock user v2 should return true"){
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
     when(service.toggleBusinessUserLock(userId, isLocked = true, Some(accountWithCreator))) thenReturn Future.successful(Right(true))
    post(s"/lock_user?user_id=$userId",accountWithCreator.encodeJson()){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).toggleBusinessUserLock(userId, isLocked = true, Some(accountWithCreator))
  }

  test("lock user v2 should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
     when(service.toggleBusinessUserLock(userId, isLocked = true, Some(accountWithCreator))) thenReturn Future.successful(Left(expectedError))
    post(s"/lock_user?user_id=$userId",accountWithCreator.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
    verify(service).toggleBusinessUserLock(userId, isLocked = true, Some(accountWithCreator))
  }

  test("unlock user v2 should return true"){
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    when(service.toggleBusinessUserLock(userId, isLocked = false, Some(accountWithCreator))) thenReturn Future.successful(Right(true))
    post(s"/unlock_user?user_id=$userId", accountWithCreator.encodeJson()){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).toggleBusinessUserLock(userId, isLocked = false, Some(accountWithCreator))
  }

  test("unlock user v2 should return user not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    when(service.toggleBusinessUserLock(userId, isLocked = false, Some(accountWithCreator))) thenReturn Future.successful(Left(expectedError))
    post(s"/unlock_user?user_id=$userId", accountWithCreator.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
    verify(service).toggleBusinessUserLock(userId, isLocked = false, Some(accountWithCreator))
  }

  test("delete business user v2 should return true"){
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(1, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), Some(AccountDelta("<EMAIL>", None, None, None, Some(Seq(AccountWithRoles(AccountIdName(1, "Account"), Seq.empty))), true)), None, Seq.empty)
     when(service.deleteBusinessUser(userId, Some(accountWithCreator))) thenReturn Future.successful(auditDetails,Right(true))
    post("/delete_user?user_id=1",accountWithCreator.encodeJson()){
      validate[Response[(AuditDetails,Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails,true)))
    }
  }

  test("delete business user v2 should return user not found"){
    val expectedError = ErrorResponseFactory.get(UnableToUpdateUserAccountAssociation)
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(1, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), None, Some(expectedError), Seq.empty)
    val expectedResponse = Response(ResponseStatus.Ok, (auditDetails,expectedError))
    when(service.deleteBusinessUser(userId, Some(accountWithCreator))) thenReturn Future.successful(auditDetails,Left(expectedError))
    post("/delete_user?user_id=1",accountWithCreator.encodeJson()){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, expectedResponse)
    }
  }

  test("delete business user v2 should fail for primary user deletion"){
    val expectedError = ErrorResponseFactory.get(UnableToUpdateUserAccountAssociation)
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(1, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), None, Some(expectedError), Seq.empty)
    val expectedResponse = Response(ResponseStatus.Ok, (auditDetails,expectedError))
    when(service.deleteBusinessUser(userId, Some(accountWithCreator))) thenReturn Future.successful(auditDetails,Left(expectedError))
    post("/delete_user?user_id=1",accountWithCreator.encodeJson()){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, expectedResponse)
    }
  }

  test("force_reset_password v2 return properly"){
    val details = UserActivationDetails("first", "last", "email", Option("activation"))
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    when(businessUserRoleService.isPermissionAvailable(accountWithCreator.creator.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(passwordService.forceResetPassword(userId, Some(accountWithCreator))) thenReturn Future.successful(Right(details))
    post(s"/force_reset_password?user_id=$userId", accountWithCreator.encodeJson()){
      validate[Response[UserActivationDetails]](status, 200, body, Response(ResponseStatus.Ok, details))
      verify(passwordService).forceResetPassword(userId, Some(accountWithCreator))
    }
  }

  test("force_reset_password v2 should return an error when service fails"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    val userId = 1
    val accountWithCreator = AccountWithCreator(1, Creator(1,1))
    when(businessUserRoleService.isPermissionAvailable(accountWithCreator.creator.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(passwordService.forceResetPassword(userId, Some(accountWithCreator))) thenReturn Future.successful(Left(expectedError))
    post(s"/force_reset_password?user_id=$userId", accountWithCreator.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
      verify(passwordService).forceResetPassword(userId, Some(accountWithCreator))
    }
  }

  test("get public account id by username properly"){
    when(service.getPublicAccountIdByUserName("test")) thenReturn Future.successful(Right("public_id"))
    get("/get_public_account_id/test"){
      validate[Response[String]](status, 200, body, Response(ResponseStatus.Ok, "public_id"))
      verify(service).getPublicAccountIdByUserName("test")
    }
  }

  test("get public account id by username should fail when the user is not found"){
    val expectedError = ErrorResponseFactory.get(UserNotFound)
    when(service.getPublicAccountIdByUserName("test")) thenReturn Future.successful(Left(expectedError))
    get("/get_public_account_id/test"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
      verify(service).getPublicAccountIdByUserName("test")
    }
  }

  test("list users v2 should return list of users"){
    val accountId = 1
    val creatorUserId = 10
    val creatorAccountId = 10
    val creator = Creator(creatorUserId,creatorAccountId)
    val user10 = DashboardUserV2(10,"guest10","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = true,isLocked = false)
    val user11 = DashboardUserV2(11,"guest11","user","<EMAIL>","sub-account2","**********",isPrimryAdmin = false,isLocked = false)
    val expected = Vector(user10, user11)
    when(service.listAllUsers(accountId, Some(creator))) thenReturn Future.successful(Right(expected))
    get(s"/list_users/$accountId?creator_account_id=$creatorAccountId&creator_user_id=$creatorUserId"){
      validate[Response[Vector[DashboardUserV2]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).listAllUsers(accountId, Some(creator))
  }

  test("list users v2 should return error"){
    val expectedError = ErrorResponseFactory.get(AccessForbidden)
    val accountId = 100
    val creatorUserId = 10
    val creatorAccountId = 10
    val creator = Creator(creatorUserId,creatorAccountId)
    when(service.listAllUsers(accountId, Some(creator))) thenReturn Future.successful(Left(expectedError))
    get(s"/list_users/$accountId?creator_account_id=$creatorAccountId&creator_user_id=$creatorUserId"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expectedError))
    }
    verify(service).listAllUsers(accountId, Some(creator))
  }


  test("should not list business user with associations for v1 account") {
    val userId = 8
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserAccountAssociation)
    when(service.getUserWithRolesAndAssociations(userId, creator)) thenReturn Future.successful(Left(expected))
    get(s"/get_user_information_v2/$userId?creator_account_id=$creatorAccountId&creator_user_id=$creatorUserId"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getUserWithRolesAndAssociations(userId, creator)
  }

  test("should list business user with associations") {
    val userId = 11
    val creatorUserId = 10
    val creatorAccountId = 6
    val creator = Creator(creatorUserId,creatorAccountId)
    val expected = DashboardUserWithAssociations(UserDetails(11,"guest11","user","<EMAIL>","**********"),List(AccountWithRoles(AccountIdName(6,"sub-account2"),Vector(UserRole(Some(1),"Role-9",None)))))
    when(service.getUserWithRolesAndAssociations(userId, creator)) thenReturn Future.successful(Right(expected))
    get(s"/get_user_information_v2/$userId?creator_account_id=$creatorAccountId&creator_user_id=$creatorUserId"){
      validate[Response[DashboardUserWithAssociations]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserWithRolesAndAssociations(userId, creator)
  }

  test("should update business user with associations") {
    val creatorUserId = 10
    val creatorAccountId = 6
    val creator = Creator(creatorUserId,creatorAccountId)
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val expected = true
    val actionUserInfo = ActionUserInfo(UserDetails(10, firstName = "first", lastName = "last", email = "email", contactNumber = "**********"), Set(1, 2), Seq("1", "2"))
    val expectedAuditDetails = AuditDetails(true, actionUserInfo, None, None, Seq.empty)
    when(service.updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) thenReturn Future.successful(expectedAuditDetails,Right(expected))
    post("/update_business_user", body = updateBusinessUserInput.encodeJson()){
      validate[Response[(AuditDetails,Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (expectedAuditDetails,expected)))
    }
    verify(service).updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)
  }

  test("should not update business user with associations") {
    val creator = Creator(10, 6)
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val expected = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateBusinessUser)
    val actionUserInfo = ActionUserInfo(UserDetails(10, firstName = "first", lastName = "last", email = "email", contactNumber = "**********"), Set(1, 2), Seq("1", "2"))
    val expectedAuditDetails = AuditDetails(true, actionUserInfo, None, None, Seq.empty)
    when(service.updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)) thenReturn Future.successful(expectedAuditDetails,Left(expected))
    post("/update_business_user", body = updateBusinessUserInput.encodeJson()){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, Response(ResponseStatus.Ok,(expectedAuditDetails, expected)))
    }
    verify(service).updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput)
  }

  test("should create business user with associations") {
    val creatorUserId = 10
    val creatorAccountId = 6
    val creator = Creator(creatorUserId,creatorAccountId)
    val createBusinessUserInput = CreateBusinessUserInput(accountId = 6, email = "email", firstName = "test",lastName = "last",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val expectedActivationDetails = UserActivationDetails("first", "last", "email", Option("activation"))
    val actionUserInfo = ActionUserInfo(UserDetails(creatorUserId,firstName = "first", lastName = "last",  email = "email", contactNumber = "**********"),Set(1,2),Seq("1","2"))
    val expectedAuditDetails = AuditDetails(true, actionUserInfo, None, None, Seq.empty)
    when(service.createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput)) thenReturn Future.successful(expectedAuditDetails,Right(expectedActivationDetails))
    post("/create_business_user", body = createBusinessUserInput.encodeJson()){
      validate[Response[(AuditDetails,UserActivationDetails)]](status, 200, body, Response(ResponseStatus.Ok, (expectedAuditDetails,expectedActivationDetails)))
    }
    verify(service).createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput)
  }

  test("should not create business user with associations") {
    val creator = Creator(10, 6)
    val createBusinessUserInput = CreateBusinessUserInput(accountId = 6,email = "email", firstName = "test",lastName = "last",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val expected = ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateBusinessUser)
    val actionUserInfo = ActionUserInfo(UserDetails(10, firstName = "first", lastName = "last", email = "email", contactNumber = "**********"), Set(1, 2), Seq("1", "2"))
    val expectedAuditDetails = AuditDetails(false, actionUserInfo, None, None, Seq.empty)
    when(service.createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput)) thenReturn Future.successful(expectedAuditDetails,Left(expected))
    post("/create_business_user", body = createBusinessUserInput.encodeJson()){
      validate[Response[(AuditDetails,ErrorResponse)]](status, 200, body, Response(ResponseStatus.Ok, (expectedAuditDetails,expected)))
    }
    verify(service).createBusinessUserWithAssociationsWithAuditDetails(createBusinessUserInput)
  }

  test("Get users list by permission - success"){
    val request = UsersByPermissionFilterRequest(Seq(1), Some(Seq(1,2)), Seq(CaseManagementPermissions.CaseManagementReview.name), 1, None, None)
    val response = UsersByPermissionFilterResponse(Seq(UserDetailsByPermissionFilter(1,"test", "last", "<EMAIL>", None, 1, 1)), None, None, 1)
    when(service.getUsersFilteredByPermissions(request)) thenReturn Future.successful(Right(response))
    post("/list_users/by_permission", body = request.encodeJson()){
      validate[Response[UsersByPermissionFilterResponse]](status, 200, body, Response(ResponseStatus.Ok, response))
      verify(service).getUsersFilteredByPermissions(request)
    }
  }

  test("Get users list by permission - failure"){
    val request = UsersByPermissionFilterRequest(Seq.empty, Some(Seq(1,2)), Seq(CaseManagementPermissions.CaseManagementReview.name), 1, None, None)
    val response = ErrorResponseFactory.get(ExceptionCodes.UserNotFound)
    when(service.getUsersFilteredByPermissions(request)) thenReturn Future.successful(Left(response))
    post("/list_users/by_permission", body = request.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, response))
      verify(service).getUsersFilteredByPermissions(request)
    }
  }

  test("Get is user is registered in quicksight - success"){
    val userId = 10
    when(service.isQuicksightUser(userId)) thenReturn Future.successful(Right(true))
    get(s"/is_quicksight_user?user_id=$userId"){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).isQuicksightUser(userId)
  }

  test("update quicksight user status - success"){
    val request = UpdateQuicksightUserStatus(10, true)
    when(service.updateQuicksightUserStatus(request)) thenReturn Future.successful(Right(true))
    put("/update_quicksight_user_status", body = request.encodeJson()){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateQuicksightUserStatus(request)
  }

  test("update quicksight user status - failure"){
    val request = UpdateQuicksightUserStatus(10, true)
    val response = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.updateQuicksightUserStatus(request)) thenReturn Future.successful(Left(response))
    put("/update_quicksight_user_status", body = request.encodeJson()){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, response))
    }
    verify(service).updateQuicksightUserStatus(request)
  }

  test("update terms od service - success"){
    when(service.updateUserTOS(10)) thenReturn Future.successful(Right(true))
    put("/update_user_tos/10"){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateUserTOS(10)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  def validateTuple[T: Manifest,A: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: (A,T)): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[(A,T)]
    actual shouldBe expected
  }

}
