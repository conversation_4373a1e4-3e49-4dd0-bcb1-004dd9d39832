package me.socure.account.servlet.account.prospect

import me.socure.account.prospect.ProspectService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.model.prospect.{ProspectExclusionDetail, ProspectExclusionInput, ProspectInclusionDetail, ProspectInclusionInput}
import me.socure.util.JsonEnrichments.JsonDecoder
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mockito.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import me.socure.util.JsonEnrichments._
import scala.concurrent.{ExecutionContext, Future}

class ProspectServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: ProspectService = mock[ProspectService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: ProspectServlet = new ProspectServlet(service, hmacVerifier)
  val clock = new FakeClock(new DateTime("2024-04-23").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get Exclusion List - success") {
    val start = Option(0)
    val size = Option(10)
    val search = ""
    val expected = Seq(ProspectExclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))

    when(service.getExclusionList(start, size, search)).thenReturn(Future.successful(Right(expected)))
    get(s"/exclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[ProspectExclusionDetail]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getExclusionList(start, size, search)
  }

  test("Get Exclusion List - Fail") {
    val start = Option(0)
    val size = Option(10)
    val search = ""
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)

    when(service.getExclusionList(start, size, search)).thenReturn(Future.successful(Left(expected)))
    get(s"/exclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getExclusionList(start, size, search)
  }

  test("Get Exclusion List total count - success") {
    when(service.getExclusionListTotalCount).thenReturn(Future.successful(Right(10)))
    get("/exclusion/total_count",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 10))
    }
    verify(service).getExclusionListTotalCount
  }

  test("Get Exclusion List total count - Fail") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.getExclusionListTotalCount).thenReturn(Future.successful(Left(expected)))
    get("/exclusion/total_count",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getExclusionListTotalCount
  }

  test("Search Exclusion List - success") {
    val start = Option(0)
    val size = Option(10)
    val search = "test"
    val expected = Seq(ProspectExclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))

    when(service.getExclusionList(start, size, search)).thenReturn(Future.successful(Right(expected)))
    get(s"/exclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[ProspectExclusionDetail]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getExclusionList(start, size, search)
  }

  test("Insert Exclusion detail - success") {
    val input = ProspectExclusionInput(Option.empty, "<EMAIL>", "test")
    val expected = true
    when(service.insertOrUpdateExclusionList(input)).thenReturn(Future.successful(Right(expected)))
    put(s"/exclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertOrUpdateExclusionList(input)
  }

  test("Insert Exclusion detail - fail") {
    val input = ProspectExclusionInput(Option.empty, "<EMAIL>", "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.insertOrUpdateExclusionList(input)).thenReturn(Future.successful(Left(expected)))
    put(s"/exclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertOrUpdateExclusionList(input)
  }

  test("Update Exclusion detail - success") {
    val input = ProspectExclusionInput(Some(1), "<EMAIL>", "test")
    val expected = true
    when(service.insertOrUpdateExclusionList(input)).thenReturn(Future.successful(Right(expected)))
    put(s"/exclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertOrUpdateExclusionList(input)
  }

  test("Update Exclusion detail - fail") {
    val input = ProspectExclusionInput(Some(1), "<EMAIL>", "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.insertOrUpdateExclusionList(input)).thenReturn(Future.successful(Left(expected)))
    put(s"/exclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertOrUpdateExclusionList(input)
  }

  test("Delete Exclusion detail - success") {
    val input = 1
    val expected = true
    when(service.deleteExclusionDetail(input)).thenReturn(Future.successful(Right(expected)))
    delete(s"/exclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deleteExclusionDetail(input)
  }

  test("Delete Non Existing Exclusion detail") {
    val input = 1
    val expected = false
    when(service.deleteExclusionDetail(input)).thenReturn(Future.successful(Right(expected)))
    delete(s"/exclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deleteExclusionDetail(input)
  }

  test("Delete Exclusion detail - fail") {
    val input = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.deleteExclusionDetail(input)).thenReturn(Future.successful(Left(expected)))
    delete(s"/exclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteExclusionDetail(input)
  }

  test("Get Inclusion List - success") {
    val start = Option(0)
    val size = Option(10)
    val search = ""
    val expected = Seq(ProspectInclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))

    when(service.getInclusionList(start, size, search)).thenReturn(Future.successful(Right(expected)))
    get(s"/inclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[ProspectInclusionDetail]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getInclusionList(start, size, search)
  }

  test("Get Inclusion List - Fail") {
    val start = Option(0)
    val size = Option(10)
    val search = ""
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)

    when(service.getInclusionList(start, size, search)).thenReturn(Future.successful(Left(expected)))
    get(s"/inclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getInclusionList(start, size, search)
  }

  test("Get Inclusion List total count - success") {
    when(service.getInclusionListTotalCount).thenReturn(Future.successful(Right(10)))
    get("/inclusion/total_count",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 10))
    }
    verify(service).getInclusionListTotalCount
  }

  test("Get Inclusion List total count - Fail") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.getInclusionListTotalCount).thenReturn(Future.successful(Left(expected)))
    get("/inclusion/total_count",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getInclusionListTotalCount
  }

  test("Search Inclusion List - success") {
    val start = Option(0)
    val size = Option(10)
    val search = "test"
    val expected = Seq(ProspectInclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))

    when(service.getInclusionList(start, size, search)).thenReturn(Future.successful(Right(expected)))
    get(s"/inclusion?start=${start.get}&size=${size.get}&search=$search",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[ProspectInclusionDetail]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getInclusionList(start, size, search)
  }

  test("Insert Inclusion detail - success") {
    val input = ProspectInclusionInput(Option.empty, "<EMAIL>", "test")
    val expected = true
    when(service.insertOrUpdateInclusionList(input)).thenReturn(Future.successful(Right(expected)))
    put(s"/inclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertOrUpdateInclusionList(input)
  }

  test("Insert Inclusion detail - fail") {
    val input = ProspectInclusionInput(Option.empty, "<EMAIL>", "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.insertOrUpdateInclusionList(input)).thenReturn(Future.successful(Left(expected)))
    put(s"/inclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertOrUpdateInclusionList(input)
  }

  test("Update Inclusion detail - success") {
    val input = ProspectInclusionInput(Some(1), "<EMAIL>", "test")
    val expected = true
    when(service.insertOrUpdateInclusionList(input)).thenReturn(Future.successful(Right(expected)))
    put(s"/inclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertOrUpdateInclusionList(input)
  }

  test("Update Inclusion detail - fail") {
    val input = ProspectInclusionInput(Some(1), "<EMAIL>", "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.insertOrUpdateInclusionList(input)).thenReturn(Future.successful(Left(expected)))
    put(s"/inclusion",
      body = Serialization.write(input),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertOrUpdateInclusionList(input)
  }

  test("Delete Inclusion detail - success") {
    val input = 1
    val expected = true
    when(service.deleteInclusionDetail(input)).thenReturn(Future.successful(Right(expected)))
    delete(s"/inclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deleteInclusionDetail(input)
  }

  test("Delete Non Existing Inclusion detail") {
    val input = 1
    val expected = false
    when(service.deleteInclusionDetail(input)).thenReturn(Future.successful(Right(expected)))
    delete(s"/inclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deleteInclusionDetail(input)
  }

  test("Delete Inclusion detail - fail") {
    val input = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.deleteInclusionDetail(input)).thenReturn(Future.successful(Left(expected)))
    delete(s"/inclusion/$input",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteInclusionDetail(input)
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
