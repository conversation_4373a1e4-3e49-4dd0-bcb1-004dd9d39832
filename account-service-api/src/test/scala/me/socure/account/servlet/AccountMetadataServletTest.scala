package me.socure.account.servlet

import me.socure.account.service.AccountMetadataService
import me.socure.common.servlet.Response
import me.socure.common.servlet.ResponseStatus
import me.socure.convertors.AccountMetadataConvertors
import me.socure.model.account.AccountMetadata
import me.socure.security.hmac.HMACHttpVerifier
import me.socure.storage.slick.tables.account.DtoAccountMetadata
import org.joda.time.DateTime
import org.json4s.native.Serialization
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite
import org.scalatestplus.mockito.MockitoSugar

import scala.concurrent.{ExecutionContext, Future}

class AccountMetadataServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec: ExecutionContext = ExecutionContext.global
  implicit val formats = org.json4s.DefaultFormats

  val accountMetadataService: AccountMetadataService = mock[AccountMetadataService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet = new AccountMetadataServlet(accountMetadataService, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(accountMetadataService)
  }

  val now = new DateTime(2023, 1, 1, 0, 0)
  val accountId = 1L
  val childId = "child123"
  val createdBy = "<EMAIL>"

  val dtoAccountMetadata = DtoAccountMetadata(
    id = 1L,
    accountId = accountId,
    childId = childId,
    createdAt = now,
    updatedAt = now,
    createdBy = createdBy,
    updatedBy = createdBy
  )

  val accountMetadata = AccountMetadata(
    accountId = accountId,
    childId = childId
  )

  def validate[T: Manifest](status: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    org.json4s.native.Serialization.read[T](body) should equal(expected)
  }

  test("GET /:accountId should return metadata when it exists") {
    when(accountMetadataService.getByAccountId(accountId)).thenReturn(Future.successful(Some(dtoAccountMetadata)))

    get(s"/${accountId}") {
      validate[Response[AccountMetadata]](
        status,
        200,
        body,
        Response(ResponseStatus.Ok, AccountMetadataConvertors.toModel(dtoAccountMetadata))
      )
    }

    verify(accountMetadataService).getByAccountId(accountId)
  }

  test("GET /:accountId should return 404 when metadata doesn't exist") {
    when(accountMetadataService.getByAccountId(accountId)).thenReturn(Future.successful(None))

    get(s"/${accountId}") {
      validate[Response[ErrorResponse]](
        status,
        400,
        body,
        Response(ResponseStatus.Error, ErrorResponse(668, "Account metadata not found"))
      )
    }

    verify(accountMetadataService).getByAccountId(accountId)
  }

  test("PUT /:accountId should create or update metadata") {
    when(accountMetadataService.createOrUpdate(accountId, childId, "system"))
      .thenReturn(Future.successful(dtoAccountMetadata))

    put(
      s"/${accountId}",
      body = Serialization.write(accountMetadata),
      headers = Map("Content-Type" -> "application/json")
    ) {
      validate[Response[AccountMetadata]](
        status,
        200,
        body,
        Response(ResponseStatus.Ok, AccountMetadataConvertors.toModel(dtoAccountMetadata))
      )
    }

    verify(accountMetadataService).createOrUpdate(accountId, childId, "system")
  }

  test("PUT /:accountId should use X-User-Email header when available") {
    when(accountMetadataService.createOrUpdate(accountId, childId, createdBy))
      .thenReturn(Future.successful(dtoAccountMetadata))

    put(
      s"/${accountId}",
      body = Serialization.write(accountMetadata),
      headers = Map(
        "Content-Type" -> "application/json",
        "X-User-Email" -> createdBy
      )
    ) {
      validate[Response[AccountMetadata]](
        status,
        200,
        body,
        Response(ResponseStatus.Ok, AccountMetadataConvertors.toModel(dtoAccountMetadata))
      )
    }

    verify(accountMetadataService).createOrUpdate(accountId, childId, createdBy)
  }

  test("PUT /:accountId should return 400 when account ID in path doesn't match body") {
    val mismatchedMetadata = AccountMetadata(
      accountId = 2L,
      childId = childId
    )

    put(
      s"/${accountId}",
      body = Serialization.write(mismatchedMetadata),
      headers = Map("Content-Type" -> "application/json")
    ) {
      status should equal(400)
      body should include("Account ID in path must match account ID in body")
    }

    verify(accountMetadataService, never()).createOrUpdate(accountId, childId, "system")
  }

  test("DELETE /:accountId should delete metadata") {
    when(accountMetadataService.delete(accountId)).thenReturn(Future.successful(1))

    delete(s"/${accountId}") {
      validate[Response[Boolean]](
        status,
        200,
        body,
        Response(ResponseStatus.Ok, true)
      )
    }

    verify(accountMetadataService).delete(accountId)
  }

  test("DELETE /:accountId should return false when no metadata was deleted") {
    when(accountMetadataService.delete(accountId)).thenReturn(Future.successful(0))

    delete(s"/${accountId}") {
      validate[Response[Boolean]](
        status,
        200,
        body,
        Response(ResponseStatus.Ok, false)
      )
    }

    verify(accountMetadataService).delete(accountId)
  }
}
