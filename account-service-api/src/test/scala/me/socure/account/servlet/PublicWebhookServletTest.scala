package me.socure.account.servlet

import me.socure.account.service.PublicWebhookService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.model.account.{PublicWebhook, PublicWebhookWithPublicAccountId, PublicWebhooksWithPublicAccountId}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito._
import org.mockito.{Mockito, Matchers => MMatchers}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

class PublicWebhookServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  val publicWebhookService: PublicWebhookService = mock[PublicWebhookService]
  val servlet = new PublicWebhookServlet(publicWebhookService)
  addServlet(servlet, "/*")

  test("list all webhooks"){
    val list = List(
      PublicWebhook(Some("Watchlist"),Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC)),
      PublicWebhook(Some("Watchlist"),Some("enable"),"test2.com", "certificate 2", 2 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    )
    val res = Response(ResponseStatus.Ok, data = list)
    when(publicWebhookService.listPublicWebhooks) thenReturn Future.successful(Right(list))
    get("/list_public_webhooks") {
      validate[Response[List[PublicWebhook]]](status, 200, body, res)
    }
  }

  test("get a webhook"){
    val publicWebhook = PublicWebhook(Some("Watchlist"),Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val res = Response(ResponseStatus.Ok, data = publicWebhook)
    when(publicWebhookService.getPublicWebhook(1)) thenReturn Future.successful(Right(publicWebhook))
    get("/get_public_webhook", Map("environment_id" -> "1")) {
      validate[Response[PublicWebhook]](status, 200, body, res)
    }
  }

  test("insert webhook should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(publicWebhookService.insertPublicWebhook(1, "test.com", "certificate 1", 1)) thenReturn Future.successful(Right(true))
    post("/insert_public_webhook", Map("environment_id" -> "1", "endpoint" -> "test.com", "public_key_certificate" -> "certificate 1", "subscription_type" -> 1.toString)) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("get webhooks by environment id and sucscription type"){
    Mockito.reset(publicWebhookService)
    val publicWebhook1 = PublicWebhook(Some("Watchlist"),Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val publicWebhookWithPublicAccountId = PublicWebhooksWithPublicAccountId(
      webhooks = Seq(publicWebhook1),
      publicAccountId = PublicIdGenerator.account()
    )
    val res = Response(ResponseStatus.Ok, data = publicWebhookWithPublicAccountId)
    when(publicWebhookService.getPublicWebhooks(1, Some(1L))) thenReturn Future.successful(Right(publicWebhookWithPublicAccountId))
    get("/environment/1/subscription/1") {
      validate[Response[PublicWebhooksWithPublicAccountId]](status, 200, body, res)
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhooks(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Option[Long]]))
  }

  test("should fail to get webhooks by environment id and sucscription type"){
    Mockito.reset(publicWebhookService)
    val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(publicWebhookService.getPublicWebhooks(1, Some(1L))) thenReturn Future.successful(Left(error))
    get("/environment/1/subscription/1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhooks(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Option[Long]]))
  }

  test("get webhooks by environment id"){
    Mockito.reset(publicWebhookService)
    val publicWebhook1 = PublicWebhook(Some("Watchlist"),Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val publicWebhook2 = PublicWebhook(Some("DV"),Some("enable"),"test.com", "certificate 2", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val publicWebhookWithPublicAccountId = PublicWebhooksWithPublicAccountId(
      webhooks = Seq(publicWebhook1, publicWebhook2),
      publicAccountId = PublicIdGenerator.account()
    )
    val res = Response(ResponseStatus.Ok, data = publicWebhookWithPublicAccountId)
    when(publicWebhookService.getPublicWebhooks(1, None)) thenReturn Future.successful(Right(publicWebhookWithPublicAccountId))
    get("/environment/1") {
      validate[Response[PublicWebhooksWithPublicAccountId]](status, 200, body, res)
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhooks(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Option[Long]]))
  }

  test("should fail to get webhooks by environment id"){
    Mockito.reset(publicWebhookService)
    val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(publicWebhookService.getPublicWebhooks(100,  None)) thenReturn Future.successful(Left(error))
    get("/environment/100") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhooks(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Option[Long]]))
  }

  test("update webhook should return true"){
    Mockito.reset(publicWebhookService)
    val res = Response(ResponseStatus.Ok, data = true)
    when(publicWebhookService.updatePublicWebhook(1, "test.com", "certificate 1", 1)) thenReturn Future.successful(Right(true))
    post("/update_public_webhook", Map("environment_id" -> "1", "endpoint" -> "test.com", "public_key_certificate" -> "certificate 1", "subscription_type" -> 1.toString)) {
      validate[Response[Boolean]](status, 200, body, res)
    }
    Mockito.verify(publicWebhookService, times(1)).updatePublicWebhook(MMatchers.any(classOf[Long]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[Long]))
  }

  test("update webhook should return false"){
    Mockito.reset(publicWebhookService)
    val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(publicWebhookService.updatePublicWebhook(1, "test.com", "certificate 1", 1)) thenReturn Future.successful(Left(error))
    post("/update_public_webhook", Map("environment_id" -> "1", "endpoint" -> "test.com", "public_key_certificate" -> "certificate 1", "subscription_type" -> 1.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    Mockito.verify(publicWebhookService, times(1)).updatePublicWebhook(MMatchers.any(classOf[Long]), MMatchers.any(classOf[String]), MMatchers.any(classOf[String]), MMatchers.any(classOf[Long]))
  }

  test("delete webhook should return true"){
    Mockito.reset(publicWebhookService)
    val res = Response(ResponseStatus.Ok, data = true)
    when(publicWebhookService.deletePublicWebhook(1L, 1L)) thenReturn Future.successful(Right(true))
    post("/delete_public_webhook", Map("environment_id" -> "1", "subscription_type" -> "1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
    Mockito.verify(publicWebhookService, times(1)).deletePublicWebhook(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]))
  }

  test("delete webhook should fail"){
    Mockito.reset(publicWebhookService)
    val error = ErrorResponseFactory.get(ExceptionCodes.UnableToDeletePublicWebhook)
    when(publicWebhookService.deletePublicWebhook(1L, 1L)) thenReturn Future.successful(Left(error))
    post("/delete_public_webhook", Map("environment_id" -> "1", "subscription_type" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    Mockito.verify(publicWebhookService, times(1)).deletePublicWebhook(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]))
  }

  test("get webhooks by accountid, env type id and sucscription type"){
    Mockito.reset(publicWebhookService)
    val publicWebhook1 = PublicWebhook(Some("Watchlist"),Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val publicWebhookWithPublicAccountId = PublicWebhookWithPublicAccountId(
      webhook = Some(publicWebhook1),
      publicAccountId = PublicIdGenerator.account(),
      provisioned = "YES",
      None
    )
    val res = Response(ResponseStatus.Ok, data = publicWebhookWithPublicAccountId)
    when(publicWebhookService.getPublicWebhookByAccount(1, 1, 1L)) thenReturn Future.successful(Right(publicWebhookWithPublicAccountId))
    get("/account/1/environment/type/1/subscription/1", Map("account_id" -> "1", "environment_type_id" -> "1", "subscription_type" -> "1")) {
      validate[Response[PublicWebhookWithPublicAccountId]](status, 200, body, res)
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhookByAccount(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]))
  }

  test("should fail to get webhooks by accountid, env type id and sucscription type"){
    Mockito.reset(publicWebhookService)
    val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(publicWebhookService.getPublicWebhookByAccount(1, 1, 1L)) thenReturn Future.successful(Left(error))
    get("/account/1/environment/type/1/subscription/1", Map("account_id" -> "1", "environment_type_id" -> "1", "subscription_type" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
    Mockito.verify(publicWebhookService, times(1)).getPublicWebhookByAccount(MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]), MMatchers.any(classOf[Long]))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
