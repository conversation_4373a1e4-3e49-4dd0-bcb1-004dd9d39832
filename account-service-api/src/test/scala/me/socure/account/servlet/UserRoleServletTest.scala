package me.socure.account.servlet

import me.socure.account.service.UserRoleService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes, PublicExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.constants.{DashboardUserPermissions, EnvironmentTypes, SystemDefiendRolesPermissions}
import me.socure.model._
import me.socure.model.account._
import me.socure.model.dashboardv2.Creator
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserRoleServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: UserRoleService = mock[UserRoleService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: UserRoleServlet = new UserRoleServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get  user role by id - success") {
    val id = 10
    val userId = 1
    val accountId = 1
    val expected = UserRole(Some(id), "name", Some("description"))
    when(service.getUserRole(id, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/$id",
      params = Map("user_id" -> userId.toString, "account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[UserRole]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserRole(id, userId, accountId)
  }

  test("Get  user role by id - failure") {
    val id = 10
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UserRoleNotFound)
    when(service.getUserRole(id, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/$id",
      params = Map("user_id" -> userId.toString, "account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getUserRole(id, userId, accountId)
  }

  test("Get user role by account - success") {
    val accountId = 1012
    val userId = 1000
    val creator = Creator(userId, accountId)
    val expected = Seq(UserRoleResult(Some(1), 1, "PrimaryAdmin", Some("Primary Administrator"), 1, Some(DateTime.now)), UserRoleResult(Some(2), 1, "InstanceAdmin", Some("Instance Administrator"), 1, Some(DateTime.now)))
    when(service.getUserRolesByAccountId(accountId, creator)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
    verify(service).getUserRolesByAccountId(accountId, creator)
  }

  test("Get user role by account - empty") {
    val accountId = 1012
    val userId = 1000
    val creator = Creator(userId, accountId)
    val expected = Seq.empty
    when(service.getUserRolesByAccountId(accountId, creator)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UserRoleResult]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUserRolesByAccountId(accountId, creator)
  }

  test("Get user role by account when have USER VIEW only - success") {
    val accountId = 1013
    val userId = 1000
    val creator = Creator(userId, accountId)
    val expected = Seq(UserRoleResult(Some(1), 1, "PrimaryAdmin", Some("Primary Administrator"), 1, Some(DateTime.now)), UserRoleResult(Some(2), 1, "InstanceAdmin", Some("Instance Administrator"), 1, Some(DateTime.now)))
    when(service.getUserRolesByAccountId(accountId, creator)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
    verify(service).getUserRolesByAccountId(accountId, creator)
  }

  test("Get user role by account system defined role is filtered  - success") {
    val accountId = 1013
    val userId = 1000
    val creator = Creator(userId, accountId)
    val expected = Seq(UserRoleResult(Some(1), 1, "PrimaryAdmin", Some("Primary Administrator"), 1, Some(DateTime.now)), UserRoleResult(Some(2), 1, "InstanceAdmin", Some("Instance Administrator"), 1, Some(DateTime.now)))
    when(service.getUserRolesByAccountId(accountId, creator, true)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "filter_system_defined_roles" -> "true"),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
    verify(service).getUserRolesByAccountId(accountId, creator, true)
  }


  test("Get user role by account - failure") {
    val accountId = 1012
    val userId = 1000
    val creator = Creator(userId, accountId)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UserRolesFetchError)
    when(service.getUserRolesByAccountId(accountId, creator)).thenReturn(Future.successful(Left(expected)))
    get(s"/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getUserRolesByAccountId(accountId, creator)
  }

  test("Insert user role - success") {
    val userRoleInput = UserRoleInput(None, "test", None, 1000, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    when(service.insertUserRole(userRoleInput)).thenReturn(Future.successful(Right(true)))
    post("/",
      body = Serialization.write(userRoleInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).insertUserRole(userRoleInput)
  }

  test("Insert user role - failure") {
    val userRoleInput = UserRoleInput(None, "test", None, 1000, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole)
    when(service.insertUserRole(userRoleInput)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(userRoleInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertUserRole(userRoleInput)
  }

  test("Update user role - success") {
    val userRoleInput = UserRoleInput(Some(1), "test", None, 1000, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    when(service.updateUserRole(userRoleInput)).thenReturn(Future.successful(Right(true)))
    put("/",
      body = Serialization.write(userRoleInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateUserRole(userRoleInput)
  }

  test("Update user role - failure") {
    val userRoleInput = UserRoleInput(Some(1), "test", None, 1000, Seq(EnvironmentPermissionsWithGlobalScope(0, Set(1001, 1002), None)), Creator(1,1))
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole)
    when(service.updateUserRole(userRoleInput)).thenReturn(Future.successful(Left(expected)))
    put("/",
      body = Serialization.write(userRoleInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updateUserRole(userRoleInput)
  }

  test("Delete user role - success") {
    val id = 1
    val userId = 1
    val accountId = 1
    when(service.deleteUserRole(id, userId, accountId)).thenReturn(Future.successful(Right(1)))
    delete(s"/$id",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).deleteUserRole(id, userId, accountId)
  }

  test("Delete user role - failure") {
    val id = 1
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteUserRole)
    when(service.deleteUserRole(id, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    delete(s"/$id",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteUserRole(id, userId, accountId)
  }

  test("Get role permission template association - success") {
    val userRoleId = 1
    val userId = 1
    val accountId = 1
    val expected = RolePermissionTemplateAssociation( 1, 1, userId, accountId)
    when(service.getRolePermissionTemplateAssociation(userRoleId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/template/$userRoleId",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[RolePermissionTemplateAssociation]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getRolePermissionTemplateAssociation(userRoleId, userId, accountId)
  }

  test("Get  user permission template association - failure") {
    val userRoleId = 10
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdateUserRole)
    when(service.getRolePermissionTemplateAssociation(userRoleId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/template/$userRoleId",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getRolePermissionTemplateAssociation(userRoleId, userId, accountId)
  }

  test("Insert or update role permission template association - success") {
    val userId = 1
    val accountId = 1
    val expected = 1
    val rolePermissionTemplateAssociation = RolePermissionTemplateAssociation( 1, 1, userId, accountId)
    when(service.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)).thenReturn(Future.successful(Right(expected)))
    put("/template",
      body = rolePermissionTemplateAssociation.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)
  }

  test("Insert or update role permission template association - failure") {
    val userId = 1
    val accountId = 1
    val rolePermissionTemplateAssociation = RolePermissionTemplateAssociation( 1, 1, userId, accountId)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpsertRolePermissionTemplateAssociation)
    when(service.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)).thenReturn(Future.successful(Left(expected)))
    put("/template",
      body = rolePermissionTemplateAssociation.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation)
  }

  test("Delete role permission template association - success") {
    val userRoleId = 1
    val userId = 1
    val accountId = 1
    val expected = 1
    when(service.deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    delete(s"/template/$userRoleId",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)
  }

  test("Delete role permission template association - failure") {
    val userRoleId = 10
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToDeleteRolePermissionTemplateAssociation)
    when(service.deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    delete(s"/template/$userRoleId",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteRolePermissionTemplateAssociation(userRoleId, userId, accountId)
  }

  test("get dashboard permissions by role id - success") {
    val userRoleId = 1
    val userId = 1
    val accountId = 1
    val permission = DashboardUserPermissions.USER_ROLES_VIEW
    val expected = Seq(DashboardUserPermissions.toPermissionResult(permission, EnvironmentTypes.GLOBAL_ENVIRONMENT.id))
    when(service.getDashboardPermissionsByRoleId(userRoleId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/$userRoleId/dashboard_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[DashboardUserPermissionResult]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getDashboardPermissionsByRoleId(userRoleId, userId, accountId)
  }

  test("get dashboard permissions by role id - failure") {
    val userRoleId = 1
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissions)
    when(service.getDashboardPermissionsByRoleId(userRoleId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/$userRoleId/dashboard_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getDashboardPermissionsByRoleId(userRoleId, userId, accountId)
  }

  test("get system defined role permissions by role type - success") {
    val userId = 1
    val accountId = 1
    val roleType = 1
    val expected = SystemDefiendRolesPermissions.sysDefinedRoles(Some(1), roleType).flatMap(permissions => permissions._2.map(permissionId => DashboardUserPermissions.toPermissionResult(DashboardUserPermissions.byId(permissionId).get, permissions._1))).toSeq
    when(service.getDashboardPermissionsByRoleTypeID(roleType, userId.toLong, accountId.toLong)).thenReturn(Future.successful(Right(expected)))
    get(s"role_type/$roleType/dashboard_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[DashboardUserPermissionResult]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getDashboardPermissionsByRoleTypeID(roleType, userId.toLong, accountId.toLong)
  }

  test("get system defined role permissions by role type - invalid role type failure") {
    val userId = 1
    val accountId = 1
    val roleType = 5
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToFetchPermissions)
    when(service.getDashboardPermissionsByRoleTypeID(roleType, userId.toLong, accountId.toLong)).thenReturn(Future.successful(Left(expected)))
    get(s"role_type/$roleType/dashboard_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getDashboardPermissionsByRoleTypeID(roleType, userId.toLong, accountId.toLong)
  }

  test("delete role with permissions - success") {
    val userId = 1
    val accountId = 1
    val roleId = 12345
    when(service.deleteUserRoleWithPermissionTemplate(roleId, userId.toLong, accountId.toLong)).thenReturn(Future.successful(Right(true)))
    delete(s"$roleId/delete_with_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).deleteUserRoleWithPermissionTemplate(roleId, userId.toLong, accountId.toLong)
  }

  test("delete role with permission - invalid role id failure") {
    val userId = 1
    val accountId = 1
    val roleId = 0L
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidUserRoleId)
    when(service.deleteUserRoleWithPermissionTemplate(roleId, userId.toLong, accountId.toLong)).thenReturn(Future.successful(Left(expected)))
    delete(s"$roleId/delete_with_permissions",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deleteUserRoleWithPermissionTemplate(roleId, userId.toLong, accountId.toLong)
  }

  test("Get user role by public account id - success") {
    val publicAccountId = "publicAccountId"

    val expected = Seq(UserRoleResult(Some(1), 1, "PrimaryAdmin", Some("Primary Administrator"), 1, Some(DateTime.now)), UserRoleResult(Some(2), 1, "InstanceAdmin", Some("Instance Administrator"), 1, Some(DateTime.now)))
    when(service.getUserRolesByPublicAccountId(publicAccountId)).thenReturn(Future.successful(Right(expected)))
    get("/roles_by_public_account_id",
      params = Map("publicAccountId" -> publicAccountId),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
    verify(service).getUserRolesByPublicAccountId(publicAccountId)
  }

  test("Get user role by public account id - failure") {
    val publicAccountId = "publicAccountId"

    val expected = Seq(UserRoleResult(Some(1), 1, "PrimaryAdmin", Some("Primary Administrator"), 1, Some(DateTime.now)), UserRoleResult(Some(2), 1, "InstanceAdmin", Some("Instance Administrator"), 1, Some(DateTime.now)))
    when(service.getUserRolesByPublicAccountId(publicAccountId)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    get("/roles_by_public_account_id",
      params = Map("publicAccountId" -> publicAccountId),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 400
    }
    verify(service).getUserRolesByPublicAccountId(publicAccountId)
  }

  test("Get total count for users and roles - success") {
    val accountIds = Array[Long](1, 2, 3, 4, 5)
    val expected = 10

    when(service.getUsersAndRolesTotalRecordCount(accountIds, None)).thenReturn(Future.successful(Right(expected)))
    get("/get_users_and_roles_for_accounts/total_count",
      params = Map("account_ids" -> accountIds.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUsersAndRolesTotalRecordCount(accountIds, None)
  }

  test("Get total count for users and roles - with empty account ids - failure") {
    val accountIds = Array[Long]()
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)

    when(service.getUsersAndRolesTotalRecordCount(accountIds, None)).thenReturn(Future.successful(Left(expected)))
    get("/get_users_and_roles_for_accounts/total_count",
      params = Map("account_ids" -> ","),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getUsersAndRolesTotalRecordCount(accountIds, None)
  }

  test("Get total count for users and roles - without account ids - failure") {
    get("/get_users_and_roles_for_accounts/total_count",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      body should include("account_ids not provided")
    }
  }

  test("Get users and roles - success") {
    val accountIds = Array[Long](1, 2, 3, 4, 5)
    val expected = Seq(
      UsersAndRoles(111, 112, 113, "name1", "Test", "User", "<EMAIL>", "+1-**********", "role11, role12", 1),
      UsersAndRoles(211, 212, 213, "name2", "Test", "User", "<EMAIL>", "+1-**********", "role21, role22", 1)
    )

    when(service.getUsersAndRoles(accountIds, None, None, None)).thenReturn(Future.successful(Right(expected)))
    get("/get_users_and_roles_for_accounts",
      params = Map("account_ids" -> accountIds.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[UsersAndRoles]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getUsersAndRoles(accountIds, None, None, None)
  }

  test("Get users and roles - with empty account ids - failure") {
    val accountIds = Array[Long]()
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)

    when(service.getUsersAndRoles(accountIds, None, None, None)).thenReturn(Future.successful(Left(expected)))
    get("/get_users_and_roles_for_accounts",
      params = Map("account_ids" -> ","),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getUsersAndRoles(accountIds, None, None, None)
  }

  test("Get users and roles - without account ids - failure") {
    get("/get_users_and_roles_for_accounts",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      body should include("account_ids not provided")
    }
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
