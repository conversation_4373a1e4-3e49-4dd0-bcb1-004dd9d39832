package me.socure.account.servlet.dashbordv2

import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.dashboardv2.ModuleManagementServlet
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model.{ErrorResponse, ModulesResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class ModuleManagementServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter{
  implicit val ec : ExecutionContext = ExecutionContext.global

  val accountService: DashboardAccountServiceV2 = mock[DashboardAccountServiceV2]
  val hmacVerifier : HMACHttpVerifier = mock[HMACHttpVerifier]
  val moduleManagementServlet: ModuleManagementServlet = new ModuleManagementServlet(accountService, hmacVerifier)

  addServlet(moduleManagementServlet, "/*")

  before{
    reset(accountService)
  }

  test("Get default modules") {
    val accountId = 1
    val ts = new DateTime(0L, DateTimeZone.UTC)
    val creator = Creator(userId = 1, accountId = 14)
    val expected = Set(77, 93, 99)
    when(accountService.getDefaultModules(accountId, creator)).thenReturn(Future.successful(Right(expected)))
    get(s"/default/$accountId",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Set[Int]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).getDefaultModules(accountId, creator)
  }

  test("Get default modules for accounts") {
    val accountId = 1
    val ts = new DateTime(0L, DateTimeZone.UTC)
    val creator = Creator(userId = 1, accountId = 14)
    val expected = ModulesResponse(Set(77, 93, 99), Some(accountId), Some(false))
    when(accountService.getDefaultModulesForAccounts(accountId, Some(creator),true)).thenReturn(Future.successful(Right(expected)))
    get(s"/accounts/default/$accountId",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ModulesResponse]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).getDefaultModulesForAccounts(accountId, Some(creator),true)
  }

  test("Get default modules - failure") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(accountService.getDefaultModules(accountId, creator)).thenReturn(Future.successful(Left(expected)))
    get(s"/default/$accountId",
      params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).getDefaultModules(accountId, creator)
  }

  test("Get modules") {
    val accountId = 1
    val creatorAccountId = 14
    val expected = Set("rff-vk5ImDnyZ9")
    when(accountService.getAccountModules(accountId, creatorAccountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/$accountId",
      params = Map("creator_account_id" -> creatorAccountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Set[String]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(accountService).getAccountModules(accountId, creatorAccountId)
  }

  test("Get modules - failure") {
    val accountId = 1
    val creatorAccountId = 14
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(accountService.getAccountModules(accountId, creatorAccountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/$accountId",
      params = Map("creator_account_id" -> creatorAccountId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).getAccountModules(accountId, creatorAccountId)
  }

  test("save default modules") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val modules = Set(1,2,3)
    val params = Map("modules" -> "1,2,3", "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    when(accountService.saveDefaultModules(accountId, modules, creator)) thenReturn Future.successful(auditDetails, Right(true))
    post(s"/default/$accountId",
      params,
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[(AuditDetails, Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, true)))
    }
    verify(accountService).saveDefaultModules(accountId, modules, creator)
  }

  test("save default modules should fail for invalid modules") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val modules = Set(1,2,3)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidModules)
    val params = Map("modules" -> "1,2,3", "creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(expected), Seq.empty)
    when(accountService.saveDefaultModules(accountId, modules, creator)) thenReturn Future.successful(auditDetails, Left(expected))
    post(s"/default/$accountId",
      params,
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[(AuditDetails, ErrorResponse)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, expected)))
    }
    verify(accountService).saveDefaultModules(accountId, modules, creator)
  }

  test("delete default modules") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)
    when(accountService.clearDefaultModules(accountId, creator)) thenReturn Future.successful(Right(true))
    delete(s"/default/$accountId",
      params,
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(accountService).clearDefaultModules(accountId, creator)
  }

  test("delete default modules should fail for invalid modules") {
    val accountId = 1
    val creator = Creator(userId = 1, accountId = 1)
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    val params = Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)
    when(accountService.clearDefaultModules(accountId, creator)) thenReturn Future.successful(Left(expected))
    delete(s"/default/$accountId",
      params,
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(accountService).clearDefaultModules(accountId, creator)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
