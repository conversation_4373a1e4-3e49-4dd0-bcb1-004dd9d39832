package me.socure.account.servlet


import me.socure.account.service.RateLimitingService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.convertors.AccountConvertors
import me.socure.model._
import me.socure.model.account._
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}
import me.socure.storage.slick.tables.account.DtoRateLimit
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class RateLimitingServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: RateLimitingService = mock[RateLimitingService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: RateLimitingServlet = new RateLimitingServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  val timeNow = new DateTime("2020-12-06", DateTimeZone.UTC)
  val rateLimitDto = DtoRateLimit(1, 97L, 1L, "/test",
    1000, 20, timeNow, timeNow, "superadmin", "superadmin")
  val rateLimitingConfig: RateLimitingConfig = AccountConvertors.toRateLimitingConfig(rateLimitDto)
  val rateLimitingEntry: RateLimitingEntry = AccountConvertors.toRateLimitEntry(rateLimitDto, Map(97L -> "Socure"))

  val headers  = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  test("Save rate limits - success") {
    val saveRateLimitingInput = SaveRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "test",
      windowInMillis = 1000,
      limit = 1,
      createdBy = "test"
    )
    when(service.saveRateLimits(saveRateLimitingInput)).thenReturn(Future.successful(Right(true)))
    post("/",
      body = Serialization.write(saveRateLimitingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).saveRateLimits(saveRateLimitingInput)
  }

  test("Save rate limits - failure") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val saveRateLimitingInput = SaveRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "test",
      windowInMillis = 100,
      limit = 1,
      createdBy = "test"
    )
    when(service.saveRateLimits(saveRateLimitingInput)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(saveRateLimitingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).saveRateLimits(saveRateLimitingInput)
  }

  test("Update rate limits - success") {
    val updateRateLimitingInput = UpdateRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "test",
      windowInMillis = 1000,
      limit = 1,
      updatedBy = "test"
    )
    when(service.updateRateLimits(updateRateLimitingInput)).thenReturn(Future.successful(Right(true)))
    put("/",
      body = Serialization.write(updateRateLimitingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
    verify(service).updateRateLimits(updateRateLimitingInput)
  }

  test("Update rate limits - failure") {
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val updateRateLimitingInput = UpdateRateLimitingInput(
      accountId = 1,
      environmentTypeId = 1,
      api = "test",
      windowInMillis = 100,
      limit = 1,
      updatedBy = "test"
    )
    when(service.updateRateLimits(updateRateLimitingInput)).thenReturn(Future.successful(Left(expected)))
    put("/",
      body = Serialization.write(updateRateLimitingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updateRateLimits(updateRateLimitingInput)
  }

  test("Get Rate limiting entries - success") {
    when(service.getRateLimits).thenReturn(Future.successful(Right(Seq(rateLimitingEntry))))
    get("/" ,  headers = headers) {
      validate[Response[Seq[RateLimitingEntry]]](status, 200, body, Response(ResponseStatus.Ok, Seq(rateLimitingEntry)))
    }
    verify(service).getRateLimits
  }

  test("Get Rate limiting policies - success") {
    when(service.getRateLimits(97 , 1, "test")).thenReturn(Future.successful(Right(Seq(rateLimitingConfig))))
    get("/policies" , Map("accountId" -> "97" , "environmentType" -> "1", "apiId" -> "test"),  headers) {
        validate[Response[Seq[RateLimitingConfig]]](status, 200, body, Response(ResponseStatus.Ok, Seq(rateLimitingConfig)))
    }
    verify(service).getRateLimits(97, 1, "test")
  }

  test("Get Rate limiting policies- empty") {
    when(service.getRateLimits(97 , 2, "test")).thenReturn(Future.successful(Right(Seq.empty)))
    get("/policies" , Map("accountId" -> "97" , "environmentType" -> "2", "apiId" -> "test"),  headers) {
      validate[Response[Seq[RateLimitingConfig]]](status, 200, body, Response(ResponseStatus.Ok, Seq()))
    }
    verify(service).getRateLimits(97, 2, "test")
  }

  test("Get Rate limiting policies_v2 - success") {
    when(service.getRateLimits(97 , 1, Set("test1", "test2"))).thenReturn(Future.successful(Right(Map("test1" -> Seq(rateLimitingConfig)))))
    get("/policies_v2" , Map("accountId" -> "97" , "environmentType" -> "1", "apiIds" -> "test1,test2"),  headers) {
      validate[Response[Map[String, Seq[RateLimitingConfig]]]](status, 200, body, Response(ResponseStatus.Ok, Map("test1" -> Seq(rateLimitingConfig))))
    }
    verify(service).getRateLimits(97, 1, Set("test1", "test2"))
  }

  test("Get Rate limiting policies_v2 - empty") {
    when(service.getRateLimits(97 , 2, Set("test1", "test2"))).thenReturn(Future.successful(Right(Map.empty[String, Seq[RateLimitingConfig]])))
    get("/policies_v2" , Map("accountId" -> "97" , "environmentType" -> "2", "apiIds" -> "test1,test2"),  headers) {
      validate[Response[Map[String, Seq[RateLimitingConfig]]]](status, 200, body, Response(ResponseStatus.Ok, Map.empty[String, Seq[RateLimitingConfig]]))
    }
    verify(service).getRateLimits(97, 2, Set("test1", "test2"))
  }

  test("Get Rate limiting apis - success") {
    when(service.getRateLimitingApis).thenReturn(Future.successful(RateLimiterPublicAPI.getAll))
    get("/apis" ,  headers = headers) {
      status shouldBe 200
    }
    verify(service).getRateLimitingApis
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
