package me.socure.account.servlet

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.{AccountPgpKeysService, PgpDecAndSigKeysService, PgpSignaturePublicKeyService}
import me.socure.model.account.AccountDetails
import me.socure.model.pgp.{PgpDecAndSigKeys, PgpPublicKey}
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, when}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/22/17.
  */
class PGPServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar{

  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val accountPgpKeysService = mock[AccountPgpKeysService]
  private val pgpPublicKeyService = mock[PgpSignaturePublicKeyService]
  private val pgpDecAndSigKeysService = mock[PgpDecAndSigKeysService]

  private val servlet = new PGPServlet(accountPgpKeysService, pgpPublicKeyService, pgpDecAndSigKeysService = pgpDecAndSigKeysService)

  addServlet(servlet, "/*")

  before {
    reset(accountPgpKeysService, pgpPublicKeyService, pgpDecAndSigKeysService)
  }

  test("create_pgp_keys should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(accountPgpKeysService.createPgpKeys(1L, None)) thenReturn Future.successful(Right(true))
    post("/create_pgp_keys", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("create_pgp_keys should return PGPKeysAlreadyExists"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.PGPExistsAlready)
    when(accountPgpKeysService.createPgpKeys(1L, None)) thenReturn Future.successful(Left(resCode))
    post("/create_pgp_keys", Map("account_id" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_active_pgp_account_list should return list of active accounts with pgp keys"){
    lazy val list = List(AccountPgpInfo(1, "acc-1234", "TestAccount", new DateTime("2017-12-06", DateTimeZone.UTC)))
    val res = Response(ResponseStatus.Ok, list)
    when(accountPgpKeysService.getActivePgpAccountList) thenReturn Future.successful(Right(list))
    get("/get_active_pgp_account_list") {
      validate[Response[List[AccountPgpInfo]]](status, 200, body, res)
    }
  }

  test("get_active_account_wo_pgp_list should return list of active accounts without pgp keys"){
    lazy val list = List(AccountDetails(1, "TestAccount", "<EMAIL>"))
    val res = Response(ResponseStatus.Ok, list)
    when(accountPgpKeysService.getActivePgpAccountWOPgpList()) thenReturn Future.successful(Right(list))
    get("/get_active_account_wo_pgp_list") {
      validate[Response[List[AccountDetails]]](status, 200, body, res)
    }
  }

  test("get_public_key should return public key"){
    val expected = "publickey"
    val res = Response(ResponseStatus.Ok, data = expected)
    when(accountPgpKeysService.getAccountPgpPublicKey(1L)) thenReturn Future.successful(Right(expected))
    get("/get_public_key/1") {
      validate[Response[String]](status, 200, body, res)
    }
  }

  test("does_pgp_key_exists should return true when PGP key exists"){
    val expected = true
    val res = Response(ResponseStatus.Ok, data = expected)
    when(accountPgpKeysService.doesPgpKeyExists(1L)) thenReturn Future.successful(Right(expected))
    get("/does_pgp_key_exists/1") {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("does_pgp_key_exists should return a 404 code when PGP key does not exist"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)
    when(accountPgpKeysService.doesPgpKeyExists(1L)) thenReturn Future.successful(Left(resCode))
    get("/does_pgp_key_exists/1") {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("deactivate_pgp_keys should return non 0"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(accountPgpKeysService.dactivatePgpKeys(1L)) thenReturn Future.successful(Right(true))
    post("/deactivate_pgp_keys", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("deactivate_pgp_keys should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.DeactivatePGPKeysFailed)
    when(accountPgpKeysService.dactivatePgpKeys(1L)) thenReturn Future.successful(Left(resCode))
    post("/deactivate_pgp_keys", Map("account_id" -> "1")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_accounts_with_signature_public_key should return list of accounts having siganture public key"){
    val list = List(AccountPgpInfo(1, "acc-1234", "AccountName1",new DateTime("2017-12-06", DateTimeZone.UTC)), AccountPgpInfo(4, "acc-1235", "AccountName4",new DateTime("2017-12-06", DateTimeZone.UTC)))
    val res = Response(ResponseStatus.Ok, data = list)
    when(pgpPublicKeyService.getAccountsWithSignaturePublicKeys) thenReturn Future.successful(Right(list))
    get("/get_accounts_with_signature_public_key") {
      validate[Response[List[AccountPgpInfo]]](status, 200, body, res)
    }
  }

  test("insert_pgp_signature_public_key should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(pgpPublicKeyService.insertPgpSignaturePublicKey(11L, "signature1")) thenReturn Future.successful(Left(resCode))
    post("/insert_pgp_signature_public_key", Map("account_id" -> "11", "publicKey" -> "signature1")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("insert_pgp_signature_public_key should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(pgpPublicKeyService.insertPgpSignaturePublicKey(1L, "signature1")) thenReturn Future.successful(Right(true))
    post("/insert_pgp_signature_public_key", Map("account_id" -> "1", "publicKey" -> "signature1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  test("get_pgp_signature_public_key should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.NoPGPSignaturePublicKeyFound)
    when(pgpPublicKeyService.getPgpSignaturePublicKey(11L)) thenReturn Future.successful(Left(resCode))
    get("/get_pgp_signature_public_key", Map("account_id" -> "11")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_pgp_signature_public_key should return publicKey"){
    val res = Response(ResponseStatus.Ok, data = PgpPublicKey("publicKey"))
    when(pgpPublicKeyService.getPgpSignaturePublicKey(1L)) thenReturn Future.successful(Right(PgpPublicKey("publicKey")))
    get("/get_pgp_signature_public_key", Map("account_id" -> "1")) {
      validate[Response[PgpPublicKey]](status, 200, body, res)
    }
  }

  test("get_pgp_dec_and_sig_keys should return both keys"){
    val res = Response(ResponseStatus.Ok, data = PgpDecAndSigKeys(decryptionPrivateKey = "privateKey", signatureVerificationPublicKey = Some("publicKey")))
    when(pgpDecAndSigKeysService.getPgpDecAndSigKeys(1L)) thenReturn Future.successful(Right(res.data))
    get("/get_dec_and_sig_keys/1") {
      validate[Response[PgpDecAndSigKeys]](status, 200, body, res)
    }
  }

  test("get_pgp_dec_and_sig_keys should return only decryption key"){
    val res = Response(ResponseStatus.Ok, data = PgpDecAndSigKeys(decryptionPrivateKey = "privateKey", signatureVerificationPublicKey = None))
    when(pgpDecAndSigKeysService.getPgpDecAndSigKeys(1L)) thenReturn Future.successful(Right(res.data))
    get("/get_dec_and_sig_keys/1") {
      validate[Response[PgpDecAndSigKeys]](status, 200, body, res)
    }
  }

  test("get_pgp_dec_and_sig_keys should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.NoPGPSignaturePublicKeyFound)
    when(pgpDecAndSigKeysService.getPgpDecAndSigKeys(1L)) thenReturn Future.successful(Left(resCode))
    get("/get_dec_and_sig_keys/1") {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("get_all_dec_and_sig_keys should return both keys"){
    val res = Response(ResponseStatus.Ok, data = Seq(PgpDecAndSigKeys(decryptionPrivateKey = "privateKey", signatureVerificationPublicKey = None)))
    when(pgpDecAndSigKeysService.getAllPgpDecAndSigKeys(1L)) thenReturn Future.successful(Right(res.data))
    get("/get_all_dec_and_sig_keys/1") {
      validate[Response[Seq[PgpDecAndSigKeys]]](status, 200, body, res)
    }
  }

  test("get_all_dec_and_sig_keys should should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(pgpDecAndSigKeysService.getAllPgpDecAndSigKeys(1L)) thenReturn Future.successful(Left(resCode))
    get("/get_all_dec_and_sig_keys/1") {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("delete_pgp_signature_public_key should return Error code"){
    val resCode = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(pgpPublicKeyService.deletePgpPublicKey(11L)) thenReturn Future.successful(Left(resCode))
    post("/delete_pgp_signature_public_key", Map("account_id" -> "11")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(resCode))
    }
  }

  test("delete_pgp_signature_public_key should return true"){
    val res = Response(ResponseStatus.Ok, data = true)
    when(pgpPublicKeyService.deletePgpPublicKey(1L)) thenReturn Future.successful(Right(true))
    post("/delete_pgp_signature_public_key", Map("account_id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, res)
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def constructErrorResponse(code : ErrorResponse) : Response[ErrorResponse] = {
    Response(ResponseStatus.Error, code)
  }

}
