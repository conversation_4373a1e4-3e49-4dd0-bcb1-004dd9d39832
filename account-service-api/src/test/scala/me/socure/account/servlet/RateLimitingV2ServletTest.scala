package me.socure.account.servlet

import me.socure.account.service.RateLimitingService
import me.socure.convertors.AccountConvertors
import me.socure.model._
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}
import me.socure.storage.slick.tables.account.DtoRateLimit
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
class RateLimitingV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec = ExecutionContext.global

  val host : String = "http://localhost"
  val service: RateLimitingService = mock[RateLimitingService]
  val servlet: RateLimitingV2Servlet = new RateLimitingV2Servlet(service)

  addServlet(servlet, "/*")

  before {
    reset(service)
  }

  val timeNow = new DateTime("2020-12-06", DateTimeZone.UTC)
  val rateLimitDto = DtoRateLimit(1, 97L, 1L, "/test",
  1000, 20, timeNow, timeNow, "superadmin", "superadmin")
  val rateLimitingConfig: RateLimitingConfig = AccountConvertors.toRateLimitingConfig(rateLimitDto)
  val rateLimitingEntry: RateLimitingEntry = AccountConvertors.toRateLimitEntry(rateLimitDto, Map(97L -> "Socure"))


  val headers  = Map(
    "Content-Type" -> "application-json; charset=UTF-8"
  )

  test("Get Rate limiting policies_v2 - success") {
    when(service.getRateLimits(97 , 1, Set("test1", "test2"))).thenReturn(Future.successful(Right(Map("test1" -> Seq(rateLimitingConfig)))))
    get("/policies_v2" , Map("accountId" -> "97" , "environmentType" -> "1", "apiIds" -> "test1,test2"),  headers) {
      validate[Response[Map[String, Seq[RateLimitingConfig]]]](status, 200, body, Response(ResponseStatus.Ok, Map("test1" -> Seq(rateLimitingConfig))))
    }
    verify(service).getRateLimits(97, 1, Set("test1", "test2"))
  }

  test("Get Rate limiting policies_v2 - empty") {
    when(service.getRateLimits(97 , 2, Set("test1", "test2"))).thenReturn(Future.successful(Right(Map.empty[String, Seq[RateLimitingConfig]])))
    get("/policies_v2" , Map("accountId" -> "97" , "environmentType" -> "2", "apiIds" -> "test1,test2"),  headers) {
      validate[Response[Map[String, Seq[RateLimitingConfig]]]](status, 200, body, Response(ResponseStatus.Ok, Map.empty[String, Seq[RateLimitingConfig]]))
    }
    verify(service).getRateLimits(97, 2, Set("test1", "test2"))
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
