package me.socure.account.servlet.account.sponsorbank

import org.joda.time.{DateTime, DateTimeZone}
import me.socure.account.service.common.exceptions.ExceptionCodes.{UnableToLinkSponsorBankProgram, UnableToUnLinkSponsorBankProgram}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.account.AccountIdName
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}
import org.scalatest.BeforeAndAfter
import org.scalatest.mockito.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, verify, when}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

class SponsorBankServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: SponsorBankService = mock[SponsorBankService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: SponsorBankServlet = new SponsorBankServlet(service, hmacVerifier)
  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get All non linked Programs - success") {
    val expected = Seq(AccountIdName(2, "Program1"))
    when(service.getNonSponsorBankPrograms()).thenReturn(Future.successful(Right(expected)))
    get(s"/non-linked",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[AccountIdName]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getNonSponsorBankPrograms()
  }

  test("Get Sponsor Bank Linked Programs - success") {
    val sponsorBankId = 1
    val expected = Seq(SponsorBankProgram(2, "Program1", "<EMAIL>", clock.now()))
    when(service.getLinkedPrograms(sponsorBankId)).thenReturn(Future.successful(Right(expected)))
    get(s"/linked/programs/$sponsorBankId",
    headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
    )) {
      validate[Response[Seq[SponsorBankProgram]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getLinkedPrograms(sponsorBankId)
  }

  test("Get Sponsor Bank Linked Programs - fail") {
    val sponsorBankId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.getLinkedPrograms(sponsorBankId)).thenReturn(Future.successful(Left(expected)))
    get(s"/linked/programs/$sponsorBankId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getLinkedPrograms(sponsorBankId)
  }

  test("Get Sponsor Bank for Programs - success") {
    val programId = 1
    val expected = AccountIdName(1, "AccountName1")
    when(service.getSponsorBank(programId)).thenReturn(Future.successful(Right(expected)))
    get(s"/program/$programId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountIdName]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getSponsorBank(programId)
  }

  test("Get Sponsor Bank for Programs - fail") {
    val programId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.getSponsorBank(programId)).thenReturn(Future.successful(Left(expected)))
    get(s"/program/$programId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getSponsorBank(programId)
  }

  test("Link Program - success") {
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(3, 1, "<EMAIL>")
    val expected = true
    when(service.linkSponsorBankProgram(sponsorBankProgramLinkRequest)).thenReturn(Future.successful(Right(expected)))
    post("/link",
      body = Serialization.write(sponsorBankProgramLinkRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).linkSponsorBankProgram(sponsorBankProgramLinkRequest)
  }

  test("Link Program - fail") {
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(3, 1, "<EMAIL>")
    val expected = ErrorResponseFactory.get(UnableToLinkSponsorBankProgram)
    when(service.linkSponsorBankProgram(sponsorBankProgramLinkRequest)).thenReturn(Future.successful(Left(expected)))
    post("/link",
      body = Serialization.write(sponsorBankProgramLinkRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).linkSponsorBankProgram(sponsorBankProgramLinkRequest)
  }

  test("UnLink Program - success") {
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(3, 1, "<EMAIL>")
    val expected = true
    when(service.unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)).thenReturn(Future.successful(Right(expected)))
    post("/unlink",
      body = Serialization.write(sponsorBankProgramLinkRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)
  }

  test("UnLink Program - fail") {
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(3, 1, "<EMAIL>")
    val expected = ErrorResponseFactory.get(UnableToUnLinkSponsorBankProgram)
    when(service.unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)).thenReturn(Future.successful(Left(expected)))
    post("/unlink",
      body = Serialization.write(sponsorBankProgramLinkRequest),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
