package me.socure.account.servlet

import me.socure.account.service.DashboardDomainService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.dashboardv2.DashboardDomainServlet
import me.socure.model.account.AccountDashboardDomain
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, UserDetails}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Matchers.anyInt
import org.mockito.Mockito.{reset, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

/**
  * Created by gopal on 11/04/2017.
  */
class DashboardDomainServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec = ExecutionContext.global

  val service = mock[DashboardDomainService]
  val servlet = new DashboardDomainServlet(service)

  before {
    reset(service)
  }

  addServlet(servlet, "/*")

  test("update dashboard domain should return true") {
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val resp: (AuditDetails, Either[ErrorResponse, Boolean]) = (auditDetails, Right(true))
    when(service.upsertDashboardDomain(1, List("domain1"))) thenReturn Future.successful(resp)
    post("/update", Map("account_id" -> "1", "domains" -> "domain1")) {
      validate[Response[(AuditDetails, Either[ErrorResponse, Boolean])]](status, 200, body, Response(ResponseStatus.Ok, resp))
    }
  }

  test("update dashboard domain should return left") {
    val error = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    val resp: (AuditDetails, Either[ErrorResponse, Boolean]) = (auditDetails, Left(error))
    when(service.upsertDashboardDomain(1, List("domain1"))) thenReturn Future.successful(resp)
    post("/update", Map("account_id" -> "1", "domains" -> "domain1")) {
      validate[Response[(AuditDetails, Either[ErrorResponse, Boolean])]](status, 200, body, Response(ResponseStatus.Ok, resp))
    }
  }

  /**
    * Get whitelisted dashboard domain for the account
    */

  test("should return whitelisted domain for account") {
    when(service.getWhitelistedDomainForAccount(1)) thenReturn Future.successful(Right("domain.com"))
    get("/get/1") {
      validate[Response[String]](status, 200, body, Response(ResponseStatus.Ok, "domain.com"))
    }
  }

  test("should records not found when there is not records") {
    when(service.getWhitelistedDomainForAccount(1)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)))
    get("/get/1") {
      status should equal(400)
      body should include(ExceptionCodes.RecordsNotFound.id.toString)
    }
  }

  /**
    * Get whitelisted dashboard domain for the account by email
    */

  test("should return whitelisted domain for account by emails") {
    when(service.getWhitelistedDomainForAccountByEmail("<EMAIL>")) thenReturn Future.successful(Right("domain.com"))
    get("/get_by_email/<EMAIL>") {
      validate[Response[String]](status, 200, body, Response(ResponseStatus.Ok, "domain.com"))
    }
  }

  test("should records not found when there is not records by email") {
    when(service.getWhitelistedDomainForAccountByEmail("<EMAIL>")) thenReturn Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    get("/get_by_email/<EMAIL>") {
      status should equal(400)
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("should return whitelisted domain with permission for account by emails") {
    when(service.getWhitelistedDomainPermissionForAccountByEmail("<EMAIL>")) thenReturn Future.successful(Right(AccountDashboardDomain(1, true, Some("domain"))))
    get("/get_permission_domain_by_email/<EMAIL>") {
      validate[Response[AccountDashboardDomain]](status, 200, body, Response(ResponseStatus.Ok, AccountDashboardDomain(1, true, Some("domain"))))
    }
  }

  test("should return associated accounts whitelisted domain with permission for account by emails") {
    when(service.getAssociatedAccountDashboardDomainListByEmailId("<EMAIL>", 1)) thenReturn Future.successful(Right(Seq(AccountDashboardDomain(1, true, Some("domain")))))
    post("/get_associated_account_dashboard_domain_list_by_email", Map("email" -> "<EMAIL>")) {
      validate[Response[Seq[AccountDashboardDomain]]](status, 200, body, Response(ResponseStatus.Ok, Seq(AccountDashboardDomain(1, true, Some("domain")))))
    }
  }

  test("should return whitelisted domain with permission for account by id") {
    val accountId = Random.nextLong()
    when(service.getWhitelistedDomainPermissionForAccountById(id = accountId)) thenReturn Future.successful(Right(AccountDashboardDomain(accountId, true, Some("domain"))))
    get(s"/get_permission_domain_by_account_id/$accountId") {
      validate[Response[AccountDashboardDomain]](status, 200, body, Response(ResponseStatus.Ok, AccountDashboardDomain(accountId, true, Some("domain"))))
    }
  }



  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
