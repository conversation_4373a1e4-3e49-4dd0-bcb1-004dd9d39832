package me.socure.account.servlet


import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.{BusinessUserService, PasswordService}
import me.socure.convertors.LegacyModelConverters
import me.socure.model.user.authorization.{UserAuth, UserAuthLegacy, UserAuthV2, UserStatus}
import me.socure.model.user.{BusinessUser, BusinessUserInfo, BusinessUserWithRoles, PasswordlessLoginCredential, PrimaryAccountUser, PrimaryAccountUserLegacy, PromoteUserResponse, UserMagicToken}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.storage.slick.tables.user.{DaoBusinessUser, DtoBusinessUser}
import me.socure.user.fixure.UserFixture
import me.socure.user.servelt.UserManagementServlet
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.mockito.Mockito
import org.mockito.Mockito.{verify, when}
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import java.util.UUID
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
  * Created by gopal on 13/05/16.
  */
class UserManagementServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  val userService: BusinessUserService = mock[BusinessUserService]
  val passwordService: PasswordService = mock[PasswordService]
  val daoBusinessUser: DaoBusinessUser = mock[DaoBusinessUser]
  val servlet = new UserManagementServlet(userService, passwordService)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(userService, passwordService)
  }

  test("validate user should return user not found") {
    val expected = ErrorResponse(UserNotFound.id, UserNotFound.description)
    Mockito.when(userService.validateUser(UserFixture.userCredential)).thenReturn(Future.successful(Left(expected)))
    post("/validate", UserFixture.userCredential.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400,body, Response(ResponseStatus.Error, expected))
    }
  }

  test("validate user should return UsernamePasswordMismatch") {

    val expected = ErrorResponse(UsernamePasswordMismatch.id, UsernamePasswordMismatch.description)
    Mockito.when(userService.validateUser(UserFixture.userCredential)).thenReturn(Future.successful(Left(expected)))
    post("/validate", UserFixture.userCredential.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("validate user should return business user - legacy") {
    val expectedbu = UserFixture.userAuth
    val expectedres = Response(ResponseStatus.Ok,LegacyModelConverters.toUserAuthLegacy(UserFixture.userAuth))

    Mockito.when(userService.validateUser(UserFixture.userCredential)).thenReturn(Future.successful(Right(expectedbu)))
    post("/validate", UserFixture.userCredential.encodeJson()) {
      validate[Response[UserAuthLegacy]](status, 200, body, expectedres)
    }
  }

  test("validate user should return business user") {
    val expectedbu = UserFixture.userAuth
    val expectedres = Response(ResponseStatus.Ok,expectedbu)

    Mockito.when(userService.validateUser(UserFixture.userCredential)).thenReturn(Future.successful(Right(expectedbu)))
    post("/validate_v2", UserFixture.userCredential.encodeJson()) {
      validate[Response[UserAuth]](status, 200, body, expectedres)
    }
  }

  test("validate V3  user should return business user") {
    val expectedbu = UserFixture.userAuthV2
    val expectedres = Response(ResponseStatus.Ok,expectedbu)

    Mockito.when(userService.validateUserV2(UserFixture.userCredential)).thenReturn(Future.successful(Right(expectedbu)))
    post("/v3/validate", UserFixture.userCredential.encodeJson()) {
      validate[Response[UserAuthV2]](status, 200, body, expectedres)
    }
  }

  //Register

  test("register user should throw RegistrationFailed") {
    val expected = ErrorResponse(RegistrationFailed.id, RegistrationFailed.description)
    Mockito.when(userService.register(UserFixture.userForm, false)) thenReturn Future.successful(Left(expected))

    post("/register", UserFixture.userForm.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }

  }

  test("register user should return Registration successful") {

    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.register(UserFixture.validUserForm, false)) thenReturn Future.successful(Right(true))
    post("/register", UserFixture.validUserForm.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }

  }

  test("register user with isActive true should return Registration successful") {

    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.register(UserFixture.validUserForm, true)) thenReturn Future.successful(Right(true))
    post(s"/register?isActive=true", UserFixture.validUserForm.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }

  }

  test("register should fail when password policy does not meet") {
    val expected = ErrorResponseFactory.get(PasswordContainsPersonalInfo)

    post("/register", UserFixture.staticUserForm.copy(password = "userform").encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("v2/register user should return Registration successful") {
    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.registerV2(UserFixture.validUserFormV2, isActive = false, isDashboardV3 = false)) thenReturn Future.successful(Right(true))
    post("/v2/register", UserFixture.validUserFormV2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("v2/register user with isActive true should return Registration successful") {
    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.registerV2(UserFixture.validUserFormV2, isActive = true, isDashboardV3 = false)) thenReturn Future.successful(Right(true))
    post("/v2/register?isActive=true", UserFixture.validUserFormV2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("v2/register user with isDashboardV3 true should return Registration successful") {
    val expected = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.registerV2(UserFixture.validUserFormV2, isActive = true, isDashboardV3 = true)) thenReturn Future.successful(Right(true))
    post("/v2/register?isActive=true&isDashboardV3=true", UserFixture.validUserFormV2.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("v2/register user should throw RegistrationFailed") {
    val expected = ErrorResponse(InvalidInputFormat.id, InvalidInputFormat.description)
    Mockito.when(userService.registerV2(UserFixture.userFormV2.copy(accountType = 7), isActive = false, isDashboardV3 = false)) thenReturn Future.successful(Left(expected))
    post("/v2/register", UserFixture.userForm.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  //Activating User By ActivationCode

  test("validate activation code should return invalidactivationcode") {
    val activationCode = "adfdilfjdlfjr"
    val error = ErrorResponse(InvalidActivationCode.id,InvalidActivationCode.description)
    Mockito.when(userService.activateUserByActivationcode(activationCode)) thenReturn Future.successful(Left(error))
    get(s"/activate_user/$activationCode") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, error))
    }
  }

  test("validate activation code should return valid true") {

    val activationCode = "349384aljfkldjfldj"
    val sucessmsg = Response[Boolean](ResponseStatus.Ok,true)
    Mockito.when(userService.activateUserByActivationcode(activationCode)) thenReturn Future.successful(Right(true))
    get(s"/activate_user/$activationCode") {
      validate[Response[Boolean]](status, 200,body, sucessmsg)
    }
  }

  test("change password should return password changed successfully") {

    val data = true
    val passwordsuccess = Response[Boolean](ResponseStatus.Ok,data)
    Mockito.when(passwordService.setPassword(UserFixture.passwordChangeForm)) thenReturn Future.successful(Right(data))
    post("/change_password",UserFixture.passwordChangeForm.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, passwordsuccess)
    }
  }

  test("change passsword should return Usernotfound") {
    val error = ErrorResponse(UserNotFound.id,UserNotFound.description)
    Mockito.when(passwordService.setPassword(UserFixture.passwordChangeForm)) thenReturn Future.successful(Left(error))
    post("/change_password",UserFixture.passwordChangeForm.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400,body, Response(ResponseStatus.Error, error))
    }
  }

  test("active users page should return users list") {
    val users = UserFixture.primaryUsersL
    Mockito.when(userService.getActivesPrimaryAccountAdmins(None, None, "")) thenReturn Future.successful(UserFixture.primaryUsersV)
    get("/active_users_v2") {
      validate[Response[List[PrimaryAccountUser]]](status, 200, body, Response[List[PrimaryAccountUser]](ResponseStatus.Ok, users))
    }
  }

  test("active users page should throw error") {
    val expected = ErrorResponse(199, "something went wrong")
    Mockito.when(userService.getActivesPrimaryAccountAdmins(None, None, "")) thenReturn Future.failed(new Exception("something went wrong"))
    get("/active_users_v2") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("active users count should return users list") {
    Mockito.when(userService.getActivesPrimaryAccountAdminsCount) thenReturn Future.successful(10)
    get("/active_users_count") {
      validate[Response[Int]](status, 200, body, Response[Int](ResponseStatus.Ok, 10))
    }
  }

  test("active users count should throw error") {
    val expected = ErrorResponse(199, "something went wrong")
    Mockito.when(userService.getActivesPrimaryAccountAdminsCount) thenReturn Future.failed(new Exception("something went wrong"))
    get("/active_users_count") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("resetting password by resetcode should be successful") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    Mockito.when(passwordService.setPasswordWithResetCode("validcode", "some_password")) thenReturn Future.successful(Right(true))
    post("/reset_password_by_resetcode", Map("resetcode" -> "validcode", "password" -> "some_password")) {
      validate[Response[Boolean]](status, 200, body, expectedRes)
    }
  }

  test("resetting password by resetcode status should be 400") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    Mockito.when(passwordService.setPasswordWithResetCode("invalid_validcode", "some_password")) thenReturn Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    post("/reset_password_by_resetcode", Map("resetcode" -> "invalid_validcode", "password" -> "some_password")) {
      validate[Response[ErrorResponse]](status, 400,body, expectedRes)
    }
  }

  test("setting password by activationcode should be successful") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    Mockito.when(passwordService.setPasswordWithActivationCode("validcode", "some_password")) thenReturn Future.successful(Right(true))
    post("/set_password_by_activationcode", Map("activationcode" -> "validcode", "password" -> "some_password")) {
      validate[Response[Boolean]](status, 200, body, expectedRes)
    }
  }

  test("setting password by activationcode status should be 400") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    Mockito.when(passwordService.setPasswordWithActivationCode("invalid_validcode", "some_password")) thenReturn Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    post("/set_password_by_activationcode", Map("activationcode" -> "invalid_validcode", "password" -> "some_password")) {
      validate[Response[ErrorResponse]](status, 400,body, expectedRes)
    }
  }

  test("get user by activationcode should be successful") {
    val expectedRes = Response(ResponseStatus.Ok, UserFixture.businessUser)
    Mockito.when(userService.getUserByActivationCode("validcode")) thenReturn Future.successful(Right(UserFixture.businessUser))
    get("/get_user_by_activationcode/validcode") {
      validate[Response[BusinessUser]](status, 200, body, expectedRes)
    }
  }

  test("get user by activationcode status should be 400") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    Mockito.when(userService.getUserByActivationCode("invalid_code")) thenReturn Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    get("/get_user_by_activationcode/invalid_code") {
      validate[Response[ErrorResponse]](status, 400,body, expectedRes)
    }
  }

  test("get_user_by_resetcode should be successful when a valid code is passed") {
    val expectedRes = Response(ResponseStatus.Ok, UserFixture.businessUser)
    Mockito.when(userService.getUserByResetCode("validcode")) thenReturn Future.successful(Right(UserFixture.businessUser))
    get("/get_user_by_resetcode/validcode") {
      validate[Response[BusinessUser]](status, 200, body, expectedRes)
    }
  }

  test("get_user_by_resetcode should return a 400 when an invalid code is passed") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    Mockito.when(userService.getUserByResetCode("invalid_code")) thenReturn Future.successful(Left(ErrorResponseFactory.get(InvalidActivationCode)))
    get("/get_user_by_resetcode/invalid_code") {
      validate[Response[ErrorResponse]](status, 400, body, expectedRes)
    }
  }

  test("log_bad_login_try should return properly on success") {
    val expectedRes = Response(ResponseStatus.Ok, UserStatus(locked = false, badLoginCount = 1))
    Mockito.when(userService.logBadLoginAttempt("<EMAIL>", "error message")) thenReturn Future.successful(Some(UserStatus(locked = false, badLoginCount = 1)))
    post("/log_bad_login_try", Map("email" -> "<EMAIL>", "errormsg" -> "error message")) {
      validate[Response[UserStatus]](status, 200,body, expectedRes)
    }
  }

  test("log_bad_login_try should return properly on failure") {
    val exception = new Exception("error")
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(exception))
    Mockito.when(userService.logBadLoginAttempt("<EMAIL>", "error message")) thenReturn Future.failed(exception)
    post("/log_bad_login_try", Map("email" -> "<EMAIL>", "errormsg" -> "error message")) {
      validate[Response[ErrorResponse]](status, 400,body, expectedRes)
    }
  }

  test("unlock_automatically should return properly on success") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    Mockito.when(userService.unlockIfNeeded("<EMAIL>")) thenReturn Future.successful(true)
    post("/unlock_automatically", Map("email" -> "<EMAIL>")) {
      validate[Response[Boolean]](status, 200,body, expectedRes)
    }
  }

  test("unlock_automatically should return properly on failure") {
    val exception = new Exception("error")
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(exception))
    Mockito.when(userService.unlockIfNeeded("<EMAIL>")) thenReturn Future.failed(exception)
    post("/unlock_automatically", Map("email" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400,body, expectedRes)
    }
  }

  test("get Account Id by user should fail with No Account") {
    val exception = new Exception("error")
    val expectedRes = Future.successful(Left(ErrorResponse(UserNotFound.id, UserNotFound.description)))
    Mockito.when(userService.getAccountIdByUsername("<EMAIL>")) thenReturn expectedRes
    get("/get_account_id/<EMAIL>") {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(UserNotFound.id, UserNotFound.description)))
    }
  }

  test("get Account Id by user should return '100 as account Id'") {
    val exception = new Exception("error")
    val expectedRes = Future.successful(Right(100L))
    Mockito.when(userService.getAccountIdByUsername("<EMAIL>")) thenReturn expectedRes
    get("/get_account_id/<EMAIL>") {
      validate[Response[Long]](status, 200,body, Response(status = ResponseStatus.Ok, data=100L))
    }
  }

  test("activate users should return true") {
    val users = List("e1", "e2")
    Mockito.when(userService.activateUsers(users)) thenReturn Future.successful(Right(true))
    post("/activate_users", users.encodeJson()) {
      validate[Response[Boolean]](status, 200,body, Response(status = ResponseStatus.Ok, data=true))
    }
  }

  test("activate users should return false") {
    val users = List("e1", "e2")
    Mockito.when(userService.activateUsers(users)) thenReturn Future.successful(Left(ErrorResponse(UserNotActivated.id, UserNotActivated.description)))
    post("/activate_users", users.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(UserNotActivated.id, UserNotActivated.description)))
    }
  }

  /**
    * Is user locked endpoint
    */
  test("should return true when user is locked") {
    Mockito.when(userService.isUserLocked("<EMAIL>")) thenReturn Future.successful(Right(true))
    get("/is_user_locked/<EMAIL>") {
      validate[Response[Boolean]](status, 200,body, Response(status = ResponseStatus.Ok, data=true))
    }
  }

  test("should return false when user is not locked") {
    Mockito.when(userService.isUserLocked("<EMAIL>")) thenReturn Future.successful(Right(false))
    get("/is_user_locked/<EMAIL>") {
      validate[Response[Boolean]](status, 200,body, Response(status = ResponseStatus.Ok, data=false))
    }
  }

  /*
   * Is User Internal Endpoint
   */
  test("is_user_internal endpoint should return true when user is linked to internal account") {
    Mockito.when(userService.isUserInternal("<EMAIL>")) thenReturn Future.successful(Right(true))
    get("/is_user_internal/<EMAIL>") {
      validate[Response[Boolean]](status, 200, body, Response(status = ResponseStatus.Ok, data = true))
    }
  }

  test("is_user_internal endpoint should return false when user is not linked to an internal account") {
    Mockito.when(userService.isUserInternal("<EMAIL>")) thenReturn Future.successful(Right(false))
    get("/is_user_internal/<EMAIL>") {
      validate[Response[Boolean]](status, 200, body, Response(status = ResponseStatus.Ok, data = false))
    }
  }

  test("getUsersWithRoleForAccount should return active users list") {
    val users = List(
      BusinessUserWithRoles(4,"gopal","haris","<EMAIL>","**********",false,Vector("Users", "Accounts"),Vector(("Production",Set("Overview", "Settings", "Create Transcation")), ("Development",Set("List Transaction", "Overview")))))
    when(userService.getBusinessUsersWithRoleForAccount(1L)) thenReturn Future.successful(Right(users))
    get("/get_users_with_roles/1") {
      validate[Response[List[BusinessUserWithRoles]]](status, 200,body, Response(status = ResponseStatus.Ok, data=users))
    }
  }

  test("getUsersWithRoleForAccount should return error for invalid account") {
    when(userService.getBusinessUsersWithRoleForAccount(100L)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    get("/get_users_with_roles/100") {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    }
  }

  test("Return exception if invalid user id found") {
    when(daoBusinessUser.getUser(100)).thenReturn(Future.successful(None))
    when(userService.promotePrimaryUser(100)) thenReturn Future.successful(Left(ExceptionCodes.UserNotFound))
    put("/100/promote/primary") {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(UserNotFound.id, UserNotFound.description)))
    }
  }

  test("Return exception if valid user id found and not primary") {

    val user = DtoBusinessUser(100, "<EMAIL>", "gopal", "haris", None,
      "**********", None, None, DateTime.now(), false, None, None, 12, true)
    when(userService.promotePrimaryUser(100)) thenReturn Future.successful(Left(ExceptionCodes.UserNotFound))

    put("/100/promote/primary") {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(UserNotFound.id, UserNotFound.description)))
    }
  }

  test("Return exception if primary user not found") {
    when(userService.promotePrimaryUser(100)) thenReturn Future.successful(Left(ExceptionCodes.UserNotFound))

    put("/100/promote/primary") {
      validate[Response[ErrorResponse]](status, 400,body, Response(status = ResponseStatus.Error, data=ErrorResponse(UserNotFound.id, UserNotFound.description)))
    }
  }

  test("Update should be done successfully for positive inputs") {

    val time = DateTime.now()
    val user = DtoBusinessUser(100, "<EMAIL>", "gopal", "haris", None,
      "**********", None, None, time, false, None, None, 12, false)
    val primaryUser = DtoBusinessUser(101, "<EMAIL>", "gopal", "haris", None,
      "**********", None, None, time, false, None, None, 12, true)

    when(userService.promotePrimaryUser(100)) thenReturn(Future.successful(Right(primaryUser)))

    put("/100/promote/primary") {
      val exceptedResponse: Response[PromoteUserResponse] = Response(status = ResponseStatus.Ok, data = PromoteUserResponse(101, 100))
      validate[Response[PromoteUserResponse]](status, 200, body, exceptedResponse)
    }
  }

  test("does email exist - success") {
    val email = "<EMAIL>"
    val expected: Boolean = true
    when(userService.doesUserExist(email)).thenReturn(Future.successful(Right(expected)))
    post("/does_email_exist", params = Map("email" -> email)) {
      validate[Response[Boolean]](status, 200, body, Response(status = ResponseStatus.Ok, data = expected))
    }
    verify(userService).doesUserExist(email)
  }

  test("does email exist - failure") {
    val email = "<EMAIL>"
    val expected: Boolean = false
    when(userService.doesUserExist(email)).thenReturn(Future.successful(Right(expected)))
    post("/does_email_exist", params = Map("email" -> email)) {
      validate[Response[Boolean]](status, 200, body, Response(status = ResponseStatus.Ok, data = expected))
    }
    verify(userService).doesUserExist(email)
  }

  test("does email exist - error") {
    val email = "<EMAIL>"
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(userService.doesUserExist(email)).thenReturn(Future.successful(Left(expected)))
    post("/does_email_exist", params = Map("email" -> email)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(status = ResponseStatus.Error, data = expected))
    }
    verify(userService).doesUserExist(email)
  }

  test("business user endpoint - success") {
    val user = UserFixture.businessUserInfo
    when(userService.getBusinessUserInfo(1)) thenReturn Future.successful(Right(user))

    get("/get_business_user_info?businessUserId=1") {
      validate[Response[BusinessUserInfo]](status, 200, body, Response(status = ResponseStatus.Ok, data = user))
    }
  }
  test("business user endpoint - not found") {
    when(userService.getBusinessUserInfo(1)) thenReturn Future.successful(Left(ErrorResponseFactory.get(BusinessUserNotFound)))

    get("/get_business_user_info?businessUserId=1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(status = ResponseStatus.Error, data = ErrorResponse(BusinessUserNotFound.id, BusinessUserNotFound.description)))
    }
  }


  test("generate_document_token with valid email and user_agent returns a valid UserMagicToken") {
    val email = "<EMAIL>"
    val userAgent = "Mozilla/5.0"
    val token = UserMagicToken("John", "Doe", email, "magic-token-123", "device-id-456", DateTime.now)
    when(userService.checkAndGenerateDocumentLinkToken(email, userAgent))
      .thenReturn(Future.successful(Right(token)))
    post("/generate_document_token", Map("email" -> email, "user_agent" -> userAgent)) {
      status should equal(200)
    }
  }

  test("POST /generate_document_token without email returns error") {
    val userAgent = "Mozilla/5.0"
    // No email parameter provided
    post("/generate_document_token", Map("user_agent" -> userAgent)) {
      status should equal(400)
      body should include("Missing required parameters")
    }
  }

  test("generate_document_token without user_agent returns error") {
    val email = "<EMAIL>"
    post("/generate_document_token", Map("email" -> email)) {
      // Assuming a missing parameter returns a 400 error
      status should equal(400)
      body should include("Missing required parameters")
    }
  }

  test("docs passwordless login should return user auth v2 on success") {
    val passwordlessLoginCredential = PasswordlessLoginCredential(email = "<EMAIL>", magicToken = UUID.randomUUID().toString, deviceidentifier = UUID.randomUUID().toString, userAgent = "Mozilla/5.0")
    val expectedRes = Response(ResponseStatus.Ok, UserFixture.userAuth)
    when(userService.validateEmailForDocsPasswordlessLogin(passwordlessLoginCredential)) thenReturn Future.successful(Right(UserFixture.userAuth))  //Future.successful(Right(UserFixture.userAuthV2))
    post("/docs/passwordless/validate_v2", passwordlessLoginCredential.encodeJson()) {
      validate[Response[UserAuth]](status, 200, body, expectedRes)
    }
  }

  test("docs passwordless login should return error on missing parameters") {
      post("/docs/passwordless/validate_v2", Map().encodeJson()) {
      status should equal(400)
      body should include("Invalid input format exception")
    }
  }

  test("validate inclusion list - success") {
    val email: String = UserFixture.inclusionListEmail
    when(userService.isDomainFromInclusionList(email,5)) thenReturn Future.successful(Right(true))

    get(s"/validate_inclusion_list?email=$email&accountType=${UserFixture.prospectAccountType}") {
      validate[Response[Boolean]](status, 200, body, Response(status = ResponseStatus.Ok, data = true))
    }
  }
  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }



}
