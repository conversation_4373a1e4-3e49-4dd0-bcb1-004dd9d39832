package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.superadmin.ActiveUsersService
import me.socure.model.account.{DeletedAccountDetails, AccountDomain}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.superadmin.ActiveUsersServlet
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 5/30/16.
  */
class ActiveUserServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter{
  implicit val ec = ExecutionContext.global

  val activeUserService = mock[ActiveUsersService]
  val service = new ActiveUsersServlet(activeUserService)

  addServlet(service, "/*")

  val positiveResponse = Response[Int](ResponseStatus.Ok, data = 1)
  val negativeResponse = ErrorResponse(AccountNotFound.id, AccountNotFound.description)
  val internalError = ErrorResponseFactory.get(InternalError)

  before{
    reset(activeUserService)
  }

  test("mark as internal with email ids"){
    when(activeUserService.markAsInternal(List("<EMAIL>"))) thenReturn Future.successful(Right(1))
    post("/mark_internal", Map("emails" -> "<EMAIL>")){
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("mark as internal with email ids throws 400 error"){
    when(activeUserService.markAsInternal(List("<EMAIL>"))) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/mark_internal", Map("emails" -> "<EMAIL>")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("mark as internal with email ids results in - Invalid input"){
    post("/mark_internal", Map.empty){
      validate[Response[ErrorResponse]](status, 500, body, Response(ResponseStatus.Error, internalError))
    }
  }

  test("unmark as internal with email ids"){
    when(activeUserService.unmarkAsInternal(List("<EMAIL>"))) thenReturn Future.successful(Right(1))
    post("/unmark_internal", Map("emails" -> "<EMAIL>")){
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("unmark as internal with email ids throws - 400 error"){
    when(activeUserService.unmarkAsInternal(List("<EMAIL>"))) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/unmark_internal", Map("emails" -> "<EMAIL>")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("mark/internal: should mark account as internal"){
    when(activeUserService.markAccountAsInternal(1L)) thenReturn Future.successful(Right(1))
    post("/mark/internal", Map("account_id" -> "1")){
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("mark/internal: should return 400 error"){
    when(activeUserService.markAccountAsInternal(100L)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/mark/internal", Map("account_id" -> "100")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("unmark/internal: should unamrk an account as internal"){
    when(activeUserService.unmarkAccountAsInternal(1L)) thenReturn Future.successful(Right(1))
    post("/unmark/internal", Map("account_id" -> "1")){
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("unmark/internal: unmark as internal 400 error"){
    when(activeUserService.unmarkAccountAsInternal(100L)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/unmark/internal", Map("account_id" -> "100")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("deactive should return account not found"){
    when(activeUserService.deactivateUser(List("<EMAIL>"))) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/deactivate", Map("emails" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("should deactive given users"){
    when(activeUserService.deactivateUser(List("<EMAIL>"))) thenReturn Future.successful(Right(1))
    post("/deactivate", Map("emails" -> "<EMAIL>")) {
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("deactivate_account: should return account not found"){
    when(activeUserService.deactivateAccount(100L)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/deactivate_account", Map("account_id" -> "100")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("deactivate_account: should deactive the account"){
    when(activeUserService.deactivateAccount(1L)) thenReturn Future.successful(Right(1))
    post("/deactivate_account", Map("account_id" -> "1")) {
      validate[Response[Int]](status, 200, body, positiveResponse)
    }
  }

  test("get domain list should return account not found"){
    when(activeUserService.getDomainByAccountId(555)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    get("/get_domains/555") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("should get domain list given users"){
    val expectedRes = Response(ResponseStatus.Ok, "domain_list")
    when(activeUserService.getDomainByAccountId(2)) thenReturn Future.successful(Right("domain_list"))
    get("/get_domains/2") {
      validate[Response[String]](status, 200, body, expectedRes)
    }
  }

  test("add domain list should return account not found"){
    when(activeUserService.addDomainByAccountId(555, List("some"))) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/add_domains", AccountDomain(555, List("some")).encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("should add domain list to users"){
    val expectedRes = Response(ResponseStatus.Ok, true)
    when(activeUserService.addDomainByAccountId(2, List("some"))) thenReturn Future.successful(Right(true))
    post("/add_domains", AccountDomain(2, List("some")).encodeJson()) {
      validate[Response[Boolean]](status, 200, body, expectedRes)
    }
  }

  test("delete account"){
    when(activeUserService.deleteAccount(1)) thenReturn Future.successful(Right(true))
    post("/delete_account", Map("account_id" -> "1")){
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, data = true))
    }
  }

  test("delete account should return 400 error"){
    when(activeUserService.deleteAccount(1)) thenReturn Future.successful(Left(ErrorResponse(AccountNotFound.id, AccountNotFound.description)))
    post("/delete_account", Map("account_id" -> "1")){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, negativeResponse))
    }
  }

  test("deleted accounts list"){
    val list = Vector(DeletedAccountDetails(1, "Name", None, "apiKey1", false), DeletedAccountDetails(1, "Account", Some("Parent"), "apiKey2", true))
    when(activeUserService.getDeletedAccountsList) thenReturn Future.successful(list)
    get("/deleted_accounts_list"){
      validate[Response[Vector[DeletedAccountDetails]]](status, 200, body, Response(ResponseStatus.Ok, data = list))
    }
  }

  test("deleted accounts list when no record is found"){
    when(activeUserService.getDeletedAccountsList) thenReturn Future.successful(Vector.empty)
    get("/deleted_accounts_list"){
      validate[Response[Vector[DeletedAccountDetails]]](status, 200, body, Response(ResponseStatus.Ok, data = Vector.empty))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
