package me.socure.account.servlet.dashbordv2

import me.socure.account.service.SubscriptionService
import me.socure.model.account.{SubscriptionUpdateInput, SubscriptionsProvisionValueGenerator}
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.dashboardv2.SubscriptionServlet
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.storage.slick.tables.account.DtoSubscriptionTypeValueGenerator
import org.mockito.Mockito.reset
import org.mockito.{Matchers, Mockito}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class SubscriptionServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val subscriptionService : SubscriptionService= mock[SubscriptionService]
  val hmacVerifier : HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet = new SubscriptionServlet(subscriptionService, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(subscriptionService)
    reset(hmacVerifier)
  }

  val subscriptionUpdateInputSuspend = SubscriptionUpdateInput(1,Set(1,2,3),"suspend")
  val subscriptionUpdateInputDelete = SubscriptionUpdateInput(1,Set(1,2,3),"delete")

  test("Subscription type endpoint must return all subscription types") {
    val dtoSubscriptionType = DtoSubscriptionTypeValueGenerator.aDtoSubscriptionType()
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.when(subscriptionService.listSubscriptionTypes).thenReturn(Future.successful(Right(Seq(dtoSubscriptionType))))
    get("/event/subscription_types",
        headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
  }

  test("Subscriptions need to be suspended if the action is suspend") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.when(subscriptionService.updateSubscriptionStatus(1, Set(1, 2, 3), 2)).thenReturn(Future.successful(Right("Subscription Status updated Successfully")))
    put("/events/subscriptions",
      subscriptionUpdateInputSuspend.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
  }

  test("Subscriptions need to be deleted if the action is delete") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    Mockito.when(subscriptionService.updateSubscriptionStatus(1, Set(1, 2, 3), 4)).thenReturn(Future.successful(Right("Subscription Status updated Successfully")))
    put("/events/subscriptions",
      subscriptionUpdateInputDelete.encodeJson(),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 200
    }
  }

  test("Should list all Subscriptions for an account ") {
    val subscribtions = Seq(1L,2L)
    val expected = Response(ResponseStatus.Ok, subscribtions)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]));
    Mockito.when(subscriptionService.listSubscriptions(1L)).thenReturn(Future.successful(Right(subscribtions)))
    get("/account/1/subscriptions",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[Long]]](status, 200, body, expected)
    }
  }

  test("Subscribe account for a valid subscription type") {
    val expected = Response(ResponseStatus.Ok, true)
    val accountId = 1L
    val subscriptionTypeId = 100L
    val operation = "subscribe"
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]));
    Mockito.when(subscriptionService.subscribe(accountId, subscriptionTypeId)).thenReturn(Future.successful(Right(true)))
    post(s"/account/${accountId}/subscriptions/${subscriptionTypeId}/${operation}",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
        validate[Response[Boolean]](status, 200, body, expected)
    }
  }

  test("Fail: Subscribe account for an invalid subscription type") {
    val expected = ErrorResponse(UnknownError.id, UnknownError.description)
    val accountId = 11L
    val subscriptionTypeId = 100L
    val operation = "subscribe"
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]));
    Mockito.when(subscriptionService.subscribe(accountId, subscriptionTypeId)).thenReturn(Future.successful(Left(expected)))
    post(s"/account/${accountId}/subscriptions/${subscriptionTypeId}/${operation}",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("Subscription type with provision endpoint must return all subscription types with provision details") {
    val accountId = 10
    val subscriptionsProvision = SubscriptionsProvisionValueGenerator.aSubscriptionsProvision()
    val headers = Map(
      "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
      "Content-Type" -> "application-json; charset=UTF-8",
      "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
    )
    val params = Map(
      "account_id" -> accountId.toString
    )
    Mockito.when(subscriptionService.listSubscriptionTypesWithProvision(accountId)).thenReturn(Future.successful(Right(Seq(subscriptionsProvision))))
    get("/event/subscription_types_with_provision", params, headers) {
      status shouldBe 200
    }
  }

  test("Subscription type with provision endpoint without account_id must return 400") {
    val accountId = 10
    val subscriptionsProvision = SubscriptionsProvisionValueGenerator.aSubscriptionsProvision()
    Mockito.when(subscriptionService.listSubscriptionTypesWithProvision(accountId)).thenReturn(Future.successful(Right(Seq(subscriptionsProvision))))
    get("/event/subscription_types_with_provision",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      status shouldBe 400
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
