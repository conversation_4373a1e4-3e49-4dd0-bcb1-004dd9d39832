package me.socure.account.servlet

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model.account.{AccountInfoV2WithPermission, AccountInfoWithPermission, ActiveAccountApiKey, ApiKeyInfo, ApiKeyStatus}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.storage.slick.tables.account.DtoApiKey
import me.socure.superadmin.AccountInfoServletV2
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.json4s.jackson.Serialization
import org.mockito.Mockito.{reset, when}
import org.mockito.{Match<PERSON>, Mockito}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountInfoServletV2Test extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {

  implicit val ec : ExecutionContext = ExecutionContext.global

  val accountInfoService : AccountInfoService = mock[AccountInfoService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]

  val servlet: AccountInfoServletV2 = new AccountInfoServletV2(accountInfoService, hmacVerifier)

  addServlet(servlet, "/*")

  before{
    reset(accountInfoService)
    reset(hmacVerifier)
  }
//
//  test("Should throw 401 if we call without HMAC") {
//    get("/accounts/123") {
//      status shouldBe 401
//    }
//  }

  test("Should throw error for invalid account id") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountInfoService.getAccountInfoWithPermission(123)) thenReturn Future.successful(None)
    get(
      uri = "/accounts/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponse(ExceptionCodes.AccountNotFound.id, ExceptionCodes.AccountNotFound.description)))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Should return value for valid account id") {
    val permission: AccountInfoWithPermission = AccountInfoWithPermission(123, "acc-l1lIghjkkl","Test Socure Account", true, Set.empty)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountInfoService.getAccountInfoWithPermission(123)) thenReturn Future.successful(Some(permission))
    get(
      uri = "/accounts/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountInfoWithPermission]](status, 200, body, Response(ResponseStatus.Ok, permission))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Validate api key fetch endpoint using public key") {
    val mockValue = DtoApiKey(1, 2, "xxx", None, None, ApiKeyStatus.ACTIVE, DateTime.now(), DateTime.now())
    when(accountInfoService.retrieveApiKeyByPublicKey("123")) thenReturn Future.successful(Some(mockValue))
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    get (
      uri = "/accounts/apikeys/public/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      val mockResponse = ApiKeyInfo(1, ApiKeyStatus.ACTIVE, "xxx")
      validate[Response[ApiKeyInfo]](status, 200, body, Response(ResponseStatus.Ok, mockResponse))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  }

  test("Validate api key fetch endpoint using public key with no value") {
    when(accountInfoService.retrieveApiKeyByPublicKey("123")) thenReturn Future.successful(None)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    get (
      uri = "/accounts/apikeys/public/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      status shouldBe 200
      body shouldBe "{\"status\":\"ok\"}"
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  }

  test("Validate account info based on environment specific public key with invalid key") {
    when(accountInfoService.retrieveAccountInfoByPublicKey("123")) thenReturn Future.successful(None)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    get (
      uri = "/accounts/key/public/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      status shouldBe 200
      body shouldBe "{\"status\":\"ok\"}"
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Validate account info based on environment specific public key with valid key") {
    val accountInfoV2: AccountInfoV2WithPermission = AccountInfoV2WithPermission(
      id = 123,
      publicId = "acc-l1lIghjkkl",
      name = "Test Socure Account",
      isInternal = true,
      parentId = Some(250),
      deleted = false,
      active = true,
      roles = Set.empty
    )
    when(accountInfoService.retrieveAccountInfoByPublicKey("123")) thenReturn Future.successful(Some(accountInfoV2))
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    get (
      uri = "/accounts/key/public/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      status shouldBe 200
      validate[Response[AccountInfoV2WithPermission]](status, 200, body, Response(ResponseStatus.Ok, accountInfoV2))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }


  test("Validate api key fetch endpoint using private key with no value") {
    when(accountInfoService.retrieveAccountInfoByPrivateKey("a039619a-xxx-473a-a228-xxxxxxx")) thenReturn Future.successful(None)
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    post(
      uri = "/accounts/key/private",
      Serialization.write(Map("privateKey" -> "a039619a-xxx-473a-a228-xxxxxxx")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      status shouldBe 200
      body shouldBe "{\"status\":\"ok\"}"
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Validate account info based on private key") {
    val accountInfoV2: AccountInfoWithPermission = AccountInfoWithPermission(
      id = 123,
      publicId = "acc-l1lIghjkkl",
      name = "Test Socure Account",
      isInternal = true,
      roles = Set.empty
    )
    when(accountInfoService.retrieveAccountInfoByPrivateKey("a039619a-xxx-473a-a228-xxxxxxx")) thenReturn Future.successful(Some(accountInfoV2))
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    post(
      uri = "/accounts/key/private",
      Serialization.write(Map("privateKey" -> "a039619a-xxx-473a-a228-xxxxxxx")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )
    ) {
      status shouldBe 200
      validate[Response[AccountInfoWithPermission]](status, 200, body, Response(ResponseStatus.Ok, accountInfoV2))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Should throw error for invalid account public id") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    when(accountInfoService.getAccountInfoWithPermission("123")) thenReturn Future.successful(None)
    get(
      uri = "/accounts/publicid/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponse(ExceptionCodes.AccountNotFound.id, ExceptionCodes.AccountNotFound.description)))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }


  test("Should return success response for valid account public id") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    val accountInfoWithPermission = AccountInfoWithPermission(
      id = 124,
      publicId = "test-1",
      name = "Socure-Unit-Test",
      isInternal = true,
      roles = Set.empty
    )
    when(accountInfoService.getAccountInfoWithPermission("123")) thenReturn Future.successful(Some(accountInfoWithPermission))
    get(
      uri = "/accounts/publicid/123",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[AccountInfoWithPermission]](status, 200, body, Response(ResponseStatus.Ok, accountInfoWithPermission))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Retrieve active API key for an account based on environment type") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
    val accountId = 1L
    val envId = 2L
    val activeApiKey: ActiveAccountApiKey = ActiveAccountApiKey(
      apiKey = "active-api-key",
      environmentId = envId,
      id = 3L
    )

    when(accountInfoService.getActiveApiKeyForAccountByEnvironmentType(accountId, envId))
      .thenReturn(Future.successful(Right(activeApiKey)))

    get(
      uri = "/apikeys/active",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      ),
      params = Map(
        "accountId" -> accountId.toString,
        "envTypeId" -> envId.toString
      )
    ) {
      status shouldBe 200
      validate[Response[ActiveAccountApiKey]](status, 200, body, Response(ResponseStatus.Ok, activeApiKey))
    }

    Mockito.verify(accountInfoService).getActiveApiKeyForAccountByEnvironmentType(accountId, envId)
  }

  test("No active API key found for the given account and environment type") {
    Mockito
      .doNothing()
      .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

    val accountId = 1L
    val envId = 2L

    when(accountInfoService.getActiveApiKeyForAccountByEnvironmentType(accountId, envId))
      .thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.ApiKeyNotFound))))

    get(
      uri = "/apikeys/active",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      ),
      params = Map(
        "accountId" -> accountId.toString,
        "envTypeId" -> envId.toString
      )
    ) {
      status shouldBe 400
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.ApiKeyNotFound)))
    }

    Mockito.verify(accountInfoService).getActiveApiKeyForAccountByEnvironmentType(accountId, envId)
  }


  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
