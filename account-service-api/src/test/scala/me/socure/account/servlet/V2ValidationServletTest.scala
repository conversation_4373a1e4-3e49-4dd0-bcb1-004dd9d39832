package me.socure.account.servlet

import me.socure.account.service.V2ValidationService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.constants.DashboardUserPermissions
import me.socure.model.dashboardv2.Creator
import me.socure.model.{dashboardv2, _}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class V2ValidationServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: V2ValidationService = mock[V2ValidationService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: V2ValidationServlet = new V2ValidationServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Is permission available - success") {
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.ACCOUNTS_CREATE.id)
    val expected = true
    when(service.isPermissionAvailable(accountId, permissions.map(_.toString), dashboardv2.Creator(userId, accountId))).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).isPermissionAvailable(accountId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("Is permission available - failure") {
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.ACCOUNTS_CREATE.id)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    when(service.isPermissionAvailable(accountId, permissions.map(_.toString), Creator(userId, accountId))).thenReturn(Future.successful(Left(expected)))
    get("/",
      params = Map("account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).isPermissionAvailable(accountId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("Is permission available for environment Id - success") {
    val envId = 1
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.SETTINGS_VIEW.id)
    val expected = true
    when(service.isValidEnvironmentPermission(envId, permissions.map(_.toString), dashboardv2.Creator(userId, accountId))).thenReturn(Future.successful(Right(expected)))
    get("/validate/environment/accessById",
      params = Map("env_id" -> envId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).isValidEnvironmentPermission(envId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("Is permission available for environment Id - failure") {
    val envId = 1
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.SETTINGS_VIEW.id)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    when(service.isValidEnvironmentPermission(envId, permissions.map(_.toString), Creator(userId, accountId))).thenReturn(Future.successful(Left(expected)))
    get("/validate/environment/accessById",
      params = Map("env_id" -> envId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).isValidEnvironmentPermission(envId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("Is permission available for environment Name - success") {
    val envName = "Certification"
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.SETTINGS_VIEW.id)
    val expected = true
    when(service.isValidEnvironmentPermissionByEnvName(envName, accountId, permissions.map(_.toString), dashboardv2.Creator(userId, accountId))).thenReturn(Future.successful(Right(expected)))
    get("/validate/environment/accessByName",
      params = Map("env_name" -> envName, "account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).isValidEnvironmentPermissionByEnvName(envName, accountId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("Is permission available for environment Name - failure") {
    val envName = "Certification"
    val userId = 1
    val accountId = 1
    val permissions = Set(DashboardUserPermissions.SETTINGS_VIEW.id)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleProvisioning)
    when(service.isValidEnvironmentPermissionByEnvName(envName, accountId, permissions.map(_.toString), Creator(userId, accountId))).thenReturn(Future.successful(Left(expected)))
    get("/validate/environment/accessByName",
      params = Map("env_name" -> envName,"account_id" -> accountId.toString, "creator_user_id" -> userId.toString, "creator_account_id" -> accountId.toString, "permissions" -> permissions.mkString(",")),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).isValidEnvironmentPermissionByEnvName(envName, accountId, permissions.map(_.toString), Creator(userId, accountId))
  }

  test("is valid active user account association - success") {
    val userId = 1
    val accountId = 1
    val expected = true
    when(service.isValidActiveUserAccountAssociation(accountId, userId)).thenReturn(Future.successful(Right(expected)))
    get("/validate/user_account_association/active",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).isValidActiveUserAccountAssociation(accountId, userId)
  }

  test("is valid active user account association - failure") {
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.isValidActiveUserAccountAssociation(accountId, userId)).thenReturn(Future.successful(Left(expected)))
    get("/validate/user_account_association/active",
      params = Map("account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).isValidActiveUserAccountAssociation(accountId, userId)
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
