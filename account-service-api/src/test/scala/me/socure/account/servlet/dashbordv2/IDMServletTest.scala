package me.socure.account.servlet.dashbordv2

import me.socure.account.dashboardv2.IDMService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.dashboardv2.IDMServlet
import me.socure.model._
import me.socure.model.account._
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Matchers
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}
import scala.io.Source


class IDMServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: IDMService = mock[IDMService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: IDMServlet = new IDMServlet(service, hmacVerifier)

  addServlet(servlet, "/*")
  val headers = Map(
    "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
    "Content-Type" -> "application-json; charset=UTF-8",
    "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
  )

  before {
    reset(service, hmacVerifier)
  }

  private val AccountInfoResponse: IdmAccountInfoResponse = Source.fromInputStream(getClass.getResourceAsStream("/responses/IdmAccountInfoSuccessResp.json")).mkString.decodeJson[IdmAccountInfoResponse]

  test("Get IDM key - success") {
    val accountId = 1
    val expected  = Option(ApiKeyInfo(1, ApiKeyStatus.ACTIVE, "test"))
    when(service.fetchApiKey(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/fetch_api_key/$accountId",
      headers = headers) {
      validate[Response[Option[ApiKeyInfo]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).fetchApiKey(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get IDM key - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.IDMKeyFetchFailed)

    when(service.fetchApiKey(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/fetch_api_key/$accountId",
      headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).fetchApiKey(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Generate IDM key - success") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    when(service.generateApiKey(idmApiKey)).thenReturn(Future.successful(Right(expected)))
    post("/generate_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).generateApiKey(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Generate IDM key - failure") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyNotInserted)

    when(service.generateApiKey(idmApiKey)).thenReturn(Future.successful(Left(expected)))
    post("/generate_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).generateApiKey(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Deprecate IDM key - success") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    when(service.deprecateApiKey(idmApiKey)).thenReturn(Future.successful(Right(expected)))
    post("/deprecate_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).deprecateApiKey(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Deprecate IDM key - failure") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyNotInserted)

    when(service.deprecateApiKey(idmApiKey)).thenReturn(Future.successful(Left(expected)))
    post("/deprecate_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deprecateApiKey(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update IDM key - success") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    when(service.updateLabel(idmApiKey)).thenReturn(Future.successful(Right(expected)))
    post("/update_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).updateLabel(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update IDM key - failure") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected = ErrorResponseFactory.get(ExceptionCodes.IDMApiKeyNotInserted)

    when(service.updateLabel(idmApiKey)).thenReturn(Future.successful(Left(expected)))
    post("/update_api_key",
      body = Serialization.write(idmApiKey),
      headers = headers) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updateLabel(idmApiKey)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("should be able to fetch account information by IDM api key") {
    val apiKey = "a16df0a5-9dda-4a58-ab72-83765492802e"
    when(service.fetchIdmAccountInformation(apiKey)).thenReturn(Future.successful(Right(AccountInfoResponse)))
    get(
      uri = s"/account_information/v1/$apiKey",
      headers = headers
    ) {
      validate[Response[IdmAccountInfoResponse]](status, 200, body, Response(ResponseStatus.Ok, AccountInfoResponse))
    }
    verify(service, atLeastOnce).fetchIdmAccountInformation(apiKey)
    verify(hmacVerifier, never).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("should not be able to fetch account information if IDM is not enabled") {
    val apiKey = "a16df0a5-9dda-4a58-ab72-83765492802e"
    val expected = ErrorResponseFactory.get(ExceptionCodes.IDMNotEnabled)
    when(service.fetchIdmAccountInformation(apiKey)).thenReturn(Future.successful(Left(expected)))
    get(
      uri = s"/account_information/v1/$apiKey"
    ) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service, atLeastOnce).fetchIdmAccountInformation(apiKey)
    verify(hmacVerifier, never).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    actualStatus should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
