package me.socure.account.servlet

import me.socure.account.superadmin.LockedUserService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.superadmin.LockedUsers
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.superadmin.LockedUserServlet
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/3/16.
  */
class LockedUserServletTest extends ScalatraFunSuite with BeforeAndAfter with MockitoSugar{
  implicit val ec = ExecutionContext.global

  val lockedService = mock[LockedUserService]
  val servlet = new LockedUserServlet(lockedService)

  addServlet(servlet, "/*")

  before {
    reset(lockedService)
  }

  val lockedUser = LockedUsers("firstname", "surname", "email", "company", "contact", "")

  test("get locked user list") {
    val res = Response(ResponseStatus.Ok, data =  List(lockedUser))
    when(lockedService.getLockedUserList) thenReturn Future.successful(Right(Vector(lockedUser)))
    get("/list") {
      validate[Response[List[LockedUsers]]](status, 200, body, res)
    }
  }

  test("get locked user list 400 exception") {
    val negCode = ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)
    when(lockedService.getLockedUserList) thenReturn Future.successful(Left(negCode))
    get("/list") {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(negCode))
    }
  }

  test("unlock single user 200") {
    val res = Response(ResponseStatus.Ok, data =  1)
    when(lockedService.unlockUser(List("<EMAIL>"))) thenReturn Future.successful(Right(1))
    post("/unlock", Map("emails" -> "<EMAIL>")) {
      validate[Response[Int]](status, 200, body, res)
    }
  }

  test("unlock multiple user 200") {
    val res = Response(ResponseStatus.Ok, data =  2)
    when(lockedService.unlockUser(List("<EMAIL>", "<EMAIL>"))) thenReturn Future.successful(Right(2))
    post("/unlock", Map("emails" -> "<EMAIL>,<EMAIL>")) {
      validate[Response[Int]](status, 200, body, res)
    }
  }

  test("unlock user 400") {
    val negRes = ErrorResponse(ExceptionCodes.BusinessUserNotFound.id, ExceptionCodes.BusinessUserNotFound.description)
    when(lockedService.unlockUser(List("<EMAIL>"))) thenReturn Future.successful(Left(negRes))
    post("/unlock", Map("emails" -> "<EMAIL>")) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(negRes))
    }
  }

  test("Invalid input") {
    val negRes = ErrorResponseFactory.get(ExceptionCodes.MissingRequiredParameters)
    post("/unlock", Map.empty) {
      validate[Response[ErrorResponse]](status, 400, body, constructErrorResponse(negRes))
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

  private def constructErrorResponse(code : ErrorResponse) : Response[ErrorResponse] = {
    Response(ResponseStatus.Error, code)
  }

}
