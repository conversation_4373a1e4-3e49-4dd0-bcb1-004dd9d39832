package me.socure.account.servlet

import me.socure.account.service.PermissionTemplateService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model._
import me.socure.model.account.{PermissionTemplateMapping, PermissionTemplateMappingInput}
import me.socure.model.dashboardv2.Creator
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class PermissionTemplateServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: PermissionTemplateService = mock[PermissionTemplateService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: PermissionTemplateServlet = new PermissionTemplateServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get  permission template mapping by template id - success") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    val permissionTemplateMapping = PermissionTemplateMapping(1, 1, 1, Set(1, 2, 3, 4), None)
    val expected = Seq(permissionTemplateMapping)
    when(service.getPermissionTemplateMappingsByTemplateId(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get("/mappings",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[PermissionTemplateMapping]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getPermissionTemplateMappingsByTemplateId(permissionTemplateId, userId, accountId)
  }

  test("Get  permission template mapping by template id - empty") {
    val permissionTemplateId = 1000
    val userId = 1
    val accountId = 1
    val expected = Seq.empty
    when(service.getPermissionTemplateMappingsByTemplateId(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get("/mappings",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Seq[PermissionTemplateMapping]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getPermissionTemplateMappingsByTemplateId(permissionTemplateId, userId, accountId)
  }

  test("Insert permission template mapping - success") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 2, Set(1, 2, 3), None)
    when(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Right(1)))
    post("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).insertPermissionTemplateMapping(permissionTemplateMappingInput)
  }

  test("Insert permission template mapping - failure") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 1, Set.empty, None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToInsertPermissionTemplateMapping)
    when(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Left(expected)))
    post("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertPermissionTemplateMapping(permissionTemplateMappingInput)
  }

  test("Insert permission template mapping - invalid environment type") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 4, Set.empty, None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidEnvironmentTypeProvided)
    when(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Left(expected)))
    post("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertPermissionTemplateMapping(permissionTemplateMappingInput)
  }

  test("Insert permission template mapping - invalid permission id") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(None, Creator(1,1), 1, 2, Set(1000), None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.InvalidPermissionIdProvided)
    when(service.insertPermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Left(expected)))
    post("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertPermissionTemplateMapping(permissionTemplateMappingInput)
  }

  test("Update permission template mapping - success") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(Some(1), Creator(1,1), 1, 1, Set(1, 2, 3), None)
    when(service.updatePermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Right(1)))
    put("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).updatePermissionTemplateMapping(permissionTemplateMappingInput)
  }

  test("Update permission template mapping - failure") {
    val permissionTemplateMappingInput = PermissionTemplateMappingInput(Some(10), Creator(1,1), 1, 1, Set.empty, None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToUpdatePermissionTemplateMapping)
    when(service.updatePermissionTemplateMapping(permissionTemplateMappingInput)).thenReturn(Future.successful(Left(expected)))
    put("/mappings",
      body = Serialization.write(permissionTemplateMappingInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updatePermissionTemplateMapping(permissionTemplateMappingInput)
  }

    test("Delete permission template mapping - success") {
      val permissionTemplateId = 1
      val environmentTypeId = 1
      val userId = 1
      val accountId = 1
      when(service.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)).thenReturn(Future.successful(Right(1)))
      delete("/mappings",
        params = Map("template_id" -> permissionTemplateId.toString, "environment_type" -> environmentTypeId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
      }
      verify(service).deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)
    }

    test("Delete permission template mapping - failure") {
      val permissionTemplateId = 1
      val environmentTypeId = 1
      val userId = 1
      val accountId = 1
      val expected = ErrorResponseFactory.get(ExceptionCodes.UnableToDeletePermissionTemplateMapping)
      when(service.deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
      delete("/mappings",
        params = Map("template_id" -> permissionTemplateId.toString, "environment_type" -> environmentTypeId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
        headers = Map(
          "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
          "Content-Type" -> "application-json; charset=UTF-8",
          "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
        )) {
        validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
      }
      verify(service).deletePermissionTemplateMapping(permissionTemplateId, environmentTypeId, userId, accountId)
    }

  test("Insert  permission template - success") {
    val permissionTemplate = PermissionTemplate(0, "Template1", 1, 1, 1, None)
    when(service.insertPermissionTemplate(permissionTemplate)).thenReturn(Future.successful(Right(1)))
    post("/",
      body = Serialization.write(permissionTemplate),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).insertPermissionTemplate(permissionTemplate)
  }

  test("Insert  permission template - empty") {
    val permissionTemplate = PermissionTemplate(100, "Template1", 4, 1, 1, None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed)
    when(service.insertPermissionTemplate(permissionTemplate)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(permissionTemplate),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).insertPermissionTemplate(permissionTemplate)
  }

  test("Get  permission template by template id - success") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    val expected = PermissionTemplate(1, "Template1", 1, 1, 1, None)

    when(service.getPermissionTemplate(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Right(expected)))
    get("/",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[PermissionTemplate]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getPermissionTemplate(permissionTemplateId, userId, accountId)
  }

  test("Get  permission template - fail") {
    val permissionTemplateId = 1000
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateCreateFailed)
    when(service.getPermissionTemplate(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    get("/",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getPermissionTemplate(permissionTemplateId, userId, accountId)
  }

  test("Update  permission template - success") {
    val permissionTemplate = PermissionTemplate(1, "Modified", 1, 1, 1, None)
    when(service.updatePermissionTemplate(permissionTemplate)).thenReturn(Future.successful(Right(1)))
    put("/",
      body = Serialization.write(permissionTemplate),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).updatePermissionTemplate(permissionTemplate)
  }

  test("Update  permission template - empty") {
    val permissionTemplate = PermissionTemplate(100, "Modified", 1, 1, 1, None)
    val expected = ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateUpdateFailed)
    when(service.updatePermissionTemplate(permissionTemplate)).thenReturn(Future.successful(Left(expected)))
    put("/",
      body = Serialization.write(permissionTemplate),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).updatePermissionTemplate(permissionTemplate)
  }

  test("Delete permission template - success") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    when(service.deletePermissionTemplate(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Right(1)))
    delete("/",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
    verify(service).deletePermissionTemplate(permissionTemplateId, userId, accountId)
  }

  test("Delete permission template - failure") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.PermissionTemplateDeleteFailed)
    when(service.deletePermissionTemplate(permissionTemplateId, userId, accountId)).thenReturn(Future.successful(Left(expected)))
    delete("/",
      params = Map("template_id" -> permissionTemplateId.toString, "account_id" -> accountId.toString, "user_id" -> userId.toString),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).deletePermissionTemplate(permissionTemplateId, userId, accountId)
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
