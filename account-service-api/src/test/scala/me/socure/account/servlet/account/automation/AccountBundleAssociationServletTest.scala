package me.socure.account.servlet.account.automation

import me.socure.account.automation.AccountBundleAssociationService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import me.socure.common.clock.FakeClock
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.model.account.automation
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.model.account.automation.{AccountBundleAssociation, DtoAccountBundleAssociation}
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, verify, when}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

class AccountBundleAssociationServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: AccountBundleAssociationService = mock[AccountBundleAssociationService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: AccountBundleAssociationServlet = new AccountBundleAssociationServlet(service, hmacVerifier)
  val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)
  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get account bundle association by account id - success") {
    val accountId = 1
    val expected = automation.DtoAccountBundleAssociation(1, 1, "Bundle 1", "<EMAIL>", clock.now, None, None)
    when(service.getAccountBundleAssociation(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[DtoAccountBundleAssociation]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getAccountBundleAssociation(accountId)
  }

  test("Get account bundle association by account id - failure") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.getAccountBundleAssociation(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).getAccountBundleAssociation(accountId)
  }

  test("Update account bundle association by account id - success") {
    val accountBundleAssociation = AccountBundleAssociation(1, "Bundle 1", "<EMAIL>")
    val expected = automation.DtoAccountBundleAssociation(2, 3, "Bundle 1", "<EMAIL>", clock.now, None, None)
    when(service.upsertAccountBundleAssociation(accountBundleAssociation)).thenReturn(Future.successful(Right(expected)))
    post("/",
      body = Serialization.write(accountBundleAssociation),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[DtoAccountBundleAssociation]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).upsertAccountBundleAssociation(accountBundleAssociation)
  }

  test("Update account bundle association by account id - failure") {
    val accountBundleAssociation = AccountBundleAssociation(100, "Bundle 1", "<EMAIL>")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    when(service.upsertAccountBundleAssociation(accountBundleAssociation)).thenReturn(Future.successful(Left(expected)))
    post("/",
      body = Serialization.write(accountBundleAssociation),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).upsertAccountBundleAssociation(accountBundleAssociation)
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
