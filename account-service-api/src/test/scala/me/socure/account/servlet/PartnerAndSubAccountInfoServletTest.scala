package me.socure.account.servlet

import me.socure.account.service.PartnerAndSubAccountInfoService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.constants.{AccountTypes, EnvironmentTypes}
import me.socure.model._
import me.socure.model.account._
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.json4s.jackson.Serialization
import org.mockito.Matchers
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class PartnerAndSubAccountInfoServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec: ExecutionContext = ExecutionContext.global

  val service: PartnerAndSubAccountInfoService = mock[PartnerAndSubAccountInfoService]
  val hmacVerifier: HMACHttpVerifier = mock[HMACHttpVerifier]
  val servlet: PartnerAndSubAccountInfoServlet = new PartnerAndSubAccountInfoServlet(service, hmacVerifier)

  addServlet(servlet, "/*")

  before {
    reset(service, hmacVerifier)
  }

  test("Get migration account details - success") {
    val accountId = 3654
    val expected = MigrationAccountDetails(accountId = accountId, "AccountName1", users = Seq.empty, modules = Seq.empty, subAccounts = Seq.empty)
    when(service.fetchAccountDetails(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/v1/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[MigrationAccountDetails]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).fetchAccountDetails(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get migration account details - failure") {
    val accountId = 3654
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.fetchAccountDetails(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/v1/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).fetchAccountDetails(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("migrate account - success") {
    val expected = true
    val migrationAccountDetailsInput = MigrationAccountDetailsInput(3654, 1, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, None, None, "username")
    when(service.migrateAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Right(expected)))
    post(s"/migrate",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).migrateAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("migrate account - failure") {
    val migrationAccountDetailsInput = MigrationAccountDetailsInput(3654, 1, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.migrateAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Left(expected)))
    post(s"/migrate",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).migrateAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("merge account - success") {
    val expected = true
    val mergeAccountDetailsInput = MergeAccountDetailsInput(3654, 3550, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, "username")
    when(service.mergeAccount(Matchers.any(classOf[MergeAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Right(expected)))
    post(s"/merge",
      body = Serialization.write(mergeAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).mergeAccount(Matchers.any(classOf[MergeAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("merge account - failure") {
    val mergeAccountDetailsInput = MergeAccountDetailsInput(3654, 3550, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, "username")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.mergeAccount(Matchers.any(classOf[MergeAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Left(expected)))
    post(s"/merge",
      body = Serialization.write(mergeAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).mergeAccount(Matchers.any(classOf[MergeAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("migrate sub-account - success") {
    val expected = true
    val migrationAccountDetailsInput = MigrationSubAccountDetailsInput(3654, Seq.empty, associateAllUsersToSubAccount = false, "username")
    when(service.migrateSubAccount(Matchers.any(classOf[MigrationSubAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Right(expected)))
    post(s"/migrate/subaccount",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).migrateSubAccount(Matchers.any(classOf[MigrationSubAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("migrate sub-account - failure") {
    val migrationAccountDetailsInput = MigrationSubAccountDetailsInput(3654, Seq.empty, associateAllUsersToSubAccount = false, "username")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.migrateSubAccount(Matchers.any(classOf[MigrationSubAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Left(expected)))
    post(s"/migrate/subaccount",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).migrateSubAccount(Matchers.any(classOf[MigrationSubAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("promote account - success") {
    val expected = true
    val migrationAccountDetailsInput = MigrationAccountDetailsInput(3654, 1, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, None, None, "username")
    when(service.promoteSubAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Right(expected)))
    post(s"/promote",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).promoteSubAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("promote account - failure") {
    val migrationAccountDetailsInput = MigrationAccountDetailsInput(3654, 1, Seq.empty, Seq.empty, associateAllUsersToSubAccount = false, None, None, "username")
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.promoteSubAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))).thenReturn(Future.successful(Left(expected)))
    post(s"/promote",
      body = Serialization.write(migrationAccountDetailsInput),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).promoteSubAccount(Matchers.any(classOf[MigrationAccountDetailsInput]), Matchers.any(classOf[DateTime]))
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }


  test("Get partner and sub-account information by account id - success") {
    val accountId = 3654
    val expected = PartnerAndSubAccountInfo(accountId = accountId, name = "AccountName1", firstActivatedAt = Some("2016-05-05T00:00:00.000Z"), publicId = "publicId1", hierarchyPath = "1012/3654/",accountTypeId =  AccountTypes.DIRECT_CUSTOMER.id, accountType = AccountTypes.DIRECT_CUSTOMER.name, users = Set.empty, roles = Set.empty, permissionTemplates = Seq.empty, Seq.empty, Seq.empty, administer = false)
    when(service.fetchAccountDetailsV2(accountId)).thenReturn(Future.successful(Right(expected)))
    get(s"/account/v2/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[PartnerAndSubAccountInfo]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).fetchAccountDetailsV2(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get partner and sub-account information by account id - failure") {
    val accountId = 3654
    val expected = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    when(service.fetchAccountDetailsV2(accountId)).thenReturn(Future.successful(Left(expected)))
    get(s"/account/v2/$accountId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(service).fetchAccountDetailsV2(accountId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get partner and sub-account information by user id - success") {
    val userId = 1
    val expected = PartnerAndSubAccountUserInfo(1,"Sunder","Raj","<EMAIL>","2015-10-20T00:00:00.000Z",Set(UserAccountAssociationInfo(1,"active",Vector(UserRoleInfo("PrimaryAdmin", Seq(EnvironmentPermissions(EnvironmentTypes.GLOBAL_ENVIRONMENT.name, Set("ACCOUNTS_CREATE", "ACCOUNTS_MODIFY", "ACCOUNTS_DELETE", "ACCOUNTS_VIEW")), EnvironmentPermissions(EnvironmentTypes.PRODUCTION_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW")), EnvironmentPermissions(EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW")), EnvironmentPermissions(EnvironmentTypes.SANDBOX_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW"))))),None)))
    when(service.fetchUserDetails(userId)).thenReturn(Future.successful(Right(expected)))
    get(s"/user/$userId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[PartnerAndSubAccountUserInfo]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).fetchUserDetails(userId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get partner and sub-account information by user id - failure") {
    val userId = 1
    val expected = ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser)
    when(service.fetchUserDetails(userId)).thenReturn(Future.successful(Left(expected)))
    get(s"/user/$userId",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).fetchUserDetails(userId)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get partner and sub-account information by email - success") {
    val email = "<EMAIL>"
    val expected = PartnerAndSubAccountUserInfo(1,"Sunder","Raj","<EMAIL>","2015-10-20T00:00:00.000Z",Set(UserAccountAssociationInfo(1,"active",Vector(UserRoleInfo("PrimaryAdmin", Seq(EnvironmentPermissions(EnvironmentTypes.GLOBAL_ENVIRONMENT.name, Set("ACCOUNTS_CREATE", "ACCOUNTS_MODIFY", "ACCOUNTS_DELETE", "ACCOUNTS_VIEW")), EnvironmentPermissions(EnvironmentTypes.PRODUCTION_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW")), EnvironmentPermissions(EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW")), EnvironmentPermissions(EnvironmentTypes.SANDBOX_ENVIRONMENT.name, Set("BATCHJOB_CREATE", "BATCHJOB_MODIFY", "BATCHJOB_DELETE", "BATCHJOB_VIEW", "TEMPLATES_CREATE", "TEMPLATES_MODIFY", "TEMPLATES_DELETE", "TEMPLATES_VIEW"))))),None)))
    when(service.fetchUserDetailsByEmail(email)).thenReturn(Future.successful(Right(expected)))
    get(s"/user",
      params = Map("email"-> email),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[PartnerAndSubAccountUserInfo]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).fetchUserDetailsByEmail(email)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get partner and sub-account information by email - failure") {
    val email = "<EMAIL>"
    val expected = ErrorResponseFactory.get(ExceptionCodes.CouldNotFindBusinessUser)
    when(service.fetchUserDetailsByEmail(email)).thenReturn(Future.successful(Left(expected)))
    get(s"/user",
      params = Map("email"-> email),
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).fetchUserDetailsByEmail(email)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Get migrated parent accounts - success") {
    val expected = List(MigratedAccount(1,"AccountName1",1,"1/"), MigratedAccount(4,"AccountName4",2,"4/"))
    when(service.getMigratedParentAccounts()).thenReturn(Future.successful(Right(expected)))
    get(s"/migrated/parents",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[List[MigratedAccount]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(service).getMigratedParentAccounts()
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Associate User Account Role - failure") {
    val userId, accountId, userRoleId = 1L
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(service.associateUserAccountRole(userId, accountId, Option(userRoleId), 0, None)).thenReturn(Future.successful(Left(expected)))
    post(s"/associate/user/$userId/account/$accountId/role/$userRoleId?isSystemRole=false",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).associateUserAccountRole(userId, accountId, Option(userRoleId), 0 , None)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Associate User Account Role - success") {
    val userId, accountId, userRoleId = 1L
    val expected = true
    when(service.associateUserAccountRole(userId, accountId, Option(userRoleId), 0, None)).thenReturn(Future.successful(Right(expected)))
    post(s"/associate/user/$userId/account/$accountId/role/$userRoleId?isSystemRole=false",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).associateUserAccountRole(userId, accountId, Option(userRoleId), 0, None)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update Account Type - failure") {
    val accountId = 1L
    val accountType = 2
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(service.updateAccountType(accountId, accountType)).thenReturn(Future.successful(Left(expected)))
    post(s"/update/account/$accountId/accountType/$accountType",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).updateAccountType(accountId, accountType)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update Account Type - success") {
    val accountId = 1L
    val accountType = 2
    val expected = true
    when(service.updateAccountType(accountId, accountType)).thenReturn(Future.successful(Right(expected)))
    post(s"/update/account/$accountId/accountType/$accountType",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).updateAccountType(accountId, accountType)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update Administer - failure") {
    val accountId = 1L
    val administer = true
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(service.updateAdminister(accountId, administer)).thenReturn(Future.successful(Left(expected)))
    post(s"/update/account/$accountId/administer/$administer",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).updateAdminister(accountId, administer)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update Administer - success") {
    val accountId = 1L
    val administer = true
    val expected = true
    when(service.updateAdminister(accountId, administer)).thenReturn(Future.successful(Right(expected)))
    post(s"/update/account/$accountId/administer/$administer",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).updateAdminister(accountId, administer)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update isSponsorBank Option - failure") {
    val accountId = 1L
    val isSponsorBank = true
    val initiatedBy = "<EMAIL>"
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccessForbidden)
    when(service.updateIsSponsorBank(accountId, isSponsorBank, initiatedBy)).thenReturn(Future.successful(Left(expected)))
    post(s"/update/account/$accountId/isSponsorBank/$isSponsorBank?initiatedBy=<EMAIL>",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error,expected))
    }
    verify(service).updateIsSponsorBank(accountId, isSponsorBank, initiatedBy)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Update isSponsorBank Option - success") {
    val accountId = 1L
    val isSponsorBank = true
    val initiatedBy = "<EMAIL>"
    val expected = true
    when(service.updateIsSponsorBank(accountId, isSponsorBank, initiatedBy)).thenReturn(Future.successful(Right(expected)))
    post(s"/update/account/$accountId/isSponsorBank/$isSponsorBank?initiatedBy=<EMAIL>",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok,expected))
    }
    verify(service).updateIsSponsorBank(accountId, isSponsorBank, initiatedBy)
    verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  def validate[T: Manifest](actualStatus: Int, expectedStatus: Int, body: String, expected: T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
