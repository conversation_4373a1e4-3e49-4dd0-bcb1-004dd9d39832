package me.socure.account.servlet

import me.socure.account.service.AccountSettingService
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.dashboard.NewAccountSettings
import me.socure.model.account.{EnvironmentWithAccount, SocialNetworkAppKeys}
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.EnvironmentNameAndId
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 5/6/16.
  */
class AccountSettingServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec = ExecutionContext.global

  val service = mock[AccountSettingService]
  val servlet = new AccountSettingsServlet(service)

  before {
    reset(service)
  }

  addServlet(servlet, "/*")

  test("should return new account not found") {
    when(service.getNewAccountSetting(3434)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))))
    get("/new/3434") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("should return properly when an exception is thrown(newAccountSettings)") {
    when(service.getNewAccountSetting(3434)).thenReturn(Future.failed(new Exception()))
    get("/new/3434") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(UnknownError.id.toString)
    }
  }

  test("should return new account object") {
    when(service.getNewAccountSetting(3434)).thenReturn(Future.successful(Right(UserFixture.accountSettingsWithApiKeys)))
    get("/new/3434") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }

  test("update domain should return account not found"){
    when(service.updateDomain(33, List("0.0.0.0"))) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    val params = """{"accountId" : 33, "domain" : ["0.0.0.0"]}"""
    post("/update_domain", params) {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("update domian should return udpate message"){
    when(service.updateDomain(22, List("0.0.0.0"))) thenReturn Future(Right("Domain Updated Successfully"))
    val params = """{"accountId" : 22, "domain" : ["0.0.0.0"]}"""
    post("/update_domain", params) {
      status should equal(200)
      body should startWith("{")
      body should endWith("}")
      body should include("Domain Updated Successfully")
    }
  }

  test("update domian should return and exception"){
    when(service.updateDomain(44, List("0.0.0.0"))) thenReturn Future.failed(new Exception)
    val params = """{"accountId" : 44, "domain" : ["0.0.0.0"]}"""
    post("/update_domain", params) {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(UnknownError.id.toString)
    }
  }

  /*
  * Remove Account Cache
  * */
  test("remove account cache should return account not found") {
    when(service.deleteAccountCache(33)) thenReturn Future(Left(ErrorResponse(ExceptionCodes.AccountNotFound.id, ExceptionCodes.AccountNotFound.description)))
    post("/remove_account_cache/33") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("remove account cache should return success response") {
    when(service.deleteAccountCache(22)) thenReturn Future(Right("Account Cache removed successfully"))
    post("/remove_account_cache/22") {
      status should equal(200)
      body should startWith("{")
      body should endWith("}")
      body should include("Account Cache removed successfully")
    }
  }

  test("remove account cache should return an exception") {
    when(service.deleteAccountCache(44)) thenReturn Future.failed(new Exception)
    post("/remove_account_cache/44") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(UnknownError.id.toString)
    }
  }

  /*
  * Remove Individual Cache
  * */
  test("remvoe individual cache should return cache id not found") {
    when(service.deleteInvidiualCache(1000)) thenReturn Future(Left(ErrorResponse(ExceptionCodes.IndividualCacheIdNotFound.id, ExceptionCodes.IndividualCacheIdNotFound.description)))
    post("/remove_invidiual_cache/1000") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.IndividualCacheIdNotFound.id.toString)
    }
  }

  test("remove individual cache should return success response") {
    when(service.deleteInvidiualCache(100)) thenReturn Future(Right("Invidual Cache removed successfully"))
    post("/remove_invidiual_cache/100") {
      status should equal(200)
      body should startWith("{")
      body should endWith("}")
      body should include("Invidual Cache removed successfully")
    }
  }

  test("remove individual cache should return failed response") {
    when(service.deleteInvidiualCache(200)) thenReturn Future(Left(ErrorResponse(ExceptionCodes.DeleteIndividualCacheFaild.id, ExceptionCodes.DeleteIndividualCacheFaild.description)))
    post("/remove_invidiual_cache/200") {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.DeleteIndividualCacheFaild.id.toString)
    }
  }

  /*
  * Update Account Cache
  * */
  test("update account cache should return cache id not found") {
    when(service.upsertAccountCache(AccountOverallCache(1,new DateTime(0, DateTimeZone.UTC), 44))) thenReturn Future(Left(ErrorResponse(ExceptionCodes.AccountNotFound.id, ExceptionCodes.AccountNotFound.description)))
    val params = """{"id" : 1, "date" : "0", "accountId" : 44}"""
    post("/update_account_cache", params) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":100,"message":"The account does not exist"}}"""
    }
  }

  test("update account cache should return success response") {
    when(service.upsertAccountCache(AccountOverallCache(1,new DateTime(0, DateTimeZone.UTC), 22))) thenReturn Future(Right("Account Cache update successful"))
    val params = """{"id" : 1, "date" : "0", "accountId" : 22}"""
    post("/update_account_cache", params) {
      status should equal(200)
      body shouldBe """{"status":"ok","data":"Account Cache update successful"}"""
    }
  }


  /*
  * Update Individual Cache
  * */
  test("update individual cache should return cache id not found") {
    when(service.upsertIndividualCache(AccountIndividualCache(1, "email", new DateTime(0L, DateTimeZone.UTC), 33))) thenReturn Future(Left(ErrorResponse(ExceptionCodes.AccountNotFound.id, ExceptionCodes.AccountNotFound.description)))
    val params = """{"id" : 1, "identifier" : "email", "date" : "0", "accountId" : 33}"""
    post("/upsert_individual_cache", params) {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":100,"message":"The account does not exist"}}"""
    }
  }

  test("update individual cache should return success response") {
    when(service.upsertIndividualCache(AccountIndividualCache(1, "email", new DateTime(0L, DateTimeZone.UTC), 22))) thenReturn Future(Right("Account Invidiual Cache update successful"))
    val params = """{"id" : 1, "identifier" : "email", "date" : "0", "accountId" : 22}"""
    post("/upsert_individual_cache", params) {
      status should equal(200)
      body shouldBe """{"status":"ok","data":"Account Invidiual Cache update successful"}"""
    }
  }

  /*
  * Remove App Key
  * */
  test("remove appkey should return cache id not found") {
    when(service.removeSocialNetworkkeys(300)) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.SocialKeyIdNotFound)))
    post("/remove_appkey/300") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":110,"message":"Social network key Id not found"}}"""
    }
  }

  test("remove appkey should return success response") {
    when(service.removeSocialNetworkkeys(100)) thenReturn Future(Right("social network keys removed successfully"))
    post("/remove_appkey/100") {
      status should equal(200)
      body shouldBe """{"status":"ok","data":"social network keys removed successfully"}"""
    }
  }

  test("remove appkey should return fail response") {
    when(service.removeSocialNetworkkeys(100)) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.DeleteSocialNetworkKeyFailed)))
    post("/remove_appkey/100") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":107,"message":"Social netowrk keys delete not successful"}}"""

    }
  }

  /*
  * Update or Insert App Key
  * */
  test("update appkey should return cache id not found") {
    when(service.upsertSocialNetworkKey(SocialNetworkAppKeys(5, "facebook", "appkey", "secret", 1, 33))) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    val params = """{"id" : 5, "provider" : "facebook", "appkey" : "appkey", "appsecret" : "secret", "environment" : 1, "accountId" : 33}"""
    post("/upsert_appkey", params) {
      status should equal(400)
      body should startWith("{")
      body should endWith("}")
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("update appkey should return success response") {
    when(service.upsertSocialNetworkKey(SocialNetworkAppKeys(5, "facebook", "appkey", "secret", 1, 22))) thenReturn Future(Right("zXsocial network keys added/updated successfully"))
    val params = """{"id" : 5, "provider" : "facebook", "appkey" : "appkey", "appsecret" : "secret", "environment" : 1, "accountId" : 22}"""
    post("/upsert_appkey", params) {
      status should equal(200)
      body should startWith("{")
      body should endWith("}")
      body should include("social network keys added/updated successfully")
    }
  }

  /*
  * Getting all environment with account details
  * */
  test("get environment with account details should say record not found") {
    when(service.getAllEnvironmentWithAccountDetails) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.RecordsNotFound)))
    get("/get_all_env") {
      status should equal(400)
      body should include(ExceptionCodes.RecordsNotFound.id.toString)
    }
  }

  test("get environment with account details") {
    when(service.getAllEnvironmentWithAccountDetails) thenReturn Future(Right(Vector(EnvironmentWithAccount(1, "AccountName", "email", 2, 1))))
    get("/get_all_env") {
      status should equal(200)
      body shouldBe """{"status":"ok","data":[{"accountId":1,"accountName":"AccountName","email":"email","environmentId":2,"environmentType":1}]}"""
    }
  }

  /*
  * Get environment details
  * */
  test("get environment details should say account not found") {
    when(service.getEnvironmentByEmail("<EMAIL>")) thenReturn Future(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    get("/get_environment_details/<EMAIL>") {
      status should equal(400)
      body should include(ExceptionCodes.AccountNotFound.id.toString)
    }
  }

  test("get environment details should return details") {
    when(service.getEnvironmentByEmail("<EMAIL>")) thenReturn Future(Right(Vector(EnvironmentNameAndId(1, "Production"))))
    get("/get_environment_details/<EMAIL>") {
      status should equal(200)
      body shouldBe """{"status":"ok","data":[{"id":1,"name":"Production"}]}"""
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
