package me.socure.account.servlet.dashbordv2

import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.dashboard.NewAccountSettings
import me.socure.dashboardv2.EnvironmentSettingsServlet
import me.socure.model.account.{ApiKeyInfo, ApiKeyStatus, EnvironmentWithDomains}
import me.socure.model.dashboardv2._
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.storage.slick.tables.account.DtoEnvironment
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito._
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 8/26/16.
  */
class EnvironmentSettingsServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val serviceMock: EnvironmentSettingsService = mock[EnvironmentSettingsService]
  val businessServiceMock: BusinessUserRoleService = mock[BusinessUserRoleService]

  val servlet = new EnvironmentSettingsServlet(serviceMock, businessServiceMock)
  val dtoEnvironment: DtoEnvironment = DtoEnvironment(1,"accessTokenProd", "secretKeyProd","accessTokenSecretProd" ,Some("domain.com"), 0, 1, None)
  before {
    reset(serviceMock)
  }

  addServlet(servlet, "/*")

  val envDomain: EnvironmentDomain = EnvironmentDomain(1, List("new_domain.com"))
  val envDomainV2: EnvironmentDomain = EnvironmentDomain(1, List("new_domain.com"),Some(Creator(1,1)))
  val environmentList = Seq(EnvironmentNameAndId(1, "Production"), EnvironmentNameAndId(2, "Development"))
  val environmentWithDomainsList = Seq(EnvironmentWithDomains(1, "Production", "prod.com"), EnvironmentWithDomains(2, "Development", "dev.com"))

  test("should return account settings with apiKeys") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(1, None)) thenReturn Future.successful(Right(UserFixture.accountSettingsWithApiKeys))
    get("/with_api_keys/1") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }

  test("should return account settings with apiKeys - V2") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(1, Some(Creator(1,1)))) thenReturn Future.successful(Right(UserFixture.accountSettingsWithApiKeys))
    get("/with_api_keys/1?creator_user_id=1&creator_account_id=1") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }

  test("get account settings with api keys should return error for invalid account access - V2") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(2, Some(Creator(1,1)))) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
    get("/with_api_keys/2?creator_user_id=1&creator_account_id=1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden)))
    }
  }

  test("get account settings with api keys should return account not found") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(2, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    get("/with_api_keys/2") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)))
    }
  }


  test("should return account settings with apiKeys when permissionChkTransaction true") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(2, Some(Creator(2,2)), true)) thenReturn Future.successful(Right(UserFixture.accountSettingsWithApiKeys))
    get("/with_api_keys/2?creator_user_id=2&creator_account_id=2&permissionChkForTransaction=true") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }


  test("get account settings with api keys should return error for invalid account access - V2 when permissionChkTransaction true") {
    when(serviceMock.getEnvironmentSettingsWithApiKeys(2, Some(Creator(1,1)), true)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
    get("/with_api_keys/2?creator_user_id=1&creator_account_id=1&permissionChkForTransaction=true") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden)))
    }
  }

  test("should return account settings with apiKeys With EvenetManager permission") {
    when(serviceMock.getEnvironmentSettingsWithApiKeysDev(1, None)) thenReturn Future.successful(Right(UserFixture.accountSettingsWithApiKeys))
    get("/with_api_keys_dev/1") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }

  test("should return account settings with apiKeys - V2 With EvenetManager permission") {
    when(serviceMock.getEnvironmentSettingsWithApiKeysDev(1, Some(Creator(1,1)))) thenReturn Future.successful(Right(UserFixture.accountSettingsWithApiKeys))
    get("/with_api_keys_dev/1?creator_user_id=1&creator_account_id=1") {
      validate[Response[NewAccountSettings]](status, 200, body, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys))
    }
  }

  test("get account settings with api keys should return error for invalid account access - V2 With EvenetManager permission") {
    when(serviceMock.getEnvironmentSettingsWithApiKeysDev(2, Some(Creator(1,1)))) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccessForbidden)))
    get("/with_api_keys_dev/2?creator_user_id=1&creator_account_id=1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden)))
    }
  }

  test("get account settings with api keys should return account not found With EvenetManager permission") {
    when(serviceMock.getEnvironmentSettingsWithApiKeysDev(2, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(AccountNotFound)))
    get("/with_api_keys_dev/2") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)))
    }
  }

  test("update domain should return success with true value") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    when(serviceMock.updateDomain(envDomain)) thenReturn Future.successful(auditDetails, Right(true))
    post("/update_domain", envDomain.encodeJson()) {
      validate[Response[(AuditDetails, Boolean)]](status, 200, body, Response(ResponseStatus.Ok, (auditDetails, true)))
    }
  }

  test("update domain should return environment not found") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    val error = ErrorResponseFactory.get(EnvironmentNotFound)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Option(error), Seq.empty)
    val expected = (auditDetails, Left(error))
    when(serviceMock.updateDomain(envDomain)) thenReturn Future.successful(expected)
    post("/update_domain", envDomain.encodeJson()) {
      validate[Response[(AuditDetails,Either[ErrorResponse,Boolean])]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
  }

  test("remove social key should return true") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.deletedSocialNetworkKeys(1, None)) thenReturn Future.successful(Right(true))
    post("/remove_appkey", Map("id" -> "1")) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("remove social key should return environent not found") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.deletedSocialNetworkKeys(10, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    post("/remove_appkey", Map("id" -> "10")) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("upsert social key should return true") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeys)) thenReturn Future.successful(Right(3L))
    post("/upsert_appkey", UserFixture.socialNetworkAppKeys.encodeJson()) {
      validate[Response[Long]](status, 200, body, Response(ResponseStatus.Ok, 3))
    }
  }

  test("upsert social key should return environment not found") {
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeys)) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    post("/upsert_appkey", UserFixture.socialNetworkAppKeys.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("get environment list should return true with list") {
    when(serviceMock.getAccountEnvironmentList(1)) thenReturn Future.successful(Right(environmentList))
    get("/get_environments/1") {
      validate[Response[Seq[EnvironmentNameAndId]]](status, 200, body, Response(ResponseStatus.Ok, environmentList))
    }
  }

  test("get environment list should return environment not found") {
    when(serviceMock.getAccountEnvironmentList(1)) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    get("/get_environments/1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("get environment with domain for an account"){
    when(serviceMock.getEnvironmentWithDomains(1)) thenReturn Future.successful(Right(environmentWithDomainsList))
    get("/get_environment_with_domains/1") {
      validate[Response[Seq[EnvironmentWithDomains]]](status, 200, body, Response(ResponseStatus.Ok, environmentWithDomainsList))
    }
  }

  test("should return error message for invalid account id") {
    when(serviceMock.getEnvironmentWithDomains(100)) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    get("/get_environment_with_domains/100"){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("generate api key should return true") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.generateApiKey(envId, None)) thenReturn Future.successful(Right(1))
    post(s"/generate_api_key",Map("env_id" -> envId.toString)) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
  }

  test("generate api key should return api key can not be renewed") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.generateApiKey(envId, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    post(s"/generate_api_key",Map("env_id" -> envId.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    }
  }

  test("generate api key should return true for event manager permission") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.generateApiKeyDev(envId, None)) thenReturn Future.successful(Right(1))
    post(s"/generate_api_key_dev",Map("env_id" -> envId.toString)) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
  }

  test("generate api key should return api key can not be renewed with event manager permission") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.generateApiKeyDev(envId, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    post(s"/generate_api_key_dev",Map("env_id" -> envId.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    }
  }

  test("deprecate api key should return true") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.changeApiKeyStatus(envId, None)) thenReturn Future.successful(Right(Seq(1)))
    post(s"/deprecate_api_key",Map("env_id" -> envId.toString)) {
      validate[Response[Seq[Int]]](status, 200, body, Response(ResponseStatus.Ok, Seq(1)))
    }
  }

  test("deprecate api key should return api key status not changed") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.changeApiKeyStatus(envId, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    post(s"/deprecate_api_key",Map("env_id" -> envId.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    }
  }

  test("deprecate api key should return true with event manager permission") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.changeApiKeyStatusDev(envId, None)) thenReturn Future.successful(Right(Seq(1)))
    post(s"/deprecate_api_key_dev",Map("env_id" -> envId.toString)) {
      validate[Response[Seq[Int]]](status, 200, body, Response(ResponseStatus.Ok, Seq(1)))
    }
  }

  test("deprecate api key should return api key status not changed with event manager permission") {
    val envId = 1
    when(businessServiceMock.isPermissionAvailable(1, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))))
    when(serviceMock.changeApiKeyStatusDev(envId, None)) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    post(s"/deprecate_api_key_dev",Map("env_id" -> envId.toString)) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    }
  }

  test("update domain v2 should return success with true value") {
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expected: (AuditDetails, Either[ErrorResponse, Boolean]) = (auditDetails, Right(true))
    val response = Response(ResponseStatus.Ok, expected)
    when(serviceMock.updateDomain(envDomainV2)) thenReturn Future.successful(expected)
    post("/update_domain", envDomainV2.encodeJson()) {
      validate[Response[(AuditDetails, Either[ErrorResponse, Boolean])]](status, 200, body, response)
    }
  }

  test("remove social key v2 should return true") {
    val socialKeyId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.deletedSocialNetworkKeys(socialKeyId, Some(creator))) thenReturn Future.successful(Right(true))
    post(s"/remove_appkey?id=$socialKeyId",creator.encodeJson()) {
      validate[Response[Boolean]](status, 200, body, Response(ResponseStatus.Ok, true))
    }
  }

  test("remove social key v2 should return environent not found") {
    val socialKeyId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.deletedSocialNetworkKeys(socialKeyId, Some(creator))) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    post(s"/remove_appkey?id=$socialKeyId",creator.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("upsert social key v2 should return true") {
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeysV2)) thenReturn Future.successful(Right(3L))
    post("/upsert_appkey", UserFixture.socialNetworkAppKeysV2.encodeJson()) {
      validate[Response[Long]](status, 200, body, Response(ResponseStatus.Ok, 3))
    }
  }

  test("upsert social key v2 should return environment not found") {
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeysV2)) thenReturn Future.successful(Left(ErrorResponseFactory.get(EnvironmentNotFound)))
    post("/upsert_appkey", UserFixture.socialNetworkAppKeysV2.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound)))
    }
  }

  test("generate api key v2 should return true") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.generateApiKey(envId, Some(creator))) thenReturn Future.successful(Right(1))
    post(s"/generate_api_key?env_id=$envId",creator.encodeJson()) {
      validate[Response[Int]](status, 200, body, Response(ResponseStatus.Ok, 1))
    }
  }

  test("generate api key v2 should return api key can not be renewed") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.generateApiKey(envId, Some(creator))) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    post(s"/generate_api_key?env_id=$envId",creator.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyCannotBeRenewed)))
    }
  }

  test("deprecate api key v2 should return true") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.changeApiKeyStatus(envId, Some(creator))) thenReturn Future.successful(Right(Seq(1)))
    post(s"/deprecate_api_key?env_id=$envId",creator.encodeJson()) {
      validate[Response[Seq[Int]]](status, 200, body, Response(ResponseStatus.Ok, Seq(1)))
    }
  }

  test("deprecate api key v2 should return api key status not changed") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.changeApiKeyStatus(envId, Some(creator))) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    post(s"/deprecate_api_key?env_id=$envId",creator.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    }
  }

  test("deprecate api key v2 should return true with event manager permission") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.changeApiKeyStatusDev(envId, Some(creator))) thenReturn Future.successful(Right(Seq(1)))
    post(s"/deprecate_api_key_dev?env_id=$envId",creator.encodeJson()) {
      validate[Response[Seq[Int]]](status, 200, body, Response(ResponseStatus.Ok, Seq(1)))
    }
  }

  test("deprecate api key v2 should return api key status not changed with event manager permission") {
    val envId = 1
    val creator = Creator(1, 1)
    when(businessServiceMock.isPermissionAvailable(envDomainV2.creator.get.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)).thenReturn(Future(Right(true)))
    when(serviceMock.changeApiKeyStatusDev(envId, Some(creator))) thenReturn Future.successful(Left(ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    post(s"/deprecate_api_key_dev?env_id=$envId",creator.encodeJson()) {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyStatusNotchanged)))
    }
  }

  test("getEnvironments should return true with list - V2") {
    when(serviceMock.getEnvironments(1, 1, Some(Creator(1, 1)))) thenReturn Future.successful(Right(environmentList))
    get("/account/1?creator_user_id=1&creator_account_id=1&role=1") {
      validate[Response[Seq[EnvironmentNameAndId]]](status, 200, body, Response(ResponseStatus.Ok, environmentList))
    }
  }

  test("getApiKeys should return ApiKeys") {
    val expected = Seq(ApiKeyInfo(1, ApiKeyStatus.ACTIVE, "1234-1234-1234-1234"))
    when(serviceMock.getApiKeys(1, Some(Creator(1, 1)))) thenReturn Future.successful(Right(expected))
    get("/apikeys/1?creator_user_id=1&creator_account_id=1") {
      validate[Response[Seq[ApiKeyInfo]]](status, 200, body, Response(ResponseStatus.Ok, expected))
    }
    verify(serviceMock).getApiKeys(1, Some(Creator(1, 1)))
  }

  test("getApiKeys should fail") {
    val expected = ErrorResponseFactory.get(ApiKeyFetchFailed)
    when(serviceMock.getApiKeys(1, Some(Creator(1, 1)))) thenReturn Future.successful(Left(expected))
    get("/apikeys/1?creator_user_id=1&creator_account_id=1") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
    verify(serviceMock).getApiKeys(1, Some(Creator(1, 1)))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }

}
