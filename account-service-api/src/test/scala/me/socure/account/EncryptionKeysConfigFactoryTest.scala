package me.socure.account

import com.amazonaws.regions.Regions
import com.typesafe.config.ConfigFactory
import me.socure.configuration.EncryptionKeysConfig
import me.socure.model.encryption.{DataKeyLen, DataKeyServiceId, KmsId, KmsIdsConfig, ServiceKmsDetail}
import org.scalatest.{FreeSpec, Matchers}

/**
  * Created by jamesanto on 4/18/17.
  */
class EncryptionKeysConfigFactoryTest extends FreeSpec with Matchers {
  "EncryptionKeysConfigFactory" - {
    "should parse config properly" in {
      val config = ConfigFactory.parseString(
        """
          |{
          | aws {
          |   access.key = "access_key"
          |   secret.key = "secret_key"
          | }
          | data.key.len = 32
          | kms.ids {
          |   "us-east-1" = "kms_id_us_east_1"
          |   "us-west-1" = "kms_id_us_west_1"
          | }
          | service.kms.ids {
          |      service1 {
          |          "us-east-1" = "kms_id_us_east_1"
          |          "us-east-1" = ${?CLIENT_ENV_KEY_USEAST}
          |          "us-west-1" = "kms_id_us_west_1"
          |          "us-west-1" = ${?CLIENT_ENV_KEY_USWEST}
          |      }
          |  }
          |}
        """.stripMargin)

      EncryptionKeysConfigFactory.get(config) shouldBe EncryptionKeysConfig(
        dataKeyLen = DataKeyLen(32),
        kmsIdsConfig = KmsIdsConfig(Map(
          Regions.US_EAST_1 -> KmsId("kms_id_us_east_1"),
          Regions.US_WEST_1 -> KmsId("kms_id_us_west_1")
        )),
        serviceKeys = Seq(
          ServiceKmsDetail(
            serviceName = DataKeyServiceId("service1"), kmsIds = KmsIdsConfig(
              Map(
                Regions.US_EAST_1 -> KmsId("kms_id_us_east_1"),
                Regions.US_WEST_1 -> KmsId("kms_id_us_west_1")
              )
            )
          )
        )
      )
    }
  }
}
