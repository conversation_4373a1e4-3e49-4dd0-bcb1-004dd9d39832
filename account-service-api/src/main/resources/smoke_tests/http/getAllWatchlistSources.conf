name = "Fetch all watchlist sources"

request {
  method = "GET"
  endpoint = ${hostname}"/settings/preferences/ca/watchlist/sources"
  configurators = [
    {
      type = "socure_hmac"
      realm = "Socure"
      version = "1.0"
      strength = 512
      secret_key = ${hmac.secret.key}
    },
    {
      type = "disable_cert_validation"
    }
  ]
}

validations = [
  {
    validator = "is_not_null"
    json_path = "headers.content-type"
  }
  {
    validator = "exists"
    json_path = "body.json"
  }
  {
    validator = "=="
    json_path = "status_code"
    value = 200
  }
  {
    validator = "equals_ignore_case"
    json_path = "body.json.status"
    value = "ok"
  }
  {
    validator = "contains"
    json_path = "headers.content-type"
    value = "application/json;charset=utf-8"
  }
]
