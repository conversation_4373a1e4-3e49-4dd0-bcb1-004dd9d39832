withDBMigration=false

database {
  socure {
    common {
      driverClassName="com.mysql.cj.jdbc.Driver"
      dataSource {
        user="socure-2018"
        password="""ENC(QBjOHzjQ7nqrHsGoPi4j475dRu9bvUrMUdGdLQQA/d6t38PEbAhZQHZNMmFDuddOo8KkFg==)"""
      }
    }
    primary {
      jdbcUrl="****************************************************************************************************************************************************************************************************************************************************************************************"
      poolName="account_service_primary"
      maximumPoolSize=10
    }
    replica {
      jdbcUrl="*******************************************************************************************************************************************************************************************************************************************************************************************"
      poolName="account_service_replica"
      maximumPoolSize=10
      readOnly=true
    }
  }
}

executionContext {
  poolSize=100
}

jetty {
  threadPool {
    minSize=20
    maxSize=20
  }
}

badloginconfig {
  maxtry=3
  autoreset=false
  timeunit="seconds"
  duration=60
}

apikeyrenewal {
  oldapikeyexpiryinhours=48
  renewaltimeinhours=1
}

scheduled{
  activities{
    user{
      inactive{
        days = 90
      }
      notloggedin{
        days = 20
      }
    }
    activationcode{
      hours = 24
    }
    passwordresetcode {
      hours = 24
    }
  }
}

password {
  expires = 90
}

client.specific.encryption {
  data.key.len = 32 //Please dont change it
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/client-specific-encryption-dev"""
  }
  service.kms.ids {
      etlv3 {
          "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/etlv3-kms-dev"""
      }
  }
}

pgp.encryption {
  kms.id {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/account-service-pgp-dev"""
  }
  keyExpiryDuration = ********
}

payload.encryption {
  kms.id {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/socure-account-service-dev"""
  }
  multi.region.kms.ids {
    "us-east-1" = "arn:aws:kms:us-east-1:************:alias/socure/socure-account-service-payload-encryption-dev"
  }
}

idpMetadata {
  schema.url = "schemas/saml-schema-metadata-2.0.xsd"
}

mailgunConfig {
  host = "https://api.mailgun.net/v2/socure.com/messages"
  key = """ENC(ibSiwI/xh3BYUTxuV1A8DrltfPfAx3SgHhcwQjcH2wYuQ8vMvR6xRdxBYFy5iw+FMNRFFdYiskBLqvRnzn0gSA==)"""
  from = "Socure Support <<EMAIL>>"
}

aws.ses {
  region = "us-east-1"
  groupName = "UXFeatures"
  flagName = "Enable_Ses_Email_Service"
}

pageConfigV2 {
  dashboard.url = "https://dashboard.dev.socure.com/"
  activatewithpassword = "#!/activate_with_password"
  resetpassword = "#/reset_password_token"
}

pageConfigV3 {
  dashboard.url = "https://dashboard.dev.socure.com/"
  activatewithpassword = "#/activate_with_password"
  resetpassword = "#/reset_password_token"
}

platformConfig {
  dashboard.url = "https://riskos.sandbox.socure.com",
  activatewithpassword = "#/activate_with_password"
  resetpassword = "#/reset_password_token"
}

supportMail = "<EMAIL>"
customerSuccessMail = "<EMAIL>"

dynamic.control.center{
  s3 {
    bucketName="idplus-global-settings-************-us-east-1"
  }
  memcached {
    host="localhost"
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

saltService {
  endpoint="https://salt.webapps.us-east-1.product-dev.socure.link"
  endpoint2="https://salt.webapps.us-east-1.product-dev.socure.link"
  groupName="UXServicesRamp"
  flagName="SaltService_Ramp"


  hmac {
    secret.key="""ENC(YPgZ7fGrHaAE87V0k+p03Y+MLM1oHKnHFAQw5/BcWaM6Rn4Km6ga0GU++8HmtaD8Eg78PI6WfMsQYHomExkAWQ==)"""
    realm="Socure"
    version="1.0"
    strength="512"
  }
  metrics.enabled = false
}

memcached.endpoint="localhost:11211"

hmac {
  secret.key="""ENC(0D+7rTd1tvMY19QuV+pXfuLd5We+Rb9kFtIdh/QuV2UrkPORFU3MbV+3DbKqdaIAgnBEFheof+Hr4PpCP8ukww==)"""
  ttl=5
  time.interval=5
  strength=512
}

pbePassword="""ENC(iPj81CiAXVSZnR9TV5CJGW9f04KulSKzhWyrqShjlsg=)"""
session.idle.timeout=480 //minutes
auto.timeout=480 //minutes
account.ui.settings {
    default {
        session.idle.timeout=480 //minutes
        auto.timeout=480 //minutes
    }
    platform {
        session.idle.timeout=1440 //minutes
        auto.timeout=120 //minutes
    }
}


enc.key="""ENC(jOTuxx34jyWio5fXp59f0K3ph2N5bC9SRftX4n9635s=)"""

#Secret key expiry check minutes in millis (5 minutes)
secretKey.expiry.check = 300000
#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-east-1"
  }
  memcached {
    host=localhost
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================ModelManagement=========================#
modelmanagement {
  endpoint = "https://model-management.webapps.us-east-1.product-dev.socure.link"
  endpoint2 = "https://model-management.webapps.us-east-1.product-dev.socure.link"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval= 5000
    aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
  }
}
#===================ModelManagement=========================#

businessuserroles.permissions.overriddenByChild = [94, 145, 146, 147, 171, 174, 193, 219, 268]
domain.whitelistedEmailDomain = ["gmail.com", "yahoo.com", "hotmail.com", "socure.com", "socure.me", "mailinator.com", "rdsmigrationstage2.com.", "applausemail.com", "testmail.com", "hotmail.com", "cotributemail.com", "yopmail.com", "zohomail.in", "outlook.com", "test.com", "mass.gov", "x.com", "paypal.com"]
decisionBaselineConfig {
  logicId = 8
  modelName = "Baseline v7"
  modelVersion = "7"
}
decision.service {
  endpoint="https://decision-service.webapps.us-east-1.product-dev.socure.link"
}

#===================DocumentManager=========================#
document.manager {
    endpoint="https://document-manager.webapps.us-east-1.product-dev.socure.link"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/dev/hmac-835044"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=localhost
      port=11211
    }
}
#===================DocumentManager=========================#

server.metrics.enabled = false
db.metrics.enabled = false

#=================Docv Orchestra Service=====================#
 docvOrchestra {
     endpoint="https://document-orchestra.webapps.us-east-1.product-dev.socure.link/"
     endpoint2 = "https://document-orchestra.webapps.us-east-1.product-dev.socure.link"
     groupName="DVServicesRamp"
     flagName="DocVOrchestraService_Ramp"
     hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/dev/hmac"
      }
      metrics.enabled = false
  }
#=================Docv Orchestra Service=====================#


default.provisioning {
    bundleReference = "Bundle 5"
    document.verification {
        documentTypes = ["passport", "license"]
        config = [
                    {
                        id = 1
                        value = 18
                        decision = 3
                    }
                ]
    }
    decision  {
        baselineConfig  {
          logicId = 8
          modelName = "Baseline v7"
          modelVersion = "7"
       }
    }
    rateLimits = [
        {
            api = "api-c6LXBEdl5o",
            environmentType = 1,
            limit = 0
        },
        {
            api = "api-c6LXBEdl5o",
            environmentType = 2,
            limit = 0
        }
    ]
}

analytics {
    isHistoricDataImported = "true"
}

#===================ai-gateway-service=========================#
ai.gateway.service {
  endpoint = "http://ai-gateway-service"
}
