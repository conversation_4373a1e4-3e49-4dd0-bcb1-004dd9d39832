withDBMigration=true

database {
  socure {
    common {
      driverClassName="com.mysql.cj.jdbc.Driver"
      dataSource {
        user="socure-2018"
        password="""ENC(QBjOHzjQ7nqrHsGoPi4j475dRu9bvUrMUdGdLQQA/d6t38PEbAhZQHZNMmFDuddOo8KkFg==)"""
      }
    }
    primary {
      jdbcUrl="****************************************************************************************************************************************************************************************************************************************************************************************"
      poolName="account_service_primary"
      maximumPoolSize=10
    }
    replica {
      jdbcUrl="*******************************************************************************************************************************************************************************************************************************************************************************************"
      poolName="account_service_replica"
      maximumPoolSize=10
      readOnly=true
    }
  }
}

executionContext {
  poolSize=100
}

jetty {
  threadPool {
    minSize=20
    maxSize=20
  }
}

badloginconfig {
  maxtry=3
  autoreset=false
  timeunit="seconds"
  duration=60
}

apikeyrenewal {
  oldapikeyexpiryinhours=48
  renewaltimeinhours=1
}

scheduled{
  activities{
    user{
      inactive{
        days = 90
      }
      notloggedin{
        days = 20
      }
    }
    activationcode{
      hours = 24
    }
    passwordresetcode {
      hours = 24
    }
  }
}

password {
  expires = 90
}

client.specific.encryption {
  data.key.len = 32 //Please dont change it
  kms.ids {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/client-specific-encryption-dev"""
  }
  service.kms.ids {
      etlv3 {
          "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/etlv3-kms-dev"""
      }
  }
}



pgp.encryption {
  kms.id {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/account-service-pgp-dev"""
  }
  keyExpiryDuration = ********
}

payload.encryption {
  kms.id {
    "us-east-1" = """arn:aws:kms:us-east-1:************:alias/socure/socure-account-service-dev"""
  }
  multi.region.kms.ids {
    "us-east-1" = "arn:aws:kms:us-east-1:************:alias/socure/socure-account-service-payload-encryption-dev"
  }
}


idpMetadata {
  schema.url = "schemas/saml-schema-metadata-2.0.xsd"
}

mailgunConfig {
  host = "https://api.mailgun.net/v2/socure.com/messages"
  key = """ENC(NBxpbPtaEFulAJH9iONB6mOkttLWekvQGd7YxjZww4i+SFfwRryzUhAlEoLn8HgzthieF9iNCok+Q/H1JtCFZF30Cvw=)"""
  from = "Socure Support <<EMAIL>>"
}

aws.ses {
  region = "us-east-1"
  assumeRoleArn = "arn:aws:iam::************:role/product-dev-ses-role"
  assumeRoleRegion = "us-east-1"
  groupName = "UXFeatures"
  flagName = "Enable_Ses_Email_Service"
}

pageConfigV2 {
  dashboard.url = "https://dashboard.dev.socure.com/"
  activatewithpassword = "#!/activate_with_password"
  resetpassword = "#/reset_password_token"
}

pageConfigV3 {
  dashboard.url = "https://dashboard.dev.socure.com/"
  activatewithpassword = "#/activate_with_password"
  resetpassword = "#/reset_password_token"
}

platformConfig {
  dashboard.url = "https://riskos.sandbox.socure.com",
  activatewithpassword = ""
  resetpassword = "login/reset_password"
}

supportMail = "<EMAIL>"
customerSuccessMail = "<EMAIL>"

dynamic.control.center{
  s3 {
    bucketName="idplus-global-settings-************-us-east-1"
  }
  memcached {
    host="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link"
    port=11211
    ttl=86400
  }
  local.cache {
    timeout.minutes=2
  }
}

saltService {
  endpoint="http://salt-service"
  endpoint2="http://salt-service"
  groupName="UXServicesRamp"
  flagName="SaltService_Ramp"


  hmac {
    secret.key="""ENC(vig/DY42h9r88X5+SqLfTua/tqBrRivhZiCrH6ztrpXDEKyR64X2y6tJQQbdLtiOBie+89jwiBge71751fAxSbBCd2Z+o5ussBAx1A==)"""
    realm="Socure"
    version="1.0"
    strength="512"
  }
  metrics.enabled = false
}

memcached.endpoint="product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link:11211"

hmac {
  secret.key="""ENC(ID54T0RD/o8QiwFSTM++bxuNQYTsr9cmkd9rsxKbWklbvw/kyPkDhpi4/ESdseL3sb2Aw5yisXfjbLncJuV1vQ==)"""
  ttl=5
  time.interval=5
  strength=512
}

pbePassword="""ENC(Gb5NonSXYlBzDc1lXtpk9z6QOgHKuUZjZFwwX6+k2cBOdqYErzShIfykXhMLmilqjOE2w4fKD6MM1g/B6Fl2FA==)"""
session.idle.timeout=480 //minutes
auto.timeout=480 //minutes

enc.key="""ENC(Hxb3DPwNHfyUhIVK1OzKvWhnm4fVB8Ya2zzjbsJGHynAOBwJ/SZwkXUw4p4ilwGK)"""

#Secret key expiry check minutes in millis (5 minutes)
secretKey.expiry.check = 300000
#===================Control Center==========================#

control.center {
  s3 {
    bucketName = "idplus-global-settings-************-us-east-1"
  }
  memcached {
    host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
    port=11211
    ttl="24 hours"
  }
}

#===================Control Center==========================#

#===================ModelManagement=========================#
modelmanagement {
  endpoint = "http://model-management"
  endpoint2 = "http://model-management"
  hmac {
    realm = "Socure"
    version = "1.0"
    strength = 512
    secret.refresh.interval= 5000
    aws.secrets.manager.id="model-management/dev/hmac-1a0b2a"
  }
}
#===================ModelManagement=========================#

businessuserroles.permissions.overriddenByChild = [94, 145, 146, 147, 171, 174, 193, 252, 219, 243, 268]
domain.whitelistedEmailDomain = ["gmail.com", "yahoo.com", "hotmail.com", "socure.com", "socure.me", "mailinator.com", "rdsmigrationstage2.com.", "applausemail.com", "testmail.com", "hotmail.com", "cotributemail.com", "yopmail.com", "zohomail.in", "outlook.com", "test.com", "mass.gov", "x.com", "paypal.com", "draftkings.com"]

decisionBaselineConfig {
  logicId = 8
  modelName = "Baseline v7"
  modelVersion = "7"
}
decision.service {
  endpoint="http://decision-service"
}

#===================DocumentManager=========================#
document.manager {
    endpoint="http://document-manager"
    hmac {
      ttl = 5
      time.interval = 5
      strength = 512
      aws.secrets.manager.id="document-manager/dev/hmac-835044"
      secret.refresh.interval = 5000
      realm="Socure"
      version = "1.0"
    }
    memcached {
      host=product-apps-dev.cfg.memcached.us-east-1.product-dev.socure.link
      port=11211
    }
}

#===================DocumentManager=========================#

server.metrics.enabled = false
db.metrics.enabled = false

#=================Docv Orchestra Service=====================#
 docvOrchestra {
     endpoint="http://document-orchestra"
     endpoint2 = "http://document-orchestra"
     groupName="DVServicesRamp"
     flagName="DocVOrchestraService_Ramp"
     hmac {
        realm="Socure"
        version = "1.0"
        strength=512
        aws.secrets.manager.id="docv-orchestra/dev/hmac"
      }
      metrics.enabled = false
  }
#=================Docv Orchestra Service=====================#

default.provisioning {
    bundleReference = "Bundle 5"
    document.verification {
        documentTypes = ["passport", "license"]
        config = [
            {
                id = 1
                value = 18
                decision = 3
            }
        ]
    }
    decision  {
        baselineConfig  {
          logicId = 8
          modelName = "Baseline v7"
          modelVersion = "7"
       }
    }
    rateLimits = [
        {
            api = "api-c6LXBEdl5o",
            environmentType = 1,
            limit = 0
        },
        {
            api = "api-c6LXBEdl5o",
            environmentType = 2,
            limit = 0
        }
    ]
}

analytics {
    isHistoricDataImported = "true"
}

#===================ai-gateway-service=========================#
ai.gateway.service {
  endpoint = "http://ai-gateway-service"
}