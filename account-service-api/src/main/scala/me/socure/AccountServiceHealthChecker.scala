package me.socure

import me.socure.common.healthcheck.checker.HealthChecker

import scala.concurrent.{ExecutionContext, Future}

/**
  * <AUTHOR>
  * @param ec
  */
class AccountServiceHealthChecker(implicit ec : ExecutionContext) extends HealthChecker {

  //TODO: Change to check against real account service app status

  override def check() : Future[Boolean] =  {
    Future.successful(true)
  }


}
