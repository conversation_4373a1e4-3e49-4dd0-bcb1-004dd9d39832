package me.socure.account

import com.typesafe.config.Config
import me.socure.configuration.ApiKeyRenewalConfig

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 3/23/17.
  */
object ApiKeyRenewalConfigFactory {
  def get(config : Config) : ApiKeyRenewalConfig = {
    val autoDeprecateTime = config.getInt("oldapikeyexpiryinhours")
    val renewalTime = config.getInt("renewaltimeinhours")
    ApiKeyRenewalConfig(autoDeprecateTime, renewalTime)
  }
}
