package me.socure.account

import com.amazonaws.regions.Regions
import com.typesafe.config.Config
import me.socure.model.encryption.{KmsId, KmsIdsConfig}

import scala.collection.JavaConverters._

/**
 * Created by a<PERSON><PERSON><PERSON><PERSON> on 11/22/2021.
 */
object PayloadKmsKeysConfigFactory {

  def get(config: Config): KmsIdsConfig = {
    KmsIdsConfig(config
      .getConfig("kms.id")
      .entrySet()
      .asScala
      .map { k =>
        Regions.fromName(k.getKey) -> KmsId(k.getValue.unwrapped().toString)
      }.toMap
    )
  }

  def getKmsIdsConfig(config: Config): KmsIdsConfig = {
    KmsIdsConfig(config
      .entrySet()
      .asScala
      .map { k =>
        Regions.fromName(k.getKey) -> KmsId(k.getValue.unwrapped().toString)
      }.toMap
    )
  }
}
