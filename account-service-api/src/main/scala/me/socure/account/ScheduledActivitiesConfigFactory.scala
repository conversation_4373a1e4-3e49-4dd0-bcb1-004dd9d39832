package me.socure.account

import com.typesafe.config.Config
import me.socure.configuration.ScheduledActivityConfig

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 06/04/2017.
  */
object ScheduledActivitiesConfigFactory {

  def get(config : Config, expiresDays : Int) : ScheduledActivityConfig = {
    val lockDays = config.getInt("user.inactive.days")
    val registerWaitDays = config.getInt("user.notloggedin.days")
    val activationCodeTTL = config.getInt("activationcode.hours")
    val passwordResetCodeTTL = config.getInt("passwordresetcode.hours")

    ScheduledActivityConfig(
      minInactiveDays = lockDays,
      registrationWaitDays = registerWaitDays,
      activationCodeTTL = activationCodeTTL,
      passwordResetTokenTTL = passwordResetCodeTTL,
      passwordExpiryDays = expiresDays)
  }

}
