package me.socure.account

import com.amazonaws.regions.Regions
import com.typesafe.config.Config
import me.socure.configuration.EncryptionKeysConfig
import me.socure.model.encryption.{DataKeyLen, DataKeyServiceId, KmsId, KmsIdsConfig, ServiceKmsDetail}

import scala.collection.JavaConverters._
import scala.collection.mutable

/**
  * Created by jamesanto on 4/17/17.
  */
object EncryptionKeysConfigFactory {

  private def getKmsIdsConfig(config: Config): KmsIdsConfig = {
    KmsIdsConfig(config
      .entrySet()
      .asScala
      .map { entry =>
        Regions.fromName(entry.getKey) -> KmsId(entry.getValue.unwrapped().toString)
      }.toMap)
  }

  private def getServiceKmsIdsConfig(config: Config): Seq[ServiceKmsDetail] = {
    val services =  config.entrySet().asScala.map(x => x.getKey.split('.').head).map(DataKeyServiceId)
    services.foldLeft[Seq[ServiceKmsDetail]](Seq.empty)((acc, elem) => {
      val kmsIds = config
        .getConfig(elem.value)
        .resolve()
        .entrySet()
        .asScala
        .map { entry =>
          Regions.fromName(entry.getKey) -> KmsId(entry.getValue.unwrapped().toString)
        }.toMap
      acc :+ ServiceKmsDetail(
        serviceName = elem,
        kmsIds = KmsIdsConfig(kmsIds)
      )
    })
  }

  def get(config: Config): EncryptionKeysConfig = {
    val serviceKeys = getServiceKmsIdsConfig(config.getConfig("service.kms.ids"))
    EncryptionKeysConfig(
      dataKeyLen = DataKeyLen(config.getInt("data.key.len")),
      kmsIdsConfig = getKmsIdsConfig(config.getConfig("kms.ids")),
      serviceKeys = serviceKeys
    )
  }
}
