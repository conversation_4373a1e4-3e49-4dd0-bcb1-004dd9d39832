package me.socure.account.servlet

import me.socure.account.service.common.AccountSearchRequest
import me.socure.account.service.common.exceptions.ExceptionCodes.InvalidInputFormat
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.ManageAccountsService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}


class ManageAccountsV2Servlet (manageAccountsService: ManageAccountsService, val hmacVerifier: HMACHttpVerifier)
                              (implicit val executor : ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts" + getClass.getSimpleName)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  put("/publicid/:publicId/delete"){
    val publicId = params("publicId")
    val delete = params("delete").toBoolean
    ScalatraResponseFactory.get(
      manageAccountsService.updateIsDeleted(publicId = publicId, isDeleted = delete)
    )
  }

  put("/publicid/:publicId/activate"){
    val publicId = params("publicId")
    val activate = params("activate").toBoolean
    ScalatraResponseFactory.get(
      manageAccountsService.updateIsActive(publicId = publicId, isActive = activate)
    )
  }

  post("/accounts/list"){
    val asr = parsedBody.extract[AccountSearchRequest]
    val resultFuture = manageAccountsService.searchAccounts(asr)
      .map(accounts => {
         Right(accounts)
      }) recover {
      case e =>
        logger.info("Error trying to search accounts: " + asr, e)
        Left(ErrorResponse(ExceptionCodes.InternalError.id, e.getMessage))
    }
    metrics.timeFuture("accounts.search.duration")(resultFuture)
    ScalatraResponseFactory.get(resultFuture)
  }

  put("/accounts/users/:userId/email") {

    val requestPayload: Map[String, String] = parsedBody.extract[Map[String, String]]
    val emailOpt: Option[String] = requestPayload.get("email")
    val userId: Long = params("userId").toLong

    val result: Future[Either[ErrorResponse, String]] = emailOpt match {
      case Some(email) if email.trim.size > 0 =>
        manageAccountsService.updateEmail(userId, email)
      case _ =>
        Future.successful(Left(ErrorResponse(ExceptionCodes.InvalidRequestPayload.id, "Email id should be present")))
    }
    ScalatraResponseFactory.get(metrics.timeFuture("accounts.users.email")(result))

  }

  post("/update_consent_reason") {
    val consentId = params.get("consent_id").getOrElse(halt(400, "consent_id is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    ScalatraResponseFactory.get(manageAccountsService.updateConsentReason(accountId.toLong, consentId.toInt))
  }

  get("/get_consent_reason/:account_id"){
    val accountId = params("account_id")
    ScalatraResponseFactory.get(manageAccountsService.getConsentId(accountId.toLong))
  }

  get("/reset_password/:email") {
    val email = params.get("email").getOrElse(halt(400, "email is not provided"))
    ScalatraResponseFactory.get(manageAccountsService.resetPassword(email))
  }

  get("/onboarding-email/:email") {
    val email = params.get("email").getOrElse(halt(400, "email is not provided"))
    ScalatraResponseFactory.get(manageAccountsService.sendOnboardMail(email))
  }
}
