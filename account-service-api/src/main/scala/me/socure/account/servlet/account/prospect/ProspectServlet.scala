package me.socure.account.servlet.account.prospect

import me.socure.account.prospect.ProspectService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.prospect.{ProspectExclusionInput, ProspectInclusionInput}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.util.Try
import scala.concurrent.ExecutionContext

class ProspectServlet(prospectService: ProspectService, val hmacVerifier: HMACHttpVerifier)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {
  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/exclusion") {
    try {
      val start = params.get("start").flatMap(s => Try(s.toInt).toOption)
      val size = params.get("size").flatMap(s => Try(s.toInt).toOption)
      val search = params.get("search").getOrElse("")
      ScalatraResponseFactory.get(prospectService.getExclusionList(start, size, search))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while fetching the exclusion list => ", exception)
        exception;
    }
  }

  get("/exclusion/total_count") {
    try {
      ScalatraResponseFactory.get(prospectService.getExclusionListTotalCount)
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while fetching the exclusion list total count => ", exception)
        exception;
    }
  }

  put("/exclusion") {
    try {
      val exclusionInput = parsedBody.extract[ProspectExclusionInput]
      ScalatraResponseFactory.get(prospectService.insertOrUpdateExclusionList(exclusionInput))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while parsing insertOrUpdateExclusionList => ", exception)
        exception;
    }
  }

  delete("/exclusion/:id") {
    try {
      val id = params("id").toLong
      ScalatraResponseFactory.get(prospectService.deleteExclusionDetail(id))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while deleting Exclusion List => ", exception)
        exception;
    }
  }


  get("/inclusion") {
    try {
      val start = params.get("start").flatMap(s => Try(s.toInt).toOption)
      val size = params.get("size").flatMap(s => Try(s.toInt).toOption)
      val search = params.get("search").getOrElse("")
      ScalatraResponseFactory.get(prospectService.getInclusionList(start, size, search))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while fetching the inclusion list => ", exception)
        exception;
    }
  }

  get("/inclusion/total_count") {
    try {
      ScalatraResponseFactory.get(prospectService.getInclusionListTotalCount)
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while fetching the exclusion list total count => ", exception)
        exception;
    }
  }

  put("/inclusion") {
    try {
      val inclusionInput = parsedBody.extract[ProspectInclusionInput]
      ScalatraResponseFactory.get(prospectService.insertOrUpdateInclusionList(inclusionInput))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while parsing insertOrUpdateInclusionList => ", exception)
        exception;
    }
  }

  delete("/inclusion/:id") {
    try {
      val id = params("id").toLong
      ScalatraResponseFactory.get(prospectService.deleteInclusionDetail(id))
    } catch {
      case exception: Exception =>
        logger.error("Error occurred while deleting Inclusion List => ", exception)
        exception;
    }
  }

}
