package me.socure.account.servlet

import me.socure.account.service.AccountMetadataService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.convertors.AccountMetadataConvertors
import me.socure.model.account.AccountMetadata
import me.socure.security.hmac.HMACHttpVerifier
import org.json4s.{DefaultFormats, Formats}
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * Servlet for account metadata endpoints.
 */
class AccountMetadataServlet(accountMetadataService: AccountMetadataService, val hmacVerifier: HMACHttpVerifier)
                            (implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
  with FutureSupport {

  override protected implicit def jsonFormats: Formats = DefaultFormats

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
  }

  /**
   * GET /account_metadata/:accountId
   * Get metadata for an account
   */
  get("/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(
      accountMetadataService.getByAccountId(accountId).map {
        case Some(metadata) => Right(AccountMetadataConvertors.toModel(metadata))
        case None => Right(None)
      }
    )
  }

  /**
   * PUT /account_metadata/:accountId
   * Create or update metadata for an account
   */
  put("/:accountId") {
    val accountId = params("accountId").toLong
    val metadata = parsedBody.extract[AccountMetadata]
    
    if (metadata.accountId != accountId) {
      halt(400, """{"error": "Account ID in path must match account ID in body"}""")
    }
    
    val createdBy = request.getHeader("X-User-Email") match {
      case null | "" => "system"
      case email => email
    }
    
    ScalatraResponseFactory.get(
      accountMetadataService.createOrUpdate(metadata.accountId, metadata.childId, createdBy).map { updatedMetadata =>
        Right(AccountMetadataConvertors.toModel(updatedMetadata))
      }
    )
  }

  /**
   * DELETE /account_metadata/:accountId
   * Delete metadata for an account
   */
  delete("/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(
      accountMetadataService.delete(accountId).map { count =>
        Right(count > 0)
      }
    )
  }
}
