package me.socure.account.servlet

import dispatch.Future
import me.socure.account.service.AccountSftpUserService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

class AccountSftpUserServlet(accountSftpUserService: AccountSftpUserService,
                            val hmacVerifier: HMACHttpVerifier)
                            (implicit val executor : ExecutionContext)
                            extends BaseScalatraServlet
                            with FutureSupport
                            with AuthenticationSupport {

  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts.sftp." + getClass.getSimpleName)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)

  post("/") {
    val accountId : Long = params("account_id").toLong
    val sftpUser: String = params("sftp_user")
    ScalatraResponseFactory.publicGet(accountSftpUserService.saveAccountSftpUser(accountId, sftpUser))
  }

  get("/") {
    ScalatraResponseFactory.publicGet(accountSftpUserService.listSftpUsers())
  }

}
