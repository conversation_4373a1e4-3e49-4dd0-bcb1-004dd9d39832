package me.socure.account.servlet

import me.socure.account.service.ProductService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.UpdateProductsRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class ProductServlet(productService: ProductService, val hmacVerifier: HMACHttpVerifier)(implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/account/:accountId") {
    val accountId = params.get("accountId").getOrElse(halt(400, "account_id not provided")).toLong
    ScalatraResponseFactory.publicGet(productService.getProductsForAccount(accountId))
  }

  post("/account") {
    val updateProductsRequest = parsedBody.extract[UpdateProductsRequest]
    ScalatraResponseFactory.publicGet(productService.updateProductsForAccount(updateProductsRequest))
  }

}
