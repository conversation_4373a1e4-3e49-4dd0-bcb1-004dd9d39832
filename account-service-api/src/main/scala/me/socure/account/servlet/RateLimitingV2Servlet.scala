package me.socure.account.servlet

import me.socure.account.service.RateLimitingService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class RateLimitingV2Servlet(rateLimitingService: RateLimitingService)
                           (implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport {
  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
  }

  get("/policies_v2"){
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId is not provided")).toLong
    val environmentTypeId = params.get("environmentType").getOrElse(halt(400, "environmentType is not provided")).toLong
    val apiIds = params.get("apiIds").getOrElse(halt(400, "apiIds is not provided")).split(",").toSet[String].map(_.trim).filter(_.nonEmpty)
    if(apiIds.isEmpty) halt(400, "at least one apiId is required")
    ScalatraResponseFactory.getGeneric(rateLimitingService.getRateLimits(accountId, environmentTypeId, apiIds))
  }

}
