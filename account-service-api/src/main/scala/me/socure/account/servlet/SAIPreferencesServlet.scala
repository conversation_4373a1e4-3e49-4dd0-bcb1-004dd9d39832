package me.socure.account.servlet

import me.socure.account.service.SaiPreferencesService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.sai.SAIPreferencesUpsertRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class SAIPreferencesServlet(saiPreferencesService: SaiPreferencesService,
                            val hmacVerifier: HMACHttpVerifier)
                           (implicit val executor: ExecutionContext)
    extends BaseScalatraServlet
        with FutureSupport
        with AuthenticationSupport {

    private val metrics: Metrics = JavaMetricsFactory.get("manage.sai.preferences." + getClass.getSimpleName)

    before() {
        contentType = formats("json")
        validateRequest()
    }

    override protected implicit def jsonFormats: Formats = JsonFormats.formats

    override val logger = LoggerFactory.getLogger(getClass)

    post("/") {
        try {
            val saiPreferencesUpsertRequest = parsedBody.extract[SAIPreferencesUpsertRequest]
            metrics.timeFuture("save.sai.preferences")(saiPreferencesService.saveSaiPreferences(saiPreferencesUpsertRequest))
            fetchSaiPreferences(saiPreferencesUpsertRequest.accountId.toLong)
        }
        catch {
            case ex: Exception => {
                metrics.increment("invalid.sai.preferences.upsert.request")
                logger.error("Failed to save SAI Preferences for account due to, ", ex)
                ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
            }
        }
    }

    get("/account/:accountId") {
        val accountId = params("accountId").toLong
        fetchSaiPreferences(accountId)
    }

    private def fetchSaiPreferences(accountId: Long) = {
        ScalatraResponseFactory.get(
            metrics.timeFuture("fetch.sai.preferences")
            (saiPreferencesService.fetchSaiPreferences(accountId))
            )
    }
}