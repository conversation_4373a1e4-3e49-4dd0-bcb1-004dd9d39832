package me.socure.account.servlet.encryption

import com.amazonaws.regions.Regions
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.EncryptionKeysService
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.encryption.{AccountId, CustomerKeyDetails, KmsId}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class EncryptionKeysServletV2(encryptionKeysService: EncryptionKeysService,
                              dashboardAccountServiceV2: DashboardAccountServiceV2,
                              val hmacVerifier: HMACHttpVerifier)
                             (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/customer_keys/:account_id") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    ScalatraResponseFactory.getGeneric(metrics.timeFuture("fetch_external_KMS_keys_by_account")(
      encryptionKeysService.getActiveCustomerKeys(AccountId(accountId)))
    )
  }

  get("/active_keys/:account_id") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.getAllActiveKeys(AccountId(accountId))
    )
  }

  get("/service_keys/all/:service_name/:region") {
    val serviceName = params.get("service_name").getOrElse(halt(400, "service_name is not provided"))
    val keysForRegion = params.get("region").getOrElse(Regions.US_EAST_1.getName)
    ScalatraResponseFactory.getGeneric(metrics.timeFuture("fetch_all_service_keys_by_service")(
      encryptionKeysService.getAllKeysForService(serviceName, keysForRegion))
    )
  }

  get("/service_keys/:service_name/:account_id") {
    val serviceName = params.get("service_name").getOrElse(halt(400, "service_name is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.getServiceAccountKeys(serviceName, AccountId(accountId))
    )
  }

  post("/customer_kms/test") {
    val customerKey = parsedBody.extract[CustomerKeyDetails]
    ScalatraResponseFactory.publicGet(metrics.timeFuture("test_customer_kms")(
      encryptionKeysService.testCustomerKms(customerKey))
    )
  }

  post("/customer_key") {
    val customerKeyDetails = parsedBody.extract[CustomerKeyDetails]
    val result = encryptionKeysService.testCustomerKms(customerKeyDetails) flatMap {
        case Right(testResult) if testResult =>
          dashboardAccountServiceV2.getParentsSubAccounts(customerKeyDetails.accountId.value) flatMap {
            case Right(subAccounts) =>
              encryptionKeysService.generateCustomerKeys(customerKeyDetails.accountId, subAccounts.map(AccountId), KmsId(customerKeyDetails.kmsArn)).map {
                case true => Right(true)
                case _ => Left(ErrorResponseFactory.get(ExceptionCodes.GenerateCustomerKeysFailed))
              }
            case Left(error) => Future.successful(Left(error))
          }
        case _ =>
          Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CustomerKMSEncryptionError)))
    }
    ScalatraResponseFactory.publicGet(metrics.timeFuture("add_customer_kms")(result))
  }

}
