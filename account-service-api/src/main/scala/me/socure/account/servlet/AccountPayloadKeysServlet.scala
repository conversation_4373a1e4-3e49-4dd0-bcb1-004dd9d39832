package me.socure.account.servlet

import me.socure.account.service.AccountPayloadKeysService
import me.socure.account.service.common.AccountPayloadKeysConstant._
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.MergePayloadKeysRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * Created by abhishek on 11/15/2021.
 *
 * All the v2 endpoints deal with MultiRegion KMS keys.
 */
class AccountPayloadKeysServlet(accountPayloadKeysService: AccountPayloadKeysService,
                                val hmacVerifier: HMACHttpVerifier
                               )(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/get_payload_keys"){
    val accountId : Option[Long] = params.get(ACCOUNT_ID).map(_.toLong)
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.getPayloadKeys(accountId))
  }

  /*
    This uses the standalone KMS key to generate new key pair and insert it into the DB.
    This will be deprecated in favour of '/create_payload_keys/v2'
    When 'is_rotation' param is set to true, it ensures to maintain the new payload key and the latest old key as active and mark all other old keys as inactive
    When 'is_rotation' param is set to false, only the new payload key is set to active and all previous payload keys are marked as inactive.
   */
  post("/create_payload_keys"){
    val accountId : Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    val keySize: Long = if(params.get(KEY_SIZE).isDefined) params(KEY_SIZE).toLong else 4096
    val customerPublicKey: String = params(CUSTOMER_PUBLIC_KEY)
    val isRotation: Boolean = if(params.get(IS_ROTATION).isDefined) params(IS_ROTATION).toBoolean else false
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.createPayloadKeys(accountId, environmentId, keySize, customerPublicKey, isRotation))
  }

  /*
    This uses the multi-region KMS keys to generate new key pair and insert it into the DB.
    When 'is_rotation' param is set to true, it ensures to maintain the new payload key and the latest old key as active and mark all other old keys as inactive
    When 'is_rotation' param is set to false, only the new payload key is set to active and all previous payload keys are marked as inactive.
   */
  post("/create_payload_keys/v2"){
    val accountId : Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    val keySize: Long = if(params.get(KEY_SIZE).isDefined) params(KEY_SIZE).toLong else 4096
    val customerPublicKey: String = params(CUSTOMER_PUBLIC_KEY)
    val isRotation: Boolean = if(params.get(IS_ROTATION).isDefined) params(IS_ROTATION).toBoolean else false
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.createPayloadKeysV2(accountId, environmentId, keySize, customerPublicKey, isRotation))
  }

  /*
    This will clone the most recent active record sorted by UpdatedAt only if such record is not protected by Multi-region key.
    The operation retrieves the latest active record and decrypts the same using existing wrapping key and encrypt the same
    using MRK keys and add it as a new record to the database table.
   */
  post("/clone_payload_keys/v2") {
    val accountId: Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.clonePayloadKeys(accountId, environmentId))
  }

  post("/merge_payload_keys"){
    val mergePayloadKeysRequest = parsedBody.extract[MergePayloadKeysRequest]
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.mergePayloadKeys(mergePayloadKeysRequest))
  }

  /*
    Supports updating key pairs to the active record in 3 flexible ways,
    1. Update both the key pairs customer side and the socure side.
    2. Update just the customer public key
    3. Update just the socure key pairs.
   */
  put("/update_payload_keys/:account_id/:environment_id"){
    val accountId : Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    val customerPublicKey: Option[String] = params.get(CUSTOMER_PUBLIC_KEY)
    val isSocureKeysUpdate: Boolean = if(params.get(IS_SOCURE_KEYS_UPDATE).isDefined) params(IS_SOCURE_KEYS_UPDATE).toBoolean else false
    val keySize: Long = if(params.get(KEY_SIZE).isDefined) params(KEY_SIZE).toLong else 4096
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.updatePayloadKeys(accountId, environmentId, customerPublicKey, isSocureKeysUpdate, keySize))
  }

  /*
    Same as existing update endpoint, in case of socure key pair update it would make use of MultiRegion KMS keys.
   */
  put("/update_payload_keys/v2/:account_id/:environment_id"){
    val accountId : Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    val customerPublicKey: Option[String] = params.get(CUSTOMER_PUBLIC_KEY)
    val isSocureKeysUpdate: Boolean = if(params.get(IS_SOCURE_KEYS_UPDATE).isDefined) params(IS_SOCURE_KEYS_UPDATE).toBoolean else false
    val keySize: Long = if(params.get(KEY_SIZE).isDefined) params(KEY_SIZE).toLong else 4096
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.updatePayloadKeysV2(accountId, environmentId, customerPublicKey, isSocureKeysUpdate, keySize))
  }

  put("/delete_payload_keys/:account_id/:environment_id"){
    val accountId : Long = params(ACCOUNT_ID).toLong
    val environmentId: Long = params(ENVIRONMENT_ID).toLong
    ScalatraResponseFactory.publicGet(accountPayloadKeysService.deletePayloadKeys(accountId, environmentId))
  }

}