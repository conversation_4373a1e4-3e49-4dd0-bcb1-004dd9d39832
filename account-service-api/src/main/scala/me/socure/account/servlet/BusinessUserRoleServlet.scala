package me.socure.account.servlet

import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.servlet.{BaseScalatraServlet, BaseServletParamHandling}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.constants.attributes.{AccountAttributeName, AccountAttributeValue}
import me.socure.model.ErrorResponse
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 5/30/16.
  */
class BusinessUserRoleServlet(service: BusinessUserRoleService)(implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with BaseServletParamHandling {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  post("/add_role_to_users") {
    val accountIds = fetchInputParam("user_ids").split(",").map(_.toLong)
    val role = fetchInputParam("role_id").toInt

    ScalatraResponseFactory.get(service.addPermissionToAccount(accountIds.head, role))
  }

  post("/add_role_to_account") {
    val accountId: String = params.get("account_id").getOrElse(throw new Exception("Account Id not provided"))
    val role: String = params.get("role_id").getOrElse(throw new Exception("Role not provided"))
    val issuedBy = params.get("issued_by").getOrElse(throw new Exception("Issued by not provided"))
    val permission = role.toInt
    ScalatraResponseFactory.get(service.addPermissionToAccount(accountId.toLong, permission, Some(issuedBy)))
  }

  post("/remove_role_from_users") {
    val accountIds = fetchInputParam("user_ids").split(",").map(_.toLong)
    val role = fetchInputParam("role_id").toInt

    ScalatraResponseFactory.get(service.removePermissionFromAccount(accountIds.head, role))
  }

  post("/add_attribute") {
    val accountId = fetchInputParam("id").toLong
    val name = AccountAttributeName(fetchInputParam("att_name").toInt)
    val value = AccountAttributeValue(fetchInputParam("att_value").toInt)

    ScalatraResponseFactory.get(service.upsertAttributeToAccount(accountId, name, value))
  }

  post("/remove_attribute") {
    val accountId = fetchInputParam("id").toLong
    val name = AccountAttributeName(fetchInputParam("att_name").toInt)

    ScalatraResponseFactory.get(service.removeAttributeFromAccount(accountId, name))
  }

  get("/list_roles") {
    ScalatraResponseFactory.get(service.listRoles)
  }

  get("/account_permissions/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(service.getAccountPermissions(accountId))
  }

  get("/is_permission_available"){
    val accountId = params("account_id").toLong
    val permission = params("permission").toInt
    ScalatraResponseFactory.get(service.isPermissionAvailable(accountId, permission))
  }

  get("/fetch_parent_account_permission/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(service.getParentAccountPermissions(accountId))
  }

  error {
     case e : Throwable => ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(InvalidInputFormat.id, InvalidInputFormat.description))))
  }
}
