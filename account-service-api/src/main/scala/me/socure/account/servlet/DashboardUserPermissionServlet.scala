package me.socure.account.servlet

import me.socure.account.service.DashboardUserPermissionService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.{Actions, DashboardUserPermissions, JsonFormats, Resources}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DashboardUserPermissionServlet(dashboardUserPermissionService: DashboardUserPermissionService,
                                     val hmacVerifier: HMACHttpVerifier)
                                    (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/") {
    val resp = DashboardUserPermissions.values.filterNot(_.action.id == Actions.PROVISION.id).map(dup => dup.copy(group = Resources.DASHBOARD_PERMISSIONS)).toSeq.sortBy(_.id)
    ScalatraResponseFactory.get(Future.successful(Right(resp)))
  }

  get("/conversion") {
    ScalatraResponseFactory.get(dashboardUserPermissionService.getCurrentAndNewPermissions.map(Right(_)))
  }
}
