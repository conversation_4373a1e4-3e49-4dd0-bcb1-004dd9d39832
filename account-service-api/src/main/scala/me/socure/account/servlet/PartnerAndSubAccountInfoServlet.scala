package me.socure.account.servlet

import me.socure.account.service.PartnerAndSubAccountInfoService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.{JsonFormats, SystemDefinedRoles}
import me.socure.model.account.{MergeAccountDetailsInput, MigrationAccountDetailsInput, MigrationSubAccountDetailsInput}
import org.joda.time.DateTime
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> Kumar
 */
class PartnerAndSubAccountInfoServlet(partnerAndSubAccountInfoService: PartnerAndSubAccountInfoService,
                                      val hmacVerifier: HMACHttpVerifier)
                                     (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/account/v2/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.fetchAccountDetailsV2(accountId))
  }

  get("/account/v1/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.fetchAccountDetails(accountId))
  }

  post("/migrate") {
    val accountDetails = parsedBody.extract[MigrationAccountDetailsInput]
    val startTime = DateTime.now()
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.migrateAccount(accountDetails, startTime))
  }

  post("/merge") {
    val accountDetails = parsedBody.extract[MergeAccountDetailsInput]
    val startTime = DateTime.now()
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.mergeAccount(accountDetails, startTime))
  }

  post("/migrate/subaccount") {
    val accountDetails = parsedBody.extract[MigrationSubAccountDetailsInput]
    val startTime = DateTime.now()
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.migrateSubAccount(accountDetails, startTime))
  }

  post("/promote") {
    val accountDetails = parsedBody.extract[MigrationAccountDetailsInput]
    val startTime = DateTime.now()
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.promoteSubAccount(accountDetails, startTime))
  }


  get("/user/:id") {
    val userId = params("id").toLong
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.fetchUserDetails(userId))
  }

  get("/user") {
    val email = params.get("email").getOrElse(halt(400, "email is not provided"))
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.fetchUserDetailsByEmail(email))
  }

  get("/migrated/parents") {
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.getMigratedParentAccounts())
  }

  post("/associate/user/:userId/account/:accountId/role/:roleId") {
    val userId = params("userId").toLong
    val accountId = params("accountId").toLong
    val roleId = params("roleId").toLong
    val isSystemRole = params.getOrElse("isSystemRole", "false").toBoolean
    val updatedBy = params.get("updatedBy").map(_.toLong)
    val roleType = if (isSystemRole) roleId.toInt else SystemDefinedRoles.CUSTOMROLE.roleType
    val associatingRoleId = if (isSystemRole) None else Option(roleId)
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.associateUserAccountRole(userId, accountId, associatingRoleId, roleType, updatedBy))
  }

  post("/update/account/:accountId/accountType/:accountType") {
    val accountId = params("accountId").toLong
    val accountType = params("accountType").toInt
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.updateAccountType(accountId, accountType))
  }

  post("/update/account/:accountId/administer/:administer") {
    val accountId = params("accountId").toLong
    val administer = params("administer").toBoolean
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.updateAdminister(accountId, administer))
  }

  post("/update/account/:accountId/isSponsorBank/:isSponsorBank") {
    val accountId = params("accountId").toLong
    val isSponsorBank = params("isSponsorBank").toBoolean
    val initiatedBy = params.get("initiatedBy").getOrElse(halt(400, "initiatedBy is not provided"))
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.updateIsSponsorBank(accountId, isSponsorBank, initiatedBy))
  }

  post("/account/:accountId/swapUserRoles/:userId/:swappingUserId") {
    val accountId = params("accountId").toLong
    val userId = params("userId").toLong
    val swappingUserId = params("swappingUserId").toLong
    ScalatraResponseFactory.get(partnerAndSubAccountInfoService.swapUserRoles(accountId, userId, swappingUserId))
  }
}
