package me.socure.account.servlet.account.sponsorbank

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.sponsor.bank.SponsorBankProgramLinkRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class SponsorBankServlet(sponsorBankService: SponsorBankService,
                          val hmacVerifier: HMACHttpVerifier)
                          (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/program/:programId") {
    val programId = params("programId").toLong
    ScalatraResponseFactory.get(sponsorBankService.getSponsorBank(programId))
  }

  get("/linked/programs/:sponsorBankId") {
    val sponsorBankId = params("sponsorBankId").toLong
    ScalatraResponseFactory.get(sponsorBankService.getLinkedPrograms(sponsorBankId))
  }

  get("/non-linked") {
    ScalatraResponseFactory.get(sponsorBankService.getNonSponsorBankPrograms())
  }

  post("/link") {
    val sponsorBankProgramLinkRequest = parsedBody.extract[SponsorBankProgramLinkRequest]
    ScalatraResponseFactory.get(sponsorBankService.linkSponsorBankProgram(
      sponsorBankProgramLinkRequest))
  }

  post("/unlink") {
    val sponsorBankProgramLinkRequest = parsedBody.extract[SponsorBankProgramLinkRequest]
    ScalatraResponseFactory.get(sponsorBankService.unlinkSponsorBankProgram(
      sponsorBankProgramLinkRequest))
  }

}
