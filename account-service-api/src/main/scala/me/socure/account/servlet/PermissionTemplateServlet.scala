package me.socure.account.servlet

import me.socure.account.service.PermissionTemplateService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.PermissionTemplate
import me.socure.model.account.PermissionTemplateMappingInput
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> Kumar
 */
class PermissionTemplateServlet(permissionTemplateService: PermissionTemplateService,
                                val hmacVerifier: HMACHttpVerifier)
                               (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  /**
   * Endpoint to fetch list of permission template mappings by template id
   */
  get("/mappings") {
    val templateId = params.get("template_id").getOrElse(halt(400, "template_id is not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    ScalatraResponseFactory.get(permissionTemplateService.getPermissionTemplateMappingsByTemplateId(templateId.toLong, userId.toLong, accountId.toLong))
  }

  /**
   * Insert permission template mapping
   */
  post("/mappings") {
    val permissionTemplateMappingInput = parsedBody.extract[PermissionTemplateMappingInput]
    ScalatraResponseFactory.get(permissionTemplateService.insertPermissionTemplateMapping(permissionTemplateMappingInput))
  }

  /**
   * Update permission template mapping
   */
  put("/mappings") {
    val permissionTemplateMappingInput = parsedBody.extract[PermissionTemplateMappingInput]
    ScalatraResponseFactory.get(permissionTemplateService.updatePermissionTemplateMapping(permissionTemplateMappingInput))
  }

  /**
   * Delete permission template mapping
   */
  delete("/mappings") {
    val permissionTemplateId = params.get("template_id").getOrElse(halt(400, "template_id is not provided"))
    val environmentTypeId = params.get("environment_type").getOrElse(halt(400, "environment_type is not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    ScalatraResponseFactory.get(permissionTemplateService.deletePermissionTemplateMapping(permissionTemplateId.toLong, environmentTypeId.toInt, userId.toLong, accountId.toLong))
  }

  get("/") {
    val templateId = params.get("template_id").getOrElse(halt(400, "template_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(permissionTemplateService.getPermissionTemplate(templateId.toLong, userId.toLong, accountId.toLong ))
  }

  post("/") {
    val permissionTemplate = parsedBody.extract[PermissionTemplate]
    ScalatraResponseFactory.get(metrics.timeFuture("insert.permission.template")(permissionTemplateService.insertPermissionTemplate(permissionTemplate)))
  }

  put("/") {
    val permissionTemplate = parsedBody.extract[PermissionTemplate]
    ScalatraResponseFactory.get(metrics.timeFuture("update.permission.template")(permissionTemplateService.updatePermissionTemplate(permissionTemplate)))
  }

  delete("/") {
    val permissionTemplateId = params.get("template_id").getOrElse(halt(400, "template_id is not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("delete.permission.template")(permissionTemplateService.deletePermissionTemplate(permissionTemplateId.toLong, userId.toLong, accountId.toLong)))
  }
}
