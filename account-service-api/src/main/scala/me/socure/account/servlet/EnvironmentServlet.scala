package me.socure.account.servlet

import me.socure.account.service.EnvironmentService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

class EnvironmentServlet (service : EnvironmentService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  get("/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(service.getEnvirontmentById(id).map(Right(_)))
  }

  get("/v2/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(service.getEnvironmentDataById(id))
  }
}
