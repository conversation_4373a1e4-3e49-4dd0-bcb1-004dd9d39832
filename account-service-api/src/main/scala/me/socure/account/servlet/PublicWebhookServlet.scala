package me.socure.account.servlet

import me.socure.account.service.PublicWebhookService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import me.socure.account.service.common.exceptions.ExceptionCodes.InvalidParametersPassed
import scala.concurrent.{ExecutionContext, Future}


class PublicWebhookServlet(publicWebhookService: PublicWebhookService)(implicit val executor :ExecutionContext) extends BaseScalatraServlet with FutureSupport {
  override protected implicit def jsonFormats :  Formats = JsonFormats.formats

  get("/list_public_webhooks") {
    ScalatraResponseFactory.get(publicWebhookService.listPublicWebhooks)
  }

  get("/get_public_webhook") {
    val environmentId = params("environment_id").toLong
    val certificateFetch = publicWebhookService.getPublicWebhook(environmentId)
    ScalatraResponseFactory.get(certificateFetch)
  }

  post("/insert_public_webhook"){
    val environmentId : Long = params("environment_id").toLong
    val endpoint : String = params("endpoint")
    val certificate : String = params("public_key_certificate")
    val subscriptionTypeId : Long = params("subscription_type").toLong
    val insertResult = publicWebhookService.insertPublicWebhook(environmentId, endpoint, certificate, subscriptionTypeId)
    ScalatraResponseFactory.get(insertResult)
  }

  post("/update_public_webhook"){
    val environmentId : Long = params("environment_id").toLong
    val endpoint : String = params("endpoint")
    val certificate : String = params("public_key_certificate")
    val subsciprtionType: Long = params("subscription_type").toLong
    val insertResult = publicWebhookService.updatePublicWebhook(environmentId, endpoint, certificate, subsciprtionType)
    ScalatraResponseFactory.get(insertResult)
  }

  post("/delete_public_webhook"){
    val environmentId = params("environment_id").toLong
    val subsciprtionType = params("subscription_type").toLong
    ScalatraResponseFactory.get(publicWebhookService.deletePublicWebhook(environmentId, subsciprtionType))
  }

  get("/environment/:environment_id/subscription/:subscription_type") {
    val envId = params("environment_id").toLong
    val subsciprtionType = params("subscription_type").toLong
    val publicWebhookQuery = publicWebhookService.getPublicWebhooks(envId, Some(subsciprtionType))
    ScalatraResponseFactory.get(publicWebhookQuery)
  }

  get("/environment/:environment_id") {
    val envId = params("environment_id").toLong
    val publicWebhookQuery = publicWebhookService.getPublicWebhooks(envId, None)
    ScalatraResponseFactory.get(publicWebhookQuery)
  }

  get("/account/:account_id/environment/type/:environment_type_id/subscription/:subscription_type") {
    val accountId = params("account_id").toLong
    val envType = params("environment_type_id").toLong
    val subscriptionType = params("subscription_type").toLong
    val publicWebhookQuery = publicWebhookService.getPublicWebhookByAccount(accountId, envType, subscriptionType)
    ScalatraResponseFactory.get(publicWebhookQuery)
  }
}
