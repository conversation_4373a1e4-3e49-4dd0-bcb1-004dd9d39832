package me.socure.account.servlet

import me.socure.account.service.MLAService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.mla.MLAInputRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class MLAFieldServlet(
                       val mlaService: MLAService,
                       val hmacVerifier: HMACHttpVerifier)
                     (implicit val executor : ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts.mla." + getClass.getSimpleName)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)

  post("/field") {
    try {
      val mlaInputReq = parsedBody.extract[MLAInputRequest]
      if(!Option(mlaInputReq.accountId).isDefined || mlaInputReq.accountId.trim().equals("") ||
         !Option(mlaInputReq.securityCode).isDefined || mlaInputReq.securityCode.trim().equals("") ||
         !Option(mlaInputReq.memberNumber).isDefined || mlaInputReq.memberNumber.trim().equals("")) {
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, "AccountNumber, SecurityCode and MemberNumber are mandatory fields."))))
      } else {
        ScalatraResponseFactory.get(metrics.timeFuture("save.mla.field")(mlaService.saveMLAFields(parsedBody.extract[MLAInputRequest])))
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.mla.field.request")
        logger.error("Failed to save MLA fields", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

}
