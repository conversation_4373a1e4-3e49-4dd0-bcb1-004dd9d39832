package me.socure.account.servlet

import me.socure.account.service.UserRoleService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{RolePermissionTemplateAssociation, UserRoleInput}
import me.socure.model.dashboardv2.Creator
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext
import scala.util.Try

/**
 * <AUTHOR> Kumar
 */
class UserRoleServlet(userRoleService: UserRoleService,
                      val hmacVerifier: HMACHttpVerifier)
                     (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/:id") {
    val roleId = params("id").toLong
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.getUserRole(roleId, userId.toLong, accountId.toLong))
  }

  get("/") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided"))
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided"))
    val filterSystemDefinedRoles = params.get("filter_system_defined_roles") match {
      case Some("true") => true
      case _ => false
    }
    ScalatraResponseFactory.get(userRoleService.getUserRolesByAccountId(accountId.toLong, Creator(creatorUserId.toLong, creatorAccountId.toLong), filterSystemDefinedRoles))
  }

  post("/") {
    val userRoleInput = parsedBody.extract[UserRoleInput]
    ScalatraResponseFactory.get(userRoleService.insertUserRole(userRoleInput))
  }

  put("/") {
    val userRoleInput = parsedBody.extract[UserRoleInput]
    userRoleInput.id.getOrElse(halt(400, "id not provided in the payload"))
    ScalatraResponseFactory.get(userRoleService.updateUserRole(userRoleInput))
  }

  delete("/:id") {
    val roleId = params("id").toLong
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.deleteUserRole(roleId, userId.toLong, accountId.toLong))
  }

  get("/roles_by_public_account_id") {
    val publicAccountId = params.get("publicAccountId").getOrElse(halt(400, "public_account_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.getUserRolesByPublicAccountId(publicAccountId))
  }

  get("/template/:id") {
    val userRoleId = params("id").toLong
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.getRolePermissionTemplateAssociation(userRoleId, userId.toLong, accountId.toLong))
  }

  put("/template") {
    val rolePermissionTemplateAssociation = parsedBody.extract[RolePermissionTemplateAssociation]
    ScalatraResponseFactory.get(userRoleService.insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation))
  }

  delete("/template/:id") {
    val userRoleId =  params("id").toLong
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.deleteRolePermissionTemplateAssociation(userRoleId, userId.toLong, accountId.toLong))
  }

  get("/:id/dashboard_permissions") {
    val userRoleId =  params("id").toLong
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.getDashboardPermissionsByRoleId(userRoleId, userId.toLong, accountId.toLong))
  }

  get("/role_type/:role_type/dashboard_permissions") {
    val roleType =  params("role_type").toInt
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.getDashboardPermissionsByRoleTypeID(roleType, userId.toLong, accountId.toLong))
  }

  delete("/:id/delete_with_permissions") {
    val userRoleId =  params("id").toLong
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided"))
    ScalatraResponseFactory.get(userRoleService.deleteUserRoleWithPermissionTemplate(userRoleId, userId.toLong, accountId.toLong))
  }

  get("/get_users_and_roles_for_accounts/total_count") {
    val accountIds = params.get("account_ids").getOrElse(halt(400, "account_ids not provided")).split(",").map(_.toLong)
    val excludeUserStatuses = params.get("exclude_user_statuses").getOrElse("").split(",").map(_.trim).flatMap(status => Try(status.toInt).toOption).toSet match {
      case s if s.isEmpty => None
      case s => Some(s)
    }
    ScalatraResponseFactory.publicGet(userRoleService.getUsersAndRolesTotalRecordCount(accountIds,excludeUserStatuses))
  }

  get("/get_users_and_roles_for_accounts") {
    val accountIds = params.get("account_ids")
        .getOrElse(halt(400, "account_ids not provided"))
        .split(",")
        .map(_.trim)
        .map(id =>
          try {
            id.toLong
          } catch {
            case _: NumberFormatException => halt(400, s"Invalid account ID: $id")
          }
        )
    val excludeUserStatuses = params.get("exclude_user_statuses").getOrElse("").split(",").map(_.trim).flatMap(status => Try(status.toInt).toOption).toSet match {
      case s if s.isEmpty => None
      case s => Some(s)
    }
    val start = params.get("start").flatMap(s => Try(s.toInt).toOption)
    val size = params.get("size").flatMap(s => Try(s.toInt).toOption)
    ScalatraResponseFactory.publicGet(userRoleService.getUsersAndRoles(accountIds, excludeUserStatuses, start, size))
  }

}
