package me.socure.account.servlet

import java.net.URLDecoder
import java.nio.charset.StandardCharsets

import me.socure.account.service.AccountHierarchyService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.{JsonFormats, Status}
import me.socure.model.account.AccountHierarchyInput
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> <PERSON>
 */
class AccountHierarchyServlet(accountHierarchyService: AccountHierarchyService,
                              val hmacVerifier: HMACHttpVerifier)
                             (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  /**
   * Endpoint to fetch account hierarchy by hierarchy id
   */
  get("/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(accountHierarchyService.getAccountHierarchy(id))
  }

  /**
   * Endpoint to fetch account hierarchy by account id
   */
  get("/") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("fetch_hierarchy_by_account")(accountHierarchyService.getAccountHierarchyByAccountId(accountId.toLong)))
  }

  /**
   * Endpoint to update account hierarchy
   */
  put("/") {
    val accountHierarchyInput = parsedBody.extract[AccountHierarchyInput]
    ScalatraResponseFactory.get(accountHierarchyService.updateAccountHierarchy(accountHierarchyInput))
  }

  /**
   * Endpoint to insert account hierarchy
   */
  post("/") {
    val accountHierarchyInput = parsedBody.extract[AccountHierarchyInput]
    ScalatraResponseFactory.get(accountHierarchyService.insertAccountHierarchy(accountHierarchyInput))
  }

  put("/update/admin_count"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val count = params.get("count").getOrElse(halt(400, "count not provided")).toInt
    ScalatraResponseFactory.get(metrics.timeFuture("update_account_hierarchy_admin_count")(accountHierarchyService.updatePrimaryAdminCount(accountId.toLong, count)))
  }

  put("/administer/allow"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("update_account_hierarchy_administer_allow")(accountHierarchyService.updateAdministerFlag(accountId.toLong, administerFlag = true)))
  }

  put("/administer/deny"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("update_account_hierarchy_administer_deny")(accountHierarchyService.updateAdministerFlag(accountId.toLong, administerFlag = false)))
  }

  put("/deactivate"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("update_account_hierarchy_deactivate")(accountHierarchyService.updateHierarchyStatus(accountId.toLong, Status.INACTIVE.id)))
  }

  put("/activate"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    ScalatraResponseFactory.get(metrics.timeFuture("update_account_hierarchy_activate")(accountHierarchyService.updateHierarchyStatus(accountId.toLong, Status.ACTIVE.id)))
  }

  get("/list"){
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided"))
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    ScalatraResponseFactory.get(accountHierarchyService.listAccountHierarchy(accountId.toLong, userId.toLong))
  }

  get("/validate") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
    ScalatraResponseFactory.get(accountHierarchyService.validateAccountAccess(accountId, creatorAccountId))
  }

  get("/validate/access/permissions") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
    val permissions0 = params.get("permissions").getOrElse(halt(400, "permissions not provided")).toString
    val permissions = try {
      URLDecoder.decode(permissions0, StandardCharsets.UTF_8.name()).trim.split(",").map(_.trim.toInt).toSet
    } catch {
      case ex: Exception =>
        logger.info("Invalid permissions ", ex)
        halt(500, "Invalid permissions")
    }
    ScalatraResponseFactory.get(accountHierarchyService.validateAccountAccess(accountId, creatorAccountId, permissions))
  }

  get("/get_root_parent/:account_id") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    ScalatraResponseFactory.get(accountHierarchyService.getRootParent(accountId).map(Right(_)))
  }

  get("/get_company_id/:account_id") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    ScalatraResponseFactory.get(accountHierarchyService.getCompanyId(accountId).map(Right(_)))
  }

  get("/subaccounts/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(accountHierarchyService.getSubAccounts(id))
  }

  get("/get_root_parent_account_type/:account_id") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    ScalatraResponseFactory.get(accountHierarchyService.getRootParentAccountType(accountId))
  }
}



