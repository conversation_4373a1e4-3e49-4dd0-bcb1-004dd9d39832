package me.socure.account.servlet.account.automation

import me.socure.account.automation.{AccountAutomationService, BundleManagementService}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.automation.UpdateAccountProvisioningDetails
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class AccountAutomationServlet (accountAutomationService: AccountAutomationService,
                                bundleManagementService: BundleManagementService,
                                val hmacVerifier: HMACHttpVerifier)
                               (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
   validateRequest()
  }

  get("/bundles") {
    ScalatraResponseFactory.get(bundleManagementService.getBundles())
  }

  get("/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(accountAutomationService.getAccountProvisioningDetails(accountId))
  }

  post("/account/:accountId") {
    val accountId = params("accountId").toLong
    val updateAccountProvisioningDetails = parsedBody.extract[UpdateAccountProvisioningDetails]
    ScalatraResponseFactory.get(accountAutomationService.saveAccountAutomation(accountId, updateAccountProvisioningDetails))
  }

}
