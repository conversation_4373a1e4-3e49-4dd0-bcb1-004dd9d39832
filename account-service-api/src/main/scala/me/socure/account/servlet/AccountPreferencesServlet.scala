package me.socure.account.servlet

import org.json4s.ext.EnumNameSerializer
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.service.{AccountPreferencesService, BusinessUserRoleService}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.{ProductSettingTraces, ProductSettingTracesFilter}
import me.socure.model.account.WatchlistPreference
import me.socure.model.dashboardv2.Creator
import me.socure.model.kyc.{KycNationalIdMatchLogic, KycPreferences, KycPreferencesRequest}
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

class AccountPreferencesServlet(val accountPreferencesService: AccountPreferencesService, businessUserRoleService: BusinessUserRoleService)(implicit val executor: ExecutionContext)
  extends BaseScalatraServlet with FutureSupport {
  override protected implicit def jsonFormats: Formats = JsonFormats.formats ++ List(
    new EnumNameSerializer(KycNationalIdMatchLogic)
  )

  get("/kyc/:environment_id") {
    val accountId = params("environment_id").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    val forceValidation = params.get("forceValidation") match {
      case None => false
      case Some(isForceValidation) => isForceValidation.toBoolean
    }
    forceValidation match {
      case true => ScalatraResponseFactory.get(accountPreferencesService.getKycPreference(accountId, creator))
      case false => ScalatraResponseFactory.get(accountPreferencesService.getKycPreference(accountId))
    }
  }

  post("/kyc/:environment_id") {
    val kycPreferences = parsedBody.extract[KycPreferences]
    val environmentId = params("environment_id").toLong
    ScalatraResponseFactory.getGeneric(accountPreferencesService.saveKycPreference(environmentId, kycPreferences))
  }

  get("/kyc/accounts/:environment_id") {
    val accountId = params("environment_id").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
     ScalatraResponseFactory.get(accountPreferencesService.getKycPreferenceForAccounts(accountId, creator))
  }

  post("/kyc/accounts/:environment_id") {
    val kycPreferencesRequest = parsedBody.extract[KycPreferencesRequest]
    val environmentId = params("environment_id").toLong
    ScalatraResponseFactory.getGeneric(accountPreferencesService.saveKycPreferenceForAccounts(environmentId, kycPreferencesRequest))
  }

  delete("/kyc/:environment_id") {
    val accountId = params("environment_id").toLong
    ScalatraResponseFactory.get(accountPreferencesService.deleteKycPreference(accountId))
  }

  get("/watchlist") {
    val accountId = params("environment_id").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    val forceValidation = params.get("forceValidation") match {
      case None => false
      case Some(isForceValidation) => isForceValidation.toBoolean
    }

    forceValidation match {
      case true => ScalatraResponseFactory.get(accountPreferencesService.getWatchlistPreference(accountId, creator))
      case false => ScalatraResponseFactory.get(accountPreferencesService.getWatchlistPreference(accountId))
    }
  }

  post("/watchlist") {
    val watchListPreferences = parsedBody.extract[WatchlistPreference]
    ScalatraResponseFactory.get(accountPreferencesService.saveWatchlistPreference(watchListPreferences))
  }

  get("/product_setting/traces/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(accountPreferencesService.getProductSettingTracesById(id))
  }

  get("/product_setting/traces") {
    val productSettingTracesFilter = parsedBody.extract[ProductSettingTracesFilter]
    ScalatraResponseFactory.get(accountPreferencesService.getProductSettingTraces(productSettingTracesFilter))
  }

  post("/product_setting/traces") {
    val productSettingTracesRequest = parsedBody.extract[ProductSettingTraces]
    ScalatraResponseFactory.get(accountPreferencesService.saveAndUpdateProductSettingTraces(productSettingTracesRequest))
  }

  get("/product_setting/audits/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(accountPreferencesService.getProductSettingAuditLogByPstId(id))
  }
}
