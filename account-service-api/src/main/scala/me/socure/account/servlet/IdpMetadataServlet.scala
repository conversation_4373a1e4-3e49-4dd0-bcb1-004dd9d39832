package me.socure.account.servlet

import me.socure.account.service.IdpMetadataService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext


class IdpMetadataServlet(idpMetadataService: IdpMetadataService)(implicit val executor :ExecutionContext) extends BaseScalatraServlet with FutureSupport {
  override protected implicit def jsonFormats :  Formats = JsonFormats.formats

  get("/list_idp_metadata") {
    ScalatraResponseFactory.get(idpMetadataService.listIdpMetadata())
  }

  get("/get_idp_metadata") {
    val entityId : String = params("entity_id")
    ScalatraResponseFactory.get(idpMetadataService.getIdpMetadata(entityId))
  }

  post("/insert_idp_metadata"){
    val accountId : Long = params("account_id").toLong
    val metadata : String = params("metadata")
    ScalatraResponseFactory.get(idpMetadataService.insertIdpMetadata(accountId, metadata))
  }

  post("/delete_idp_metadata"){
    val accountId : Long = params("account_id").toLong
    ScalatraResponseFactory.get(idpMetadataService.deleteIdpMetadata(accountId))
  }

  get("/get_idp_metadata_by_account") {
    val accountId : Long = params("account_id").toLong
    ScalatraResponseFactory.get(idpMetadataService.getIdpMetadata(accountId))
  }

}
