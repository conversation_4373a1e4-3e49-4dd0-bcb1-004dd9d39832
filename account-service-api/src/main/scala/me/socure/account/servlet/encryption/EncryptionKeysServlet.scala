package me.socure.account.servlet.encryption

import me.socure.account.service.EncryptionKeysService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.encryption.{AccountId, ApiKeyString}
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by jamesanto on 4/17/17.
  */
class EncryptionKeysServlet(
                             encryptionKeysService: EncryptionKeysService,
                             accountInfoService: AccountInfoService
                           )(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  post("/generate/:account_id") {
    val accountId = AccountId(params("account_id").toLong)
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.generate(accountId = accountId)
    )
  }

  post("/regenerate/:account_id") {
    val accountId = AccountId(params("account_id").toLong)
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.regenerate(accountId = accountId)
    )
  }

  get("/get/:account_id") {
    val accountId = AccountId(params("account_id").toLong)
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.getKeys(accountId = accountId)
    )
  }

  get("/has/:account_id") {
    val accountId = AccountId(params("account_id").toLong)
    ScalatraResponseFactory.getGeneric(
      encryptionKeysService.hasKeys(accountId = accountId)
    )
  }

  get("/get_account_id_by_api_key/:api_key") {
    val apiKeyString = ApiKeyString(params("api_key"))
    ScalatraResponseFactory.get(
      accountInfoService
        .getAccountIdByApiKey(apiKeyString)
        .map(Right(_))
    )
  }
}
