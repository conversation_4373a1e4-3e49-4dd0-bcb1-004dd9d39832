package me.socure.account.servlet

import me.socure.account.service.SubscriptionChannelRegistryService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, UpdateWebhookSecretKeyRotation}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class SubscriptionChannelRegistryServlet
  (val subscriptionChannelRegistryService: SubscriptionChannelRegistryService, val hmacVerifier: HMACHttpVerifier)
  (implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
  with FutureSupport
  with AuthenticationSupport {
  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/meta/types"){
    ScalatraResponseFactory.getGeneric(subscriptionChannelRegistryService.getChannelTypes())
  }

  get("/meta/subscriptiontypes"){
    ScalatraResponseFactory.getGeneric(subscriptionChannelRegistryService.getSubscriptionTypes())
  }

  get("/meta/communicationmodes"){
    ScalatraResponseFactory.getGeneric(subscriptionChannelRegistryService.getCommunicationModes())
  }

  get("/meta/actions"){
    ScalatraResponseFactory.getGeneric(subscriptionChannelRegistryService.getActions())
  }

  get("/:id") {
    val id = params("id").toLong
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId not found")).toLong
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.getSubscriptionChannelRegistry(id,Creator(0L,accountId)))
  }

  post("/") {
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.createSubscriptionChannelRegistry(parsedBody.extract[DtoSubscriptionChannelRegistry]))
  }

  post("/v2") {
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.createSubscriptionChannelRegistryV2(parsedBody.extract[DtoSubscriptionChannelRegistry]))
  }

  put("/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.updateSubscriptionChannelRegistry(id, parsedBody))
  }

  put("/v2/:id") {
    val id = params("id").toLong
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.updateSubscriptionChannelRegistryV2(id, parsedBody))
  }

  patch("/:id"){
    val id = params("id").toLong
    val actionName = (parsedBody\\ "action").extract[String]
    val creator = (parsedBody \\ "creator").extract[Creator]

    ScalatraResponseFactory.get(subscriptionChannelRegistryService.updateSubscriptionChannelState(id,actionName, creator))
  }

  patch("/v2/:id"){
    val id = params("id").toLong
    val actionName = (parsedBody\\ "action").extract[String]
    val creator = (parsedBody \\ "creator").extract[Creator]

    ScalatraResponseFactory.get(subscriptionChannelRegistryService.updateSubscriptionChannelStateV2(id,actionName, creator))
  }

  delete("/:id") {
    val id = params("id").toLong
    val creator = (parsedBody \\ "creator").extract[Creator]
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.deleteSubscriptionChannelRegistry(id,creator))
  }

  get("/environment/:environment_id") {
    val environmentId = params("environment_id").toLong
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId not found")).toLong
    ScalatraResponseFactory.get(
      subscriptionChannelRegistryService.getSubscriptionChannelRegistries(environmentId,Creator(0L,accountId))
    )
  }

  get("/v2/environment/:environment_id") {
    val environmentId = params("environment_id").toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not found")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not found")).toLong
    ScalatraResponseFactory.get(
      subscriptionChannelRegistryService.getSubscriptionChannelRegistriesV2(
        environmentId,
        Creator(creatorUserId, creatorAccountId)
      )
    )
  }

  get("/environment/:environment_id/subscription/:subscription_type_id") {
    val environmentId = params("environment_id").toLong
    val subscriptionTypeId = params("subscription_type_id").toLong
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId not found")).toLong
    ScalatraResponseFactory.get(
      subscriptionChannelRegistryService.getSubscriptionChannelRegistries(
        environmentId,
        subscriptionTypeId,
        accountId
      )
    )
  }

  get("/account/:account_id/environment/type/:environment_type_id/subscription/:subscription_type_id") {
    val accountId = params("account_id").toLong
    val environmentTypeId = params("environment_type_id").toLong
    val subscriptionTypeId = params("subscription_type_id").toLong
    val featureTypeId = params.getAs[Int]("featureType")
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId))
  }

  get("/environments/:environment_id/channels/is_active") {
    val accountId = params("accountId").toLong
    val envId = params("environment_id").toLong
    val subscriptionTypeId = params("subscriptionTypeId").toInt
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.hasActiveChannels(accountId, envId, subscriptionTypeId))
  }

  get("/v2/environments/:environment_id/channels/is_active") {
    val accountId = params("accountId").toLong
    val envId = params("environment_id").toLong
    val userId = params("userId").toLong
    val subscriptionTypeId = params("subscriptionTypeId").toInt
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.hasActiveChannelsV2(Creator(userId,accountId) , envId , subscriptionTypeId))
  }

  put("/account/:accountId/environment/type/:environmentTypeId/subscription/type/:subscriptionTypeId") {
    val requestPayload: Map[String, String] = parsedBody.extract[Map[String, String]]
    val secretKey = requestPayload.getOrElse("secret_key", halt(400, "secret_key not provided"))
    val accountId = params("accountId").toLong
    val environmentTypeId = params("environmentTypeId").toInt
    val subscriptionTypeId = params("subscriptionTypeId").toLong
    val featureTypeId = params.getAs[Int]("featureType")
    val result = subscriptionChannelRegistryService.updateSecretKeyForWatchlistWebhook(accountId, environmentTypeId, subscriptionTypeId, secretKey, featureTypeId)
    ScalatraResponseFactory.get(result)
  }

  get("/secret_key_sources/expired") {
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.getSecretKeyRotationDetails())
  }

  put("/secret_key_rotation") {
    val requestPayload: Seq[UpdateWebhookSecretKeyRotation] = parsedBody.extract[Seq[UpdateWebhookSecretKeyRotation]]
    ScalatraResponseFactory.get(subscriptionChannelRegistryService.updateSecretKeyRotation(requestPayload))
  }
}
