package me.socure.account.servlet

import me.socure.account.service.V2ValidationService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.dashboardv2.Creator
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> Kumar
 */
class V2ValidationServlet(v2ValidationService: V2ValidationService,
                          val hmacVerifier: HMACHttpVerifier)
                         (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    val permissions = params("permissions").split(",").toSet
    ScalatraResponseFactory.get(v2ValidationService.isPermissionAvailable(accountId, permissions, Creator(creatorUserId, creatorAccountId)))
  }

  get("/validate/environment/accessById") {
    val envId = params.get("env_id").getOrElse(halt(400, "env_id is not provided")).toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    val permissions = params("permissions").split(",").toSet
    ScalatraResponseFactory.get(v2ValidationService.isValidEnvironmentPermission(envId, permissions, Creator(creatorUserId, creatorAccountId)))
  }

  get("/validate/environment/accessByName") {
    val envName = params.get("env_name").getOrElse(halt(400, "env_name is not provided"))
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    val permissions = params("permissions").split(",").toSet
    ScalatraResponseFactory.get(v2ValidationService.isValidEnvironmentPermissionByEnvName(envName, accountId, permissions, Creator(creatorUserId, creatorAccountId)))
  }

  get("/validate/user_account_association/active") {
    val accountId = params.get("account_id").getOrElse(halt(400, "account_id is not provided")).toLong
    val userId = params.get("user_id").getOrElse(halt(400, "user_id is not provided")).toLong
    ScalatraResponseFactory.get(v2ValidationService.isValidActiveUserAccountAssociation(accountId, userId))
  }
}
