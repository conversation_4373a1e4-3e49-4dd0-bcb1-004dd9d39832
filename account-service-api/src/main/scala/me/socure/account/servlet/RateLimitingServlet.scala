package me.socure.account.servlet

import me.socure.account.service.RateLimitingService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{DeleteRateLimitingInput, SaveRateLimitingInput, UpdateRateLimitingInput}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class RateLimitingServlet (rateLimitingService: RateLimitingService, val hmacVerifier: HMACHttpVerifier)
  (implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
  with FutureSupport
  with AuthenticationSupport {
  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/") {
    val accountIdOpt = params.get("accountId")
    accountIdOpt match {
      case Some(accountId) => ScalatraResponseFactory.get(rateLimitingService.getRateLimits(accountId.toLong))
      case None => ScalatraResponseFactory.get(rateLimitingService.getRateLimits)
    }
  }

  get("/policies"){
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId is not provided")).toLong
    val environmentTypeId = params.get("environmentType").getOrElse(halt(400, "environmentType is not provided")).toLong
    val api = params.get("apiId").getOrElse(halt(400, "apiId is not provided"))
    ScalatraResponseFactory.getGeneric(rateLimitingService.getRateLimits(accountId, environmentTypeId, api))
  }

  get("/policies_v2"){
    val accountId = params.get("accountId").getOrElse(halt(400, "accountId is not provided")).toLong
    val environmentTypeId = params.get("environmentType").getOrElse(halt(400, "environmentType is not provided")).toLong
    val apiIds = params.get("apiIds").getOrElse(halt(400, "apiIds is not provided")).split(",").toSet[String].map(_.trim).filter(_.nonEmpty)
    if(apiIds.isEmpty) halt(400, "at least one apiId is required")
    ScalatraResponseFactory.getGeneric(rateLimitingService.getRateLimits(accountId, environmentTypeId, apiIds))
  }

  get("/apis") {
    ScalatraResponseFactory.getGeneric(rateLimitingService.getRateLimitingApis)
  }

  post("/") {
    val saveRateLimitingInput = parsedBody.extract [SaveRateLimitingInput]
    ScalatraResponseFactory.get(rateLimitingService.saveRateLimits(saveRateLimitingInput))
  }

  put("/") {
    val updateRateLimitingInput = parsedBody.extract[UpdateRateLimitingInput]
    ScalatraResponseFactory.get(rateLimitingService.updateRateLimits(updateRateLimitingInput))
  }

  delete("/") {
    val deleteRateLimitingInput = parsedBody.extract[DeleteRateLimitingInput]
    ScalatraResponseFactory.get(rateLimitingService.deleteRateLimit(deleteRateLimitingInput))
  }

  put("/upsert"){
    val updateRateLimitingInput = parsedBody.extract[Seq[UpdateRateLimitingInput]]
    ScalatraResponseFactory.get(rateLimitingService.upsertRateLimits(updateRateLimitingInput))
  }

  delete("/bulk") {
    val deleteRateLimitingInput = parsedBody.extract[Seq[DeleteRateLimitingInput]]
    ScalatraResponseFactory.get(rateLimitingService.deleteRateLimits(deleteRateLimitingInput))
  }

}

