package me.socure.account.servlet.account.automation

import me.socure.account.automation.AccountBundleAssociationService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.automation.AccountBundleAssociation
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class AccountBundleAssociationServlet (accountBundleAssociationService: AccountBundleAssociationService,
                                       val hmacVerifier: HMACHttpVerifier)
                                      (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(accountBundleAssociationService.getAccountBundleAssociation(accountId))
  }

  post("/") {
    val accountBundleAssociation = parsedBody.extract[AccountBundleAssociation]
    ScalatraResponseFactory.get(accountBundleAssociationService.upsertAccountBundleAssociation(accountBundleAssociation))
  }

}
