package me.socure.account.servlet

import me.socure.account.service.DvConfigurationService
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{DvConfiguration, DvConfigurationRequest}
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model.{DvConfigurationResponse, ErrorResponse}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DvConfigurationServlet(dvConfigurationService: DvConfigurationService,
                             val hmacVerifier: HMACHttpVerifier)
                            (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  private def generateEmptyAuditUser(): AuditDetails = {
    AuditDetails(false, ActionUserInfo(UserDetails(0L, "", "", "", ""), Set(), Seq()), None, None, Seq.empty)
  }

  get("/:environmentId") {
    try {
      val environmentId = params("environmentId").toLong
      val creatorUserId = params.get("creator_user_id")
      val creatorAccountId = params.get("creator_account_id")
      val creator = (creatorAccountId, creatorUserId) match {
        case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong , accountId.toLong))
        case (_, _) => None
      }
      val result: Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
        dvConfigurationService.listDvConfigurationByEnvironment(environmentId.toLong, creator) map {
          case dvConfigurationMap@Right(_) =>
            dvConfigurationMap
          case error@Left(_) =>
            error
        }
      }
      ScalatraResponseFactory.get(result)
    } catch {
      case t: Throwable =>
        logger.error("Some error occurred while fetching dv configuration", t)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponseFactory.get(CouldNotFetchDvConfiguration))))
    }
  }

  put("/:environmentId") {
    try {
      val dvConfigurationSeq = parsedBody.extract[Seq[DvConfiguration]]
      val environmentId = params("environmentId").toLong
      val creatorUserId = params.get("creator_user_id")
      val creatorAccountId = params.get("creator_account_id")
      val creator = (creatorAccountId, creatorUserId) match {
        case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong , accountId.toLong))
        case (_, _) => None
      }
      val result = {
        dvConfigurationService.saveEnvironmentDvConfiguration(dvConfigurationSeq, environmentId, creator) map {
          case (auditDetails, resp@Right(_)) => (auditDetails, resp)
          case (auditDetails, error@Left(_)) => (auditDetails, error)
        }
      }
      ScalatraResponseFactory.getGeneric(result)
    } catch {
      case t: Throwable =>
        logger.info("Some error occurred while saving dv configuration", t)
        ScalatraResponseFactory.getGeneric(Future.successful(generateEmptyAuditUser(),Left(ErrorResponseFactory.get(CouldNotSaveDvConfiguration))))
    }
  }

  get("/accounts/:environmentId") {
    try {
      val environmentId = params("environmentId").toLong
      val creatorUserId = params.get("creator_user_id")
      val creatorAccountId = params.get("creator_account_id")
      val creator = (creatorAccountId, creatorUserId) match {
        case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
        case (_, _) => None
      }
      val result: Future[Either[ErrorResponse, DvConfigurationResponse]] = {
        dvConfigurationService.listDvConfigurationForAccountsByEnvironment(environmentId.toLong, creator) map {
          case dvConfigurationMap@Right(_) =>
            dvConfigurationMap
          case error@Left(_) =>
            error
        }
      }
      ScalatraResponseFactory.get(result)
    } catch {
      case t: Throwable =>
        logger.error("Some error occurred while fetching dv configuration", t)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponseFactory.get(CouldNotFetchDvConfiguration))))
    }
  }

  put("/accounts/:environmentId") {
    try {
      val dvConfigurationRequest = parsedBody.extract[DvConfigurationRequest]
      val environmentId = params("environmentId").toLong
      val creatorUserId = params.get("creator_user_id")
      val creatorAccountId = params.get("creator_account_id")
      val creator = (creatorAccountId, creatorUserId) match {
        case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
        case (_, _) => None
      }
      val result = {
        dvConfigurationService.saveEnvironmentDvConfigurationForAccounts(dvConfigurationRequest, environmentId, creator) map {
          case (auditDetails, resp@Right(_)) => (auditDetails, resp)
          case (auditDetails, error@Left(_)) => (auditDetails, error)
        }
      }
      ScalatraResponseFactory.getGeneric(result)
    } catch {
      case t: Throwable =>
        logger.info("Some error occurred while saving dv configuration", t)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponseFactory.get(CouldNotSaveDvConfiguration))))
    }
  }
}
