package me.socure.account.servlet

import me.socure.account.service.UserAccountAssociationService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{UserAccountAssociationInput, UserAssociationInput}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * <AUTHOR> Kumar
 */
class UserAccountAssociationServlet(userAccountAssociationService: UserAccountAssociationService,
                                    val hmacVerifier: HMACHttpVerifier)
                                   (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  /**
   * Endpoint to fetch list of accounts with which a user is associated with.
   */
  get("/") {
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    ScalatraResponseFactory.get(userAccountAssociationService.getUserAccountAssociationsByUserId(userId.toLong))
  }

  /**
   * Endpoint to fetch list of accounts by email with which a user is associated with.
   */
  post("/email") {
    val email = parsedBody.extract[String]
    ScalatraResponseFactory.get(userAccountAssociationService.getUserAccountAssociationsByEmail(email))
  }

  /**
   * Endpoint to validate user account association and return UserAuthV2
   */
  post("/validate") {
    val userId = params.get("user_id").getOrElse(halt(400, "userId not provided"))
    val accountId: String = params.get("account_id").getOrElse(halt(400, "accountId not provided"))
    ScalatraResponseFactory.get(userAccountAssociationService.validateUserAccountAssociation(userId.toLong, accountId.toLong))
  }

  /**
   * Insert user account association
   */
  post("/") {
    val userAccountAssociationInput = parsedBody.extract[UserAccountAssociationInput]
    ScalatraResponseFactory.get(userAccountAssociationService.insertUserAccountAssociation(userAccountAssociationInput))
  }

  /**
   * Update user account association
   */
  put("/") {
    val userAccountAssociationInput = parsedBody.extract[UserAccountAssociationInput]
    ScalatraResponseFactory.get(userAccountAssociationService.updateUserAccountAssociation(userAccountAssociationInput))
  }

  /**
   * Insert user account associations
   */
  post("/bulk") {
    val accountId = params.get("account_id").getOrElse(halt(100, "account_id not found"))
    val updatedBy = params.get("updated_by").getOrElse(halt(100, "updated_by not found"))
    val userAssociationInputSeq = parsedBody.extract[Seq[UserAssociationInput]]
    ScalatraResponseFactory.get(userAccountAssociationService.insertUserAccountAssociations(accountId.toLong, userAssociationInputSeq, updatedBy.toLong).map(Right(_)))
  }

  /**
   * Delete User account association
   */
  post("/delete") {
    val userAccountAssociationInput = parsedBody.extract[UserAccountAssociationInput]
    ScalatraResponseFactory.get(userAccountAssociationService.deleteUserAccountAssociation(userAccountAssociationInput))
  }
}
