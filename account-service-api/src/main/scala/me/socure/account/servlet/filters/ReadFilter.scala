package me.socure.account.servlet.filters


import java.util.concurrent.TimeUnit

import javax.servlet.http.{HttpServletRequest, HttpServletResponse}
import javax.servlet.{Filter<PERSON>hain, FilterConfig, ServletRequest, ServletResponse}
import me.socure.dynamic.control.center.v2.response.EvaluateResponse
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import org.apache.http.HttpStatus
import org.scalatra.ScalatraFilter

import scala.concurrent.Await
import scala.concurrent.duration.Duration
import scala.util.Try

class ReadFilter(dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate) extends ScalatraFilter {

  val excludeCalls = Set("/account/v3/info/accounts/key/private", "/user/validate_v2", "/dashboard/domain/get_permission_domain_by_email",
    "/dashboard_user/list_users/by_permission", "/dashboard_user/get_user_details")

  override def init(filterConfig: FilterConfig): Unit = {

  }

  override def doFilter(request: ServletRequest, response: ServletResponse, chain: FilterChain): Unit = {
    val httpReq = request.asInstanceOf[HttpServletRequest]

    Try(Await.result(dynamicControlCenterV2Evaluate.evaluate(groupName = "UXFeatures", flagName = "Account_Service_Aurora_Upgrade"),
      Duration(5, TimeUnit.SECONDS)).right.get).toOption match {
      case Some(evaluateResponse: EvaluateResponse) if evaluateResponse.isFlagActive && !httpReq.getMethod.equalsIgnoreCase("get")
        && !excludeCalls.exists(api => httpReq.getRequestURI.contains(api)) => //fail write
        val httpRes = response.asInstanceOf[HttpServletResponse]
        httpRes.setStatus(HttpStatus.SC_BAD_REQUEST)
      case _ => super.doFilter(request, response, chain)
    }
  }
}
