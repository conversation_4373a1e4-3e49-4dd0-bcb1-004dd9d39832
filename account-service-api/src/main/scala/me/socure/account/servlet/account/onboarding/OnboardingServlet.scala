package me.socure.account.servlet.account.onboarding

import me.socure.account.onboarding.{OnboardingRequest, OnboardingService, OnboardingSubAccountRequest, OnboardingUserRequest, ProvisionRequest, WelcomeEmailRequest}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class OnboardingServlet  (onBoardingService: OnboardingService)
                         (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  post("/") {
    val onboardingRequest = parsedBody.extract[OnboardingRequest]
    ScalatraResponseFactory.get(onBoardingService.registration(onboardingRequest))
  }

  post("/subaccount") {
    val onboardingRequest = parsedBody.extract[OnboardingSubAccountRequest]
    ScalatraResponseFactory.get(onBoardingService.createSubAccount(onboardingRequest))
  }

  post("/provision") {
    val onboardingProvisionRequest = parsedBody.extract[ProvisionRequest]
    ScalatraResponseFactory.get(onBoardingService.provision(onboardingProvisionRequest))
  }

  post("/welcome-email") {
    val welcomeEmailRequest = parsedBody.extract[WelcomeEmailRequest]
    ScalatraResponseFactory.get(onBoardingService.sendWelcomeEmail(welcomeEmailRequest))
  }

  post("/user") {
    val onboardingUserRequest = parsedBody.extract[OnboardingUserRequest]
    ScalatraResponseFactory.get(onBoardingService.createUser(onboardingUserRequest))
  }

}
