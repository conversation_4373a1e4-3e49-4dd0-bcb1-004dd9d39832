package me.socure.account.servlet

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.ManageAccountsService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.superadmin.AccountCreationForm
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/25/17.
  */
class ManageAccountsServlet(manageAccountsService: ManageAccountsService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  post("/create_account") {
    val accountCreationForm = parsedBody.extract[AccountCreationForm]
    ScalatraResponseFactory.get(manageAccountsService.createAccountIfNotExists(accountCreationForm))
  }

  get("/parent_accounts") {
    ScalatraResponseFactory.get(manageAccountsService.getParentAccounts())
  }

  post("/activate_accounts") {
    val accountIds = parsedBody.extract[List[String]]
    ScalatraResponseFactory.get(manageAccountsService.activateAccounts(accountIds.map(_.trim).filter(_.length > 0).map(_.toLong)))
  }

  post("/update/account-name/by/public-id"){
    val publicId = params("publicId")
    val accountName = params("accountName")
    ScalatraResponseFactory.get(
      manageAccountsService.updateAccountNamebyPublicId(publicId = publicId, accountName = accountName)
    )
  }
}
