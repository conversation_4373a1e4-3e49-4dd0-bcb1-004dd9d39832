package me.socure.account.servlet

import me.socure.account.service.EINService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.ein.{EINRequest, LookupApiKeyRequest, LookupApiKeyServiceIdRequest}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

class EINServlet(einService: EINService,
                  val hmacVerifier: HMACHttpVerifier)
                  (implicit val executor : ExecutionContext)
                  extends BaseScalatraServlet
                  with FutureSupport
                  with AuthenticationSupport {

  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts.ein." + getClass.getSimpleName)

  before() {
  contentType = formats("json")
  validateRequest()
}

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)

  post("/") {
    try {
      val einRequest = parsedBody.extract[EINRequest]
      if(Option(einRequest.accountId).isEmpty ||
          Option(einRequest.ein).isEmpty) {
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, "AccountNumber and EIN are mandatory fields."))))
      } else {
        ScalatraResponseFactory.get(einService.upsertEIN(einRequest))
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.ein.request")
        logger.error("Failed to save EIN for account", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

  put("/") {
    try {
      val einRequest = parsedBody.extract[EINRequest]
      if(Option(einRequest.accountId).isEmpty ||
        Option(einRequest.ein).isEmpty) {
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, "AccountNumber and EIN are mandatory fields."))))
      } else {
        ScalatraResponseFactory.get(metrics.timeFuture("update.ein")(einService.upsertEIN(einRequest)))
      }
    } catch {
      case ex: Exception => {
        metrics.increment("invalid.ein.request")
        logger.error("Failed to update EIN for account", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
      }
    }
  }

  get("/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(metrics.timeFuture("fetch.ein")(einService.fetchEIN(accountId)))
  }

  get("/lookup-api-key/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(einService.fetchLookupKey(accountId))
  }

  post("/lookup-api-key") {
    try {
      val lookupApiKeyRequest = parsedBody.extract[LookupApiKeyRequest]
      if(Option(lookupApiKeyRequest.accountId).isEmpty ||
        Option(lookupApiKeyRequest.lookupApiKey).isEmpty) {
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, "AccountNumber and LookupApiKey are mandatory fields."))))
      } else {
        ScalatraResponseFactory.get(einService.upsertLookupApiKey(lookupApiKeyRequest, onlyWhen = true))
      }
    } catch {
      case ex: Exception =>
        metrics.increment("invalid.lookupApiKey.request")
        logger.error("Failed to save LookupApiKey for account", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
    }
  }

  get("/lookup-api-key-servicesid/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(einService.fetchLookupKeyServiceSid(accountId))
  }

  post("/lookup-api-key-servicesid") {
    try {
      val lookupApiKeyServiceIdRequest = parsedBody.extract[LookupApiKeyServiceIdRequest]
      if(Option(lookupApiKeyServiceIdRequest.accountId).isEmpty ||
        Option(lookupApiKeyServiceIdRequest.apiKey).isEmpty ||
        Option(lookupApiKeyServiceIdRequest.serviceId).isEmpty ) {
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, "AccountId, apiKey  and ServiceId are mandatory fields."))))
      } else {
        ScalatraResponseFactory.get(einService.upsertLookupApiKeyAndServiceSid(lookupApiKeyServiceIdRequest, onlyWhen = true))
      }
    } catch {
      case ex: Exception =>
        metrics.increment("invalid.lookupApiKeyServiceSid.request")
        logger.error("Failed to save LookupApiKeyServiceSid for account", ex)
        ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(400, ex.getMessage))))
    }
  }

}
