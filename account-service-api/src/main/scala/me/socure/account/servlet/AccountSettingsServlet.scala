package me.socure.account.servlet


import me.socure.account.service.AccountSettingService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{AccountDomain, SocialNetworkAppKeys}
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.Creator
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 02/05/16.
  */
class AccountSettingsServlet(val accountService: AccountSettingService)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/new/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(accountService.getNewAccountSetting(accountId))
  }

  post("/update_domain") {
    val accountDomain = parsedBody.extract[AccountDomain]
    ScalatraResponseFactory.get(accountService.updateDomain(accountDomain.accountId, accountDomain.domain))
  }

  post("/remove_social_appkey_config/:accountId/:provider") {
    val accountId = params("id").toLong
    val provider = params("provider")
    ScalatraResponseFactory.get(accountService.removeSocialNetworkkeys(accountId, provider))
  }

  post("/remove_appkey/:id") {
    val socialAppKeyId = params("id").toLong
    ScalatraResponseFactory.get(accountService.removeSocialNetworkkeys(socialAppKeyId))
  }

  post("/upsert_appkey") {
    val accountkeys = parsedBody.extract[SocialNetworkAppKeys]
    ScalatraResponseFactory.get(accountService.upsertSocialNetworkKey(accountkeys))
  }

  get("/get_environment_details/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(accountService.getEnvironmentByEmail(email))
  }


  //These are not used any more.

  post("/remove_account_cache/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(accountService.deleteAccountCache(accountId))
  }

  post("/remove_invidiual_cache/:individual_cache_id") {
    val individualCacheId = params("individual_cache_id").toLong
    ScalatraResponseFactory.get(accountService.deleteInvidiualCache(individualCacheId))
  }

  post("/remove_individual_cache_identifier") {
    val accountIndividual = parsedBody.extract[AccountIndividualCache]
    ScalatraResponseFactory.get(accountService.deleteInvidiualCache(accountIndividual.accountId, accountIndividual.identifier))
  }

  post("/update_account_cache") {
    val accountCache = parsedBody.extract[AccountOverallCache]
    ScalatraResponseFactory.get(accountService.upsertAccountCache(accountCache))
  }

  post("/upsert_individual_cache") {
    val accountIndividual = parsedBody.extract[AccountIndividualCache]
    ScalatraResponseFactory.get(accountService.upsertIndividualCache(accountIndividual))
  }

  get("/get_all_env") {
    ScalatraResponseFactory.get(accountService.getAllEnvironmentWithAccountDetails)
  }

  get("/modules/:account_id") {
    val accountId = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not found")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not found")).toLong
    ScalatraResponseFactory.get(accountService.getModulesByAccountId(accountId, Some(Creator(userId = creatorUserId, accountId = creatorAccountId)),forceValidate = true))
  }
}
