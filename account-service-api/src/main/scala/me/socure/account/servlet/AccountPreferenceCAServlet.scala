package me.socure.account.servlet

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.service.{AccountPreferencesService, BusinessUserRoleService, WatchlistSourceService}
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferenceRequest}
import me.socure.model.dashboardv2.Creator
import me.socure.model.{ErrorResponse, WatchlistSourceForEnvironment, WatchlistSourceRequest}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

class AccountPreferenceCAServlet (val accountPreferencesService: AccountPreferencesService,
                                  val watchlistSourceService: WatchlistSourceService,
                                  val businessUserRoleService: BusinessUserRoleService,
                                  val hmacVerifier: HMACHttpVerifier)
                                 (implicit val executor : ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  private val metrics: Metrics = JavaMetricsFactory.get("manage.accounts.ca." + getClass.getSimpleName)

  before() {
    contentType = "application/json"
    validateRequest()
  }

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)

  get("/watchlist/3.0") {
    val accountId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    val forceValidation = params.get("forceValidation") match {
      case None => false
      case Some(isForceValidation) => isForceValidation.toBoolean
    }

    forceValidation match {
      case true => ScalatraResponseFactory.get(accountPreferencesService.getCAWatchListPreference(accountId, creator))
      case false => ScalatraResponseFactory.get(accountPreferencesService.getCAWatchListPreference(accountId))
    }
  }

  get("/cawatchlist/3.0") {
    val environmentId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    val forceValidation = params.get("forceValidation") match {
      case None => false
      case Some(isForceValidation) => isForceValidation.toBoolean
    }
    forceValidation match {
      case true => ScalatraResponseFactory.get(accountPreferencesService.getCAWatchListPreferences(environmentId, creator))
      case false => ScalatraResponseFactory.get(accountPreferencesService.getCAWatchListPreferences(environmentId))
    }
  }

  get("/accounts/cawatchlist/3.0") {
    val environmentId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(accountPreferencesService.getCAWatchListPreferencesForAccounts(environmentId, creator))
  }

  get("/watchlist/sources/included") {
    val environmentId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id")
    val creatorAccountId = params.get("creator_account_id")
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId.toLong, accountId.toLong))
      case (_, _) => None
    }
    val forceValidation = params.get("forceValidation") match {
      case None => false
      case Some(isForceValidation) => isForceValidation.toBoolean
    }

    forceValidation match {
      case true => ScalatraResponseFactory.get(watchlistSourceService.includedWatchlistSources(environmentId, creator))
      case false => ScalatraResponseFactory.get(watchlistSourceService.includedWatchlistSources(environmentId))
    }
  }

  post("/watchlist/3.0") {
    val caWatchListPreferences = parsedBody.extract[CAWatchlistPreference]
    ScalatraResponseFactory.getGeneric(accountPreferencesService.saveCAWatchlistPreference(caWatchListPreferences))
  }

  post("/watchlist/sources/include") {
    val watchlistSourceForEnvironment = parsedBody.extract[WatchlistSourceForEnvironment]
    ScalatraResponseFactory.getGeneric(metrics.timeFuture("include.watchlist.sources")(watchlistSourceService.includeWatchlistSource(watchlistSourceForEnvironment)))
  }

  post("/accounts/watchlist/3.0") {
    val caWatchListPreferencesRequest = parsedBody.extract[CAWatchlistPreferenceRequest]
    ScalatraResponseFactory.getGeneric(accountPreferencesService.saveCAWatchlistPreference(caWatchListPreferencesRequest))
  }

  post("/accounts/watchlist/sources/include") {
    val watchlistSourceRequest = parsedBody.extract[WatchlistSourceRequest]
    ScalatraResponseFactory.getGeneric(metrics.timeFuture("include.watchlist.sources")(watchlistSourceService.includeWatchlistSource(watchlistSourceRequest)))
  }

  get("/watchlist/sources") {
    ScalatraResponseFactory.get(metrics.timeFuture("fetch.watchlist.sources")(watchlistSourceService.list))
  }

  get("/watchlist/sources/category/:categoryId") {
    metrics.increment("fetch.watchlist.sources.by.category.count")
    val categoryId = params("categoryId").toInt
    ScalatraResponseFactory.get(metrics.timeFuture("fetch.watchlist.sources.by.category")(watchlistSourceService.listByCategory(categoryId)))
  }

  post("/watchlist/sources/import") {
    metrics.increment("import.watchlist.sources.csv.count")
    val fileContent = request.body
    val resultFuture = watchlistSourceService.importWatchlistSources(fileContent).map(watchlistSource => {
      Right(watchlistSource)
    }) recover {
      case ex => logger.info("Error while importing watchlist sources",ex)
        Left(ErrorResponse(ExceptionCodes.InternalError.id, ex.getMessage))
    }
    ScalatraResponseFactory.get(metrics.timeFuture("import.watchlist.sources.csv")(resultFuture))
  }

  get("/historicalrange") {
    ScalatraResponseFactory.getGeneric(accountPreferencesService.getHistoricalRange())
  }

}
