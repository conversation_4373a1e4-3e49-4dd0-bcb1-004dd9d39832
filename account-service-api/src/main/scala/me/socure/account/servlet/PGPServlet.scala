package me.socure.account.servlet

import me.socure.account.service.{AccountPgpKeysService, PgpDecAndSigKeysService, PgpSignaturePublicKeyService}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/15/17.
  */
class PGPServlet(
                  accountPgpKeysService: AccountPgpKeysService,
                  pgpSignaturePublicKeyService: PgpSignaturePublicKeyService,
                  pgpDecAndSigKeysService: PgpDecAndSigKeysService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {
  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/get_dec_and_sig_keys/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(pgpDecAndSigKeysService.getPgpDecAndSigKeys(accountId))
  }

  get("/get_all_dec_and_sig_keys/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(pgpDecAndSigKeysService.getAllPgpDecAndSigKeys(accountId))
  }

  get("/get_public_key/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(accountPgpKeysService.getAccountPgpPublicKey(accountId))
  }

  post("/create_pgp_keys"){
    val accountId : Long = params("account_id").toLong
    val expiryInSeconds = params.get("expiryInSeconds").map(_.toLong)
    ScalatraResponseFactory.get(accountPgpKeysService.createPgpKeys(accountId, expiryInSeconds))
  }

  get("/get_active_pgp_account_list") {
    ScalatraResponseFactory.get(accountPgpKeysService.getActivePgpAccountList())
  }

  get("/get_active_account_wo_pgp_list") {
    ScalatraResponseFactory.get(accountPgpKeysService.getActivePgpAccountWOPgpList())
  }

  post("/deactivate_pgp_keys"){
    val accountId : Long = params("account_id").toLong
    ScalatraResponseFactory.get(accountPgpKeysService.dactivatePgpKeys(accountId))
  }

  get("/get_accounts_with_signature_public_key") {
    ScalatraResponseFactory.get(pgpSignaturePublicKeyService.getAccountsWithSignaturePublicKeys())
  }

  get("/get_pgp_signature_public_key") {
    val accountId : Long = params("account_id").toLong
    ScalatraResponseFactory.get(pgpSignaturePublicKeyService.getPgpSignaturePublicKey(accountId))
  }

  get("/does_pgp_key_exists/:account_id") {
    val accountId: Long  = params("account_id").toLong
    val result = accountPgpKeysService.doesPgpKeyExists(accountId)
    ScalatraResponseFactory.get(result)
  }

  post("/insert_pgp_signature_public_key"){
    val accountId : Long = params("account_id").toLong
    val publicKey : String = params("publicKey")
    ScalatraResponseFactory.get(pgpSignaturePublicKeyService.insertPgpSignaturePublicKey(accountId, publicKey))
  }

  post("/delete_pgp_signature_public_key"){
    val accountId : Long = params("account_id").toLong
    ScalatraResponseFactory.get(pgpSignaturePublicKeyService.deletePgpPublicKey(accountId))
  }
}
