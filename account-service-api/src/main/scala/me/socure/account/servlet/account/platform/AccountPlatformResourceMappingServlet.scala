package me.socure.account.servlet.account.platform

import me.socure.account.platform.AccountPlatformResourceMappingService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.platform.resource.mapping.AccountPlatformResourceMapping
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class AccountPlatformResourceMappingServlet (accountPlatformResourceMappingService: AccountPlatformResourceMappingService)
                                            (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/:accountId/associations") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(accountPlatformResourceMappingService.getAccountPlatformResourceMapping(accountId))
  }

  post("/:accountId/associations") {
    val accountId = params("accountId").toLong
    val accountPlatformResourceMapping = parsedBody.extract[AccountPlatformResourceMapping]
    ScalatraResponseFactory.get(accountPlatformResourceMappingService.saveAccountPlatformResourceMapping(accountId, accountPlatformResourceMapping))
  }

}
