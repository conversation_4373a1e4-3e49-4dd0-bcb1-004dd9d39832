package me.socure.account.servlet

import me.socure.account.service.ConfigureAccountService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.SubAccountCreationRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

class ConfigureAccountServlet(configureAccountService: ConfigureAccountService, val hmacVerifier: HMACHttpVerifier)
                             (implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  override val logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  post("/create") {
    val subAccountCreationRequest = parsedBody.extract[SubAccountCreationRequest]
    ScalatraResponseFactory.publicGet(
      configureAccountService.createSubAccount(subAccountCreationRequest)
    )
  }

}

