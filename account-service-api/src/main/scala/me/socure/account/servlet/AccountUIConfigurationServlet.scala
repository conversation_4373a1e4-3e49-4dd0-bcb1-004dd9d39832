package me.socure.account.servlet

import me.socure.account.service.{AccountHierarchyService, AccountUIConfigurationService}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.validator.V2Validator
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.{AccountManagementDefaults, AccountTypes, JsonFormats}
import me.socure.model.dashboardv2.Creator
import me.socure.model.{AccountUIConfigurationRequest, AccountsUIConfigurationRequest, ErrorResponse}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountUIConfigurationServlet(uIAccountConfigurationService: AccountUIConfigurationService,
                                    val hmacVerifier: HMACHttpVerifier,
                                    val sessionIdleTimeout: Int,
                                    val autoTimeout: Int,
                                    v2Validator: V2Validator)(implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with AuthenticationSupport {

  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/:accountId") {
    val accountId = params.get("accountId").getOrElse(halt(400, "account_id not provided")).toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    ScalatraResponseFactory.publicGet(
      uIAccountConfigurationService.getUIAccountConfiguration(accountId, Creator(creatorUserId, creatorAccountId))
    )
  }

  post("/") {
    val uIAccountConfigurationRequest = parsedBody.extract[AccountUIConfigurationRequest]
    val creatorUserId = params.get("creator_user_id").map(_.toLong).getOrElse(halt(400, "creator_user_id is not provided"))
    val creatorAccountId = params.get("creator_account_id").map(_.toLong).getOrElse(halt(400, "creator_account_id is not provided"))

    val rootParent = v2Validator.getRootParentAccountType(creatorAccountId)

    def validateTimeouts(autoTimeoutLimit: Int, idleTimeoutLimit: Int): Either[ErrorResponse, Unit] = {
      if (uIAccountConfigurationRequest.autoTimeoutInMinutes < uIAccountConfigurationRequest.idleTimeoutInMinutes)
        Left(ErrorResponseFactory.get(ExceptionCodes.IdleTimeoutGreaterThanAutoTimeout))
      else if (uIAccountConfigurationRequest.autoTimeoutInMinutes < 1 || uIAccountConfigurationRequest.autoTimeoutInMinutes > autoTimeoutLimit)
        Left(ErrorResponseFactory.get(ExceptionCodes.AutoTimeoutExceeded))
      else if (uIAccountConfigurationRequest.idleTimeoutInMinutes < 1 || uIAccountConfigurationRequest.idleTimeoutInMinutes > idleTimeoutLimit)
        Left(ErrorResponseFactory.get(ExceptionCodes.IdleTimeoutExceeded))
      else
        Right(())
    }

    rootParent.map {
      case Some(accountType) =>
        val (autoTimeoutLimit, idleTimeoutLimit) = accountType match {
          case AccountTypes.DIRECT_EFFECTIV.id => (1440, 480)
          case _ => (autoTimeout, sessionIdleTimeout)
        }

        validateTimeouts(autoTimeoutLimit, idleTimeoutLimit) match {
          case Left(errorResponse) =>
            ScalatraResponseFactory.publicGet(Future.successful(Left(errorResponse)))
          case Right(_) =>
            ScalatraResponseFactory.getGeneric(
              uIAccountConfigurationService.saveUIAccountConfiguration(
                uIAccountConfigurationRequest,
                Creator(creatorUserId, creatorAccountId)
              )
            )
        }

      case None =>
        ScalatraResponseFactory.publicGet(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.CouldNotUpdateUIAccountConfiguration))))
    }
  }

  get("/accounts/:accountId") {
    val accountId = params.get("accountId").getOrElse(halt(400, "account_id not provided")).toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    ScalatraResponseFactory.publicGet(
      uIAccountConfigurationService.getUIAccountConfiguration(accountId, Creator(creatorUserId, creatorAccountId))
    )
  }

  post("/accounts") {
    val uIAccountConfigurationRequest = parsedBody.extract[AccountsUIConfigurationRequest]
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    ScalatraResponseFactory.getGeneric(
      uIAccountConfigurationService.validateAndSaveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest, Creator(creatorUserId, creatorAccountId), autoTimeout, sessionIdleTimeout)
    )
  }

}
