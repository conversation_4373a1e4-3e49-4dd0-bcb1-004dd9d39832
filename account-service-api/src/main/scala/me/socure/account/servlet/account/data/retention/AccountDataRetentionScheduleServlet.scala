package me.socure.account.servlet.account.data.retention

import me.socure.account.data.retention.AccountDataRetentionScheduleService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.data.retention.UpdateAccountDataRetentionSchedule
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class AccountDataRetentionScheduleServlet(accountDataRetentionScheduleService: AccountDataRetentionScheduleService,
                                          val hmacVerifier: HMACHttpVerifier)
                                         (implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    validateRequest()
  }

  get("/") {
    ScalatraResponseFactory.get(accountDataRetentionScheduleService.getAccountDataRetentionSchedule())
  }

  get("/account/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(accountDataRetentionScheduleService.getAccountDataRetentionSchedule(accountId))
  }

  get("/account-hierarchy/:accountId") {
    val accountId = params("accountId").toLong
    ScalatraResponseFactory.get(accountDataRetentionScheduleService.getAccountDataRetentionScheduleWithHierarchy(accountId))
  }

  post("/account/:accountId") {
    val accountId = params("accountId").toLong
    val updateAccountDataRetentionSchedule = parsedBody.extract[UpdateAccountDataRetentionSchedule]
    ScalatraResponseFactory.get(accountDataRetentionScheduleService.upsertAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule))
  }

}
