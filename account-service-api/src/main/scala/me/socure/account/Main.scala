package me.socure.account

import java.util.concurrent.{TimeUnit, TimeoutException}
import com.google.common.util.concurrent.ServiceManager.Listener
import com.google.common.util.concurrent.{MoreExecutors, Service, ServiceManager}
import me.socure.common.config.EnvironmentConfigurationProvider
import me.socure.common.environment._
import me.socure.common.jmx.SocureMicroJMXService
import me.socure.common.logs.EnvironmentLogConfigurationLoader
import org.slf4j.LoggerFactory
import me.socure.common.microservice.defaults.DefaultMicroservice

import scala.collection.JavaConverters._

/**
  * Created by alexand<PERSON> on 5/9/16.
  */
object Main extends DefaultMicroservice {

  private val logger = LoggerFactory.getLogger("Main")

  def startUp(args: Array[String]): Unit = {
    val appName = AppNameResolver.resolve
    val environment = EnvironmentResolver.resolve()

    val logConfigurationLoader = new EnvironmentLogConfigurationLoader
    logConfigurationLoader.get(environment, AppName("account-service"))

    logger.info("Environment: " + environment)
    val configurationLoaderProvider = new EnvironmentConfigurationProvider
    val configurationLoader = configurationLoaderProvider.get(environment, appName)
    val config = configurationLoader.load(environment)


    val accountServiceFactory = new AccountServiceFactory(config)
    val service = accountServiceFactory.get()
    val jmxService: SocureMicroJMXService = new SocureMicroJMXService()

    val services = Set(service, jmxService)
    val manager = new ServiceManager(services.asJava)

    manager.addListener(new Listener() {
      override def stopped(): Unit = {
        logger.info("Account Service Stopped, dont know why")
      }

      override def healthy(): Unit = {
        // Services have been initialized and are healthy, start accepting requests...
        logger.info("Account Service Started Successfully")
      }

      override def failure(service: Service): Unit = {
        //TODO:  log, notify a load balancer, or some other action.  For now we will just exit. (Terminate jvm)
        logger.info("Account Service Start: Failed", service.failureCause())
        System.exit(1)
      }
    }, MoreExecutors.directExecutor())

    sys.addShutdownHook {
      try {
        manager.stopAsync().awaitStopped(5, TimeUnit.SECONDS);
      } catch {
        case e: TimeoutException ⇒ logger.info("Account Service Shutdown(TimeoutException) ",e)
        case ex:Throwable => logger.info("Account Service Shutdown ",ex)
      }
    }

    manager.startAsync(); // start all the services asynchronously
  }

}
