package me.socure.account

import com.amazonaws.regions.Regions
import com.typesafe.config.Config
import me.socure.model.encryption.{KmsId, KmsIdsConfig}
import me.socure.model.pgp.PgpKmsConfig

import scala.collection.JavaConverters._

/**
  * Created by gopal on 16/05/2017.
  */
object PgpKmsKeysConfigFactory {

  private def getKmsKeysConfig(config : Config) = {
    KmsIdsConfig(config
      .entrySet()
      .asScala
      .map { k =>
        Regions.fromName(k.getKey) -> KmsId(k.getValue.unwrapped().toString)
      }.toMap
    )
  }
  def get(config : Config) = {

    PgpKmsConfig(
      kmsId = getKmsKeysConfig(config.getConfig("kms.id"))
    )
  }

}
