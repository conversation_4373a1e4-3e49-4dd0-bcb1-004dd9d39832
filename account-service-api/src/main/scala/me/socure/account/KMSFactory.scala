package me.socure.account

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.regions.Regions
import com.amazonaws.services.kms.AWSKMSAsyncClientBuilder
import me.socure.common.kms.KmsService
import me.socure.model.encryption.KmsIdsConfig
import org.slf4j.LoggerFactory

/**
  * Created by a<PERSON>hishe<PERSON> on 11/16/2021.
  */
object KMSFactory {

  private val logger = LoggerFactory.getLogger(getClass)

  def get(config: KmsIdsConfig) : KmsService = {
    val awsCredentialsProvider = new DefaultAWSCredentialsProviderChain
    val awsKmsAsync = AWSKMSAsyncClientBuilder
        .standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(config.value.keys.headOption.getOrElse(throw new Exception("Bad KMS configuration provided : keys not found")))
        .build()
      new KmsService(awsKmsAsync)
  }

  //Creating KmsService for given region
  def createKmsService(region: Regions): KmsService = {
    try {
      val awsCredentialsProvider = new DefaultAWSCredentialsProviderChain
      val awsKmsAsync = AWSKMSAsyncClientBuilder
        .standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(region)
        .build()
      new KmsService(awsKmsAsync)
    } catch {
      case exception: Exception =>
        logger.error(s"Exception while creating KmsService for the region ${region.getName}", exception)
        throw exception
    }

  }

  //Creating KmsService for all unique regions
  def get(config: KmsIdsConfig, multiRegionKmsConfig: KmsIdsConfig): Map[Regions, KmsService] = {
    val allRegions = config.value.keySet ++ multiRegionKmsConfig.value.keySet
    allRegions.map(region => (region, createKmsService(region))).toMap
  }
}