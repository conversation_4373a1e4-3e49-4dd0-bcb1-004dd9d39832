package me.socure.account

import java.util.concurrent.TimeUnit

import com.typesafe.config.Config
import me.socure.configuration.BadLoginConfig

import scala.concurrent.duration.FiniteDuration

/**
  * Created by <PERSON><PERSON><PERSON> on 6/24/16.
  */
object BadLoginConfigFactory{

  def get(config : Config) : BadLoginConfig = {
    val maxTry = config.getInt("maxtry")
    val timeUnit = config.getString("timeunit")
    val duration = config.getLong("duration")
    val reset = config.getBoolean("autoreset")

    BadLoginConfig(maxTry, reset, FiniteDuration(duration, TimeUnit.SECONDS)) //TODO:TimeUnit should be fixed

  }

}
