package me.socure.account

import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.services.kms.AWSKMSAsyncClientBuilder
import me.socure.common.kms.KmsService
import me.socure.model.pgp.PgpKmsConfig

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/16/17.
  */
object KMSFactoryForPGP {

  def get(config : PgpKmsConfig) : KmsService = {

    val awsCredentialsProvider = new DefaultAWSCredentialsProviderChain
    val awsKmsAsync = AWSKMSAsyncClientBuilder
        .standard()
        .withCredentials(awsCredentialsProvider)
        .withRegion(config.kmsId.value.keys.headOption.getOrElse(throw new Exception("Bad KMS configuration provided : keys not found")))
        .build()
      new KmsService(awsKmsAsync)
  }
}
