package me.socure.account

import java.util.{EnumSet => JEnumSet}
import com.google.common.util.concurrent._
import dispatch.Http

import javax.crypto.spec.SecretKeySpec
import javax.servlet.DispatcherType
import javax.sql.DataSource
import me.socure.account.audit.AccountAuditService
import me.socure.account.automation.{AccountAutomationService, AccountBundleAssociationService, BundleManagementService}
import me.socure.account.basic.{BasicEngine, BasicServlet}
import me.socure.account.blacklist.{BlackListService, BlackListServlet}
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.account.dashboard.DashboardUserService
import me.socure.account.dashboardv2.{DashboardAccountServiceV2, DashboardUserServiceV2, EnvironmentSettingsService, IDMService}
import me.socure.account.data.retention.AccountDataRetentionScheduleService
import me.socure.account.etl.EnvironmentUpdaterService
import me.socure.account.idplus.engine.IdPlusEngine
import me.socure.account.idplus.servlet.{IdPlusServlet, IdPlusV2Servlet}
import me.socure.account.industries.IndustriesManagementService
import me.socure.account.onboarding.OnboardingService
import me.socure.account.platform.AccountPlatformResourceMappingService
import me.socure.account.prospect.ProspectService
import me.socure.account.saml.SamlValidator
import me.socure.account.service._
import me.socure.account.service.common.{AccInfoCacheKeyProvider, CacheKeyProvider}
import me.socure.account.servlet._
import me.socure.account.servlet.account.automation.{AccountAutomationServlet, AccountBundleAssociationServlet}
import me.socure.account.servlet.account.data.retention.AccountDataRetentionScheduleServlet
import me.socure.account.servlet.account.onboarding.OnboardingServlet
import me.socure.account.servlet.account.platform.AccountPlatformResourceMappingServlet
import me.socure.account.servlet.account.sponsorbank.SponsorBankServlet
import me.socure.account.servlet.account.prospect.ProspectServlet
import me.socure.account.servlet.encryption.{EncryptionKeysServlet, EncryptionKeysServletV2}
import me.socure.account.servlet.filters.ReadFilter
import me.socure.account.sponsorbank.SponsorBankService
import me.socure.account.superadmin._
import me.socure.account.validator.{SubscriptionChannelValidator, V2Validator}
import me.socure.batchjob.{AccountManagementService, BatchJobServlet}
import me.socure.common.check.impl.db._
import me.socure.common.check.impl.http._
import me.socure.common.check.servlet.api.CheckerServlet
import me.socure.common.clock.Clock
import me.socure.common.encryption.{SocurePBEConfig, SocurePBEStringEncryptor}
import me.socure.common.healthcheck.servlet.HealthCheckServlet
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.config.HMACConfig
import me.socure.common.hmac.factory.{HMACEncrypterFactory, NonceCacheServiceFactory}
import me.socure.common.hmac.verifier.HMACHttpVerifier
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.metrics.models.MetricTags
import me.socure.common.pgp.PgpKeyPairGenerator
import me.socure.common.servlet.metrics.ServletMetricsFilter
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.configuration._
import me.socure.constants.AccountProvisioningConfiguration
import me.socure.control.center.client.ControlCenterServiceClient
import me.socure.control.center.{ControlCenterService, ControlCenterServlet}
import me.socure.dashboard.DashboardUserServlet
import me.socure.dashboardv2._
import me.socure.decision.service.client.DecisionServiceClientV2
import me.socure.document.manager.client.DocumentManagerClient
import me.socure.docv.client.DocvOrchestraClient
import me.socure.dynamic.control.center.v2.service.DynamicControlCenterV2Evaluate
import me.socure.etl.EnvironmentUpdaterServlet
import me.socure.industry.IndustryServlet
import me.socure.mail.client.MailNotificationClient
import me.socure.mail.model.PageConfiguration
import me.socure.mail.service.MailNotificationService
import me.socure.model.encryption.KmsIdsConfig
import me.socure.model.idp.metadata.IdpMetadataConfig
import me.socure.model.management.client.ModelManagementClient
import me.socure.model.pgp.PgpKmsConfig
import me.socure.salt.client.SaltClient
import me.socure.scheduler.ScheduledActivitiesServlet
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account.mappers.{CAWatchlistPreferenceMapper, WatchlistPreferenceMapper}
import me.socure.storage.slick.tables.user.DaoBusinessUser
import me.socure.superadmin._
import me.socure.user.MagicLinkAuditService
import me.socure.user.servelt.{PasswordServlet, UserManagementServlet}
import me.socure.utils.DBProxyWithMetrics
import me.socure.{AccountServiceHealthChecker, DaoAccount}
import org.eclipse.jetty.server.{Server, ServerConnector}
import org.eclipse.jetty.servlet.{FilterHolder, ServletContextHandler, ServletHolder}
import org.eclipse.jetty.util.thread.ThreadPool
import org.flywaydb.core.Flyway
import org.flywaydb.core.api.MigrationState
import org.flywaydb.core.internal.info.MigrationInfoImpl
import org.slf4j.LoggerFactory
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 25/04/16.
  */
class AccountService(dataSource: DataSource,
                     dataSourceReplica: DataSource,
                     threadPool: ThreadPool,
                     port: Int,
                     withDBMigration: Boolean,
                     badLoginConfig: BadLoginConfig,
                     clock: Clock,
                     apiKeyRenewalConfig : ApiKeyRenewalConfig,
                     scheduledActConfig : ScheduledActivityConfig,
                     encryptionKeysConfig: EncryptionKeysConfig,
                     passwordExpiresDays : Int,
                     pgpKmsConfig : PgpKmsConfig,
                     payloadKmsConfig: KmsIdsConfig,
                     mailClient: MailNotificationClient,
                     pageConfig : (PageConfiguration, PageConfiguration, PageConfiguration),
                     idpMetadataConfig: IdpMetadataConfig,
                     supportMailId: String,
                     customerSuccessMailId: Option[String],
                     saltClient: SaltClient,
                     scalaCache: ScalaCache[_],
                     hmacConfig: HMACConfig,
                     pbePassword: String,
                     auditRequestEncipher: AuditRequestEncipher,
                     sessionIdleTimeout: Int,
                     autoTimeout: Int,
                     controlCenterServiceClient: ControlCenterServiceClient,
                     modelManagementClient: ModelManagementClient,
                     dynamicControlCenterV2Evaluate: DynamicControlCenterV2Evaluate,
                     payloadMultiRegionKmsConfig: KmsIdsConfig,
                     permissionsToBeOverriddenByChild: Set[Integer],
                     secretKeyExpiryCheck: Int,
                     decisionServiceClientV2: DecisionServiceClientV2,
                     pgpKeyExpiryDuration: Long,
                     documentManagerClient: DocumentManagerClient,
                     accountProvisioningConfig: AccountProvisioningConfiguration,
                     docvOrchestraClient: DocvOrchestraClient,
                     serverMetricsEnabled: Boolean,
                     dbMetricsEnabled: Boolean,
                     analyticsConfig: AnalyticsConfig,
                     aiGatewayServiceConfig: AIGatewayServiceConfig,
                     whitelistedEmailDomain: Set[String]
                    )(implicit ec: ExecutionContext) extends AbstractIdleService {

  private val logger = LoggerFactory.getLogger(classOf[AccountService])

  val server = new Server(threadPool)
  val connector = new ServerConnector(server)
  connector.setPort(port)
  server.addConnector(connector)

  private val driver = slick.driver.MySQLDriver

  override def shutDown(): Unit = {
    logger.info("Terminating account service...")
    server.stop()
    logger.info("Account service terminated")
  }

  override def startUp(): Unit = {

    logger.info("Starting account service...")
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = driver,
        dataSource = dataSourceReplica
      )
    )

    val db = JdbcBackend.Database.forDataSource(dataSource)

    def repair(flyway: Flyway): Unit = {
      {
        val dbSession = db.createSession()
        val migrationInfo = flyway.info()
        migrationInfo.all().foreach{ migrationState =>
          val appliedVersion = migrationState.asInstanceOf[MigrationInfoImpl].getAppliedMigration
          if(migrationState.getState.asInstanceOf[MigrationState].isResolved){
            val resolvedVersion = migrationState.asInstanceOf[MigrationInfoImpl].getResolvedMigration
            if(migrationState.getState.asInstanceOf[MigrationState].isApplied && appliedVersion.getChecksum != resolvedVersion.getChecksum) {
              logger.info(s"** Checksum mismatch - ${migrationState.getDescription}, AppliedVersion: ${appliedVersion.getChecksum}, ResolvedVersion: ${resolvedVersion.getChecksum} **")
              logger.info("Updating Checksum....")
              val updateQuery = s"UPDATE schema_version SET checksum=${resolvedVersion.getChecksum} where installed_rank=${migrationState.getInstalledRank};"
              logger.info(updateQuery)
              dbSession.prepareStatement(updateQuery).execute()
            }
            else{
              logger.info(s"No touch required.. All good with script - ${migrationState.getDescription}, ")
            }
          }else{
            logger.info(s"** Unresolved Script - ${appliedVersion.getDescription} **")
            logger.info("Deleting ....")
            val deleteQuery = s"DELETE FROM schema_version WHERE installed_rank=${migrationState.getInstalledRank};"
            logger.info(deleteQuery)
            dbSession.prepareStatement(deleteQuery).execute()
          }
        }
        dbSession.close()
      }
    }

    if (withDBMigration) {
      logger.info("Applying DB Migration scripts")
      val flyway = new Flyway()
      flyway.setDataSource(dataSource)
      flyway.setBaselineOnMigrate(true)
      //repair(flyway)
      flyway.migrate()
    }
    
    val pbeEncryptor = new SocurePBEStringEncryptor()
    pbeEncryptor.setConfig(new SocurePBEConfig(pbePassword))

    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave, dbMetricsEnabled)

    val daoAccount = new DaoAccount(dbProxyWithMetrics, driver, Some(analyticsConfig))
    val daoKYCPreferences = new DaoKycPreferences(dbProxyWithMetrics, driver)
    val daoWatchlistPreferences = new DaoWatchlistPreferences(dbProxyWithMetrics, driver)
    val daoComplyWatchlistPreferences = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, driver)
    val daoPgpSignaturePublicKeys = new DaoPgpSignaturePublicKeys(dbProxyWithMetrics, driver)
    val daoIdpMetadata = new DaoIdpMetadata(dbProxyWithMetrics, driver)
    val daoPublicKeyCertificate = new DaoPublicWebhook(dbProxyWithMetrics, driver)
    val daoSubscriptionChannelRegistry = new DaoSubscriptionChannelRegistry(dbProxyWithMetrics, driver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, driver)
    val daoRateLimit = new DaoRateLimit(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoRateLimits = new DaoRateLimit(dbProxyWithMetrics, driver)
    val daoUIAccountConfiguration = new DaoAccountUIConfiguration(dbProxyWithMetrics, driver)
    val v2Validator = new V2Validator(daoAccountV2)
    val accountService = new AccountSettingService(daoAccount, apiKeyRenewalConfig=apiKeyRenewalConfig, v2Validator = v2Validator, clock=clock)
    val daoSubscriptionType = new DaoSubscriptionType(dbProxyWithMetrics, driver)
    val daoAccountSftpUserService = new DaoAccountSftpUser(dbProxyWithMetrics, driver)
    val daoSubscriptionStatus = new DaoSubscriptionStatus(dbProxyWithMetrics, driver)

    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, driver)

    val daoDvConfiguration = new DaoDvConfiguration(dbProxyWithMetrics, driver)

    val handler = new ServletContextHandler()
    val daoBusinessUser = new DaoBusinessUser(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, driver)
    val daoAccountPgpKeys = new DaoAccountPgpKeys(dbProxyWithMetrics, driver)
    val daoWatchlistSource = new DaoWatchlistSource(dbProxyWithMetrics, driver)
    val daoMLAFields = new DaoMLAFields(dbProxyWithMetrics, driver)
    val daoEIN = new DaoEIN(dbProxyWithMetrics, driver)
    val daoSaiPreferences = new DaoSaiPreferences(Http.default, aiGatewayServiceConfig.endpoint, dbProxyWithMetrics, driver)
    val daoAccountPayloadKeys = new DaoAccountPayloadKeys(dbProxyWithMetrics, driver)
    val daoIDMApiKey = new DaoIDMApiKey(dbProxyWithMetrics, driver)
    val daoProduct = new DaoProduct(dbProxyWithMetrics, driver)
    val daoAccountBundleAssociation = new DaoAccountBundleAssociation(dbProxyWithMetrics, driver)
    val daoAccountBundleAssociationAudit = new DaoAccountAudit(dbProxyWithMetrics, driver)
    val daoAccountDataRetentionSchedule = new DaoAccountDataRetentionSchedule(dbProxyWithMetrics, driver)
    val daoMagicLinkAudit = new DaoMagicLinkAudit(dbProxyWithMetrics, driver)
    val daoSponsorBankProgram = new DaoSponsorBankProgram(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoProspect = new DaoProspect(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountPlatformResourceMapping = new DaoAccountPlatformResourceMapping(dbProxyWithMetrics, slick.driver.MySQLDriver)

    val encryptionKeysService = EncryptionKeysServiceImpl(
      encryptionKeysConfig = encryptionKeysConfig,
      dbProxy = dbProxyWithMetrics,
      driver = driver,
      daoAccount = daoAccount,
      clock = clock,
      scalaCache = scalaCache
    )
    val kmsService = KMSFactoryForPGP.get(pgpKmsConfig)
    logger.info("Account Service: Start Debug.....")

    val xmlSchemaValidator = XmlSchemaValidatorFactory.get(idpMetadataConfig)
    logger.info("Account Service: xmlSchemaValidator - Done")
    val mailNotificationService = new MailNotificationService(mailClient, pageConfig._1, pageConfig._2, pageConfig._3)
    logger.info("Account Service: mailNotificationService - Done")

    logger.info("Account Service: Service Creation - start")
    val businessUserCommonService = new BusinessUserCommonService(daoBusinessUser: DaoBusinessUser,
                                                                  daoAccount: DaoAccount,
                                                                  daoAccountV2: DaoAccountV2
                                                                  )
    val accountInfoService = new AccountInfoService(daoAccount, v2Validator)
    val samlValidator = new SamlValidator(
      accountInfoService = accountInfoService,
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      v2Validator = v2Validator
    )
    val rateLimitingService = new RateLimitingService(daoRateLimits, scalaCache)
    val daoIndustry = new DaoIndustries(dbProxyWithMetrics, driver)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, driver)
    val daoDashboardDomain = new DaoDashboardDomain(dbProxyWithMetrics, driver)
    val passwordStorageService = new PasswordStorageService(dbProxyWithMetrics, driver, clock, saltClient)
    val magicLinkAuditService = new MagicLinkAuditService(daoMagicLinkAudit, daoBusinessUser)
    val passwordService = new PasswordService(passwordStorageService, daoBusinessUser, daoAccountV2, samlValidator, clock, passwordExpiresDays, v2Validator, magicLinkAuditService)

    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    val environmentSettingService = new EnvironmentSettingsService(daoEnvironment, daoAccount, daoAccountV2, clock, apiKeyRenewalConfig, v2Validator, auditDetailsService)
    val activeUserService = new ActiveUsersService(daoAccount, daoBusinessUser, passwordStorageService, environmentSettingService, daoEnvironment)
    val lockedUserService = new LockedUserService(daoBusinessUser, clock)
    val inactiveUserService = new InactiveUserService(daoBusinessUser, samlValidator, clock)
    val delegatedAdminService = new DelegatedAdminService(daoBusinessUser, passwordService, samlValidator)
    val leaderboardService = new LeaderboardService(daoAccount)
    val dashboardService = new DashboardUserService(daoAccount, daoBusinessUser)
    val blacklistService = new BlackListService(dbProxyWithMetrics, driver)
    val dashboardUserServiceV2 = new DashboardUserServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordService, daoEnvironment, clock, v2Validator, businessUserCommonService, mailNotificationService,auditDetailsService)
    val subscriptionService = new SubscriptionService(daoSubscriptions, daoSubscriptionType, daoSubscriptionStatus, daoEnvironment, daoSubscriptionChannelRegistry, daoAccountV2, v2Validator)
    val userRoleService = new UserRoleService(daoAccount,daoAccountV2, v2Validator, daoUIAccountConfiguration)
   val accountHierarchyService = new AccountHierarchyService(daoAccount, daoAccountV2,v2Validator, clock)
    val userAccountAssociationService = new UserAccountAssociationService(daoAccountV2, daoAccount, daoBusinessUser, daoSubscriptions, v2Validator, daoUIAccountConfiguration, passwordService)
    val permissionTemplateService = new PermissionTemplateService(daoAccountV2, v2Validator, clock)
    val dashboardUserPermissionService = new DashboardUserPermissionService
    val dvConfigurationService = new DvConfigurationService(daoDvConfiguration, v2Validator, auditDetailsService)
    val basicEngine = new BasicEngine(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val industryService = new IndustriesManagementService(daoIndustry)
    val v2ValidationService = new V2ValidationService(accountHierarchyService, v2Validator, daoBusinessUser, daoAccountV2)
    val uIAccountConfigurationService = new AccountUIConfigurationService(daoUIAccountConfiguration, v2Validator, scalaCache, auditDetailsService)

    val scheduledActivitiesService = new ScheduledActivitiesService(daoBusinessUser, clock, scheduledActConfig, passwordService, mailNotificationService, magicLinkAuditService, inactiveUserService)
    val accountPgpKeysService = new AccountPgpKeysService(daoAccountPgpKeys, new PgpKeyPairGenerator(), kmsService, pgpKmsConfig.kmsId, supportMailId, clock : Clock)
    val dashboardAccountServiceV2 = new DashboardAccountServiceV2(daoAccount, daoAccountV2, daoBusinessUser, passwordStorageService, daoEnvironment, clock,
      encryptionKeysService, apiKeyRenewalConfig, customerSuccessMailId, mailNotificationService,
      daoPublicApiKey, v2Validator, pbeEncryptor, auditRequestEncipher, rateLimitingService,
      modelManagementClient, daoUIAccountConfiguration, businessUserCommonService, dashboardUserServiceV2, accountPgpKeysService, pgpKeyExpiryDuration, auditDetailsService, analyticsConfig)
    val dashboardDomainService = new DashboardDomainService(daoDashboardDomain, daoBusinessUser, clock, scalaCache, auditDetailsService, dashboardAccountServiceV2)
    val manageAccountsService  = new ManageAccountsService(daoAccount, daoBusinessUser, daoEnvironment, clock, encryptionKeysService, apiKeyRenewalConfig, daoPublicApiKey, pbeEncryptor, modelManagementClient, businessUserCommonService, mailNotificationService, inactiveUserService)
    val pgpSignaturePublicKeyService  = new PgpSignaturePublicKeyService(daoPgpSignaturePublicKeys, daoAccount, kmsService, pgpKmsConfig.kmsId, clock)
    val pgpDecAndSigKeysService = new PgpDecAndSigKeysService(
      accountPgpKeysService = accountPgpKeysService,
      pgpSignaturePublicKeyService = pgpSignaturePublicKeyService
    )
    val idpMetadataService = new IdpMetadataService(
      daoIdpMetadata = daoIdpMetadata,
      daoAccount = daoAccount,
      daoAccountV2 = daoAccountV2,
      xmlSchemaValidator = xmlSchemaValidator,
      accountInfoService = accountInfoService,
      clock = clock)
    val watchlistPreferenceMapper = new WatchlistPreferenceMapper(clock)
    val caWatchlistPreferenceMapper = new CAWatchlistPreferenceMapper(clock)
    val accountPreferencesService = new AccountPreferencesService(daoKYCPreferences, daoAccountV2, daoWatchlistPreferences, watchlistPreferenceMapper, daoComplyWatchlistPreferences, caWatchlistPreferenceMapper, v2Validator, scalaCache,auditDetailsService)
    val publicWebhookService = new PublicWebhookService(daoPublicKeyCertificate,daoSubscriptionStatus, clock)
    val environmentService = new EnvironmentService(daoEnvironment, v2Validator, daoAccount)
    val watchlistSourceService = new WatchlistSourceService(daoWatchlistSource, daoAccountV2, clock, v2Validator, scalaCache,auditDetailsService)
    val subscriptionChannelValidator = new SubscriptionChannelValidator(daoEnvironment,daoAccount, daoSubscriptions,daoSubscriptionChannelRegistry)
    val subscriptionChannelRegistryService = new SubscriptionChannelRegistryServiceImpl(daoSubscriptionChannelRegistry, daoEnvironment, v2Validator,subscriptionChannelValidator, clock, secretKeyExpiryCheck, scalaCache)
    val mlaService = new MLAService(
      daoAccount = daoAccount,
      daoMLAFields = daoMLAFields,
      clock
    )
    val einService = new EINService(daoEIN = daoEIN, daoAccount = daoAccount, clock = clock)
    val saiPreferencesService = new SaiPreferencesService(daoSaiPreferences = daoSaiPreferences, daoAccount = daoAccount, clock = clock)

    val daoAccountPermission = new DaoAccountPermission(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val productService = new ProductService(accountHierarchyService, dashboardAccountServiceV2, daoProduct, daoAccountPermission, daoEnvironment)
    val accountAuditService = new AccountAuditService(daoAccountBundleAssociationAudit, daoAccount, clock)
    val accountBundleAssociationService = new AccountBundleAssociationService(daoAccountBundleAssociation = daoAccountBundleAssociation, daoAccount, accountHierarchyService, clock)
    val accountDataRetentionScheduleService = new AccountDataRetentionScheduleService(daoAccountDataRetentionSchedule, daoAccountV2, clock)
    val bundleManagementService = new BundleManagementService()
    val accountAutomationService = new AccountAutomationService(accountBundleAssociationService,
      productService,
      einService,
      saiPreferencesService,
      accountAuditService,
      accountDataRetentionScheduleService,
      bundleManagementService,
      decisionServiceClientV2,
      accountPgpKeysService,
      activeUserService,
      pgpKeyExpiryDuration,
      documentManagerClient,
      accountProvisioningConfig,
      rateLimitingService,
      docvOrchestraClient,
      dvConfigurationService,
      accountPreferencesService,
      clock,
      scalaCache)
    val sponsorBankService = new SponsorBankService(daoSponsorBankProgram, accountAuditService, clock)

    val prospectService = new ProspectService(daoProspect)

    val partnerAndSubAccountInfoService = new PartnerAndSubAccountInfoService(daoAccountV2, v2Validator, sponsorBankService, clock)

    val businessService = new BusinessUserService(daoBusinessUser, daoAccount, daoEnvironment, badLoginConfig, passwordService, encryptionKeysService,
      samlValidator, clock, daoPublicApiKey, daoSubscriptions, daoAccountV2, daoRateLimit,
      pbeEncryptor, rateLimitingService, daoUIAccountConfiguration, modelManagementClient, businessUserCommonService,
      v2Validator, magicLinkAuditService, accountAutomationService, mailNotificationService, accountBundleAssociationService,sessionIdleTimeout, daoProspect, whitelistedEmailDomain)

    //ETL
    val environmentUpdaterService = new EnvironmentUpdaterService(daoEnvironment)
    val controlCenterService = new ControlCenterService(controlCenterServiceClient)
    val payloadKmsService = KMSFactory.get(payloadKmsConfig, payloadMultiRegionKmsConfig)
    val accountPayloadKeysService = new AccountPayloadKeysService(
      daoAccountPayloadKeys,
      payloadKmsService,
      payloadKmsConfig,
      payloadMultiRegionKmsConfig,
      scalaCache,
      clock
    )
    val accountSftpUserService = new AccountSftpUserService(daoAccountSftpUserService, clock)
    val onboardingService = new OnboardingService(businessService,
                                                  dashboardAccountServiceV2,
                                                  dashboardUserServiceV2,
                                                  manageAccountsService,
                                                  productService,
                                                  accountAutomationService,
                                                  daoUIAccountConfiguration,
                                                  partnerAndSubAccountInfoService)
    val accountPlatformResourceMappingService = new AccountPlatformResourceMappingService(daoAccountPlatformResourceMapping, clock)
    val encrypter: HMACEncrypter = HMACEncrypterFactory.get(hmacConfig.secretKey, hmacConfig.strength)
    val nonceCacheService = NonceCacheServiceFactory.create(hmacConfig.ttl)
    val hmacHttpVerifier: HMACHttpVerifier = new HMACHttpVerifier(hmacConfig.timeInterval, encrypter, nonceCacheService, clock)

    server.setHandler(handler)

    val homeServletHolder = new ServletHolder(new HomeServlet)
    handler.addServlet(homeServletHolder, "/*")

    val idPlusEngine = new IdPlusEngine(
      driver,
      accountPreferencesService,
      daoAccount,
      subscriptionService,
      watchlistSourceService,
      dvConfigurationService,
      mlaService,
      einService,
      saiPreferencesService,
      daoPublicApiKey,
      daoAccountV2,
      permissionsToBeOverriddenByChild
    )

    val idPlusServlet = new ServletHolder(new IdPlusServlet(idPlusEngine))
    handler.addServlet(idPlusServlet, "/idplus/*")

    val blacklistServlet = new ServletHolder(new BlackListServlet(blacklistService))
    handler.addServlet(blacklistServlet, "/blacklist/*")

    val accountInfoCacheInvalidator = new AccountInfoCacheInvalidator(
      accInfoCacheKeyProvider = AccInfoCacheKeyProvider,
      cacheKeyProvider = CacheKeyProvider,
      scalaCache = scalaCache
    )

    val businessUserRoleService = new BusinessUserRoleService(
      dbProxyWithMetrics = dbProxyWithMetrics,
      profile = driver,
      daoBusinessUser = daoBusinessUser,
      daoAccountV2 = daoAccountV2,
      accountInfoCacheInvalidator,
      subscriptionService,
      daoAccount,
      daoComplyWatchlistPreferences,
      v2Validator,
      scalaCache
    )
    val accountManagementService = new AccountManagementService(businessUserRoleService)
    val configureAccountService = new ConfigureAccountService(v2Validator, dashboardAccountServiceV2)
    val idmService = new IDMService(daoIDMApiKey, daoAccountV2, daoEnvironment, clock)

    logger.info("Account Service: Service Creation - end")
    logger.info("Account Service: Servlet Registrations - start")
    val accountServletHolder = new ServletHolder(new AccountSettingsServlet(accountService))
    handler.addServlet(accountServletHolder, "/settings/*")

    val accountPreferencesServletHolder = new ServletHolder(new AccountPreferencesServlet(accountPreferencesService, businessUserRoleService))
    handler.addServlet(accountPreferencesServletHolder, "/settings/preferences/*")

    val accountPreferenceCAServletHolder = new ServletHolder(new AccountPreferenceCAServlet(accountPreferencesService, watchlistSourceService, businessUserRoleService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(accountPreferenceCAServletHolder, "/settings/preferences/ca/*")

    val mlaServletHolder = new ServletHolder(new MLAFieldServlet(mlaService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(mlaServletHolder, "/dashboard/mla/*")

    val einServletHolder = new ServletHolder(new EINServlet(einService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(einServletHolder, "/ein/*")

    val saiPreferencesServletHolder = new ServletHolder(new SAIPreferencesServlet(saiPreferencesService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(saiPreferencesServletHolder, "/sai/preferences/*")

    val dashboardDomainServletHolder = new ServletHolder(new DashboardDomainServlet(dashboardDomainService))
    handler.addServlet(dashboardDomainServletHolder, "/dashboard/domain/*")

    val businessUserServletHolder = new ServletHolder(new UserManagementServlet(businessService, passwordService))
    handler.addServlet(businessUserServletHolder, "/user/*")

    val healthCheckServletHolder = new ServletHolder(new HealthCheckServlet(new AccountServiceHealthChecker()))
    handler.addServlet(healthCheckServletHolder, "/healthcheck/*")

    val businessUserRoleHolder = new ServletHolder(new BusinessUserRoleServlet(businessUserRoleService))
    handler.addServlet(businessUserRoleHolder, "/businessuserrole/*")

    val activeUserServletHolder = new ServletHolder(new ActiveUsersServlet(activeUserService))
    handler.addServlet(activeUserServletHolder, "/superadmin/*")

    val lockedUserServletHolder = new ServletHolder(new LockedUserServlet(lockedUserService))
    handler.addServlet(lockedUserServletHolder, "/locked/*")

    val inactiveUserServletHolder = new ServletHolder(new InactiveUserServlet(inactiveUserService))
    handler.addServlet(inactiveUserServletHolder, "/inactive/*")

    val delegatedAdminServletHolder = new ServletHolder(new DelegatedAdminServlet(delegatedAdminService))
    handler.addServlet(delegatedAdminServletHolder, "/delegated/*")

    val leaderbordServletHolder = new ServletHolder(new LeaderboardServlet(leaderboardService))
    handler.addServlet(leaderbordServletHolder, "/leaderboard/*")

    val accountInfoServletHolder = new ServletHolder(new AccountInfoServlet(accountInfoService))
    handler.addServlet(accountInfoServletHolder, "/account/info/*")

    val accountInfoServletV2Holder = new ServletHolder(new AccountInfoServletV2(accountInfoService, hmacHttpVerifier))
    handler.addServlet(accountInfoServletV2Holder, "/account/v2/info/*")

    val accountInfoServletV3Holder = new ServletHolder(new AccountInfoServletV3(accountInfoService, hmacHttpVerifier))
    handler.addServlet(accountInfoServletV3Holder, "/account/v3/info/*")

    val accountManagmentServletHolder = new ServletHolder(new ManageAccountsServlet(manageAccountsService = manageAccountsService))
    handler.addServlet(accountManagmentServletHolder, "/account/management/*")

    val accountManagmentV2ServletHolder = new ServletHolder(new ManageAccountsV2Servlet(manageAccountsService = manageAccountsService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(accountManagmentV2ServletHolder, "/account/management/v2/*")

    val idPlusV2Servlet = new ServletHolder(new IdPlusV2Servlet(idPlusEngine, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(idPlusV2Servlet, "/idplus/v2/*")

    val dashboardServletHolder = new ServletHolder(new DashboardUserServlet(dashboardService, passwordService))
    handler.addServlet(dashboardServletHolder, "/dashboard/*")

    val accountServlet2 = new ServletHolder(new AccountV2Servlet(dashboardAccountServiceV2, accountAutomationService))
    handler.addServlet(accountServlet2, "/dashboard_account/*")

    val dashboardUserServlet2 = new ServletHolder(new DashboardUserV2Servlet(dashboardUserServiceV2, passwordService, businessUserRoleService))
    handler.addServlet(dashboardUserServlet2, "/dashboard_user/*")

    val subscriptionServlet = new ServletHolder(new SubscriptionServlet(subscriptionService, hmacHttpVerifier))
    handler.addServlet(subscriptionServlet, "/subscription/*")

    val userRoleServlet = new ServletHolder(new UserRoleServlet(userRoleService, hmacHttpVerifier))
    handler.addServlet(userRoleServlet, "/roles/*")

    val partnerAndSubAccountServlet = new ServletHolder(new PartnerAndSubAccountInfoServlet(partnerAndSubAccountInfoService, hmacHttpVerifier))
    handler.addServlet(partnerAndSubAccountServlet, "/partner_and_sub_account_info/*")

    val validationServlet = new ServletHolder(new V2ValidationServlet(v2ValidationService, hmacHttpVerifier))
    handler.addServlet(validationServlet, "/validation/*")

    val accountHierarchyServlet = new ServletHolder(new AccountHierarchyServlet(accountHierarchyService, hmacHttpVerifier))
    handler.addServlet(accountHierarchyServlet, "/hierarchy/*")

    val userAccountAssociationServlet = new ServletHolder(new UserAccountAssociationServlet(userAccountAssociationService, hmacHttpVerifier))
    handler.addServlet(userAccountAssociationServlet, "/user_account_associations/*")

    val permissionTemplateServlet = new ServletHolder(new PermissionTemplateServlet(permissionTemplateService, hmacHttpVerifier))
    handler.addServlet(permissionTemplateServlet, "/permission_templates/*")

    val dashboardUserPermissionServlet = new ServletHolder(new DashboardUserPermissionServlet(dashboardUserPermissionService, hmacHttpVerifier))
    handler.addServlet(dashboardUserPermissionServlet, "/permissions/*")

    val rateLimitingServlet = new ServletHolder(new RateLimitingServlet(rateLimitingService, hmacHttpVerifier))
    handler.addServlet(rateLimitingServlet, "/api/1.0/rate-limiting/*")

    val rateLimitingV2Servlet = new ServletHolder(new RateLimitingV2Servlet(rateLimitingService))
    handler.addServlet(rateLimitingV2Servlet, "/api/2.0/rate-limiting/*")

    val dvConfigurationServlet = new ServletHolder(new DvConfigurationServlet(dvConfigurationService, hmacHttpVerifier))
    handler.addServlet(dvConfigurationServlet, "/dv/configuration/*")

    val basicServletHolder = new ServletHolder(new BasicServlet(basicEngine))
    handler.addServlet(basicServletHolder, "/basic/*")

    val environmentSettingServletHolder = new ServletHolder(new EnvironmentSettingsServlet(environmentSettingService, businessUserRoleService))
    handler.addServlet(environmentSettingServletHolder, "/environment_settings/*")

    val environmentSettingsV2ServletHolder = new ServletHolder(new EnvironmentSettingsV2Servlet(environmentSettingService, hmacHttpVerifier))
    handler.addServlet(environmentSettingsV2ServletHolder, "/settings/environments/*")


    val industryServletHolder = new ServletHolder(new IndustryServlet(industryService))
    handler.addServlet(industryServletHolder, "/industries/*")

    //ETL
    val etlEnvironmentServletHolder = new ServletHolder(new EnvironmentUpdaterServlet(environmentUpdaterService))
    handler.addServlet(etlEnvironmentServletHolder, "/etl/environment/*")

    val passwordServlet = new ServletHolder(new PasswordServlet)
    handler.addServlet(passwordServlet, "/password/*")

    //Scheduled Servlet
    val scheduledActivitiesHolder = new ServletHolder(new ScheduledActivitiesServlet(scheduledActivitiesService))
    handler.addServlet(scheduledActivitiesHolder, "/schedule/*")

    //Encryption Keys
    val encryptionKeysHolder = new ServletHolder(new EncryptionKeysServlet(
      encryptionKeysService = encryptionKeysService,
      accountInfoService = accountInfoService
    ))
    handler.addServlet(encryptionKeysHolder, "/encryption_keys/*")

    //Encryption Keys
    val encryptionKeysV2Holder = new ServletHolder(new EncryptionKeysServletV2(
      encryptionKeysService = encryptionKeysService,
      dashboardAccountServiceV2 = dashboardAccountServiceV2,
      hmacHttpVerifier
    ))
    handler.addServlet(encryptionKeysV2Holder, "/encryption_keys_v2/*")

    //PGP Keys
    val pgpKeysHolder = new ServletHolder(new PGPServlet(
      accountPgpKeysService = accountPgpKeysService,
      pgpSignaturePublicKeyService = pgpSignaturePublicKeyService,
      pgpDecAndSigKeysService = pgpDecAndSigKeysService
    ))
    handler.addServlet(pgpKeysHolder, "/pgp/*")

    val idpMetadataHolder = new ServletHolder(new IdpMetadataServlet(idpMetadataService = idpMetadataService))
    handler.addServlet(idpMetadataHolder, "/idp/metadata/*")

    val publicWebhookHolder = new ServletHolder(new PublicWebhookServlet(publicWebhookService = publicWebhookService))
    handler.addServlet(publicWebhookHolder, "/settings/webhook/*")

    val environmentHolder = new ServletHolder(new EnvironmentServlet(environmentService))
    handler.addServlet(environmentHolder, "/environment/*")

    val subscriptionChannelRegistryHolder = new ServletHolder(new SubscriptionChannelRegistryServlet(subscriptionChannelRegistryService, hmacHttpVerifier))
    handler.addServlet(subscriptionChannelRegistryHolder, "/settings/channel/*")

    val uIAccountConfigurationServlet = new ServletHolder(new AccountUIConfigurationServlet(uIAccountConfigurationService, hmacHttpVerifier, sessionIdleTimeout, autoTimeout, v2Validator))
    handler.addServlet(uIAccountConfigurationServlet, "/ui/configuration/*")

    val moduleManagementServletHolder = new ServletHolder(new ModuleManagementServlet(dashboardAccountServiceV2, hmacHttpVerifier))
    handler.addServlet(moduleManagementServletHolder, "/modules/*")

    val configureAccountServlet = new ServletHolder(new ConfigureAccountServlet(configureAccountService, hmacVerifier = hmacHttpVerifier))
    handler.addServlet(configureAccountServlet, "/configure/account/*")

    val controlCenterServlet = new ServletHolder(new ControlCenterServlet(controlCenterService, hmacHttpVerifier))
    handler.addServlet(controlCenterServlet, "/control_center/*")

    // payload_keys is servlet mapping path which will have actual endpoints like get_payload_keys
    val accountPayloadKeysServlet = new ServletHolder(new AccountPayloadKeysServlet(accountPayloadKeysService, hmacHttpVerifier))
    handler.addServlet(accountPayloadKeysServlet, "/payload_keys/*")

    val accountSettingsServlet = new ServletHolder(new AccountSettingsViewServlet(accountPreferencesService,
      subscriptionChannelRegistryService, dvConfigurationService, daoUIAccountConfiguration, environmentSettingService,
      accountService, dashboardDomainService,dashboardAccountServiceV2,watchlistSourceService, hmacHttpVerifier))
    handler.addServlet(accountSettingsServlet, "/view/settings/*")

    val batchJobServlet = new ServletHolder(new BatchJobServlet(accountManagementService, hmacHttpVerifier))
    handler.addServlet(batchJobServlet, "/batchjob/*")

    val productServlet = new ServletHolder(new ProductServlet(productService, hmacHttpVerifier))
    handler.addServlet(productServlet, "/products/*")

    val accountBundleAssociationServlet = new ServletHolder(new AccountBundleAssociationServlet(accountBundleAssociationService, hmacHttpVerifier))
    handler.addServlet(accountBundleAssociationServlet, "/bundles/*")

    val accountAutomationServlet = new ServletHolder(new AccountAutomationServlet(accountAutomationService, bundleManagementService, hmacHttpVerifier))
    handler.addServlet(accountAutomationServlet, "/automation/*")

    val sponsorBankServlet = new ServletHolder(new SponsorBankServlet(sponsorBankService, hmacHttpVerifier))
    handler.addServlet(sponsorBankServlet, "/sponsorbank/*")

    val accountDataRetentionScheduleServlet = new ServletHolder(new AccountDataRetentionScheduleServlet(accountDataRetentionScheduleService, hmacHttpVerifier))
    handler.addServlet(accountDataRetentionScheduleServlet, "/data/retention/schedule/*")

    val smokeTestServlet = CheckerServlet(
      DbCheckerAutoLoader,
      HttpCheckerAutoLoader
    )
    handler.addServlet(new ServletHolder(smokeTestServlet), "/smoke-tests/*")
    val accountSftpUserServlet = new ServletHolder(new AccountSftpUserServlet(accountSftpUserService, hmacHttpVerifier))
    handler.addServlet(accountSftpUserServlet, "/sftp/user/*")

    val idmServlet = new ServletHolder(new IDMServlet(idmService, hmacHttpVerifier))
    handler.addServlet(idmServlet, "/idm/*")

    val prospectServlet = new ServletHolder(new ProspectServlet(prospectService, hmacHttpVerifier))
    handler.addServlet(prospectServlet, "/prospect/*")

    val onboardingServlet = new ServletHolder(new OnboardingServlet(onboardingService))
    handler.addServlet(onboardingServlet, "/onboarding/*")

    val accountPlatformResourceMappingServlet = new ServletHolder(new AccountPlatformResourceMappingServlet(accountPlatformResourceMappingService))
    handler.addServlet(accountPlatformResourceMappingServlet, "/account/platform/*")

    logger.info("Account Service: Servlet Registrations - end")

    logger.info("Account Service: Filter Registrations - start")

    val dispatchTypes = JEnumSet.of(
      DispatcherType.REQUEST,
      DispatcherType.FORWARD,
      DispatcherType.ASYNC,
      DispatcherType.ERROR,
      DispatcherType.INCLUDE
    )
    if(serverMetricsEnabled) {
      val servletMetricsFilter = new ServletMetricsFilter(
        metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix),
        baseTags = MetricTags(serviceName = Some("account-service")),
        apiNameMapping = ServletRegexMapping.value
      )
    handler.addFilter(new FilterHolder(servletMetricsFilter), "/*", dispatchTypes)
    }

    handler.addFilter(new FilterHolder(new ReadFilter(dynamicControlCenterV2Evaluate)), "/*", dispatchTypes)

    logger.info("Account Service: Filter Registrations - end")
    server.start()
    logger.info("Account service started")
  }

  def getPort: Option[Int] = {
    server.getConnectors.headOption.map(_.asInstanceOf[ServerConnector].getLocalPort)
  }

}

