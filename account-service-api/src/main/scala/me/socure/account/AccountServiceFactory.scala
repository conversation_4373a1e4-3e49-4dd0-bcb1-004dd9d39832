package me.socure.account

import com.typesafe.config.Config

import javax.sql.DataSource
import me.socure.account.ccm.AuditRequestEncipher
import me.socure.common.clock.RealClock
import me.socure.common.executioncontext.factory.ExecutionContextFactory
import me.socure.common.hmac.config.{HMACConfig, HMACConfigFactory}
import me.socure.common.jettythreadpool.factory.JettyThreadPoolFactory
import me.socure.common.metrics.JavaMetricsFactory
import me.socure.common.rds.impl.HikariDataSourceProvider
import me.socure.configuration.{AIGatewayServiceConfig, AnalyticsConfig}
import me.socure.constants.{AccountProvisioningConfigFactory, AccountProvisioningConfiguration}
import me.socure.control.center.client.ControlCenterServiceClient
import me.socure.control.center.factory.ControlCenterServiceClientFactory
import me.socure.decision.service.client.DecisionServiceClientV2
import me.socure.decision.service.client.factory.DecisionServiceClientFactory
import me.socure.document.manager.client.DocumentManagerClientFactory
import me.socure.docv.client.DocvOrchestraClientFactory
import me.socure.dynamic.control.center.v2.factory.DynamicControlCenterV2Factory
import me.socure.mail.client.MailNotificationClientFactory
import me.socure.mail.factory.PageConfigFactory
import me.socure.model.management.client.ModelManagementClientFactory
import me.socure.salt.client.SaltClientFactory
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import scalacache.ScalaCache

import scala.collection.JavaConverters._
import scala.util.{Success, Try}

/**
  * Created by alexandre on 5/12/16.
  */
class AccountServiceFactory(config: Config) {

  private def getDataSource(config: Config, identifier: String): DataSource = {
    val provider = if (config.hasPath("common")) {
      new HikariDataSourceProvider(config.getConfig(identifier).withFallback(config.getConfig("common")))
    } else {
      new HikariDataSourceProvider(config)
    }
    provider.provide()
  }


  def get() = {

    val metrics = JavaMetricsFactory.get("account_service.threadpool")
    val withDBMigration = config.getBoolean("withDBMigration")

    val primarySource = getDataSource(config.getConfig("database.socure"), "primary")
    val replicaSource = getDataSource(config.getConfig("database.socure"), "replica")

    implicit val executionContext = ExecutionContextFactory.get(config.getConfig("executionContext"), metrics)

    val threadPool = JettyThreadPoolFactory.get(config.getConfig("jetty.threadPool"))

    val badLoginConfig = BadLoginConfigFactory.get(config.getConfig("badloginconfig"))
    val apiKeyRenewalConfig = ApiKeyRenewalConfigFactory.get(config.getConfig("apikeyrenewal"))
    val clock = new RealClock

    val expiresDays = config.getInt("password.expires")
    val scheduledActConfig = ScheduledActivitiesConfigFactory.get(config.getConfig("scheduled.activities"), expiresDays)
    val encryptionKeysConfig = EncryptionKeysConfigFactory.get(config.getConfig("client.specific.encryption"))
    val pgpKmsKeyConfig = PgpKmsKeysConfigFactory.get(config.getConfig("pgp.encryption"))
    val pgpKeyExpiryDuration = config.getLong("pgp.encryption.keyExpiryDuration")
    val payloadKmsConfig = PayloadKmsKeysConfigFactory.get(config.getConfig("payload.encryption"))
    val payloadMultiRegionKmsConfig = PayloadKmsKeysConfigFactory.getKmsIdsConfig(config.getConfig("payload.encryption.multi.region.kms.ids"))
    val pageConfigV2 = PageConfigFactory.get(config.getConfig("pageConfigV2"))
    val pageConfigV3 = PageConfigFactory.get(config.getConfig("pageConfigV3"))
    val platformConfig = PageConfigFactory.get(config.getConfig("platformConfig"))
    val idpMetadataConfig = IdpMetadataConfigFactory.get(config.getConfig("idpMetadata"))
    val supportMailId = config.getString("supportMail")
    val customerSuccessMailId = Try{config.getString("customerSuccessMail")} match {
      case Success(v) => Some(v)
      case _ => None
    }

    val saltClient = SaltClientFactory.create(config.getConfig("saltService"))

    val modelManagementFactory = new ModelManagementClientFactory(config)
    val modelManagementClient = modelManagementFactory.get()

    val auditRequestEncipher = new AuditRequestEncipher(config.getString("enc.key"))

    val scalaCache = ScalaCache(MemcachedCache(config.getString("memcached.endpoint")))
    val hmacConfig: HMACConfig = HMACConfigFactory.create(config.getConfig("hmac"))
    val pbePassword = config.getString("pbePassword")

    val sessionIdleTimeout = config.getInt("session.idle.timeout")
    val autoTimeout = config.getInt("auto.timeout")

    val controlCenterServiceClient: ControlCenterServiceClient = new ControlCenterServiceClientFactory().getClient(config)

    val dynamicControlCenterV2 = DynamicControlCenterV2Factory.getEvaluator(config)

    val mailClient = MailNotificationClientFactory.get(config, dynamicControlCenterV2)

    val permissionsToBeOverriddenByChild = config.getIntList("businessuserroles.permissions.overriddenByChild").asScala.toSet

    val whitelistedEmailDomain = config.getStringList("domain.whitelistedEmailDomain").asScala.toSet

    val secretKeyExpiryCheckInMillis = config.getInt("secretKey.expiry.check")

    val decisionServiceClientV2: DecisionServiceClientV2 = DecisionServiceClientFactory.createV2(config)
    val accountProvisioningConfig: AccountProvisioningConfiguration = AccountProvisioningConfigFactory.get(config.getConfig("default.provisioning"))

    val documentManagerClient = DocumentManagerClientFactory.create(config)
    val docvOrchestraClient = DocvOrchestraClientFactory.create(config.getString("docvOrchestra.endpoint"), config.getConfig("docvOrchestra.hmac"))
    val serverMetricsEnabled = Try(config.getBoolean("server.metrics.enabled")).getOrElse(true)
    val dbMetricsEnabled = Try(config.getBoolean("db.metrics.enabled")).getOrElse(true)
    val enableV2QueryForAccountPermissionFetch = Try(config.getBoolean("enableV2QueryForAccountPermissionFetch")).getOrElse(false)
    val analyticsConfig = AnalyticsConfig(Try(config.getBoolean("analytics.isHistoricDataImported")).getOrElse(false))
    val aiGatewayServiceConfig = AIGatewayServiceConfig(endpoint = Try(config.getString("ai.gateway.service.endpoint")).getOrElse(""))

    new AccountService(
      dataSource = primarySource,
      dataSourceReplica = replicaSource,
      threadPool = threadPool,
      port = 5000,
      withDBMigration = withDBMigration,
      badLoginConfig = badLoginConfig,
      clock = clock,
      apiKeyRenewalConfig = apiKeyRenewalConfig,
      scheduledActConfig = scheduledActConfig,
      encryptionKeysConfig = encryptionKeysConfig,
      passwordExpiresDays = expiresDays,
      pgpKmsConfig = pgpKmsKeyConfig,
      payloadKmsConfig = payloadKmsConfig,
      mailClient = mailClient,
      pageConfig = (pageConfigV2, pageConfigV3, platformConfig),
      idpMetadataConfig = idpMetadataConfig,
      supportMailId = supportMailId,
      customerSuccessMailId = customerSuccessMailId,
      saltClient = saltClient,
      scalaCache = scalaCache,
      hmacConfig = hmacConfig,
      pbePassword = pbePassword,
      auditRequestEncipher = auditRequestEncipher,
      sessionIdleTimeout = sessionIdleTimeout,
      autoTimeout = autoTimeout,
      controlCenterServiceClient = controlCenterServiceClient,
      modelManagementClient = modelManagementClient,
      dynamicControlCenterV2Evaluate = dynamicControlCenterV2,
      payloadMultiRegionKmsConfig = payloadMultiRegionKmsConfig,
      permissionsToBeOverriddenByChild = permissionsToBeOverriddenByChild,
      secretKeyExpiryCheck = secretKeyExpiryCheckInMillis,
      decisionServiceClientV2 = decisionServiceClientV2,
      pgpKeyExpiryDuration = pgpKeyExpiryDuration,
      documentManagerClient = documentManagerClient,
      accountProvisioningConfig = accountProvisioningConfig,
      docvOrchestraClient = docvOrchestraClient,
      serverMetricsEnabled = serverMetricsEnabled,
      dbMetricsEnabled = dbMetricsEnabled,
      analyticsConfig = analyticsConfig,
      aiGatewayServiceConfig = aiGatewayServiceConfig,
      whitelistedEmailDomain = whitelistedEmailDomain
    )
  }
}
