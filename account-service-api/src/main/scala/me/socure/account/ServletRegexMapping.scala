package me.socure.account

import java.util.regex.Pattern

object ServletRegexMapping {

  val value: Map[Pattern, String] = Map(
    Pattern.compile("^/idplus/fetch_account_information_by_public_api_key/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/idplus/fetch_account_information_by_public_api_key",
    Pattern.compile("^/idplus/fetch_account_information_v2/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/idplus/fetch_account_information_v2",
    Pattern.compile("^/idplus/fetch_account_information/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/idplus/fetch_account_information",
    Pattern.compile("^/blacklist/fetch_industry/\\d+$") -> "/blacklist/fetch_industry",
    Pattern.compile("^/blacklist/fetch_account_information/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/blacklist/fetch_account_information",
    Pattern.compile("^/settings/remove_invidiual_cache/\\d+$") -> "/settings/remove_invidiual_cache",
    Pattern.compile("^/settings/remove_account_cache/\\d+$") -> "/settings/remove_account_cache",
    Pattern.compile("^/settings/remove_appkey/\\d+$") -> "/settings/remove_appkey",
    Pattern.compile("^/settings/remove_social_appkey_config/\\d+/[a-zA-Z]+$") -> "/settings/remove_social_appkey_config",
    Pattern.compile("^/settings/modules/\\d+$") -> "/settings/modules",
    Pattern.compile("^/settings/get_environment_details/\\S+$") -> "/settings/get_environment_details",
    Pattern.compile("^/settings/new/\\d+$") -> "/settings/new",
    Pattern.compile("^/settings/preferences/kyc/\\d+$") -> "/settings/preferences/kyc",
    Pattern.compile("^/settings/preferences/kyc/\\d+$") -> "/settings/preferences/kyc",
    Pattern.compile("^/settings/preferences/kyc/\\d+$") -> "/settings/preferences/kyc",
    Pattern.compile("^/settings/preferences/ca/watchlist/sources/category/\\d+$") -> "/settings/preferences/ca/watchlist/sources/category",
    Pattern.compile("^/ein/account/\\d+$") -> "/ein/account",
    Pattern.compile("^/dashboard/domain/get_permission_domain_by_account_id/\\d+$") -> "/dashboard/domain/get_permission_domain_by_account_id",
    Pattern.compile("^/dashboard/domain/get_permission_domain_by_email/\\S+$") -> "/dashboard/domain/get_permission_domain_by_email",
    Pattern.compile("^/dashboard/domain/get_by_email/\\S+$") -> "/dashboard/domain/get_by_email",
    Pattern.compile("^/dashboard/domain/get/\\d+$") -> "/dashboard/domain/get",
    Pattern.compile("^/user/get_users_with_roles/\\d+$") -> "/user/get_users_with_roles",
    Pattern.compile("^/user/is_user_internal/\\S+$") -> "/user/is_user_internal",
    Pattern.compile("^/user/is_user_locked/\\S+$") -> "/user/is_user_locked",
    Pattern.compile("^/user/get_account_id/\\S+$") -> "/user/get_account_id",
    Pattern.compile("^/user/get_user_by_resetcode/[a-zA-Z0-9-]{36}$") -> "/user/get_user_by_resetcode",
    Pattern.compile("^/user/get_user_by_activationcode/[a-zA-Z0-9-]{36}$") -> "/user/get_user_by_activationcode",
    Pattern.compile("^/user/activate_user/[a-zA-Z0-9-]{36}$") -> "/user/activate_user",
    Pattern.compile("^/user/\\d+/promote/primary$") -> "/user/promote/primary",
    Pattern.compile("^/businessuserrole/account_permissions/\\d+$") -> "/businessuserrole/account_permissions",
    Pattern.compile("^/superadmin/get_domains/\\d+$") -> "/superadmin/get_domains",
    Pattern.compile("^/inactive/get_reset_code/\\S+$") -> "/inactive/get_reset_code",
    Pattern.compile("^/inactive/get_activation_code/\\S+$") -> "/inactive/get_activation_code",
    Pattern.compile("^/delegated/is_user_exist/\\S+$") -> "/delegated/is_user_exist",
    Pattern.compile("^/delegated/delegated_admins_v2/\\S+$") -> "/delegated/delegated_admins_v2",
    Pattern.compile("^/delegated/delegated_admins/\\S+$") -> "/delegated/delegated_admins",
    Pattern.compile("^/leaderboard/get_account_by_apikey/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/leaderboard/get_account_by_apikey",
    Pattern.compile("^/account/info/preferences/\\d+$") -> "/account/info/preferences",
    Pattern.compile("^/account/info/public-account/\\d+$") -> "/account/info/public-account/",
    Pattern.compile("^/account/info/get_account_name_by_id/\\d+$") -> "/account/info/get_account_name_by_id",
    Pattern.compile("^/account/info/get_account_details_by_id_v2/\\d+$") -> "/account/info/get_account_details_by_id_v2",
    Pattern.compile("^/account/info/get_account_details_by_id/\\d+$") -> "/account/info/get_account_details_by_id",
    Pattern.compile("^/account/info/publicid/accounts/\\d+$") -> "/account/info/publicid/accounts",
    Pattern.compile("^/account/v2/info/accounts/key/public/[a-zA-Z0-9-]{36}$") -> "/account/v2/info/accounts/key/public",
    Pattern.compile("^/account/v2/info/accounts/apikeys/public/[a-zA-Z0-9-]{36}$") -> "/account/v2/info/accounts/apikeys/public",
    Pattern.compile("^/account/v2/info/accounts/\\d+$") -> "/account/v2/info/accounts",
    Pattern.compile("^account/v2/info/accounts/[a-zA-Z0-9-]{14}$") -> "/account/v2/info/accounts/publicid",
    Pattern.compile("^account/v2/info/accounts/key/public/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/account/v2/info/accounts/key/public",
    Pattern.compile("^/account/v3/info/accounts/key/public/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/account/v3/info/accounts/key/public",
    Pattern.compile("^/production_credentials/\\d+$") -> "/production_credentials",
    Pattern.compile("^/account/management/v2/get_consent_reason/\\d+$") -> "/account/management/v2/get_consent_reason",
    Pattern.compile("^/account/management/v2/accounts/users/\\S+/email$") -> "/account/management/v2/accounts/users/email",
    Pattern.compile("^/account/management/v2/publicid/acc-[a-zA-Z0-9]{10}/activate$") -> "/account/management/v2/publicid/activate",
    Pattern.compile("^/account/management/v2/publicid/acc-[a-zA-Z0-9]{10}/delete$") -> "/account/management/v2/publicid/delete",
    Pattern.compile("^/idplus/v2/accounts/publicapikeys/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/idplus/v2/accounts/publicapikeys",
    Pattern.compile("^/dashboard/subaccounts_for_id/\\d+$") -> "/dashboard/subaccounts_for_id",
    Pattern.compile("^/dashboard/get_accountid/\\S+$") -> "/dashboard/get_accountid",
    Pattern.compile("^/dashboard/get_primary_user/\\S+$") -> "/dashboard/get_primary_user",
    Pattern.compile("^/dashboard/get_user/\\S+$") -> "/dashboard/get_user",
    Pattern.compile("^/dashboard/get_account_creds/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/dashboard/get_account_creds",
    Pattern.compile("^/dashboard_account/get_account_info_for_environment/\\d+$") -> "/dashboard_account/get_account_info_for_environment",
    Pattern.compile("^/dashboard_account/get_apikeys_for_account_and_subaccounts/\\d+$") -> "/dashboard_account/get_apikeys_for_account_and_subaccounts",
    Pattern.compile("^/dashboard_account/get_apikeys_for_subaccounts/\\d+$") -> "/dashboard_account/get_apikeys_for_subaccounts",
    Pattern.compile("^/dashboard_account/get_account_list/\\d+$") -> "/dashboard_account/get_account_list",
    Pattern.compile("^/dashboard_account/list_sub_accounts_with_env_details/\\d+$") -> "/dashboard_account/list_sub_accounts_with_env_details",
    Pattern.compile("^/dashboard_account/subaccounts/\\d+$") -> "/dashboard_account/subaccounts",
    Pattern.compile("^/dashboard_account/list_sub_accounts/\\d+$") -> "/dashboard_account/list_sub_accounts",
    Pattern.compile("^/dashboard_account/list_accounts/\\d+$") -> "/dashboard_account/list_accounts",
    Pattern.compile("^/dashboard_account/list_sub_accounts_by_public_id/\\d+$") -> "/dashboard_account/list_sub_accounts_by_public_id",
    Pattern.compile("^/dashboard_user/get_public_account_id/\\S+$") -> "/dashboard_user/get_public_account_id",
    Pattern.compile("^/dashboard_user/get_user_id/\\S+$") -> "/dashboard_user/get_user_id",
    Pattern.compile("^/dashboard_user/get_user_information_v2/\\d+$") -> "/dashboard_user/get_user_information_v2",
    Pattern.compile("^/dashboard_user/get_user_information/\\d+$") -> "/dashboard_user/get_user_information",
    Pattern.compile("^/dashboard_user/list_userids/\\d+$") -> "/dashboard_user/list_userids",
    Pattern.compile("^/dashboard_user/list_users/\\d+$") -> "/dashboard_user/list_users",
    Pattern.compile("^/subscription/account/\\d+/subscriptions/\\d+/\\S$") -> "/subscription/account/subscriptions",
    Pattern.compile("^/subscription/account/\\d+/secret_keys/\\d+$") -> "/subscription/account/secret_keys",
    Pattern.compile("^/subscription/account/\\d+/secret_keys$") -> "/subscription/account/secret_keys",
    Pattern.compile("^/subscription/account/\\d+/subscriptions$") -> "/subscription/account/subscriptions",
    Pattern.compile("^/subscription/account/\\d+/subscriptions/\\d+$") -> "/subscription/account/subscriptions",
    Pattern.compile("/roles/template/\\d+$") -> "/roles/template",
    Pattern.compile("/roles/\\d+$") -> "/roles",
    Pattern.compile("/roles/\\d+/dashboard_permissions$") -> "/roles/dashboard_permissions",
    Pattern.compile("/roles/template/\\d+$") -> "/roles/template/",
    Pattern.compile("/roles/\\d+$") -> "/roles",
    Pattern.compile("^/partner_and_sub_account_info/associate/user/\\d+/account/\\d+/role/\\d+$") -> "/partner_and_sub_account_info/associate/user/account/role",
    Pattern.compile("^/partner_and_sub_account_info/update/account/\\d+/accountType/\\d+$") -> "/partner_and_sub_account_info/update/accountType",
    Pattern.compile("^/partner_and_sub_account_info/update/account/\\d+/administer/\\S+$") -> "/partner_and_sub_account_info/update/administer",
    Pattern.compile("^/partner_and_sub_account_info/account/v1/\\d+$") -> "/partner_and_sub_account_info/account/v1",
    Pattern.compile("^/partner_and_sub_account_info/account/v2/\\d+$") -> "/partner_and_sub_account_info/account/v2",
    Pattern.compile("^/hierarchy/\\d+$") -> "/hierarchy",
    Pattern.compile("^/dv/configuration/\\d+$") -> "/dv/configuration",
    Pattern.compile("^/dv/configuration/\\d+$") -> "/dv/configuration",
    Pattern.compile("^/environment_settings/apikeys/\\d+$") -> "/environment_settings/apikeys",
    Pattern.compile("^/environment_settings/account/\\d+$") -> "/environment_settings/account",
    Pattern.compile("^/environment_settings/get_environment_with_domains/\\d+$") -> "/environment_settings/get_environment_with_domains",
    Pattern.compile("^/environment_settings/get_appkey_ids/\\d+$") -> "/environment_settings/get_appkey_ids",
    Pattern.compile("^/environment_settings/get_environments/\\d+$") -> "/environment_settings/get_environments",
    Pattern.compile("^/environment_settings/get_environments_by_account_ids/\\d+$") -> "/environment_settings/get_environments_by_account_ids",
    Pattern.compile("^/environment_settings/with_api_keys/\\d+$") -> "/environment_settings/with_api_keys",
    Pattern.compile("^/industries/get_industry/\\S+$") -> "/industries/get_industry",
    Pattern.compile("^/industries/get_accounts_by_industry/\\S+$") -> "/industries/get_accounts_by_industry",
    Pattern.compile("/encryption_keys/regenerate/\\d+$") -> "/encryption_keys/regenerate",
    Pattern.compile("/encryption_keys/get_account_id_by_api_key/[a-z0-9-]{36}$") -> "/encryption_keys/get_account_id_by_api_key",
    Pattern.compile("/encryption_keys/has/\\d+$") -> "/encryption_keys/has",
    Pattern.compile("/encryption_keys/get/\\d+$") -> "/encryption_keys/get",
    Pattern.compile("^/encryption_keys_v2/active_keys/\\d+$") -> "/encryption_keys_v2/active_keys",
    Pattern.compile("^/encryption_keys_v2/customer_keys/\\d+$") -> "/encryption_keys_v2/customer_keys",
    Pattern.compile("^/pgp/does_pgp_key_exists/\\d+$") -> "/pgp/does_pgp_key_exists",
    Pattern.compile("^/pgp/get_public_key/\\d+$") -> "/pgp/get_public_key",
    Pattern.compile("^/pgp/get_dec_and_sig_keys/\\d+$") -> "/pgp/get_dec_and_sig_keys",
    Pattern.compile("^/pgp/get_all_dec_and_sig_keys/\\d+$") -> "/pgp/get_all_dec_and_sig_keys",
    Pattern.compile("^/settings/webhook/account/\\d+/environment/type/\\d+/subscription/\\d+$") -> "/settings/webhook/account/environment/type/subscription",
    Pattern.compile("^/settings/webhook/environment/\\d+$") -> "/settings/webhook/environment",
    Pattern.compile("^/settings/webhook/environment/\\d+/subscription/\\d+$") -> "/settings/webhook/environment/subscription/",
    Pattern.compile("^/environment/\\d+$") -> "/environment",
    Pattern.compile("^/settings/channel/v2/environments/\\d+/channels/is_active$") -> "/settings/channel/v2/environments/channels/is_active",
    Pattern.compile("^/settings/channel/environments/\\d+/channels/is_active$") -> "/settings/channel/environments/channels/is_active",
    Pattern.compile("^/settings/channel/account/\\d+/environment/type/\\d+/subscription/\\d+$") -> "/settings/channel/account/environment/type/subscription",
    Pattern.compile("^/settings/channel/environment/\\d+/subscription/\\d+$") -> "/settings/channel/environment/subscription",
    Pattern.compile("^/settings/channel/v2/environment/\\d+$") -> "/settings/channel/v2/environment",
    Pattern.compile("^/settings/channel/environment/\\d+$") -> "/settings/channel/environment",
    Pattern.compile("^/settings/channel/v2/\\d+$") -> "/settings/channel/v2",
    Pattern.compile("^/settings/channel/\\d+$") -> "/settings/channel",
    Pattern.compile("^/settings/channel/\\d+$") -> "/settings/channel",
    Pattern.compile("^/settings/channel/v2/\\d+$") -> "/settings/channel/v2",
    Pattern.compile("^/settings/channel/\\d+$") -> "/settings/channel",
    Pattern.compile("^/ui/configuration/\\d+$") -> "/ui/configuration",
    Pattern.compile("^/modules/default/\\d+$") -> "/modules/default",
    Pattern.compile("^/modules/\\d+$") -> "/modules",
    Pattern.compile("^/idplus/fetch_account_information_v3/[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$") -> "/idplus/fetch_account_information_v3",
    Pattern.compile("^/idm/fetch_api_key/\\d+$") -> "/idm/fetch_api_key",
    Pattern.compile("^/automation/account/\\d+$") -> "/automation/account",
    Pattern.compile("^/prospect/exclusion/\\d+$") -> "/prospect/exclusion",
    Pattern.compile("^/prospect/inclusion/\\d+$") -> "/prospect/inclusion"
  )

}
