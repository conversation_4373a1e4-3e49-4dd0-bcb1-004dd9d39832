package me.socure.industry

import me.socure.account.industries.IndustriesManagementService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.Industry
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 8/31/16.
  */
class IndustryServlet(industryService: IndustriesManagementService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  get("/get_list") {
    ScalatraResponseFactory.get(industryService.getIndustriesList)
  }

  post("/delete_industry") {
    val sector = params("sector")
    ScalatraResponseFactory.get(industryService.deleteIndustry(sector))
  }

  post("/upsert_industry") {
    val industry = parsedBody.extract[Industry]
    ScalatraResponseFactory.get(industryService.upsertIndustry(industry))
  }

  get("/get_accounts_by_industry/:sector"){
    val sector = params("sector")
    ScalatraResponseFactory.get(industryService.getAccountByIndustry(sector))
  }

  get("/get_industry/:sector") {
    val sector = params("sector")
    ScalatraResponseFactory.get(industryService.getIndustryBySector(sector))
  }


}
