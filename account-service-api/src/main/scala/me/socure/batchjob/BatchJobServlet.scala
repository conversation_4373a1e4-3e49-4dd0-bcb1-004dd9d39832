package me.socure.batchjob

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Failure, Success, Try}

class BatchJobServlet (accountManagementService: AccountManagementService, val hmacVerifier: HMACHttpVerifier)
                      (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
    validateRequest()
  }

  post("/account/permission/update"){
    val updateFuture = Try{parsedBody.extract[List[AccountPermissionUpdateRequest]]} match {
      case Success(accountPermissionUpdateRequest) =>
        accountManagementService.updateAccountPermissions(accountPermissionUpdateRequest).map(Right(_))
      case Failure(exception) =>
        logger.info("Failed to Update permissions", exception)
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidInputFormat)))
    }
    ScalatraResponseFactory.get(updateFuture)
  }

}
