package me.socure.dashboard

import me.socure.account.dashboard.DashboardUserService
import me.socure.account.service.PasswordService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 6/22/16.
  */
class DashboardUserServlet(dashboardUserService : DashboardUserService, passwordService: PasswordService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/get_account_creds/:socurekey"){
    val socurekey = params("socurekey")
    ScalatraResponseFactory.get(dashboardUserService.getAccountCrdentialsByApi<PERSON>ey(socurekey))
  }

  get("/get_user/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardUserService.findUserByEmail(email))
  }

  get("/check_tos_agreement/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardUserService.checkToSAgreementByUser(email))
  }

  get("/get_primary_user/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardUserService.getPrimaryUser(email))
  }

  get("/get_accountid/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardUserService.getAccountIdByEmail(email))
  }

  get("/get_userinfo_with_accountid/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardUserService.getUserInfoWithAccountIdByEmail(email))
  }

  get("/subaccounts_for_id/:accountid") {
    val accountId = params("accountid").toLong
    val result = dashboardUserService.findSubAccountsForAccountId(accountId).map(Right(_))
    ScalatraResponseFactory.get(result)
  }


  post("/change_password") {
    val email = params("email")
    val currentPassword = params("currentpassword")
    val newpassword = params("newpassword")
    ScalatraResponseFactory.get(passwordService.changePassword(email, currentPassword, newpassword))
  }

  post("/passwordless_login/change_password") {
    val email = params("email")
    val newpassword = params("newpassword")
    ScalatraResponseFactory.get(passwordService.passwordlessLoginChangePassword(email, newpassword))
  }

}
