package me.socure.scheduler

import me.socure.account.service.ScheduledActivitiesService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 03/04/2017.
  */
class ScheduledActivitiesServlet(scheduledActivitiesService : ScheduledActivitiesService)(implicit val executor : ExecutionContext)extends BaseScalatraServlet with FutureSupport{

  /**
    * No activities found for > 90 days
    */
  post("/lock_inactive_user") {
    ScalatraResponseFactory.get(scheduledActivitiesService.lockInactiveBusinessUser)
  }

  post("/lock_user_not_loggedin_after_signup") {
    ScalatraResponseFactory.get(scheduledActivitiesService.lockPrimaryUserNotLoggedIn)
  }

  post("/invalidate_activation_tokens"){
    ScalatraResponseFactory.get(scheduledActivitiesService.invalidateActivationCode)
  }

  post("/invalidate_reset_tokens") {
    ScalatraResponseFactory.get(scheduledActivitiesService.invalidateOldPasswordResetTokens)
  }

  post("/password_reset_notification"){
    ScalatraResponseFactory.get(scheduledActivitiesService.passwordResetNotification)
  }

  delete("/delete_expired_magic_tokens"){
    ScalatraResponseFactory.get(scheduledActivitiesService.deleteExpiredMagicTokensWithAudit)
  }
}
