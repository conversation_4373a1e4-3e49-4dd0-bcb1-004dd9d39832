package me.socure.control.center

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.control.center.ControlCenterSettingUpdateRequest
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
 * Created by sumitkumar on 28/06/21
 **/
class ControlCenterServlet(controlCenterService: ControlCenterService, val hmacVerifier: HMACHttpVerifier)
                          (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
    validateRequest()
  }

  post("/settings/update"){
    val settingUpdateRequest = parsedBody.extract[ControlCenterSettingUpdateRequest]
    val settingName = settingUpdateRequest.settingName
    val updatedValue = settingUpdateRequest.value
    val updateFuture = controlCenterService.updateSetting(settingName, updatedValue)
    ScalatraResponseFactory.get(updateFuture)
  }
}
