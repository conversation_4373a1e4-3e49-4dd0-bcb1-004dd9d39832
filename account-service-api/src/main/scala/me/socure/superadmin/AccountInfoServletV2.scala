package me.socure.superadmin

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountInfoV2WithPermission, AccountInfoWithPermission, AccountPrivateValue, ApiKeyInfo}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by <PERSON><PERSON> on 07/22/19.
 */
class AccountInfoServletV2(accountInfoService: AccountInfoService, val hmacVerifier: HMACHttpVerifier)
                          (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/accounts/publicid/:publicKey") {
    val accountPublicId = params("publicKey")
    val eventualResponseOrPermission: Future[Either[ErrorResponse, AccountInfoWithPermission]] = accountInfoService.getAccountInfoWithPermission(accountPublicId).map {
      case Some(x) => Right(x)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }

    ScalatraResponseFactory.get(eventualResponseOrPermission)
  }

  get("/accounts/:accountId") {
    val accountId: Long = params("accountId").toLong
    val eventualResponseOrPermission: Future[Either[ErrorResponse, AccountInfoWithPermission]] = accountInfoService.getAccountInfoWithPermission(accountId).map {
      case Some(x) => Right(x)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }

    ScalatraResponseFactory.get(eventualResponseOrPermission)
  }


  get("/accounts/apikeys/public/:publicKey") {
    val publicKey = params("publicKey")

    val accountInfoOpt: Future[Either[ErrorResponse, Option[ApiKeyInfo]]] = accountInfoService.retrieveApiKeyByPublicKey(publicKey)
      .map {
        case Some(a) =>
          Right(Some(ApiKeyInfo(a.id, a.status, a.apiKey)))
        case _ =>
          logger.info(s"No active api keys are found for public key $publicKey")
          Right(None)
      }
      .recover {
        case e: Exception =>
          logger.info(s"Problem in fetching api keys using public key $publicKey", e)
          Left(ErrorResponse(ExceptionCodes.ApiKeyFetchFailed.id, ExceptionCodes.ApiKeyFetchFailed.code))
      }

    ScalatraResponseFactory.get(accountInfoOpt)
  }

  get("/accounts/key/public/:publicApiKey") {
    val publicApiKey = params("publicApiKey")
    val accountsInfo: Future[Either[ErrorResponse, Option[AccountInfoV2WithPermission]]] = accountInfoService.retrieveAccountInfoByPublicKey(publicApiKey)
      .map(Right(_))
      .recover {
        case e: Exception =>
          logger.info(s"Problem in fetching account information using public key $publicApiKey", e)
          Left(ErrorResponse(ExceptionCodes.PublicApiKeyAccountFetchFailed.id, ExceptionCodes.PublicApiKeyAccountFetchFailed.code))
      }
    ScalatraResponseFactory.get(accountsInfo)
  }

  post("/accounts/key/private") {
    val privateKeyValue: AccountPrivateValue = parsedBody.extract[AccountPrivateValue]
    val accountsInfo: Future[Either[ErrorResponse, Option[AccountInfoWithPermission]]] = accountInfoService.retrieveAccountInfoByPrivateKey(privateKeyValue.privateKey)
      .map(Right(_))
      .recover {
        case e: Exception =>
          logger.info(s"Problem in fetching account information using private key $privateKeyValue", e)
          Left(ErrorResponse(ExceptionCodes.PrivateApiKeyAccountFetchFailed.id, ExceptionCodes.PrivateApiKeyAccountFetchFailed.code))
      }
    ScalatraResponseFactory.get(accountsInfo)
  }

  get("/apikeys/active") {
    val accountId: Long = params("accountId").toLong
    val envTypeId: Int = params("envTypeId").toInt
    ScalatraResponseFactory.get(accountInfoService.getActiveApiKeyForAccountByEnvironmentType(accountId,envTypeId))
  }

}
