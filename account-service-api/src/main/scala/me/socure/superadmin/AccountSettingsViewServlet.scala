package me.socure.superadmin

import me.socure.account.dashboardv2.{DashboardAccountServiceV2, EnvironmentSettingsService}
import me.socure.account.service._
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account._
import me.socure.model.kyc.KycAddressMatchLogic
import me.socure.model.{AccountUIConfiguration, ErrorResponse}
import me.socure.storage.slick.dao.DaoAccountUIConfiguration
import me.socure.storage.slick.tables.account.DtoAccountUIConfiguration
import org.json4s.Formats
import org.json4s.ext.EnumNameSerializer
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext


class AccountSettingsViewServlet(accountPreferencesService: AccountPreferencesService,
                                 subscriptionChannelRegistryService: SubscriptionChannelRegistryServiceImpl,
                                 dvConfigurationService: DvConfigurationService, daoAccountUIConfiguration: DaoAccountUIConfiguration,
                                 environmentSettingsService: EnvironmentSettingsService, accountSettingService: AccountSettingService,
                                 dashboardDomainService: DashboardDomainService, dashboardAccountServiceV2: DashboardAccountServiceV2,
                                 watchlistSourceService: WatchlistSourceService, val hmacVerifier: HMACHttpVerifier)
  (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
  contentType = formats("json")
  validateRequest()
  }

  get("/account/:account_id") {
      val accountId = params("account_id").toLong
      val accountSettings = for {
        kyc <- accountPreferencesService.getKycPreferenceForAccount(accountId)
        watchlist <- accountPreferencesService.getCAWatchListPreferenceForAccount(accountId)
        subscriptions <- subscriptionChannelRegistryService.getSubscriptionChannelRegistriesForAccount(accountId)
        dv <- dvConfigurationService.getDvConfigurationForAccount(accountId)
      } yield (kyc, watchlist, subscriptions, dv)
      val res = accountSettings.map { result =>
        val kycPreferences = result._1 match {
          case Right(kyc) => kyc
          case _ =>
            logger.info(s"Error while fetching the KYC preferences for account($accountId)")
            Seq.empty
        }
        val wlPreferences = result._2 match {
          case Right(watchlist) => watchlist
          case _ =>
            logger.info(s"Error while fetching the Watchlist preferences for account($accountId)")
            Seq.empty
        }
        val webhooks = result._3 match {
          case Right(webhook) => webhook
          case _ =>
            logger.info(s"Error while fetching the Subscription Channel Registry for account($accountId)")
            Seq.empty
        }
        val dvConfigurations = result._4 match {
          case Right(dvConfiguration) => dvConfiguration
          case _ =>
            logger.info(s"Error while fetching the DV Configurations for account($accountId)")
            Seq.empty
        }
        Right(AccountSettings(kyc = kycPreferences, wl = wlPreferences, webhook = webhooks, dv = dvConfigurations))
      } recover {
        case e: Exception =>
          logger.info(s"Problem in fetching account settings for account $accountId", e)
          Left(ErrorResponse(ExceptionCodes.UnknownError.id, ExceptionCodes.UnknownError.code))
      }
    ScalatraResponseFactory.get(res)
  }

  get("/account/overall/:account_id") {
    val accountId = params("account_id").toLong
    val environmentId = params("environmentId").toLong
    logger.info(s"$environmentId")

    val overallAccountSettings = for {
      kyc <- accountPreferencesService.getKycPreference(environmentId)
      watchlist <- accountPreferencesService.getCAWatchListPreference(environmentId)
      dv <- dvConfigurationService.listDvConfigurationByEnvironment(environmentId)
      ui <- daoAccountUIConfiguration.getUIAccountConfiguration(accountId)
      envWithDomains <- environmentSettingsService.getEnvironmentWithDomains(accountId)
      dashboardDomains <- dashboardDomainService.getWhitelistedDomainForAccount(accountId)
      watchListSources <- watchlistSourceService.includedWatchlistSources(environmentId)
      modulesByAccountId <- accountSettingService.getModulesByAccountId(accountId, None, forceValidate = false)
      defaultModules <- dashboardAccountServiceV2.getDefaultModulesForAccounts(accountId, None, forceValidate = false)
    } yield (kyc, watchlist, dv, ui, envWithDomains, dashboardDomains, watchListSources, modulesByAccountId, defaultModules)
    val res = overallAccountSettings.map { result =>
      val kycPreferences = result._1 match {
        case Right(kyc) => Some(kyc)
        case Left(error) =>
          logger.info(s"Error while fetching the KYC preferences for account($accountId) : ${error}")
          None
      }
      val wlPreferences = result._2 match {
        case Right(watchlist) => Some(watchlist)
        case Left(error) =>
          logger.info(s"Error while fetching the Watchlist preferences for account($accountId) : ${error}")
          None
      }
      val dvConfigurations = result._3 match {
        case Right(dvConfiguration) =>
          dvConfiguration.map {
            dv =>
              (dv._1, dv._2.getVerboseDvConfigurationForDebug)
          }
        case Left(error) =>
          logger.info(s"Error while fetching the DV Configurations for account($accountId) : ${error}")
          Map.empty[String, VerboseDvConfiguration]
      }

      val uiConfig = result._4 match {
        case Some(dtoUIAccountConfiguration: DtoAccountUIConfiguration) =>
          Some(AccountUIConfiguration(Some(dtoUIAccountConfiguration.accountId), Some(dtoUIAccountConfiguration.autoTimeoutInMinutes), Some(dtoUIAccountConfiguration.idleTimeoutInMinutes), None, dtoUIAccountConfiguration.isForceInherit, dtoUIAccountConfiguration.hideSystemDefinedRoles))
        case None =>
          logger.info(s"Error while fetching the UI preferences for account($accountId)")
          None
      }

      val envDomains = result._5 match {
        case Right(envDomains) =>
          envDomains
        case Left(error) =>
          logger.error(s"Error while fetching the env domains for account($accountId) : ${error}")
          Seq.empty[EnvironmentWithDomains]
      }

      val dashboardDomains = result._6 match {
        case Right(dashboardDomains) =>
          val dashboardDomainsSeq: Seq[String] = dashboardDomains.split(",").map(_.trim).toSeq
          dashboardDomainsSeq
        case Left(error) =>
          logger.info(s"Error while fetching the dashboard domains for account($accountId) : ${error}")
          Seq.empty[String]
      }

      val watchListSources = result._7 match {
        case Right(watchList) => {
          watchList
        }
        case Left(error) =>
          logger.info(s"Error while fetching the watchlist sources for account($accountId) : ${error}")
          Seq.empty

      }

      val allModules = result._8 match {
        case Right(modules) =>
          modules.map { module => (module.id, module.label) }
        case Left(error) =>
          logger.info(s"Error while fetching modules for account($accountId) : ${error}")
          Seq.empty[(Int, String)]
      }

      val defaultModules = result._9 match {
        case Right(defaultModule) =>
          defaultModule.modules.map {
            moduleId =>
              allModules.find(allMod => allMod._1 == moduleId).map { case (_, label) => label }.getOrElse(moduleId.toString)
          }.toSeq

        case Left(error) =>
          logger.info(s"Error while fetching the DV Configurations for account($accountId) : ${error}")
          Seq.empty[String]
      }

      Right(OverallEnvironmentSettings(kyc = kycPreferences, wl = wlPreferences, dv = dvConfigurations, accountUIConfiguration = uiConfig, defaultModules, watchListSources, envDomains, dashboardDomains))
    } recover {
      case e: Exception =>
        logger.info(s"Problem in fetching account settings for account $accountId", e)
        Left(ErrorResponse(ExceptionCodes.UnknownError.id, ExceptionCodes.UnknownError.code))
    }
    ScalatraResponseFactory.getGeneric(res)
  }
}
