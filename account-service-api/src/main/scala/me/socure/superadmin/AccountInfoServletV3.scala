package me.socure.superadmin

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountInfoV3, AccountPrivateValue}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by <PERSON><PERSON> on 08/04/20.
 */
class AccountInfoServletV3(accountInfoService: AccountInfoService, val hmacVerifier: HMACHttpVerifier)
                          (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/accounts/key/public/:publicApiKey") {
    val publicApiKey = params("publicApiKey")
    val accountsInfo: Future[Either[ErrorResponse, Option[AccountInfoV3]]] = accountInfoService.retrieveAccountInfoByPublicKeyV3(publicApiKey)
      .map(Right(_))
      .recover {
        case e: Exception =>
          logger.info(s"Problem in fetching account information using public key $publicApiKey", e)
          Left(ErrorResponse(ExceptionCodes.PublicApiKeyAccountFetchFailed.id, ExceptionCodes.PublicApiKeyAccountFetchFailed.code))
      }
    ScalatraResponseFactory.get(accountsInfo)
  }

  post("/accounts/key/private") {
    val privateKeyValue: AccountPrivateValue = parsedBody.extract[AccountPrivateValue]
    val accountsInfo: Future[Either[ErrorResponse, Option[AccountInfoV3]]] = accountInfoService.retrieveAccountInfoByPrivateKeyV3(privateKeyValue.privateKey)
      .map(Right(_))
      .recover {
        case e: Exception =>
          logger.info(s"Problem in fetching account information using private key $privateKeyValue", e)
          Left(ErrorResponse(ExceptionCodes.PrivateApiKeyAccountFetchFailed.id, ExceptionCodes.PrivateApiKeyAccountFetchFailed.code))
      }
    ScalatraResponseFactory.get(accountsInfo)
  }

}
