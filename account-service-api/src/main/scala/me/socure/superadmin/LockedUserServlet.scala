package me.socure.superadmin

import me.socure.account.service.common.exceptions.ExceptionCodes.MissingRequiredParameters
import me.socure.account.service.common.servlet.{BaseScalatraServlet, BaseServletParamHandling}
import me.socure.account.superadmin.LockedUserService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/3/16.
  */
class LockedUserServlet(lockedUserService: LockedUserService)(implicit val executor : ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with BaseServletParamHandling {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/list") {
    ScalatraResponseFactory.get(lockedUserService.getLockedUserList)
  }

  post("/unlock") {
    val accountIds = fetchInputParam("emails").split(",").toList
    ScalatraResponseFactory.get(lockedUserService.unlockUser(accountIds))
  }

  error {
    case e : Throwable =>
      ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(MissingRequiredParameters.id, MissingRequiredParameters.description))))
  }
}
