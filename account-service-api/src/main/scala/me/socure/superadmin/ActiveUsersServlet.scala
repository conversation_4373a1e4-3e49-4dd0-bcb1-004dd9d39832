package me.socure.superadmin

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.ActiveUsersService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.AccountDomain
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON> on 5/30/16.
  */
class ActiveUsersServlet(activeUserService : ActiveUsersService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  post("/mark_internal") {
    val emails = request.getParameter("emails").split(",").toList
    ScalatraResponseFactory.get(activeUserService.markAsInternal(emails))
  }

  post("/unmark_internal") {
    val emails = request.getParameter("emails").split(",").toList
    ScalatraResponseFactory.get(activeUserService.unmarkAsInternal(emails))
  }

  post("/deactivate"){
    val emails = request.getParameter("emails").split(",").toList
    ScalatraResponseFactory.get(activeUserService.deactivateUser(emails))
  }

  post("/mark/internal") {
    ScalatraResponseFactory.get(activeUserService.markAccountAsInternal(getAccountId))
  }

  post("/unmark/internal") {
    ScalatraResponseFactory.get(activeUserService.unmarkAccountAsInternal(getAccountId))
  }

  post("/deactivate_account"){
    ScalatraResponseFactory.get(activeUserService.deactivateAccount(getAccountId))
  }

  get("/get_domains/:account_id"){
    ScalatraResponseFactory.get(activeUserService.getDomainByAccountId(getAccountId))
  }

  post("/add_domains"){
    val domains = parsedBody.extract[AccountDomain]
    ScalatraResponseFactory.get(activeUserService.addDomainByAccountId(domains.accountId, domains.domain))

  }

  post("/delete_account") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(activeUserService.deleteAccount(accountId))
  }

  get("/deleted_accounts_list") {
    ScalatraResponseFactory.get(activeUserService.getDeletedAccountsList.map(Right(_)))
  }

  private def getAccountId(): Long = {
    (params("account_id") match {
      case x if x.nonEmpty => x.toLong
      case _ => 0
    })
  }

}
