package me.socure.superadmin

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.LeaderboardService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON> on 6/17/16.
  */
class LeaderboardServlet(leaderboardService : LeaderboardService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/get_internal_accounts") {
    ScalatraResponseFactory.get(leaderboardService.getInternalAccountIds)
  }

  get("/get_account_name"){
    ScalatraResponseFactory.get(leaderboardService.getAccountNameList)
  }

  get("/get_account_by_apikey/:apikey"){ //TODO: This endpoint is used for clearing cache. So it should be env specific. Currently is implemented with default env
    val apiKey = params("apikey")
    ScalatraResponseFactory.get(leaderboardService.getAccountByApiKey(apiKey))
  }


}
