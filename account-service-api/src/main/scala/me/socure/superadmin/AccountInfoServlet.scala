package me.socure.superadmin

import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.AccountInfoService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.convertors.LegacyModelConverters
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountAnalyticsInfoRequest, AccountIdName, AccountIdNamesByRolesRequest, AccountIdNamesByRolesRequestLegacy, PublicAccountIdName}
import me.socure.model.analytics.AnalyticsGlobalInfoRequest
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization.{AccountWithEnvironmentDetails, AccountWithEnvironmentDetailsLegacy, AccountWithEnvironmentDetailsWithPublicId, AccountWithEnvironmentDetailsWithPublicIdLegacy}
import org.joda.time.format.DateTimeFormat
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 9/15/16.
  */
class AccountInfoServlet(accountInfoService: AccountInfoService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/publicid/accounts/:accountId") {
    val accountId = params("accountId").toLong
    val result = accountInfoService.fetchPublicIdByAccountId(accountId).map {
      case Some(x) => Right(x)
      case _ => Left(ErrorResponseFactory.get(AccountNotFound))
    }
    ScalatraResponseFactory.get(result)
  }

  get("/list") {
    val result = accountInfoService.list()
    ScalatraResponseFactory.get(result)
  }

  get("/list_account_ids") {
    ScalatraResponseFactory.get(accountInfoService.listAccountIds())
  }

  get("/get_all_account_names"){
    val resultFuture: Future[Either[ErrorResponse, Map[Long, AccountIdName]]] = accountInfoService.getActiveAccountNameList
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_non_internal_active_accounts") {
    val resultFuture: Future[Either[ErrorResponse, Seq[Long]]] = accountInfoService.getNonInternalActiveAccountList
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/has_role") {
    val apiKeyString = ApiKeyString(params("api_key"))
    val resultFuture: Future[Either[ErrorResponse, Boolean]] = accountInfoService.hasRole(
      apiKeyString = apiKeyString,
      role = params("role").toInt
    ).map {
      case Some(hasRole) => Right(hasRole)
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_parent_accounts_no_prim_users"){
    val resultFuture: Future[Either[ErrorResponse, Seq[AccountIdName]]] = accountInfoService.getParentAccountsWithoutPrimaryUsers()
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_account_details_by_public_id") {
    val publicId = params("publicId")
    val resultFuture: Future[Either[ErrorResponse, AccountWithEnvironmentDetailsLegacy] with Product with Serializable] = accountInfoService.getAccountWithEnvironmentDetailsByPublicId(publicId = publicId).map {
      case Some(accWithEnv) => Right(LegacyModelConverters.toAccountWithEnvironmentDetailsLegacy(accWithEnv))
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_account_details_by_public_id_v2") {
    val publicId = params("publicId")
    val resultFuture: Future[Either[ErrorResponse, AccountWithEnvironmentDetails] with Product with Serializable] = accountInfoService.getAccountWithEnvironmentDetailsByPublicId(publicId = publicId).map {
      case Some(accWithEnv) => Right(accWithEnv)
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_account_details_by_id/:id") {
    val id = params("id").toLong
    val resultFuture: Future[Either[ErrorResponse, AccountWithEnvironmentDetailsWithPublicIdLegacy]] = accountInfoService.getAccountWithEnvironmentDetailsById(id = id).map {
      case Some(accWithEnv) => Right(LegacyModelConverters.toAccountWithEnvironmentDetailsWithPublicIdLegacy(accWithEnv))
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_account_details_by_id_v2/:id") {
    val id = params("id").toLong
    val resultFuture: Future[Either[ErrorResponse, AccountWithEnvironmentDetailsWithPublicId]] = accountInfoService.getAccountWithEnvironmentDetailsById(id = id).map {
      case Some(accWithEnv) => Right(accWithEnv)
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/get_account_id_names_by_roles") {
    val accountIdNamesByRolesRequest = parsedBody.extract[AccountIdNamesByRolesRequestLegacy]
    ScalatraResponseFactory.get(
      accountInfoService.getAccountIdNamesWithRoles(
        roles = LegacyModelConverters.namesToIds(accountIdNamesByRolesRequest.roles),
        onlyParents = accountIdNamesByRolesRequest.onlyParent
      ).map(Right.apply)
    )
  }

  post("/get_account_id_names_by_roles_v2") {
    val accountIdNamesByRolesRequest = parsedBody.extract[AccountIdNamesByRolesRequest]
    ScalatraResponseFactory.get(
      accountInfoService.getAccountIdNamesWithRoles(
        roles = accountIdNamesByRolesRequest.roles,
        onlyParents = accountIdNamesByRolesRequest.onlyParent
      ).map(Right.apply)
    )
  }

  get("/get_account_name_by_id/:account_id") {
    val account_id = params("account_id").toLong
    ScalatraResponseFactory.get(
      accountInfoService.getAccountNameById(account_id).map{
        case Some(x) => Right(x)
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      }
    )
  }

  get("/public-account/:account_id") {
    val account_id = params("account_id").toLong
    ScalatraResponseFactory.get(
      accountInfoService.getAccountNameAndPublicId(account_id).map{
        case Some(x) => Right(x)
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      }
    )
  }

  get("/get_encrypted_parent_accounts"){
    val result = accountInfoService.getEncryptedParentAccounts()
    ScalatraResponseFactory.get(result)
  }

  get("/preferences/:id"){
    val accountId = params("id").toLong
    ScalatraResponseFactory.get(
      accountInfoService.getAccountPreferences(accountId).map{
        case Some(x) => Right(x)
        case _ => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      }
    )
  }

  get("/get_all_account_names_with_public_id"){
    val resultFuture: Future[Either[ErrorResponse, Map[String, PublicAccountIdName]]] = accountInfoService.getAllAccountNamesWithPublicId
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/analytics/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(accountInfoService.getAnalyticsImportInfo(accountId))
  }

  post("/analytics"){
    val analyticsInfoReq = parsedBody.extract[AccountAnalyticsInfoRequest]
    ScalatraResponseFactory.get(accountInfoService.addAnalyticsImportInfo(analyticsInfoReq))
  }

  put("/analytics/last_imported_date"){
    val accountId = params("account_id").toLong
    val dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    val lastImportedDateStr = params("last_imported_date")
    val lastImportedDate = dateTimeFormatter.parseDateTime(lastImportedDateStr)
    ScalatraResponseFactory.get(accountInfoService.updateAnalyticsLastImportedDate(accountId, lastImportedDate))
  }

  get("/analytics"){
    ScalatraResponseFactory.get(accountInfoService.getAnalyticsGlobalImportInfo())
  }

  post("/analytics"){
    val analyticGlobalInfoReq = parsedBody.extract[AnalyticsGlobalInfoRequest]
    ScalatraResponseFactory.get(accountInfoService.addAnalyticsGlobalImportInfo(analyticGlobalInfoReq))
  }

  get("/get_accounts_with_permission"){
    val permissionId = params("permission_id").toInt
    ScalatraResponseFactory.get(accountInfoService.getAccountsWithPermission(permissionId))
  }

  get("/get_accounts_with_and_without_permission"){
    val withPermissionId = params("with_permission_id").toInt
    val withoutPermissionId = params("with_out_permission_id").toInt
    ScalatraResponseFactory.get(accountInfoService.getAccountIdNamesWithAndWithoutPermission(withPermissionId, withoutPermissionId))
  }
}
