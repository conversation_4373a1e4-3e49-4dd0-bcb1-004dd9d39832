package me.socure.superadmin

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.DelegatedAdminService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.convertors.LegacyModelConverters
import me.socure.model.ErrorResponse
import me.socure.model.superadmin.{DelegatedAdmin, DelegatedAdminLegacy}
import me.socure.util.JsonEnrichments._
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/9/16.
  */
class DelegatedAdminServlet(delegateAdminService : DelegatedAdminService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/get_all_admins") {
    ScalatraResponseFactory.get(delegateAdminService.getAccountName)
  }

  get("/delegated_admins/:email") {
    val email = params("email")
    val resultFuture: Future[Either[ErrorResponse, Vector[DelegatedAdminLegacy]]] = delegateAdminService
      .getDelegatedAdminForBusinessUser(email)
      .map(_.right.map(_.map(LegacyModelConverters.toDelegatedAdminLegacy)))

    ScalatraResponseFactory.get(resultFuture)
  }

  get("/delegated_admins_v2/:email") {
    val email = params("email")
    val resultFuture: Future[Either[ErrorResponse, Vector[DelegatedAdmin]]] = delegateAdminService.getDelegatedAdminForBusinessUser(email)
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/create_delegated_admin"){
    val newAdmin = params("user").decodeJson[DelegatedAdminLegacy]
    val adminEmail = params("adminemail")
    val password = params("password")
    ScalatraResponseFactory.get(delegateAdminService.createDelegatedAdmin(adminEmail, LegacyModelConverters.toDelegatedAdmin(newAdmin), password))
  }

  post("/create_delegated_admin_v2"){
    val newAdmin = params("user").decodeJson[DelegatedAdmin]
    val adminEmail = params("adminemail")
    val password = params("password")
    ScalatraResponseFactory.get(delegateAdminService.createDelegatedAdmin(adminEmail, newAdmin, password))
  }

  post("/delete_delegated_admin") {
    val email : String = params("email")
    ScalatraResponseFactory.get(delegateAdminService.deleteDelegatedAdmin(email))
  }

  post("/update_password") {
    val email : String = params("email")
    val password : String = params("password")
    ScalatraResponseFactory.get(delegateAdminService.resetPassword(email, password))
  }

  post("/update_roles") {
    val email : String = params("email")
    val roles : Set[Int] = params("roles").split(",").map(_.toInt).toSet
    ScalatraResponseFactory.get(delegateAdminService.updatePermission(email, roles))
  }

  post("/update_user_information"){
    val updateInfo = params("user").decodeJson[DelegatedAdminLegacy]
    val email = params("email")
    ScalatraResponseFactory.get(delegateAdminService.updateUserInformation(email, LegacyModelConverters.toDelegatedAdmin(updateInfo)))
  }

  post("/update_user_information_v2"){
    val updateInfo = params("user").decodeJson[DelegatedAdmin]
    val email = params("email")
    ScalatraResponseFactory.get(delegateAdminService.updateUserInformation(email, updateInfo))
  }

  post("/promote") {
    val email = params("email")
    ScalatraResponseFactory.get(delegateAdminService.promoteDelegatedAdmin(email))
  }

  get("/is_user_exist/:email"){
    val email = params("email")
    ScalatraResponseFactory.get(delegateAdminService.isUserExist(email))
  }
}
