package me.socure.superadmin

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.superadmin.InactiveUserService
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.convertors.LegacyModelConverters
import me.socure.model.ErrorResponse
import me.socure.model.user.{PrimaryAccountUser, PrimaryAccountUserLegacy}
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/7/16.
  */
class InactiveUserServlet(inactiveUserService : InactiveUserService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/inactive_users") {
    val resultFuture: Future[Either[ErrorResponse, Vector[PrimaryAccountUserLegacy]]] = inactiveUserService
      .getInactivesPrimaryAccountAdmins
      .map(values => Right(
        values.map(LegacyModelConverters.toPrimaryAccountUserLegacy)
      ))
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/inactive_users_v2") {
    val resultFuture: Future[Either[ErrorResponse, Vector[PrimaryAccountUser]]] = inactiveUserService
      .getInactivesPrimaryAccountAdmins
      .map(Right(_))
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/get_activation_code/:email") {
    val email = params("email").split(",").toList
    ScalatraResponseFactory.get(inactiveUserService.getActivationLink(email))
  }

  get("/get_reset_code/:emails") {
    val emails = params("emails").split(",").toList
    ScalatraResponseFactory.get(inactiveUserService.getPasswordResetLinkAsActivationDetails(emails))
  }
}
