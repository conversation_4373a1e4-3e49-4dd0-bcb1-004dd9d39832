package me.socure.etl

import me.socure.account.etl.EnvironmentUpdaterService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON><PERSON> on 11/3/16.
  */
class EnvironmentUpdaterServlet(service : EnvironmentUpdaterService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  get("/get_environment_table") {
    ScalatraResponseFactory.get(service.getEnvironmentTable.map(Right(_)))
  }

  get("/get_environment_type_table") {
    ScalatraResponseFactory.get(service.getEnvironmentTypeTable.map(Right(_)))
  }

}
