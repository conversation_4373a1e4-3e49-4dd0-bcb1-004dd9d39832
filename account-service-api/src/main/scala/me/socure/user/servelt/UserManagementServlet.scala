package me.socure.user.servelt

import me.socure.account.service.common.exceptions.ExceptionCodes.ExceptionCodes
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.service.{BusinessUserService, PasswordService}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.convertors.LegacyModelConverters
import me.socure.model.ErrorResponse
import me.socure.model.user._
import me.socure.model.user.authorization.{CognitoStatus, UserAuth, UserAuthLegacy, UserAuthV2}
import me.socure.password.PasswordConstraintCheck
import me.socure.storage.slick.tables.user.DtoBusinessUser
import me.socure.user.validator.UserFormValidator
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import scala.util.Try

/**
  * Created by gopal on 11/05/16.
  */

class UserManagementServlet(val businessUserService: BusinessUserService,
                           passwordService: PasswordService)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  private val LOGGER: Logger = LoggerFactory.getLogger(this.getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  post("/register") {
    val userform = parsedBody.extract[UserForm]
    val isActive = params.get("isActive").map(_.toBoolean)

    UserFormValidator.validateParams(userform) match {
      case Right(true) => {
        PasswordConstraintCheck.validatePasswordConstraint(userform.email, userform.firstname, userform.lastname, userform.password) match {
          case Right(true) => ScalatraResponseFactory.get(businessUserService.register(userform, isActive.getOrElse(false)))
          case Right(false) => ScalatraResponseFactory.get(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy))))
          case Left(x) => ScalatraResponseFactory.get(Future.successful(Left(x)))
        }
      }
      case Left(x) => ScalatraResponseFactory.get(Future.successful(Left(x)))
    }
  }

  post("/v2/register") {
    val userform = parsedBody.extract[UserFormV2]
    val isActive = params.get("isActive").map(_.toBoolean)
    val isDashboardV3 = params.get("isDashboardV3").map(_.toBoolean)

    UserFormValidator.validateParams(userform) match {
      case Right(true) => {
        PasswordConstraintCheck.validatePasswordConstraint(userform.email, userform.firstname, userform.lastname, userform.password) match {
          case Right(true) => ScalatraResponseFactory.get(businessUserService.registerV2(userform, isActive.getOrElse(false), isDashboardV3.getOrElse(false)))
          case Right(false) => ScalatraResponseFactory.get(Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.PasswordPolicy))))
          case Left(x) => ScalatraResponseFactory.get(Future.successful(Left(x)))
        }
      }
      case Left(x) => ScalatraResponseFactory.get(Future.successful(Left(x)))
    }
  }

  post("/register_primary_user_parent_account") {
    val userForm = parsedBody.extract[ParentAccountPrimaryUserForm]
    ScalatraResponseFactory.get(businessUserService.registerPrimaryUserParentAccount(userForm))
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  post("/validate") {
    val credentional = parsedBody.extract[UserCredential]
    val resultFuture: Future[Either[ErrorResponse, UserAuthLegacy]] = {
      businessUserService
        .validateUser(credentional)
        .map(_.right.map(LegacyModelConverters.toUserAuthLegacy))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/validate_v2") {
    val credentional = parsedBody.extract[UserCredential]
    val resultFuture: Future[Either[ErrorResponse, UserAuth]] = {
      businessUserService.validateUser(credentional)
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/v3/validate") {
    val credentional = parsedBody.extract[UserCredential]
    val resultFuture: Future[Either[ErrorResponse, UserAuthV2]] = {
      businessUserService.validateUserV2(credentional)
    }
    ScalatraResponseFactory.get(resultFuture)
  }
  get("/get_validated_user") {
    val  userId = params.get("userId").getOrElse(halt(400, "userId is not provided")).toLong
    val userName = params.get("userName").getOrElse(halt(400, "userName is not provided"))
    ScalatraResponseFactory.get(businessUserService.getValidatedUserAuth(userId, userName))
  }
  get("/get_cognito_status") {
    val userName = params.get("userName").getOrElse(halt(400, "userName is not provided"))
    ScalatraResponseFactory.get(businessUserService.getCognitoStatus(userName))
  }
  get("/get_cognito_status_by_id") {
    val userId = params.get("userId").getOrElse(halt(400, "userId is not provided")).toLong
    ScalatraResponseFactory.get(businessUserService.getCognitoStatusById(userId))
  }
  post("/update_cognito_status") {
    val userName = params.get("userName").getOrElse(halt(400, "userName is not provided"))
    val cognitoStatus = params.get("migrationStatus").getOrElse(halt(400, "status is not provided")).toInt
    ScalatraResponseFactory.get(businessUserService.updateCognitoStatus(userName, cognitoStatus))
  }
  get("/activate_user/:activationcode") {
    val activationCode =  params("activationcode")
    ScalatraResponseFactory.get(businessUserService.activateUserByActivationcode(activationCode))
  }

  post("/activate_users") {
    val emails =  parsedBody.extract[List[String]]
    ScalatraResponseFactory.get(businessUserService.activateUsers(emails))
  }

  post("/change_password") { //TODO:This is not used in the legacy code. So check once again and clean up if not used
    val changeForm = parsedBody.extract[PasswordChangeForm]
    ScalatraResponseFactory.get(passwordService.setPassword(changeForm))
  }

  get("/active_users_v2") {
    val start = params.get("start").flatMap(s => Try(s.toInt).toOption)
    val size = params.get("size").flatMap(s => Try(s.toInt).toOption)
    val search = params.get("search").getOrElse("")
    val resultFuture: Future[Either[ErrorResponse, Vector[PrimaryAccountUser]]] = {
      businessUserService
        .getActivesPrimaryAccountAdmins(start, size, search)
        .map(Right(_))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  get("/active_users_count") {
    val resultFuture: Future[Either[ErrorResponse, Int]] = {
      businessUserService
        .getActivesPrimaryAccountAdminsCount
        .map(Right(_))
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/log_bad_login_try"){
    val email = params("email")
    val errorMsg = params("errormsg")

    ScalatraResponseFactory.get(businessUserService.logBadLoginAttempt(email, errorMsg).map(Right(_)))
  }

  get("/get_invalid_attempts") {
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.getInvalidAttemptsByEmail(email).map(Right(_)))
  }

  post("/unlock_automatically"){
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.unlockIfNeeded(email).map(Right.apply))
  }

  post("/set_password_by_activationcode"){
    val activationCode = params("activationcode")
    val password = params("password")
    ScalatraResponseFactory.get(passwordService.setPasswordWithActivationCode(activationCode, password))
  }

  post("/reset_password_by_resetcode"){
    val resetcode = params("resetcode")
    val password = params("password")
    ScalatraResponseFactory.get(passwordService.setPasswordWithResetCode(resetcode, password))
  }

  get("/get_user_by_activationcode/:activationcode"){
    val activationCode = params("activationcode")
    ScalatraResponseFactory.get(businessUserService.getUserByActivationCode(activationCode))
  }

  get("/get_user_by_resetcode/:resetcode") {
    val resetCode = params("resetcode")
    ScalatraResponseFactory.get(businessUserService.getUserByResetCode(resetCode))
  }

  get("/get_account_id/:username") {
    val uname = params("username")
    ScalatraResponseFactory.get(businessUserService.getAccountIdByUsername(uname))
  }

  get("/is_user_locked/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.isUserLocked(email))
  }

  get("/is_user_internal/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.isUserInternal(email))
  }

  get("/get_users_with_roles/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(businessUserService.getBusinessUsersWithRoleForAccount(accountId))
  }


  put("/:userId/promote/primary") {
    val userIdOpt: Option[Long] = Option(params("userId")).map(_.toLong)

    val serviceResponse: Future[Either[ExceptionCodes, DtoBusinessUser]] = userIdOpt match {
      case Some(userId) =>
        businessUserService.promotePrimaryUser(userId)
      case None =>
        LOGGER.info("No user id provided")
        Future.successful(Left(ExceptionCodes.UserNotFound))
    }
    val response: Future[Either[ErrorResponse, PromoteUserResponse]] = serviceResponse.map {
      case Right(dtoBusinessUser) =>
        Right(PromoteUserResponse(dtoBusinessUser.id, userIdOpt.getOrElse(0)))
      case Left(errorCode) => Left(ErrorResponseFactory.get(errorCode))
    }
    ScalatraResponseFactory.get(response)
  }

  post("/does_email_exist") {
    val email = params.get("email").getOrElse(halt(400, "email is not provided"))
    ScalatraResponseFactory.get(businessUserService.doesUserExist(email))
  }

  get("/get_login_details"){
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.getUserLoginDetails(email))
  }

  get("/validate_email_domain") {
    val email = params("email")
    val accountType = params("accountType").toInt
    ScalatraResponseFactory.get(businessUserService.validateEmailDomain(email, accountType))
  }

  get("/validate_inclusion_list") {
    val email = params("email")
    val accountType = params("accountType").toInt
    ScalatraResponseFactory.get(businessUserService.isDomainFromInclusionList(email, accountType))
  }

  get("/is_user_locked_by_admin") {
    val email = params("email")
    ScalatraResponseFactory.get(passwordService.isUserLockedByAdmin(email))
  }

  post("/generate_magic_token"){
    val email = params("email")
    val userAgent = params("user_agent")
    ScalatraResponseFactory.get(businessUserService.checkAndGenerateMagicToken(email, userAgent))
  }

  post("/generate_document_token") {
    val email = params("email")
    val userAgent = params("user_agent")
    ScalatraResponseFactory.get(businessUserService.checkAndGenerateDocumentLinkToken(email, userAgent))
  }

  post("/passwordless/validate_v2") {
    val credential = parsedBody.extract[PasswordlessLoginCredential]
    val resultFuture: Future[Either[ErrorResponse, UserAuth]] = {
      businessUserService.validateUserForPasswordlessLogin(credential)
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  post("/docs/passwordless/validate_v2") {
    val credential = parsedBody.extract[PasswordlessLoginCredential]
    val resultFuture: Future[Either[ErrorResponse, UserAuth]] = {
      businessUserService.validateEmailForDocsPasswordlessLogin(credential)
    }
    ScalatraResponseFactory.get(resultFuture)
  }

  delete("/delete_magic_token") {
    val userId = params("userid")
    val userAgent = params("user_agent")
    ScalatraResponseFactory.get(businessUserService.deleteMagicTokenForUser(userId.toLong, userAgent))
  }

  get("/get_business_user_info") {
    val businessUserId = params("businessUserId").toLong
     ScalatraResponseFactory.get(businessUserService.getBusinessUserInfo(businessUserId))
    
  }
  get("/unlock_cognito_user"){
    val email = params("email")
    ScalatraResponseFactory.get(businessUserService.unlockCognitoUser(email))
  }
}
