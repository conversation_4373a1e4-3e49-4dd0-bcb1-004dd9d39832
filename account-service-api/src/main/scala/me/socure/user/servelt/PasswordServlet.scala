package me.socure.user.servelt

import me.socure.account.service.common.exceptions.ExceptionCodes.{InvalidInputFormat, MissingRequiredParameters}
import me.socure.account.service.common.servlet.{BaseScalatraServlet, BaseServletParamHandling}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.password.PasswordConstraintCheck
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 3/26/17.
  */
class PasswordServlet(implicit val executor: ExecutionContext)
  extends BaseScalatraServlet
    with FutureSupport
    with BaseServletParamHandling {

  private val logger = LoggerFactory.getLogger(classOf[PasswordServlet])

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  before() {
    contentType = formats("json")
  }

  error {
    case e : Throwable =>
      logger.info("An error occured", e)
      ScalatraResponseFactory.get(Future.successful(Left(ErrorResponse(MissingRequiredParameters.id,
                                                                      MissingRequiredParameters.description))))
  }

  post("/syntax_check") {
    val password = fetchInputParam("password")
    val email = fetchInputParam("email")
    val firstname = fetchInputParam("firstname")
    val lastname = fetchInputParam("lastname")

    ScalatraResponseFactory.get(Future {
      val result = PasswordConstraintCheck.validatePasswordConstraint(
        email = email,
        firstname = firstname,
        lastname = lastname,
        password = password)
      result
    })
  }
}
