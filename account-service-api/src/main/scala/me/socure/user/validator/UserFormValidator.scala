package me.socure.user.validator

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.constants.AccountTypes
import me.socure.model.ErrorResponse
import me.socure.model.user.{UserForm, UserFormV2}
import org.apache.commons.validator.routines.EmailValidator
import org.slf4j.LoggerFactory

object UserFormValidator {

  private val logger = LoggerFactory.getLogger(UserFormValidator.getClass)

  def validateParams(userForm: UserForm): Either[ErrorResponse, Boolean] = {
    val res = isValidString(userForm.firstname,"First Name") &&
      isValidString(userForm.lastname,"Last Name") &&
      EmailValidator.getInstance().isValid(userForm.email) &&
      isValidString(userForm.companyname, "Company Name")
    res match {
      case true => Right(true)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed))
    }
  }

  def validateParams(userForm: UserFormV2): Either[ErrorResponse, Boolean] = {
    val res = isValidString(userForm.firstname,"First Name") &&
      isValidString(userForm.lastname,"Last Name") &&
      EmailValidator.getInstance().isValid(userForm.email) &&
      isValidString(userForm.companyname, "Company Name") &&
      AccountTypes.isValid(userForm.accountType)
    res match {
      case true => Right(true)
      case _ => Left(ErrorResponseFactory.get(ExceptionCodes.RegistrationFailed))
    }
  }

  private def isValidString(string : String, information: String): Boolean = {
    Option(string).exists(_.trim.nonEmpty) match {
      case true => true
      case false =>
        logger.info(s"${information} is invalid")
        false
    }
  }
}
