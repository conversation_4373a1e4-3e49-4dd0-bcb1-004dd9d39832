package me.socure.dashboardv2

import java.net.URLDecoder
import java.nio.charset.StandardCharsets

import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.dashboardv2.Creator
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

  class ModuleManagementServlet(dashboardAccountServiceV2: DashboardAccountServiceV2,
                            val hmacVerifier: HMACHttpVerifier)
                           (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

    override val logger: Logger = LoggerFactory.getLogger(getClass)
    override protected implicit def jsonFormats: Formats = JsonFormats.formats
    before() {
      validateRequest()
    }

    get("/:account_id") {
      val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toInt
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getAccountModules(accountId, creatorAccountId))
    }

    get("/accounts/default/:account_id") {
      val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toInt
      val creatorUserId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getDefaultModulesForAccounts(accountId, Some(Creator(creatorUserId, creatorAccountId)),forceValidate = true))
    }

    get("/default/:account_id") {
      val accountId = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toInt
      val creatorUserId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getDefaultModules(accountId, Creator(creatorUserId, creatorAccountId)))
    }

    post("/default/:account_id") {
      val accountId : Long = params("account_id").toLong
      val modules0 = params("modules")
      val modules = try {
        if(modules0.trim.nonEmpty) {
          URLDecoder.decode(modules0, StandardCharsets.UTF_8.name()).trim.split(",").map(_.trim.toInt).toSet
        }else {
          Set.empty[Int]
        }
      } catch {
        case ex: Exception =>
          logger.info(s"Invalid default modules $modules0 for account: $accountId", ex)
          halt(500, "Invalid modules: "+modules0)
      }
      val creatorUserId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      ScalatraResponseFactory.getGeneric(dashboardAccountServiceV2.saveDefaultModules(accountId, modules, Creator(creatorUserId, creatorAccountId)))
    }

    post("/accounts/default") {
      val accountIds  = params.get("accountIds").getOrElse(halt(400, "accountIds is Not Provided")).split(",").filter(_.nonEmpty).map(_.trim.toLong).toSeq
      val modules0 = params("modules")
      val isForceInherit = params.get("isForceInherit").map(_.toBoolean)
      val creatorUserId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      val modules = try {
        if (modules0.trim.nonEmpty) {
          URLDecoder.decode(modules0, StandardCharsets.UTF_8.name()).trim.split(",").map(_.trim.toInt).toSet
        } else {
          Set.empty[Int]
        }
      } catch {
        case ex: Exception =>
          logger.info(s"Invalid default modules $modules0 for account: $creatorAccountId", ex)
          halt(500, "Invalid modules: " + modules0)
      }
      ScalatraResponseFactory.getGeneric(dashboardAccountServiceV2.saveDefaultModulesForAccounts(accountIds, modules, isForceInherit.getOrElse(false), Creator(creatorUserId, creatorAccountId)))
    }

    delete("/default/:account_id") {
      val accountId : Long = params("account_id").toLong
      val creatorUserId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
      val creatorAccountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
      ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.clearDefaultModules(accountId, Creator(creatorUserId, creatorAccountId)))
    }
}
