package me.socure.dashboardv2

import me.socure.account.service.SubscriptionService

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.OperationNotSupported

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.account.{SubscriptionStatuses, SubscriptionUpdateInput}
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class SubscriptionServlet(subscriptionService: SubscriptionService,
                          val hmacVerifier: HMACHttpVerifier)
                   (implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override val logger: Logger = LoggerFactory.getLogger(getClass)
  private val metrics: Metrics = JavaMetricsFactory.get(getClass.getSimpleName)
  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  before() {
    validateRequest()
  }

  get("/event/subscription_types") {
    metrics.increment("event.subscription_types.count")
    val future = {
      subscriptionService.listSubscriptionTypes.map {
        case subscriptionTypes@Right(_) =>
          metrics.increment("event.subscription_types.success")
          subscriptionTypes
        case error@Left(_) =>
          metrics.increment("event.subscription_types.failure")
          error
      }
    }
    ScalatraResponseFactory.get(future)
  }

  put("/events/subscriptions") {
    metrics.increment("event.update_subscriptions")
    val subscriptionUpdateInput = parsedBody.extract[SubscriptionUpdateInput]
    val environmentId: Int = subscriptionUpdateInput.environmentId
    val subscriptionTypes: Set[Long] = subscriptionUpdateInput.subscriptionTypes
    val actionString = subscriptionUpdateInput.action
    val subscriptionStatusInt: Int = SubscriptionStatuses.byLabel(actionString) match {
      case Some(subscriptionStatus) => subscriptionStatus.id
      case None => -1
    }
    ScalatraResponseFactory.get(subscriptionService.updateSubscriptionStatus(environmentId, subscriptionTypes, subscriptionStatusInt))
  }

  get("/account/:id/subscriptions") {
    val accountId = params("id").toLong
    val future = {
      subscriptionService.listSubscriptions(accountId).map {
        case subscriptions@Right(_) =>
          metrics.increment("event.subscriptions.success")
          subscriptions
        case error@Left(_) =>
          metrics.increment("event.subscriptions.failure")
          error
      }
    }
    ScalatraResponseFactory.get(future)
  }

  post("/account/:id/subscriptions/:subscription_type_id/:operation") {
    val accountId = params("id").toLong
    val subscriptionTypeId = params("subscription_type_id").toLong
    val operation = params("operation")
    val future = {
      operation.toLowerCase match {
        case "subscribe" =>
          subscriptionService.subscribe(accountId, subscriptionTypeId)
        case "unsubscribe" =>
          subscriptionService.unsubscribe(accountId, subscriptionTypeId)
        case _ =>
          logger.error(s"Unsupported operation '${operation}' for the account ${accountId}")
          Future.successful(Left(ErrorResponseFactory.get(OperationNotSupported)))
      }
    }
    ScalatraResponseFactory.get(future)
  }

  get("/event/subscription_types_with_provision") {
    val accountId = params("account_id").toLong
    val result = subscriptionService.listSubscriptionTypesWithProvision(accountId)
    ScalatraResponseFactory.get(result)
  }

}
