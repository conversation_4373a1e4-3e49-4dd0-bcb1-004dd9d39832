package me.socure.dashboardv2

import me.socure.account.dashboardv2.DashboardUserServiceV2
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.account.service.{BusinessUserRoleService, PasswordService}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.model.dashboardv2.{AccountWithCreator, BusinessUserForm, CreateBusinessUserInput, Creator, GetUserDetailsRequest, UpdateBusinessUserInput, UsersByPermissionFilterRequest}
import me.socure.model.user.UpdateQuicksightUserStatus
import me.socure.util.JsonEnrichments._
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 04/08/16.
  */
class DashboardUserV2Servlet(dashboardUserService: DashboardUserServiceV2, passwordService: PasswordService, businessUserRoleService: BusinessUserRoleService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  get("/list_users/:accountid"){
    val accountId : Long = params("accountid").toLong
    val showSubAccountUser = params.get("showSubAccount").map(_.toBoolean)
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(dashboardUserService.listAllUsers(accountId, creator, showSubAccountUser.getOrElse(false)))
  }

  get("/list_userids/:accountid"){
    val accountId : Long = params("accountid").toLong
    val showSubAccountUser = params.get("showSubAccount").map(_.toBoolean)
    ScalatraResponseFactory.get(dashboardUserService.listAllUserIds(accountId, showSubAccountUser.getOrElse(false)))
  }

  post("/create_user"){
    val userForm = parsedBody.extract[BusinessUserForm]
    ScalatraResponseFactory.get(dashboardUserService.createUser(userForm))
  }

  post("/update_user") {
    val userId = params("userid").toLong
    val userForm = params("user").decodeJson[BusinessUserForm]
    ScalatraResponseFactory.get(dashboardUserService.updateUser(userId, userForm))
  }

  post("/create_business_user"){
    val userForm = parsedBody.extract[CreateBusinessUserInput]
    ScalatraResponseFactory.getGeneric(dashboardUserService.createBusinessUserWithAssociationsWithAuditDetails(userForm))
  }

  post("/update_business_user") {
    val updateBusinessUserInput = parsedBody.extract[UpdateBusinessUserInput]
    ScalatraResponseFactory.getGeneric(dashboardUserService.updateBusinessUserWithAssociationsWithAuditDetails(updateBusinessUserInput))
  }

  post("/lock_user") {
    val userId : Long = params("user_id").toLong
    val accountWithCreator = parsedBody.extractOpt[AccountWithCreator]
    ScalatraResponseFactory.get(dashboardUserService.toggleBusinessUserLock(userId, isLocked = true, accountWithCreator))
  }

  post("/unlock_user") {
    val userId : Long = params("user_id").toLong
    val accountWithCreator = parsedBody.extractOpt[AccountWithCreator]
    ScalatraResponseFactory.get(dashboardUserService.toggleBusinessUserLock(userId, isLocked = false, accountWithCreator))
  }

  post("/delete_user") {
    val userId : Long = params("user_id").toLong
    val accountWithCreator = parsedBody.extractOpt[AccountWithCreator]
    ScalatraResponseFactory.getGeneric(dashboardUserService.deleteBusinessUser(userId, accountWithCreator))
  }

  get("/get_user_information/:user_id") {
    val userId = params("user_id").toLong
    val accountWithCreatorOpt = params.get("accountWithCreator")
    accountWithCreatorOpt match {
      case Some(accountWithCreator) => ScalatraResponseFactory.get(dashboardUserService.getUserWithRoles(userId, accountWithCreator.decodeJsonOpt[AccountWithCreator]))
      case None => ScalatraResponseFactory.get(dashboardUserService.getUserWithRoles(userId, None))
    }
  }

  get("/get_user_information_v2/:user_id") {
    val userId = params("user_id").toLong
    val creatorUserId = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id is not provided")).toLong
    val creatorAccountId = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id is not provided")).toLong
    ScalatraResponseFactory.get(dashboardUserService.getUserWithRolesAndAssociations(userId, Creator(creatorUserId, creatorAccountId)))
  }

  post("/force_reset_password") {
    val userId = params("user_id").toLong
    val accountWithCreator = parsedBody.extractOpt[AccountWithCreator]
    ScalatraResponseFactory.get(passwordService.forceResetPassword(userId, accountWithCreator))
  }

  get("/get_user_id/:username") {
    val uname = params("username")
    ScalatraResponseFactory.get(dashboardUserService.getUserIdByUsername(uname))
  }

  get("/get_public_account_id/:userName") {
    val uname = params("userName")
    ScalatraResponseFactory.get(dashboardUserService.getPublicAccountIdByUserName(uname))
  }

  post("/list_users/by_permission") {
    val userByPermFilterReq = parsedBody.extract[UsersByPermissionFilterRequest]
    ScalatraResponseFactory.get(dashboardUserService.getUsersFilteredByPermissions(userByPermFilterReq))
  }

  post("/list_users/case_mgmt/by_permission") {
    val userByPermFilterReq = parsedBody.extract[UsersByPermissionFilterRequest]
    ScalatraResponseFactory.get(dashboardUserService.getUsersFilteredByPermissionsForCasemgmt(userByPermFilterReq))
  }

  post("/get_user_details"){
    val userDetailsReq = parsedBody.extract[GetUserDetailsRequest]
    ScalatraResponseFactory.get(dashboardUserService.getUserBasicDetails(userDetailsReq))
  }

  get("/is_quicksight_user"){
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    ScalatraResponseFactory.get(dashboardUserService.isQuicksightUser(userId.toLong))
  }

  put("/update_quicksight_user_status"){
    val updateQuicksightUserStatus = parsedBody.extract[UpdateQuicksightUserStatus]
    ScalatraResponseFactory.get(dashboardUserService.updateQuicksightUserStatus(updateQuicksightUserStatus))
  }

  get("/sponsor_bank_users/:sponsor_bank_id"){
    val sponsorBankAccountId = params.get("sponsor_bank_id").getOrElse(halt(400, "sponsor_bank_id not provided")).toLong
    ScalatraResponseFactory.get(dashboardUserService.getSponsorBankUsers(sponsorBankAccountId))
  }

  put("/update_user_tos/:user_id"){
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided")).toLong
    ScalatraResponseFactory.get(dashboardUserService.updateUserTOS(userId))
  }
}
