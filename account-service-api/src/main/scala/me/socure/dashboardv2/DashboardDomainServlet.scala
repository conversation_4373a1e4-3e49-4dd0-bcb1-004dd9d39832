package me.socure.dashboardv2

import me.socure.account.service.DashboardDomainService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 11/04/2017.
  */
class DashboardDomainServlet(dashboardDomainService : DashboardDomainService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/get/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainForAccount(accountId))
  }

  // We will be removing this in upcoming release
  get("/get_by_email/:email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainForAccountByEmail(email))
  }

  // We kept it POST method to avoid email information being printed in logger.
  post("/get_by_email") {
    val email = params("email")
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainForAccountByEmail(email))
  }

  // We will be removing this in upcoming release
  get("/get_permission_domain_by_email/:email") {
    val email: String = params("email")
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainPermissionForAccountByEmail(email))
  }

  // We kept it POST method to avoid email information being printed in logger.
  post("/get_permission_domain_by_email") {
    val email: String = params("email")
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainPermissionForAccountByEmail(email))
  }

  get("/get_permission_domain_by_account_id/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(dashboardDomainService.getWhitelistedDomainPermissionForAccountById(accountId))
  }

  // We kept it POST method to avoid email information being printed in logger.
  post("/get_associated_account_dashboard_domain_list_by_email") {
    val email: String = params("email")
    ScalatraResponseFactory.get(dashboardDomainService.getAssociatedAccountDashboardDomainListByEmailId(email))
  }

  post("/update") {
    val accountId = params("account_id").toLong
    val domain = params("domains").split(",").toList
    ScalatraResponseFactory.getGeneric(dashboardDomainService.upsertDashboardDomain(accountId, domain))
  }

}
