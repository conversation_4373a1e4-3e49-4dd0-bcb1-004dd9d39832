package me.socure.dashboardv2

import me.socure.account.dashboardv2.IDMService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.IdmApiKey
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

class IDMServlet(idmService : IDMService, val hmacVerifier: HMACHttpVerifier) (implicit val executor : ExecutionContext)
  extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  private val AccountInformationUrlPrefix = "/account_information"

  before() {
    if(request.getPathInfo.isEmpty || !request.getPathInfo.contains(AccountInformationUrlPrefix)) validateRequest()
  }

  get("/fetch_api_key/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(idmService.fetchApiKey(accountId))
  }

  post("/generate_api_key") {
    val idmApiKey = parsedBody.extract[IdmApiKey]
    ScalatraResponseFactory.get(idmService.generateApiKey(idmApiKey))
  }

  post("/deprecate_api_key") {
    val idmApiKey = parsedBody.extract[IdmApiKey]
    ScalatraResponseFactory.get(idmService.deprecateApiKey(idmApiKey))
  }

  post("/update_api_key") {
    val idmKeyUpdateRequest = parsedBody.extract[IdmApiKey]
    ScalatraResponseFactory.get(idmService.updateLabel(idmKeyUpdateRequest))
  }

  get("/account_information/v1/:apiKey") {
    val apiKey: String = params("apiKey")
    ScalatraResponseFactory.get(idmService.fetchIdmAccountInformation(apiKey))
  }
}
