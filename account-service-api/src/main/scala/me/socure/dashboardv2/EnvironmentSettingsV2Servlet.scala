package me.socure.dashboardv2

import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.ExecutionContext

/**
  * Created by <PERSON><PERSON> on 7/16/19.
  */
class EnvironmentSettingsV2Servlet(
                                  envSettingsService : EnvironmentSettingsService,
                                  val hmacVerifier: HMACHttpVerifier
                                )(implicit val executor : ExecutionContext)
  extends BaseScalatraServlet with FutureSupport with AuthenticationSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger: Logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  post("/generate/apikeys/public") {
    val envId = params("environmentId").toLong
    ScalatraResponseFactory.get(envSettingsService.generatePublicApiKey(envId))
  }

  post("/deprecate/apikeys/public") {
    val envId = params("environmentId").toLong
    ScalatraResponseFactory.get(envSettingsService.changePublicApiKeyStatus(envId))
  }

  post("/generate/missing/apikeys/public") {
    ScalatraResponseFactory.get(envSettingsService.generateMissingPublicApiKeys())
  }
}
