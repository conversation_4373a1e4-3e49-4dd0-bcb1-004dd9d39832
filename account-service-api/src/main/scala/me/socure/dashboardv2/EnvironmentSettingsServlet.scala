package me.socure.dashboardv2

import me.socure.account.dashboardv2.EnvironmentSettingsService
import me.socure.account.service.BusinessUserRoleService
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.model.account.SocialNetworkAppKeys
import me.socure.model.dashboardv2.{ApiKeyUpdateRequest, Creator, EnvironmentDomain}
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext


/**
  * Created by sun<PERSON><PERSON> on 8/25/16.
  */
class EnvironmentSettingsServlet(envSettingsService : EnvironmentSettingsService, businessUserRoleService: BusinessUserRoleService)(implicit val executor : ExecutionContext)
  extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/with_api_keys/:account_id") {
    val accountId = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    val permissionChkForTransaction = params.get("permissionChkForTransaction").map(_.toBoolean)
    ScalatraResponseFactory.get(envSettingsService.getEnvironmentSettingsWithApiKeys(accountId, creator, permissionChkForTransaction.getOrElse(false)))
  }

  get("/with_api_keys_dev/:account_id") {
    val accountId = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(envSettingsService.getEnvironmentSettingsWithApiKeysDev(accountId, creator))
  }

  post("/update_domain") {
    val updateDomain = parsedBody.extract[EnvironmentDomain]

    ScalatraResponseFactory.getGeneric(envSettingsService.updateDomain(updateDomain))
  }

  post("/remove_appkey") {
    val socialAppKeyId = params("id").toLong
    val creator = parsedBody.extractOpt[Creator]
    ScalatraResponseFactory.get(envSettingsService.deletedSocialNetworkKeys(socialAppKeyId, creator))
  }

  post("/upsert_appkey") {
    val accountkeys = parsedBody.extract[SocialNetworkAppKeys]
    ScalatraResponseFactory.get(envSettingsService.upsertSocialNetworkKeys(accountkeys))
  }

  get("/get_environments/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(envSettingsService.getAccountEnvironmentList(accountId))
  }
  post("/get_environments_by_account_ids") {
    val body = parsedBody.extract[Map[String, Seq[Long]]]
    val accountIds = body.get("accountIds").getOrElse(Seq.empty[Long])
    val envTypeIds = body.get("envTypeIds").getOrElse(Seq.empty[Long])
    ScalatraResponseFactory.get(envSettingsService.getEnvironmentListByAccountIds(accountIds, envTypeIds))
  }

  post("/generate_api_key") {
    val envId = params("env_id").toLong
    val creator = parsedBody.extractOpt[Creator]
    ScalatraResponseFactory.get(envSettingsService.generateApiKey(envId, creator))
  }

  post("/generate_api_key_dev") {
    val envId = params("env_id").toLong
    val creator = parsedBody.extractOpt[Creator]
    ScalatraResponseFactory.get(envSettingsService.generateApiKeyDev(envId, creator))
  }

  post("/deprecate_api_key") {
    val envId = params("env_id").toLong
    val creator = parsedBody.extractOpt[Creator]
     ScalatraResponseFactory.get(envSettingsService.changeApiKeyStatus(envId, creator))
  }

  post("/deprecate_api_key_dev") {
    val envId = params("env_id").toLong
    val creator = parsedBody.extractOpt[Creator]
    ScalatraResponseFactory.get(envSettingsService.changeApiKeyStatusDev(envId, creator))
  }

  get("/get_appkey_ids/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(envSettingsService.getSocialAppKeyIds(accountId))
  }

  get("/get_environment_with_domains/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.get(envSettingsService.getEnvironmentWithDomains(accountId))
  }

  get("/account/:account_id") {
    val accountId = params("account_id").toLong
    val role = params("role").toInt
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(envSettingsService.getEnvironments(accountId, role, creator))
  }

  get("/apikeys/:environmentId") {
    val envId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(envSettingsService.getApiKeys(envId, creator))
  }

  get("/apikeys_dev/:environmentId") {
    val envId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.get(envSettingsService.getApiKeysDev(envId, creator))
  }

  get("/publicapikeys/:environmentId") {
    val envId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.getPublicApiKeys(envId, creator))
  }

  get("/publicapikeys_dev/:environmentId") {
    val envId = params("environmentId").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.getPublicApiKeysDev(envId, creator))
  }

  put("/apikeys") {
    val apiKeyUpdateRequest = parsedBody.extract[ApiKeyUpdateRequest]
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.updateApiKey(apiKeyUpdateRequest, creator))
  }

  put("/apikeys_dev") {
    val apiKeyUpdateRequest = parsedBody.extract[ApiKeyUpdateRequest]
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.updateApiKeyDev(apiKeyUpdateRequest, creator))
  }

  put("/publicapikeys") {
    val apiKeyUpdateRequest = parsedBody.extract[ApiKeyUpdateRequest]
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.updatePublicApiKey(apiKeyUpdateRequest, creator))
  }

  put("/publicapikeys_dev") {
    val apiKeyUpdateRequest = parsedBody.extract[ApiKeyUpdateRequest]
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(envSettingsService.updatePublicApiKeyDev(apiKeyUpdateRequest, creator))
  }
}
