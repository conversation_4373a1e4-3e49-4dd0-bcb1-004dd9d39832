package me.socure.dashboardv2

import dispatch.Future
import me.socure.account.automation.AccountAutomationService
import me.socure.account.dashboardv2.DashboardAccountServiceV2
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.{AccountTypes, JsonFormats}
import me.socure.model.account.SubAccount
import me.socure.model.dashboardv2.Creator
import me.socure.model.user.{SubAccountFormV2, UserForm, UserFormWithNoPassword}
import me.socure.util.JsonEnrichments._
import org.json4s.Formats
import org.scalatra.FutureSupport

import scala.concurrent.ExecutionContext
import scala.util.Try


/**
  * Created by gopal on 04/08/16.
  */
class AccountV2Servlet(dashboardAccountServiceV2: DashboardAccountServiceV2, accountAutomationService: AccountAutomationService)(implicit val executor : ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  get("/list_accounts/:parentid"){
    val parentId = params("parentid").toInt
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.listAllAccounts(parentId))
  }

  get("/list_sub_accounts_by_public_id/:publicParentId") {
    val parentId = params("publicParentId")
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.listSubAccountsByPublicId(parentId))
  }

  get("/get_primary_user_by_accountid/:accountid") {
    val accountId: Long = params("accountid").toLong
    ScalatraResponseFactory.get(dashboardAccountServiceV2.getPrimaryUserByAccountId(accountId))
  }

  get("/list_sub_accounts/:parentid"){
    val parentId = params("parentid").toInt
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.listSubAccounts(parentId))
  }

  get("/subaccounts/:id") {
    val parentId = params("id").toLong
    val userId: Long = params.get("user_id").getOrElse(halt(400, "user_id not provided")).toLong
    val accountId: Long = params.get("account_id").getOrElse(halt(400, "account_id not provided")).toLong
    val page = params.get("page").flatMap(s => Try(s.toInt).toOption)
    val size = params.get("size").flatMap(s => Try(s.toInt).toOption)
    val skipPermissionChk = params.get("skip_permission_chk").map(_.toBoolean)
    ScalatraResponseFactory.get(dashboardAccountServiceV2.listSubAccountsV2(parentId, Creator(userId= userId, accountId = accountId), page, size, skipPermissionChk.getOrElse(false)))
  }

  get("/list_sub_accounts_with_env_details/:parentId"){
    val parentId = params("parentId").toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.listSubAccountsWithEnvDetails(parentId))
  }

  post("/create_sub_account"){
    val parentId: Long = params("parentid").toLong
    val accountInfo : UserForm = parsedBody.extract[UserForm]
    val isActive = params.get("isActive").map(_.toBoolean)
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.createSubAcount(parentId, accountInfo, isActive.getOrElse(false)))
  }

  post("/create_sub_account_v2"){
    val parentId: Long = params("parentid").toLong
    val accountInfo : SubAccountFormV2 = parsedBody.extract[SubAccountFormV2]
    val isActive = params.get("isActive").map(_.toBoolean)
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.createSubAccountV2(parentId, accountInfo, isActive.getOrElse(false)))
  }

  post("/create_sub_account_with_no_password"){
    val parentId: Long = params("parentid").toLong
    val accountInfo: UserFormWithNoPassword = params("user").decodeJson[UserFormWithNoPassword]
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.createSubAcountWithNoPassword(parentId, accountInfo))
  }

  post("/create_sub_account_with_min_details") {
    val parentId: Long = params("parentid").toLong
    val accountInfo: SubAccount = params("user").decodeJson[SubAccount]
    val userId = params.get("user_id").map(_.toLong)
    val accountId: Option[Long] = params.get("account_id").map(_.toLong)
    val primaryUserId: Option[Long] = params.get("primary_user_id").map(_.toLong)
    val isDirectEffectiv: Boolean = params.get("isDirectEffectiv").getOrElse("false").toBoolean

    // Using publicGet to handle future response
    ScalatraResponseFactory.publicGet(
      dashboardAccountServiceV2.createSubAcountWithMinDetails(parentId, accountInfo, userId, accountId, primaryUserId).flatMap {
        case Right((parentAccount, subAccountId)) =>
          // If sub-account creation succeeds, proceed to baseline logic provisioning
          if(!isDirectEffectiv) {
            accountAutomationService.baselineLogicProvisioningForSubaccount(subAccountId, parentId).map {
              case Right(_) =>
                // On success of baseline logic provisioning, return the expected JSON format
                Right(true)
              case Left(error) =>
                // Return error if baseline logic provisioning fails
                Left(ErrorResponseFactory.get(ExceptionCodes.BaseLineLogicMappingFailed))
            }
          } else {
            Future.successful(Right(true))
          }
        case Left(errorResponse) =>
          // Return error if sub-account creation fails
          Future.successful(Left(errorResponse))
      }
    )
  }

  post("/map_baseline_logic_for_subaccount") {
    val parentId: Long = params("parentid").toLong
    val subAccountId: Long = params("subAccountId").toLong
    accountAutomationService.baselineLogicProvisioningForSubaccount(subAccountId, parentId).map {
      case Right(_) =>
        // Return success if both sub-account creation and baseline logic provisioning succeed
        Right(true)
      case Left(error) =>
        // Return error if baseline logic provisioning fails
        Left(ErrorResponseFactory.get(ExceptionCodes.BaseLineLogicMappingFailed))
      case Left(errorResponse) =>
        // Return error if sub-account creation failed
        Future.successful(Left(errorResponse))
    }
  }


  post("/update_sub_account") {
    val userForm : UserForm = parsedBody.extract[UserForm]
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.updateSubAccount(userForm.email, userForm))
  }

  post("/activate") {
    val accountId : Long = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.toggleAccountActivateStatus(accountId, isActive = true, creator))
  }

  post("/deactivate") {
    val accountId : Long = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.toggleAccountActivateStatus(accountId, isActive = false, creator))
  }

  get("/get_account_list/:account_id"){
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getAllAccounts(accountId))
  }

  get("/get_account_lists_by_name"){
    val accountNames = params.get("account_names").getOrElse(halt(400, "account_names not provided"))
    val parentAccountId = params.get("parent_account_id").map(_.toLong).getOrElse(halt(400, "parent_account_id not provided"))
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getAccountsByName(accountNames, parentAccountId))
  }

  get("/get_associated_accounts_for_user"){
    val userId = params.get("user_id").getOrElse(halt(400, "user_id not provided"))
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getAssociatedAccounts(userId.toLong))
  }

  post("/deprecate_api_keys"){
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.deprecateApiKeys())
  }

  get("/get_apikeys_for_subaccounts/:account_id") {
    val accountId : Long = params("account_id").toLong
    val creatorUserId = params.get("creator_user_id").map(_.toLong)
    val creatorAccountId = params.get("creator_account_id").map(_.toLong)
    val creator = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Some(Creator(userId, accountId))
      case (_, _) => None
    }
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getApiKeysForSubAccounts(accountId, creator))
  }

  get("/get_apikeys_for_account_and_subaccounts/:account_id") {
    val accountId = params("account_id").toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getApiKeysForAccountAndSubAccounts(accountId))
  }

  get("/get_account_info_for_environment/:environment_id") {
    val environmentId = params("environment_id").toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getAccountInfoForEnvironment(environmentId))
  }

  get("/validate/apikey/access") {
    val apiKey = params.get("apikey").getOrElse(halt(400, "apikey not provided"))
    val userId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
    val accountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
    val permissions = params.get("permissions").getOrElse(halt(400, "permissions not provided"))
    val status = params.get("status")
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.validateAccessPermission(apiKey, Creator(userId, accountId), permissions, status))
  }

  get("/validate/environmenttype/access") {
    val environmentTypeId = params.get("environment_type_id").getOrElse(halt(400, "environment_type_id not provided")).toInt
    val userId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
    val accountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
    val permissions = params.get("permissions").getOrElse(halt(400, "permissions not provided"))
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.validateAccessPermission(environmentTypeId, Creator(userId, accountId), permissions))
  }

  get("/validate/environmenttype/access/permissions") {
    val environmentTypeId = params.get("environment_type_id").getOrElse(halt(400, "environment_type_id not provided")).toInt
    val userId: Long = params.get("creator_user_id").getOrElse(halt(400, "creator_user_id not provided")).toLong
    val accountId: Long = params.get("creator_account_id").getOrElse(halt(400, "creator_account_id not provided")).toLong
    val permissions = params.get("permissions").getOrElse(halt(400, "permissions not provided"))
    val orPermissions = params.get("or_permissions")
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.validateAccessWithOrPermission(environmentTypeId, Creator(userId, accountId), permissions, orPermissions))
  }

  get("/get_apikeys_for_account_by_envType/:account_id/:environment_type_id") {
    val accountId = params("account_id").toLong
    val environmentTypeId = params.get("environment_type_id").getOrElse(halt(400, "environment_type_id not provided")).toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getApiKeysForAccountByEnvironmentType(accountId, environmentTypeId))
  }

  get("/get_sponsor_bank_id/:programId") {
    val programId = params.get("programId").getOrElse(halt(400, "programId not provided")).toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getSponsorBankIdsByProgramId(programId))
  }

  get("/get_sponsor_bank_programs/:sponsorBankId") {
    val sponsorBankId = params.get("sponsorBankId").getOrElse(halt(400, "sponsorBankId not provided")).toLong
    ScalatraResponseFactory.publicGet(dashboardAccountServiceV2.getSponsorBankPrograms(sponsorBankId))
  }

}
