package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object AccountTypes extends Enum with By<PERSON><PERSON>ber {
  type AccountType = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val DIRECT_CUSTOMER = new AccountType(1, "DirectCustomer")
  val RESELLER = new AccountType(2, "Reseller")
  val AGGREGATOR = new AccountType(3, "Aggregator")
  val SUB_ACCOUNT = new AccountType(4, "SubAccount")
  val PROSPECT = new AccountType(5, "PROSPECT")
  val DIRECT_EFFECTIV = new AccountType(6, "Direct_RiskOS")

  val byId: Int => Option[AccountType] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byAccountTypeName(name: String): Option[AccountType] = values.find(_.name.contains(name.trim.toLowerCase))

  def byAccountTypeId(id: Int): Option[AccountType] = values.find(_.id == id)

  def byAccountType(value: Value): Option[AccountType] = values.find(_ == value)

  def getIdByValue(value: Value): Int = AccountTypes.byAccountType(value).map(_.id).get

  def getAll: Set[AccountType] = {
    values.toSet
  }

  def isTypeDirect(t: Int): Boolean = t.equals(AccountTypes.DIRECT_CUSTOMER.id) || t.equals(AccountTypes.DIRECT_EFFECTIV.id)
  def isTypeChannelPartner(t: Int): Boolean = t.equals(AccountTypes.RESELLER.id) || t.equals(AccountTypes.AGGREGATOR.id)
}
