package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object Resources extends Enum with ByMember {
  type Resource = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val DEPRECATED = new Resource(0, "Deprecated")
  val DASHBOARD_PERMISSIONS = new Resource(1, "DashboardPermissions")
  val IDPLUS = new Resource(2, "Idplus (Modules)")
  val FEATURE_FLAGS = new Resource(3, "FeatureFlags")


  val byId: Int => Option[Resource] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byResourceName(name: String): Option[Resource] = values.find(_.name.contains(name.trim.toLowerCase))

  def byResourceId(id: Int): Option[Resource] = values.find(_.id == id)

  def byResource(value: Value): Option[Resource] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Resources.byResource(value).map(_.id).get

  def getAll: Set[Resource] = {
    values.toSet
  }
}