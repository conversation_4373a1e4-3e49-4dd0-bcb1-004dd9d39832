package me.socure.constants

import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.JsonAST.JString
import org.json4s._

/**
  * Created by alexand<PERSON> on 6/1/16.
  */
object TimestampJodaDateTimeSerializer extends CustomSerializer[DateTime](format => (
  {
    case JString(s) => new DateTime(s.toLong, DateTimeZone.UTC)
    case JNull => null
  },
  {
    case d: DateTime => JString(d.getMillis.toString)
  }
  ))
