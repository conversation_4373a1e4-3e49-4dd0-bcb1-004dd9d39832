package me.socure.constants

import me.socure.constants

import scala.collection.mutable
import scala.concurrent.Future

object SystemDefiendRolesPermissions {

  type RolePermissions = Map[Int, Set[Int]]

  private val rolePermissionsMap: Map[Int, RolePermissions] = Map(
    SystemDefinedRoles.ACCOUNTOWNER.roleType -> SystemDefinedDashboardRoles.accountOwner,
    SystemDefinedRoles.ADMINISTRATOR.roleType -> SystemDefinedDashboardRoles.administrator,
    SystemDefinedRoles.ANALYST.roleType -> SystemDefinedDashboardRoles.analyst,
    SystemDefinedRoles.DEVELOPER.roleType -> SystemDefinedDashboardRoles.developer,
    SystemDefinedRoles.CASE_ANALYST.roleType -> SystemDefinedDashboardRoles.caseAnalyst,
    SystemDefinedRoles.CASE_SUPERVISOR.roleType -> SystemDefinedDashboardRoles.caseSupervisor,
    SystemDefinedRoles.CASE_OFFICER.roleType -> SystemDefinedDashboardRoles.caseOfficer,
    SystemDefinedRoles.DOCV_ANALYST.roleType -> SystemDefinedDashboardRoles.docVCaseAnalyst,
    SystemDefinedRoles.BSA_OFFICER.roleType -> SystemDefinedDashboardRoles.bsaOfficer,
    SystemDefinedRoles.COMPLIANCE_ANALYST_ROLE.roleType -> SystemDefinedDashboardRoles.complianceAnalystRole,
    SystemDefinedRoles.INTEGRATION_MANAGER.roleType -> SystemDefinedDashboardRoles.integrationManager,
    SystemDefinedRoles.PROSPECT.roleType -> SystemDefinedDashboardRoles.prospect
  )

  private val platformRolePermissionsMap: Map[Int, RolePermissions] = Map(
    SystemDefinedRoles.ACCOUNTOWNER.roleType -> SystemDefinedPlatformRoles.accountOwner,
    SystemDefinedRoles.ADMINISTRATOR.roleType -> SystemDefinedPlatformRoles.administrator,
    SystemDefinedRoles.ANALYST.roleType -> SystemDefinedPlatformRoles.analyst,
    SystemDefinedRoles.DEVELOPER.roleType -> SystemDefinedPlatformRoles.developer,
    SystemDefinedRoles.CASE_ANALYST.roleType -> SystemDefinedPlatformRoles.caseAnalyst,
    SystemDefinedRoles.CASE_SUPERVISOR.roleType -> SystemDefinedPlatformRoles.caseSupervisor,
    SystemDefinedRoles.CASE_OFFICER.roleType -> SystemDefinedPlatformRoles.caseOfficer,
    SystemDefinedRoles.RISKOS_SUPPORT_VIEWER.roleType -> SystemDefinedPlatformRoles.riskOsSupportViewer,
    SystemDefinedRoles.RISKOS_SUPPORT_ADMIN.roleType -> SystemDefinedPlatformRoles.riskOsSupportAdmin
  )

  private def getRolePermissions(parentAccountType: Option[Int], roleType: Int): RolePermissions = {
    getAccountTypeBasedRolePermissionMap(parentAccountType).getOrElse(roleType,
      throw new IllegalArgumentException(s"Invalid Sys defined role type - $roleType"))
  }

  def sysDefinedRoles(rootAccountType: Option[Int], roleType: Int): RolePermissions = {
    getRolePermissions(rootAccountType, roleType)
  }

  def sysDefinedRolesAsSeq(rootAccountType: Option[Int], roleType: Int): Seq[(Int, Set[Int])] = {
    getRolePermissions(rootAccountType, roleType).toSeq
  }

  def sysDefinedRolesPermissions(rootAccountType: Option[Int], roleType: Int, envType: Int): String = {
    getRolePermissions(rootAccountType, roleType)
      .getOrElse(envType, Set.empty[Int])
      .mkString(",")
  }

  def sysDefinedRolesFuture(rootAccountType: Option[Int], roleType: Int): Future[RolePermissions] = {
    Future.successful(getRolePermissions(rootAccountType, roleType))
  }

  def mergePermissions(customPermissions:Map[Int, Set[Int]], systemPermissions:Map[Int, Set[Int]]): Map[Int, Set[Int]] ={
    val mergedMap: mutable.Map[Int, Set[Int]] = mutable.Map()
    mergedMap ++= customPermissions
    systemPermissions.foreach{keys =>
      if(mergedMap.contains(keys._1)){
        val mergedPermissionsList = keys._2++mergedMap.getOrElse(keys._1, Set.empty)
        mergedMap.update(keys._1, mergedPermissionsList)
      } else {
        mergedMap.put(keys._1, keys._2)
      }
    }
    mergedMap.toMap
  }

  def getAccountTypeBasedRolePermissionMap(rootAccountType: Option[Int]): Map[Int, RolePermissions] = {
    if(rootAccountType.isDefined && rootAccountType.get == AccountTypes.DIRECT_EFFECTIV.id){
      platformRolePermissionsMap
    } else {
      rolePermissionsMap
    }
  }

}
