package me.socure.constants

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object Routine extends Enum with <PERSON><PERSON><PERSON>ber {
  type Routine = EnumVal

  sealed case class EnumVal(id: Int, name: String, max: Int) extends Value

  val DAYS = new Routine(0, "Days", 6)
  val WEEK = new Routine(1, "Weeks", 53)
  val MONTH = new Routine(2, "Months", 11)
  val YEAR = new Routine(3, "Years", 7)


  val byId: Int => Option[Routine] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byRoutineName(name: String): Option[Routine] = values.find(_.name.contains(name.trim))

  def byRoutineId(id: Int): Option[Routine] = values.find(_.id == id)

  def byRoutine(value: Value): Option[Routine] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Routine.byRoutine(value).map(_.id).get

  def getAll: Set[Routine] = {
    values.toSet
  }
}