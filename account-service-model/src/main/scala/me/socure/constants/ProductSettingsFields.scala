package me.socure.constants

import me.socure.model.dv.DVConfiguration._

import scala.collection.immutable.ListMap


object ProductSettingsFields {

  val GENERAL = "General"
  val KYC = "KYC"
  val WATCHLIST = "Watchlist"
  val DOCUMENT_VERIFICATION = "Document Verification"
  val WATCHLIST_CUSTOM_SETTINGS = "Watchlist CustomSettings"

  private val dateRangeMap = Map(
    "exact_yyyy_mm_dd" -> "Exact YYYY/MM/DD",
    "one_year_radius_yyyy_mm_dd" -> "One Year Radius YYYY/MM/DD",
    "exact_yyyy" -> "Exact YYYY",
    "one_year_radius_yyyy" -> "One Year Radius YYYY",
    "exact_yyyy_mm" -> "Exact YYYY/MM",
    "one_year_radius_yyyy_mm" -> "One Year Radius YYYY/MM",
    "one_year_radius_2_digit_transposition_yyyy_mm_dd" -> "One Year Radius & 2 Digit Transposition YYYY/MM/DD",
    "one_year_radius_2_digit_transposition_yyyy_mm" -> "One Year Radius & 2 Digit Transposition YYYY/MM")

  //WL Case Management
  val ONE_STEP = "1 Step"
  val TWO_STEP = "2 Step"

  //KYC
  val KYC_DOB = "KYC Date of Birth Tolerance"
  val KYC_SSN = "KYC National ID Tolerance"
  val KYC_ADDRESS = "KYC Address Match Logic"

  //GENERAL
  val GENERAL_AUTO_SIGNOUT = "Auto Sign-out (minutes)"
  val GENERAL_IDLE_SIGNOUT = "Idle Session Sign-out (minutes)"

  val API_ALLOWED_DOMAINS = "API Allowable IPs & Domain"
  val DASHBOARD_ALLOWED_DOMAINS = "Dashboard Allowable IPs & Domains"

  val WATCHLIST_DOB_NAME = "Global Watchlist - Only Display Results Which Contain DOB Within the Source Record"
  val WATCHLIST_NAME_FUZZY = "Global Watchlist Name Fuzziness"
  val WATCHLIST_ENTITIES_LIMIT = "Global Watchlist Entities Limit"
  val WATCHLIST_AUTO_MONITOR = "Global Watchlist Auto Monitor Watchlist Transactions"
  val WATCHLIST_SUPPRESS_PEPS = "Global Watchlist Suppress PEPs Without URLs"
  val WATCHLIST_DOB_TOLERANCE = "Global Watchlist Date of Birth Tolerance"
  val WATCHLIST_SOURCE_COUNTRIES = "Global Watchlist Source List Countries"
  val WATCHLIST_CATEGORIES = "Global Watchlist Categories"
  val WATCHLIST_SOURCES = "Global Watchlist Sources"
  val WATCHLIST_ADVERSE_MEDIA = "Global Watchlist Source List Adverse Media"
  val WATCHLIST_ENFORCEMENT = "Global Watchlist Source List Enforcement"
  val WATCHLIST_PEP = "Global Watchlist Source List PEP"
  val WATCHLIST_SANCTIONS = "Global Watchlist Source List Sanctions"
  val WATCHLIST_CUSTOM = "Global Watchlist Source List Custom Watchlist"
  val WATCHLIST_HISTORICAL_RANGE = "Global Watchlist Adverse Media Historical Range"

  val AUTO_MONITORING = "Auto Monitoring"
  val ENTITY_TYPE = "Entity Type"
  val SCREENING_POLICIES = "Screening Policies"
  val MONITORING_POLICIES = "Monitoring Policies"
  val STATUS = "Status"
  val AUTHOR_NAME = "Author Name"
  val CREATION_TIMESTAMP = "Creation Timestamp"
  val LAST_UPDATE_TIMESTAMP = "Last Update Timestamp"
  val LAST_UPDATED_BY = "Last Updated By"
  val SOURCE_LIST ="Source List"
  val THRESHOLD_CRITERIA = "Threshold Criteria"
  val FALSE_POSITIVE_REDUCTION_CRITERIA = "False Positive Reduction Criteria"

  private val WL_EXTRA_COUNTRIES_BY_CODE = Map(
    "xk" -> "Kosovo"
  )

  def getCountryName(value: String): String = {
    WL_EXTRA_COUNTRIES_BY_CODE.get(value).getOrElse(value)
  }

  def getFormattedDateRange(value: String): String = {
    dateRangeMap.get(value).getOrElse(value)
  }

  val dvFields: ListMap[String, List[String]] = ListMap(
    Strategy.name -> List("Overall Account Strategy"),
    MinimumAge.name -> List("Minimum Age Requirement - Under The Age Of", "Minimum Age Requirement - Then"),
    FTBMatchingDates.name -> List("Document Matching Tolerance - Difference in Months", "Document Matching Tolerance - Difference in Months - Then"),
    FTBMatchingNonDates.name -> List("Document Matching Tolerance - Under Threshold Of", "Document Matching Tolerance - Under Threshold Of - Then"),
    FirstNameMatchWithNickNameDB.name -> List("Input & Extracted Data Matching Tolerance - Match ‘First Name’ against known ‘Nicknames’ and ‘Aliases"),
    IPVsExtractedMatchingDates.name -> List("Input & Extracted Data Matching Tolerance - Difference in Months", "Input & Extracted Data Matching Tolerance - Difference in Months - Then"),
    IPVsExtractedMatchingNonDates.name -> List("Input & Extracted Data Matching Tolerance - Under Threshold Of", "Input & Extracted Data Matching Tolerance - Under Threshold Of - Then"),
    DocumentExpirationGracePast.name -> List("Document Expiration Date Rules - Grace Period", "Document Expiration Date Rules - Grace Period - Day(s) after Expiration", "Document Expiration Date Rules - Grace Period - Day(s) after Expiration - Then"),
    DocumentExpirationGraceFuture.name -> List("Document Expiration Date Rules - Intercept soon to expire", "Document Expiration Date Rules - Intercept soon to expire - Day(s) until Expiration", "Document Expiration Date Rules - Intercept soon to expire - Day(s) until Expiration - Then")
  )
  private val dvFieldsUnderThresholdValues: ListMap[Int, String] = ListMap(
    50 -> "50%",
    55 -> "55%",
    60 -> "60%",
    65 -> "Very Lenient 65%",
    70 -> "70%",
    75 -> "Lenient 75%",
    80 -> "80%",
    85 -> "Strict 85%",
    90 -> "90%",
    95 -> "Very Strict 95%",
    100 -> "Exact Match 100%"
  )

  def getDdvFieldsUnderThresholdValues(value: String) = {
    try {
      dvFieldsUnderThresholdValues.get(value.trim.toInt).getOrElse(value)
    } catch {
      case _ => value
    }
  }

  //Setting values

  val ENABLED: String = "Enabled"
  val DISABLED: String = "Disabled"

  private val formattedDvConfigValues: Map[String, String] = Map(
    "1" -> ENABLED,
    "lenient" -> "Lenient",
    "strict" -> "Strict",
    "0" -> DISABLED
  )

  def getFormattedDvConfigValues(value: String) = {
    formattedDvConfigValues.get(value).getOrElse(DISABLED)
  }

}
