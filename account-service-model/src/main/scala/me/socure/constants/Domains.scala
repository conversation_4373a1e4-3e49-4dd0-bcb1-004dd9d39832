package me.socure.constants

import me.socure.constants.Groups.Group
import me.socure.constants.platform.PlatformPermissionScope
import me.socure.types.scala.{ByMember, Enum}

object Domains extends Enum with ByMember {
  type Domain = EnumVal

  sealed case class EnumVal(id: Int, name: String, group: Int, platformScope: Int) extends Value

  val ACCOUNTS = new Domain(1,"Accounts", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val USERS = new Domain(2,"Users", Groups.GLOBAL.id, PlatformPermissionScope.GLOBAL.id)
  val DOCUMENTATION = new Domain(3,"Documentation", Groups.GLOBAL.id, PlatformPermissionScope.GLOBAL.id)
  val BATCHJOB = new Domain(4,"BatchJob", Groups.GLOBAL.id, PlatformPermissionScope.GLOBAL.id)
  val FILE_UPLOADS = new Domain(5,"FileUploads", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val TRANSACTIONS = new Domain(6,"Transactions", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val EVENT_MANAGERS = new Domain(7,"EventManagers", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val REPORTS = new Domain(8,"Reports", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val SETTINGS = new Domain(9,"Settings", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val TEMPLATES = new Domain(10,"Templates", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val USER_ROLES = new Domain(11,"UserRoles", Groups.GLOBAL.id, PlatformPermissionScope.GLOBAL.id)
  val OVERVIEW = new Domain(12,"Overview", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.NONE.id)
  val BATCH = new Domain(13, "Batch", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val EXPORT_AUDIT = new Domain(14, "ExportAudit", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val DECISION_LOGIC = new Domain(15, "DecisionLogic", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.NONE.id)
  val EXPORT_EVENT_AUDIT = new Domain(16, "ExportEventAudit", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val ExportWlUserActions = new Domain(17,"ExportWlUserActions", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val PII_ACCESS = new Domain(18,"PIIAccess", Groups.GLOBAL.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val CASE_MANAGEMENT = new Domain(19, "CaseManagement", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val CUSTOM_WATCHLIST = new Domain(20, "CustomWatchlist", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val CUSTOMER_FILE_UPLOADS = new Domain(21,"CustomerFileUploads", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val DASHBOARD_IP_DOMAIN = new Domain(22,"DashboardIpDomain", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val REPORTING = new Domain(23,"Reporting", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val DOCV_CASE_MANAGEMENT = new Domain(24,"DocVCaseManagement", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.NONE.id)
  val ANALYTICS = new Domain(25,"Analytics", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val SPONSOR_BANK = new Domain(26,"SponsorBank", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val NEG_POS_LIST = new Domain(27,"NEG_POS_LIST", Groups.GLOBAL.id, PlatformPermissionScope.NONE.id)
  val DASHBOARDS = new Domain(25,"Dashboards", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val WORKFLOW = new Domain(26, "Workflow", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val ALLOW_DENY_LIST = new Domain(27, "AllowDenyList", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val AUDIT_LOGS = new Domain(28, "AuditLogs", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val CONFIGURATIONS = new Domain(29, "Configurations", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val PLATFORM_CUSTOMER_FILE_UPLOADS = new Domain(30,"PlatformCustomerFileUploads", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)
  val FRAUD_CASE_MANAGEMENT = new Domain(31, "FraudCaseManagement", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.WORKFLOW_SPECIFIC.id)
  val INTERNAL_SETTINGS = new Domain(32, "InternalSettings", Groups.ENVIRONMENT_SPECIFIC.id, PlatformPermissionScope.GLOBAL.id)

  val byId: Int => Option[Domain] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byDomainName(name: String): Option[Domain] = values.find(_.name.contains(name.trim.toLowerCase))

  def byDomainId(id: Int): Option[Domain] = values.find(_.id == id)

  def byGroupId(id: Int): Seq[Domain] = values.filter(_.group == id)

  def globalDomains(): Seq[Domain] = values.filter(_.group == Groups.GLOBAL.id)

  def isOfDomainType(id: Int, domainType: Group): Boolean = values.exists(v => v.id == id && v.group == domainType.id)

  def environmentSpecificDomains(): Seq[Domain] = values.filter(_.group == Groups.ENVIRONMENT_SPECIFIC.id)

  def byDomain(value: Value): Option[Domain] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Domains.byDomain(value).map(_.id).get

  def getDomainGroupedByPlatformScope():  Map[Int, Seq[Domain]] = values.groupBy(_.platformScope)

  def getDomainsFilteredByPlatformScope(scope: Int): Seq[Domain] = values.filter(_.platformScope == scope)

  def getAll: Set[Domain] = {
    values.toSet
  }
}