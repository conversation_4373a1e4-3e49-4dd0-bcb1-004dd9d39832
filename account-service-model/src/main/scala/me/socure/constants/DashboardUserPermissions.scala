package me.socure.constants

import me.socure.constants.Actions.Action
import me.socure.constants.Domains.Domain
import me.socure.constants.Resources.Resource
import me.socure.model.account.DashboardUserPermissionResult

import scala.collection.mutable.{Set => MSet}

object DashboardUserPermissions {

  case class DashboardUserPermission(id: Int,
                                     name: String,
                                     label: String,
                                     group: Resource,
                                     domain: Domain,
                                     action: Action)

  private val mutableRoles = MSet.empty[DashboardUserPermission]

  lazy val values: Set[DashboardUserPermission] = mutableRoles.toSet

  lazy val permissionIds: Set[Int] = mutableRoles.map(_.id).toSet

  private def createRole(id: Int, name: String, label: String, group: Resource, domain: Domain, action: Action): DashboardUserPermission = {
    if (mutableRoles.exists(_.id == id)) {
      throw new IllegalArgumentException(s"Role with id = $id already exists")
    } else {
      val role = DashboardUserPermission(
        id = id,
        name = name,
        label = label,
        group = group,
        domain = domain,
        action = action
      )
      mutableRoles.add(role)
      role
    }
  }

  val ACCOUNTS_CREATE: DashboardUserPermission = createRole(1001, "ACCOUNTS_CREATE", "Create Accounts",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ACCOUNTS, action = Actions.CREATE)
  val ACCOUNTS_MODIFY: DashboardUserPermission = createRole(1002, "ACCOUNTS_MODIFY", "Modify Accounts",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ACCOUNTS, action = Actions.MODIFY)
  val ACCOUNTS_VIEW: DashboardUserPermission = createRole(1003, "ACCOUNTS_VIEW", "View Accounts",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ACCOUNTS, action = Actions.VIEW)
  val ACCOUNTS_DELETE: DashboardUserPermission = createRole(1004, "ACCOUNTS_DELETE", "Delete Accounts",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ACCOUNTS, action = Actions.DELETE)

  val USERS_CREATE: DashboardUserPermission = createRole(1005, "USERS_CREATE", "Create Users",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USERS, action = Actions.CREATE)
  val USERS_MODIFY: DashboardUserPermission = createRole(1006, "USERS_MODIFY", "Modify Users",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USERS, action = Actions.MODIFY)
  val USERS_VIEW: DashboardUserPermission = createRole(1007, "USERS_VIEW", "View Users",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USERS, action = Actions.VIEW)
  val USERS_DELETE: DashboardUserPermission = createRole(1008, "USERS_DELETE", "Delete Users",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USERS, action = Actions.DELETE)

  val TRANSACTIONS_CREATE: DashboardUserPermission = createRole(1009, "TRANSACTIONS_CREATE", "Create Transaction",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TRANSACTIONS, action = Actions.CREATE)
  val TRANSACTIONS_VIEW: DashboardUserPermission = createRole(1010, "TRANSACTIONS_VIEW", "View Transaction",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TRANSACTIONS, action = Actions.VIEW)

  val BATCHJOB_CREATE: DashboardUserPermission = createRole(1011, "BATCHJOB_CREATE", "Create BatchJob",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.BATCHJOB, action = Actions.CREATE)
  val BATCHJOB_MODIFY: DashboardUserPermission = createRole(1012, "BATCHJOB_MODIFY", "Modify BatchJob",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.BATCHJOB, action = Actions.MODIFY)
  val BATCHJOB_VIEW: DashboardUserPermission = createRole(1013, "BATCHJOB_VIEW", "View BatchJob",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.BATCHJOB, action = Actions.VIEW)
  val BATCHJOB_DELETE: DashboardUserPermission = createRole(1014, "BATCHJOB_DELETE", "Delete BatchJob",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.BATCHJOB, action = Actions.DELETE)

  val EVENT_MANAGERS_CREATE: DashboardUserPermission = createRole(1015, "EVENT_MANAGERS_CREATE", "Create Event Manager",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EVENT_MANAGERS, action = Actions.CREATE)
  val EVENT_MANAGERS_MODIFY: DashboardUserPermission = createRole(1016, "EVENT_MANAGERS_MODIFY", "Modify Event Manager",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EVENT_MANAGERS, action = Actions.MODIFY)
  val EVENT_MANAGERS_VIEW: DashboardUserPermission = createRole(1017, "EVENT_MANAGERS_VIEW", "View Event Manager",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EVENT_MANAGERS, action = Actions.VIEW)
  val EVENT_MANAGERS_DELETE: DashboardUserPermission = createRole(1018, "EVENT_MANAGERS_DELETE", "Delete Event Manager",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EVENT_MANAGERS, action = Actions.DELETE)

  val REPORTS_CREATE: DashboardUserPermission = createRole(1019, "REPORTS_CREATE", "Create Reports",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTS, action = Actions.CREATE)
  val REPORTS_MODIFY: DashboardUserPermission = createRole(1020, "REPORTS_MODIFY", "Modify Reports",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTS, action = Actions.MODIFY)
  val REPORTS_VIEW: DashboardUserPermission = createRole(1021, "REPORTS_VIEW", "View Reports",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTS, action = Actions.VIEW)
  val REPORTS_DELETE: DashboardUserPermission = createRole(1022, "REPORTS_DELETE", "Delete Reports",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTS, action = Actions.DELETE)

  val SETTINGS_MODIFY: DashboardUserPermission = createRole(1026, "SETTINGS_MODIFY", "Modify Settings",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SETTINGS, action = Actions.MODIFY)
  val SETTINGS_VIEW: DashboardUserPermission = createRole(1027, "SETTINGS_VIEW", "View Settings",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SETTINGS, action = Actions.VIEW)

  val TEMPLATES_CREATE: DashboardUserPermission = createRole(1028, "TEMPLATES_CREATE", "Create Templates",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TEMPLATES, action = Actions.CREATE)
  val TEMPLATES_MODIFY: DashboardUserPermission = createRole(1029, "TEMPLATES_MODIFY", "Modify Templates",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TEMPLATES, action = Actions.MODIFY)
  val TEMPLATES_VIEW: DashboardUserPermission = createRole(1030, "TEMPLATES_VIEW", "View Templates",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TEMPLATES, action = Actions.VIEW)
  val TEMPLATES_DELETE: DashboardUserPermission = createRole(1031, "TEMPLATES_DELETE", "Delete Templates",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TEMPLATES, action = Actions.DELETE)

  val USER_ROLES_CREATE: DashboardUserPermission = createRole(1032, "USER_ROLES_CREATE", "Create User Roles",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USER_ROLES, action = Actions.CREATE)
  val USER_ROLES_MODIFY: DashboardUserPermission = createRole(1033, "USER_ROLES_MODIFY", "Modify User Roles",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USER_ROLES, action = Actions.MODIFY)
  val USER_ROLES_VIEW: DashboardUserPermission = createRole(1034, "USER_ROLES_VIEW", "View User Roles",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USER_ROLES, action = Actions.VIEW)
  val USER_ROLES_DELETE: DashboardUserPermission = createRole(1035, "USER_ROLES_DELETE", "Delete User Roles",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USER_ROLES, action = Actions.DELETE)

  val OVERVIEW_VIEW: DashboardUserPermission = createRole(1036, "OVERVIEW_VIEW", "Overview",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.OVERVIEW, action = Actions.VIEW)
  val DOCUMENTATION_VIEW: DashboardUserPermission = createRole(1037, "DOCUMENTATION_VIEW", "Documentation",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DOCUMENTATION, action = Actions.VIEW)

  val DECISION_LOGIC_CREATE: DashboardUserPermission = createRole(1038, "DECISION_LOGIC_CREATE", "Create Decision Logic",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DECISION_LOGIC, action = Actions.CREATE)
  val DECISION_LOGIC_EDIT: DashboardUserPermission = createRole(1039, "DECISION_LOGIC_EDIT", "Edit Decision Logic",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DECISION_LOGIC, action = Actions.MODIFY)
  val DECISION_LOGIC_VIEW: DashboardUserPermission = createRole(1040, "DECISION_LOGIC_VIEW", "View Decision Logic",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DECISION_LOGIC, action = Actions.VIEW)
  val DECISION_LOGIC_SIMULATE: DashboardUserPermission = createRole(1041, "DECISION_LOGIC_SIMULATE", "Delete Decision Logic",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DECISION_LOGIC, action = Actions.DELETE)
  val DECISION_LOGIC_DEPLOY: DashboardUserPermission = createRole(1042, "DECISION_LOGIC_DEPLOY", "Deploy Decision Logic",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DECISION_LOGIC, action = Actions.DEPLOY)

  val PII_ACCESS_VIEW: DashboardUserPermission = createRole(1043, "PII_ACCESS_VIEW", "View PII Access", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.PII_ACCESS, action = Actions.VIEW)

  val CASE_MANAGEMENT_REVIEW: DashboardUserPermission = createRole(1044, "CASE_MANAGEMENT_REVIEW", "Case Management Review", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.REVIEW)
  val CASE_MANAGEMENT_COMMENT: DashboardUserPermission = createRole(1045, "CASE_MANAGEMENT_COMMENT", "Case Management Comment", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.COMMENT)
  val CASE_MANAGEMENT_APPROVE: DashboardUserPermission = createRole(1046, "CASE_MANAGEMENT_APPROVE", "Case Management Approve", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.APPROVE)
  val CASE_MANAGEMENT_SCHEDULE: DashboardUserPermission = createRole(1047, "CASE_MANAGEMENT_SCHEDULE", "Case Management Schedule", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.SCHEDULE)
  val CASE_MANAGEMENT_VIEW: DashboardUserPermission = createRole(1048, "CASE_MANAGEMENT_VIEW", "Case Management View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.VIEW)
  val CASE_MANAGEMENT_INVESTIGATE: DashboardUserPermission = createRole(1059, "CASE_MANAGEMENT_INVESTIGATE", "Case Management Investigate", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.INVESTIGATE)

  //CW - Custom Watchlist
  val CUSTOM_WATCHLIST_CREATE: DashboardUserPermission = createRole(1049, "CUSTOM_WATCHLIST_CREATE", "Create Custom Watchlist", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CUSTOM_WATCHLIST, action = Actions.CREATE)
  val CUSTOM_WATCHLIST_MODIFY: DashboardUserPermission = createRole(1050, "CUSTOM_WATCHLIST_MODIFY", "Edit Custom Watchlist", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CUSTOM_WATCHLIST, action = Actions.MODIFY)
  val CUSTOM_WATCHLIST_VIEW: DashboardUserPermission = createRole(1051, "CUSTOM_WATCHLIST_VIEW", "View Custom Watchlist", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CUSTOM_WATCHLIST, action = Actions.VIEW)
  val CUSTOM_WATCHLIST_DELETE: DashboardUserPermission = createRole(1052, "CUSTOM_WATCHLIST_DELETE", "Delete Custom Watchlist", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CUSTOM_WATCHLIST, action = Actions.DELETE)

  val DASHBOARD_IP_DOMAIN_VIEW: DashboardUserPermission = createRole(1053, "DASHBOARD_IP_DOMAIN_VIEW", "View Dashboard Ip Domain", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DASHBOARD_IP_DOMAIN, action = Actions.VIEW)
  val DASHBOARD_IP_DOMAIN_MODIFY: DashboardUserPermission = createRole(1054, "DASHBOARD_IP_DOMAIN_MODIFY", "Modify Dashboard Ip Domain", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DASHBOARD_IP_DOMAIN, action = Actions.MODIFY)
  val DASHBOARD_IP_DOMAIN_CREATE: DashboardUserPermission = createRole(1055, "DASHBOARD_IP_DOMAIN_CREATE", "Create Dashboard Ip Domain", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DASHBOARD_IP_DOMAIN, action = Actions.CREATE)
  val DASHBOARD_IP_DOMAIN_DELETE: DashboardUserPermission = createRole(1056, "DASHBOARD_IP_DOMAIN_DELETE", "Delete Dashboard Ip Domain", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DASHBOARD_IP_DOMAIN, action = Actions.DELETE)

  val REPORTING_VIEW: DashboardUserPermission = createRole(1057, "REPORTING_VIEW", "View Reporting", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTING, action = Actions.VIEW)
  val REPORTING_CREATE: DashboardUserPermission = createRole(1058, "REPORTING_CREATE", "Create Reporting", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTING, action = Actions.CREATE)

  val DOCV_CASE_MANAGEMENT_REVIEW: DashboardUserPermission = createRole(1060, "DOCV_CASE_MANAGEMENT_REVIEW", "DocV Case Management Review", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DOCV_CASE_MANAGEMENT, action = Actions.REVIEW)
  val DOCV_CASE_MANAGEMENT_COMMENT: DashboardUserPermission = createRole(1061, "DOCV_CASE_MANAGEMENT_COMMENT", "DocV Case Management Comment", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DOCV_CASE_MANAGEMENT, action = Actions.COMMENT)
  val DOCV_CASE_MANAGEMENT_VIEW: DashboardUserPermission = createRole(1062, "DOCV_CASE_MANAGEMENT_VIEW", "DocV Case Management View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DOCV_CASE_MANAGEMENT, action = Actions.VIEW)

  val ANALYTICS_VIEW: DashboardUserPermission = createRole(1063, "ANALYTICS_VIEW", "Analytics View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ANALYTICS, action = Actions.VIEW)

  val DASHBOARDS_VIEW: DashboardUserPermission = createRole(1064, "DASHBOARDS_VIEW", "Dashboards View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DASHBOARDS, action = Actions.VIEW)

  val WORKFLOW_CREATE: DashboardUserPermission = createRole(1065, "WORKFLOW_CREATE", "Create Workflow",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.CREATE)
  val WORKFLOW_MODIFY: DashboardUserPermission = createRole(1066, "WORKFLOW_MODIFY", "Modify Workflow",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.MODIFY)
  val WORKFLOW_VIEW: DashboardUserPermission = createRole(1067, "WORKFLOW_VIEW", "View Workflow",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.VIEW)
  val WORKFLOW_DELETE: DashboardUserPermission = createRole(1068, "WORKFLOW_DELETE", "Delete Workflow",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.DELETE)
  val WORKFLOW_MOVE_TO_LIVE: DashboardUserPermission = createRole(1069, "WORKFLOW_MOVE_TO_LIVE", "Make Workflow Live",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.MOVE_TO_LIVE)
  val WORKFLOW_TEST: DashboardUserPermission = createRole(1070, "WORKFLOW_TEST", "Test Workflow",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.WORKFLOW, action = Actions.TEST)

  val CASE_MANAGEMENT_ASSIGN: DashboardUserPermission = createRole(1071, "CASE_MANAGEMENT_ASSIGN", "Case Management Assign", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.ASSIGN)
  val CASE_MANAGEMENT_EVALUATE: DashboardUserPermission = createRole(1072, "CASE_MANAGEMENT_EVALUATE", "Case Management Evaluate", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CASE_MANAGEMENT, action = Actions.EVALUATE)

  val ALLOW_DENY_LIST_CREATE: DashboardUserPermission = createRole(1073, "ALLOW_DENY_LIST_CREATE", "Create Allow/Deny List",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ALLOW_DENY_LIST, action = Actions.CREATE)
  val ALLOW_DENY_LIST_MODIFY: DashboardUserPermission = createRole(1074, "ALLOW_DENY_LIST_MODIFY", "Modify Allow/Deny List",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ALLOW_DENY_LIST, action = Actions.MODIFY)
  val ALLOW_DENY_LIST_VIEW: DashboardUserPermission = createRole(1075, "ALLOW_DENY_LIST_VIEW", "View Allow/Deny List",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ALLOW_DENY_LIST, action = Actions.VIEW)
  val ALLOW_DENY_LIST_DELETE: DashboardUserPermission = createRole(1076, "ALLOW_DENY_LIST_DELETE", "Delete Allow/Deny List",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ALLOW_DENY_LIST, action = Actions.DELETE)

  val AUDIT_LOGS_VIEW: DashboardUserPermission = createRole(1077, "AUDIT_LOGS_VIEW", "Audit Logs View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.AUDIT_LOGS, action = Actions.VIEW)

  val CONFIGURATIONS_CREATE: DashboardUserPermission = createRole(1078, "CONFIGURATIONS_CREATE", "Create Configurations",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CONFIGURATIONS, action = Actions.CREATE)
  val CONFIGURATIONS_MODIFY: DashboardUserPermission = createRole(1079, "CONFIGURATIONS_MODIFY", "Modify Configurations",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CONFIGURATIONS, action = Actions.MODIFY)
  val CONFIGURATIONS_VIEW: DashboardUserPermission = createRole(1080, "CONFIGURATIONS_VIEW", "View Configurations",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CONFIGURATIONS, action = Actions.VIEW)
  val CONFIGURATIONS_DELETE: DashboardUserPermission = createRole(1081, "CONFIGURATIONS_DELETE", "Delete Configurations",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CONFIGURATIONS, action = Actions.DELETE)

  val PLATFORM_FILE_UPLOAD_PROVISION: DashboardUserPermission = createRole(1082, "PLATFORM_FILE_UPLOAD_PROVISION", "Platform Customer File Upload",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.PLATFORM_CUSTOMER_FILE_UPLOADS, action = Actions.CREATE)

  val FRAUD_CASE_MANAGEMENT_REVIEW: DashboardUserPermission = createRole(1083, "FRAUD_CASE_MANAGEMENT_REVIEW", "Fraud Case Management Review", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.FRAUD_CASE_MANAGEMENT, action = Actions.REVIEW)
  val FRAUD_CASE_MANAGEMENT_ASSIGN: DashboardUserPermission = createRole(1084, "FRAUD_CASE_MANAGEMENT_ASSIGN", "Fraud Case Management Assign", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.FRAUD_CASE_MANAGEMENT, action = Actions.ASSIGN)
  val FRAUD_CASE_MANAGEMENT_COMMENT: DashboardUserPermission = createRole(1085, "FRAUD_CASE_MANAGEMENT_COMMENT", "Fraud Case Management Comment", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.FRAUD_CASE_MANAGEMENT, action = Actions.COMMENT)
  val FRAUD_CASE_MANAGEMENT_VIEW: DashboardUserPermission = createRole(1086, "FRAUD_CASE_MANAGEMENT_VIEW", "Fraud Case Management View", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.FRAUD_CASE_MANAGEMENT, action = Actions.VIEW)
  val FRAUD_CASE_MANAGEMENT_EVALUATE: DashboardUserPermission = createRole(1087, "FRAUD_CASE_MANAGEMENT_EVALUATE", "Fraud Case Management Evaluate", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.FRAUD_CASE_MANAGEMENT, action = Actions.EVALUATE)

  val FILE_UPLOAD_PROVISION: DashboardUserPermission = createRole(2012, "FILE_UPLOAD_PROVISION", "Customer File Upload",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.CUSTOMER_FILE_UPLOADS, action = Actions.CREATE)

  val ACCOUNTS_PROVISION: DashboardUserPermission = createRole(2001, "ACCOUNTS_PROVISION", "Provison Accounts",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ACCOUNTS, action = Actions.PROVISION)
  val USERS_PROVISION: DashboardUserPermission = createRole(2002, "USERS_PROVISION", "Provison Users",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USERS, action = Actions.PROVISION)
  val TRANSACTIONS_PROVISION: DashboardUserPermission = createRole(2003, "TRANSACTIONS_PROVISION", "Provison Transactions",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TRANSACTIONS, action = Actions.PROVISION)
  val BATCHJOB_PROVISION: DashboardUserPermission = createRole(2004, "BATCHJOB_PROVISION", "Provision Batchjob",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.BATCHJOB, action = Actions.PROVISION)
  val EVENT_MANAGERS_PROVISION: DashboardUserPermission = createRole(2005, "EVENT_MANAGERS_PROVISION", "Provision Event Managers",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EVENT_MANAGERS, action = Actions.PROVISION)
  val REPORTS_PROVISION: DashboardUserPermission = createRole(2006, "REPORTS_PROVISION", "Provision Reports",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.REPORTS, action = Actions.PROVISION)
  val SETTINGS_PROVISION: DashboardUserPermission = createRole(2007, "SETTINGS_PROVISION", "Provision Settings",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SETTINGS, action = Actions.PROVISION)
  val TEMPLATES_PROVISION: DashboardUserPermission = createRole(2008, "TEMPLATES_PROVISION", "Provision Templates",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.TEMPLATES, action = Actions.PROVISION)
  val USER_ROLES_PROVISION: DashboardUserPermission = createRole(2009, "USER_ROLES_PROVISION", "Provision User Roles",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.USER_ROLES, action = Actions.PROVISION)
  val OVERVIEW_PROVISION: DashboardUserPermission = createRole(2010, "OVERVIEW_PROVISION", "Provision Overview",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.OVERVIEW, action = Actions.PROVISION)
  val DOCUMENTATION_PROVISION: DashboardUserPermission = createRole(2011, "DOCUMENTATION_PROVISION", "Provision Documentation",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.DOCUMENTATION, action = Actions.PROVISION)
  val AUDIT_EXPORT_PROVISION: DashboardUserPermission = createRole(2013, "AUDIT_EXPORT_PROVISION", "Provision Export Audit",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EXPORT_AUDIT, action = Actions.PROVISION)
  val EVENT_AUDIT_EXPORT_PROVISION: DashboardUserPermission = createRole(2014, "EVENT_AUDIT_EXPORT_PROVISION", "Provision Event Audit Export",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.EXPORT_EVENT_AUDIT, action = Actions.PROVISION)
  val ExportWlUserActionsProvision: DashboardUserPermission = createRole(2015, "ExportWlUserActionsProvision", "Provision WL User Action Export",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.ExportWlUserActions, action = Actions.PROVISION)

  val CHANGE_REQUESTS_CREATE: DashboardUserPermission = createRole(2016, "CHANGE_REQUESTS_CREATE", "Create Change Requests",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SPONSOR_BANK, action = Actions.CREATE)
  val CHANGE_REQUESTS_MODIFY: DashboardUserPermission = createRole(2017, "CHANGE_REQUESTS_MODIFY", "Modify Change Requests",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SPONSOR_BANK, action = Actions.MODIFY)
  val CHANGE_REQUESTS_VIEW: DashboardUserPermission = createRole(2018, "CHANGE_REQUESTS_VIEW", "View Change Requests",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SPONSOR_BANK, action = Actions.VIEW)
  val CHANGE_REQUESTS_DELETE: DashboardUserPermission = createRole(2019, "CHANGE_REQUESTS_DELETE", "Delete Change Requests",  group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.SPONSOR_BANK, action = Actions.DELETE)

  val NEG_POS_LIST_VIEW: DashboardUserPermission = createRole(2020, "NEG_POS_LIST_VIEW", "View NegPos List", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.NEG_POS_LIST, action = Actions.VIEW)
  val NEG_POS_LIST_CREATE: DashboardUserPermission = createRole(2021, "NEG_POS_LIST_CREATE", "Create NegPos List", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.NEG_POS_LIST, action = Actions.CREATE)
  val NEG_POS_LIST_EDIT: DashboardUserPermission = createRole(2022, "NEG_POS_LIST_EDIT", "Edit NegPos List", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.NEG_POS_LIST, action = Actions.MODIFY)

  val INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW: DashboardUserPermission = createRole(2023, "INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW", "View RiskOS Internal General Settings Instance Name", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_EDIT:DashboardUserPermission = createRole(2024, "INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_EDIT", "Edit RiskOS Internal General Settings Instance Name", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)
  val INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW: DashboardUserPermission = createRole(2025, "INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW", "View RiskOS Internal General Settings General Rate limit", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_EDIT:DashboardUserPermission = createRole(2026, "INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_EDIT", "Edit RiskOS Internal General Settings Rate limit", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)

  val INTERNAL_ENRICHMENTS_SETTINGS_CREATE:DashboardUserPermission = createRole(2027, "INTERNAL_ENRICHMENTS_SETTINGS_CREATE", "Create RiskOS Internal Enrichment Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.CREATE)
  val INTERNAL_ENRICHMENTS_SETTINGS_VIEW:DashboardUserPermission = createRole(2028, "INTERNAL_ENRICHMENTS_SETTINGS_VIEW", "View RiskOS Internal Enrichment Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_ENRICHMENTS_SETTINGS_EDIT:DashboardUserPermission = createRole(2029, "INTERNAL_ENRICHMENTS_SETTINGS_EDIT", "Edit RiskOS Internal Enrichment Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)
  val INTERNAL_ENRICHMENTS_SETTINGS_DELETE:DashboardUserPermission = createRole(2030, "INTERNAL_ENRICHMENTS_SETTINGS_DELETE", "Delete RiskOS Internal Enrichment Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.DELETE)

  val INTERNAL_USE_CASES_SETTINGS_VIEW:DashboardUserPermission = createRole(2031, "INTERNAL_USE_CASES_SETTINGS_VIEW", "View RiskOS Internal Use cases Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_USE_CASES_SETTINGS_EDIT:DashboardUserPermission = createRole(2032, "INTERNAL_USE_CASES_SETTINGS_EDIT", "Edit RiskOS Internal Use cases Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)

  val INTERNAL_TILE_CONFIG_SETTINGS_CREATE:DashboardUserPermission = createRole(2033, "INTERNAL_TILE_CONFIG_SETTINGS_CREATE", "Create RiskOS Internal Tile Config Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.CREATE)
  val INTERNAL_TILE_CONFIG_SETTINGS_VIEW:DashboardUserPermission = createRole(2034, "INTERNAL_TILE_CONFIG_SETTINGS_VIEW", "View RiskOS Internal Tile Config Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_TILE_CONFIG_SETTINGS_EDIT:DashboardUserPermission = createRole(2035, "INTERNAL_TILE_CONFIG_SETTINGS_EDIT", "Edit RiskOS Internal Tile Config Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)
  val INTERNAL_TILE_CONFIG_SETTINGS_DELETE:DashboardUserPermission = createRole(2036, "INTERNAL_TILE_CONFIG_SETTINGS_DELETE", "Delete RiskOS Internal Tile Config Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.DELETE)

  val INTERNAL_FEATURE_FLAGS_SETTINGS_CREATE:DashboardUserPermission = createRole(2037, "INTERNAL_FEATURE_FLAGS_SETTINGS_CREATE", "Create RiskOS Internal Feature Flags Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.CREATE)
  val INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW: DashboardUserPermission = createRole(2038,"INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW", "View RiskOS Internal Feature Flags Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.VIEW)
  val INTERNAL_FEATURE_FLAGS_SETTINGS_EDIT:DashboardUserPermission = createRole(2039, "INTERNAL_FEATURE_FLAGS_SETTINGS_EDIT", "Edit RiskOS Internal Feature Flags Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.MODIFY)
  val INTERNAL_FEATURE_FLAGS_SETTINGS_DELETE:DashboardUserPermission = createRole(2040, "INTERNAL_FEATURE_FLAGS_SETTINGS_DELETE", "Delete RiskOS Internal Feature Flags Settings", group = Resources.DASHBOARD_PERMISSIONS, domain = Domains.INTERNAL_SETTINGS, action = Actions.DELETE)

  def byId(id: Int): Option[DashboardUserPermission] = {
    values.find(_.id == id)
  }

  def byName(name: String): Option[DashboardUserPermission] = {
    values.find(_.name == name)
  }

  def isProvisioningPermission(id: Int): Boolean = {
    values.find(_.id == id).exists(_.action == Actions.PROVISION)
  }

  def startsWithName(name: String): Set[Int] = {
    values.filter(_.name.startsWith(name)).map(_.id)
  }

  def isKnown(id: Int): Boolean = values.exists(_.id == id)

  def contains(id: Int): Boolean = permissionIds.contains(id)

  def getAllProvisioningPermission(): Set[DashboardUserPermission] = {
    values.filter(r => r.action == Actions.PROVISION)
  }

  def provisioningPermission(domainId: Int): Set[DashboardUserPermission] = {
    values.filter(r => r.domain.id == domainId && r.action == Actions.PROVISION)
  }

  def toPermissionResult(permission: DashboardUserPermission, environmentTypeId: Int = -1): DashboardUserPermissionResult = {
    DashboardUserPermissionResult(permission.id, if (environmentTypeId == -1) None else Option(environmentTypeId), permission.name,
      if (permission.domain != null) me.socure.model.account.Domain(permission.domain.id, permission.domain.name, permission.domain.group) else null,
      if (permission.action != null) me.socure.model.account.Action(permission.action.id, permission.action.name) else null
    )
  }
}