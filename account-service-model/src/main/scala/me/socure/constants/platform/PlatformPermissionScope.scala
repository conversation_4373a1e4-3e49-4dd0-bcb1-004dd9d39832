package me.socure.constants.platform

import me.socure.types.scala.{ByMember, Enum}

object PlatformPermissionScope extends Enum with ByMember {
  type PlatformPermissionScope = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val NONE = new PlatformPermissionScope(0, "None")
  val GLOBAL = new PlatformPermissionScope(1, "Global")
  val WORKFLOW_SPECIFIC = new PlatformPermissionScope(2, "Workflow Specific")

  val byId: Int => Option[PlatformPermissionScope] = byMember(_.id)
}