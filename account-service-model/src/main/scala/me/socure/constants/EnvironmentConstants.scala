package me.socure.constants

/**
  * Created by <PERSON><PERSON><PERSON> on 7/21/16.
  */
object EnvironmentConstants extends Enumeration{

  type EnvironmentConstants = Value

  val PRODUCTION_ENVIRONMENT = Value(1, "Production")
  val DEVELOPMENT_ENVIRONMENT = Value(2, "Development")
  val SANDBOX_ENVIRONMENT = Value(3, "Sandbox")

  //Make sure the EnvironmentTypes enum is updated, on any updates

}
