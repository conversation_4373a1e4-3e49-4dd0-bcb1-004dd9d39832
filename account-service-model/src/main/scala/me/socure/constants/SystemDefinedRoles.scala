package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object SystemDefinedRoles extends Enum with ByMember {
  type SystemDefinedRole = EnumVal

  sealed case class EnumVal(roleType: Int, name: String, description: String) extends Value

  val CUSTOMROLE = new SystemDefinedRole(0, "Custom Role", "")

  val ACCOUNTOWNER = new SystemDefinedRole(1, "Account Owner", "")
  val ADMINISTRATOR = new SystemDefinedRole(2, "Administrator", "")
  val ANALYST = new SystemDefinedRole(3, "Analyst", "")
  val DEVELOPER = new SystemDefinedRole(4, "Developer", "")

  val CASE_ANALYST = new SystemDefinedRole(5, "Case Analyst", "")
  val CASE_SUPERVISOR = new SystemDefinedRole(6, "Case Supervisor", "")
  val CASE_OFFICER = new SystemDefinedRole(7, "Case Officer", "")

  val DOCV_ANALYST = new SystemDefinedRole(8, "DocV Analyst", "DocV Analyst")

  val BSA_OFFICER = new SystemDefinedRole(9, "BSA Officer", "")
  val COMPLIANCE_ANALYST_ROLE = new SystemDefinedRole(10, "Compliance Analyst", "")
  val INTEGRATION_MANAGER = new SystemDefinedRole(11, "Integration Manager", "")

  val PROSPECT = new SystemDefinedRole(12, "Prospect", "")

  val RISKOS_SUPPORT_VIEWER = new SystemDefinedRole(13, "RiskOS Support Viewer", "")
  val RISKOS_SUPPORT_ADMIN = new SystemDefinedRole(14, "RiskOS Support Admin", "")

  val byRoleType: Int => Option[SystemDefinedRole] = byMember(_.roleType)

  def isValidRoleType(roleType: Int): Boolean = values.exists(_.roleType == roleType)

  def getAll: Set[SystemDefinedRole] = {
    values.toSet
  }
}
