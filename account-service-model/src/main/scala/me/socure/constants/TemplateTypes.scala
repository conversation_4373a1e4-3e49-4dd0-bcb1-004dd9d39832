package me.socure.constants

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object TemplateTypes extends Enum with ByM<PERSON>ber {
  type TemplateType = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val DEFAULT = new TemplateType(1, "Default")
  val CUSTOM = new TemplateType(2, "Custom")

  val byId: Int => Option[TemplateType] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byTemplateTypeName(name: String): Option[TemplateType] = values.find(_.name.contains(name.trim.toLowerCase))

  def byTemplateTypeId(id: Int): Option[TemplateType] = values.find(_.id == id)

  def byTemplateType(value: Value): Option[TemplateType] = values.find(_ == value)

  def getIdByValue(value: Value): Int = TemplateTypes.byTemplateType(value).map(_.id).get

  def getAll: Set[TemplateType] = {
    values.toSet
  }
}