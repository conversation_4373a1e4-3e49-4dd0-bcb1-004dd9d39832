package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object Actions extends Enum with By<PERSON>ember {
  type Action = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val CREATE = new Action(1, "Create")
  val MODIFY = new Action(2, "Modify")
  val VIEW = new Action(3, "View")
  val DELETE = new Action(4, "Delete")
  val PROVISION = new Action(5, "Provision")
  val DOWNLOAD = new Action(6, "Download")
  val DEPLOY = new Action(7,"Deploy")
  val REVIEW = new Action(8,"Review")
  val COMMENT = new Action(9,"Comment")
  val APPROVE = new Action(10,"Approve")
  val SCHEDULE = new Action(11,"Schedule")
  val INVESTIGATE = new Action(12, "Investigate")
  val ASSIGN = new Action(13, "Assign")
  val EVALUATE = new Action(14, "Evaluate")
  val MOVE_TO_LIVE = new Action(15, "MoveToLive")
  val TEST = new Action(16, "Test")


  val byId: Int => Option[Action] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byActionName(name: String): Option[Action] = values.find(_.name.contains(name.trim.toLowerCase))

  def byActionId(id: Int): Option[Action] = values.find(_.id == id)

  def byAction(value: Value): Option[Action] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Actions.byAction(value).map(_.id).get

  def getAll: Set[Action] = {
    values.toSet
  }
}