package me.socure.constants

import me.socure.model.user.DashboardUserRole
import me.socure.model.user.authorization.{Account, User, UserAuth}

object UserMagicTokenConstants {
  lazy val expiryTime = 10 //In Minutes
  lazy val maxClickCount = 3

  lazy val expiryTimeForDocumentLink = 2880 //In Minutes
  lazy val maxClickCountForDocumentLink  = 7

  def documentLinkGuestUser(email: String): UserAuth = {
    UserAuth(
      user = User(id = 1000000000L, firstName = "fname", lastName = "lname", email = email, isLocked = false, isAdmin = true, None, None),
      account = Account(id = 1000000000L, name = "accountname", permission = Set(DashboardUserRole.DOCUMENTATION.id), isActive = true, isInternal = true, Set.empty),
      accounts = Seq.empty,
      dashboardEnvRoles = Set.empty,
      environment = List.empty,
      redirectUrl = None
    )
  }
}
