package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object SubscriptionFeatureTypes extends Enum with ByMember {
  type SubscriptionFeatureType = EnumVal

  sealed case class EnumVal(id: Int, name: String, subscriptionTypeId: Int) extends Value

  // For Document Verification subscription type the feature type will be 0

  val Watchlist_Monitoring_Event = new SubscriptionFeatureType(1, "Monitoring", SubscriptionTypes.Watchlist_Monitoring.id)
  val Watchlist_Case_Management_Event = new SubscriptionFeatureType(2, "Case Management", SubscriptionTypes.Watchlist_Monitoring.id)

  val byId: Int => Option[SubscriptionFeatureType] = byMember(_.id)
  def bySubscriptionTypeId(subTypeId: Int): Seq[SubscriptionFeatureType] = values.filter(_.subscriptionTypeId == subTypeId)
  def isValid(groupId: Int, ids: Set[Int]): Boolean = {
    val groupedValues = values.filter(_.subscriptionTypeId == groupId)
    ids.forall(featureId => groupedValues.exists(_.id == featureId))
  }
  def isValid(groupId: Int, id: Int): Boolean =  values.filter(_.subscriptionTypeId == groupId).exists(_.id == id)

}
