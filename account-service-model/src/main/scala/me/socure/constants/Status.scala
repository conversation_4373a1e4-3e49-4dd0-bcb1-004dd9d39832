package me.socure.constants

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object Status extends Enum with <PERSON><PERSON><PERSON>ber {
  type Status = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val ACTIVE = new Status(1, "Active")
  val INACTIVE = new Status(0, "Inactive")

  val byId: Int => Option[Status] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byStatusName(name: String): Option[Status] = values.find(_.name.contains(name.trim.toLowerCase))

  def byStatusId(id: Int): Option[Status] = values.find(_.id == id)

  def byStatus(value: Value): Option[Status] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Status.byStatus(value).map(_.id).get

  def getAll: Set[Status] = {
    values.toSet
  }
}