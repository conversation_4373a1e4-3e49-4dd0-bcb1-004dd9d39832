package me.socure.constants

object SystemDefinedPlatformRoles {

  lazy val accountOwner = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,
      DashboardUserPermissions.ACCOUNTS_CREATE.id,

      DashboardUserPermissions.USER_ROLES_VIEW.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    )
  )

  lazy val administrator = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    )
  )

  lazy val analyst = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id
    )
  )

  lazy val developer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.DOCUMENTATION_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    )
  )

  lazy val caseAnalyst = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_VIEW.id,

      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    )
  )

  lazy val caseSupervisor = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USER_ROLES_VIEW.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_APPROVE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_APPROVE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id
    )
  )

  lazy val caseOfficer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.USER_ROLES_VIEW.id,

      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.CASE_MANAGEMENT_EVALUATE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_INVESTIGATE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.CASE_MANAGEMENT_EVALUATE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_INVESTIGATE.id
    )
  )

  lazy val riskOsSupportViewer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.PII_ACCESS_VIEW.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.ACCOUNTS_VIEW.id,
      DashboardUserPermissions.ACCOUNTS_CREATE.id,
      DashboardUserPermissions.USER_ROLES_VIEW.id,
      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,
      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,
      DashboardUserPermissions.DOCUMENTATION_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.DASHBOARDS_VIEW.id,
      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.DASHBOARDS_VIEW.id,
      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW.id
    )
  )

  lazy val riskOsSupportAdmin = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,
      DashboardUserPermissions.ACCOUNTS_CREATE.id,

      DashboardUserPermissions.USER_ROLES_VIEW.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_EDIT.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_EDIT.id,

      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_DELETE.id,

      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_EDIT.id,

      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_DELETE.id,

      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_DELETE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.WORKFLOW_CREATE.id,
      DashboardUserPermissions.WORKFLOW_MODIFY.id,
      DashboardUserPermissions.WORKFLOW_VIEW.id,
      DashboardUserPermissions.WORKFLOW_DELETE.id,
      DashboardUserPermissions.WORKFLOW_MOVE_TO_LIVE.id,
      DashboardUserPermissions.WORKFLOW_TEST.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.ALLOW_DENY_LIST_VIEW.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_CREATE.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_MODIFY.id,
      DashboardUserPermissions.ALLOW_DENY_LIST_DELETE.id,

      DashboardUserPermissions.DASHBOARDS_VIEW.id,

      DashboardUserPermissions.AUDIT_LOGS_VIEW.id,

      DashboardUserPermissions.PLATFORM_FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.CONFIGURATIONS_VIEW.id,
      DashboardUserPermissions.CONFIGURATIONS_CREATE.id,
      DashboardUserPermissions.CONFIGURATIONS_MODIFY.id,
      DashboardUserPermissions.CONFIGURATIONS_DELETE.id,

      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_ASSIGN.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.FRAUD_CASE_MANAGEMENT_EVALUATE.id,

      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_INSTANCE_NAME_EDIT.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_VIEW.id,
      DashboardUserPermissions.INTERNAL_GENERAL_SETTINGS_RATE_LIMIT_EDIT.id,

      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_ENRICHMENTS_SETTINGS_DELETE.id,

      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_USE_CASES_SETTINGS_EDIT.id,

      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_TILE_CONFIG_SETTINGS_DELETE.id,

      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_CREATE.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_VIEW.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_EDIT.id,
      DashboardUserPermissions.INTERNAL_FEATURE_FLAGS_SETTINGS_DELETE.id
    )
  )

}
