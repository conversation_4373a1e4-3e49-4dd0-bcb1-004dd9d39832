package me.socure.constants

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object Groups extends Enum with ByMember {
  type Group = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val GLOBAL = new Group(1, "Global")
  val ENVIRONMENT_SPECIFIC = new Group(2, "Environment Specific")


  val byId: Int => Option[Group] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byActionName(name: String): Option[Group] = values.find(_.name.contains(name.trim.toLowerCase))

  def byGroupId(id: Int): Option[Group] = values.find(_.id == id)

  def byGroup(value: Value): Option[Group] = values.find(_ == value)

  def getIdByValue(value: Value): Int = Groups.byGroup(value).map(_.id).get

  def getAll: Set[Group] = {
    values.toSet
  }
}