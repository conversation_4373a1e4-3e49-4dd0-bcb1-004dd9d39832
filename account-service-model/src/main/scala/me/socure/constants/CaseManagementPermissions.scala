package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object CaseManagementPermissions extends Enum with ByM<PERSON>ber {
  type CaseManagementPermission = EnumVal

  sealed case class EnumVal(name: String, id: Int) extends Value

  val CaseManagementReview = new CaseManagementPermission("CASE_MANAGEMENT_REVIEW", DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id)
  val CaseManagementComment = new CaseManagementPermission("CASE_MANAGEMENT_COMMENT", DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id)
  val CaseManagementApprove = new CaseManagementPermission("CASE_MANAGEMENT_APPROVE", DashboardUserPermissions.CASE_MANAGEMENT_APPROVE.id)
  val CaseManagementSchedule = new CaseManagementPermission("CASE_MANAGEMENT_SCHEDULE", DashboardUserPermissions.CASE_MANAGEMENT_SCHEDULE.id)
  val CaseManagementView = new CaseManagementPermission("CASE_MANAGEMENT_VIEW", DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id)
  val caseManagementInvestigate = new CaseManagementPermission("CASE_MANAGEMENT_INVESTIGATE", DashboardUserPermissions.CASE_MANAGEMENT_INVESTIGATE.id)
  val DocVCaseManagementReview = new CaseManagementPermission("DOCV_CASE_MANAGEMENT_REVIEW", DashboardUserPermissions.DOCV_CASE_MANAGEMENT_REVIEW.id)
  val DocVCaseManagementComment = new CaseManagementPermission("DOCV_CASE_MANAGEMENT_COMMENT", DashboardUserPermissions.DOCV_CASE_MANAGEMENT_COMMENT.id)
  val DocVCaseManagementView = new CaseManagementPermission("DOCV_CASE_MANAGEMENT_VIEW", DashboardUserPermissions.DOCV_CASE_MANAGEMENT_VIEW.id)

  val byPermissionName: String => Option[CaseManagementPermission] = byMember(_.name)
  val byPermissionId: Int => Option[CaseManagementPermission] = byMember(_.id)


  def getPermissionIdByName(permissionName: String): Option[Int] = CaseManagementPermissions.byPermissionName(permissionName).map(_.id)

  def getPermissionNameById(permissionId: Int): Option[String] = CaseManagementPermissions.byPermissionId(permissionId).map(_.name)

  def getAll: Set[CaseManagementPermission] = {
    values.toSet
  }
}