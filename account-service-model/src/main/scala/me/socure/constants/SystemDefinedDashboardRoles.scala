package me.socure.constants

object SystemDefinedDashboardRoles {

  lazy val accountOwner = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.ACCOUNTS_CREATE.id,
      DashboardUserPermissions.ACCOUNTS_MODIFY.id,
      DashboardUserPermissions.ACCOUNTS_DELETE.id,
      DashboardUserPermissions.ACCOUNTS_VIEW.id,

      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.USER_ROLES_CREATE.id,
      DashboardUserPermissions.USER_ROLES_MODIFY.id,
      DashboardUserPermissions.USER_ROLES_VIEW.id,
      DashboardUserPermissions.USER_ROLES_DELETE.id,

      DashboardUserPermissions.TEMPLATES_CREATE.id,
      DashboardUserPermissions.TEMPLATES_MODIFY.id,
      DashboardUserPermissions.TEMPLATES_VIEW.id,
      DashboardUserPermissions.TEMPLATES_DELETE.id,

      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id,

      DashboardUserPermissions.ACCOUNTS_PROVISION.id,
      DashboardUserPermissions.USERS_PROVISION.id,
      DashboardUserPermissions.TRANSACTIONS_PROVISION.id,
      DashboardUserPermissions.EVENT_MANAGERS_PROVISION.id,
      DashboardUserPermissions.BATCHJOB_PROVISION.id,
      DashboardUserPermissions.TEMPLATES_PROVISION.id,
      DashboardUserPermissions.USER_ROLES_PROVISION.id,
      DashboardUserPermissions.DOCUMENTATION_PROVISION.id,
      DashboardUserPermissions.OVERVIEW_PROVISION.id,
      DashboardUserPermissions.SETTINGS_PROVISION.id,
      DashboardUserPermissions.FILE_UPLOAD_PROVISION.id,
      DashboardUserPermissions.AUDIT_EXPORT_PROVISION.id,
      DashboardUserPermissions.EVENT_AUDIT_EXPORT_PROVISION.id,
      DashboardUserPermissions.ExportWlUserActionsProvision.id,

      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_VIEW.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_MODIFY.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_CREATE.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_DELETE.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.ANALYTICS_VIEW.id,

      DashboardUserPermissions.NEG_POS_LIST_VIEW.id,
      DashboardUserPermissions.NEG_POS_LIST_CREATE.id,
      DashboardUserPermissions.NEG_POS_LIST_EDIT.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    )
  )

  lazy val administrator = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.ACCOUNTS_CREATE.id,
      DashboardUserPermissions.ACCOUNTS_MODIFY.id,
      DashboardUserPermissions.ACCOUNTS_DELETE.id,
      DashboardUserPermissions.ACCOUNTS_VIEW.id,

      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.USER_ROLES_CREATE.id,
      DashboardUserPermissions.USER_ROLES_MODIFY.id,
      DashboardUserPermissions.USER_ROLES_VIEW.id,
      DashboardUserPermissions.USER_ROLES_DELETE.id,

      DashboardUserPermissions.TEMPLATES_CREATE.id,
      DashboardUserPermissions.TEMPLATES_MODIFY.id,
      DashboardUserPermissions.TEMPLATES_VIEW.id,
      DashboardUserPermissions.TEMPLATES_DELETE.id,

      DashboardUserPermissions.BATCHJOB_CREATE.id,
      DashboardUserPermissions.BATCHJOB_MODIFY.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.BATCHJOB_DELETE.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id,

      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_VIEW.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_MODIFY.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_CREATE.id,
      DashboardUserPermissions.DASHBOARD_IP_DOMAIN_DELETE.id,

      DashboardUserPermissions.AUDIT_EXPORT_PROVISION.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.ANALYTICS_VIEW.id,
      DashboardUserPermissions.FILE_UPLOAD_PROVISION.id,

      DashboardUserPermissions.NEG_POS_LIST_VIEW.id,
      DashboardUserPermissions.NEG_POS_LIST_CREATE.id,
      DashboardUserPermissions.NEG_POS_LIST_EDIT.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.OVERVIEW_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,
      DashboardUserPermissions.DECISION_LOGIC_DEPLOY.id
    )
  )

  lazy val analyst = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id,

      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,

      DashboardUserPermissions.NEG_POS_LIST_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_EDIT.id,
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    )
  )

  lazy val developer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.DOCUMENTATION_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_CREATE.id,
      DashboardUserPermissions.REPORTS_MODIFY.id,
      DashboardUserPermissions.REPORTS_VIEW.id,
      DashboardUserPermissions.REPORTS_DELETE.id,

      DashboardUserPermissions.EVENT_MANAGERS_CREATE.id,
      DashboardUserPermissions.EVENT_MANAGERS_MODIFY.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_DELETE.id,

      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CUSTOM_WATCHLIST_CREATE.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_MODIFY.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_VIEW.id,
      DashboardUserPermissions.CUSTOM_WATCHLIST_DELETE.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id
    )
  )

  lazy val caseAnalyst = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_VIEW.id,

      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id
    )
  )

  lazy val caseSupervisor = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.USER_ROLES_VIEW.id,

      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_APPROVE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_SCHEDULE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_APPROVE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_SCHEDULE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id
    )
  )

  lazy val caseOfficer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_CREATE.id,
      DashboardUserPermissions.USERS_MODIFY.id,
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USERS_DELETE.id,

      DashboardUserPermissions.USER_ROLES_VIEW.id,

      DashboardUserPermissions.BATCHJOB_VIEW.id,

      DashboardUserPermissions.DOCUMENTATION_VIEW.id,

      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.REPORTS_VIEW.id,

      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_SCHEDULE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_INVESTIGATE.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.CASE_MANAGEMENT_SCHEDULE.id,
      DashboardUserPermissions.CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.CASE_MANAGEMENT_VIEW.id,
      DashboardUserPermissions.CASE_MANAGEMENT_INVESTIGATE.id
    )
  )

  lazy val docVCaseAnalyst = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.BATCHJOB_VIEW.id,
      DashboardUserPermissions.DOCUMENTATION_VIEW.id,
      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_VIEW.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_REVIEW.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_COMMENT.id,
      DashboardUserPermissions.DOCV_CASE_MANAGEMENT_VIEW.id
    )
  )

  lazy val bsaOfficer = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.PII_ACCESS_VIEW.id,
      DashboardUserPermissions.ANALYTICS_VIEW.id,
      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,
      DashboardUserPermissions.AUDIT_EXPORT_PROVISION.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,

      DashboardUserPermissions.CHANGE_REQUESTS_VIEW.id,
      DashboardUserPermissions.CHANGE_REQUESTS_MODIFY.id,
      DashboardUserPermissions.CHANGE_REQUESTS_DELETE.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.DECISION_LOGIC_VIEW.id
    )
  )

  lazy val complianceAnalystRole = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.PII_ACCESS_VIEW.id,
      DashboardUserPermissions.ANALYTICS_VIEW.id,
      DashboardUserPermissions.REPORTING_VIEW.id,
      DashboardUserPermissions.REPORTING_CREATE.id,
      DashboardUserPermissions.AUDIT_EXPORT_PROVISION.id
    ),
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id,
      DashboardUserPermissions.DECISION_LOGIC_SIMULATE.id,

      DashboardUserPermissions.CHANGE_REQUESTS_VIEW.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,

      DashboardUserPermissions.SETTINGS_VIEW.id,

      DashboardUserPermissions.DECISION_LOGIC_VIEW.id
    )
  )

  lazy val integrationManager = Map(
    EnvironmentTypes.PRODUCTION_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.CHANGE_REQUESTS_VIEW.id,
      DashboardUserPermissions.CHANGE_REQUESTS_CREATE.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,
      DashboardUserPermissions.SETTINGS_MODIFY.id
    )
  )

  lazy val prospect = Map(
    EnvironmentTypes.GLOBAL_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.USERS_VIEW.id,
      DashboardUserPermissions.USER_ROLES_VIEW.id,
      DashboardUserPermissions.DOCUMENTATION_VIEW.id,
      DashboardUserPermissions.PII_ACCESS_VIEW.id
    ),
    EnvironmentTypes.SANDBOX_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.SETTINGS_MODIFY.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,
      DashboardUserPermissions.TRANSACTIONS_CREATE.id,
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id
    ),
    EnvironmentTypes.DEVELOPMENT_ENVIRONMENT.id -> Set(
      DashboardUserPermissions.TRANSACTIONS_VIEW.id,
      DashboardUserPermissions.SETTINGS_VIEW.id,
      DashboardUserPermissions.EVENT_MANAGERS_VIEW.id
    )
  )

}
