package me.socure.constants

import me.socure.model.account.ProductProvisioningTypes
import me.socure.model.account.{ApiKeyStatus, WatchlistCategories}
import me.socure.model.kyc.{KycAddressMatchLogic, KycNationalIdMatchLogic}
import me.socure.model.user.DashboardUserRole
import me.socure.model.{AccountStatus, ResponseStatus}
import me.socure.util.{AwsRegionKeySerializer, AwsRegionSerializer, ByteArraySerializer}
import org.json4s.ext.EnumNameSerializer
import org.json4s.{DefaultFormats, Formats}

/**
  * Created by gopal on 19/05/16.
  */
object JsonFormats {

  lazy val formats: Formats = {
    DefaultFormats +
      TimestampJodaDateTimeSerializer +
      new EnumNameSerializer(ResponseStatus) +
      new EnumNameSerializer(DashboardUserRole) +
      new EnumNameSerializer(ApiKeyStatus) +
      new EnumNameSerializer(EnvironmentConstants) +
      new EnumNameSerializer(WatchlistCategories) +
      new EnumNameSerializer(AccountStatus) +
      new EnumNameSerializer(ProductProvisioningTypes) +
      AwsRegionSerializer +
      ByteArraySerializer +
      AwsRegionKeySerializer +
      new EnumNameSerializer(KycNationalIdMatchLogic) +
      new EnumNameSerializer(KycAddressMatchLogic)
  }
}
