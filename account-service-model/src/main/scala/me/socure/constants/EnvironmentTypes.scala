package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object EnvironmentTypes extends Enum with ByM<PERSON>ber {
  type EnvironmentType = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value
  val GLOBAL_ENVIRONMENT = EnumVal(0, "Global")
  val PRODUCTION_ENVIRONMENT = EnumVal(1, "Production")
  val DEVELOPMENT_ENVIRONMENT = EnumVal(2, "Development")
  val SANDBOX_ENVIRONMENT = EnumVal(3, "Sandbox")

  val byId: Int => Option[EnvironmentType] = byMember(_.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def byEnvironmentTypeName(name: String): Option[EnvironmentType] = values.find(_.name.toLowerCase.contains(name.trim.toLowerCase))

  def byEnvironmentTypeId(id: Int): Option[EnvironmentType] = values.find(_.id == id)

  def byEnvironmentType(value: Value): Option[EnvironmentType] = values.find(_ == value)

  def getIdByValue(value: Value): Int = EnvironmentTypes.byEnvironmentType(value).map(_.id).get

  def getAll: Set[EnvironmentType] = {
    values.toSet
  }

  //TODO Need eliminate these kind of duplicate definitions
}
