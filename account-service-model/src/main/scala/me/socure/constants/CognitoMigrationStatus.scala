package me.socure.constants

import me.socure.types.scala.{ByMember, Enum}

object CognitoMigrationStatus extends Enum with ByMember {
  type CognitoMigrationStatus = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val Pending = new CognitoMigrationStatus(0, "Pending")
  val Migrated = new CognitoMigrationStatus(1, "Migrated")
  val Failed = new CognitoMigrationStatus(2, "Failed")
}