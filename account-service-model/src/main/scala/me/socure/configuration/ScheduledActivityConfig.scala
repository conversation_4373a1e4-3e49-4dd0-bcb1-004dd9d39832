package me.socure.configuration

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 06/04/2017.
  */

/**
  *
  * @param minInactiveDays Minimum number of days inactive for business User
  * @param registrationWaitDays Number of days to wait B<PERSON> should login in dashboard after registration
  */
case class ScheduledActivityConfig(minInactiveDays : Int,
                                   registrationWaitDays: Int,
                                   activationCodeTTL : Int,
                                   passwordResetTokenTTL: Int,
                                   passwordExpiryDays : Int)
