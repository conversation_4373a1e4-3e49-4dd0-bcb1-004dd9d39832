package me.socure.account.blacklist

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.{BlackListAccountInformation, Industry, Response}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike, Ignore}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 6/4/16.
  */
@Ignore
class BlackListServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec = ExecutionContext.global

  val engine = mock[BlackListService]
  val servlet = new BlackListServlet(engine)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(engine)
  }

  test("fetchIndustry should return account not found") {

    Mockito.when(engine.fetchIndustry(1)).thenReturn(Future.successful(Left(ErrorResponseFactory.get(AccountNotFound))))
    get("/fetch_industry/1") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":100,"message":"The account does not exist"}}"""
    }
  }

  test("fetchIndustry should return properly when an exception is thrown") {

    Mockito.when(engine.fetchIndustry(1)).thenReturn(Future.failed(new Exception("a message")))
    get("/fetch_industry/1") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":199,"message":"a message"}}"""
    }
  }

  test("fetchIndustry should return properly whith success") {

    val industry = Industry("sector", "description")

    Mockito.when(engine.fetchIndustry(1)).thenReturn(Future.successful(Right(industry)))
    get("/fetch_industry/1") {
      status should equal(200)
      body.decodeJson[Response[Industry]].data shouldBe industry
    }
  }

  test("fetchAccountInformation should return account not found") {

    Mockito.when(engine.fetchByApiKey("1")).thenReturn(Future.successful(Left(ErrorResponseFactory.get(AccountNotFound))))
    get("/fetch_account_information/1") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":100,"message":"The account does not exist"}}"""
    }
  }

  test("fetchAccountInformation should return properly when an exception is thrown") {

    Mockito.when(engine.fetchByApiKey("1")).thenReturn(Future.failed(new Exception("a message")))
    get("/fetch_account_information/1") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":199,"message":"a message"}}"""
    }
  }

  test("fetchAccountInformation should return properly whith success") {

    val accountInformation = BlackListAccountInformation(1)

    Mockito.when(engine.fetchByApiKey("1")).thenReturn(Future.successful(Right(accountInformation)))
    get("/fetch_account_information/1") {
      status should equal(200)
      body.decodeJson[Response[BlackListAccountInformation]].data shouldBe accountInformation
    }
  }
}