package me.socure.account.blacklist

import javax.sql.DataSource
import com.mchange.v2.c3p0.ComboPooledDataSource
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.model.{BlackListAccountInformation, Industry}
import me.socure.utils.DBProxyWithMetrics
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, FunSuite, Ignore, Matchers}
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.ExecutionContext

@Ignore
class BlackListServiceTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))


  implicit val ec = ExecutionContext.global

  private val mysqlService: MysqlService = MysqlService("account-blacklist-service")


  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint()
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {

    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas("socure")
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildEngine(dataSource: DataSource) = {

    val db = JdbcBackend.Database.forDataSource(dataSource)
    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )

    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    new BlackListService(dbProxyWithMetrics, slick.driver.MySQLDriver)
  }

  override def beforeAll() {

    val dataSource = buildDataSource()
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(buildDataSource())

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('industry sector', 'industry description')")
    sqlExecutor.execute(s"INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key, external_id) VALUES (1, 'accountName', 'industry sector', false, true, NULL, true, '${PublicIdGenerator.account().value}', 'publicApiKey1','externalId1')")
    sqlExecutor.execute("INSERT INTO tbl_business_user (id, email, registered_on, contact_number, first_name, last_name, is_primary_user, account_id) VALUES(1, '<EMAIL>', '2016-05-05', 'contact number', 'first name', 'last name', true, 1)")
    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES(1, 'Production')")
    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id) VALUES(1, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 1, 1)")

    //Api Keys
    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 1, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 1, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(3, 1, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(4, 1, 'api-key2', 'deprecated', '2017-03-06 20:11:22', '2017-03-06 20:11:22');")
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop()
  }

  test("should return left when no entry exist") {
    val engine = buildEngine(buildDataSource())

    val actual = engine.fetchByApiKey("wrong-api-key")
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(a => a.code shouldBe 100, _ => fail)
    }
  }

  test("should return correct object when it exists") {
    val dataSource = buildDataSource()
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("99-16ca6193-4149-456b-ae00-00fdad2437c6")

    whenReady(actual) { response =>
      response shouldBe 'right
      val expected = BlackListAccountInformation(
        accountId = 1L
      )

      response.fold(_ => fail, a => a shouldBe expected)
    }
  }

  test("fetch industry should return left when no account") {
    val engine = buildEngine(buildDataSource())

    val actual = engine.fetchIndustry(2L)
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(a => a.code shouldBe 100, _ => fail)
    }
  }

  test("fetch industry should return the industry when the account exists") {
    val engine = buildEngine(buildDataSource())

    val actual = engine.fetchIndustry(1L)
    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, a =>  a shouldBe Industry("industry sector", "industry description"))
    }
  }
}
