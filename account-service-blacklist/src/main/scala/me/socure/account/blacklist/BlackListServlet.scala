package me.socure.account.blacklist

import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext

/**
  * Created by alexand<PERSON> on 6/4/16.
  */
class BlackListServlet(service: BlackListService)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport{

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  private val logger = LoggerFactory.getLogger(classOf[BlackListServlet])

  private val metrics: Metrics = JavaMetricsFactory.get("account-service." + classOf[BlackListServlet].getSimpleName)

  get("/fetch_account_information/:apiKey") {
    val futureResponse = metrics.timeFuture("fetchAccountInformation.duration") {
      logger.info("Fetching data per API Key")
      val apiKey = params("apiKey")

      service.fetchByApiKey(apiKey)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for apiKey", e)
        metrics.increment("fetchAccountInformation.exception", "class:" + e.getClass.getSimpleName)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/fetch_industry/:accountId") {
    val futureResponse = metrics.timeFuture("fetchIndustry.duration") {
      logger.info("Fetching data per API Key")
      val accountId = params("accountId").toLong

      service.fetchIndustry(accountId)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for apiKey", e)
        metrics.increment("fetchIndustry.exception", "class:" + e.getClass.getSimpleName)
    }

    ScalatraResponseFactory.get(futureResponse)
  }
}
