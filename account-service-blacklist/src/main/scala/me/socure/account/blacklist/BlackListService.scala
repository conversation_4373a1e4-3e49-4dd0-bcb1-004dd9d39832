package me.socure.account.blacklist

import me.socure.convertors.AccountConvertors
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.account.ApiKeyStatus
import me.socure.model.{BlackListAccountInformation, ErrorResponse, Industry}
import me.socure.storage.slick.tables.account.{DaoTblAccount, DaoTblApiKey, DaoTblEnvironment}
import me.socure.storage.slick.tables.industry.DaoTblIndustry
import slick.driver.JdbcProfile
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 4/28/16.
  */
class BlackListService(val dbProxyWithMetrics: DBProxyWithMetrics, val profile: JdbcProfile)(implicit ec: ExecutionContext)
    extends DaoTblAccount
    with DaoTblIndustry
    with DaoTblEnvironment
    with DaoTblApiKey{

  import profile.api._

  def fetchIndustry(accountId: Long): Future[Either[ErrorResponse, Industry]] = {

    val query = for {
      account <- TblAccounts.filter(_.id === accountId)
      industry <- TableQuery[TblIndustries] if industry.sector === account.industrySector
    } yield {
      industry
    }

    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblIndustry), DBActions.Select, "fetchIndustry").map(_.headOption match {
      case Some(industry) => Right(AccountConvertors.getIndustry(industry))
      case None => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
    })
  }

  def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, BlackListAccountInformation]] = {

    val query = TblApiKey.filter(a => {a.apiKey === apiKey && a.status != ApiKeyStatus.DEPRECATED}) join TblEnvironment on(_.environmentId === _.id)

    dbProxyWithMetrics.run(query.result, DBTables.TblApiKey, DBActions.Select, "fetchByApiKey").map(_.headOption match {
      case None =>
        Left(
          ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      case Some(list) =>
        Right(
          BlackListAccountInformation(
            accountId = list._2.accountId
          )
        )
    })
  }
}
