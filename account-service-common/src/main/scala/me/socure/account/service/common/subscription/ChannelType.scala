package me.socure.account.service.common.subscription

import me.socure.types.scala.{ByMember, Enum}

object ChannelType extends Enum with ByMember {
  type ChannelType = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val PRIMARY = new ChannelType(1, "Primary")
  val SECONDARY = new ChannelType(2, "Secondary")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(name: String): Boolean = values.exists(_.toString.toLowerCase == name.toLowerCase)
  val byId: Int => Option[ChannelType] = byMember(_.id)
}
