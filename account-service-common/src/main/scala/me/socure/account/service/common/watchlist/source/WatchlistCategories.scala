package me.socure.account.service.common.watchlist.source

import me.socure.types.scala.{ByMember, Enum}
object WatchlistCategories extends Enum with ByMember {
  type WatchlistCategories = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val Sanctions: WatchlistCategories.Value = new WatchlistCategories(1, "Sanctions")
  val Enforcement: WatchlistCategories.Value = new WatchlistCategories(2, "Enforcement")
  val PEP: WatchlistCategories.Value = new WatchlistCategories(3, "PEP")
  val AdverseMedia: WatchlistCategories.Value = new WatchlistCategories(4, "Adverse Media")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(value: String): Boolean = values.exists(_.toString.toLowerCase == value.toLowerCase)
  val byId: Int => Option[WatchlistCategories] = byMember(_.id)

}
