package me.socure.account.service.common.watchlist

import me.socure.account.service.common.CacheKeyProvider

object ManageCAAccountsCacheKeyProvider {

  def provideForGetCAWatchList(environmentId: Long): String =
    CacheKeyProvider.provide(s"managecaa_watchListPreference_environment_$environmentId")

  def provideForGetWatchListSource(environmentId: Long): String =
    CacheKeyProvider.provide(s"managecaa_watchListSources_environment_$environmentId")

}
