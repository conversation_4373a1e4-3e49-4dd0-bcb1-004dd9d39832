package me.socure.account.service.common.watchlist.source

import me.socure.account.service.common.watchlist.source.WatchlistCategories.{WatchlistCategories, byMember}
import me.socure.types.scala.{ByMember, Enum}
object WatchlistSubCategories  extends Enum with ByM<PERSON>ber {
  type WatchlistSubCategories = EnumVal
  sealed case class EnumVal(id: Int, name: String) extends Value

  val Warnings: WatchlistSubCategories.Value = new WatchlistSubCategories(1, "Warnings")
  val Sanctions: WatchlistSubCategories.Value = new WatchlistSubCategories(2, "Sanctions")
  val PEPClass1: WatchlistSubCategories.Value = new WatchlistSubCategories(3, "PEP Class 1")
  val PEPClass2: WatchlistSubCategories.Value = new WatchlistSubCategories(4, "PEP Class 2")
  val PEPClass3: WatchlistSubCategories.Value = new WatchlistSubCategories(5, "PEP Class 3")
  val PEPClass4: WatchlistSubCategories.Value = new WatchlistSubCategories(6, "PEP Class 4")
  val FitnessProbity: WatchlistSubCategories.Value = new WatchlistSubCategories(7, "Fitness Probity")
  val AdverseMedia: WatchlistSubCategories.Value = new WatchlistSubCategories(8, "Adverse Media")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(value: String): Boolean = values.exists(_.toString.toLowerCase == value.toLowerCase)
  val byId: Int => Option[WatchlistSubCategories] = byMember(_.id)

}
