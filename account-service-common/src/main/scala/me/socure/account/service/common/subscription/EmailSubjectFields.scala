package me.socure.account.service.common.subscription

import me.socure.types.scala.{ByM<PERSON>ber, Enum}

object EmailSubjectFields extends Enum with ByM<PERSON>ber {
  type EmailSubjectFields = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val REFERENCE_ID = new EmailSubjectFields(1, "Reference Id")
  val ENVIRONMENT_ID = new EmailSubjectFields(2, "Environment Id")
  val SUBSCRIPTION_TYPE = new EmailSubjectFields(3, "Subscription Type")

  def isValid(id: Int): Boolean = values.exists(_.id == id)

  def isValid(name: String): Boolean = values.exists(_.toString.toLowerCase == name.toLowerCase)

  val byId: Int => Option[EmailSubjectFields] = byMember(_.id)

  def getActionByName(name: String) : Option[EmailSubjectFields] = {
    values.find(_.name==name) match {
      case e if e.isDefined => Some(e.get)
      case _ => None
    }
  }
}
