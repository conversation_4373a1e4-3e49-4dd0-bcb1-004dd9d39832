package me.socure.account.service.common.watchlist

import me.socure.types.scala.{ByMember, Enum}

object HistoricalRange extends Enum with ByMember {
  type HistoricalRange = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val SixMonths: HistoricalRange.Value = new HistoricalRange(1, "6 Months")
  val OneYear: HistoricalRange.Value = new HistoricalRange(2, "1 Year")
  val TwoYears: HistoricalRange.Value = new HistoricalRange(3, "2 Years")
  val ThreeYears: HistoricalRange.Value = new HistoricalRange(4, "3 Years")
  val FourYears: HistoricalRange.Value = new HistoricalRange(5, "4 Years")
  val FiveYears: HistoricalRange.Value = new HistoricalRange(6, "5 Years")
  val SixYears: HistoricalRange.Value = new HistoricalRange(7, "6 Years")
  val SevenYears: HistoricalRange.Value = new HistoricalRange(8, "7 Years")
  val TenYears: HistoricalRange.Value = new HistoricalRange(9, "10 Years")
  val AllTime: HistoricalRange.Value = new HistoricalRange(10, "All Time")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(value: String): Boolean = values.exists(_.toString.toLowerCase == value.toLowerCase)
  val byId: Int => Option[HistoricalRange] = byMember(_.id)
}

