package me.socure.account.service.common.subscription

import me.socure.types.scala.{ByMember, Enum}

object ChannelAction extends Enum with ByM<PERSON>ber {
  type ChannelAction = EnumVal

  sealed case class EnumVal(id: Int, name: String , statusId: Int) extends Value

  val ENABLE = new ChannelAction(1, "ENABLE",SubscriptionChannelRegistryStatus.ACTIVE.id)
  val SUSPEND = new ChannelAction(2, "SUSPEND",SubscriptionChannelRegistryStatus.SUSPENDED.id)
  val DISABLE = new ChannelAction(3, "DISABLE",SubscriptionChannelRegistryStatus.DISABLED.id)
  val DELETE = new ChannelAction(4, "DELETE",SubscriptionChannelRegistryStatus.DELETED.id)

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(name: String): Boolean = values.exists(_.toString.toLowerCase == name.toLowerCase)
  val byId: Int => Option[ChannelAction] = byMember(_.id)

  def getActionByName(name: String) : Option[ChannelAction] = {
     values.find(_.name==name) match {
       case e if e.isDefined => Some(e.get)
       case _ => None
     }
  }
}
