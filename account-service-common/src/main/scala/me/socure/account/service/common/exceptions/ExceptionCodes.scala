package me.socure.account.service.common.exceptions

/**
  * Created by <PERSON><PERSON><PERSON> on 5/10/16.
  */
object ExceptionCodes extends Enumeration {
  type ExceptionCodes = EnumVal

  sealed class EnumVal(val id : Int, val code : String, val description : String) extends Value

  val AccountNotFound = new EnumVal(100, "AccountNotFound", "The account does not exist")
  val DomainUpdateFailed = new EnumVal(101, "DomainNotUpdated", "Account Domain update not successful")
  val APIKeyGenrationFailed = new EnumVal(102, "APIKeyNotGenerated", "Account key generation failed")
  val DeleteIndividualCacheFaild = new EnumVal(103, "IndividualCacheNotDeleted", "Individual Cache could not be removed")
  val DeleteAccountCacheFailed = new EnumVal(104, "AccountCacheNotDeleted", "Account Cache could not be removed")
  val UpdateAccountCacheFailed = new EnumVal(105, "AccountCacheNotUpdated", "Account Cache could is not updated")
  val UpdateAccountIndividualCacheFailed = new EnumVal(106, "AccountIndividualCacheNotUpdated", "Account Invidiual Cache could is not updated")
  val DeleteSocialNetworkKeyFailed = new EnumVal(107, "SocialKeyNotDeleted", "Social netowrk keys delete not successful")
  val UpdateSocialNetworkKeyFailed = new EnumVal(108, "SocialKeyNotUpdated", "Social netowrk keys update not successful")
  val IndividualCacheIdNotFound = new EnumVal(109, "IndividualCacheIdNotFound", "Individual Cache Id not found")
  val SocialKeyIdNotFound = new EnumVal(110, "SocialKeyIdNotFound", "Social network key Id not found")
  val RegistrationFailed = new EnumVal(111, "Registrationfailed", "Registration is failed")
  val UsernamePasswordMismatch = new EnumVal(112, "UsernamePasswordMismatch", "Username and password does not match")
  val UserNotFound = new EnumVal(113, "UserNotFound", "User not found")
  val InvalidActivationCode = new EnumVal(114, "InvalidActivationCode", "Activation code is invalid")
  val InvalidInputFormat = new EnumVal(115, "InvalidInputFormat", "Invalid input format exception")
  val BusinessUserNotFound = new EnumVal(116, "BusinessUserNotFound", "Business user not found")
  val RecordsNotFound = new EnumVal(116, "RecordsNotFound", "Records are not found")
  val CurrentPasswordMismatch = new EnumVal(117, "CurrentPasswordMismatch", "Current password is incorrect")

  //Request Handling
  val NoHandlerFound = new EnumVal(118, "NotFound", "No handler found")
  val InternalError = new EnumVal(119, "InternalError", "Internal Error")

  //MultiAccount
  val SubAccountNotFound = new EnumVal(120, "SubAccountNotFound", "Sub account not found")
  val AccessForbidden = new EnumVal(121,"AccessForbidded","Access Forbidden")
  val EnvironmentNotFound = new EnumVal(122, "EnvironmentNotFound", "Environment not found")
  val PrimaryBusinessUserDeletion = new EnumVal(123, "PrimaryBusinessUserDeletion", "Cant delete primary business user")
  val RolesNotFound = new EnumVal(124, "RolesNotFound", "Business user has no roles")
  val MissingRequiredParameters = new EnumVal(125, "MissingRequiredParameters", "Missing required parameters")
  val AccountsAssociatedWithIndustry = new EnumVal(126, "IndustryCantbeDeleted", "Industry is referenced by Accounts")
  val IndustryNotFound = new EnumVal(127, "IndustryNotFound", "Industry not found")
  val UserExistsAlready = new EnumVal(128, "UserExistsAlready", "User exists already")
  val AccountDeleted = new EnumVal(129, "AccountDeleted", "The account deleted")
  val PasswordPolicy = new EnumVal(130, "PasswordPolicy", "Password constraints not fulfilled")
  val UserNotActivated = new EnumVal(132, "UserActivationFailed", "No user is activated")
  val AccountAttributeAddFailed = new EnumVal(133, "AccountAttributeAddFailed", "Unable to add account attribute")
  val AccountAlreadyExists = new EnumVal(134, "AccountAlreadyExists", "Account already exists")
  val ParentAccountNotFound = new EnumVal(135, "ParentAccountNotFound", "The parent account does not exist")
  val DomainNotValid = new EnumVal(136, "DomainNotValid", "Domain not valid")
  val RecaptchaNotFound = new EnumVal(137, "RecaptchaNotFound", "Recaptcha not found")
  val EmailAlreadyExists = new EnumVal(138, "EmailAlreadyExists", "Email already exists")
  val EmailDomainAlreadyExists = new EnumVal(139, "EmailDomainAlreadyExists", "Email Domain already exists")
  val PrimaryBusinessUserRoleRemoval = new EnumVal(598, "PrimaryBusinessUserRoleRemoval", "Cant remove primary business user role")
  val ModifyPrimaryUserRoleError = new EnumVal(599, "ModifyPrimaryUserRoleError", "Error while modifying primary user role")
  val ModelMappingFailed = new EnumVal(601, "ModelMappingFailed", "Account created successfully but failed to map default models")
  val AccountCreationFailed = new EnumVal(602, "AccountCreationFailed", "Account Creation Failed")
  val EncryptionKeyGenerationWhenAccountCreationFailed = new EnumVal(603, "EncryptionKeyGenerationWhenAccountCreationFailed", "Failed to create account due to encryption key generation failure")
  val EncryptionKeyGenerationAndRevertAccountFailed = new EnumVal(604, "RevertCreatedAccountFailed", "Failed to generate encryption key for the account. Please contact the Engg. team")
  val EncryptionKeyExists = new EnumVal(605, "EncryptionKeyExists", "Encryption key exists for the account")

  //ApiKey Renewal
  val ApiKeyCannotBeRenewed = new EnumVal(150, "ApiKeyCannotBeRenewed", "ApiKey cannot be renewed")
  val ApiKeyNotInserted = new EnumVal(151, "ApiKeyNotInserted", "ApiKey not inserted")
  val ApiKeyStatusNotchanged = new EnumVal(152, "ApiKeyStatusNotchanged", "ApiKey status not changed")
  val DeprecateApiKeysFailed = new EnumVal(153, "DeprecateApiKeysFailed", "Deprecate Api Keys Failed")
  val PasswordExpired = new EnumVal(154, "PasswordExpired", "Password got expired")
  val PasswordSizeConstraint = new EnumVal(155, "PasswordSizeConstraint", "Password should be between 8 and 50 characters long")
  val PasswordContainsPersonalInfo = new EnumVal(156, "PasswordContainsPersonalInfo", "Password should not contain your first name, surname, or portion of your email address")
  val PasswordCharsConstraint = new EnumVal(157, "PasswordCharsConstraint", "Password should have at least one uppercase, one lowercase letter, one number and one special character (such as !,#,*, etc.)")
  val PasswordAlreadyUsed = new EnumVal(158, "PasswordAlreadyUsed", "Password should not have been used as one of the last 8 passwords")
  val DomainNotAuthorized = new EnumVal(159, "DomainNotAuthorized", "Dashboard domain not authorized")
  val NotFirstActivation = new EnumVal(160, "NotFirstActivation", "It is not first activation")
  val UnknownRole = new EnumVal(161, "UnknownRole", "Unknown role")
  val NoPGPPublicKey = new EnumVal(162, "NoPGPPublicKey", "PGP Public key is not generated")
  val PGPExistsAlready = new EnumVal(163, "PGPExistsAlready", "PGP exists already")
  val DeactivatePGPKeysFailed = new EnumVal(164, "DeactivatePGPKeysFailed", "Deactivating PGP keys failed")
  val NoPGPKeys = new EnumVal(165, "NoPGPKeys", "No valid PGP keys exists for the account")
  val PGPSignaturePublicKeyExists = new EnumVal(166, "PGPSignaturePublicKeyExists", "PGP Signature Public Key exists")
  val NoPGPSignaturePublicKeyFound = new EnumVal(167, "NoPGPSignaturePublicKeyFound", "No PGP Signature Public Key Found")
  val InvlidPGPSignaturePublicKey = new EnumVal(168, "InvlidPGPSignaturePublicKey", "Invalid PGP Signature Public Key")
  val IdpMetadataAlreadyExists = new EnumVal(169, "IdpMetadataAlreadyExists", "Idp metadata exists")
  val NoIdpMetadataFound = new EnumVal(170, "NoIdpMetadataFound", "No Idp Metadata Found")
  val InvalidIdpMetadata = new EnumVal(171, "InvalidIdpMetadata", "Invalid Idp Metadata")
  val NotSupportedForSAMLEnabledAccounts = new EnumVal(172, "NotSupportedForSAMLEnabledAccounts", "Not supported for SAML 2.0 enabled accounts")
  val SAMLNotEnabled = new EnumVal(173, "SAMLNotEnabled", "SAML 2.0 must be enabled")
  val MetadataDoesNotMatch = new EnumVal(174, "MetadataDoesNotMatch", "Metadata does not match")
  val DuplicateIdpMetadata = new EnumVal(175, "DuplicateIdpMetadata", "IDP Metadata with entityID already exists in DB")
  val WlMatchScoreOutOfRange = new EnumVal(176, "WlMatchScoreOutOfRange", "Watchlist Match Score should be with 70-100")
  val CAWlMatchScoreNotInRange = new EnumVal(180, "CAWlMatchScoreNotInRange", "Watchlist Match Score should be among [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1]")
  val CAWlLimitOutOfRange = new EnumVal(181, "CAWlLimitOutOfRange", "Watchlist limit should be with 1-100")
  val NotAnInternalAccount = new EnumVal(182, "NotAnInternalAccount", "Not an Internal account")
  val NoPublicWebhookFound = new EnumVal(170, "NoPublicWebhookFound", "No public webhook found")

  // Public API key
  val PublicApiKeyNotInserted = new EnumVal(176, "PublicApiKeyNotInserted", "PublicApiKey not inserted")
  val PublicApiKeyCannotBeRenewed = new EnumVal(177, "PublicApiKeyCannotBeRenewed", "PublicApiKey cannot be renewed")
  val PublicApiKeyStatusNotchanged = new EnumVal(178, "PublicApiKeyStatusNotchanged", "PublicApiKey status not changed")
  val ApiKeyFetchFailed = new EnumVal(179, "ApiKeyFetchFailed", "ApiKeyFetchFailed")

  // Invalid Parameters passed
  val InvalidParametersPassed = new EnumVal(180,"InvalidParametersPassed", "Invalid Parameters passed")
  val InvalidPhoneNumber = new EnumVal(181,"InvalidPhoneNumber", "Phone field must be a number")
  val PublicApiKeyAccountFetchFailed = new EnumVal(182, "PublicApiKeyAccountFetchFailed", "PublicApiKeyAccountFetchFailed")
  val InvalidWatchlistCategory = new EnumVal(183, "InvalidWatchlistCategory", "Invalid Watchlist Category")
  val InvalidWatchlistSubCategory = new EnumVal(184, "InvalidWatchlistSubCategory", "Invalid Watchlist Sub-Category")
  val UnableToIncludeWatchlistSource = new EnumVal(185, "UnableToIncludeWatchlistSource", "Unable To Include Watchlist Source")
  val UnableToExcludeWatchlistSource = new EnumVal(186, "UnableToExcludeWatchlistSource", "Unable To Exclude Watchlist Source")
  val UnableToCreatePublicWebhook = new EnumVal(187, "UnableToCreatePublicWebhook", "Unable To create public webhook")
  val UnableToUpdatePublicWebhook = new EnumVal(188, "UnableToUpdatePublicWebhook", "Unable To update public webhook")
  val UnableToDeletePublicWebhook = new EnumVal(189, "UnableToDeletePublicWebhook", "Unable To delete public webhook")
  val WebhookAlreadyExist = new EnumVal(190, "WebhookAlreadyExist", "Webhook already exist")
  val InvalidSubscriptionType = new EnumVal(191, "InvalidSubscriptionType", "Invalid Subscription Type")
  val DocumentVerificationNotProvisioned = new EnumVal(192, "DocumentVerificationNotProvisioned", "Document Verification Not Provisioned")
  val CannotRemoveAuthenticIDV2 = new EnumVal(193, "CannotRemoveAuthenticIDV2", "Cannot remove AuthenticID V2 permission")
  val cannotRemoveAuthenticIDV2Production = new EnumVal(199, "CannotRemoveAuthenticIDV2Production", "Cannot remove AuthenticID V2 Production Environment permission")

  val CouldNotFindDvConfiguration: EnumVal = new EnumVal(194, "CouldNotFindDvConfiguration", "Could not find DV configuration")
  val InvalidConfigIdPassed: EnumVal = new EnumVal(195, "InvalidConfigIdPassed", "Invalid config id passed")
  val CouldNotSaveDvConfiguration: EnumVal = new EnumVal(196, "CouldNotSaveDvConfiguration", "Could not save dv configuration")
  val CouldNotUpdateDvConfiguration: EnumVal = new EnumVal(197, "CouldNotUpdateDvConfiguration", "Could not update dv configuration")
  val CouldNotFetchDvConfiguration: EnumVal = new EnumVal(198, "CouldNotFetchDvConfiguration", "Could not fetch dv configuration")

  val UnknownError = new EnumVal(199, "UnknownError", "Something went wrong")
  val InvalidAccountsRoles = new EnumVal(201,"InvalidAccountsRoles", "Accounts Roles Association must be non empty")
  val InvalidAccountRoleAssociation = new EnumVal(202,"InvalidAccountAssociation", "Invalid Accounts Roles Association")

  val InvalidParentAccount = new EnumVal(401, "InvalidParentAccount", "Account is not of Parent type or is deleted")
  val PrimaryUserAlreadyPresent = new EnumVal(402, "PrimaryUserAlreadyPresent", "Account already has primary user")
  val InvalidPublicApiKey = new EnumVal(403, "InvalidPublicApiKey", "Invalid Public ApiKey")
  val InvalidRequestPayload = new EnumVal(404, "InvalidRequestPayload", "Invalid Request payload")
  val InvalidAccountType = new EnumVal(405, "InvalidAccountType", "Invalid account type")

  val ParentPermissionMissing = new EnumVal(500, "ParentPermissionMissing", "Parent permission is not enabled")
  val OperationNotSupported = new EnumVal(501, "OperationNotSupported", "Operation not supported")
  val InvalidRoleProvisioning = new EnumVal(502, "InvalidRoleProvisioning", "Invalid Role Provisioning")
  val PrivateApiKeyAccountFetchFailed = new EnumVal(503, "PrivateApiKeyAccountFetchFailed", "PrivateApiKeyAccountFetchFailed")
  val SecretKeyEmptyOrUndefined = new EnumVal(504, "EmptyOrUndefinedSecretKey","Secret key is not updated or secret key has empty value")
  val SecretKeyListError = new EnumVal(505,"SecretKeysError","Error occured while fetching secret keys")
  val UserNotCreated = new EnumVal(506,"UserNotCreated","Failed to create user")

  val UserRoleNotFound = new EnumVal(507, "UserRoleNotFound", "User Role not found")
  val UnableToUpdateUserRole = new EnumVal(508, "UnableToUpdateUserRole", "Unable to update user role")
  val UnableToDeleteUserRole = new EnumVal(509, "UnableToDeleteUserRole", "Unable to delete user role")
  val UserRolesFetchError = new EnumVal(510, "UserRolesFetchError", "Error occurred while fetching user roles")
  val UserAccountAssociationsFetchError = new EnumVal(511, "UserAccountAssociationsFetchError", "Error occurred while fetching user account associations")
  val UnableToUpdateUserAccountAssociation = new EnumVal(512, "UnableToUpdateUserAccountAssociation", "Unable to update user account association")
  val UnableToInsertUserAccountAssociation = new EnumVal(513, "UnableToInsertUserAccountAssociation", "Unable to insert user account association")
  val UnableToDeleteUserAccountAssociation = new EnumVal(514, "UnableToDeleteUserAccountAssociation", "Unable to delete user account association")
  val PermissionTemplateMappingsFetchError = new EnumVal(515, "PermissionTemplateMappingsFetchError", "Error occurred while fetching permission template mappings")
  val UnableToUpdatePermissionTemplateMapping = new EnumVal(516, "UnableToUpdatePermissionTemplateMapping", "Unable to update permission template mapping")
  val UnableToInsertPermissionTemplateMapping = new EnumVal(517, "UnableToInsertPermissionTemplateMapping", "Unable to insert permission template mapping")
  val UnableToDeletePermissionTemplateMapping = new EnumVal(518, "UnableToDeletePermissionTemplateMapping", "Unable to delete permission template mapping")
  val InvalidPermissionIdProvided = new EnumVal(519, "InvalidPermissionIdProvided", "Invalid permission id provided")
  val InvalidEnvironmentTypeProvided = new EnumVal(520, "InvalidEnvironmentTypeProvided", "Invalid environment type provided")
  val AccountHierarchyNotFound = new EnumVal(521, "AccountHierarchyNotFound", "Account Hierarchy not found")
  val AccountHierarchyByAccountNotFound = new EnumVal(522, "AccountHierarchyByAccountNotFound", "Account Hierarchy not found for account")
  val AccountHierarchyByAccountFetchError = new EnumVal(523, "AccountHierarchyByAccountFetchError", "Error occurred while fetching account hierarchy")
  val UnableToInsertAccountHierarchy = new EnumVal(524, "UnableToInsertAccountHierarchy", "Unable to insert account hierarchy")
  val UnableToUpdateAccountHierarchy = new EnumVal(525, "UnableToUpdateAccountHierarchy", "Unable to update account hierarchy")
  val SubAccountFetchError = new EnumVal(526, "SubAccountFetchError", "Error occurred while fetching sub accounts")
  val PermissionTemplateCreateFailed = new EnumVal(527, "PermissionTemplateCreateFailed", "Permission Template not Created")
  val PermissionTemplateUpdateFailed = new EnumVal(528, "PermissionTemplateUpdateFailed", "Permission Template not Updated")
  val PermissionTemplateFetchFailed = new EnumVal(529, "PermissionTemplateFetchFailed", "Fetch Permission Template failed")
  val PermissionTemplateDeleteFailed = new EnumVal(530, "PermissionTemplateDeleteFailed", "Delete Permission Template failed")
  val UnableToListPermissions = new EnumVal(531, "UnableToListPermissions", "Error occurred while listing permissions")
  val UnableToFetchPermissionTemplate = new EnumVal(532, "UnableToFetchPermissionTemplate", "Error occurred while fetching permission template")
  val UnableToUpsertRolePermissionTemplateAssociation = new EnumVal(533, "UnableToUpsertRolePermissionTemplateAssociation", "Could not insert or update role permission template association")
  val UnableToDeleteRolePermissionTemplateAssociation = new EnumVal(534, "UnableToDeleteRolePermissionTemplateAssociation", "Could not delete role permission template association")
  val AccountAssociationHistoryNotFound = new EnumVal(535, "AccountAssociationHistoryNotFound", "Account Association History Not Found")
  val UnableToInsertAccountAssociationHistory = new EnumVal(536, "UnableToInsertAccountAssociationHistory", "Unable to insert account hierarchy history")
  val UnableToUpdateAccountAssociationHistory = new EnumVal(537, "UnableToUpdateAccountAssociationHistory", "Unable to update account hierarchy history")
  val CreatorDetailsNotAvailable = new EnumVal(538, "CreatorDetailsNotAvailable", "Creator details not available")
  val CouldNotFindBusinessUser = new EnumVal(539, "CouldNotFindBusinessUser","Invalid user details provided")
  val InvalidPermissions = new EnumVal(540, "InvalidPermissions", "Invalid Permissions")
  val NoUserAccountAssociation = new EnumVal(541, "NoUserAccountAssociation", "User account association doesn't exist")
  val UnableToValidateUserAccountAssociation = new EnumVal(542, "UnableToValidateUserAccountAssociation", "Error occurred while validating user account association")
  val SubscriptionChannelRegistryNotFound = new EnumVal(543, "SubscriptionChannelRegistryNotFound", "No Subscription Channel Registry Found")
  val SubscriptionChannelRegistryAlreadyExists = new EnumVal(544, "SubscriptionChannelRegistryAlreadyExists", "Subscription Channel Registry Already Exists")
  val SubscriptionChannelRegistryUpdateFailed = new EnumVal(545, "SubscriptionChannelRegistryUpdateFailed", "Unable to update Subscription Channel Registry. Check your request and try again")
  val SubscriptionChannelRegistryNotDeleted = new EnumVal(id = 546, "SubscriptionChannelRegistryNotDeleted", "Unable to delete. Subscription Channel Registry doesn't exist or is already deleted")
  val SubscriptionChannelStatusNotUpdated = new EnumVal(id = 547, "SubscriptionChannelStatusNotUpdated", "Unable to change Subscription Channel Status. Subscription Channel Registry doesn't exist or is already deleted")
  val SubscriptionChannelInvalidAction = new EnumVal(id = 548, "SubscriptionChannelInvalidAction", "Unable to change Subscription Channel Status. Invalid Action!")
  val SubscriptionChannelInvalidActionStatusMapping = new EnumVal(id = 549, "SubscriptionChannelInvalidActionStatusMapping", "Unable to change Subscription Channel Status!. Invalid Action , Status Mapping!")
  val PrimaryAdminLimitExceeded = new EnumVal(id = 550, "PrimaryAdminLimitExceeded", "Primary Admins limit exceeded. Contact Administrator")
  val AdministerSubAccountsNotProvisioned = new EnumVal(id = 551, "AdministerSubAccountsNotProvisioned", "Administer Sub Accounts not provisioned for the Account")
  val InvalidParentAccountType = new EnumVal(id = 552, "InvalidParentAccountType", "Invalid Parent account type")
  val AdminsterNotProvisioned = new EnumVal(id = 553, "AdminsterNotProvisioned", "Adminsiter flag is off")
  val InvalidPermissionTemplateId = new EnumVal(id = 554, "InvalidPermissionTemplateId", "Invalid permission template id")
  val InvalidSubAccount = new EnumVal(id = 555, "InvalidSubAccount", "Invalid sub account")
  val PrimaryUserNotFound = new EnumVal(556,"PrimaryUserNotFound","Primary user not found")
  val InvalidUserRoleId = new EnumVal(557, "InvalidUserRoleId", "Invalid user role id")
  val UnableToInsertUserRole = new EnumVal(558, "UnableToInsertUserRole", "Unable to insert user role")
  val UpdateUserAccountStatusFailed = new EnumVal(559,"UpdateUserAccountStatusFailed","Update User Account Status Failed")
  val InvalidUserAccountAssociation = new EnumVal(560, "InvalidUserAccountAssociation", "Invalid User Account Association")
  val UpdateAccountStatusFailed = new EnumVal(561, "UpdateAccountStatusFailed", "Update Account Status failed")
  val NotAPartnerAccount = new EnumVal(562, "NotAPartnerAccount", "Not a Partner Account")
  val UpdateAccountStatusFailedHasActiveSubAccounts = new EnumVal(563, "UpdateAccountStatusFailedHasActiveSubAccounts", "Update Account Status failed, Account has active sub accounts")
  val CouldNotListRateLimits = new EnumVal(564, "CouldNotListRateLimits", "Could not list rate limits")
  val CouldNotSaveRateLimits = new EnumVal(565, "CouldNotSaveRateLimits", "Could not save rate limits")
  val CouldNotUpdateRateLimits = new EnumVal(566, "CouldNotUpdateRateLimits", "Could not update rate limits")
  val CouldNotDeleteRateLimits = new EnumVal(567, "CouldNotDeleteRateLimits", "Could not delete rate limits")
  val cannotRemoveBYOKProvision = new EnumVal(568, "CannotRemoveBYOKProvision", "Cannot remove BYOK provision")
  val CustomerKMSRegionNotSupported = new EnumVal(569, "CustomerKMSRegionNotSupported", "Un-supported KMS region")
  val CustomerKMSEncryptionError = new EnumVal(570, "CustomerKMSEncryptionError", "Doesn't have access to generate/encrypt data key")
  val CustomerKMSDecryptionError = new EnumVal(571, "CustomerKMSDecryptionError", "Doesn't have access to decrypt keys")
  val CustomerKMSARNParsingError = new EnumVal(572, "CustomerKMSARNParsingError", "KMS ARN parsing error")
  val GenerateCustomerKeysFailed = new EnumVal(574, "GenerateCustomerKeysFailed", "Generate/Rotate Customer Keys failed")
  val CouldNotUpdateBusinessUser = new EnumVal(573, "CouldNotUpdateBusinessUser", "Could not update business user")
  val CouldNotUpsertRateLimits = new EnumVal(574, "CouldNotUpsertRateLimits", "Could not upsert rate limits")
  val UnableToFetchPermissions = new EnumVal(575, "UnableToFetchPermissions", "Error occurred while fetching permissions")
  val UnableToFetchDefaultModules = new EnumVal(576, "UnableToFetchDefaultModules", "Error occurred while fetching default modules")
  val InvalidModules = new EnumVal(577, "InvalidModules", "Invalid modules")
  val UnableToSaveDefaultModules = new EnumVal(578, "UnableToSaveDefaultModules", "Unable To Save Default Modules")
  val UserAccountRoleAssociationExists = new EnumVal(579, "UserAccountRoleAssociationExists", "User Account Role Association Exists")
  val RoleNamePolicy = new EnumVal(580, "RoleNamePolicy", "Role name constraints not fulfilled")
  val InvalidHistoricalRange = new EnumVal(581, "InvalidHistoricalRange", "Invalid historical range")
  val InvalidRequestAction = new EnumVal(582, "InvalidRequestAction", "Invalid request action")
  val InvalidRequestSource = new EnumVal(583, "InvalidRequestSource", "Invalid request source")
  val CouldNotSaveApiAudit = new EnumVal(584, "CouldNotSaveApiAudit", "Could not save API audit")
  val InvalidEIN = new EnumVal(585, "InvalidEIN", "Invalid EIN")
  val InvalidPublicAccountId = new EnumVal(586, "InvalidPublicAccountId", "Invalid Public AccountId")
  val UnableToUpdateAccountType = new EnumVal(587, "UnableToUpdateAccountType", "Unable to update account type")
  val UnableToUpdateAdminister = new EnumVal(588, "UnableToUpdateAdminister", "Unable to update administer")
  val UnableToUpdateMLAFields = new EnumVal(589, "UnableToUpdateMLAFields", "Unable to save MLA Fields")
  val UIAccountConfigurationNotExist = new EnumVal(590, "UIAccountConfigurationNotExist", "UI configuration doesn't Exist for the account")
  val CouldNotFetchUIAccountConfiguration = new EnumVal(591, "CouldNotFetchUIAccountConfiguration", "Error occurred while fetching UI configuration")
  val CouldNotSaveUIAccountConfiguration = new EnumVal(592, "CouldNotSaveUIAccountConfiguration", "Could not save UI configuration")
  val CouldNotUpdateUIAccountConfiguration = new EnumVal(593, "CouldNotUpdateUIAccountConfiguration", "Could not update UI configuration")
  val AutoTimeoutExceeded = new EnumVal(594, "AutoTimeoutExceeded", "Please provide proper auto timeout value, greater than 0 and less than 480")
  val IdleTimeoutExceeded = new EnumVal(595, "IdleTimeoutExceeded", "Please provide proper idle timeout value, greater than 0 and less than 480")
  val IdleTimeoutGreaterThanAutoTimeout = new EnumVal(596, "IdleTimeoutGreaterThanAutoTimeout", "Idle timeout greater than Auto timeout")
  val InvalidReCaptcha = new EnumVal(121, "InvalidReCaptcha", "Invalid Recaptcha")
  val PayloadKeysDecryptionError = new EnumVal(140, "PayloadKeyDecryptionFailed", "Error while decrypting payload private keys" )
  val CreatePayloadKeysError = new EnumVal(141, "CreatePayloadKeysError", "Error while creating account payload keys")
  val UpdatePayloadKeysError = new EnumVal(142, "UpdatePayloadKeysError", "Error while updating account payload keys")
  val DeletePayloadKeysError = new EnumVal(143, "DeletePayloadKeysError", "Error while deleting account payload keys")
  val NoPayloadKeyFoundError = new EnumVal(144, "NoPayloadKeyFoundError", "No payload key found for given account and environment")
  val CustomerPublicKeyNotDefinedError = new EnumVal(145, "CustomerPublicKeyNotDefinedError", "No customer public key provided to update")
  val NoActivePayloadKeysForCloningError = new EnumVal(146, "NoActivePayloadKeysForCloningError", "No active payload keys found for cloning")
  val CloningPayloadKeysNotRequiredError = new EnumVal(147, "CloningPayloadKeysNotRequiredError", "Active payload key(s) already using multi-region keys, no need for cloning")
  val CloningPayloadKeysError = new EnumVal(148, "CloningPayloadKeysError", "Error while cloning account payload keys")
  val SAMLNotEnabledForParent = new EnumVal(597, "SAMLNotEnabledForParent", "SAML 2.0 must be enabled, for the parent account")
  val PrefillModuleNotEnabled = new EnumVal(600, "PrefillModuleNotEnabled", "Prefill Module Not Enabled")
  val NonFunctionalMailsAssociated = new EnumVal(601, "NonFunctionalMailsAssociated", "NonFunctional Users(emails) Associated With This Account")
  val NonFunctionalMail = new EnumVal(602, "NonFunctionalMails", "NonFunctional User(email)")
  val InvalidRoleType = new EnumVal(606, "InvalidRoleType", "Role type does not exist")
  val InvalidCustomRole = new EnumVal(607, "InvalidCustomRole", "Custom role Id not found")
  val CouldNotAddSFTPUserForAccount= new EnumVal(608, "CouldNotAddSFTPUserForAccount", "Could Not Add SFTP User For Account")
  val UnableToListSFTPUsers= new EnumVal(609, "UnableToListSFTPUsers", "Unable To List SFTP Users")
  val RoleLimitReached = new EnumVal(610, "RoleLimitReached", "System role limit reached")
  val InvalidFile = new EnumVal(611, "InvalidFile", "File is invalid")
  val AccountBundleAssociationNotFound = new EnumVal(612, "AccountBundleAssociationNotFound", "Account Bundle Association Not Found")
  val AccountBundleAssociationNotSaved = new EnumVal(613, "AccountBundleAssociationNotSaved", "Account Bundle Association Not Saved/Updated")
  val AccountBundleAssociationAuditNotFound = new EnumVal(614, "AccountBundleAssociationAuditNotFound", "Account Bundle Association Audit Not Found")
  val AccountAuditNotSaved = new EnumVal(615, "AccountAuditNotSaved", "Account Audit Not Saved/Updated")
  val AccountProvisioningNotUpdated = new EnumVal(616, "AccountProvisioningNotUpdated", "Account Provisioning Not Saved/Updated")
  val AccountAuditNotUpdated = new EnumVal(617, "AccountAuditNotUpdated", "Account Audit Information Not Found")
  val InvalidAccountNames = new EnumVal(611, "InvalidAccountNames", "User could not be created due to incorrectly formatted Account name")
  val InvalidRoleNames = new EnumVal(611, "InvalidRoleNames", "User could not be created due to incorrectly formatted Role name")

  // IDM API key
  val IDMApiKeyNotInserted = new EnumVal(620, "IDMApiKeyNotInserted", "IDMApiKey not inserted")
  val IDMApiKeyCannotBeDeprecated = new EnumVal(621, "IDMApiKeyCannotBeDeprecated", "IDMApiKey status not deprecate")
  val IDMApiKeyCannotBeUpdated = new EnumVal(622, "IDMApiKeyCannotBeUpdated", "IDMApiKey status not updated")
  val IDMKeyFetchFailed = new EnumVal(623, "IDMKeyFetchFailed", "IDMKeyFetchFailed")
  val IDMApiKeyNotFound = new EnumVal(624, "IDMApiKeyNotFound", "The given api key is not found or active.")
  val IDMNotEnabled = new EnumVal(625, "IDMNotEnabled", "IDM feature not enabled for the account.")

//  Products
  val InvalidProductUpdate = new EnumVal(626, "InvalidProductUpdate", "Contains products which are not valid or can't be updated")
  val InvalidProduct = new EnumVal(627, "InvalidProduct", "Invalid Product, Product Not Found")
  val ProductConfigNotUpdated = new EnumVal(628, "ProductConfigNotUpdated", "Product Config Not Saved/Updated")

  val MagicTokenAlreadyCreated = new EnumVal(629, "MagicTokenAlreadyCreated", "Magic token is already created for the user")
  val DeviceIdentifierNotFound = new EnumVal(630, "DeviceIdentifierNotFound", "The passwordless login link must be opened by the same browser or device as the login attempt")
  val InvalidDeviceIdentifier = new EnumVal(631, "InvalidDeviceIdentifier", "The passwordless login link must be opened by the same browser or device as the login attempt")
  val MagicTokenForUserNotFound = new EnumVal(632, "MagicTokenForUserNotFound", "Magic token for the user was expired or not found")
  val MagicTokenExpired = new EnumVal(633, "MagicTokenExpired", "Magic token for the user is expired")
  val CurrentPasswordNotFound = new EnumVal(634, "CurrentPasswordNotFound", "Current Password is required to reset password")
  val ForcePasswordlessLogin = new EnumVal(635, "ForcePasswordlessLogin", "Force passwordless login for more than or equal to 2 bad login attempts")
  val MagicLinkAuditNotSaved = new EnumVal(636, "MagicLinkAuditNotSaved", "Magic link audit Not saved")
  val AccountOrUserLocked = new EnumVal(637, "AccountOrUserLocked", "Account/User locked")
  val WebhookSecretKeySizeExceeded = new EnumVal(638, "WebhookSecretKeySizeExceeded", "Webhook secret key exceeds max limit")
  val UnauthorizedTransactionId = new EnumVal(639, "UnauthorizedTransactionId", "No access allowed to this transaction")
  val WebhookSecretKeyUpdateFailed = new EnumVal(640, "WebhookSecretKeyUpdateFailed", "Webhook secret key cannot be updated when Oauth is enabled")
  val AccountDataRetentionScheduleNotFound = new EnumVal(641, "AccountDataRetentionScheduleNotFound", "Account Data Retention Schedule Not Found")
  val AccountDataRetentionScheduleNotSaved = new EnumVal(642, "AccountDataRetentionScheduleNotSaved", "Account Data Retention Schedule Not Saved/Updated")
  val InvalidAccountDataRetentionSchedule = new EnumVal(643, "InvalidAccountDataRetentionSchedule", "Invalid Account Data Retention Schedule")
  val BundlesInformationNotFound = new EnumVal(644, "BundlesInformationNotFound", "Bundle Information Not Found")
  val UnableToFetchDefaultFeatureFlags = new EnumVal(645, "UnableToFetchDefaultFeatureFlags", "Unable to Fetch Default FeatureFlags")
  val AccountAutomationFailed = new EnumVal(646, "AccountAutomationFailed", "Account Automation Failed")
  val UnableToUpdateIsSponsorBank = new EnumVal(647, "UnableToUpdateIsSponsorBank", "Unable to update the is sponsor bank option")
  val SponsorBankProgramNotFound = new EnumVal(648, "SponsorBankProgramNotFound", "Sponsor Bank Option Not Found")
  val UnableToLinkSponsorBankProgram = new EnumVal(649, "UnableToLinkSponsorBankProgram", "Unable to Link Sponsor Bank Program")
  val UnableToUnLinkSponsorBankProgram = new EnumVal(650, "UnableToUnLinkSponsorBankProgram", "Unable to unlink Sponsor Bank Program")
  val NoSponsorBankLinked = new EnumVal(651, "NoSponsorBankAssociated", "No Sponsor Bank Program Linked")
  val MagicLinkNotCreatedForSAMLUser = new EnumVal(652, "MagicLinkNotCreatedForSAMLUser", "Magic token can't be created.")
  val AccountOrUserInactive = new EnumVal(653, "InactiveUser", "Account/User inactive")
  val AccountAnalyticsInfoNotFound = new EnumVal(654, "AccountAnalyticsInfoNotFound", "The account analytics import info does not exist")
  val UserLockedAutomatically = new EnumVal(655, "UserLockedAutomatically", "Account/User locked Automatically")
  val DemoRestrictedDomain = new EnumVal(656, "Not Available", "The Socure Demo system is not available for your email domain at this time.")
  val InclusionListCheckFailed = new EnumVal(657, "InclusionListCheckFailed", "Inclusion List Check Failed")
  val NoDataFound = new EnumVal(658, "NoData", "No Data Found")
  val EmailDomainNotEmpty = new EnumVal(659, "EmailNotEmpty", "Email Domain Should not be empty")
  val ApiKeyNotFound = new EnumVal(660, "ApiKeyNotFound", "ApiKeyNotFound")
  val BaseLineLogicMappingFailed = new EnumVal(660, "BaseLineLogicMappingFailed", "Failed mapping the baseline logic")
  val SaiPreferencesUpsertFailed = new EnumVal(662, "UnableToUpsertSaiPreferences", "Unable to save SAI Preferences")
  val SaiPreferencesNotFound = new EnumVal(663, "SaiPreferencesNotFound", "Unable to fetch SAI Preferences")
  val AccountPlatformResourceMappingNotFound = new EnumVal(664, "AccountPlatformResourceMappingNotFound", "Account Platform Resource Mapping Not Found")
  val AccountPlatformResourceMappingNotSaved = new EnumVal(613, "AccountPlatformResourceMappingNotSaved", "Account Platform Resource Mapping Not Saved/Updated")
  val TimeoutConfigurationFailed = new EnumVal(665, "TimeoutConfigurationFailed", "Idle Timeout/Auto Sign out  Configuration Not Saved")
  val UnableToSaveLookupApiKey = new EnumVal(666, "UnableToSaveLookupApiKey", "Unable to save SimSwapLookup ApiKey")
  val UnableToSaveOTPWorkFlow = new EnumVal(667, "UnableToSaveOTPWorkFlow", "Unable to save OTP WorkFlow information")
  val DocumentTokenAlreadyCreated = new EnumVal(668, "DocumentTokenAlreadyCreated", "Document token is already created for the user")
  val DocRestrictedDomain = new EnumVal(669, "DocRestrictedDomain", "Access to documentation is not available. Please provide a valid business email address.")
  val DocumentLinkAuditNotSaved = new EnumVal(670, "DocumentLinkAuditNotSaved", "Document link audit Not saved")
  val DocumentLinkTokenForEmailNotFound = new EnumVal(671, "DocumentLinkTokenForEmailNotFound", "Documet link token for the email was expired or not found")
  val CognitoUserNotMigrated = new EnumVal(672, "CognitoUserNotMigrated", "User is not migrated to Cognito")
  val CognitoUserAlreadyMigrated = new EnumVal(673, "CognitoUserAlreadyMigrated", "User is migrated to Cognito")
  val CognitoUserNotInScope = new EnumVal(674, "CognitoUserNotInScope", "User is not Cognito Scope")
  val AccountMetadataNotFound = new EnumVal(675, "AccountMetadataNotFound", "Account metadata not found")
}
