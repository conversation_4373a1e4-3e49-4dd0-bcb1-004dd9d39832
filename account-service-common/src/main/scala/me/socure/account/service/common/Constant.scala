package me.socure.account.service.common

object Constant {
  val WL_DEFAULT_FUZZINESS = 0.5
  val WL_DEFAULT_EXACT_DOB = true
  val DEFAULT_DOB_MATCH_LOGIC_EXACT = "exact_yyyy_mm_dd"
  val DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY = "exact_yyyy"
  val DEFAULT_ONE_YEAR_RADIUS_YYYY_MM_DD = "one_year_radius_yyyy_mm_dd"
  val WL_DEFAULT_DOB_AND_NAME = true
  val WL_DEFAULT_MONITORING = false
  val WL_DEFAULT_LIMIT = 10
  val SANCTION = "sanction"
  val FITNESS_PROBITY = "fitness-probity"
  val PEP = "pep"
  val WARNING = "warning"
  val ADVERSE_MEDIA = "adverse-media"
}
