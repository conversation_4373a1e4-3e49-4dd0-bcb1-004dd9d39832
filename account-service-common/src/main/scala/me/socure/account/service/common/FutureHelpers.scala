package me.socure.account.service.common

import scala.concurrent.{ExecutionContext, Future}

object FutureHelpers {
  def seqFutures[T, U](items: TraversableOnce[T])(yourfunction: T => Future[U])(implicit ec: ExecutionContext): Future[List[U]] = {
    items.foldLeft(Future.successful[List[U]](Nil)) {
      (f, item) => f.flatMap {
        x => yourfunction(item).map(_ :: x)
      }
    } map (_.reverse)
  }
}
