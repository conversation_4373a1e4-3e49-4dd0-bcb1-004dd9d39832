package me.socure.account.service.common

trait AccInfoCacheKeyProvider {
  def provide(apiKey: String): String = {
    s"v2_$apiKey"
  }

  def provideV3(apiKey: String): String = {
    s"v3_$apiKey"
  }

  def getAccountPermissionsKey(accountId: Long) = s"account-permissions-account-id${accountId.toString}"
  def keyForGetAccountDetailsById(id: Long): String = CacheKeyProvider provide s"account_details_by_id_v2_$id"
}

object AccInfoCacheKeyProvider extends AccInfoCacheKeyProvider
