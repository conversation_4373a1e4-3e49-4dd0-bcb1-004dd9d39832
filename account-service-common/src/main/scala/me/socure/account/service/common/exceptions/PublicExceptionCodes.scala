package me.socure.account.service.common.exceptions

object PublicExceptionCodes extends Enumeration {

  type ExceptionCodes = EnumVal

  sealed class EnumVal(val id: Int, val code: String, val description: String) extends Value

  val AccountNameRequired = new EnumVal(4001, "AccountNameRequired", "Account Name is required")
  val IndustryRequired = new EnumVal(4002, "IndustryRequired", "Industry is required")
  val IncorrectEmailId = new EnumVal(4003, "IncorrectEmailId", "Incorrect Email ID format")
  val SourceNotWhiteListed = new EnumVal(4004, "SourceNotWhiteListed", "Request should come only from a whitelisted source")
  val NonPrimaryAccountAPIKey = new EnumVal(4005, "NonPrimaryAccountAPIKey", "Socure API key of the primary account should be used")
  val ModulesIncorrect = new EnumVal(4006, "ModulesIncorrect", "One or more modules are incorrect")
  val UserFirstNameRequired = new EnumVal(4007, "UserFirstNameRequired", "User First Name is required")
  val UserLastNameRequired = new EnumVal(4008, "UserLastNameRequired", "User Last Name is required")
  val UserContactNumberRequired = new EnumVal(4009, "UserContactNumberRequired", "User Contact Number is required")
  val RateLimitExceeded = new EnumVal(4010, "RateLimitExceeded", "Rate Limit Exceeded")
  val InvalidApiKey = new EnumVal(4011, "InvalidApiKey", "Invalid ApiKey")
  val WhiteListDomainsNotConfigured = new EnumVal(4012, "WhiteListDomainsNotConfigured", "WhiteList Domains Not Configured")
  val IncorrectIndustry = new EnumVal(4013, "IncorrectIndustry", "Incorrect Industry")
  val ApiKeyNotPresent = new EnumVal(4014, "ApiKeyNotPresent", "Api Key Not Present")
  val AccountNotV2Provisioned = new EnumVal(4015, "AccountNotV2Provisioned", "Account Not V2 Provisioned")
  val PermissionMissing = new EnumVal(4016, "PermissionMissing", "Permissions Missing")
  val UserNotAssociatedToParentAccount = new EnumVal(4017, "UserNotAssociatedToParentAccount", "User Not Associated To Parent Account")
  val NoPrimaryUsersPresentForParentAccount = new EnumVal(4018, "NoPrimaryUsersPresentForParentAccount", "No Primary Users Present For Parent")
  val UnknownError = new EnumVal(4019, "UnknownError", "Something went wrong")
  val AccountNameAlreadyExists = new EnumVal(4020, "AccountNameAlreadyExists", "Account name already exists")
  val AccountIdsEmpty = new EnumVal(4020, "AccountIdsEmpty", "Account Id(s) should not be empty")
}
