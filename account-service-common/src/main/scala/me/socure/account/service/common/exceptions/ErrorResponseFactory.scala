package me.socure.account.service.common.exceptions

import me.socure.model.ErrorResponse

/**
  * Created by alexand<PERSON> on 5/31/16.
  */
object ErrorResponseFactory {

  def get(codes: ExceptionCodes.EnumVal) = {
    ErrorResponse(codes.id, codes.description)
  }

  def get(code: Int, message: String) = {
    ErrorResponse(code, message)
  }

  def get(e: Throwable) = {
    me.socure.factory.ErrorResponseFactory.get(e)
  }

  def get(codes: PublicExceptionCodes.EnumVal) = {
    ErrorResponse(codes.id, codes.description)
  }
}
