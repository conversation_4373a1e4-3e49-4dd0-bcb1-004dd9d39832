package me.socure.account.service.common.subscription

import me.socure.types.scala.{ByMember, Enum}

object SubscriptionChannelRegistryStatus extends Enum with ByMember {
  type SubscriptionChannelRegistryStatus = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val ACTIVE = new SubscriptionChannelRegistryStatus(1, "Active")
  val SUSPENDED = new SubscriptionChannelRegistryStatus(2, "Suspended")
  val DISABLED = new SubscriptionChannelRegistryStatus(3, "Disabled")
  val DELETED = new SubscriptionChannelRegistryStatus(4, "Deleted")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(name: String): Boolean = values.exists(_.toString.toLowerCase == name.toLowerCase)
  val byId: Int => Option[SubscriptionChannelRegistryStatus] = byMember(_.id)

  def getStatusByName(name: String) : Option[SubscriptionChannelRegistryStatus] = {
    values.find(_.name==name) match {
      case e if e.isDefined => Some(e.get)
      case _ => None
    }
  }

}
