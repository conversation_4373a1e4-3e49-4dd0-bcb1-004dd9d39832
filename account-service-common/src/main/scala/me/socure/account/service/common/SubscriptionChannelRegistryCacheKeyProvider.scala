package me.socure.account.service.common

object SubscriptionChannelRegistryCacheKeyProvider {

  def provide(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int] = None): String = {
    featureTypeId match {
      case Some(featureType) =>
        CacheKeyProvider provide s"subscription_channel_registry_${accountId}_environment_${environmentTypeId}_subscriptionTypeId_${subscriptionTypeId}_featureTypeId_${featureType}"
      case None =>
        CacheKeyProvider provide s"subscription_channel_registry_${accountId}_environment_${environmentTypeId}_subscriptionTypeId_$subscriptionTypeId"
    }
  }

}
