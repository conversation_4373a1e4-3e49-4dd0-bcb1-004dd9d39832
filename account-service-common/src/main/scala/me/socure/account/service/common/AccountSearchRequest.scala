package me.socure.account.service.common

import me.socure.common.slick.domain.Pagination


case class AccountSearchRequest (id: Option[Long],
                                 publicId: Option[String],
                                 name: Option[String],
                                 apiKey: Option[String],
                                 publicApiKey: Option[String],
                                 email: Option[String],
                                 deleted: Option[<PERSON><PERSON>an],
                                 active: Option[<PERSON><PERSON>an],
                                 internal: Option[<PERSON>olean],
                                 isParent: Option[Boolean],
                                 permissions: Option[Set[Int]],
                                 pagination: Option[Pagination]){

  def next: AccountSearchRequest = {
    pagination match {
      case Some(paging) =>
        copy(
          pagination = Some(
            paging.next
          )
        )
      case None => throw new IllegalStateException("Cannot call next without defined pagination")
    }
  }
}