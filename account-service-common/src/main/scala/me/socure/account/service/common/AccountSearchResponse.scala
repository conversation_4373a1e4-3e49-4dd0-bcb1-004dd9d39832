package me.socure.account.service.common

import scala.beans.BeanProperty


case class AccountSearchResponse(
                                  @BeanProperty val accountId: Long,
                                  @BeanProperty val publicId: String,
                                  @BeanProperty val name: String,
                                  @BeanProperty val active: <PERSON><PERSON><PERSON>,
                                  @BeanProperty val internal: <PERSON><PERSON><PERSON>,
                                  @BeanProperty val deleted: <PERSON><PERSON><PERSON>,
                                  @BeanProperty val parentId: Option[String],
                                  @BeanProperty val primaryUser: Option[String],
                                  @BeanProperty val encryptionEnabled: <PERSON>olean,
                                  @BeanProperty val piiMaskEligible: <PERSON>olean
                                )
