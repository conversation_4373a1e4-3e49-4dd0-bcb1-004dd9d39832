package me.socure.account.service.common.subscription

import me.socure.types.scala.{ByMember, Enum}

object CommunicationMode extends Enum with ByMember{
  type CommunicationMode = EnumVal

  sealed case class EnumVal(id: Int, name: String) extends Value

  val WEBHOOK = new CommunicationMode(1, "Webhook")
  val EMAIL = new CommunicationMode(2, "Email")

  def isValid(id: Int): Boolean = values.exists(_.id == id)
  def isValid(name: String): Boolean = values.exists(_.toString.toLowerCase == name.toLowerCase)
  val byId: Int => Option[CommunicationMode] = byMember(_.id)
}
