package me.socure.account.idplus


import me.socure.account.idplus.engine.IdPlusEngine
import me.socure.account.idplus.servlet.IdPlusV2Servlet
import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.hmac.verifier.{HMACHttpVerifier, HmacVerificationRequest}
import me.socure.model._
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferences
import me.socure.model.mla.MLAFields
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito.{reset, when}
import org.mockito.{Match<PERSON>, Mockito}
import org.scalatest.BeforeAndAfter
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.{ExecutionContext, Future}


class IdPlusV2ServletTest extends ScalatraFunSuite with MockitoSugar with BeforeAndAfter {
  implicit val ec : ExecutionContext = ExecutionContext.global

  val service : IdPlusEngine = mock[IdPlusEngine]
  val hmacVerifier = mock[HMACHttpVerifier]
  val servlet : IdPlusV2Servlet = new IdPlusV2Servlet(service, hmacVerifier)
  addServlet(servlet, "/*")

  before{
    reset(service)
    reset(hmacVerifier)
  }

  Mockito
    .doNothing()
    .when(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))

  private val accountInformation = AccountInformation(
    active = true,
    publicId = "1",
    accountId = "1",
    accountName = "accountName",
    industry = Industry("some sector", "some industry"),
    isInternal = true,
    environment = Environment(
      publicApiKeys = Seq.empty,
      id = 456,
      name = "environment",
      domain = Set("domain1", "domain2"),
      accessCredentials = AccessCredentials(
        apiKey = "api-key",
        secretKey = "secret key",
        accessToken = "access token",
        accessTokenSecret = "access token secret",
        certificate = ""
      ),
      socialAccounts = Seq(
        SocialNetworkAppKeys(
          id = 145,
          provider= "provider",
          appkey = "app key",
          appsecret = "app secret",
          environment = 566,
          accountId = 999
        )
      ),
      invidiualCache = Seq(
        AccountIndividualCache(
          id = 666L,
          date = new DateTime(0L, DateTimeZone.UTC),
          identifier = "identifier",
          accountId = 876L
        )
      ),
      overallCache = Some(AccountOverallCache(
        id = 234L,
        date = new DateTime(0L, DateTimeZone.UTC),
        accountId = 8765L))
    ),
    watchlistPreference = WatchlistPreferenceValueGenerator.aWatchlistPreference(),
    watchlistPreference_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreference(),
    watchlistPreferences_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreferences(),
    roles = Set(
      BusinessUserRoles.WATCHLIST.id
    ),
    primaryFraudModel = Some(FraudModel(
      id = 123,
      identifier = "identifier",
      name = "name",
      url = "url",
      version = "123",
      createdDate = new DateTime(0L, DateTimeZone.UTC),
      lastUpdated = new DateTime(0L, DateTimeZone.UTC)
    )),
    fraudModels = Set(
      FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L, DateTimeZone.UTC),
        lastUpdated = new DateTime(0L, DateTimeZone.UTC)
      )
    ),
    kycPreferences = KycPreferences(exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = true),
    subscriptionTypeIds = Seq(),
    includedWatchlistSources = Seq(),
    dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
    consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
    externalId = None,
    mlaField = MLAFields("", ""),
    ein = None,
    webhookNotificationPreferences = Seq()
  )

  test("Should fetch account details by public api key"){
    val publicApiKey = "publicApiKey"

    when(service.fetchByPublicApiKeyV2(publicApiKey)) thenReturn Future.successful(Right(accountInformation))
    get(
      uri = s"/accounts/publicapikeys/$publicApiKey",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[AccountInformation]](status, 200, body, Response(ResponseStatus.Ok, accountInformation))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  test("Should fail : fetch account details by public api key") {
    val publicApiKey = "publicApiKey"
    val error = ErrorResponseFactory.get(ExceptionCodes.InvalidPublicApiKey)
    when(service.fetchByPublicApiKeyV2(publicApiKey)) thenReturn Future.successful(Left(error))
    get(
      uri = s"/accounts/publicapikeys/$publicApiKey",
      headers = Map(
        "Authorization" -> """realm="Socure",nonce="1cba0e49-603a-48f9-8c11-3fcb2d9d3fdf",version="1.0",signature="vwH06fPfh2bBrX5A9u3OcbNq794/T/BXMcvRxx+zBy/bhLbQ4BS5SeKeG/nCxEIYT43Ahd31jWr9mdOQu2qVkQ=="""",
        "Content-Type" -> "application-json; charset=UTF-8",
        "X-Authorization-Timestamp" -> s"${System.currentTimeMillis() / 1000}"
      )){
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.InvalidPublicApiKey)))
    }
    Mockito
      .verify(hmacVerifier).verify(Matchers.any(classOf[HmacVerificationRequest]))
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
