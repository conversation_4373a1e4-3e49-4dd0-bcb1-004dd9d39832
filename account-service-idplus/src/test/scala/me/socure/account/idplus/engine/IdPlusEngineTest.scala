package me.socure.account.idplus.engine

import com.mchange.v2.c3p0.ComboPooledDataSource
import dispatch.Http

import javax.sql.DataSource
import me.socure.DaoAccount
import me.socure.account.service._
import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.validator.V2Validator
import me.socure.common.clock.RealClock
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.k8s.mysql.MysqlService
import me.socure.common.slick.masterslave.{DB, DatabaseConfiguration}
import me.socure.common.sql.SQLExecutor
import me.socure.constants.KeyProviders
import me.socure.model._
import me.socure.model.account._
import me.socure.model.ein.EIN
import me.socure.model.kyc.KycNationalIdMatchLogic.exact
import me.socure.model.kyc.KycPreferences
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import me.socure.storage.slick.dao._
import me.socure.storage.slick.tables.account.mappers.{CAWatchlistPreferenceMapper, WatchlistPreferenceMapper}
import me.socure.utils.DBProxyWithMetrics
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.flywaydb.core.Flyway
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfterAll, EitherValues, FunSuite, Matchers}
import scalacache.ScalaCache
import slick.jdbc.JdbcBackend

import scala.collection.mutable.ListBuffer
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

/**
  * Created by alexandre on 5/4/16.
  */
class IdPlusEngineTest extends FunSuite with Matchers with BeforeAndAfterAll with ScalaFutures with EitherValues with MemcachedTestSupport {

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  val clock = new RealClock
  val mysqlService: MysqlService = MysqlService("idplus-engine")
  private val dbName = "socure"
  override def memcachedPodLabel(): String = "idplus-service-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private val dataSources: ListBuffer[ComboPooledDataSource] = ListBuffer.empty

  import java.text.SimpleDateFormat

  val formatter = new SimpleDateFormat("yyyy-MM-dd")

  private def buildDataSource(database: Option[String] = None) = {
    val endpoint = mysqlService.endpoint
    val dataSource = new ComboPooledDataSource()
    dataSource.setMaxPoolSize(5)
    dataSource.setJdbcUrl(endpoint)
    dataSource.setUser("root")
    dataSource.setPassword("root")
    dataSource.setDriverClass("com.mysql.cj.jdbc.Driver")
    dataSources.append(dataSource)
    dataSource
  }

  private def prepareSchema(dataSource: DataSource) = {

    val flyway = new Flyway()
    flyway.setDataSource(dataSource)
    flyway.setSchemas(dbName)
    flyway.setBaselineOnMigrate(true)
    flyway.migrate()
  }

  private def buildEngine(dataSource: DataSource) = {

    val db = JdbcBackend.Database.forDataSource(dataSource)

    val dbSlave = DB.slave(
      databaseConfiguration = DatabaseConfiguration.slave(
        profile = slick.driver.MySQLDriver,
        dataSource = dataSource
      )
    )
    val dbProxyWithMetrics = new DBProxyWithMetrics(db, dbSlave)
    val daoKycPreferences = new DaoKycPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoComplyWatchlistPreferences = new DaoComplyWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val caWatchlistPreferenceMapper = new CAWatchlistPreferenceMapper(clock)
    val daoAccount = new DaoAccount(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoAccountV2 = new DaoAccountV2(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val v2Validator = new V2Validator(daoAccountV2, daoAccount)
    val watchlistPreference = new DaoWatchlistPreferences(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val auditDetailsService = new AuditDetailsService(daoAccountV2, v2Validator)
    val accountPreference = new AccountPreferencesService(daoKycPreferences, daoAccountV2, watchlistPreference, new WatchlistPreferenceMapper(clock), daoComplyWatchlistPreferences, caWatchlistPreferenceMapper, v2Validator, scalaCache, auditDetailsService)
    val daoSubscriptionType = new DaoSubscriptionType(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionStatus = new DaoSubscriptionStatus(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptions = new DaoSubscriptions(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoEnvironment = new DaoEnvironment(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoSubscriptionChannelRegistry = new DaoSubscriptionChannelRegistry(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val daoWatchlistSource = new DaoWatchlistSource(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val subscriptionService = new SubscriptionService(daoSubscriptions, daoSubscriptionType, daoSubscriptionStatus, daoEnvironment, daoSubscriptionChannelRegistry, daoAccountV2, v2Validator)
    val watchlistSourceService = new WatchlistSourceService(daoWatchlistSource, daoAccountV2, clock, v2Validator, scalaCache, auditDetailsService)
    val daoDvConfiguration = new DaoDvConfiguration(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val DvConfigurationService = new DvConfigurationService(daoDvConfiguration, v2Validator, auditDetailsService)
    val daoPublicApiKey = new DaoPublicApiKey(dbProxyWithMetrics, slick.driver.MySQLDriver)
    val mlaService = new MLAService(daoAccount, new DaoMLAFields(dbProxyWithMetrics, slick.driver.MySQLDriver), clock)
    val einService = new EINService(new DaoEIN(dbProxyWithMetrics, slick.driver.MySQLDriver), daoAccount, clock)
    val saiPreferencesService = new SaiPreferencesService(new DaoSaiPreferences(Http.default, "", dbProxyWithMetrics, slick.driver.MySQLDriver), daoAccount, clock)
    val permissionsToBeOverriddenByChild = Set(new Integer(94), new Integer(174))

    new IdPlusEngine(
      slick.driver.MySQLDriver,
      accountPreference,
      daoAccount,
      subscriptionService,
      watchlistSourceService,
      DvConfigurationService,
      mlaService,
      einService,
      saiPreferencesService,
      daoPublicApiKey,
      daoAccountV2,
      permissionsToBeOverriddenByChild
    )
  }

  override def beforeAll() {
    mysqlService.start()
    mysqlService.waitForService()
    val dataSource = buildDataSource()
    prepareSchema(dataSource)

    val sqlExecutor = new SQLExecutor(buildDataSource(Some(dbName)))

    sqlExecutor.execute("INSERT INTO tbl_industry (sector, description) VALUES ('industry sector', 'industry description')")

    sqlExecutor.execute("INSERT INTO tbl_account (id, name, industry_sector, is_internal, is_active, parent_id, is_deleted, public_id, public_api_key,external_id) " +
      s"VALUES (1, 'accountName', 'industry sector', false, true, NULL, false, '${PublicIdGenerator.account().value}', 'publicApiKey1','externalId1'), " +
      s"(2, 'accountName1', 'industry sector', false, true, NULL, true, '${PublicIdGenerator.account().value}', 'publicApiKey2','externalId2'), " +
      s"(3, 'SubaccountName1', 'industry sector', false, true, 1, false, '${PublicIdGenerator.account().value}', 'publicApiKey3','externalId3'), " +
      s"(4, 'SubaccountName2', 'industry sector', false, true, 1, false, '${PublicIdGenerator.account().value}', 'publicApiKey4','externalId4'), " +
      s"(5, 'accountName2', 'industry sector', false, true, NULL, false, '${PublicIdGenerator.account().value}', 'publicApiKey5','externalId5'), " +
      s"(6, 'SubAccountName6', 'industry sector', false, true, 5, false, '${PublicIdGenerator.account().value}', 'publicApiKey6','externalId6'), " +
      s"(7, 'SubAccountName7', 'industry sector', false, true, 2, false, '${PublicIdGenerator.account().value}', 'publicApiKey7','externalId7')"
    )


    sqlExecutor.execute("INSERT INTO tbl_account_permission(account_id, permission) VALUES(1, 22), (1, 94), (1,174), (2, 22), (3, 3), (1,97), (3, 97), (3, 94), (5,22), (6,97)")

    sqlExecutor.execute("INSERT INTO tbl_environment_type (id, name) VALUES (1, 'Production'), (2, 'Development')")

    sqlExecutor.execute("INSERT INTO tbl_environment (id, access_token, secret_key, access_token_secret, domain, account_id, environment_type_id)" +
      " VALUES(100, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 1, 1), " +
      "(101, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 2, 1), " +
      "(102, 'subaccessToken', 'subsecretKey', 'subaccessTokenSecret', 'subdomain1,subdomain2', 3, 1), " +
      "(103, 'accessTokenDev', 'secretKeyDev', 'accessTokenSecretDev', 'domain1Dev,domain2Dev', 1, 2), " +
      "(104, 'subaccessTokenDev', 'subsecretKeyDev', 'subaccessTokenSecretDev', 'subdomain1Dev,subdomain2Dev', 3, 2), " +
      "(105, 'subaccessTokenDev2_1', 'subsecretKeyDev2_1', 'subaccessTokenSecretDev2_1', NULL, 4, 1), " +
      "(106, 'subaccessTokenDev2_2', 'subsecretKeyDev2_2', 'subaccessTokenSecretDev2_2', NULL, 4, 2), " +
      "(107, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 5, 1), " +
      "(108, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 6, 1), " +
      "(109, 'accessToken', 'secretKey', 'accessTokenSecret', 'domain1,domain2', 7, 1)"
    )

    sqlExecutor.execute("INSERT INTO tbl_environment_cache (id, cache_skip_date, skip_cache, environment_id) VALUES(1, '2016-05-01', 1, 100)")

    sqlExecutor.execute("INSERT INTO tbl_environment_individual_cache (id, date, identifier, environment_id) VALUES (1, '2016-06-02', 'identifier1', 100)")

    sqlExecutor.execute("INSERT INTO tbl_environment_individual_cache (id, date, identifier, environment_id) VALUES (2, '2016-06-03', 'identifier2', 100)")

    sqlExecutor.execute("INSERT INTO tbl_environment_social_key (id, application_key, application_secret, network, environment_id) VALUES (1, 'application key', 'application secret', 1, 100)")

    sqlExecutor.execute("INSERT INTO tbl_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(1, 100, '99-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(2, 100, '88-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(3, 100, '77-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(4, 101, 'api-key2', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(5, 102, 'subapi-key3', 'active', '2017-11-06 20:11:22', '2017-11-06 20:11:22'), " +
      "(6, 104, 'subapi-key3Dev', 'active', '2017-11-09 20:11:22', '2017-11-09 20:11:22'), " +
      "(7, 105, 'subkey2-prod', 'active', '2017-11-09 20:11:22', '2017-11-09 20:11:22'), " +
      "(8, 106, 'subkey2-dev', 'active', '2017-11-09 20:11:22', '2017-11-09 20:11:22'), " +
      "(9, 107, '66-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(10, 108, '07-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(11, 109, '09-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22');"
    )

    sqlExecutor.execute("INSERT INTO tbl_public_api_key (id, environment_id, api_key, status, created_at, updated_at) VALUES "+
      "(0, 100, '09-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), "+
      "(0, 100, '08-16ca6193-4149-456b-ae00-00fdad2437c6', 'new', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(0, 100, '07-16ca6193-4149-456b-ae00-00fdad2437c6', 'deprecated', '2017-03-06 20:11:22', '2017-03-13 23:40:22'), "+
      "(0, 101, 'public-api-key2', 'new', '2017-03-06 20:11:22', '2017-03-06 20:11:22'), " +
      "(0, 107, '06-16ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 108, '07-26ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22'), " +
      "(0, 109, '09-26ca6193-4149-456b-ae00-00fdad2437c6', 'active', '2017-03-06 20:11:22', '2017-03-11 20:11:22');"
    )

    sqlExecutor.execute(
      """
        |INSERT INTO tbl_kyc_preferences(environment_id, exact_dob) VALUES (100, true), (102, false), (107, true);
      """.stripMargin)

    sqlExecutor.execute(
      """
         INSERT INTO tbl_ca_watchlist_preferences (environment_id,exact_dob,dob_match_logic,dob_and_name,monitoring,matching_thresholds,`limit`,screening_categories,country,updated_at) values (106, 0, "exact_yyyy_mm_dd",0, 0, 0.6, 10, "adverse-media, sanction, warning", "US", DATE'2016-12-05');
      """.stripMargin)

    sqlExecutor.execute(
      """
        INSERT INTO tbl_ca_watchlist_preferences (environment_id,exact_dob,dob_match_logic,dob_and_name,monitoring,matching_thresholds,`limit`,screening_categories,country,updated_at) values (105, 1, "exact_yyyy_mm_dd", 0, 0, 0.6, 10, "adverse-media, sanction, warning", "US", DATE'2016-12-05');
      """.stripMargin)

    sqlExecutor.execute(
      """
        INSERT INTO tbl_ca_watchlist_preferences (environment_id,exact_dob,dob_match_logic,dob_and_name,monitoring,matching_thresholds,`limit`,screening_categories,country,updated_at) values (104, 1, "exact_yyyy_mm_dd", 0, 0, 0.5, 10, "adverse-media, sanction, warning", "US", DATE'2016-12-05');
      """.stripMargin)

    sqlExecutor.execute(
      """
        INSERT INTO tbl_ca_watchlist_preferences (environment_id,exact_dob,dob_match_logic,dob_and_name,monitoring,matching_thresholds,`limit`,screening_categories,country,updated_at) values (102, 1, "exact_yyyy_mm_dd", 1, 0, 0.5, 10, "adverse-media, sanction, warning", "US", DATE'2016-12-05');
      """.stripMargin)

    sqlExecutor.execute("INSERT INTO tbl_filtered_watchlist_source(environment_id, source_id) VALUES(100, 1), (100, 5), (100, 10);")

    sqlExecutor.execute("INSERT INTO tbl_mla_fields(id,account_id, member_number, security_code, updated_at) VALUES(1, 1, \"memberNumber\", \"securityCode\", DATE'2016-12-05'), (2, 2, \"memberNumber\", \"securityCode\", DATE'2016-12-05'), (3, 3, \"memberNumber\", \"securityCode\", DATE'2016-12-05'), (4, 4, \"memberNumber\", \"securityCode\", DATE'2016-12-05')")

    sqlExecutor.execute("insert into tbl_account_permission(account_id, permission) values (5,140)")
    sqlExecutor.execute("insert into tbl_account_permission(account_id, permission) values (5,142)")
    sqlExecutor.execute("insert into tbl_account_permission(account_id, permission) values (5,143)")

    sqlExecutor.execute("INSERT INTO `account_hierarchy`(account_id, hierarchy_path,account_type,hierarchy_status,administer) VALUES " +
      "('1','1/',1,1,1), " +
      "('3','1/3',4,1,1), " +
      "('6','1/6/',4,1,1) ")

    sqlExecutor.execute("INSERT INTO employee_identification_number(account_id, ein, updated_at) VALUES(1, *********, '2021-06-01'), (3, *********, '2021-06-01')")
  }

  override def afterAll() {
		dataSources.foreach(_.close())
    mysqlService.stop
    cleanupMemcached
  }

  test("should return left when no entry exist") {
    val engine = buildEngine(buildDataSource(Some(dbName)))

    val actual = engine.fetchByApiKey("wrong-api-key")
    whenReady(actual) { response =>
      response shouldBe 'left
      response.fold(_.code shouldBe 100, _ => fail)
    }
  }

  test("should return account deleted when account is marked as deleted") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("api-key2")

    whenReady(actual) { response =>
      response.fold(_.code shouldBe ExceptionCodes.AccountDeleted.id, _ => fail)
    }
  }

  test("should return correct object for the active key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("99-16ca6193-4149-456b-ae00-00fdad2437c6")

    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, accountInfo => {
          checkAccountInfomation(accountInfo)
          checkAccessCredentialsForActiveKey(accountInfo.environment.accessCredentials)
      })
    }
  }

  test("should return correct object for the new key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("88-16ca6193-4149-456b-ae00-00fdad2437c6")

    whenReady(actual) { response =>
      response shouldBe 'right
      response.fold(_ => fail, accountInfo => {
        checkAccountInfomation(accountInfo)
        checkAccessCredentialsForNewKey(accountInfo.environment.accessCredentials)
      })
    }
  }

  test("should return no object for the deprecated key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("77-16ca6193-4149-456b-ae00-00fdad2437c6")

    whenReady(actual) { response =>
      response shouldBe 'left
      val expected = ErrorResponse(100,"The account does not exist")
      response.fold(_ shouldBe expected, _ => fail)
    }
  }

  test("should return correct object for the V2 Sub account active key, taking some parent account information but use sub account info if available") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("subapi-key3")

    whenReady(actual) { response =>
      response shouldBe 'right
      response.right.toSeq.head.watchlistPreferences_3_0 shouldBe CAWatchlistPreferences(
        CAWatchlistPreference(Some(false),
          102,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = true,
          monitoring = false,
          0.5,
          10,
          Set("warning", "sanction", "fitness-probity"),
          Some(Set("warning", "sanction", "fitness-probity")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          102,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = true,
          monitoring = false,
          0.5,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          102,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = true,
          monitoring = false,
          0.5,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None))
      response.right.get.roles.count(_ == BusinessUserRoles.ECBSV.id) shouldBe 1 // ecbsv enabled at parent and child
      response.right.get.roles.count(_ == BusinessUserRoles.InternationalExpansionEnabled.id) shouldBe 0 // InternationalExpansion enabled at parent, not at child
      response.right.get.ein shouldBe Some(EIN("*********")) // ecbsv enabled at parent and child, child EIN came

    }
  }

  test("should return correct object for Subaccount key(Dev environment), with parent account information but use sub account info if available") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("subapi-key3Dev")

    whenReady(actual) { response =>
      response shouldBe 'right

      response.right.toSeq.head.watchlistPreferences_3_0 shouldBe CAWatchlistPreferences(
        CAWatchlistPreference(Some(false),
          104,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.5,
          10,
          Set("warning", "sanction", "fitness-probity"),
          Some(Set("warning", "sanction", "fitness-probity")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          104,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.5,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          104,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.5,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None))

    }
  }

  test("should return correct object for the V1 Sub account active key, taking some parent account information") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("subkey2-prod")

    whenReady(actual) { response =>
      response shouldBe 'right

      response.right.toSeq.head.watchlistPreferences_3_0 shouldBe CAWatchlistPreferences(
        CAWatchlistPreference(Some(false),
          105,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("warning", "sanction", "fitness-probity"),
          Some(Set("warning", "sanction", "fitness-probity")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          105,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          105,
          Some(true),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None))
      response.right.get.roles.count(_ == BusinessUserRoles.ECBSV.id) shouldBe 1 // ecbsv enabled at parent, not at child
      response.right.get.roles.count(_ == BusinessUserRoles.InternationalExpansionEnabled.id) shouldBe 1 // InternationalExpansion enabled at parent, not at child
      response.right.get.ein shouldBe Some(EIN("*********")) // ecbsv enabled at parent, not at child
    }
  }

  test("should return correct object for Subaccount key(Dev environment), with parent account information") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    val actual = engine.fetchByApiKey("subkey2-dev")

    whenReady(actual) { response =>
      response shouldBe 'right

      response.right.toSeq.head.watchlistPreferences_3_0 shouldBe CAWatchlistPreferences(
        CAWatchlistPreference(Some(false),
          106,
          Some(false),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("warning", "sanction", "fitness-probity"),
          Some(Set("warning", "sanction", "fitness-probity")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          106,
          Some(false),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          None),
        CAWatchlistPreference(Some(false),
          106,
          Some(false),
          Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT),
          dobAndName = false,
          monitoring = false,
          0.6,
          10,
          Set("adverse-media", "sanction", "warning"),
          Some(Set("adverse-media", "sanction", "warning")),
          Some(Set("us")),
          historicalRange = None))
    }
  }

  test("should return correct parent for V2 Sub account, merged to a different parent") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
   whenReady(engine.getParent(6L, Some(5L))){ response =>
     response shouldBe Some(1L)
   }
  }

  test("should return correct parent for V2 Sub account") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.getParent(3L, Some(1L))){ response =>
      response shouldBe Some(1L)
    }
  }

  test("should return correct parent for V1 Sub account") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    whenReady(engine.getParent(3L, Some(1L))){ response =>
      response shouldBe Some(1L)
    }
  }

  test("should return account details for the given public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.fetchByPublicApiKey("publicApiKey1")){response =>
      response.fold(_ => fail, accountInfo => checkAccountInfomation(accountInfo))
    }
  }

  test("should return error for invalid public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    val publicApiKey = engine.fetchByPublicApiKey("945-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(publicApiKey) { res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("should return error for empty public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    val publicApiKey = engine.fetchByPublicApiKey("")
    whenReady(publicApiKey) { res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.AccountNotFound.id, _ => fail)
    }
  }

  test("fetchByPublicApiKeyV2: should return account details for the given public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)

    whenReady(engine.fetchByPublicApiKeyV2("09-16ca6193-4149-456b-ae00-00fdad2437c6")){response =>
      response.fold(_ => fail, a => {
        a.accountId shouldBe "1"
        a.accountName shouldBe "accountName"
        a.industry shouldBe Industry("industry sector", "industry description")
        a.isInternal shouldBe false
        a.watchlistPreference shouldBe WatchlistPreference(
          environmentId = 100,
          exactDoB = true,
          dobAndName = false,
          matchScore = 90,
          categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
        )
        a.roles shouldBe Set(
          BusinessUserRoles.WATCHLIST.id, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id, BusinessUserRoles.ECBSV.id, BusinessUserRoles.InternationalExpansionEnabled.id
        )
        a.primaryFraudModel shouldBe None
        a.fraudModels shouldBe Set.empty
        checkEnvironment(a.environment)
        checkAccessCredentialsForActiveKey(a.environment.accessCredentials)
        a.kycPreferences shouldBe KycPreferences(
          exactDob = Some(true),
          dobMatchLogic = None,
          ssnExactMatch = true,
          nationalIdMatchLogic = Some(exact),
          creator = None,
          addressMatchLogic = None
        )
      })
    }
  }

  test("fetchByPublicApiKeyV2: should return error for invalid public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    val publicApiKey = engine.fetchByPublicApiKeyV2("945-16ca6193-4149-456b-ae00-00fdad2437c6")
    whenReady(publicApiKey) { res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.InvalidPublicApiKey.id, _ => fail)
    }
  }

  test("fetchByPublicApiKeyV2: should return error for empty public api key") {
    val dataSource = buildDataSource(Some(dbName))
    val engine = buildEngine(dataSource)
    val publicApiKey = engine.fetchByPublicApiKeyV2("")
    whenReady(publicApiKey) { res =>
      res shouldBe 'left
      res.fold(_.code shouldBe ExceptionCodes.InvalidPublicApiKey.id, _ => fail)
    }
  }

  private def checkAccountInfomation(accountInformation: AccountInformation): Unit = {
    accountInformation.accountId shouldBe "1"
    accountInformation.accountName shouldBe "accountName"
    accountInformation.industry shouldBe Industry("industry sector", "industry description")
    accountInformation.isInternal shouldBe false
    accountInformation.watchlistPreference shouldBe WatchlistPreference(
      environmentId = 100,
      exactDoB = true,
      dobAndName = false,
      matchScore = 90,
      categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
    )
//    ecbsv, InternationalExpansionEnabled enabled at parent
    accountInformation.roles shouldBe Set(
      BusinessUserRoles.WATCHLIST.id, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id, BusinessUserRoles.ECBSV.id, BusinessUserRoles.InternationalExpansionEnabled.id
    )
    accountInformation.primaryFraudModel shouldBe None
    accountInformation.fraudModels shouldBe Set.empty
    checkEnvironment(accountInformation.environment)
    val includedWatchlistSources = Seq("Argentina Ministerio de Relaciones Exteriores y Culto Sanciones de la ONU",
      "Australia Autonomous Sanctions North Korea Designated Vessels",
      "Bangladesh Domestic Sanctions List")
    accountInformation.includedWatchlistSources shouldBe includedWatchlistSources
    accountInformation.ein shouldBe Some(EIN("*********"))
  }

  private def checkAccessCredentialsForActiveKey(accessCredentials: AccessCredentials): Unit = {
    accessCredentials shouldBe AccessCredentials(
      apiKey = "99-16ca6193-4149-456b-ae00-00fdad2437c6",
      secretKey = "secretKey",
      accessToken = "accessToken",
      accessTokenSecret = "accessTokenSecret",
      certificate = ""
    )
  }

  private def checkAccessCredentialsForNewKey(accessCredentials: AccessCredentials): Unit = {
    accessCredentials shouldBe AccessCredentials(
      apiKey = "88-16ca6193-4149-456b-ae00-00fdad2437c6",
      secretKey = "secretKey",
      accessToken = "accessToken",
      accessTokenSecret = "accessTokenSecret",
      certificate = ""
    )
  }

  private def checkEnvironment(environment: Environment): Unit ={
    environment.socialAccounts shouldBe Seq(
      SocialNetworkAppKeys(
        id = 1,
        provider = KeyProviders.FACEBOOK.toString,
        appkey = "application key",
        appsecret = "application secret",
        environment = 100,
        accountId = 1L
      )
    ).toVector

    environment.overallCache.foreach{ c =>
      c.id shouldBe 1L
      c.accountId shouldBe 1L
      formatter.format(c.date.toDate) shouldBe "2016-05-01"
    }
    environment.invidiualCache.size shouldBe 2
    val ic = environment.invidiualCache.find(_.id.equals(2L))
    ic.foreach{ i =>
      i.id shouldBe 2L
      i.identifier shouldBe "identifier2"
      i.accountId shouldBe 1L
      formatter.format(i.date.toDate) shouldBe "2016-06-03"
    }
  }
}
