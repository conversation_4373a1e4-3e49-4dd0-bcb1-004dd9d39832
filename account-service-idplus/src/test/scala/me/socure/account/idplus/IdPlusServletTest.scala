package me.socure.account.idplus

import me.socure.account.idplus.engine.IdPlusEngine
import me.socure.account.idplus.servlet.IdPlusServlet
import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.convertors.LegacyModelConverters
import me.socure.model._
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferences
import me.socure.model.mla.MLAFields
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.scalatest.mock.MockitoSugar
import org.scalatest.{BeforeAndAfter, FunSuiteLike}
import org.scalatra.test.scalatest.ScalatraSuite

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor, Future}

/**
  * Created by alexandre on 5/3/16.
  */
class IdPlusServletTest extends ScalatraSuite with FunSuiteLike with BeforeAndAfter with MockitoSugar {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private val engine = mock[IdPlusEngine]
  val servlet = new IdPlusServlet(engine)

  addServlet(servlet, "/*")

  before {
    Mockito.reset(engine)
  }

  private val essentialV3 = Essential(AccountStatus.ACTIVE,
    publicId = "1",
    accountId = "1",
    accountName = "accountName",
    industry = Industry("some sector", "some industry"),
    isInternal = true,
    environment = Environment(
      publicApiKeys = Seq.empty,
      id = 456,
      name = "environment",
      domain = Set("domain1", "domain2"),
      accessCredentials = AccessCredentials(
        apiKey = "api-key",
        secretKey = "secret key",
        accessToken = "access token",
        accessTokenSecret = "access token secret",
        certificate = ""
      ),
      socialAccounts = Seq(
        SocialNetworkAppKeys(
          id = 145,
          provider= "provider",
          appkey = "app key",
          appsecret = "app secret",
          environment = 566,
          accountId = 999
        )
      ),
      invidiualCache = Seq(
        AccountIndividualCache(
          id = 666L,
          date = new DateTime(0L, DateTimeZone.UTC),
          identifier = "identifier",
          accountId = 876L
        )
      ),
      overallCache = Some(AccountOverallCache(
        id = 234L,
        date = new DateTime(0L, DateTimeZone.UTC),
        accountId = 8765L))
    ), externalId = None, rootParentId = 1L)

  private val settingV3 = Setting(
    watchlistPreference = WatchlistPreferenceValueGenerator.aWatchlistPreference(),
    watchlistPreference_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreference(),
    watchlistPreferences_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreferences(),
    roles = Set(
      BusinessUserRoles.WATCHLIST.id
    ),
    primaryFraudModel = Some(FraudModel(
      id = 123,
      identifier = "identifier",
      name = "name",
      url = "url",
      version = "123",
      createdDate = new DateTime(0L, DateTimeZone.UTC),
      lastUpdated = new DateTime(0L, DateTimeZone.UTC)
    )),
    fraudModels = Set(
      FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L, DateTimeZone.UTC),
        lastUpdated = new DateTime(0L, DateTimeZone.UTC)
      )
    ),
    kycPreferences = KycPreferences(exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = true),
    subscriptionTypeIds = Seq(),
    includedWatchlistSources = Seq(),
    dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
    consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
    mlaField = MLAFields("", ""),
    ein = None,
    webhookNotificationPreferences = Seq()
  )

  private val accountInformationV3 = AccountInformationV3(essential = essentialV3, setting = Some(settingV3))

  private val accountInformationV2 = AccountInformation(
    active = true,
    publicId = "1",
    accountId = "1",
    accountName = "accountName",
    industry = Industry("some sector", "some industry"),
    isInternal = true,
    environment = Environment(
      publicApiKeys = Seq.empty,
      id = 456,
      name = "environment",
      domain = Set("domain1", "domain2"),
      accessCredentials = AccessCredentials(
        apiKey = "api-key",
        secretKey = "secret key",
        accessToken = "access token",
        accessTokenSecret = "access token secret",
        certificate = ""
      ),
      socialAccounts = Seq(
        SocialNetworkAppKeys(
          id = 145,
          provider= "provider",
          appkey = "app key",
          appsecret = "app secret",
          environment = 566,
          accountId = 999
        )
      ),
      invidiualCache = Seq(
        AccountIndividualCache(
          id = 666L,
          date = new DateTime(0L, DateTimeZone.UTC),
          identifier = "identifier",
          accountId = 876L
        )
      ),
      overallCache = Some(AccountOverallCache(
        id = 234L,
        date = new DateTime(0L, DateTimeZone.UTC),
        accountId = 8765L))
    ),
    watchlistPreference = WatchlistPreferenceValueGenerator.aWatchlistPreference(),
    watchlistPreference_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreference(),
    watchlistPreferences_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreferences(),
    roles = Set(
      BusinessUserRoles.WATCHLIST.id
    ),
    primaryFraudModel = Some(FraudModel(
      id = 123,
      identifier = "identifier",
      name = "name",
      url = "url",
      version = "123",
      createdDate = new DateTime(0L, DateTimeZone.UTC),
      lastUpdated = new DateTime(0L, DateTimeZone.UTC)
    )),
    fraudModels = Set(
      FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L, DateTimeZone.UTC),
        lastUpdated = new DateTime(0L, DateTimeZone.UTC)
      )
    ),
    kycPreferences = KycPreferences(exactDob = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      ssnExactMatch = true),
    subscriptionTypeIds = Seq(),
    includedWatchlistSources = Seq(),
    dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
    consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
    externalId = None,
    mlaField = MLAFields("", ""),
    ein = None,
    webhookNotificationPreferences = Seq()
  )

  test("should return account not found") {

    Mockito.when(engine.fetchByApiKey("1234")).thenReturn(Future.successful(Left(ErrorResponseFactory.get(AccountNotFound))))
    get("/fetch_account_information/1234") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":100,"message":"The account does not exist"}}"""
    }
  }

  test("should return properly when an exception is thrown") {

    Mockito.when(engine.fetchByApiKey("1234")).thenReturn(Future.failed(new Exception("a message")))
    get("/fetch_account_information/1234") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":199,"message":"a message"}}"""
    }
  }

  test("should return properly with success") {
    Mockito.when(engine.fetchByApiKey("1234")).thenReturn(Future.successful(Right(accountInformationV2)))
    get("/fetch_account_information_v2/1234") {
      status should equal(200)
      body.decodeJson[Response[AccountInformation]].data shouldBe accountInformationV2
    }
  }

  test("should return properly with success V3") {
    Mockito.when(engine.fetchByApiKeyV3("1234")).thenReturn(Future.successful(Right(accountInformationV3)))
    get("/fetch_account_information_v3/1234") {
      validate[Response[AccountInformationV3]](status, 200, body, Response(ResponseStatus.Ok, data = accountInformationV3))
    }
  }

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  test("should return properly with success - legacy") {

    val accountInformationLegacy = LegacyModelConverters.toAccountInformationLegacy(accountInformationV2)

    Mockito.when(engine.fetchByApiKey("1234")).thenReturn(Future.successful(Right(accountInformationV2)))
    get("/fetch_account_information/1234") {
      status should equal(200)
      body.decodeJson[Response[AccountInformationLegacy]].data shouldBe accountInformationLegacy
    }
  }

  test("should return account not found for invalid public account api key") {
    val expected = ErrorResponse(AccountNotFound.id, AccountNotFound.description)
    Mockito.when(engine.fetchByPublicApiKey("1234")).thenReturn(Future.successful(Left(ErrorResponseFactory.get(AccountNotFound))))
    get("/fetch_account_information_by_public_api_key/1234") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("should return properly when an exception is thrown for invalid public account api key") {
    val expected = ErrorResponse(UnknownError.id, "a message")
    Mockito.when(engine.fetchByPublicApiKey("1234")).thenReturn(Future.failed(new Exception("a message")))
    get("/fetch_account_information_by_public_api_key/1234") {
      validate[Response[ErrorResponse]](status, 400, body, Response(ResponseStatus.Error, expected))
    }
  }

  test("should return properly with success for public api key") {
    Mockito.when(engine.fetchByPublicApiKey("1234")).thenReturn(Future.successful(Right(accountInformationV2)))
    get("/fetch_account_information_by_public_api_key/1234") {
      validate[Response[AccountInformation]](status, 200, body, Response(ResponseStatus.Ok, data = accountInformationV2))
    }
  }

  test("should return invalid public account id") {

    Mockito.when(engine.fetchByPublicId("abcd")).thenReturn(Future.successful(Left(ErrorResponseFactory.get(InvalidPublicAccountId))))
    get("/fetch_account_information_by_publicId/abcd") {
      status should equal(400)
      body shouldBe """{"status":"error","data":{"code":586,"message":"Invalid Public AccountId"}}"""
    }
  }

  test("should return DV account properly with success") {
    val dvAccount = DocvAccountInformationValueGenerator.anDocvAccountInformation()
    Mockito.when(engine.fetchByPublicId("abcd")).thenReturn(Future.successful(Right(dvAccount)))
    get("/fetch_account_information_by_publicId/abcd") {
      status should equal(200)
      body.decodeJson[Response[DocvAccountInformation]].data shouldBe dvAccount
    }
  }

  def validate[T : Manifest](actualStatus : Int, expectedStatus : Int, body : String, expected : T): Unit = {
    status should equal(expectedStatus)
    val actual = body.decodeJson[T]
    actual shouldBe expected
  }
}
