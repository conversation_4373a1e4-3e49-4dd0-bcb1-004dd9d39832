package me.socure.account.idplus.servlet

import me.socure.account.idplus.engine.IdPlusEngine
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.hmac.verifier.{AuthenticationSupport, HMACHttpVerifier}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.ExecutionContext


class IdPlusV2Servlet(engine: IdPlusEngine, val hmacVerifier: HMACHttpVerifier)
                                  (implicit val executor : ExecutionContext)
                                  extends BaseScalatraServlet
                                  with FutureSupport
                                  with AuthenticationSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats
  override val logger = LoggerFactory.getLogger(getClass)

  before() {
    contentType = formats("json")
    validateRequest()
  }

  get("/accounts/publicapikeys/:publicApiKey"){
    val publicApiKey = params("publicApiKey")
    ScalatraResponseFactory.get(
      engine.fetchByPublicApiKeyV2(publicApiKey = publicApiKey)
    )
  }
}
