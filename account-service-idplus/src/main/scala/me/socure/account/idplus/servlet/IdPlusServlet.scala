package me.socure.account.idplus.servlet

import me.socure.account.idplus.engine.IdPlusEngine
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.servlet.ScalatraResponseFactory
import me.socure.constants.JsonFormats
import me.socure.convertors.LegacyModelConverters
import me.socure.model.{AccountInformationLegacy, AccountInformation, AccountInformationV3, DocvAccountInformation, ErrorResponse}
import org.json4s.Formats
import org.scalatra.FutureSupport
import org.slf4j.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 4/29/16.
  */
class IdPlusServlet(engine: IdPlusEngine)(implicit val executor: ExecutionContext) extends BaseScalatraServlet with FutureSupport {

  override protected implicit def jsonFormats: Formats = JsonFormats.formats

  private val logger = LoggerFactory.getLogger(classOf[IdPlusServlet])

  private val metrics: Metrics = JavaMetricsFactory.get("account-service." + classOf[IdPlusServlet].getSimpleName)

  //TODO:DEPRECATED: Clean it up after dependent services are upgraded
  get("/fetch_account_information/:apiKey") {
    val futureResponse: Future[Either[ErrorResponse, AccountInformationLegacy]] =  {
      logger.info("Fetching data per API Key")
      val apiKey = params("apiKey")
      engine
        .fetchByApiKey(apiKey)
        .map(_.right.map(LegacyModelConverters.toAccountInformationLegacy))
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for apiKey", e)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/fetch_account_information_v2/:apiKey") {
    val futureResponse: Future[Either[ErrorResponse, AccountInformation]] =  {
      logger.info("Fetching data per API Key")
      val apiKey = params("apiKey")
      engine.fetchByApiKey(apiKey)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for apiKey", e)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/fetch_account_information_v3/:apiKey") {
    val futureResponse: Future[Either[ErrorResponse, AccountInformationV3]] =  {
      logger.info("Fetching data per API Key V3")
      val apiKey = params("apiKey")
      engine.fetchByApiKeyV3(apiKey)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for apiKey", e)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/fetch_account_information_by_publicId/:publicAccountId") {
    val futureResponse: Future[Either[ErrorResponse, DocvAccountInformation]] = {
      logger.info("Fetching data per public account id")
      val publicAccountId = params("publicAccountId")
      engine.fetchByPublicId(publicAccountId)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occurred fetching data for public account id", e)
        metrics.increment("fetchDVAccountInformation.exception", "class:" + e.getClass.getSimpleName)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/fetch_account_information_by_public_api_key/:publicApiKey") {
    val futureResponse: Future[Either[ErrorResponse, AccountInformation]] = {
      logger.info("Fetching data per Account Public API Key")
      val publicApiKey = params("publicApiKey")
      engine.fetchByPublicApiKey(publicApiKey)
    }

    futureResponse.onFailure {
      case e: Throwable =>
        logger.info("An error occured fetching data for publicApiKey", e)
        metrics.increment("fetchAccountInfoByPublicApiKey.exception", "class:" + e.getClass.getSimpleName)
    }

    ScalatraResponseFactory.get(futureResponse)
  }

  get("/account-information/public-apikey/:publicApiKey"){
    val publicApiKey = params("publicApiKey")
    ScalatraResponseFactory.get(
      engine.fetchByPublicApiKeyV3(publicApiKey = publicApiKey)
    )
  }

  get("/account-information/account-id/:accountId") {
    try {
      val accountId = params("accountId").toLong
      val environmentTypeId = params.get("envTypeId").map(_.toInt).getOrElse(1)

      ScalatraResponseFactory.get(
        engine.fetchByAccountIdV3(accountId, environmentTypeId)
      )
    } catch {
      case _: NumberFormatException =>
        halt(400, "Invalid account_id or envTypeId — must be numeric")
    }
  }
}
