package me.socure.account.idplus.engine

import me.socure.DaoAccount
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service._
import me.socure.account.validator.V2Helper
import me.socure.constants.attributes.AccountAttributeValue
import me.socure.convertors.AccountConvertors
import me.socure.model.AccountStatus.{AccountStatus, DELETED, INACTIVE}
import me.socure.constants.AccountTypes
import me.socure.constants.VendorConfigConstants.{Twilio, ApiKey => CApiKey, ServiceId => CServiceId}
import me.socure.model.account._
import me.socure.model.ein.{EIN, VendorSettings}
import me.socure.model.kyc.KycPreferences
import me.socure.model.mla.MLAFields
import me.socure.model.sai.SAIPreferences
import me.socure.model.{AccountInformation, AccountInformationV3, AccountStatus, BusinessUserRoles, DocvAccountInformation, ErrorResponse, Essential, ParentAccountDetails, Setting}
import me.socure.storage.slick.dao.{DaoAccountV2, DaoPublicApiKey}
import me.socure.storage.slick.tables.DtoAccountMLAField
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.accountcache.{DaoEnvironmentCache, DaoEnvironmentCacheIndividual, DtoEnvironmentCache, DtoEnvironmentCacheInvidiual}
import me.socure.storage.slick.tables.accountsocialkeys.{DaoTblEnvironmentSocialKeys, DtoEnvironmentSocialKeys}
import me.socure.storage.slick.tables.industry.{DaoTblIndustry, DtoIndustry}
import org.slf4j.{Logger, LoggerFactory}
import slick.driver.JdbcProfile

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 4/28/16.
  */
class IdPlusEngine(val profile: JdbcProfile,
                   accountPreference: AccountPreferencesService,
                   daoAccount: DaoAccount,
                   subscriptionService: SubscriptionService,
                   watchlistSourceService: WatchlistSourceService,
                   dvConfigurationService: DvConfigurationService,
                   mlaService: MLAService,
                   einService: EINService,
                   saiPreferencesService: SaiPreferencesService,
                   daoPublicApiKey: DaoPublicApiKey,
                   daoAccountV2: DaoAccountV2,
                   permissionsToBeOverriddenByChild: Set[Integer]
                  )(implicit ec: ExecutionContext)
  extends DaoTblAccount
  with DaoTblAccountAttribute
  with DaoTblAccountPermission
  with DaoTblEnvironment
  with DaoTblIndustry
  with DaoEnvironmentCache
  with DaoEnvironmentCacheIndividual
  with DaoTblEnvironmentSocialKeys
  with DaoTblApiKey{

  private val logger : Logger = LoggerFactory.getLogger(getClass)

  def fetchByPublicApiKey (publicApiKey : String) : Future[Either[ErrorResponse, AccountInformation]] = {
    daoAccount.fetchAccountByPublicApiKey(publicApiKey).flatMap {
      case Some(a) => getAccountInfo(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
      case _ =>
        logger.info(s"No account for given public api key $publicApiKey")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def fetchByPublicApiKeyV2(publicApiKey : String) : Future[Either[ErrorResponse, AccountInformation]] = {
    daoAccount.fetchAccountByPublicApiKeyV2(publicApiKey).flatMap {
      case Some(a) => getAccountInfo(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
      case _ =>
        logger.info(s"No account for given public api key $publicApiKey")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPublicApiKey)))
    }
  }

  def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {
    daoAccount.isAccountDeleted(apiKey) flatMap {
      case Some(_) => Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountDeleted)))
      case None =>
        daoAccount.fetchAccount(apiKey).flatMap {
          case Some(a) => getAccountInfo(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
          case _ =>
            logger.info(s"No account for given api key $apiKey")
            Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
        }
    }
  }

  def fetchByApiKeyV3(apiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {
    daoAccount.fetchAccountV3(apiKey).flatMap {
      case Some(a) => getAccountInfoV3(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
      case _ =>
        logger.info(s"No account for given api key $apiKey")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def fetchByPublicApiKeyV3(publicApiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {
    daoAccount.fetchAccountByPublicKeyV3(publicApiKey).flatMap {
      case Some(a) => getAccountInfoV3(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
      case _ =>
        logger.info(s"No account for given public api key $publicApiKey")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def fetchByAccountIdV3(accountId: Long, environmentTypeId: Int = 1): Future[Either[ErrorResponse, AccountInformationV3]] = {
    daoAccount.fetchAccountByAccountIdV3(accountId, environmentTypeId).flatMap {
      case Some(a) => getAccountInfoV3(account=a._1, environment=a._2, industry=a._3, apiKeys=a._4)
      case _ =>
        logger.info(s"No account for given Account Id $accountId or with environmentTypeId $environmentTypeId")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)))
    }
  }

  def fetchByPublicId(publicAccountId: String): Future[Either[ErrorResponse, DocvAccountInformation]] = {
    daoAccount.fetchAccountByPublicAccountId(publicAccountId).flatMap {
      case Some(a) => getDVAccountInfo(a)
      case _ =>
        logger.info(s"No account for given public accountId $publicAccountId")
        Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidPublicAccountId)))
    }
  }

  private def getWhitelistForAccount(account: DtoAccount, environment: DtoEnvironment): Future[Option[String]] = {
    //use current account's domain if configured, else use parent account's domain.
    environment.domain match {
      case d @ Some(_) => Future.successful(d)
      case None => account.parentId match {
        case Some(parentAccountId) => daoAccount.fetchEnvironmentByType(parentAccountId, environment.environmentType).map(_.flatten)
        case None => Future.successful(None)
      }
    }
  }

  private def buildIdPlusResponse(account: DtoAccount,
                                  industry: DtoIndustry,
                                  accountCache: Option[DtoEnvironmentCache],
                                  accountCacheIndividuals: Seq[DtoEnvironmentCacheInvidiual],
                                  accountKeys: Seq[DtoEnvironmentSocialKeys],
                                  accountRoles: Seq[DtoAccountPermission],
                                  accountAttributes : Seq[DtoAccountAttribute],
                                  environment : DtoEnvironment,
                                  apiKeys : Seq[DtoApiKey],
                                  envWhiteList: Option[String],
                                  watchlistPreference: WatchlistPreference,
                                  watchlistPreference_3_0: CAWatchlistPreference,
                                  watchlistPreferences_3_0: CAWatchlistPreferences,
                                  kycPreferences: KycPreferences,
                                  subscriptionTypeIds: Seq[Long],
                                  includedWatchlistSources: Seq[String],
                                  dvConfiguration: Map[String, DvConfiguration],
                                  consentReason: Int,
                                  publicApiKeys: Seq[DtoPublicApiKey],
                                  mlaFields: DtoAccountMLAField,
                                  ein: Option[EIN],
                                  webhookNotificationPreferences: Seq[WebhookNotificationPreference],
                                  rootParentId :Long,
                                  rootParentAccountHierarchy: Option[DtoAccountHierarchy]
                                 ): AccountInformation = {
    val parentAccountType = rootParentAccountHierarchy match {
      case Some(ah) => Some(ah.accountType)
      case _ => None
    }
    AccountInformation(
      active = account.isActive,
      publicId = account.publicId,
      accountId = account.accountId.toString,
      accountName = account.name,
      industry = AccountConvertors.getIndustry(industry),
      isInternal = account.isInternal,
      environment = AccountConvertors.getEnvironment(
        account,
        environment,
        apiKeys = apiKeys,
        key = accountKeys,
        cache = accountCacheIndividuals,
        overallCache = accountCache,
        envWhiteList,
        publicApiKeys),
      watchlistPreference = watchlistPreference,
      watchlistPreference_3_0 = watchlistPreference_3_0,
      watchlistPreferences_3_0 = watchlistPreferences_3_0,
      roles = accountRoles.map(AccountConvertors.getRole).toSet ++
              accountAttributes.flatMap(a => AccountConvertors.convertAttributeToRole(AccountAttributeValue(a.value.toInt))).toSet,
      primaryFraudModel = None,
      fraudModels = Set.empty,
      kycPreferences = kycPreferences,
      subscriptionTypeIds = subscriptionTypeIds,
      includedWatchlistSources = includedWatchlistSources,
      dvConfiguration = dvConfiguration,
      consentReason = consentReason,
      externalId = account.externalId,
      mlaField = MLAFields(memberNumber = mlaFields.memberNumber, securityCode = mlaFields.securityCode),
      ein = ein,
      webhookNotificationPreferences = webhookNotificationPreferences,
      rootParentIdOpt = Some(rootParentId),
      rootParentAccountType = parentAccountType
    )
  }

  private def buildIdPlusResponseV3(account: DtoAccount,
                                    industry: DtoIndustry,
                                    accountCache: Option[DtoEnvironmentCache],
                                    accountCacheIndividuals: Seq[DtoEnvironmentCacheInvidiual],
                                    accountKeys: Seq[DtoEnvironmentSocialKeys],
                                    accountRoles: Seq[DtoAccountPermission],
                                    accountAttributes : Seq[DtoAccountAttribute],
                                    environment : DtoEnvironment,
                                    apiKeys : Seq[DtoApiKey],
                                    envWhiteList: Option[String],
                                    watchlistPreference: WatchlistPreference,
                                    watchlistPreference_3_0: CAWatchlistPreference,
                                    watchlistPreferences_3_0: CAWatchlistPreferences,
                                    kycPreferences: KycPreferences,
                                    subscriptionTypeIds: Seq[Long],
                                    includedWatchlistSources: Seq[String],
                                    dvConfiguration: Map[String, DvConfiguration],
                                    consentReason: Int,
                                    publicApiKeys: Seq[DtoPublicApiKey],
                                    mlaFields: DtoAccountMLAField,
                                    ein: Option[EIN],
                                    webhookNotificationPreferences: Seq[WebhookNotificationPreference],
                                    rootParentId: Long,
                                    hierarchy: Option[DtoAccountHierarchy],
                                    rootParentAccountHierarchy: Option[DtoAccountHierarchy],
                                    saiPreferences: Option[SAIPreferences],
                                    vendorSettings: Option[VendorSettings]
                                 ): AccountInformationV3 = {

    val accountStatus: AccountStatus = if(account.isDeleted) AccountStatus.DELETED else if(account.isActive) AccountStatus.ACTIVE else AccountStatus.INACTIVE
    val hierarchyPath = hierarchy match {
      case Some(ah) => Some(ah.hierarchyPath)
      case _ => None
    }
    val parentAccountType = rootParentAccountHierarchy match {
      case Some(ah) => Some(ah.accountType)
      case _ => None
    }
    val essential: Essential = Essential(
      status = accountStatus,
      publicId = account.publicId,
      accountId = account.accountId.toString,
      accountName = account.name,
      industry = AccountConvertors.getIndustry(industry),
      externalId = account.externalId,
      isInternal = account.isInternal,
      environment = AccountConvertors.getEnvironment(
        account,
        environment,
        apiKeys = apiKeys,
        key = accountKeys,
        cache = accountCacheIndividuals,
        overallCache = accountCache,
        envWhiteList,
        publicApiKeys),
      rootParentId = rootParentId,
      hierarchyPath = hierarchyPath,
      rootParentAccountType = parentAccountType
    )
    accountStatus match {
      case DELETED | INACTIVE =>
        AccountInformationV3(essential=essential,None)
      case _  =>
        val setting: Setting = Setting(
          watchlistPreference = watchlistPreference,
          watchlistPreference_3_0 = watchlistPreference_3_0,
          watchlistPreferences_3_0 = watchlistPreferences_3_0,
          roles = accountRoles.map(AccountConvertors.getRole).toSet ++
            accountAttributes.flatMap(a => AccountConvertors.convertAttributeToRole(AccountAttributeValue(a.value.toInt))).toSet,
          primaryFraudModel = None,
          fraudModels = Set.empty,
          kycPreferences = kycPreferences,
          subscriptionTypeIds = subscriptionTypeIds,
          includedWatchlistSources = includedWatchlistSources,
          dvConfiguration = dvConfiguration,
          consentReason = consentReason,
          mlaField = MLAFields(memberNumber = mlaFields.memberNumber, securityCode = mlaFields.securityCode),
          ein = ein,
          webhookNotificationPreferences = webhookNotificationPreferences,
          saiPreferences = saiPreferences,
          vendorSettings = vendorSettings
        )
        AccountInformationV3(essential,Some(setting))
    }
  }

  private def getPublicApiKeys(publicApiKey: DtoPublicApiKey): PublicApiKey = {
    PublicApiKey(id = publicApiKey.id,
      environmentId = publicApiKey.environmentId,
      apiKey = publicApiKey.apiKey,
      status = publicApiKey.status,
      createdAt = publicApiKey.createdAt,
      updatedAt = publicApiKey.updatedAt,
      timeLeft = None)
  }

  private def getDVAccountInfo(account: DtoAccount): Future[Either[ErrorResponse, DocvAccountInformation]] = {
    val accountParent = getParent(account.accountId, account.parentId)
    val v2Provisioned = daoAccountV2.isAccountV2Provisioned(Set(account.accountId))

    val futureAccountPermissions = accountParent flatMap {
      case Some(parent) => daoAccount.fetchAccountPermission(parent) flatMap { parentPermissionsList => {
        //        override only for v2 sub accounts
        v2Provisioned flatMap {
          case false => Future.successful(parentPermissionsList)
          case true =>
            daoAccount.fetchAccountPermission(account.accountId) map { childPermissionsList =>
              parentPermissionsList.filterNot(permission => permissionsToBeOverriddenByChild.contains(permission.permission)) ++
                childPermissionsList.filter(permission => permissionsToBeOverriddenByChild.contains(permission.permission))
            }
        }
      }
      }
      case _ => daoAccount.fetchAccountPermission(account.accountId)
    }
    val futureAccountAttribute = daoAccount.getAccountAttributes(account.parentId.getOrElse(account.accountId))
    val futureParent = if (account.parentId.isDefined) {
      daoAccount.getAccount(account.parentId.get)
    } else {
      Future.successful(None)
    }
    for {
      accountPermission <- futureAccountPermissions
      accountAttributes <- futureAccountAttribute
      parentAcc <- futureParent
    } yield Right(
      DocvAccountInformation(
        active = account.isActive,
        publicId = account.publicId,
        accountId = account.accountId,
        isInternal = account.isInternal,
        parent = buildParentAccount(parentAcc),
        roles = accountPermission.map(AccountConvertors.getRole).toSet ++
          accountAttributes.flatMap(a => AccountConvertors.convertAttributeToRole(AccountAttributeValue(a.value.toInt))).toSet
      ))
  }

  private def fetchAccountPermissions(accountId:Long, parentAccountId: Option[Long]) = {
    daoAccount.fetchAccountPermission(accountId) flatMap {  permissions  =>
        if (permissions.exists(_.permission == BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id)) {
          Future.successful(permissions)
        } else {
          parentAccountId match {
            case Some(id: Long) =>
              val ecbsvAndMlaPermissions = permissions.filter(permission => V2Helper.getEcbsvAndMlaFlags.contains(permission.permission)).map(permission => permission.copy(accountId = id))
              daoAccount.fetchAccountPermission(id).map(
                parentAccountPermissions => (parentAccountPermissions ++ ecbsvAndMlaPermissions).distinct
              )
            case _ => Future.successful(permissions)
          }
        }
    }
  }

  private def buildParentAccount(parent: Option[DtoAccount]): Option[ParentAccountDetails] = {
    parent match {
      case Some(acc) => Some(ParentAccountDetails(
        publicId = acc.publicId,
        accountId = acc.accountId))
      case _ => None
    }
  }
  private def getAccountInfo(account: DtoAccount, environment: DtoEnvironment, industry: DtoIndustry, apiKeys: DtoApiKey): Future[Either[ErrorResponse, AccountInformation]] = {

    val accountParent = getParent(account.accountId, account.parentId)
    val v2Provisioned = daoAccountV2.isAccountV2Provisioned(Set(account.accountId))

    val futureAccountPermissions = accountParent flatMap {
      case Some(parent) => daoAccount.fetchAccountPermission(parent) flatMap { parentPermissionsList => {
//        override only for v2 sub accounts
        v2Provisioned flatMap {
          case false => Future.successful(parentPermissionsList)
          case true =>
            daoAccount.fetchAccountPermission(account.accountId) map { childPermissionsList =>
              parentPermissionsList.filterNot(permission => permissionsToBeOverriddenByChild.contains(permission.permission)) ++
                childPermissionsList.filter(permission => permissionsToBeOverriddenByChild.contains(permission.permission))
            }
        }
      }
      }
      case _ => daoAccount.fetchAccountPermission(account.accountId)
    }
    val futureAccountAttribute = daoAccount.getAccountAttributes(account.parentId.getOrElse(account.accountId))
    val futureAccountCache = daoAccount.fetchAccountCache(environment.id)
    val futureAccountCacheIndividual = daoAccount.fetchAccountCacheIndividuals(environment.id)
    val futureAccountKeys = daoAccount.fetchAccountkeys(environment.id)
    val futureEnvironmentForWhitelist = getWhitelistForAccount(account, environment)
    val futureWatchlistPreference = accountPreference.getWatchlistPreference(environment.id).map(_.right.get)
    val futureCAWatchlistPreference = accountPreference.getCAWatchListPreference(environment.id, false).map(_.right.get)
    val futureCAWatchlistPreferences = accountPreference.getCAWatchListPreferences(environment.id, false).map(_.right.get)
    val futureKycPreferences = accountPreference.getKycPreference(environmentId = environment.id).map(_.right.get)
    val futureSubscriptionTypeIds = subscriptionService.listSubscriptions(account.parentId.getOrElse(account.accountId)).map(_.right.get)
    val futureIncludedWatchlistSources = watchlistSourceService.includedWatchlistSourcesNames(environment.id).map(_.right.get)
    val futureDvConfiguration: Future[Map[String, DvConfiguration]] = dvConfigurationService.listDvConfigurationByEnvironment(environment.id).map(_.right.get)
    val eventualPublicApiKeys: Future[Seq[DtoPublicApiKey]] = daoPublicApiKey.fetchByEnvironmentId(environment.id)
    val futureConsentReason = daoAccount.getConsentId(account.accountId).map{
      case Some(dto) => dto.consentId
      case None => 1
    }

    val futureMLAFields = mlaService.fetchMLAFields(account.parentId.getOrElse(account.accountId))

    //  fetch from sub account if v2
    val futureEIN = v2Provisioned flatMap {
      case false => einService.fetchEIN(account.parentId.getOrElse(account.accountId)).map(_.right.get)
      case true => einService.fetchEIN(account.accountId).map(_.right.get)
    }

    val futureWlAndDvNotificationPreference = subscriptionService.getEnvSubscriptionRegistryDetails(envId = environment.id).map(_.right.get)
    val rootParentIdF = getRootParent(account.accountId)
    for {
      accountPermissions <- futureAccountPermissions
      accountAttributes <- futureAccountAttribute
      accountCache <- futureAccountCache
      accountCacheIndividuals <- futureAccountCacheIndividual
      accountKeys <- futureAccountKeys
      environmentForWhitelist <- futureEnvironmentForWhitelist
      watchlistPreference <- futureWatchlistPreference
      watchlistPreference_3_0 <- futureCAWatchlistPreference
      watchlistPreferences_3_0 <- futureCAWatchlistPreferences
      kycPreferences <- futureKycPreferences
      subscriptionTypeIds <- futureSubscriptionTypeIds
      includedWatchlistSources <- futureIncludedWatchlistSources
      dvConfiguration <- futureDvConfiguration
      consentReason <- futureConsentReason
      publicApiKeys <- eventualPublicApiKeys
      mlaFields <- futureMLAFields
      ein <- futureEIN
      notificationDetails <- futureWlAndDvNotificationPreference
      rootParentId <- rootParentIdF
      rootParentAccountHierarchy <- daoAccountV2.getAccountHierarchyByAccountId(rootParentId)

    } yield Right(buildIdPlusResponse(
      account = account,
      industry = industry,
      accountCache = accountCache,
      accountCacheIndividuals = accountCacheIndividuals,
      accountKeys = accountKeys,
      accountRoles = accountPermissions,
      accountAttributes = accountAttributes,
      environment = environment,
      apiKeys = List(apiKeys),
      envWhiteList = environmentForWhitelist,
      watchlistPreference = watchlistPreference,
      watchlistPreference_3_0 = watchlistPreference_3_0,
      watchlistPreferences_3_0 = watchlistPreferences_3_0,
      kycPreferences = kycPreferences,
      subscriptionTypeIds = subscriptionTypeIds,
      includedWatchlistSources = includedWatchlistSources,
      dvConfiguration = dvConfiguration,
      consentReason = consentReason,
      publicApiKeys = publicApiKeys,
      mlaFields = mlaFields,
      ein = ein,
      webhookNotificationPreferences = notificationDetails,
      rootParentId = rootParentId,
      rootParentAccountHierarchy = rootParentAccountHierarchy
    ))
  }

  private def getAccountInfoV3(account: DtoAccount, environment: DtoEnvironment, industry: DtoIndustry, apiKeys: DtoApiKey): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val accountParent = getParent(account.accountId, account.parentId)
    val rootParentId = getRootParent(account.accountId)
    val v2Provisioned = daoAccountV2.isAccountV2Provisioned(Set(account.accountId))

    val futureAccountPermissions = accountParent flatMap {
      case Some(parent) => daoAccount.fetchAccountPermission(parent) flatMap { parentPermissionsList => {
//        override only for v2 sub accounts
        v2Provisioned flatMap {
          case false => Future.successful(parentPermissionsList)
          case true =>
            daoAccount.fetchAccountPermission(account.accountId) map { childPermissionsList =>
              parentPermissionsList.filterNot(permission => permissionsToBeOverriddenByChild.contains(permission.permission)) ++
                childPermissionsList.filter(permission => permissionsToBeOverriddenByChild.contains(permission.permission))
            }
        }
      }
      }
      case _ => daoAccount.fetchAccountPermission(account.accountId)
    }
    val futureAccountAttribute = daoAccount.getAccountAttributes(account.parentId.getOrElse(account.accountId))
    val futureAccountCache = daoAccount.fetchAccountCache(environment.id)
    val futureAccountCacheIndividual = daoAccount.fetchAccountCacheIndividuals(environment.id)
    val futureAccountKeys = daoAccount.fetchAccountkeys(environment.id)
    val futureEnvironmentForWhitelist = getWhitelistForAccount(account, environment)
    val futureWatchlistPreference = accountPreference.getWatchlistPreference(environment.id).map(_.right.get)
    val futureCAWatchlistPreference = accountPreference.getCAWatchListPreference(environment.id, false).map(_.right.get)
    val futureCAWatchlistPreferences = accountPreference.getCAWatchListPreferences(environment.id, false).map(_.right.get)
    val futureKycPreferences = accountPreference.getKycPreference(environmentId = environment.id).map(_.right.get)
    val futureSubscriptionTypeIds = subscriptionService.listSubscriptions(account.parentId.getOrElse(account.accountId)).map(_.right.get)
    val futureIncludedWatchlistSources = watchlistSourceService.includedWatchlistSourcesNames(environment.id).map(_.right.get)
    val futureDvConfiguration: Future[Map[String, DvConfiguration]] = dvConfigurationService.listDvConfigurationByEnvironment(environment.id).map(_.right.get)
    val eventualPublicApiKeys: Future[Seq[DtoPublicApiKey]] = daoPublicApiKey.fetchByEnvironmentId(environment.id)
    val futureConsentReason = daoAccount.getConsentId(account.accountId).map{
      case Some(dto) => dto.consentId
      case None => 1
    }

    val futureMLAFields = mlaService.fetchMLAFields(account.parentId.getOrElse(account.accountId))
    //  fetch from sub account if v2
    val futureEIN = v2Provisioned flatMap {
      case false => einService.fetchEIN(account.parentId.getOrElse(account.accountId)).map(_.right.get)
      case true => einService.fetchEIN(account.accountId).map(_.right.get)
    }
    val futureWlAndDvNotificationPreference = subscriptionService.getEnvSubscriptionRegistryDetails(envId = environment.id).map(_.right.get)
    val saiPreferencesFuture: Future[Option[SAIPreferences]] =
      saiPreferencesService.fetchSaiPreferences(accountId = account.accountId).map {
        case Right(saiPreferences) => saiPreferences
        case Left(_) => None
      }
    val futureLookupApiKey = einService.fetchLookupKey(account.accountId).map(_.right.get)
    val fetchLookupKeyServiceSid = einService.fetchLookupKeyServiceSid(account.accountId).map(_.right.get)

    def futureVendorSettings: Future[Option[VendorSettings]] = {
      val twilioSettings = Map.newBuilder[String, String]
      for {
        lookupApiKeyOpt <- futureLookupApiKey
        lookupKeyServiceSidOpt  <- fetchLookupKeyServiceSid
      } yield {
        lookupApiKeyOpt match {
          case Some(apiKey) => twilioSettings += CApiKey -> apiKey.value
          case None => logger.info("LookupApiKey Not found")
        }
        lookupKeyServiceSidOpt match {
          case Some(lookupKeyServiceSidInfo) =>
            twilioSettings ++= Seq(
              CApiKey -> lookupKeyServiceSidInfo.apiKey,
              CServiceId -> lookupKeyServiceSidInfo.serviceId
            )
          case None =>
            logger.info("OTP Workflow Settings Not found")
        }
        if (twilioSettings.result().nonEmpty) Some(VendorSettings(Map(Twilio -> twilioSettings.result()))) else None
      }
    }

    for {
      accountPermissions <- futureAccountPermissions
      accountAttributes <- futureAccountAttribute
      accountCache <- futureAccountCache
      accountCacheIndividuals <- futureAccountCacheIndividual
      accountKeys <- futureAccountKeys
      environmentForWhitelist <- futureEnvironmentForWhitelist
      watchlistPreference <- futureWatchlistPreference
      watchlistPreference_3_0 <- futureCAWatchlistPreference
      watchlistPreferences_3_0 <- futureCAWatchlistPreferences
      kycPreferences <- futureKycPreferences
      subscriptionTypeIds <- futureSubscriptionTypeIds
      includedWatchlistSources <- futureIncludedWatchlistSources
      dvConfiguration <- futureDvConfiguration
      consentReason <- futureConsentReason
      publicApiKeys <- eventualPublicApiKeys
      mlaFields <- futureMLAFields
      ein <- futureEIN
      notificationDetails <- futureWlAndDvNotificationPreference
      rootParentId <- rootParentId
      rootParentAccountHierarchy <- daoAccountV2.getAccountHierarchyByAccountId(rootParentId)
      hierarchy <- daoAccountV2.getAccountHierarchyByAccountId(account.accountId)
      saiPreferences <- saiPreferencesFuture
      vendorSettings <- futureVendorSettings
    } yield Right(buildIdPlusResponseV3(
      account = account,
      industry = industry,
      accountCache = accountCache,
      accountCacheIndividuals = accountCacheIndividuals,
      accountKeys = accountKeys,
      accountRoles = accountPermissions,
      accountAttributes = accountAttributes,
      environment = environment,
      apiKeys = List(apiKeys),
      envWhiteList = environmentForWhitelist,
      watchlistPreference = watchlistPreference,
      watchlistPreference_3_0 = watchlistPreference_3_0,
      watchlistPreferences_3_0 = watchlistPreferences_3_0,
      kycPreferences = kycPreferences,
      subscriptionTypeIds = subscriptionTypeIds,
      includedWatchlistSources = includedWatchlistSources,
      dvConfiguration = dvConfiguration,
      consentReason = consentReason,
      publicApiKeys = publicApiKeys,
      mlaFields = mlaFields,
      ein = ein,
      webhookNotificationPreferences = notificationDetails,
      rootParentId = rootParentId,
      hierarchy = hierarchy,
      rootParentAccountHierarchy = rootParentAccountHierarchy,
      saiPreferences = saiPreferences,
      vendorSettings = vendorSettings
    ))
  }

  def getParent(accountId: Long, currentParent: Option[Long]): Future[Option[Long]] = {
    daoAccountV2.isAccountV2Provisioned(Set(accountId)) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) map {
          case Some(ah) => V2Helper.getRootParent(ah.hierarchyPath)
          case _ =>
            logger.info(s"No hierarchy path found for account($accountId), The accountId will be used as the parent.")
            None
        }
      case _ =>
        Future.successful(currentParent)
    }
  }

  def getRootParent(accountId: Long): Future[Long] = {
    daoAccountV2.isAccountPermissionProvisioned(accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id) flatMap {
      case true =>
        daoAccountV2.getAccountHierarchyByAccountId(accountId) flatMap{
          //        in v2 account management, the account id's hierarchy are stored as 1/2/3, so here if we want root parent of 2 or 3, by below logic we will get 1
          case Some(ah) =>
            val accountIds = ah.hierarchyPath.split("/").map(_.toLong)
            val tmpRootParent = if (accountIds.nonEmpty) accountIds.head else accountId

            val rootParent = daoAccountV2.getAccountHierarchyByAccountId(tmpRootParent) map{
              case Some(tempHierarchy) =>
                if(tempHierarchy.accountType==AccountTypes.RESELLER.id){
                  //go one step above
                  if(accountIds.length>1){
                    accountIds(1)
                  }else{
                    accountId
                  }
                }else{
                  tmpRootParent
                }
              case _ => accountId
            }
            rootParent
          case _ => Future.successful(accountId)
        }
      case _ =>
        daoAccount.getParentAccountId(accountId) map { a =>
          a.flatten match {
            case Some(x) => x
            case _ => accountId
          }
        }
    }
  }
}
