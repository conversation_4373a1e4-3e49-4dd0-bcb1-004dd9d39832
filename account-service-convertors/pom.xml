<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <parent>
    <artifactId>account-service</artifactId>
    <groupId>me.socure</groupId>
    <version>${revision}</version>
  </parent>

  <modelVersion>4.0.0</modelVersion>
  <artifactId>account-service-convertors</artifactId>
  <version>${revision}</version>


  <dependencies>
    <dependency>
      <groupId>me.socure</groupId>
      <artifactId>account-service-util</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>me.socure</groupId>
      <artifactId>account-service-storage</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>me.socure</groupId>
      <artifactId>account-service-model</artifactId>
      <version>${project.version}</version>
    </dependency>

    <dependency>
      <groupId>org.scalatest</groupId>
      <artifactId>scalatest_${sc.ver}</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.scalatest</groupId>
        <artifactId>scalatest-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>com.google.cloud.tools</groupId>
        <artifactId>jib-maven-plugin</artifactId>
        <version>3.2.1</version>
        <configuration>
            <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
