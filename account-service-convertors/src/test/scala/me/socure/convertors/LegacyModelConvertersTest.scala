package me.socure.convertors

import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferencesValueGenerator
import me.socure.model.mla.MLAFields
import me.socure.model.{AccountConsentReasons, AccountInformation, AccountInformationLegacy, BusinessUserRoles, Industry}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{FreeSpec, Matchers}

class LegacyModelConvertersTest extends FreeSpec with Matchers {
  "should convert to legacy account information properly" in {
    val wlPreference = WatchlistPreferenceValueGenerator.aWatchlistPreference()
    val caWLPreference = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreference()
    val caWLPreferences = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreferences()
    val dvConfig = DvConfigurationValueGenerator.aDvConfigurationValue()
    val accountInformationV2 = AccountInformation(
      active = true,
      publicId = "1",
      accountId = "1",
      accountName = "accountName",
      industry = Industry("some sector", "some industry"),
      isInternal = true,
      environment = Environment(
        publicApiKeys = Seq.empty,
        id = 456,
        name = "environment",
        domain = Set("domain1", "domain2"),
        accessCredentials = AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret key",
          accessToken = "access token",
          accessTokenSecret = "access token secret",
          certificate = ""
        ),
        socialAccounts = Seq(
          SocialNetworkAppKeys(
            id = 145,
            provider = "provider",
            appkey = "app key",
            appsecret = "app secret",
            environment = 566,
            accountId = 999
          )
        ),
        invidiualCache = Seq(
          AccountIndividualCache(
            id = 666L,
            date = new DateTime(0L, DateTimeZone.UTC),
            identifier = "identifier",
            accountId = 876L
          )
        ),
        overallCache = Some(AccountOverallCache(
          id = 234L,
          date = new DateTime(0L, DateTimeZone.UTC),
          accountId = 8765L))
      ),
      watchlistPreference = wlPreference,
      watchlistPreference_3_0 = caWLPreference,
      watchlistPreferences_3_0 = caWLPreferences,
      roles = Set(
        BusinessUserRoles.WATCHLIST.id
      ),
      primaryFraudModel = Some(FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L, DateTimeZone.UTC),
        lastUpdated = new DateTime(0L, DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 123,
          identifier = "identifier",
          name = "name",
          url = "url",
          version = "123",
          createdDate = new DateTime(0L, DateTimeZone.UTC),
          lastUpdated = new DateTime(0L, DateTimeZone.UTC)
        )
      ),
      kycPreferences = KycPreferencesValueGenerator.aKycPreferences(),
      subscriptionTypeIds = Seq(),
      includedWatchlistSources = Seq(),
      dvConfiguration = dvConfig,
      consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
      externalId = None,
      mlaField = MLAFields("", ""),
      ein = None,
      webhookNotificationPreferences = Seq()
    )

    val legacyAccountInformation = AccountInformationLegacy(
      active = true,
      accountId = "1",
      accountName = "accountName",
      industry = Industry("some sector", "some industry"),
      isInternal = true,
      environment = Environment(
        publicApiKeys = Seq.empty,
        id = 456,
        name = "environment",
        domain = Set("domain1", "domain2"),
        accessCredentials = AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret key",
          accessToken = "access token",
          accessTokenSecret = "access token secret",
          certificate = ""
        ),
        socialAccounts = Seq(
          SocialNetworkAppKeys(
            id = 145,
            provider = "provider",
            appkey = "app key",
            appsecret = "app secret",
            environment = 566,
            accountId = 999
          )
        ),
        invidiualCache = Seq(
          AccountIndividualCache(
            id = 666L,
            date = new DateTime(0L, DateTimeZone.UTC),
            identifier = "identifier",
            accountId = 876L
          )
        ),
        overallCache = Some(AccountOverallCache(
          id = 234L,
          date = new DateTime(0L, DateTimeZone.UTC),
          accountId = 8765L))
      ),
      watchlistPreference = wlPreference,
      roles = Set(
        "ROLE_WATCHLIST"
      ),
      primaryFraudModel = Some(FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L, DateTimeZone.UTC),
        lastUpdated = new DateTime(0L, DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 123,
          identifier = "identifier",
          name = "name",
          url = "url",
          version = "123",
          createdDate = new DateTime(0L, DateTimeZone.UTC),
          lastUpdated = new DateTime(0L, DateTimeZone.UTC)
        )
      )
    )

    LegacyModelConverters.toAccountInformationLegacy(accountInformationV2) shouldBe legacyAccountInformation
  }
}
