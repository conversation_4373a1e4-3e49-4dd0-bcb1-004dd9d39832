package me.socure.convertors

import me.socure.common.clock.FakeClock
import me.socure.storage.slick.tables.account.DtoEnvironment
import me.socure.storage.slick.tables.user.role.DtoBusinessUserEnvironmentRole
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{FunSuite, Matchers}

/**
  * Created by alexand<PERSON> on 2/26/17.
  */
class DashboardEnvironmentConvertorsTest extends FunSuite with Matchers {

  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
  private def buildDtoEnvironment(envId: Int, envType: Int) = {
    DtoEnvironment(id = envId, secretKey = "", accessToken = "", accessTokenSecret = "", domain = None, accountId = 1, environmentType = envType, updatedAt = Some(clock.now()))
  }

  private val productionEnvironment = buildDtoEnvironment(envId = 1, envType = 1)
  private val developmentEnvironment = buildDtoEnvironment(envId = 2, envType = 2)

  private def buildRoles(envId: Option[Long], roleId: Int) = {
    DtoBusinessUserEnvironmentRole(environmentId = envId, businessUserId = 1, role = roleId)
  }

  test("should construct DashboardEnvironment when both envs have roles") {
    val environments = Seq(productionEnvironment, developmentEnvironment)
    val roles = Seq(
      buildRoles(envId = Some(1), roleId = 1),
      buildRoles(envId = Some(2), roleId = 2)
    )

    val actual = DashboardEnvironmentConvertors.dashboardEnvironments(isUserAdmin = false, roles, environments)
    actual should have size 2
  }

  test("should construct DashboardEnvironment when only one env has roles") {
    val environments = Seq(productionEnvironment, developmentEnvironment)
    val roles = Seq(
      buildRoles(envId = Some(1), roleId = 1),
      buildRoles(envId = Some(1), roleId = 2)
    )

    val actual = DashboardEnvironmentConvertors.dashboardEnvironments(isUserAdmin = false, roles, environments)
    actual should have size 1
  }
}
