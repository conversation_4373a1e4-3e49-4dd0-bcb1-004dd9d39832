package me.socure.convertors


import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.constants.EnvironmentConstants.EnvironmentConstants
import me.socure.constants.{DashboardUserPermissions, EnvironmentConstants}
import me.socure.model.BusinessUserRoles
import me.socure.model.account.{ApiKeyStatus, Subscription}
import me.socure.model.account.ApiKeyStatus.ApiKeyStatus
import me.socure.model.user.DashboardUserRole
import me.socure.model.user.authorization._
import me.socure.storage.slick.tables.account._
import org.joda.time.DateTime
import org.scalatest.{FreeSpec, Matchers}

import scala.util.Random

class AccountConvertorsTest extends FreeSpec with Matchers {
  "AccountConvertors" - {
    "should convert relevant details to AccountWithEnvironmentDetails object" in {

      val publicId = PublicIdGenerator.account().value

      val dtoAccount = DtoAccount(
        accountId = 2,
        name = "acc",
        industrySector = "IT",
        isInternal = false,
        isActive = true,
        parentId = Some(1),
        isDeleted = false,
        firstActivatedAt = Some(DateTime.now()),
        publicId = publicId,
        publicApiKey = "94-16ca6193-4149-456b-ae00-00fdad2437c6"
      )

      val dtoEnvironments = EnvironmentConstants.values.map(createEnv)
      val roles = Random.shuffle(BusinessUserRoles.values.map(_.id)).take(5)
      val dtoPermissions = roles.map(createPermission)
      val dtoApiKeys = Set(
        createApiKey(EnvironmentConstants.PRODUCTION_ENVIRONMENT, ApiKeyStatus.NEW),
        createApiKey(EnvironmentConstants.PRODUCTION_ENVIRONMENT, ApiKeyStatus.ACTIVE),
        createApiKey(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT, ApiKeyStatus.ACTIVE),
        createApiKey(EnvironmentConstants.SANDBOX_ENVIRONMENT, ApiKeyStatus.ACTIVE)
      )

      val expectedResult = AccountWithEnvironmentDetails(
        account = Account(
          id = 2,
          name = "acc",
          permission = roles,
          isActive = true,
          isInternal = false,
          Set(Subscription(1L, "Watchlist Monitoring"))
        ),
        environment = Set(
          EnvironmentSettings(
            id = EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
            name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.PRODUCTION_ENVIRONMENT.id}_${ApiKeyStatus.NEW.id}"
          ),
          EnvironmentSettings(
            id = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id,
            name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id}_${ApiKeyStatus.ACTIVE.id}"
          ),
          EnvironmentSettings(
            id = EnvironmentConstants.SANDBOX_ENVIRONMENT.id,
            name = EnvironmentConstants.SANDBOX_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.SANDBOX_ENVIRONMENT.id}_${ApiKeyStatus.ACTIVE.id}"
          )
        )
      )

      val actualResult = AccountConvertors.convertToAccountWithEnv(
        account = dtoAccount,
        environments = dtoEnvironments,
        permissions = dtoPermissions,
        apiKeys = dtoApiKeys,
        subscriptions = Set(Subscription(1L, "Watchlist Monitoring"))
      )

      actualResult shouldBe expectedResult
    }

    "should convert relevant details to AccountWithEnvironmentDetailsWithPublicId object" in {

      val publicId = PublicIdGenerator.account().value

      val dtoAccount = DtoAccount(
        accountId = 2,
        name = "acc",
        industrySector = "IT",
        isInternal = false,
        isActive = true,
        parentId = Some(1),
        isDeleted = false,
        firstActivatedAt = Some(DateTime.now()),
        publicId = publicId,
        publicApiKey = "94-16ca6193-4149-456b-ae00-00fdad2437c6"
      )

      val dtoEnvironments = EnvironmentConstants.values.map(createEnv)
      val roles = Random.shuffle(BusinessUserRoles.values.map(_.id)).take(5)
      val dtoPermissions = roles.map(createPermission)
      val dtoApiKeys = Set(
        createApiKey(EnvironmentConstants.PRODUCTION_ENVIRONMENT, ApiKeyStatus.NEW),
        createApiKey(EnvironmentConstants.PRODUCTION_ENVIRONMENT, ApiKeyStatus.ACTIVE),
        createApiKey(EnvironmentConstants.DEVELOPMENT_ENVIRONMENT, ApiKeyStatus.ACTIVE),
        createApiKey(EnvironmentConstants.SANDBOX_ENVIRONMENT, ApiKeyStatus.ACTIVE)
      )

      val expectedResult = AccountWithEnvironmentDetailsWithPublicId(
        account = AccountWithPublicId(
          id = 2,
          publicId = publicId,
          name = "acc",
          permission = roles,
          isActive = true,
          isInternal = false,
          subscriptions = Set(Subscription(1, "Watchlist Monitoring")),
          false
        ),
        environment = Set(
          EnvironmentSettings(
            id = EnvironmentConstants.PRODUCTION_ENVIRONMENT.id,
            name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.PRODUCTION_ENVIRONMENT.id}_${ApiKeyStatus.NEW.id}"
          ),
          EnvironmentSettings(
            id = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id,
            name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.id}_${ApiKeyStatus.ACTIVE.id}"
          ),
          EnvironmentSettings(
            id = EnvironmentConstants.SANDBOX_ENVIRONMENT.id,
            name = EnvironmentConstants.SANDBOX_ENVIRONMENT.toString,
            socureKey = s"api_key_${EnvironmentConstants.SANDBOX_ENVIRONMENT.id}_${ApiKeyStatus.ACTIVE.id}"
          )
        )
      )

      val actualResult = AccountConvertors.convertToAccountWithEnvWithPublicId(
        account = dtoAccount,
        environments = dtoEnvironments,
        permissions = dtoPermissions,
        apiKeys = dtoApiKeys,
        subscriptions = Set(Subscription(1, "Watchlist Monitoring"))
      )

      actualResult shouldBe expectedResult
    }
  }

  "Dashboard user roles has to be converted to permissions " in {
    AccountConvertors.toDashboardPermissions(
      Set(
        DashboardUserRole.USERS.id,
        DashboardUserRole.BATCH_JOB.id
      )
    ) shouldBe
      Set(
        DashboardUserPermissions.USERS_CREATE.id,
        DashboardUserPermissions.USERS_DELETE.id,
        DashboardUserPermissions.USERS_MODIFY.id,
        DashboardUserPermissions.USERS_VIEW.id,
        DashboardUserPermissions.BATCHJOB_CREATE.id,
        DashboardUserPermissions.BATCHJOB_DELETE.id,
        DashboardUserPermissions.BATCHJOB_MODIFY.id,
        DashboardUserPermissions.BATCHJOB_VIEW.id,
        DashboardUserPermissions.USERS_PROVISION.id,
        DashboardUserPermissions.BATCHJOB_PROVISION.id
      )
  }

  "Dashboard user roles has to be converted to permissions - Transactions " in {
    AccountConvertors.toDashboardPermissions(
      Set(
        DashboardUserRole.CREATE_TRANSACTION.id
      )) shouldBe
      Set(
        DashboardUserPermissions.TRANSACTIONS_CREATE.id
      )
  }


  "Dashboard user permissions should be converted back to Dashboard user roles" in {
    AccountConvertors.toDashboardUserRoles(
      Set(
        DashboardUserPermissions.USERS_VIEW.id,
        DashboardUserPermissions.ACCOUNTS_VIEW.id,
        DashboardUserPermissions.BATCHJOB_CREATE.id,
        DashboardUserPermissions.BATCHJOB_DELETE.id
      )
    ) shouldBe
      Set(
        DashboardUserRole.ACCOUNTS.id,
        DashboardUserRole.USERS.id,
        DashboardUserRole.BATCH_JOB.id
      )
  }

  "Dashboard user permissions should be converted back to Dashboard user roles - transactions" in {
    AccountConvertors.toDashboardUserRoles(
      Set(
        DashboardUserPermissions.TRANSACTIONS_CREATE.id,
        DashboardUserPermissions.TRANSACTIONS_VIEW.id
      )
    ) shouldBe
      Set(
        DashboardUserRole.CREATE_TRANSACTION.id,
        DashboardUserRole.LIST_TRANSACTION.id
      )
  }

  private def createEnv(environmentConstants: EnvironmentConstants): DtoEnvironment = {
    DtoEnvironment(
      id = environmentConstants.id,
      secretKey = "secret eky",
      accessToken = "access token",
      accessTokenSecret = "access token secret",
      domain = Some("domain"),
      accountId = 2,
      environmentType = environmentConstants.id.toLong,
      updatedAt = Some(DateTime.now())
    )
  }

  private def createPermission(permission: Int): DtoAccountPermission = {
    DtoAccountPermission(
      accountId = 2,
      permission = permission
    )
  }

  private def createApiKey(environmentConstants: EnvironmentConstants, apiKeyStatus: ApiKeyStatus): DtoApiKey = {
    DtoApiKey(
      id = environmentConstants.id,
      environmentId = environmentConstants.id.toLong,
      apiKey = s"api_key_${environmentConstants.id}_${apiKeyStatus.id}",
      status = apiKeyStatus,
      createdAt = DateTime.now(),
      updatedAt = DateTime.now(),
      label = None,
      lastUsedAt = None
    )
  }
}
