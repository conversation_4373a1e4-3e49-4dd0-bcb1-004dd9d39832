package me.socure.convertors

import me.socure.common.clock.RealClock
import me.socure.constants.EnvironmentConstants
import me.socure.constants.attributes.AccountAttributeValue
import me.socure.model.account.{AccountIdName, ApiKeyStatus, Subscription}
import me.socure.model.dashboardv2.DelegatedUserForm
import me.socure.model.superadmin.{DelegatedAdmin, UserActivationDetails}
import me.socure.model.user._
import me.socure.model.user.authorization._
import me.socure.model.{AccountUIConfiguration, BusinessUserRoles}
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.user._
import me.socure.storage.slick.tables.user.role.DtoBusinessUserEnvironmentRole
import org.joda.time.DateTime
import scalaz.syntax.std.boolean._
/**
  * Created by gopal on 10/05/16.
  */
object BusinessUserConvertors extends RealClock {

  def getUserAuth(bu: DtoBusinessUser,
                  account: DtoAccount,
                  roles: Seq[DtoBusinessUserEnvironmentRole],
                  environmentWithApiKeys: Map[DtoEnvironment, Seq[DtoApiKey]],
                  permissions: Seq[DtoAccountPermission],
                  subscriptions: Seq[Subscription],
                  accounts: Seq[AccountIdName],
                  dtoAccountUIConfigurationOpt: Option[DtoAccountUIConfiguration] = None,
                  passwordUpdatedAt: Option[DateTime],
                  passwordExpireAt: Option[DateTime]) : UserAuth = {

    val isUserAdmin = roles.map(_.role).contains(DashboardUserRole.ADMIN.id)
    val env = environmentWithApiKeys map { e =>
      val y = e._2 groupBy {_.status}
       val apiKeyObj = if (y.contains(ApiKeyStatus.NEW)) y.get(ApiKeyStatus.NEW) else y.get(ApiKeyStatus.ACTIVE)
       (DtoEnvironment(e._1.id,
         secretKey = e._1.secretKey,
         accessToken = e._1.accessToken,
         accessTokenSecret = e._1.accessTokenSecret,
         environmentType = e._1.environmentType,
         accountId = e._1.accountId,
         updatedAt = Some(now())
       ) -> apiKeyObj.getOrElse(Seq.empty).headOption)
    }
    val accountUIConfigurationOpt = if(dtoAccountUIConfigurationOpt.isDefined) Some(AccountUIConfiguration(Some(dtoAccountUIConfigurationOpt.get.accountId),
      Some(dtoAccountUIConfigurationOpt.get.autoTimeoutInMinutes), Some(dtoAccountUIConfigurationOpt.get.idleTimeoutInMinutes))) else None
    UserAuth(
      user = User(
        id = bu.id,
        firstName = bu.firstName,
        lastName = bu.lastName,
        email = bu.email,
        isLocked = !bu.accountNonLocked,
        isAdmin = isUserAdmin,
        passwordUpdatedAt,
        passwordExpireAt,
        isAgreedTermsOfService = bu.isAgreedTermsOfService),
      account = Account(
        id = account.accountId,
        name = account.name,
        permission = permissions.map(_.permission).toSet,
        isActive = account.isActive,
        isInternal = account.isInternal,
        subscriptions = subscriptions.toSet,
        isSponsorBank = account.isSponsorBank),
      accounts = if(accounts.isEmpty) Seq(AccountIdName(account.accountId, account.name)) else accounts,
      dashboardEnvRoles = DashboardEnvironmentConvertors.dashboardEnvironments(isUserAdmin, roles, env.keys.toList),
      environment = env.map{ case (dtoEnv, apiKey) =>
                                        EnvironmentSettings(id = dtoEnv.id,
                                                            name = EnvironmentConstants(dtoEnv.environmentType.toInt).toString,
                                                            socureKey = apiKey.map(_.apiKey).
                                                              getOrElse(throw new Exception("Failed to retrieve API Key in environment"))
                                        )
                    }.toList,
      redirectUrl = None,
      accountUIConfiguration = accountUIConfigurationOpt
    )
  }

  def getUserAuthV2(bu: DtoBusinessUser,
                  account: DtoAccount,
                  roles: Seq[DtoBusinessUserEnvironmentRole],
                  environmentWithApiKeys: Map[DtoEnvironment, Seq[DtoApiKey]],
                  permissions: Seq[DtoAccountPermission],
                  subscriptions: Seq[Subscription],
                  accounts: Seq[AccountIdName],
                  passwordUpdatedAt: Option[DateTime],
                  passwordExpireAt: Option[DateTime]) : UserAuthV2 = {

    val isUserAdmin = roles.map(_.role).contains(DashboardUserRole.ADMIN.id)
    val env = environmentWithApiKeys map { e =>
      val y = e._2 groupBy {_.status}
      val apiKeyObj = if (y.contains(ApiKeyStatus.NEW)) y.get(ApiKeyStatus.NEW) else y.get(ApiKeyStatus.ACTIVE)
      (DtoEnvironment(e._1.id,
        secretKey = e._1.secretKey,
        accessToken = e._1.accessToken,
        accessTokenSecret = e._1.accessTokenSecret,
        environmentType = e._1.environmentType,
        accountId = e._1.accountId,
        updatedAt = Some(now())
      ) -> apiKeyObj.getOrElse(Seq.empty).headOption)
    }
    UserAuthV2(
      user = User(
        id = bu.id,
        firstName = bu.firstName,
        lastName = bu.lastName,
        email = bu.email,
        isLocked = !bu.accountNonLocked,
        isAdmin = isUserAdmin,
        passwordUpdatedAt,
        passwordExpireAt,
        isAgreedTermsOfService = bu.isAgreedTermsOfService),
      account = AccountV2(
        id = account.accountId,
        name = account.name,
        permission = permissions.map(_.permission).toSet,
        isActive = account.isActive,
        isInternal = account.isInternal,
        subscriptions = subscriptions.toSet,
        publicId = account.publicId,
        isSponsorBank = account.isSponsorBank
      ),
      accounts = if(accounts.isEmpty) Seq(AccountIdName(account.accountId, account.name)) else accounts,
      dashboardEnvRoles = DashboardEnvironmentConvertors.dashboardEnvironments(isUserAdmin, roles, env.keys.toList),
      environment = env.map{ case (dtoEnv, apiKey) =>
        EnvironmentSettings(id = dtoEnv.id,
          name = EnvironmentConstants(dtoEnv.environmentType.toInt).toString,
          socureKey = apiKey.map(_.apiKey).
            getOrElse(throw new Exception("Failed to retrieve API Key in environment"))
        )
      }.toList,
      redirectUrl = None
    )
  }

  def getBusinessUserForDashboardV2(user: DtoBusinessUser,
                                    env: Seq[DtoEnvironment],
                                    roles : Seq[DtoBusinessUserEnvironmentRole],
                                    account : DtoAccount): BusinessUser = {
    BusinessUser(
      id = user.id,
      firstname = user.firstName,
      lastname = user.lastName,
      email = user.email,
      companyName = account.name,
      contactNumber = user.contactNumber,
      registeredOn = user.registeredOn,
      status = if(account.isActive) 1 else 0,
      isLocked = !user.accountNonLocked,
      accountId = account.accountId,
      isPrimaryAdmin = user.isPrimaryUser,
      roles = List.empty,
      isAgreedTermsOfService = user.isAgreedTermsOfService
    )
  }

  def getDtoBusinessUser(user: UserForm) = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contactnumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = 0,
      isPrimaryUser = true
    )
  }
  def getDtoBusinessUser(user: SubAccountFormV2) = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contactnumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = 0,
      isPrimaryUser = true
    )
  }

  def getDtoBusinessUser(userForm: ParentAccountPrimaryUserForm) = {
    DtoBusinessUser(
      id = 0,
      email = userForm.email,
      firstName = userForm.firstName,
      lastName = userForm.lastName,
      contactNumber = userForm.contactNumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = userForm.accountId,
      isPrimaryUser = true
    )
  }

  def getDtoBusinessUser(user: UserFormWithNoPassword) = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contactnumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = 0,
      isPrimaryUser = true
    )
  }

  def getDtoBusinessUser(user: DelegatedUserForm) = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contactnumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = 0,
      isPrimaryUser = true
    )
  }

  private def extractNotNullAttribute[A](optVal: Option[A], attrName: String): A = {
    optVal match {
      case Some(a) => a
      case _ => throw new Exception(s"Found empty value for attribute that should be not null: $attrName")
    }
  }

  def getInactiveAccountPrimaryAdmin(dtoUser : DtoInactiveAccountPrimaryUser): PrimaryAccountUser = {
    dtoUser.id match {
      case Some(userId) => {
        PrimaryAccountUser(
          companyName = dtoUser.accountName,
          accountId = dtoUser.accountId,
          apiKey = dtoUser.apiKey,
          isEncryptionEnabled = dtoUser.isEncryptionEnabled,
          isInternal = dtoUser.isInternal,
          isPiiMaskEnabled = dtoUser.permission.exists(_.contains(BusinessUserRoles.MASK_PII.id.toString)),
          isPiiMaskEligible = dtoUser.isPiiMaskEligible && dtoUser.id.nonEmpty,
          hasPGPKeys = dtoUser.hasPGPKeys,
          parentAccountId = dtoUser.parentAccount.getOrElse(0),
          whitelistedDomains = dtoUser.whitelistedDomains.map(_.split(""",""").toSet).getOrElse(Set()),
          roles = dtoUser.roles.map(_.split(""",""").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.permission.map(_.split(",").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.attributes.map(_.split(",").map(a => AccountAttributeValue(a.toInt)).flatMap(aa => AccountConvertors.convertAttributeToRole(aa)).toSet).getOrElse(Set.empty[Int]),
          optUser = Some(PrimaryUser(
            id = userId,
            firstName = extractNotNullAttribute(dtoUser.firstName, "firstName"),
            lastName = extractNotNullAttribute(dtoUser.lastName, "lastName"),
            contactNumber = extractNotNullAttribute(dtoUser.contactNumber, "contactNumber"),
            email = extractNotNullAttribute(dtoUser.email, "email"),
            registeredOn = extractNotNullAttribute(dtoUser.registeredOn, "registeredOn"),
            externalAccountId =  dtoUser.externalAccountId
          )),
          publicAccountId = dtoUser.publicAccountId,
          externalAccountId = dtoUser.externalAccountId
        )
      }
      case _ => {
        PrimaryAccountUser(
          companyName = dtoUser.accountName,
          accountId = dtoUser.accountId,
          apiKey = dtoUser.apiKey,
          isEncryptionEnabled = dtoUser.isEncryptionEnabled,
          isInternal = dtoUser.isInternal,
          isPiiMaskEnabled = dtoUser.permission.exists(_.contains(BusinessUserRoles.MASK_PII.id.toString)),
          isPiiMaskEligible = dtoUser.isPiiMaskEligible && dtoUser.id.nonEmpty,
          hasPGPKeys = dtoUser.hasPGPKeys,
          parentAccountId = dtoUser.parentAccount.getOrElse(0),
          whitelistedDomains = dtoUser.whitelistedDomains.map(_.split(""",""").toSet).getOrElse(Set()),
          roles = dtoUser.roles.map(_.split(""",""").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.permission.map(_.split(",").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.attributes.map(_.split(",").map(a => AccountAttributeValue(a.toInt)).flatMap(aa => AccountConvertors.convertAttributeToRole(aa)).toSet).getOrElse(Set.empty[Int]),
          optUser = None,
          publicAccountId = dtoUser.publicAccountId,
          externalAccountId = dtoUser.externalAccountId
        )
      }
     }
  }

  def  getInactiveAccountPrimaryAdmins(users : Vector[DtoInactiveAccountPrimaryUser]) = {
      users.map(getInactiveAccountPrimaryAdmin)
  }

  def getParentAccountPrimaryAdmin(dtoUser : DtoParentAccountPrimaryUser):PrimaryAccountUser = {
    dtoUser.id match {
      case Some(userId) => {
        val roles0 = dtoUser.permission.map(_.split(",").map(_.toInt).toSet).getOrElse(Set.empty[Int])
        val validatedRoles = BusinessUserRoles.values.map(_.id).intersect(roles0)
        PrimaryAccountUser(
          companyName = dtoUser.accountName,
          accountId = dtoUser.accountId,
          apiKey = dtoUser.apiKey,
          isEncryptionEnabled = dtoUser.isEncryptionEnabled,
          isInternal = dtoUser.isInternal,
          isPiiMaskEnabled = dtoUser.permission.exists(_.contains(BusinessUserRoles.MASK_PII.id.toString)),
          isPiiMaskEligible = dtoUser.isPiiMaskEligible && dtoUser.id.nonEmpty,
          hasPGPKeys = dtoUser.hasPGPKeys,
          parentAccountId = 0,
          whitelistedDomains = dtoUser.whitelistedDomains.map(_.split(""",""").toSet).getOrElse(Set()),
          roles = dtoUser.roles.map(_.split(""",""").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            validatedRoles ++
            dtoUser.attributes.map(_.split(",").map(a => AccountAttributeValue(a.toInt)).flatMap(aa => AccountConvertors.convertAttributeToRole(aa)).toSet).getOrElse(Set.empty[Int]),
          optUser = Some(PrimaryUser(
            id = userId,
            firstName = extractNotNullAttribute(dtoUser.firstName, "firstName"),
            lastName = extractNotNullAttribute(dtoUser.lastName, "lastName"),
            contactNumber = extractNotNullAttribute(dtoUser.contactNumber, "contactNumber"),
            email = extractNotNullAttribute(dtoUser.email, "email"),
            registeredOn = extractNotNullAttribute(dtoUser.registeredOn, "registeredOn"),
            externalAccountId =  dtoUser.externalAccountId
          )),
          publicAccountId = dtoUser.publicAccountId,
          externalAccountId = dtoUser.externalAccountId
        )
      }
      case _ => {
        PrimaryAccountUser(
          companyName = dtoUser.accountName,
          accountId = dtoUser.accountId,
          apiKey = dtoUser.apiKey,
          isEncryptionEnabled = dtoUser.isEncryptionEnabled,
          isInternal = dtoUser.isInternal,
          isPiiMaskEnabled = dtoUser.permission.exists(_.contains(BusinessUserRoles.MASK_PII.id.toString)),
          isPiiMaskEligible = dtoUser.isPiiMaskEligible && dtoUser.id.nonEmpty,
          hasPGPKeys = dtoUser.hasPGPKeys,
          parentAccountId = 0,
          whitelistedDomains = dtoUser.whitelistedDomains.map(_.split(""",""").toSet).getOrElse(Set()),
          roles = dtoUser.roles.map(_.split(""",""").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.permission.map(_.split(",").map(_.toInt).toSet).getOrElse(Set.empty[Int]) ++
            dtoUser.attributes.map(_.split(",").map(a => AccountAttributeValue(a.toInt)).flatMap(aa => AccountConvertors.convertAttributeToRole(aa)).toSet).getOrElse(Set.empty[Int]),
          optUser = None,
          publicAccountId = dtoUser.publicAccountId,
          externalAccountId = dtoUser.externalAccountId
        )
      }
    }
  }

  def  getParentAccountPrimaryAdmins(users : Vector[DtoParentAccountPrimaryUser]) = {
    users.map(getParentAccountPrimaryAdmin)
  }

  def getActivationDetails(daoUser : DtoBusinessUser, account : DtoAccount, dtoToken : DtoActivationToken) = {
    UserActivationDetails(
      firstname = daoUser.firstName,
      surname = daoUser.lastName,
      email = daoUser.email,
      activationCode = dtoToken.token,
      status = account.isActive ? 1 | 0
    )
  }

  def getDeleagedUserFromUserForm(userForm : UserForm) = {
    DelegatedAdmin(
      id = 0,
      firstname = userForm.firstname,
      lastname = userForm.lastname,
      company = userForm.companyname,
      contact = userForm.contactnumber,
      email = "", //Since its used in updating information, email is not going to change. So making it as empty
      roles = None
    )
  }

  def convertDelegtedUser(userForm : DelegatedUserForm) = {
    DelegatedAdmin(
      id = 0,
      firstname = userForm.firstname,
      lastname = userForm.lastname,
      company = userForm.accountname,
      contact = userForm.contactnumber,
      email = "", //Since its used in updating information, email is not going to change. So making it as empty
      roles = None
    )
  }

  def getDtoBusinessUser(user: UserFormV2): DtoBusinessUser = {
    DtoBusinessUser(
      id = 0,
      email = user.email,
      firstName = user.firstname,
      lastName = user.lastname,
      contactNumber = user.contactnumber,
      registeredOn = now(),
      accountNonLocked = true,
      accountId = 0,
      isPrimaryUser = true
    )
  }

  def getDtoBusinessUserInfo(user: UserFormV2, businessUserId: Long) : DtoBusinessUserInfo = {
    DtoBusinessUserInfo (
      id = 0L,
      businessUserId = businessUserId,
      addressLine1 = user.addressLine1,
      addressLine2 = user.addressLine2,
      jobTitle = user.jobTitle,
      businessWebsite = user.businessWebsite,
      createdAt = DateTime.now,
      lastUpdatedAt = DateTime.now
    )
  }

  def getBusinessUserInfo(dtoBusinessUserInfo: DtoBusinessUserInfo) : BusinessUserInfo = {
    BusinessUserInfo(
      id = dtoBusinessUserInfo.id,
      businessUserId = dtoBusinessUserInfo.businessUserId,
      addressLine1 = dtoBusinessUserInfo.addressLine1,
      addressLine2 = dtoBusinessUserInfo.addressLine2,
      jobTitle = dtoBusinessUserInfo.jobTitle,
      businessWebsite = dtoBusinessUserInfo.businessWebsite,
      createdAt = dtoBusinessUserInfo.createdAt,
      lastUpdatedAt = dtoBusinessUserInfo.lastUpdatedAt
    )
  }

  def getDtoBuMagicToken(daoUser: DtoBusinessUser, magicToken: String, deviceIdentifier: String, createdAt: DateTime, clickCount: Int ): DtoMagicToken = {
    DtoMagicToken(
      id = 0L,
      businessUserId = daoUser.id,
      token = magicToken,
      identifier = deviceIdentifier,
      createdAt = createdAt,
      clickCount = clickCount
    )
  }

  def getDtoDocumentLinkToken(email: String, magicToken: String, deviceIdentifier: String, createdAt: DateTime, clickCount: Int): DtoDocumentLinkToken = {
    DtoDocumentLinkToken(
      id = 0L,
      email = email,
      token = magicToken,
      identifier = deviceIdentifier,
      createdAt = createdAt,
      clickCount = clickCount
    )
  }

}
