package me.socure.convertors

import me.socure.model.account.AccountMetadata
import me.socure.storage.slick.tables.account.DtoAccountMetadata

/**
 * Converters for account metadata.
 * This object provides methods for converting between DTO and model classes.
 */
object AccountMetadataConvertors {

  /**
   * Convert a DTO to a model.
   *
   * @param dto The DTO to convert
   * @return The converted model
   */
  def toModel(dto: DtoAccountMetadata): AccountMetadata = {
    AccountMetadata(
      accountId = dto.accountId,
      childId = dto.childId
    )
  }

  /**
   * Convert a model to a DTO.
   * Note: This is a partial conversion as it doesn't include audit fields.
   * Use DtoAccountMetadataValueGenerator.create for creating new DTOs.
   *
   * @param model The model to convert
   * @return The converted DTO
   */
  def toDto(model: AccountMetadata): (Long, String) = {
    (model.accountId, model.childId)
  }
}
