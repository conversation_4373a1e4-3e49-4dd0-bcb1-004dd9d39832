package me.socure.convertors

import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.account.utils.UUIDUtilities
import me.socure.common.clock.Clock
import me.socure.constants.EnvironmentConstants._
import me.socure.constants.SystemDefinedRoles.SystemDefinedRole
import me.socure.constants.attributes.AccountAttributeValue
import me.socure.constants.attributes.AccountAttributeValue.AccountAttributeValue
import me.socure.constants.{EnvironmentConstants, _}
import me.socure.dashboard.{AccountSettings, NewAccountSettings}
import me.socure.mapping.ApiKeyMapper
import me.socure.model.account.ApiKeyStatus.ApiKeyStatus
import me.socure.model.account.{ApiKeyStatus, Account => _, _}
import me.socure.model.analytics.AnalyticsGlobalInfoResponse
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.{AccountWithRoles, DashboardUserWithAssociations, UserDetails, UserDetailsByPermissionFilter, UsersByPermissionFilterResponse}
import me.socure.model.etl.DtoEtlEnvironment
import me.socure.model.pgp.PgpKeysPair
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}
import me.socure.model.superadmin.AccountCreationForm
import me.socure.model.user._
import me.socure.model.user.authorization._
import me.socure.model.user.platform.{PlatformEnvironmentPermissions, PlatformUserAuth}
import me.socure.model._
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.accountcache.{DtoEnvironmentCache, DtoEnvironmentCacheInvidiual}
import me.socure.storage.slick.tables.accountsocialkeys.DtoEnvironmentSocialKeys
import me.socure.storage.slick.tables.dashboarddomain.DtoDashboardDomain
import me.socure.storage.slick.tables.industry.DtoIndustry
import me.socure.storage.slick.tables.user.{DtoBusinessUser, DtoUserDetailsByPermission}
import org.jasypt.encryption.StringEncryptor
import org.joda.time.{DateTime, DateTimeZone, Hours, Minutes}

import scala.collection.mutable

/**
 * Created by gopal on 03/05/16.
 */
object AccountConvertors {

  //TODO:  explore this https://github.com/twitter/bijection if it can help here.

  def getAccessCredentials(environment: DtoEnvironment, apiKeys: Seq[DtoApiKey], publicApiKeys: List[DtoPublicApiKey] = List.empty): NewAccessCredentials = {
    NewAccessCredentials(
      apiKeys = apiKeys.map(ApiKeyMapper.toView).toList,
      publicApiKeys = publicApiKeys.map(ApiKeyMapper.toView),
      secretKey = environment.secretKey,
      accessToken = environment.accessToken,
      accessTokenSecret = environment.accessTokenSecret,
      certificate = ""
    ) // Prefer to go with get here as I know this data can't be null and in new schema will fix it as NOT NULL to be consisten
  }

  def getAccessCredentials(environment: DtoEnvironment, apiKey: String) = {
    AccessCredentials(
      apiKey,
      secretKey = environment.secretKey,
      accessToken = environment.accessToken,
      accessTokenSecret = environment.accessTokenSecret,
      certificate = ""
    ) // Prefer to go with get here as I know this data can't be null and in new schema will fix it as NOT NULL to be consisten
  }

  private def getLatestApiKey(apiKeys: Seq[DtoApiKey]): String = {
    val y = apiKeys groupBy {
      _.status
    }
    val apiKeyObj = if (y.contains(ApiKeyStatus.NEW)) y.get(ApiKeyStatus.NEW) else y.get(ApiKeyStatus.ACTIVE)

    apiKeyObj match {
      case Some(apiKeyObj +: apiKeyObjs) => apiKeyObj.apiKey
      case _ => throw new IllegalArgumentException("No new or active API Keys available")
    }
  }

  def getAccessCredentialsWithLatestApiKey(environment: DtoEnvironment, apiKeys: Seq[DtoApiKey]) = {

    AccessCredentials(
      getLatestApiKey(apiKeys),
      secretKey = environment.secretKey,
      accessToken = environment.accessToken,
      accessTokenSecret = environment.accessTokenSecret,
      certificate = ""
    ) // Prefer to go with get here as I know this data can't be null and in new schema will fix it as NOT NULL to be consisten
  }

  def getSocialNetworkAppKeys(keys: Seq[DtoEnvironmentSocialKeys], accountId: Long) = {
    keys.map(key => SocialNetworkAppKeys(id = key.id,
      provider = KeyProviders(key.network).toString,
      appkey = key.applicationKey.getOrElse(""),
      appsecret = key.applicationSecret.getOrElse(""),
      environment = key.environmentId,
      accountId = accountId))
  }

  def getAccountIndividualCache(invidualCaches: Seq[DtoEnvironmentCacheInvidiual], accountId: Long) = {
    invidualCaches.map(cache => {
      AccountIndividualCache(
        id = cache.id,
        identifier = cache.identifier.getOrElse(throw new Exception("Failed to find cache identifier")),
        date = cache.date.getOrElse(throw new Exception("Failed to find cache date")),
        accountId = accountId)
    })
  }

  def getAccountOverallCache(overallCache: Option[DtoEnvironmentCache], accountId: Long) = {
    overallCache.filter(_.skipcache.exists(_ == true)).map(c => {
      AccountOverallCache(
        id = c.id,
        date = c.cacheskipdate.getOrElse(throw new Exception("Failed to retrieve cache skip date")),
        accountId = accountId)
    })
  }

  def getSettingsEnvironment(account: DtoAccount, environment: DtoEnvironment, apiKeys: Seq[DtoApiKey], key: Seq[DtoEnvironmentSocialKeys], cache: Seq[DtoEnvironmentCacheInvidiual], overallCache: Option[DtoEnvironmentCache], renewalTimeInHours: Int, clock: Clock) = me.socure.model.account.NewEnvironment(
    id = environment.id,
    name = EnvironmentConstants(environment.environmentType.toInt).toString,
    domain = environment.domain.map(_.split(""",""").toSet).getOrElse(Set()),
    accessCredentials = getAccessCredentials(environment, apiKeys),
    socialAccounts = getSocialNetworkAppKeys(key, account.accountId),
    invidiualCache = getAccountIndividualCache(cache, account.accountId),
    overallCache = getAccountOverallCache(overallCache, account.accountId),
    canRenewApiKey = environment.updatedAt match {
      case Some(u) => Some(Hours.hoursBetween(u, clock.now()).isGreaterThan(Hours.hours(renewalTimeInHours)))
      case _ => Some(true)
    },
    timeLeftToRenew = environment.updatedAt match {
      case Some(updateTime) => Some((renewalTimeInHours * 60) - Minutes.minutesBetween(updateTime, clock.now()).getMinutes)
      case None => None
    }
  )

  def getEnvironment(account: DtoAccount,
                     environment: DtoEnvironment,
                     apiKeys: Seq[DtoApiKey],
                     key: Seq[DtoEnvironmentSocialKeys],
                     cache: Seq[DtoEnvironmentCacheInvidiual],
                     overallCache: Option[DtoEnvironmentCache],
                     domain: Option[String],
                     publicApiKeys: Seq[DtoPublicApiKey]): Environment = {
    val apiKey = apiKeys.headOption.map {
      _.apiKey
    }.getOrElse(
      throw new IllegalArgumentException("Failed to retrieve API Key for account: " + account.accountId))
    me.socure.model.account.Environment(
      id = environment.id,
      name = EnvironmentConstants(environment.environmentType.toInt).toString,
      domain = domain.map(_.split(""",""").toSet).getOrElse(Set()),
      accessCredentials = getAccessCredentials(environment, apiKey),
      socialAccounts = getSocialNetworkAppKeys(key, account.accountId),
      invidiualCache = getAccountIndividualCache(cache, account.accountId),
      overallCache = getAccountOverallCache(overallCache, account.accountId),
      publicApiKeys = getPublicApiKeys(publicApiKeys)
    )
  }


  def getPublicApiKeys(keys: Seq[DtoPublicApiKey]): Seq[PublicApiKey] = {
    keys.map { key =>
      PublicApiKey(
        id = key.id,
        environmentId = key.environmentId,
        apiKey = key.apiKey,
        status = key.status,
        createdAt = key.createdAt,
        updatedAt = key.updatedAt,
        timeLeft = None
      )
    }
  }

  def getDtoAccountCache(accountOverallCache: AccountOverallCache) = {
    DtoEnvironmentCache(accountOverallCache.id, Some(accountOverallCache.date), Some(true), accountOverallCache.accountId)
  }

  def getDtoInvdividualCache(accountIndividualCache: AccountIndividualCache) = {
    DtoEnvironmentCacheInvidiual(
      accountIndividualCache.id,
      Some(accountIndividualCache.date),
      Some(accountIndividualCache.identifier),
      accountIndividualCache.accountId)
  }

  def getDtoAccountKeys(socialNetworkAppKeys: SocialNetworkAppKeys) = {
    DtoEnvironmentSocialKeys(
      id = socialNetworkAppKeys.id,
      applicationKey = Some(socialNetworkAppKeys.appkey),
      applicationSecret = Some(socialNetworkAppKeys.appsecret),
      network = KeyProviders.withName(socialNetworkAppKeys.provider).id,
      environmentId = socialNetworkAppKeys.environment)
  }

  def getAccount(userForm: UserForm, encryptor: StringEncryptor, isActive: Boolean = false) = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = userForm.companyname,
      isInternal = false,
      industrySector = userForm.industry,
      isActive = isActive,
      isDeleted = false,
      parentId = None,
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  def getAccount(userForm: UserFormWithNoPassword, encryptor: StringEncryptor) = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = userForm.companyname,
      isInternal = false,
      industrySector = userForm.industry,
      isActive = false,
      isDeleted = false,
      parentId = None,
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  def getAccount(parentId: Long, isInternal: Boolean, subAccount: SubAccount, encryptor: StringEncryptor) = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = subAccount.companyname,
      isInternal = isInternal,
      industrySector = subAccount.industry,
      isActive = false,
      isDeleted = false,
      parentId = Some(parentId),
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  def getAccount(accountCreationForm: AccountCreationForm, encryptor: StringEncryptor) = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = accountCreationForm.companyName,
      isInternal = accountCreationForm.isInternal,
      industrySector = accountCreationForm.industry,
      isActive = false,
      isDeleted = false,
      parentId = accountCreationForm.parentId,
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  //This convert AccountPermission to BusinessUserRole. This adopts exising code base.
  def getRole(role: DtoAccountPermission): Int = {
    role.permission
  }

  def convertAttributeToRole(att: AccountAttributeValue): Option[Int] = {
    //At present there are only two attributes need to convert as role (SLA and VERSION)
    att match {
      case v if AccountAttributeValue.DEFAULT_SLA == v => Some(BusinessUserRoles.DEFAULT_SLA.id)
      case v if AccountAttributeValue.HIGH_PERFORMANCE_SLA == v => Some(BusinessUserRoles.HIGH_PERFORMANCE_SLA.id)
      case v if AccountAttributeValue.EXTREME_PERFORMANCE_SLA == v => Some(BusinessUserRoles.EXTREME_PERFORMANCE_SLA.id)
      case v if AccountAttributeValue.PRODUCTION == v => Some(BusinessUserRoles.PRODUCTION.id)
      case v => None
    }
  }

  def convertRoleToAttribute(role: Int): Option[AccountAttributeValue] = {
    role match {
      case v if BusinessUserRoles.DEFAULT_SLA.id == v => Some(AccountAttributeValue.DEFAULT_SLA)
      case v if BusinessUserRoles.HIGH_PERFORMANCE_SLA.id == v => Some(AccountAttributeValue.HIGH_PERFORMANCE_SLA)
      case v if BusinessUserRoles.EXTREME_PERFORMANCE_SLA.id == v => Some(AccountAttributeValue.EXTREME_PERFORMANCE_SLA)
      case v if BusinessUserRoles.PRODUCTION.id == v => Some(AccountAttributeValue.PRODUCTION)
      case _ => None
    }
  }

  def getIndustry(industry: DtoIndustry) = {
    Industry(
      sector = industry.sector,
      description = industry.description)
  }

  def getDtoEnvironment(accountId: Long, clock: Clock) = {
    Seq(DtoEnvironment(
      id = 0,
      environmentType = PRODUCTION_ENVIRONMENT.id,
      accessToken = UUIDUtilities.getRandomUUID,
      accessTokenSecret = UUIDUtilities.getRandomSecretId,
      secretKey = UUIDUtilities.getRandomSecretId,
      accountId = accountId,
      updatedAt = Some(clock.now())
    ),
      DtoEnvironment(
        id = 0,
        environmentType = DEVELOPMENT_ENVIRONMENT.id,
        accessToken = UUIDUtilities.getRandomUUID,
        accessTokenSecret = UUIDUtilities.getRandomSecretId,
        secretKey = UUIDUtilities.getRandomSecretId,
        accountId = accountId,
        updatedAt = Some(clock.now())
      ),
      DtoEnvironment(
        id = 0,
        environmentType = SANDBOX_ENVIRONMENT.id,
        accessToken = UUIDUtilities.getRandomUUID,
        accessTokenSecret = UUIDUtilities.getRandomSecretId,
        secretKey = UUIDUtilities.getRandomSecretId,
        accountId = accountId,
        updatedAt = Some(clock.now())
      )
    )
  }

  def getEtlEnvironment(e: DtoEnvironment) = {
    DtoEtlEnvironment(
      id = e.id,
      secretKey = e.secretKey,
      accessToken = e.accessToken,
      accessTokenSecret = e.accessTokenSecret,
      domain = e.domain.getOrElse(""),
      accountId = e.accountId,
      environmentType = e.environmentType
    )
  }

  def getDtoApiKey(environmentId: Long, apiKeyStatus: ApiKeyStatus = ApiKeyStatus.NEW, clock: Clock) = {
    DtoApiKey(
      id = 0,
      environmentId = environmentId,
      apiKey = UUIDUtilities.getRandomUUID,
      status = apiKeyStatus,
      createdAt = clock.now(),
      updatedAt = clock.now(),
      label = None,
      lastUsedAt = None
    )
  }

  def getPublicDtoApiKey(environmentId: Long, apiKeyStatus: ApiKeyStatus = ApiKeyStatus.NEW, clock: Clock): DtoPublicApiKey = {
    DtoPublicApiKey(
      id = 0,
      environmentId = environmentId,
      apiKey = UUIDUtilities.getRandomUUID,
      status = apiKeyStatus,
      createdAt = clock.now(),
      updatedAt = clock.now(),
      label = None,
      lastUsedAt = None
    )
  }

  //hotfix - settings/:accountID
  def toAccountSettings(newAccountSettings: NewAccountSettings): AccountSettings = {
    val environments = newAccountSettings.environments.map(a => {
      val accessCredentials = new AccessCredentials(
        apiKey =
          a.accessCredentials.apiKeys.
            find(_.status.equals(ApiKeyStatus.ACTIVE)).
            map(_.apiKey).getOrElse(throw new Exception("Failed to retrieve API Key for account: " + newAccountSettings.accountId)),
        secretKey = a.accessCredentials.secretKey,
        accessToken = a.accessCredentials.accessToken,
        accessTokenSecret = a.accessCredentials.accessTokenSecret,
        certificate = a.accessCredentials.certificate
      )
      new Environment(id = a.id,
        name = a.name,
        domain = a.domain,
        accessCredentials = accessCredentials,
        socialAccounts = a.socialAccounts,
        invidiualCache = a.invidiualCache,
        overallCache = a.overallCache,
        publicApiKeys = Seq.empty
      )
    })
    new AccountSettings(
      newAccountSettings.accountId,
      environments)
  }

  def getDtoDashboardDomain(domain: AccountDomain, clock: Clock): DtoDashboardDomain = {
    DtoDashboardDomain(
      id = 0,
      accountId = domain.accountId,
      allowedDomain = Some(domain.domain.mkString(",")),
      updatedAt = Some(clock.now())
    )
  }

  def getAccountDaomain(domain: DtoDashboardDomain): AccountDomain = {
    AccountDomain(domain.accountId, domain.allowedDomain.fold(List.empty[String])(d => d.split(",").toList))
  }

  def getAccountPgpDto(accountId: Long, keys: PgpKeysPair, clock: Clock, needSubkey: Boolean): DtoAccountPgpKeys = {
    DtoAccountPgpKeys(
      id = 0,
      accountId = accountId,
      publicKey = keys.publicKey.value,
      privateKey = keys.privateKey.value,
      createdAt = clock.now(),
      updatedAt = Some(clock.now()),
      status = true,
      hasSubkey = needSubkey
    )
  }

  def toParentAccounts(dtoAccount: DtoAccount): ParentAccount = {
    ParentAccount(id = dtoAccount.accountId, name = dtoAccount.name, isInternal = dtoAccount.isInternal)
  }

  def convertToAccountWithEnv(
                               account: DtoAccount,
                               environments: Set[DtoEnvironment],
                               permissions: Set[DtoAccountPermission],
                               apiKeys: Set[DtoApiKey],
                               subscriptions: Set[Subscription],
                               rootAccountDetails: Option[(DtoAccount, DtoAccountHierarchy)] = None
                             ): AccountWithEnvironmentDetails = {
    convertToAccountWithEnvWithPublicId(
      account = account,
      environments = environments,
      permissions = permissions,
      apiKeys = apiKeys,
      subscriptions = subscriptions,
      rootAccountDetails = rootAccountDetails
    ).toAccountWithEnvironmentDetails
  }

  def convertToAccountWithEnvWithPublicId(
                                           account: DtoAccount,
                                           environments: Set[DtoEnvironment],
                                           permissions: Set[DtoAccountPermission],
                                           apiKeys: Set[DtoApiKey],
                                           subscriptions: Set[Subscription],
                                           rootAccountDetails: Option[(DtoAccount, DtoAccountHierarchy)] = None
                                         ): AccountWithEnvironmentDetailsWithPublicId = {

    def getApiKey(envId: Long): String = {
      apiKeys
        .find(key => key.environmentId == envId && key.status == ApiKeyStatus.NEW)
        .orElse(apiKeys.find(key => key.environmentId == envId && key.status == ApiKeyStatus.ACTIVE))
        .map(_.apiKey)
        .getOrElse(throw new Exception(s"No new or active api key found for environment with id $envId"))
    }

    val authAccount = AccountWithPublicId(
      id = account.accountId,
      publicId = account.publicId,
      name = account.name,
      permission = permissions.map(_.permission),
      isActive = account.isActive,
      isInternal = account.isInternal,
      subscriptions = subscriptions,
      isSponsorBank = account.isSponsorBank
    )

    val environmentSettings = environments.map { env =>
      EnvironmentSettings(
        id = env.id,
        name = EnvironmentConstants(env.environmentType.toInt).toString,
        socureKey = getApiKey(envId = env.id)
      )
    }

    val rootAccDetails = rootAccountDetails.map { rootAcc =>
      RootAccountDetails(
        rootAccountId = rootAcc._1.accountId,
        rootAccountType = rootAcc._2.accountType,
        rootAccountPublicId = rootAcc._1.publicId
      )
    }

    AccountWithEnvironmentDetailsWithPublicId(
      account = authAccount,
      environment = environmentSettings,
      rootAccount = rootAccDetails
    )
  }

  def toDtoUserRole(userRoleInput: UserRoleInput): DtoUserRole = {
    DtoUserRole(
      id = userRoleInput.id.getOrElse(0),
      name = userRoleInput.name,
      description = userRoleInput.description,
      byBusinessUserId = userRoleInput.creator.userId,
      byAccountId = userRoleInput.accountId,
      updatedAt = DateTime.now
    )
  }

  def toUserRole(dtoUserRole: DtoUserRole): UserRole = {
    UserRole(
      id = Some(dtoUserRole.id),
      name = dtoUserRole.name,
      description = dtoUserRole.description
    )
  }

  def toUserRole(systemDefinedRole: SystemDefinedRole): UserRole = {
    UserRole(
      id = None,
      name = systemDefinedRole.name,
      description = Some(systemDefinedRole.description),
      roleType = systemDefinedRole.roleType
    )
  }


  def toUserRoleResult(dtoUserRole: ((DtoUserRole, DtoRolePermissionTemplateAssociation), DtoPermissionTemplate), roleIdVsUsersCountMap: Map[Long, Int]): UserRoleResult = {
    UserRoleResult (
      id = Some(dtoUserRole._1._1.id),
      name = dtoUserRole._1._1.name,
      templateType = dtoUserRole._2.`type`,
      roleType = SystemDefinedRoles.CUSTOMROLE.roleType,
      description = dtoUserRole._1._1.description,
      usersCount = roleIdVsUsersCountMap.getOrElse(dtoUserRole._1._1.id, 0),
      updatedAt = Some(dtoUserRole._1._1.updatedAt),
      byAccountId = Some(dtoUserRole._1._1.byAccountId)
    )
  }

  def toUserSysDefinedRoleResult(systemDefinedRole: SystemDefinedRole, roleTypeVsUsersCountMap: Map[Int, Int]): UserRoleResult = {
    UserRoleResult (
      id = None,
      name = systemDefinedRole.name,
      templateType = TemplateTypes.DEFAULT.id,
      roleType = systemDefinedRole.roleType,
      description = Some(systemDefinedRole.description),
      usersCount = roleTypeVsUsersCountMap.getOrElse(systemDefinedRole.roleType, 0),
      updatedAt = None
    )
  }

  def toUserAccountAssociation(dtoUserAccountAssociation: DtoUserAccountAssociation, roles: Set[Long], roleType: Option[Set[Int]] = None): UserAccountAssociation = {
    UserAccountAssociation(
      id = dtoUserAccountAssociation.id,
      userId = dtoUserAccountAssociation.businessUserId,
      accountId = dtoUserAccountAssociation.accountId,
      status = dtoUserAccountAssociation.status,
      isPrimaryUser = dtoUserAccountAssociation.isPrimaryUser,
      userRoles = Some(roles),
      roleType = roleType,
      revoked = dtoUserAccountAssociation.revoked.map { revoked =>
        if (revoked.nonEmpty) revoked.split(",").map(_.toLong).toSet else Set.empty
      }
    )
  }

  def toDtoUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): DtoUserAccountAssociation = {
    DtoUserAccountAssociation(
      id = userAccountAssociationInput.id.getOrElse(0),
      businessUserId = userAccountAssociationInput.userId,
      accountId = userAccountAssociationInput.accountId,
      status = UserAccountAssociationStatuses.ACTIVE.id,
      isPrimaryUser = false,
      revoked = userAccountAssociationInput.revoked.map(_.mkString(",")),
      updatedBy = Some(userAccountAssociationInput.updatedBy),
      updatedAt = DateTime.now
    )
  }

  def toBusinessUserWithUserAccountAssociation(dtoBusinessUser: DtoBusinessUser, dtoUserAccountAssociation: DtoUserAccountAssociation, roles: Set[Long], roleType: Set[Int])  = {
    BusinessUserWithUserAccountAssociation(
      firstName = dtoBusinessUser.firstName,
      lastName = dtoBusinessUser.lastName,
      email = dtoBusinessUser.email,
      userAccountAssociation = toUserAccountAssociation(dtoUserAccountAssociation, roles, Some(roleType))
    )
  }

  def toDtoUserAccountAssociation(userAssociationInput: UserAssociationInput, accountId: Long, updatedBy: Long) : DtoUserAccountAssociation = {
    DtoUserAccountAssociation(
      id = userAssociationInput.id.getOrElse(0),
      businessUserId = userAssociationInput.userId,
      accountId = accountId,
      status = UserAccountAssociationStatuses.ACTIVE.id,
      isPrimaryUser = false,
      revoked = userAssociationInput.revoked.map(_.mkString(",")),
      updatedBy = Some(updatedBy),
      updatedAt = DateTime.now
    )
  }

  def toPermissionTemplateMapping(dtoPermissionTemplateMapping: DtoPermissionTemplateMapping): PermissionTemplateMapping = {
    PermissionTemplateMapping(
      id = dtoPermissionTemplateMapping.id,
      permissionTemplateId = dtoPermissionTemplateMapping.permissionTemplateId,
      environmentTypeId = dtoPermissionTemplateMapping.environmentTypeId,
      permissions = if (dtoPermissionTemplateMapping.permissions.nonEmpty) dtoPermissionTemplateMapping.permissions.split(",").map(_.toInt).toSet else Set.empty,
      globalScope = dtoPermissionTemplateMapping.globalScope.map { globalScope =>
        if (globalScope.nonEmpty) globalScope.split(",").map(_.toInt).toSet else Set.empty
      }
    )
  }

  def toDtoPermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): DtoPermissionTemplateMapping = {
    DtoPermissionTemplateMapping(
      id = permissionTemplateMappingInput.id.getOrElse(0),
      permissionTemplateId = permissionTemplateMappingInput.permissionTemplateId,
      environmentTypeId = permissionTemplateMappingInput.environmentTypeId,
      permissions = permissionTemplateMappingInput.permissions.mkString(","),
      globalScope = permissionTemplateMappingInput.globalScope.map(_.mkString(","))
    )
  }

  def toAccountHierarchy(dtoAccountHierarchy: DtoAccountHierarchy): AccountHierarchy = {
    AccountHierarchy(
      id = dtoAccountHierarchy.id,
      accountId = dtoAccountHierarchy.accountId,
      hierarchyPath = dtoAccountHierarchy.hierarchyPath,
      accountType = dtoAccountHierarchy.accountType,
      hierarchyStatus = dtoAccountHierarchy.hierarchyStatus,
      administer = dtoAccountHierarchy.administer,
      numberOfPrimaryAdmins = dtoAccountHierarchy.numberOfPrimaryAdmins
    )
  }

  def toDtoAccountHierarchy(accountHierarchy: AccountHierarchyInput): DtoAccountHierarchy = {
    DtoAccountHierarchy(
      id = accountHierarchy.id.getOrElse(0),
      accountId = accountHierarchy.accountId,
      hierarchyPath = accountHierarchy.hierarchyPath,
      accountType = accountHierarchy.accountType,
      hierarchyStatus = accountHierarchy.hierarchyStatus,
      administer = accountHierarchy.administer,
      numberOfPrimaryAdmins = accountHierarchy.numberOfPrimaryAdmins.getOrElse(0)
    )
  }

  def toPermissionTemplate(dtoPermissionTemplate: DtoPermissionTemplate): PermissionTemplate = {
    PermissionTemplate(
      id = dtoPermissionTemplate.id,
      name = dtoPermissionTemplate.name,
      templateType = dtoPermissionTemplate.`type`,
      accountId = dtoPermissionTemplate.accountId,
      updatedBy = dtoPermissionTemplate.updatedBy,
      updatedAt = Some(dtoPermissionTemplate.updateAt)
    )
  }

  def toDtoPermissionTemplate(permissionTemplate: PermissionTemplate, clock: Clock): DtoPermissionTemplate = {
    DtoPermissionTemplate(
      id = permissionTemplate.id,
      name = permissionTemplate.name,
      `type` = permissionTemplate.templateType,
      accountId = permissionTemplate.accountId,
      updatedBy = permissionTemplate.updatedBy,
      updateAt = clock.now
    )
  }

  def toAccountAssociationHistory(dtoAccountAssociationHistory: DtoAccountAssociationHistory): AccountAssociationHistory = {
    AccountAssociationHistory(
      id = Some(dtoAccountAssociationHistory.id),
      accountHierarchyId = dtoAccountAssociationHistory.accountHierarchyId,
      activatedAt = dtoAccountAssociationHistory.activatedAt,
      deactivatedAt = dtoAccountAssociationHistory.deactivatedAt
    )
  }

  def toDtoAccountAssociationHistory(accountAssociationHistory: AccountAssociationHistory): DtoAccountAssociationHistory = {
    DtoAccountAssociationHistory(
      id = accountAssociationHistory.id.getOrElse(0),
      accountHierarchyId = accountAssociationHistory.accountHierarchyId,
      activatedAt = accountAssociationHistory.activatedAt,
      deactivatedAt = accountAssociationHistory.deactivatedAt
    )
  }


  def getAccountV2(userForm: UserFormV2, encryptor: StringEncryptor, isActive: Boolean = false, isInternal: Boolean = false): DtoAccount = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = userForm.companyname,
      isInternal = isInternal,
      industrySector = userForm.industry,
      isActive = isActive,
      isDeleted = false,
      parentId = None,
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  def getSubAccountV2(userForm: SubAccountFormV2, encryptor: StringEncryptor, isActive: Boolean = false): DtoAccount = {
    val publicIdVal = PublicIdGenerator.account().value
    DtoAccount(
      accountId = 0,
      name = userForm.companyname,
      isInternal = false,
      industrySector = userForm.industry,
      isActive = isActive,
      isDeleted = false,
      parentId = None,
      firstActivatedAt = None,
      publicId = publicIdVal,
      publicApiKey = PublicIdGenerator.accountApiKey().value,
      externalId = Some(encryptor.encrypt(publicIdVal))
    )
  }

  def toDtoRolePermissionTemplateAssociation(rolePermissionTemplateAssociation: RolePermissionTemplateAssociation): DtoRolePermissionTemplateAssociation = {
    DtoRolePermissionTemplateAssociation(
      id = 0,
      permissionTemplateId = rolePermissionTemplateAssociation.permissionTemplateId,
      userRoleId = rolePermissionTemplateAssociation.userRoleId
    )
  }

  def toRolePermissionTemplateAssociation(dtoRolePermissionTemplateAssociation: DtoRolePermissionTemplateAssociation, userId: Long, accountId: Long): RolePermissionTemplateAssociation = {
    RolePermissionTemplateAssociation(
      permissionTemplateId = dtoRolePermissionTemplateAssociation.permissionTemplateId,
      userRoleId = dtoRolePermissionTemplateAssociation.userRoleId,
      userId = userId,
      accountId = accountId
    )
  }

  val dashboardUserRoleVsDashboardUserPermissionsMap: Map[Int, Set[Int]] = Map(
    DashboardUserRole.OVERVIEW.id -> DashboardUserPermissions.startsWithName("OVERVIEW"),
    DashboardUserRole.SETTINGS.id -> DashboardUserPermissions.startsWithName("SETTINGS"),
    DashboardUserRole.CREATE_TRANSACTION.id -> DashboardUserPermissions.startsWithName("TRANSACTIONS_CREATE"),
    DashboardUserRole.LIST_TRANSACTION.id -> DashboardUserPermissions.startsWithName("TRANSACTIONS_VIEW"),
    DashboardUserRole.USERS.id -> DashboardUserPermissions.startsWithName("USERS"),
    DashboardUserRole.ACCOUNTS.id -> DashboardUserPermissions.startsWithName("ACCOUNTS"),
    DashboardUserRole.DOCUMENTATION.id -> DashboardUserPermissions.startsWithName("DOCUMENTATION"),
    DashboardUserRole.EVENT_MANAGER.id -> DashboardUserPermissions.startsWithName("EVENT_MANAGERS"),
    DashboardUserRole.FILE_UPLOAD.id -> DashboardUserPermissions.startsWithName("FILE_UPLOAD_PROVISION"),
    DashboardUserRole.BATCH_JOB.id -> DashboardUserPermissions.startsWithName("BATCHJOB"),
    DashboardUserRole.REPORTING.id -> DashboardUserPermissions.startsWithName("REPORTING"),
    DashboardUserRole.EXPORT_AUDIT.id -> DashboardUserPermissions.startsWithName("AUDIT_EXPORT_PROVISION"),
    DashboardUserRole.DECISION_LOGIC.id -> DashboardUserPermissions.startsWithName("DECISION_LOGIC"),
    DashboardUserRole.EXPORT_EVENT_AUDIT.id -> DashboardUserPermissions.startsWithName("EVENT_AUDIT_EXPORT_PROVISION"),
    DashboardUserRole.ExportWlUserActions.id -> DashboardUserPermissions.startsWithName("ExportWlUserActions"),
    DashboardUserRole.NEG_POS_LIST.id -> DashboardUserPermissions.startsWithName("NEG_POS_LIST")
  )

  val userPermissionsVsDashboardUserRoleMap: Map[Int, Int] = dashboardUserRoleVsDashboardUserPermissionsMap.flatMap { case (k, v) => v.map(_ -> k) }

  def toDashboardUserRoles(dashboardUserPermissions: Set[Int]): Set[Int] = {
    dashboardUserPermissions.map(userPermissionsVsDashboardUserRoleMap.getOrElse(_, -1))
  }

  def toDashboardPermissions(dashboardUserRoles: Set[Int]): Set[Int] = {
    dashboardUserRoles.flatMap(dashboardUserRoleVsDashboardUserPermissionsMap.get).flatten
  }

  def toPermissionTemplateInfo(dtoPermissionTemplateSeq: Seq[DtoPermissionTemplate], dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping], dtoUserRoleSeq: Seq[DtoUserRole], dtoRolePermissionTemplateAssociationSeq : Seq[DtoRolePermissionTemplateAssociation], dtoUserAccountAssociationSeq: Seq[DtoUserAccountAssociation]): Seq[PermissionTemplateInfo] = {
    dtoPermissionTemplateSeq map { pt =>
      val roleId = dtoRolePermissionTemplateAssociationSeq.find(_.permissionTemplateId == pt.id).map(_.userRoleId)
      val roleName = if(roleId.isDefined) dtoUserRoleSeq.find(_.id == roleId.get).map(_.name) else None
      PermissionTemplateInfo(
        id = pt.id,
        name = pt.name,
        templateType = pt.`type`,
        roleId = roleId,
        roleName = roleName,
        environmentPermissions = AccountConvertors.toPermissionTemplateMappingInfo(dtoPermissionTemplateMappingSeq.filter(ptm => ptm.permissionTemplateId == pt.id)).toSet,
        updatedBy = AccountConvertors.toUserAccountAssociationMinimalInfo(pt.updatedBy, dtoUserAccountAssociationSeq),
        updatedAt = new DateTime(pt.updateAt, DateTimeZone.UTC).toString
      )
    }
  }

  def toPermissionTemplateMappingInfo(dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping]): Seq[PermissionTemplateMappingInfo] = {
    dtoPermissionTemplateMappingSeq map { dtoPermissionTemplateMapping =>
      PermissionTemplateMappingInfo(
        environmentTypeId = dtoPermissionTemplateMapping.environmentTypeId,
        environmentName = EnvironmentTypes.byEnvironmentTypeId(dtoPermissionTemplateMapping.environmentTypeId) match {
          case None =>"-"
          case Some(env) => env.name
        },
        permissions = if( dtoPermissionTemplateMapping.permissions.nonEmpty)  {
          dtoPermissionTemplateMapping.permissions.split(",").map { p => DashboardUserPermissions.byId(p.toInt).map(_.name).get}.toSet
        } else Set.empty,
        globalScope = dtoPermissionTemplateMapping.globalScope.map { globalScope =>
          if (globalScope.nonEmpty) globalScope.split(",").map(_.toInt).toSet else Set.empty
        }
      )
    }
  }

  def toUserAccountAssociationMinimalInfo(updatedBy: Long, dtoUserAccountAssociationSeq: Seq[DtoUserAccountAssociation]): UserAccountAssociationMinimalInfo = {
    dtoUserAccountAssociationSeq.find(_.id == updatedBy) match {
      case None => UserAccountAssociationMinimalInfo (
        userId = -1,
        accountId = -1
      )
      case Some(dtoUserAccountAssociation) =>UserAccountAssociationMinimalInfo (
        userId = dtoUserAccountAssociation.businessUserId,
        accountId = dtoUserAccountAssociation.accountId
      )
    }
  }

  def toPartnerAndSubAccountUserInfo(userId: Long, dtoUserAccountAssociationSeq: Seq[DtoUserAccountAssociation], dtoUserAccountRoleAssociationSeq: Seq[DtoUserAccountRoleAssociation], dtoUserRoleSeq: Seq[DtoUserRole], dtoRolePermissionTemplateAssociationSeq: Seq[DtoRolePermissionTemplateAssociation], dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping], dtoBusinessUser: DtoBusinessUser): PartnerAndSubAccountUserInfo = {
    val roleVsPermissionTemplateMap = dtoRolePermissionTemplateAssociationSeq.map(dtoPTA => dtoPTA.userRoleId -> dtoPTA.permissionTemplateId).toMap
    val permissionTemplateVsPermissionsMap: Map[Long, Seq[DtoPermissionTemplateMapping]] = AccountConvertors.toPermissionTemplateVsPermissionsMap(dtoPermissionTemplateMappingSeq)
    val accounts = dtoUserAccountAssociationSeq map { dtoUserAccountAssociation =>
      val userRoleIds = dtoUserAccountRoleAssociationSeq.filter(_.userAccountAssociationId == dtoUserAccountAssociation.id).map(_.userRoleId.get)
      val userRoles = userRoleIds map { userRoleId =>
        UserRoleInfo (
          roleName = dtoUserRoleSeq.find(_.id == userRoleId).map(_.name).getOrElse(""),
          environmentPermissions = permissionTemplateVsPermissionsMap(roleVsPermissionTemplateMap(userRoleId)).map(dtoPermissionTemplateMapping => EnvironmentPermissions(EnvironmentTypes.byId(dtoPermissionTemplateMapping.environmentTypeId).map(_.name).getOrElse(""), dtoPermissionTemplateMapping.permissions.split(",").map(p=>DashboardUserPermissions.byId(p.toInt).map(_.name).get).toSet))
        )
      }
      UserAccountAssociationInfo (
        accountId = dtoUserAccountAssociation.accountId,
        status = UserAccountAssociationStatuses.byId(dtoUserAccountAssociation.status).map(_.label).getOrElse("Unknown"),
        userRoles = userRoles,
        revoked = dtoUserAccountAssociation.revoked.map(r => r.split(",").map(p=>DashboardUserPermissions.byId(p.toInt).map(_.name).get).toSet)
      )
    }
    PartnerAndSubAccountUserInfo (
      userId = userId,
      firstName = dtoBusinessUser.firstName,
      lastName = dtoBusinessUser.lastName,
      email = dtoBusinessUser.email,
      registeredOn = new DateTime(dtoBusinessUser.registeredOn, DateTimeZone.UTC).toString,
      accounts = accounts.toSet
    )
  }

  def toPermissionTemplateVsPermissionsMap(dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping]): Map[Long, Seq[DtoPermissionTemplateMapping]] = {
    val permissionTemplateVsPermissionsMap = mutable.Map[Long, Seq[DtoPermissionTemplateMapping]]()
    dtoPermissionTemplateMappingSeq map { dtoPermissionTemplateMapping =>
      if(permissionTemplateVsPermissionsMap.contains(dtoPermissionTemplateMapping.permissionTemplateId)) {
        var dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping] = permissionTemplateVsPermissionsMap(dtoPermissionTemplateMapping.permissionTemplateId)
        dtoPermissionTemplateMappingSeq = dtoPermissionTemplateMappingSeq :+ dtoPermissionTemplateMapping
        permissionTemplateVsPermissionsMap.put(dtoPermissionTemplateMapping.permissionTemplateId , dtoPermissionTemplateMappingSeq)
      } else {
        permissionTemplateVsPermissionsMap.put(dtoPermissionTemplateMapping.permissionTemplateId , Seq(dtoPermissionTemplateMapping))
      }
    }
    permissionTemplateVsPermissionsMap.toMap
  }

  def toUserAuthV2(bu: DtoBusinessUser, account: DtoAccount, environmentWithApiKeys: Map[DtoEnvironment, Seq[DtoApiKey]], permissions: Seq[DtoAccountPermission], subscriptions: Seq[Subscription], dashboardUserPermissions: Map[Int, Set[Int]], dtoUserAccountAssociation: DtoUserAccountAssociation) : UserAuthV2 = {
    val isUserAdmin = dtoUserAccountAssociation.isPrimaryUser
    val environmentVsPermissionsMap = dashboardUserPermissions.map(entry => entry._1 -> toDashboardUserRoles(entry._2))
    val env = environmentWithApiKeys map { e =>
      val y = e._2 groupBy {_.status}
      val apiKeyObj = if (y.contains(ApiKeyStatus.NEW)) y.get(ApiKeyStatus.NEW) else y.get(ApiKeyStatus.ACTIVE)
      (DtoEnvironment(e._1.id,
        secretKey = e._1.secretKey,
        accessToken = e._1.accessToken,
        accessTokenSecret = e._1.accessTokenSecret,
        environmentType = e._1.environmentType,
        accountId = e._1.accountId,
        updatedAt = Some(DateTime.now)
      ) -> apiKeyObj.getOrElse(Seq.empty).headOption)
    }
    UserAuthV2(
      user = User(
        id = bu.id,
        firstName = bu.firstName,
        lastName = bu.lastName,
        email = bu.email,
        isLocked = !bu.accountNonLocked,
        isAdmin = isUserAdmin,
        isAgreedTermsOfService = bu.isAgreedTermsOfService),
      account = AccountV2(
        id = account.accountId,
        name = account.name,
        permission = permissions.map(_.permission).toSet,
        isActive = account.isActive,
        isInternal = account.isInternal,
        subscriptions = subscriptions.toSet,
        publicId = account.publicId,
        isSponsorBank = account.isSponsorBank
      ),
      accounts = Seq(AccountIdName(account.accountId, account.name)),
      dashboardEnvRoles = DashboardEnvironmentConvertors.dashboardEnvironments(environmentVsPermissionsMap),
      environment = env.map{ case (dtoEnv, apiKey) =>
        EnvironmentSettings(id = dtoEnv.id,
          name = EnvironmentConstants(dtoEnv.environmentType.toInt).toString,
          socureKey = apiKey.map(_.apiKey).
            getOrElse(throw new Exception("Failed to retrieve API Key in environment"))
        )
      }.toList,
      redirectUrl = None
    )
  }

  def toUserAuth(bu: DtoBusinessUser,
                 account: DtoAccount,
                 environmentWithApiKeys: Map[DtoEnvironment, Seq[DtoApiKey]],
                 permissions: Seq[DtoAccountPermission],
                 subscriptions: Seq[Subscription],
                 dashboardUserPermissions: Map[Int, Set[Int]],
                 dtoUserAccountAssociation: DtoUserAccountAssociation,
                 accounts: Seq[AccountIdName],
                 dtoAccountUIConfigurationOpt: Option[DtoAccountUIConfiguration] = None,
                 isAccountOwner: Boolean,
                 passwordUpdatedAt: Option[DateTime],
                 passwordExpireAt: Option[DateTime],
                 programs: Option[Seq[AccountIdName]] = None,
                 isProgram: Option[Boolean] = None,
                 analyticsGlobalInfo: Option[AnalyticsGlobalInfoResponse],
                 accountType: Option[Int],
                 tosTimeStamp: Option[DateTime],
                 rootAccHierarchy: Option[DtoAccountHierarchy],
                 rootAccountDetail: Option[DtoAccount],
                 platformEnvPermissions: Option[Seq[PlatformEnvironmentPermissions]]
                ) : UserAuth = {
    val environmentVsPermissionsMap = dashboardUserPermissions.map(entry => entry._1 -> toDashboardUserRoles(entry._2))
    val env = environmentWithApiKeys map { e =>
      val y = e._2 groupBy {_.status}
      val apiKeyObj = if (y.contains(ApiKeyStatus.NEW)) y.get(ApiKeyStatus.NEW) else y.get(ApiKeyStatus.ACTIVE)
      (DtoEnvironment(e._1.id,
        secretKey = e._1.secretKey,
        accessToken = e._1.accessToken,
        accessTokenSecret = e._1.accessTokenSecret,
        environmentType = e._1.environmentType,
        accountId = e._1.accountId,
        updatedAt = Some(DateTime.now)
      ) -> apiKeyObj.getOrElse(Seq.empty).headOption)
    }
    val accountUIConfigurationOpt = if(dtoAccountUIConfigurationOpt.isDefined) Some(AccountUIConfiguration(Some(dtoAccountUIConfigurationOpt.get.accountId),
      Some(dtoAccountUIConfigurationOpt.get.autoTimeoutInMinutes), Some(dtoAccountUIConfigurationOpt.get.idleTimeoutInMinutes))) else None
    val analyticsGlobalInfoOption = if(permissions.map(_.permission).contains(BusinessUserRoles.CustomerFacingAnalytics.id)) analyticsGlobalInfo else None
    val platformResponse = if(rootAccHierarchy.isDefined && rootAccountDetail.isDefined && platformEnvPermissions.isDefined){
      Some(PlatformUserAuth(
        nameSpaceId = rootAccHierarchy.get.accountId,
        nameSpacePublicId = rootAccountDetail.get.publicId,
        environmentPermissions = platformEnvPermissions.get
      ))
    } else None
    UserAuth(
      user = User(
        id = bu.id,
        firstName = bu.firstName,
        lastName = bu.lastName,
        email = bu.email,
        isLocked = !bu.accountNonLocked,
        isAdmin = isAccountOwner,
        passwordUpdatedAt,
        passwordExpireAt,
        isAgreedTermsOfService = bu.isAgreedTermsOfService,
        tosAgreedTimeStamp = tosTimeStamp,
        contactNumber = Some(bu.contactNumber)
      ),
      account = Account(
        id = account.accountId,
        name = account.name,
        permission = permissions.map(_.permission).toSet,
        isActive = account.isActive,
        isInternal = account.isInternal,
        subscriptions = subscriptions.toSet,
        isSponsorBank = account.isSponsorBank,
        accountType = accountType
      ),
      accounts = if(accounts.nonEmpty) accounts else Seq(AccountIdName(account.accountId, account.name)),
      dashboardEnvRoles = DashboardEnvironmentConvertors.dashboardEnvironments(environmentVsPermissionsMap),
      environment = env.map{ case (dtoEnv, apiKey) =>
        EnvironmentSettings(id = dtoEnv.id,
          name = EnvironmentTypes.byId(dtoEnv.environmentType.toInt).get.name,
          socureKey = apiKey.map(_.apiKey).getOrElse("")
        )
      }.toList,
      redirectUrl = None,
      dashboardEnvPermissionsV2 = Option(DashboardEnvironmentConvertors.dashboardEnvironmentsV2(dashboardUserPermissions)),
      accountUIConfiguration = accountUIConfigurationOpt,
      programs = programs,
      isProgram = isProgram,
      analyticsGlobalInfo = analyticsGlobalInfoOption,
      platform = platformResponse
    )
  }

  def toDtoRateLimit(saveRateLimitingInput: SaveRateLimitingInput): DtoRateLimit = {
    DtoRateLimit (
      id = 0,
      accountId = saveRateLimitingInput.accountId,
      environmentTypeId = saveRateLimitingInput.environmentTypeId,
      api = saveRateLimitingInput.api,
      windowInMillis = saveRateLimitingInput.windowInMillis,
      limit = saveRateLimitingInput.limit,
      createdAt = DateTime.now(),
      lastUpdatedAt = DateTime.now(),
      createdBy = saveRateLimitingInput.createdBy,
      lastUpdatedBy = saveRateLimitingInput.createdBy
    )
  }

  def toDtoRateLimit(updateRateLimitingInput: UpdateRateLimitingInput): DtoRateLimit = {
    DtoRateLimit (
      id = 0,
      accountId = updateRateLimitingInput.accountId,
      environmentTypeId = updateRateLimitingInput.environmentTypeId,
      api = updateRateLimitingInput.api,
      windowInMillis = updateRateLimitingInput.windowInMillis,
      limit = updateRateLimitingInput.limit,
      createdAt = DateTime.now(),
      lastUpdatedAt = DateTime.now(),
      createdBy = "",
      lastUpdatedBy = updateRateLimitingInput.updatedBy
    )
  }

  def toDtoRateLimit(deleteRateLimitingInput: DeleteRateLimitingInput): DtoRateLimit = {
    DtoRateLimit (
      id = 0,
      accountId = deleteRateLimitingInput.accountId,
      environmentTypeId = deleteRateLimitingInput.environmentTypeId,
      api = deleteRateLimitingInput.api,
      windowInMillis = deleteRateLimitingInput.windowInMillis,
      limit = 0,
      createdAt = DateTime.now(),
      lastUpdatedAt = DateTime.now(),
      createdBy = "",
      lastUpdatedBy =""
    )
  }

  def toDtoRateLimits(updateRateLimitingInputs: Seq[UpdateRateLimitingInput]) : Seq[DtoRateLimit] = {
    updateRateLimitingInputs.map(rl => toDtoRateLimit(rl))
  }

  def toDtoRateLimitsFromDeleteInput(deleteRateLimitingInputs: Seq[DeleteRateLimitingInput]) : Seq[DtoRateLimit] = {
    deleteRateLimitingInputs.map(rl => toDtoRateLimit(rl))
  }

  def toRateLimitingConfig(dtoRateLimit: DtoRateLimit): RateLimitingConfig = {
    RateLimitingConfig(
      windowInMillis = dtoRateLimit.windowInMillis,
      limit = dtoRateLimit.limit
    )
  }

  def toRateLimitEntry(dtoRateLimit: DtoRateLimit, accountIdNames: Map[Long, String]): RateLimitingEntry = {
    RateLimitingEntry (
      accountId = dtoRateLimit.accountId,
      name = accountIdNames.getOrElse(dtoRateLimit.accountId, ""),
      environmentTypeId = dtoRateLimit.environmentTypeId,
      api = dtoRateLimit.api,
      windowInMillis = dtoRateLimit.windowInMillis,
      limit = dtoRateLimit.limit,
      createdAt = dtoRateLimit.createdAt,
      lastUpdatedAt = dtoRateLimit.lastUpdatedAt,
      createdBy = dtoRateLimit.createdBy,
      lastUpdatedBy = dtoRateLimit.lastUpdatedBy
    )
  }

  def toRateLimitingConfigs(dtoRateLimits: Seq[DtoRateLimit]): Seq[RateLimitingConfig] = {
    dtoRateLimits.map(drl => toRateLimitingConfig(drl))
  }

  def toRateLimitingConfigsV2(dtoRateLimits: Seq[DtoRateLimit]): Map[String, Seq[RateLimitingConfig]] = {
    dtoRateLimits
      .groupBy(_.api)
      .mapValues(toRateLimitingConfigs)
  }

  def toRateLimitingEntries(dtoRateLimits: Seq[DtoRateLimit], accountIdNames: Map[Long, String]): Seq[RateLimitingEntry] = {
    dtoRateLimits.map(drl => toRateLimitEntry(drl, accountIdNames))
  }

  def toDashboardUserWithAssociations(userDetails: UserDetails, accountIdVsNameMap: Map[Long, String], accountIdVsRolesMap: Map[Long, Seq[UserRole]]): DashboardUserWithAssociations = {
    DashboardUserWithAssociations (
      userDetails = userDetails,
      accountsWithRoles = accountIdVsNameMap.map(entry => AccountWithRoles(
        accountDetails = AccountIdName(entry._1, entry._2),
        roles = accountIdVsRolesMap.getOrElse(entry._1, Seq.empty)
      )).toSeq
    )
  }

  def getAccountPayloadKeysDto(accountId: Long,
                               environmentId: Long,
                               customerPublicKey: String,
                               wrappingRef: String,
                               publicKey: String,
                               privateKey: String,
                               clock: Clock): DtoAccountPayloadKeys = {
    DtoAccountPayloadKeys(
      id = 0,
      accountId = accountId,
      environmentTypeId = environmentId,
      publicKey = publicKey,
      privateKey = privateKey,
      customerPublicKey = customerPublicKey,
      wrappingKeyRef = wrappingRef,
      createdAt = clock.now(),
      updatedAt = clock.now(),
      status = true
    )
  }

  def toAccountPayloadKey(dtoAccountPayloadKeys: DtoAccountPayloadKeys, decryptedKey: String): AccountPayloadKey = {
    AccountPayloadKey(
      id = dtoAccountPayloadKeys.id,
      accountId = dtoAccountPayloadKeys.accountId,
      environmentTypeId = dtoAccountPayloadKeys.environmentTypeId,
      publicKey = dtoAccountPayloadKeys.publicKey,
      privateKey = decryptedKey,
      customerPublicKey = dtoAccountPayloadKeys.customerPublicKey,
      createdAt = dtoAccountPayloadKeys.createdAt,
      updatedAt = dtoAccountPayloadKeys.updatedAt
    )
  }

  def toUserDetailsbyPermission(userDetails: Map[Int, Option[Map[Long, Seq[DtoUserDetailsByPermission]]]], envTypes: Seq[Int]): Seq[UserDetailsByPermissionFilter] = {
    envTypes.flatMap{ env =>
      userDetails.get(env) match {
        case Some(accountUserDetail) =>
          accountUserDetail.get.keys.flatMap{ accountId =>
            accountUserDetail.get.get(accountId) match {
              case Some(userSeq) =>
                userSeq.groupBy(_.id).map{ user =>

                  val permissions: mutable.Set[String] = mutable.Set.empty[String]
                  user._2.foreach{ uValue =>
                    uValue.matchedPermissions match {
                      case Some(matchedPerm) => matchedPerm.foreach(perm => permissions.add(perm))
                      case _ =>
                    }
                  }
                  val accumalatedPermissions = if (permissions.nonEmpty){
                    Some(permissions.toSeq)
                  } else {
                    None
                  }

                  UserDetailsByPermissionFilter(id = user._1,
                    firstName = user._2.head.firstName,
                    lastName = user._2.head.lastName,
                    email = user._2.head.email,
                    matchedPermissions = accumalatedPermissions,
                    environmentType = env,
                    accountId = accountId
                  )
                }
              case _ => Seq.empty[UserDetailsByPermissionFilter]
            }
          }
        case _ => Seq.empty[UserDetailsByPermissionFilter]
      }
    }
  }

  def toUserDetailsbyPermissionForCaseMgmt(userDetails: Map[Int, Option[Map[Long, Seq[DtoUserDetailsByPermission]]]], envTypes: Seq[Int]): Seq[UserDetailsByPermissionFilter] = {
    val userDetailsBypermission: mutable.ListBuffer[UserDetailsByPermissionFilter] = mutable.ListBuffer.empty[UserDetailsByPermissionFilter]
    envTypes.foreach { env =>
      userDetails.get(env) match {
        case Some(accountUserDetail) =>
          accountUserDetail.get.keys.foreach{ accountId =>
            accountUserDetail.get.get(accountId) match {
              case Some(userSeq) =>
                userSeq.groupBy(_.id).map { user =>

                  val permissions: mutable.Set[String] = mutable.Set.empty[String]
                  user._2.foreach { uValue =>
                    uValue.matchedPermissions match {
                      case Some(matchedPerm) => matchedPerm.foreach(perm => permissions.add(perm))
                      case _ =>
                    }
                  }
                  if(permissions.nonEmpty) {
                    permissions.foreach { p =>
                      userDetailsBypermission.append(
                        UserDetailsByPermissionFilter(id = user._1,
                          firstName = user._2.head.firstName,
                          lastName = user._2.head.lastName,
                          email = user._2.head.email,
                          matchedPermissions = Some(Seq(p)),
                          environmentType = env,
                          accountId = accountId
                        )
                      )
                    }
                  } else {
                    UserDetailsByPermissionFilter(id = user._1,
                      firstName = user._2.head.firstName,
                      lastName = user._2.head.lastName,
                      email = user._2.head.email,
                      matchedPermissions = None,
                      environmentType = env,
                      accountId = accountId
                    )
                  }
                }
              case _ => Seq.empty[UserDetailsByPermissionFilter]
            }
          }
        case _ => Seq.empty[UserDetailsByPermissionFilter]
      }
    }
    userDetailsBypermission.toSeq
  }

  def toUsersByPermissionFilterResponse(users: Seq[UserDetailsByPermissionFilter], page: Option[Int], size: Option[Int], totalSize: Int): UsersByPermissionFilterResponse ={
    UsersByPermissionFilterResponse( users = users,
      page = page,
      size = size,
      totalSize = totalSize
    )
  }

}