package me.socure.convertors

import me.socure.model.superadmin.{DelegatedAdmin, DelegatedAdminLegacy}
import me.socure.model.user.authorization._
import me.socure.model.user.{PrimaryAccountUser, PrimaryAccountUserLegacy}
import me.socure.model.{AccountInformation, AccountInformationLegacy, BusinessUserRoles}

@deprecated
object LegacyModelConverters {

  def toAccountInformationLegacy(accountInformation: AccountInformation): AccountInformationLegacy = {
    AccountInformationLegacy(
      active = accountInformation.active,
      accountId = accountInformation.accountId,
      accountName = accountInformation.accountName,
      environment = accountInformation.environment,
      watchlistPreference = accountInformation.watchlistPreference,
      industry = accountInformation.industry,
      isInternal = accountInformation.isInternal,
      roles = idsToNames(accountInformation.roles),
      primaryFraudModel = accountInformation.primaryFraudModel,
      fraudModels = accountInformation.fraudModels
    )
  }

  def toDelegatedAdminLegacy(delegatedAdmin: DelegatedAdmin): DelegatedAdminLegacy = {
    DelegatedAdminLegacy(
      id = delegatedAdmin.id,
      firstname = delegatedAdmin.firstname,
      lastname = delegatedAdmin.lastname,
      company = delegatedAdmin.company,
      contact = delegatedAdmin.contact,
      email = delegatedAdmin.email,
      roles = delegatedAdmin.roles.map(idsToNames)
    )
  }

  def toDelegatedAdmin(delegatedAdmin: DelegatedAdminLegacy): DelegatedAdmin = {
    DelegatedAdmin(
      id = delegatedAdmin.id,
      firstname = delegatedAdmin.firstname,
      lastname = delegatedAdmin.lastname,
      company = delegatedAdmin.company,
      contact = delegatedAdmin.contact,
      email = delegatedAdmin.email,
      roles = delegatedAdmin.roles.map(namesToIds)
    )
  }

  def toPrimaryAccountUserLegacy(primaryAccountUser: PrimaryAccountUser): PrimaryAccountUserLegacy = {
    PrimaryAccountUserLegacy(
      accountId = primaryAccountUser.accountId,
      companyName = primaryAccountUser.companyName,
      apiKey = primaryAccountUser.apiKey,
      isInternal = primaryAccountUser.isInternal,
      isEncryptionEnabled = primaryAccountUser.isEncryptionEnabled,
      isPiiMaskEnabled = primaryAccountUser.isPiiMaskEnabled,
      isPiiMaskEligible = primaryAccountUser.isPiiMaskEligible,
      hasPGPKeys = primaryAccountUser.hasPGPKeys,
      parentAccountId = primaryAccountUser.parentAccountId,
      whitelistedDomains = primaryAccountUser.whitelistedDomains,
      roles = idsToNames(primaryAccountUser.roles),
      optUser = primaryAccountUser.optUser,
      publicAccountId = primaryAccountUser.publicAccountId
    )
  }

  def toAccountLegacy(account: Account): AccountLegacy = {
    AccountLegacy(
      id = account.id,
      name = account.name,
      permission = idsToNames(account.permission),
      isActive = account.isActive,
      isInternal = account.isInternal
    )
  }

  def toAccountWithPublicIdLegacy(account: AccountWithPublicId): AccountWithPublicIdLegacy = {
    AccountWithPublicIdLegacy(
      id = account.id,
      publicId = account.publicId,
      name = account.name,
      permission = idsToNames(account.permission),
      isActive = account.isActive,
      isInternal = account.isInternal
    )
  }

  def toAccountWithEnvironmentDetailsLegacy(accountWithEnvironmentDetails: AccountWithEnvironmentDetails): AccountWithEnvironmentDetailsLegacy = {
    AccountWithEnvironmentDetailsLegacy(
      account = toAccountLegacy(accountWithEnvironmentDetails.account),
      environment = accountWithEnvironmentDetails.environment
    )
  }

  def toAccountWithEnvironmentDetailsWithPublicIdLegacy(accountWithEnvironmentDetails: AccountWithEnvironmentDetailsWithPublicId): AccountWithEnvironmentDetailsWithPublicIdLegacy = {
    AccountWithEnvironmentDetailsWithPublicIdLegacy(
      account = toAccountWithPublicIdLegacy(accountWithEnvironmentDetails.account),
      environment = accountWithEnvironmentDetails.environment
    )
  }

  def toUserAuthLegacy(userAuth: UserAuth): UserAuthLegacy = {
    UserAuthLegacy(
      user = userAuth.user,
      account = toAccountLegacy(userAuth.account),
      dashboardEnvRoles = userAuth.dashboardEnvRoles,
      environment = userAuth.environment
    )
  }

  def idToName(id: Int): Option[String] = {
    BusinessUserRoles.values.find(_.id == id).map(_.name)
  }

  def idsToNames(ids: Set[Int]): Set[String] = {
    ids.flatMap(idToName)
  }

  def nameToId(name: String): Option[Int] = {
    BusinessUserRoles.values.find(_.name.toLowerCase == name.toLowerCase).map(_.id)
  }

  def namesToIds(names: Set[String]): Set[Int] = {
    names.flatMap(nameToId)
  }
}
