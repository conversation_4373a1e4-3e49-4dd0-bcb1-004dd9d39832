package me.socure.convertors

import me.socure.constants.Domains.Domain
import me.socure.constants.{DashboardUserPermissions, EnvironmentConstants, EnvironmentTypes}
import me.socure.model.user.platform.PlatformPermission
import me.socure.model.user.{DashboardEnvironment, DashboardEnvironmentV2, DashboardUserRole}
import me.socure.storage.slick.tables.account.DtoEnvironment
import me.socure.storage.slick.tables.user.role.DtoBusinessUserEnvironmentRole

/**
  * Created by alexand<PERSON> on 2/26/17.
  */
object DashboardEnvironmentConvertors {

  def dashboardEnvironment(environment: DtoEnvironment,
                           roles: Seq[DtoBusinessUserEnvironmentRole]): DashboardEnvironment = {

    val environmentRoles = roles.filter(r => r.environmentId.contains(environment.id)).map(r => DashboardUserRole(r.role))


    DashboardEnvironment(
      id = environment.id,
      name = EnvironmentConstants(environment.environmentType.toInt).toString,
      roles = environmentRoles.toSet
    )
  }

  def dashboardEnvironments(isUserAdmin: <PERSON>olean,
                            roles: Seq[DtoBusinessUserEnvironmentRole],
                            env: Seq[DtoEnvironment]): Set[DashboardEnvironment] = {

    if(!isUserAdmin) {
      env.map(e => dashboardEnvironment(e, roles)).toSet.filter(_.roles.nonEmpty)
    } else {
      Set.empty[DashboardEnvironment]
    }
  }

  def dashboardEnvironments(dashboardEnvPermissions: Map[Int, Set[Int]]) : Set[DashboardEnvironment] = {
    dashboardEnvPermissions.map(entry => DashboardEnvironment(
      id = entry._1,
      name = EnvironmentTypes.byId(entry._1).get.name,
      roles = entry._2.filterNot(_ == -1).map(perm => DashboardUserRole.byId(perm).get)
    )).toSet
  }

  def dashboardEnvironmentsV2(dashboardUserPermissions: Map[Int, Set[Int]]): Set[DashboardEnvironmentV2] = {
    dashboardUserPermissions.map(entry => DashboardEnvironmentV2(
      id = entry._1,
      name = EnvironmentTypes.byId(entry._1).get.name,
      permissions = entry._2.filterNot(_ == -1).map(permId => DashboardUserPermissions.byId(permId).map(perm => DashboardUserPermissions.toPermissionResult(perm)).get)
    )).toSet
  }

  def toPlatformPermissions(envType: Int, platformEnvPermissions: Map[Int, Set[Int]], scopeDomains: Seq[Domain]): Seq[PlatformPermission] = {
    val mergedPermIds = platformEnvPermissions.getOrElse(EnvironmentTypes.GLOBAL_ENVIRONMENT.id, Set.empty) ++
      platformEnvPermissions.getOrElse(envType, Set.empty)

    mergedPermIds
      .map(DashboardUserPermissions.byId)
      .filter(_.exists(perm => scopeDomains.contains(perm.domain)))
      .collect {
        case Some(perm) => PlatformPermission(
          permissionId = perm.id,
          permissionName = perm.name,
          domain = perm.domain.name,
          action = perm.action.name
        )
      }.toSeq
  }
}
