package me.socure.user.client.dashboard2
import dispatch.Defaults.executor
import me.socure.model.ErrorResponse
import me.socure.account.client.GenericClient._
import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.account.AccountDashboardDomain
import me.socure.model.dashboardv2.AuditDetails

import scala.concurrent.Future
import scala.concurrent.duration.Duration

class CachedDashboardDomainClient (
                                    dashboardDomainClient: DashboardDomainClient,
                                    domainCache: Storage[AccountDashboardDomain],
                                    domainSeqCache: Storage[Seq[AccountDashboardDomain]],
                                    timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)],
                                    memcachedTtl: Option[Duration] = None
                                  ) extends DashboardDomainClient {
  val getDomainByAccountIdApiName = "/dashboard/domain/get_permission_domain_by_account_id"
  val getAssociatedAccountDashboardDomainListApiName = "/dashboard/domain/get_associated_account_dashboard_domain_list_by_email"
  val additionalTags = if (memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]


  override def getDashboardWhiteListPermissionAndDomainsByEmail (email: String): Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    dashboardDomainClient.getDashboardWhiteListPermissionAndDomainsByEmail(email)
  }

  def getDashboardWhiteListPermissionAndDomainsById(accountId: Long) : Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
    getCache(getDomainByAccountIdApiName, domainCache, cacheKey, timeoutOpt) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => dashboardDomainClient.getDashboardWhiteListPermissionAndDomainsById(accountId = accountId).flatMap {
        case r@Right(result) => storeCache(getDomainByAccountIdApiName, domainCache, cacheKey, result, timeoutOpt, additionalTags) map (_ => r)
        case r => Future successful r
      }
    }
  }


  def getAssociatedAccountDashboardDomainListByEmailId( email: String): Future[Either[ErrorResponse, Seq[AccountDashboardDomain]]] = {
    val cacheKey = DashboardDomainCacheKeyProvider.provide(email)
    getCache(getAssociatedAccountDashboardDomainListApiName, domainSeqCache, cacheKey, timeoutOpt) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None =>
        dashboardDomainClient.getAssociatedAccountDashboardDomainListByEmailId(email).flatMap {
          case r@Right(result) => storeCache(getAssociatedAccountDashboardDomainListApiName, domainSeqCache, cacheKey, result, timeoutOpt, additionalTags).map(_ => r)
          case r => Future.successful(r)
        }
    }
  }


  def getDashboardDomainsByEmail(email : String) : Future[Either[ErrorResponse, String]] ={
    dashboardDomainClient.getDashboardDomainsByEmail(email)
  }

  override def getDashboardDomains(accountId: Long): Future[Either[ErrorResponse, String]] = {
    dashboardDomainClient.getDashboardDomains(accountId)
  }

  override def updateDashboardDomain(accountId: Long, domains: List[String]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] ={
    dashboardDomainClient.updateDashboardDomain(accountId, domains)
  }

}
