package me.socure.user.client.dashboard2

import java.nio.charset.Charset
import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountApiKeys, AccountDetails, AccountIdName, SubAccount, SubAccountApiKeys}
import me.socure.model.dashboardv2.{AccountInfoWithEnvDetails, AccountV2, SubAccountV2, UserDetails}
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{SubAccountFormV2, UserForm, UserFormWithNoPassword}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunder<PERSON> on 8/19/16.
  */
class SubAccountManagementClientImpl(endpoint : String,config:Option[Config]=None)(implicit ec : ExecutionContext) extends SubAccountManagementClient {
  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  val urlPrefix : String = "/dashboard_account"

  override def getSubAccountList(accountId: Long): Future[Either[ErrorResponse, Vector[AccountV2]]] = {
    val request = url(s"$endpoint$urlPrefix/list_accounts/$accountId")
    callCommunicate[Vector[AccountV2]]("/dashboard_account/list_accounts", "GET", request, accountId = Some(accountId))
  }

  override def getSubAccounts(parentId: Long): Future[Either[ErrorResponse, List[SubAccountV2]]] = {
    val request = url(s"$endpoint$urlPrefix/list_sub_accounts/$parentId")
    callCommunicate[List[SubAccountV2]]("/dashboard_account/list_accounts", "GET", request, accountId = Some(parentId))
  }

  override def getSubAccountsByPublicId(publicParentId: String): Future[Either[ErrorResponse, List[SubAccountV2]]] = {
    val request = url(s"$endpoint$urlPrefix/list_sub_accounts_by_public_id/$publicParentId")
    callCommunicate[List[SubAccountV2]]("/dashboard_account/list_sub_accounts_by_public_id", "GET", request)
  }

  override def getPrimaryUserByAccountId(accountId: Long): Future[Either[ErrorResponse, UserDetails]] = {
    val request = url(s"$endpoint$urlPrefix/get_primary_user_by_accountid/$accountId")
    GenericClient.communicate[UserDetails]("/dashboard_user/get_primary_user_by_accountid", "GET", request, accountId = Some(accountId)) map {
      case Right(u) => Right(UserDetails(
        id = u.id,
        firstName = u.firstName,
        lastName = u.lastName,
        email = u.email,
        contactNumber = u.contactNumber
      ))
      case Left(errRes) =>
        logger.info(s"Could not get user details by accountId [$accountId] with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getSubAccountsV2(parentId: Long, userId: Long, accountId: Long, page: Option[Int] = None, size: Option[Int] = None, skipPermissionChk: Boolean = false): Future[Either[ErrorResponse, List[SubAccountV2]]] = {
    val params = Seq("user_id" -> userId.toString, "account_id" -> accountId.toString, "skip_permission_chk" -> skipPermissionChk.toString) ++
      page.map("page" -> _.toString) ++
      size.map("size" -> _.toString)
    val request = (url(endpoint) / "dashboard_account" / "subaccounts")/ parentId <<? params
    callCommunicate[List[SubAccountV2]]("/dashboard_account/subaccounts", "GET", request, accountId = Some(parentId))
  }

  override def getSubAccountsWithEnvDetails(parentId: Long): Future[Either[ErrorResponse, Set[AccountInfoWithEnvDetails]]] = {
    val request = url(s"$endpoint$urlPrefix/list_sub_accounts_with_env_details/$parentId")
    callCommunicate[Set[AccountInfoWithEnvDetails]]("/dashboard_account/list_sub_accounts_with_env_details", "GET", request, accountId = Some(parentId))
  }

  override def createSubAccount(parentId: Long, userForm: UserForm, isActive: Boolean = false): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/create_sub_account").addQueryParameter("isActive",isActive.toString).addQueryParameter("parentid",parentId.toString).setContentType("application/json", Charset.forName("UTF-8")) << userForm.encodeJson()
    callCommunicate[Boolean]("/dashboard_account/create_sub_account", "POST", request, accountId = Some(parentId))
  }

  override def createSubAccountV2(parentId: Long, userForm: SubAccountFormV2, isActive: Boolean = false): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/create_sub_account_v2").addQueryParameter("isActive",isActive.toString).addQueryParameter("parentid",parentId.toString).setContentType("application/json", Charset.forName("UTF-8")) << userForm.encodeJson()
    callCommunicate[Boolean]("/dashboard_account/create_sub_account_v2", "POST", request, accountId = Some(parentId))
  }

  override def createSubAccountWithNoPassword(parentId: Long, userForm: UserFormWithNoPassword): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val request = url(s"$endpoint$urlPrefix/" +
      s"create_sub_account_with_no_password") << Map("parentid" -> parentId.toString, "user" -> userForm.encodeJson())
    callCommunicate[UserActivationDetails]("/dashboard_account/create_sub_account_with_no_password", "POST", request, accountId = Some(parentId))
  }

  override def createSubAccountWithMinDetails(parentId: Long, subAccount: SubAccount, userId: Option[Long], accountId: Option[Long], primaryUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Seq("parentid" -> parentId.toString, "user" -> subAccount.encodeJson()) ++
                  accountId.map("account_id" -> _.toString) ++
                  userId.map("user_id" -> _.toString) ++
                  primaryUserId.map("primary_user_id" -> _.toString)
    val request = url(s"$endpoint$urlPrefix/create_sub_account_with_min_details") << params.toMap
    callCommunicate[Boolean]("/dashboard_account/create_sub_account_with_min_details", "POST", request, accountId = Some(parentId))
  }

  override def updateSubAccount(userForm: UserForm): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_sub_account").setContentType("application/json", Charset.forName("UTF-8")) << userForm.encodeJson()
    callCommunicate[Boolean]("/dashboard_account/update_sub_account", "POST", request)
  }

  override def activate(accountId: Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Seq("account_id" -> accountId.toString) ++
      creatorAccountId.map("creator_account_id" -> _.toString) ++
      creatorUserId.map("creator_user_id" -> _.toString)
    val request = url(s"$endpoint$urlPrefix/activate") << params
    callCommunicate[Boolean]("/dashboard_account/activate", "POST", request, accountId = Some(accountId))
  }

  override def deactivate(accountId: Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Seq("account_id" -> accountId.toString) ++
      creatorAccountId.map("creator_account_id" -> _.toString) ++
      creatorUserId.map("creator_user_id" -> _.toString)
    val request = url(s"$endpoint$urlPrefix/deactivate") << params
    callCommunicate[Boolean]("/dashboard_account/deactivate", "POST", request, accountId = Some(accountId))
  }

  override def getAccountList(accountId: Long): Future[Either[ErrorResponse, Vector[AccountDetails]]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_list/$accountId")
    callCommunicate[Vector[AccountDetails]]("/dashboard_account/get_account_list", "GET", request, accountId = Some(accountId))
  }

  override def deprecateApiKeys(): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/deprecate_api_keys").POST
    callCommunicate[String]("/dashboard_account/deprecate_api_keys", "POST", request)
  }

  override def getApiKeysForSubAccounts(accountId: Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]): Future[Either[ErrorResponse, List[SubAccountApiKeys]]] = {
    val params =
      creatorAccountId.map("creator_account_id" -> _.toString) ++
      creatorUserId.map("creator_user_id" -> _.toString)
    val request = url(s"$endpoint$urlPrefix/get_apikeys_for_subaccounts/$accountId") <<? params.toMap
    callCommunicate[List[SubAccountApiKeys]]("/dashboard_account/get_apikeys_for_subaccounts", "GET", request, accountId = Some(accountId))
  }

  override def getApiKeysForSubAccounts(accountId: Long): Future[Either[ErrorResponse, List[SubAccountApiKeys]]] = {
    val request = url(s"$endpoint$urlPrefix/get_apikeys_for_subaccounts/$accountId")
    callCommunicate[List[SubAccountApiKeys]]("/dashboard_account/get_apikeys_for_subaccounts", "GET", request, accountId = Some(accountId))

  }

  override def validateAccountAccess(apiKey: String,
                                     creatorAccountId:  Long,
                                     creatorUserId: Long,
                                     permissions: String,
                                     status: Option[String] = None): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/validate/apikey/access") <<? Map("apikey" -> apiKey, "creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString, "permissions" -> permissions) ++ status.map("status" -> _)
    callCommunicate[Boolean]("/dashboard_account/validate/apikey/access", "GET", request)
  }

  override def validateAccountAccess(environmentTypeId: Int,
                                     creatorAccountId:  Long,
                                     creatorUserId: Long,
                                     permissions: String): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/validate/environmenttype/access") <<? Map("environment_type_id" -> environmentTypeId.toString, "creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString, "permissions" -> permissions)
    callCommunicate[Boolean]("/dashboard_account/validate/environmenttype/access", "GET", request)
  }

  override def validateAccountAccessWithOrPermissions(environmentTypeId: Int,
                                     creatorAccountId:  Long,
                                     creatorUserId: Long,
                                     permissions: String, orPermissions: Option[String]): Future[scala.Either[ErrorResponse, Boolean]] = {
    val orPermissionsParam = orPermissions.getOrElse("")
    val request = url(s"$endpoint$urlPrefix/validate/environmenttype/access/permissions") <<? Map("environment_type_id" -> environmentTypeId.toString, "creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString, "permissions" -> permissions, "or_permissions" -> orPermissionsParam)
    callCommunicate[Boolean]("/dashboard_account/validate/environmenttype/access/permissions", "GET", request)
  }

  override def getAccountAPIKeysByEnvTypeId(accountId:  Long, environmentTypeId: Int):  Future[Either[ErrorResponse, List[AccountApiKeys]]] = {
    val request = url(s"$endpoint$urlPrefix/get_apikeys_for_account_by_envType/$accountId/$environmentTypeId")
    callCommunicate[List[AccountApiKeys]]("/dashboard_account/get_apikeys_for_account_by_envType", "GET", request, accountId = Some(accountId))
  }

  override def getSponsorBankIdsByProgramId(programId: Long): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint$urlPrefix/get_sponsor_bank_id/$programId")
    callCommunicate[Long]("/dashboard_account/get_sponsor_bank_id", "GET", request, accountId = Some(programId))
  }

  override def getSponsorBankPrograms(sponsorBankId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_sponsor_bank_programs/$sponsorBankId")
    callCommunicate[Seq[AccountIdName]]("/dashboard_account/get_sponsor_bank_programs", "GET", request, accountId = Some(sponsorBankId))
  }

  private def callCommunicate[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None): Future[Either[ErrorResponse, A]] ={
    GenericClient.communicate[A](apiName, httpMethod, request, accountId = accountId,config = config)
  }
}
