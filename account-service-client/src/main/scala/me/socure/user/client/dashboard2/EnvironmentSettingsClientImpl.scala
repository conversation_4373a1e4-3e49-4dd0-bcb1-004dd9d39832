package me.socure.user.client.dashboard2

import java.nio.charset.Charset

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.dashboard.NewAccountSettings
import me.socure.model.ErrorResponse
import me.socure.model.account.{Api<PERSON>eyInfo, EnvironmentWithDomains, SocialNetworkAppKeys}
import me.socure.model.dashboardv2._
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 8/26/16.
  */
class EnvironmentSettingsClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends EnvironmentSettingsClient {

  val urlPrefix : String = "/environment_settings"

  override def getEnvironmentSettingsWithApiKeys(accountId: Long): Future[Either[ErrorResponse, NewAccountSettings]] = {
    val request = url(s"$endpoint$urlPrefix/with_api_keys/$accountId")
    GenericClient.communicate[NewAccountSettings]("/environment_settings/with_api_keys", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironmentSettingsWithApiKeys(accountId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long], permissionChkForTransaction: Boolean = false): Future[Either[ErrorResponse, NewAccountSettings]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/with_api_keys/$accountId").addQueryParameter("creator_account_id", creatorAccountId.toString).addQueryParameter("creator_user_id", creatorUserId.toString).addQueryParameter("permissionChkForTransaction", permissionChkForTransaction.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/with_api_keys/$accountId")
    }
    GenericClient.communicate[NewAccountSettings]("/environment_settings/with_api_keys", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironmentSettingsWithApiKeysDev(accountId: Long): Future[Either[ErrorResponse, NewAccountSettings]] = {
    val request = url(s"$endpoint$urlPrefix/with_api_keys_dev/$accountId")
    GenericClient.communicate[NewAccountSettings]("/environment_settings/with_api_keys_dev", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironmentSettingsWithApiKeysDev(accountId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, NewAccountSettings]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/with_api_keys_dev/$accountId").addQueryParameter("creator_account_id", creatorAccountId.toString).addQueryParameter("creator_user_id", creatorUserId.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/with_api_keys/$accountId")
    }
    GenericClient.communicate[NewAccountSettings]("/environment_settings/with_api_keys_dev", "GET", request, accountId = Some(accountId))
  }

  override def upsertSocialNetworkKeys(socialKeys: SocialNetworkAppKeys): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint$urlPrefix/upsert_appkey").setContentType("application/json", Charset.forName("UTF-8")) << socialKeys.encodeJson()
    GenericClient.communicate[Long]("/environment_settings/upsert_appkey", "POST", request)
  }

  override def deleteSocialNetworkKeys(id: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/remove_appkey").addQueryParameter("id",id.toString).setContentType("application/json", Charset.forName("UTF-8")).POST << creator.encodeJson()
    GenericClient.communicate[Boolean]("/environment_settings/remove_appkey", "POST", request)
  }

  override def updateEnvironmentDomaon(envId: Long, domains: List[String], creator: Option[Creator] = None): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val request = url(s"$endpoint$urlPrefix/update_domain").setContentType("application/json", Charset.forName("UTF-8")) << EnvironmentDomain(envId, domains, creator).encodeJson()
    GenericClient.communicateWithAuditDetails[Boolean]("/environment_settings/update_domain", "POST", request)
  }

  override def getEnironmentList(accountId: Long): Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
    val request = url(s"$endpoint$urlPrefix/get_environments/$accountId")
    GenericClient.communicate[Seq[EnvironmentNameAndId]]("/environment_settings/get_environments", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironmentListForAccounts(accountIds: Seq[Long], envTypeIds: Seq[Long]): Future[Either[ErrorResponse, Map[Long, Seq[Long]]]] = {
    val request = url(s"$endpoint$urlPrefix/get_environments_by_account_ids").setContentType("application/json", Charset.forName("UTF-8")) << Serialization.write(Map(("accountIds" -> accountIds), ("envTypeIds" -> envTypeIds)))
    GenericClient.communicate[Map[Long, Seq[Long]]]("/environment_settings/get_environments_by_account_ids", "POST", request)
  }

  override def generateApiKey(envId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$urlPrefix/generate_api_key").addQueryParameter("env_id",envId.toString).setContentType("application/json", Charset.forName("UTF-8")).POST << creator.encodeJson()
    GenericClient.communicate[Int]("/environment_settings/generate_api_key", "POST", request, additionalTags = Set(s"environmentId:$envId"))
  }

  override def generateApiKeyDev(envId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$urlPrefix/generate_api_key_dev").addQueryParameter("env_id",envId.toString).setContentType("application/json", Charset.forName("UTF-8")).POST << creator.encodeJson()
    GenericClient.communicate[Int]("/environment_settings/generate_api_key_dev", "POST", request, additionalTags = Set(s"environmentId:$envId"))
  }

  override def deprecateApiKey(envId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Seq[Int]]] = {
    val request = url(s"$endpoint$urlPrefix/deprecate_api_key").addQueryParameter("env_id",envId.toString).setContentType("application/json", Charset.forName("UTF-8")).POST << creator.encodeJson()
    GenericClient.communicate[Seq[Int]]("/environment_settings/deprecate_api_key", "POST", request, additionalTags = Set(s"environmentId:$envId"))
  }

  override def deprecateApiKeyDev(envId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Seq[Int]]] = {
    val request = url(s"$endpoint$urlPrefix/deprecate_api_key_dev").addQueryParameter("env_id",envId.toString).setContentType("application/json", Charset.forName("UTF-8")).POST << creator.encodeJson()
    GenericClient.communicate[Seq[Int]]("/environment_settings/deprecate_api_key_dev", "POST", request, additionalTags = Set(s"environmentId:$envId"))
  }

  override def getSocialIdsByAccountId(accountId: Long): Future[Either[ErrorResponse, Seq[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/get_appkey_ids/$accountId")
    GenericClient.communicate[Seq[Long]]("/environment_settings/get_appkey_ids", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironmentWithDomains(accountId: Long): Future[Either[ErrorResponse, List[EnvironmentWithDomains]]] = {
    val request = url(s"$endpoint$urlPrefix/get_environment_with_domains/$accountId")
    GenericClient.communicate[List[EnvironmentWithDomains]]("/environment_settings/get_environment_with_domains", "GET", request, accountId = Some(accountId))
  }

  override def getEnvironments(accountId: Long, role: Int, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/account/$accountId")
        .addQueryParameter("creator_account_id", creatorAccountId.toString)
        .addQueryParameter("creator_user_id", creatorUserId.toString)
        .addQueryParameter("role", role.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/account/$accountId")
    }
    GenericClient.communicate[Seq[EnvironmentNameAndId]]("/environment_settings/account", "GET", request, accountId = Some(accountId))
  }

  override def getApiKeys(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/apikeys/$environmentId")
        .addQueryParameter("creator_account_id", creatorAccountId.toString)
        .addQueryParameter("creator_user_id", creatorUserId.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/apikeys/$environmentId")
    }
    GenericClient.communicate[Seq[ApiKeyInfo]]("/environment_settings/apikeys", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getApiKeysDev(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/apikeys_dev/$environmentId")
        .addQueryParameter("creator_account_id", creatorAccountId.toString)
        .addQueryParameter("creator_user_id", creatorUserId.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/apikeys_dev/$environmentId")
    }
    GenericClient.communicate[Seq[ApiKeyInfo]]("/environment_settings/apikeys_dev", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getPublicApiKeys(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/publicapikeys/$environmentId")
        .addQueryParameter("creator_account_id", creatorAccountId.toString)
        .addQueryParameter("creator_user_id", creatorUserId.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/publicapikeys/$environmentId")
    }
    GenericClient.communicate[Seq[ApiKeyInfo]]("/environment_settings/publicapikeys", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getPublicApiKeysDev(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]] = {
    val request = (creatorAccountId, creatorUserId) match {
      case (Some(creatorAccountId), Some(creatorUserId)) => url(s"$endpoint$urlPrefix/publicapikeys_dev/$environmentId")
        .addQueryParameter("creator_account_id", creatorAccountId.toString)
        .addQueryParameter("creator_user_id", creatorUserId.toString)
      case (_, _) => url(s"$endpoint$urlPrefix/publicapikeys_dev/$environmentId")
    }
    GenericClient.communicate[Seq[ApiKeyInfo]]("/environment_settings/publicapikeys_dev", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def updateApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Map() ++ creatorUserId.map("creator_user_id" -> _.toString) ++ creatorAccountId.map("creator_account_id" -> _.toString)
    val request = (url(s"$endpoint$urlPrefix/apikeys").setContentType("application/json", Charset.forName("UTF-8"))
      <<? params
      << apiKeyUpdateRequest.encodeJson()).PUT
    GenericClient.communicate[Boolean]("/environment_settings/apikeys", "PUT", request)
  }

  override def updateApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Map() ++ creatorUserId.map("creator_user_id" -> _.toString) ++ creatorAccountId.map("creator_account_id" -> _.toString)
    val request = (url(s"$endpoint$urlPrefix/apikeys_dev").setContentType("application/json", Charset.forName("UTF-8"))
      <<? params
      << apiKeyUpdateRequest.encodeJson()).PUT
    GenericClient.communicate[Boolean]("/environment_settings/apikeys_dev", "PUT", request)
  }

  override def updatePublicApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Map() ++ creatorUserId.map("creator_user_id" -> _.toString) ++ creatorAccountId.map("creator_account_id" -> _.toString)
    val request = (url(s"$endpoint$urlPrefix/publicapikeys").setContentType("application/json", Charset.forName("UTF-8"))
      <<? params
      << apiKeyUpdateRequest.encodeJson()).PUT
    GenericClient.communicate[Boolean]("/environment_settings/publicapikeys", "PUT", request)
  }

  override def updatePublicApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val params = Map() ++ creatorUserId.map("creator_user_id" -> _.toString) ++ creatorAccountId.map("creator_account_id" -> _.toString)
    val request = (url(s"$endpoint$urlPrefix/publicapikeys_dev").setContentType("application/json", Charset.forName("UTF-8"))
      <<? params
      << apiKeyUpdateRequest.encodeJson()).PUT
    GenericClient.communicate[Boolean]("/environment_settings/publicapikeys_dev", "PUT", request)
  }
}
