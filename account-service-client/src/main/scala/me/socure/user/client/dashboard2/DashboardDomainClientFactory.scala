package me.socure.user.client.dashboard2
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.account.AccountDashboardDomain
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration
import scalacache.serialization.Codec
object DashboardDomainClientFactory {

  def createCached(dashboardDomainClient: DashboardDomainClient, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): CachedDashboardDomainClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl

    implicit val seqCodec: Codec[Seq[AccountDashboardDomain], Array[Byte]] = JavaSerializationCodec.codec[Seq[AccountDashboardDomain]]

    new CachedDashboardDomainClient(
      dashboardDomainClient,
      new ScalaCacheStorage[AccountDashboardDomain, Array[Byte]](),
      new ScalaCacheStorage[Seq[AccountDashboardDomain], Array[Byte]](),
      memcachedTimeout,
      memcachedTtl
    )
  }
}
