package me.socure.user.client.dashboard2

import me.socure.dashboard.NewAccountSettings
import me.socure.model.ErrorResponse
import me.socure.model.account.{ApiKeyInfo, EnvironmentWithDomains, SocialNetworkAppKeys}
import me.socure.model.dashboardv2.{ApiKeyUpdateRequest, AuditDetails, Creator, EnvironmentNameAndId}

import scala.concurrent.Future

/**
  * Created by sun<PERSON><PERSON> on 8/26/16.
  */
trait EnvironmentSettingsClient {

  def getEnvironmentSettingsWithApiKeys(accountId : Long) : Future[Either[ErrorResponse, NewAccountSettings]]

  def getEnvironmentSettingsWithApiKeys(accountId : Long, creatorAccountId: Option[Long] , creatorUserId: Option[Long], permissionChkForTransaction: Boolean = false) : Future[Either[ErrorResponse, NewAccountSettings]]

  def getEnvironmentSettingsWithApiKeysDev(accountId : Long) : Future[Either[ErrorResponse, NewAccountSettings]]

  def getEnvironmentSettingsWithApiKeysDev(accountId : Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]) : Future[Either[ErrorResponse, NewAccountSettings]]

  def updateEnvironmentDomaon(envId : Long, domains : List[String], creator: Option[Creator] = None) : Future[(AuditDetails, Either[ErrorResponse, Boolean])]

  def upsertSocialNetworkKeys(socialKeys : SocialNetworkAppKeys) : Future[Either[ErrorResponse, Long]]

  def deleteSocialNetworkKeys(id : Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Boolean]]

  def getEnironmentList(accountId: Long) : Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]]

  def getEnvironmentListForAccounts(accountIds: Seq[Long], envTypeIds: Seq[Long]): Future[Either[ErrorResponse, Map[Long, Seq[Long]]]]

  def generateApiKey(envId : Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Int]]

  def generateApiKeyDev(envId : Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Int]]

  def deprecateApiKey(envId : Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Seq[Int]]]

  def deprecateApiKeyDev(envId : Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Seq[Int]]]

  def getSocialIdsByAccountId(accountId: Long) : Future[Either[ErrorResponse, Seq[Long]]]

  def getEnvironmentWithDomains(accountId: Long): Future[Either[ErrorResponse, List[EnvironmentWithDomains]]]

  def getEnvironments(accountId: Long, role: Int, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]]

  def getApiKeys(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]]

  def getApiKeysDev(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]]

  def getPublicApiKeys(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]]

  def getPublicApiKeysDev(environmentId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Seq[ApiKeyInfo]]]

  def updateApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]]

  def updateApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]]

  def updatePublicApiKey(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]]

  def updatePublicApiKeyDev(apiKeyUpdateRequest: ApiKeyUpdateRequest, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Boolean]]
}
