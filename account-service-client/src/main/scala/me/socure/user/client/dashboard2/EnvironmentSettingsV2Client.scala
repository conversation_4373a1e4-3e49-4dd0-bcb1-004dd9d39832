package me.socure.user.client.dashboard2

import java.nio.charset.StandardCharsets
import me.socure.util.JsonEnrichments.formats
import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse

import scala.concurrent.{ExecutionContext, Future}

class EnvironmentSettingsV2Client(http: Http, endpoint : String) (implicit ec : ExecutionContext) {

  private val urlPrefix : String = "settings/environments"

  def generatePublicApiKey(environmentId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint/$urlPrefix/generate/apikeys/public") <<? Map("environmentId" -> environmentId.toString))
      .POST
      .setContentType("application/json", StandardCharsets.UTF_8)

    GenericClient.callApi[Int](http, "/settings/environments/generate/apikeys/public", "POST", request, additionalTags = Set(s"environmentId:${environmentId}"))
  }


  def deprecatePublicApikey(environmentId: Long): Unit = {
    val request = (url(s"$endpoint/$urlPrefix/deprecate/apikeys/public") <<? Map("environmentId" -> environmentId.toString))
      .POST
      .setContentType("application/json", StandardCharsets.UTF_8)

    GenericClient.callApi[Int](http, "/settings/environments/deprecate/apikeys/public", "POST", request, additionalTags = Set(s"environmentId:${environmentId}"))
  }

}
