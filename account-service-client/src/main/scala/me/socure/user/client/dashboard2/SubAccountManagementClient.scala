package me.socure.user.client.dashboard2

import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountApiKeys, AccountDetails, AccountIdName, SubAccount, SubAccountApiKeys}
import me.socure.model.dashboardv2.{AccountInfoWithEnvDetails, AccountV2, SubAccountV2, UserDetails}
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{SubAccountFormV2, UserForm, UserFormWithNoPassword}

import scala.concurrent.Future

/**
  * Created by sun<PERSON><PERSON> on 8/19/16.
  */
trait SubAccountManagementClient {

  def getSubAccountList(parentId: Long): Future[Either[ErrorResponse, Vector[AccountV2]]]

  def getSubAccounts(parentId: Long): Future[Either[ErrorResponse, List[SubAccountV2]]]

  def getSubAccountsByPublicId(publicParentId: String): Future[Either[ErrorResponse, List[SubAccountV2]]]

  def getPrimaryUserByAccountId(accountId: Long): Future[Either[ErrorResponse, UserDetails]]

  def getSubAccountsV2(parentId: Long, userId: Long, accountId: Long, page: Option[Int] = None, size: Option[Int] = None, skipPermissionChk: Boolean = false): Future[Either[ErrorResponse, List[SubAccountV2]]]

  def getSubAccountsWithEnvDetails(parentId: Long): Future[Either[ErrorResponse, Set[AccountInfoWithEnvDetails]]]

  def createSubAccount(parentId: Long, userForm: UserForm, isActive: Boolean = false): Future[Either[ErrorResponse, Boolean]]

  def createSubAccountV2(parentId: Long, userForm: SubAccountFormV2, isActive: Boolean = false): Future[Either[ErrorResponse, Boolean]]

  def createSubAccountWithNoPassword(parentId: Long, userForm: UserFormWithNoPassword) : Future[Either[ErrorResponse, UserActivationDetails]]

  def createSubAccountWithMinDetails(parentId: Long, subAccount: SubAccount, userId: Option[Long], accountId: Option[Long], primaryuserId: Option[Long]) : Future[Either[ErrorResponse, Boolean]]

  def updateSubAccount(userForm : UserForm) : Future[Either[ErrorResponse, Boolean]]

  def activate(accountId : Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]) : Future[Either[ErrorResponse, Boolean]]

  def deactivate(accountId : Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]) : Future[Either[ErrorResponse, Boolean]]

  def getAccountList(accountId : Long) : Future[Either[ErrorResponse, Vector[AccountDetails]]]

  def deprecateApiKeys() : Future[Either[ErrorResponse, String]]

  def getApiKeysForSubAccounts(accountId: Long, creatorUserId: Option[Long], creatorAccountId: Option[Long]): Future[Either[ErrorResponse, List[SubAccountApiKeys]]]

  def getApiKeysForSubAccounts(accountId: Long): Future[Either[ErrorResponse, List[SubAccountApiKeys]]]

  def validateAccountAccess(apiKey: String, creatorAccountId: Long, creatorUserId: Long, permissions: String, status: Option[String] = None): Future[Either[ErrorResponse, Boolean]]

  def validateAccountAccess(environmentTypeId: Int, creatorAccountId: Long, creatorUserId: Long, permissions: String): Future[Either[ErrorResponse, Boolean]]

  def validateAccountAccessWithOrPermissions(environmentTypeId: Int, creatorAccountId: Long, creatorUserId: Long, permissions: String, orPermissions: Option[String]): Future[Either[ErrorResponse, Boolean]]

  def getAccountAPIKeysByEnvTypeId(accountId: Long, environmentTypeId: Int): Future[Either[ErrorResponse, List[AccountApiKeys]]]

  def getSponsorBankIdsByProgramId(programId: Long): Future[Either[ErrorResponse, Long]]

  def getSponsorBankPrograms(sponsorBankId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]]
}
