package me.socure.user.client.dashboard

import me.socure.model.user.authorization.{CognitoStatus, UserAuth, UserAuthV2}
import me.socure.model.user.{PasswordChangeForm, PasswordlessLoginCredential, UserCredential, UserForm}
import me.socure.model.{ErrorResponse, Response}

import scala.concurrent.Future

/**
  * Created by gopal on 18/05/16.
  */
trait BusinessUserServiceDashboardClient {

  def validateUser(userCredential: UserCredential) : Future[Either[ErrorResponse, Response[UserAuth]]]

  def validateUserV2(userCredential: UserCredential) : Future[Either[ErrorResponse, Response[UserAuthV2]]]

  def register(user: UserForm) : Future[Either[ErrorResponse, Response[String]]]

  def lockUser(username : String) : Future[Either[ErrorResponse, Response[String]]]

  def unlockUser(username: String) : Future[Either[ErrorResponse, Response[String]]]

  def getUserAuthDetails(userId: Long, userName: String): Future[Either[ErrorResponse, Response[UserAuth]]]

  def getCognitoStatus(userName: String): Future[Either[ErrorResponse, Response[CognitoStatus]]]

  def getCognitoStatusById(userId: Long): Future[Either[ErrorResponse, Response[CognitoStatus]]]

  def updateCognitoStatus(userName: String, cognitoMigrationStatus: String): Future[Either[ErrorResponse, Response[Boolean]]]

  def regenerateActivationCode(username : String) : Future[Either[ErrorResponse, Response[String]]]

  def changePassword(passwordChangeForm: PasswordChangeForm) : Future[Either[ErrorResponse, Response[String]]]

  def validateActivationCode(activationCode : String) : Future[Either[ErrorResponse, Response[Boolean]]]

  def validateUserForPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, Response[UserAuth]]]

  def validateUserForDocsPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, Response[UserAuth]]]
}
