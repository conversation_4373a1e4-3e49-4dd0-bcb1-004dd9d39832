package me.socure.user.client.dashboard2

import java.nio.charset.Charset

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountDashboardDomain
import me.socure.model.dashboardv2.AuditDetails

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 11/04/2017.
  */
class DashboardDomainClientImpl(baseUrl : String)(implicit ec : ExecutionContext) extends DashboardDomainClient {

  val urlPrefix : String = "/dashboard/domain"

  override def getDashboardDomains(accountId: Long): Future[Either[ErrorResponse, String]] = {
    val request = url(baseUrl + s"$urlPrefix/get/${accountId.toString}")
    GenericClient.communicate[String]("/dashboard/domain/get", "GET", request, accountId = Some(accountId))
  }

  override def updateDashboardDomain(accountId: Long, domains: List[String]): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val request = url(baseUrl + s"$urlPrefix/update") << Map("account_id" -> accountId.toString, "domains" -> domains.mkString(","))
    GenericClient.communicateWithAuditDetails[Boolean]("", "POST", request, accountId = Some(accountId))
  }

  override def getDashboardDomainsByEmail(email: String): Future[Either[ErrorResponse, String]] = {
    val request = url(baseUrl + s"$urlPrefix/get_by_email/")
      .POST.setContentType("application/x-www-form-urlencoded", Charset.forName("UTF-8"))
        .addParameter("email", email)
    GenericClient.communicate[String]("/dashboard/domain/get_by_email", "POST", request)
  }

  override def getDashboardWhiteListPermissionAndDomainsByEmail(email: String): Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    val body: Map[String, String] = Map("email" -> email)

    val request = url(baseUrl + s"$urlPrefix/get_permission_domain_by_email")
      .POST.setContentType("application/x-www-form-urlencoded", Charset.forName("UTF-8"))
      .addParameter("email", email)

    GenericClient.communicate[AccountDashboardDomain]("/dashboard/domain/get_permission_domain_by_email", "POST", request)
  }

  override def getAssociatedAccountDashboardDomainListByEmailId(email: String): Future[Either[ErrorResponse, Seq[AccountDashboardDomain]]] = {

    val request = url(baseUrl + s"$urlPrefix/get_associated_account_dashboard_domain_list_by_email")
      .POST.setContentType("application/x-www-form-urlencoded", Charset.forName("UTF-8"))
      .addParameter("email", email)

    GenericClient.communicate[Seq[AccountDashboardDomain]]("/dashboard/domain/get_associated_account_dashboard_domain_list_by_email", "POST", request)
  }

  override def getDashboardWhiteListPermissionAndDomainsById(accountId: Long): Future[Either[ErrorResponse, AccountDashboardDomain]] = {
    val request = url(baseUrl + s"$urlPrefix/get_permission_domain_by_account_id/$accountId")
    GenericClient.communicate[AccountDashboardDomain]("/dashboard/domain/get_permission_domain_by_account_id", "GET", request, accountId = Some(accountId))
  }
}
