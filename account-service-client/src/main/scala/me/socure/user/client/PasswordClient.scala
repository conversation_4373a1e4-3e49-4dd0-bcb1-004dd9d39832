package me.socure.user.client

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexa<PERSON><PERSON> on 3/26/17.
  */
class PasswordClient(http: Http, baseUrl : String)(implicit ec: ExecutionContext) {

  private implicit val formats = JsonFormats.formats

  private val endpoint = baseUrl + "/password"

  def checkPassword(email: String,
                    firstname: String,
                    lastname: String,
                    password: String): Future[Either[ErrorResponse, Boolean]] = {

    val request = url(endpoint + "/syntax_check").POST
      .addParameter("email", email)
      .addParameter("firstname", firstname)
      .addParameter("lastname", lastname)
      .addParameter("password", password)

    GenericClient.callApi[Boolean](http, "/password/syntax_check", "POST", request)
  }
}
