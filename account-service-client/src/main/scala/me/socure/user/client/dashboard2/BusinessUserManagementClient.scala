package me.socure.user.client.dashboard2

import me.socure.model.ErrorResponse
import me.socure.model.account.AccountIdName
import me.socure.model.dashboardv2._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.UpdateQuicksightUserStatus

import scala.concurrent.Future

/**
  * Created by sun<PERSON><PERSON> on 8/19/16.
  */
trait BusinessUserManagementClient {

  def getUserId(username: String): Future[Either[ErrorResponse, Long]]

  def getUserList(accountId: Long): Future[Either[ErrorResponse, Vector[DashboardUserV2]]]

  def getAllUserList(accountId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Vector[DashboardUserV2]]]

  def getAllUserIdsList(accountId: Long): Future[Either[ErrorResponse, List[Long]]]

  def createBusinessUser(accountId: Long, userForm: BusinessUserFormRequest): Future[Either[ErrorResponse, UserActivationDetails]]

  def updateBusinessUser(userId: Long, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]]

  def lockBusinessUser(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, Boolean]]

  def unlockBusinessUser(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, Boolean]]

  def deleteBusinessUser(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[(AuditDetails,Either[ErrorResponse, Boolean])]

  def getBusinessUserWithRoles(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, DashboardUserWithRoles]]

  def forceResetPassword(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, UserActivationDetails]]

  def getPublicAccountIdByUserName(username: String): Future[Either[ErrorResponse, String]]

  def getUserWithRolesAndAssociations(userId: Long, creator: Creator): Future[Either[ErrorResponse, DashboardUserWithAssociations]]

  def updateBusinessUserWithAssociations(updateBusinessUserInput: UpdateBusinessUserInput): Future[(AuditDetails,Either[ErrorResponse, Boolean])]

  def createBusinessUserWithAssociations(createBusinessUserInput: CreateBusinessUserInput): Future[(AuditDetails,Either[ErrorResponse, UserActivationDetails])]

  def getUsersFilteredByPermission(userByPermissionReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]]

  def getUsers(userByPermissionReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]]

  def getUsersDetailsByIds(userIds: GetUserDetailsRequest): Future[Either[ErrorResponse, Seq[UserDetails]]]

  def getAccountsByName(accountNames: String, parentId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]]

  def getAssociatedAccounts(userId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]]

  def isUserRegisteredInQuicksight(userId: Long): Future[Either[ErrorResponse, Boolean]]

  def updateQuickSightUserStatus(updateQuicksightUserStatus: UpdateQuicksightUserStatus): Future[Either[ErrorResponse, Boolean]]

  def getSponsorBankUsers(sponsorBankAccountId: Long): Future[Either[ErrorResponse, Vector[DashboardUserV2]]]

  def updateUserTos(userId: Long): Future[Either[ErrorResponse, Boolean]]

}
