package me.socure.user.client

import dispatch.Future
import me.socure.model.ErrorResponse
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{BusinessUserWithRoles, PrimaryAccountUser, PromoteUserResponse}

/**
  * Created by gopal on 30/05/16.
  */
trait AdminUsersClient {

  def activeUsers(start: Option[Int] = None, size: Option[Int] = None, search: Option[String] = None) : Future[Either[ErrorResponse, List[PrimaryAccountUser]]]

  def activeUsersCount: Future[Either[ErrorResponse, Int]]

  def inactiveUsers: Future[Either[ErrorResponse, List[PrimaryAccountUser]]]

  def getActivationCode(email : String) : Future[Either[ErrorResponse, List[UserActivationDetails]]]

  def activateUserByActivationCode(code : String) : Future[Either[ErrorResponse, Boolean]]

  def activateUsers(emails : List[String]) : Future[Either[ErrorResponse, Boolean]]

  def getBusinessUsersWithRoles(accountId: Long): Future[Either[ErrorResponse, List[BusinessUserWithRoles]]]

  def togglePrimaryAdmin(userId: Long): Future[Either[ErrorResponse, PromoteUserResponse]]

  def doesEmailExist(email: String): Future[Either[ErrorResponse, Boolean]]
}
