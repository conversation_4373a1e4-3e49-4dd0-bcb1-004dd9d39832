package me.socure.user.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.AccessCredentials
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.authorization.UserStatus
import me.socure.model.user.{BusinessUser, BusinessUserInfo, DashboardUser, ParentAccountPrimaryUserForm, UserForm, UserFormV2, UserInfoWithAccountId, UserMagicToken}

import scala.concurrent.Future

/**
  * Created by gopal on 14/06/16.
  */
trait DashboardUserClient {

  def getAccountCrdentials(socureKey: String): Future[Either[ErrorResponse, AccessCredentials]]

  def findByEmail(email: String) : Future[Either[ErrorResponse, DashboardUser]]

  def checkToSAgreementByUser(email: String) : Future[Either[ErrorResponse, Boolean]]

  def changePassword(username : String, currentPassword : String, newPassword : String) : Future[Either[ErrorResponse, Boolean]]

  def getPrimaryUser(delegatedAdminEmail : String) : Future[Either[ErrorResponse, DashboardUser]]

  def getAccountIdByUsername(email : String) : Future[Either[ErrorResponse, Long]]

  def getUserInfoWithAccountIdByEmail(email : String) : Future[Either[ErrorResponse, UserInfoWithAccountId]]

  def logBadLoginAttempt(email: String, errorMsg: String): Future[Either[ErrorResponse, Option[UserStatus]]]

  def getInvalidAttemptsByEmail(email: String) : Future[Either[ErrorResponse, Int]]

  def unlockAfterCoolingPeriod(email: String): Future[Either[ErrorResponse, Boolean]]

  def register(user : UserForm, isActive: Boolean = false) : Future[Either[ErrorResponse, Boolean]]

  def registerV2(user : UserFormV2, isActive: Boolean = false, isDashboardV3: Boolean = false) : Future[Either[ErrorResponse, Boolean]]

  def forgotPassword(email : String) : Future[Either[ErrorResponse, UserActivationDetails]]

  def getPasswordResetCode(email : String) : Future[Either[ErrorResponse, UserActivationDetails]]

  def resetPasswordByResetCode(resetCode : String, password : String) : Future[Either[ErrorResponse, Boolean]]

  def getUserByActivationCode(activationCode : String) : Future[Either[ErrorResponse, BusinessUser]]

  def getUserByResetCode(resetCode : String) : Future[Either[ErrorResponse, BusinessUser]]

  def getUserAccountId(username : String) : Future[Either[ErrorResponse, Long]]

  def activateUserByActivationCode(code : String) : Future[Either[ErrorResponse, Boolean]]

  def isUserLocked(email : String) : Future[Either[ErrorResponse, Boolean]]

  def isUserInternal(email : String) : Future[Either[ErrorResponse, Boolean]]

  def registerPrimaryUserParentAccount(userForm: ParentAccountPrimaryUserForm) : Future[Either[ErrorResponse, UserActivationDetails]]

  def setPasswordByActivationCode(activationCode: String, password: String): Future[Either[ErrorResponse, Boolean]]

  def getUserLoginDetailsByEmail(email: String): Future[Either[ErrorResponse, Map[String, Int]]]

  def validateEmailDomain(email: String, accountType: Int): Future[Either[ErrorResponse, Boolean]]

  def generateMagicToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]]

  def generateDocumentToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]]

  def deleteMagicTokenForUser(userId: Long, userAgent: String): Future[Either[ErrorResponse, Int]]

  def PasswordlessLoginResetPassword(username : String, newPassword : String) : Future[Either[ErrorResponse, Boolean]]

  def isUserLockedByAdmin(email : String) : Future[Either[ErrorResponse, Boolean]]

  def getBusinessUserInfo(businessUserId: Long): Future[Either[ErrorResponse,BusinessUserInfo]]
  def validateInclusionListDomain(email: String,accountType: Int): Future[Either[ErrorResponse, Boolean]]
  def unlockCognitoUser(email: String): Future[Either[ErrorResponse, Int]]
}
