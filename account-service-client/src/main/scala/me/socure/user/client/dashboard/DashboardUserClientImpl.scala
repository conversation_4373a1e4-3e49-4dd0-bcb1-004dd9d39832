package me.socure.user.client.dashboard

import java.nio.charset.Charset
import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.account.AccessCredentials
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.authorization.UserStatus
import me.socure.model.user.{BusinessUser, BusinessUserInfo, DashboardUser, ParentAccountPrimaryUserForm, UserForm, UserFormV2, UserInfoWithAccountId, UserMagicToken}
import me.socure.model.ErrorResponse
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sunder<PERSON> on 6/22/16.
 */
class DashboardUserClientImpl(endpoint : String,config:Option[Config]=None)(implicit ec : ExecutionContext) extends DashboardUserClient{
  val logger : Logger = LoggerFactory.getLogger(classOf[DashboardUserClientImpl])

  val urlPrefix : String = "/dashboard"

  override def getAccountCrdentials(socureKey: String): Future[Either[ErrorResponse, AccessCredentials]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_creds/$socureKey")
    callCommunicate[AccessCredentials]("/dashboard/get_account_creds", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get access creds with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getAccountIdByUsername(email: String): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint$urlPrefix/get_accountid/$email")
    callCommunicate[Long]("/dashboard/get_accountid", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
      logger.info(s"Could not get accountId with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getUserInfoWithAccountIdByEmail(email: String): Future[Either[ErrorResponse, UserInfoWithAccountId]] = {
    val request = url(s"$endpoint$urlPrefix/get_userinfo_with_accountid/$email")
    callCommunicate[UserInfoWithAccountId]("/dashboard/get_userinfo_with_accountid", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user info and accountId with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def findByEmail(email: String): Future[Either[ErrorResponse, DashboardUser]] = {
    val request = url(s"$endpoint$urlPrefix/get_user/$email")
    callCommunicate[DashboardUser]("/dashboard/get_user", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def checkToSAgreementByUser(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/check_tos_agreement/$email")
    callCommunicate[Boolean]("/dashboard/check_tos_agreement", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user with response [${errRes.message}]")
        Left(errRes)
    }
  }
  override def getPrimaryUser(delegatedAdminEmail: String): Future[Either[ErrorResponse, DashboardUser]] = {
    val request = url(s"$endpoint$urlPrefix/get_primary_user/$delegatedAdminEmail")
    callCommunicate[DashboardUser]("/dashboard/get_primary_user", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get primary user with response [${errRes.message}]")
        Left(errRes)
    }
  }

  val userPrefix : String = "/user"

  override def logBadLoginAttempt(email: String, errorMsg: String): Future[Either[ErrorResponse, Option[UserStatus]]] = {
    val request = url(s"$endpoint$userPrefix/log_bad_login_try") << Map("email" -> email, "errormsg" -> errorMsg)
    callCommunicate[Option[UserStatus]]("/user/log_bad_login_try", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not log bad login with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getInvalidAttemptsByEmail(email: String): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$userPrefix/get_invalid_attempts").GET.addQueryParameter("email",email)
    callCommunicate[Int]("/user/get_invalid_attempts", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not fetch invalid attempts with the email [${errRes.message}]")
        Left(errRes)
    }
  }

  override def unlockAfterCoolingPeriod(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/unlock_automatically") << Map("email" -> email)
    callCommunicate[Boolean]("/user/unlock_automatically", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could reset user login with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def register(user: UserForm, isActive: Boolean = false): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/register").addQueryParameter("isActive",isActive.toString).setContentType("application/json", Charset.forName("UTF-8")) << user.encodeJson()
    callCommunicate[Boolean]("/user/register", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not register  with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def registerV2(user: UserFormV2, isActive: Boolean = false, isDashboardV3: Boolean = false): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/v2/register").addQueryParameter("isActive",isActive.toString).addQueryParameter("isDashboardV3",isDashboardV3.toString).setContentType("application/json", Charset.forName("UTF-8")) << user.encodeJson()
    callCommunicate[Boolean]("/user/v2/register", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not register  with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def registerPrimaryUserParentAccount(userForm: ParentAccountPrimaryUserForm) : Future[Either[ErrorResponse, UserActivationDetails]] = {
    val request = url(s"$endpoint$userPrefix/register_primary_user_parent_account").POST.setBody(userForm.encodeJson()).setHeader("Content-Type", "application/json")
    callCommunicate[UserActivationDetails]("/user/register_primary_user_parent_account", "GET", request)
  }

  override def forgotPassword(email: String): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val request = url(s"$endpoint/inactive/get_activation_code/$email") //Both getting activation link and forgot password link does the same
    callCommunicate[List[UserActivationDetails]]("/user/inactive/get_activation_code", "GET", request) map {
      case Right(res) => Right(res.head)
      case Left(errRes) =>
        logger.info(s"Could not get forogot password link with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def getPasswordResetCode(email: String): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val request = url(s"$endpoint/inactive/get_reset_code/$email")
    callCommunicate[List[UserActivationDetails]]("/user/inactive/get_reset_code", "GET", request) map {
      case Right(res) => Right(res.head)
      case Left(errRes) =>
        logger.error(s"Could not get forgot password link with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def changePassword(username: String, currentPassword: String, newPassword: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/change_password").addParameter("email", username).addParameter("currentpassword", currentPassword).addParameter("newpassword", newPassword).POST
    callCommunicate[Boolean]("/user/change_password", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not change password  with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def resetPasswordByResetCode(resetCode: String, password: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/reset_password_by_resetcode") << Map("resetcode" -> resetCode, "password" -> password)
    callCommunicate[Boolean]("/user/reset_password_by_resetcode", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not reset password with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def setPasswordByActivationCode(activationCode: String, password: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/set_password_by_activationcode") << Map("activationcode" -> activationCode, "password" -> password)
    callCommunicate[Boolean]("/user/set_password_by_activationcode", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not set password with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def getUserByActivationCode(activationCode: String): Future[Either[ErrorResponse, BusinessUser]] = {
    val request = url(s"$endpoint$userPrefix/get_user_by_activationcode/$activationCode")
    callCommunicate[BusinessUser]("/user/get_user_by_activationcode", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def getUserByResetCode(resetCode: String): Future[Either[ErrorResponse, BusinessUser]] = {
    val request = url(s"$endpoint$userPrefix/get_user_by_resetcode/$resetCode")
    callCommunicate[BusinessUser]("/user/get_user_by_resetcode", "GET", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.error(s"Could not get user with response : ${errRes.message}")
        Left(errRes)
    }
  }

  override def getUserAccountId(username: String): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint$userPrefix/get_account_id/$username")
    callCommunicate[Long]("/user/get_account_id", "GET", request)
  }

  override def activateUserByActivationCode(code: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/activate_user/$code")
    callCommunicate[Boolean]("/user/activate_user", "GET", request)
  }

  override def isUserLocked(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/is_user_locked/$email")
    callCommunicate[Boolean]("/user/is_user_locked", "GET", request)
  }

  override def isUserInternal(email : String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/is_user_internal/$email")
    callCommunicate[Boolean]("/user/is_user_internal", "GET", request)
  }

  override def getUserLoginDetailsByEmail(email: String): Future[Either[ErrorResponse, Map[String, Int]]] = {
    val request = url(s"$endpoint$userPrefix/get_login_details").addQueryParameter("email", email).GET
    callCommunicate[Map[String, Int]]("/user/get_login_details", "GET", request)
  }

  override def validateEmailDomain(email: String, accountType: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/validate_email_domain").addQueryParameter("email", email).addQueryParameter("accountType", accountType.toString).GET
    callCommunicate[Boolean]("/user/validate_email_domain", "GET", request)
  }

  override def isUserLockedByAdmin(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/is_user_locked_by_admin").addQueryParameter("email", email).GET
    callCommunicate[Boolean]("/user/is_user_locked_by_admin", "GET", request)
  }

  override def generateMagicToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    val request = url(s"$endpoint$userPrefix/generate_magic_token").addQueryParameter("email", email).addQueryParameter("user_agent", userAgent).POST
    callCommunicate[UserMagicToken]("/user/generate_magic_token", "POST", request)
  }

  override def generateDocumentToken(email: String, userAgent: String): Future[Either[ErrorResponse, UserMagicToken]] = {
    val request = url(s"$endpoint$userPrefix/generate_document_token").addQueryParameter("email", email).addQueryParameter("user_agent", userAgent).POST
    callCommunicate[UserMagicToken]("/user/generate_document_token", "POST", request)
  }
  override def deleteMagicTokenForUser(userId: Long, userAgent: String): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$userPrefix/delete_magic_token").addQueryParameter("userid", userId.toString).addQueryParameter("user_agent", userAgent).DELETE
    callCommunicate[Int]("/user/delete_magic_token", "DELETE", request)
  }

  override def PasswordlessLoginResetPassword(username : String, newPassword : String) : Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/passwordless_login/change_password").addParameter("email", username).addParameter("newpassword", newPassword).POST
    callCommunicate[Boolean]("/user/passwordless_login/change_password", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not change password  with response [${errRes.message}]")
        Left(errRes)
    }
  }

  private def callCommunicate[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None): Future[Either[ErrorResponse, A]] ={
    GenericClient.communicate[A](apiName, httpMethod, request, accountId = accountId,config = config)
  }

  def getBusinessUserInfo(businessUserId: Long): Future[Either[ErrorResponse,BusinessUserInfo]] = {
    val request = url(s"$endpoint$userPrefix/get_business_user_info").addQueryParameter("businessUserId", businessUserId.toString).GET
    callCommunicate[BusinessUserInfo]("/user/get_business_user_info", "GET", request)
  }

  def validateInclusionListDomain(email: String,accountType: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$userPrefix/validate_inclusion_list").addQueryParameter("email", email).addQueryParameter("accountType",accountType.toString).GET
    callCommunicate[Boolean]("/user/validate_inclusion_list", "GET", request)
  }
  def unlockCognitoUser(email: String): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$userPrefix/unlock_cognito_user").addQueryParameter("email", email).GET
    callCommunicate[Int]("/user/unlock_cognito_user", "GET", request)
  }
}
