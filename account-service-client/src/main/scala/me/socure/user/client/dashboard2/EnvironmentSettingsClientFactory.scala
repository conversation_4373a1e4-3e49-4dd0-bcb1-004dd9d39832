package me.socure.user.client.dashboard2

import com.typesafe.config.Config
import dispatch.Http
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object EnvironmentSettingsClientFactory {

  def createV2Client(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): EnvironmentSettingsV2Client = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient: Http = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new EnvironmentSettingsV2Client(httpClient, endpoint)
  }

  def getV2Client(config: Config)(implicit ec : ExecutionContext): EnvironmentSettingsV2Client = {
    val hmacConfig: Config = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new EnvironmentSettingsV2Client(httpClient, endpoint)
  }

}
