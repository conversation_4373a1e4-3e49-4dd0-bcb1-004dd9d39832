package me.socure.user.client.dashboard2

import me.socure.model.ErrorResponse
import me.socure.model.account.AccountDetails
import me.socure.model.pgp.PgpDecAndSigKeysDecoded
import me.socure.model.superadmin.AccountPgpInfo

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/15/17.
  */
trait PGPKeysManagementClient {
  def createPgpKeys(accountId : Long, expiryInSeconds: Option[Long] = None) : Future[Either[ErrorResponse, Boolean]]

  def doesPgpKeyExists(accountId : Long) : Future[Either[ErrorResponse, Boolean]]

  def deactivatePgpKeys(accountId : Long) : Future[Either[ErrorResponse, Boolean]]

  def getDecAndSigKeys(accountId : Long) : Future[Either[ErrorResponse, PgpDecAndSigKeysDecoded]]

  def getAllDecAndSigKeys(accountId : Long) : Future[Either[ErrorResponse, Seq[PgpDecAndSigKeysDecoded]]]

  def getAccountPgpPublicKey(accountId : Long) : Future[Either[ErrorResponse, String]]

  def getActivePgpAccountList() : Future[Either[ErrorResponse, List[AccountPgpInfo]]]

  def getActiveAccountWOPGPList() : Future[Either[ErrorResponse, List[AccountDetails]]]

}
