package me.socure.user.client
import java.nio.charset.Charset

import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.{BusinessUserWithRoles, PrimaryAccountUser, PromoteUserResponse}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}
/**
  * Created by gopal on 30/05/16.
  */
class AdminUsersClientImpl(baseUrl : String,config:Option[Config]=None)(implicit ec : ExecutionContext) extends AdminUsersClient {

  val prefix = "/user"

  override def activeUsers(start: Option[Int] = None, size: Option[Int] = None, search: Option[String] = None): Future[Either[ErrorResponse, List[PrimaryAccountUser]]] = {
      val params = Map() ++ start.map("start" -> _.toString) ++ size.map("size" -> _.toString) ++ search.map("search" -> _.toString)
      val request = url(s"$baseUrl$prefix/active_users_v2").GET <<? params
    callCommunicate[List[PrimaryAccountUser]]("/user/active_users_v2", "GET", request)
  }

  override def activeUsersCount: Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$baseUrl$prefix/active_users_count").GET
    callCommunicate[Int]("/user/active_users_count", "GET", request)
  }

  override def inactiveUsers: Future[Either[ErrorResponse, List[PrimaryAccountUser]]] = {
    val request = url(s"$baseUrl/inactive/inactive_users_v2")
    callCommunicate[List[PrimaryAccountUser]]("/inactive/inactive_users_v2", "GET", request)
  }

  override def getActivationCode(email : String) : Future[Either[ErrorResponse, List[UserActivationDetails]]] = {
    val request = url(s"$baseUrl/inactive/get_activation_code/" + email)
    callCommunicate[List[UserActivationDetails]]("/inactive/get_activation_code", "GET", request)
  }

  override def activateUserByActivationCode(code: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$baseUrl$prefix/activate_user/" + code)
    callCommunicate[Boolean]("/user/activate_user", "GET", request)
  }

  override def activateUsers(emails: List[String]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$baseUrl$prefix/activate_users").setContentType("application/json", Charset.forName("UTF-8")) << emails.encodeJson()
    callCommunicate[Boolean]("/user/activate_user", "POST", request)
  }

   override def getBusinessUsersWithRoles(accountId: Long): Future[Either[ErrorResponse, List[BusinessUserWithRoles]]] = {
    val request = url(s"$baseUrl$prefix/get_users_with_roles/$accountId").GET
     callCommunicate[List[BusinessUserWithRoles]]("/user/get_users_with_roles", "GET", request, accountId = Some(accountId))
   }


  override def togglePrimaryAdmin(userId: Long): Future[Either[ErrorResponse, PromoteUserResponse]] = {
    val request = url(s"$baseUrl$prefix/$userId/promote/primary").PUT
    callCommunicate[PromoteUserResponse]("/user/promote/primary", "PUT", request)
  }

  override def doesEmailExist(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$baseUrl$prefix/does_email_exist").addQueryParameter("email", email).POST
    callCommunicate[Boolean]("/user/does_email_exist", "POST", request)
  }

  private def callCommunicate[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None): Future[Either[ErrorResponse, A]] ={
    GenericClient.communicate[A](apiName, httpMethod, request, accountId = accountId,config = config)
  }
}
