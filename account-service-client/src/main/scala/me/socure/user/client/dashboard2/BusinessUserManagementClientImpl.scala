package me.socure.user.client.dashboard2

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.{ErrorResponse, Response}
import me.socure.model.account.AccountIdName
import me.socure.model.dashboardv2._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.UpdateQuicksightUserStatus
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunder<PERSON> on 8/19/16.
  */
class BusinessUserManagementClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends BusinessUserManagementClient {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  val urlPrefix = "/dashboard_user"
  val account_apis_url_prefix ="/dashboard_account"

  override def getUserList(accountId: Long): Future[Either[ErrorResponse, Vector[DashboardUserV2]]] = {
    val request = url(s"$endpoint$urlPrefix/list_users/$accountId")
    GenericClient.communicate[Vector[DashboardUserV2]]("/dashboard_user/list_users", "GET", request, accountId = Some(accountId)) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user list for accountId [$accountId] with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getAllUserList(accountId: Long, creatorAccountId: Option[Long], creatorUserId: Option[Long]): Future[Either[ErrorResponse, Vector[DashboardUserV2]]] = {
    val queryParams = (creatorAccountId, creatorUserId) match {
      case (Some(accountId), Some(userId)) => Map("creator_account_id" -> accountId.toString, "creator_user_id" -> userId.toString)
      case (Some(accountId), _) => Map("creator_account_id" -> accountId.toString)
      case (_, Some(userId)) => Map("creator_user_id" -> userId.toString)
      case (_, _) => Map.empty
    }
    val request = url(s"$endpoint$urlPrefix/list_users/$accountId") <<? queryParams
    GenericClient.communicate[Vector[DashboardUserV2]]("/dashboard_user/list_users", "GET", request, accountId = Some(accountId)) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user list for accountId [$accountId] with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def getAllUserIdsList(accountId: Long): Future[Either[ErrorResponse, List[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/list_userids/$accountId?showSubAccount=true")
    GenericClient.communicate[List[Long]]("/dashboard_user/list_userids", "GET", request, accountId = Some(accountId)) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user list for accountId [$accountId] with response [${errRes.message}]")
        Left(errRes)
    }
  }
  
  override def createBusinessUser(accountId: Long, userForm: BusinessUserFormRequest): Future[Either[ErrorResponse, UserActivationDetails]] = {
    getEnvironmentList(userForm.accountId).flatMap((environments: Either[ErrorResponse, Seq[EnvironmentNameAndId]]) => {
      val businessUserForm = environments match {
        case Right(environmentNameAndIds) => BusinessUserForm(
          accountId = userForm.accountId,
          userInfo = userForm.userInfo,
          environmentRoles = userForm.environmentRoles.map(role => {
            EnvironmentRoles(
              roles = role.roles,
              environmentId = environmentNameAndIds.
                              find(env => env.name.equals(role.environment)).
                              headOption.map(_.id).getOrElse(throw new Exception("Failed to match environment role: " + role.environment))
            )
          }),
          creator = userForm.creator
        )
        case Left(_) => BusinessUserForm(userForm.accountId, List.empty, userForm.userInfo)
      }
      val request = url(s"$endpoint$urlPrefix/create_user").setContentType("application/json", Charset.forName("UTF-8")) << businessUserForm.encodeJson()
      GenericClient.communicate[UserActivationDetails]("/dashboard_user/create_user", "POST", request, accountId = Some(accountId)) map {
        case Right(res) => Right(res)
        case Left(errRes) =>
          logger.info(s"Could not create business user : ${errRes.message}")
          Left(errRes)
      }
    })
  }

  override def updateBusinessUser(userId: Long, userForm: BusinessUserForm): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_user") << Map("userid" -> userId.toString, "user" -> userForm.encodeJson())
    GenericClient.communicate[Boolean]("/dashboard_user/update_user", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not update business user information : ${errRes.message}")
        Left(errRes)
    }
  }

  override def lockBusinessUser(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/lock_user?user_id=${userId.toString}")
      .setContentType("application/json", Charset.forName("UTF-8")) << accountWithCreator.encodeJson()
    GenericClient.communicate[Boolean]("/dashboard_user/lock_user", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Locking business user failed for userId [$userId]: ${errRes.message}")
        Left(errRes)
    }
  }

  override def unlockBusinessUser(userId: Long ,accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/unlock_user?user_id=${userId.toString}")
      .setContentType("application/json", Charset.forName("UTF-8")) << accountWithCreator.encodeJson()
    GenericClient.communicate[Boolean]("/dashboard_user/unlock_user", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Unlocking business user failed for userId [$userId]: ${errRes.message}")
        Left(errRes)
    }
  }

  override def deleteBusinessUser(userId: Long,  accountWithCreator: Option[AccountWithCreator]): Future[(AuditDetails,Either[ErrorResponse, Boolean])] = {
    val request = url(s"$endpoint$urlPrefix/delete_user?user_id=${userId.toString}")
      .setContentType("application/json", Charset.forName("UTF-8")) << accountWithCreator.encodeJson()
    GenericClient.communicateWithAuditDetails[Boolean]("/dashboard_user/delete_user", "POST", request)
  }

  override def getBusinessUserWithRoles(userId: Long, accountWithCreatorOpt: Option[AccountWithCreator]): Future[Either[ErrorResponse, DashboardUserWithRoles]] = {
    val request = accountWithCreatorOpt match {
      case None => url(s"$endpoint$urlPrefix/get_user_information/$userId")
      case Some(accountWithCreator) => url(s"$endpoint$urlPrefix/get_user_information/$userId").addQueryParameter("accountWithCreator", accountWithCreator.encodeJson())
    }
    GenericClient.communicate[DashboardUserWithRoles]("/dashboard_user/get_user_information", "GET", request)
  }

  override def forceResetPassword(userId: Long, accountWithCreator: Option[AccountWithCreator]): Future[Either[ErrorResponse, UserActivationDetails]] = {
    val request = url(s"$endpoint$urlPrefix/force_reset_password?user_id=${userId.toString}")
      .setContentType("application/json", Charset.forName("UTF-8")) << accountWithCreator.encodeJson()
    GenericClient.communicate[UserActivationDetails]("/dashboard_user/force_reset_password", "POST", request)
  }

  override def getUserId(username: String): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint$urlPrefix/get_user_id/$username").GET
    GenericClient.communicate[Long]("/dashboard_user/get_user_id", "GET", request)
  }

  private def getEnvironmentList(accountId: Long): Future[Either[ErrorResponse, Seq[EnvironmentNameAndId]]] = {
    val request = url(s"$endpoint/environment_settings/get_environments/$accountId")
    GenericClient.communicate[Seq[EnvironmentNameAndId]]("/dashboard_user/environment_settings/get_environments", "GET", request, accountId = Some(accountId))
  }

  override def getPublicAccountIdByUserName(username: String): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/get_public_account_id/$username").GET
    GenericClient.communicate[String]("/dashboard_user/get_public_account_id", "GET", request)
  }

  override def getUserWithRolesAndAssociations(userId: Long, creator: Creator): Future[Either[ErrorResponse, DashboardUserWithAssociations]] = {
    val request = (url(s"$endpoint$urlPrefix/get_user_information_v2/$userId") <<? Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)).GET
    GenericClient.communicate[DashboardUserWithAssociations]("/dashboard_user/get_user_information_v2", "GET", request)
  }

  override def updateBusinessUserWithAssociations(updateBusinessUserInput: UpdateBusinessUserInput): Future[(AuditDetails,Either[ErrorResponse, Boolean])] = {
    val request = (url(s"$endpoint$urlPrefix/update_business_user").setContentType("application/json", Charset.forName("UTF-8")) << updateBusinessUserInput.encodeJson()).POST
    GenericClient.communicateWithAuditDetails[Boolean]("/dashboard_user/update_business_user", "POST", request)
  }

  override def createBusinessUserWithAssociations(createBusinessUserInput: CreateBusinessUserInput): Future[(AuditDetails,Either[ErrorResponse, UserActivationDetails])] = {
    val request = (url(s"$endpoint$urlPrefix/create_business_user").setContentType("application/json", Charset.forName("UTF-8")) << createBusinessUserInput.encodeJson()).POST
    GenericClient.communicateWithAuditDetails[UserActivationDetails]("/dashboard_user/create_business_user", "POST", request)
  }

  override def getUsersFilteredByPermission(usersByPermissionReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]] = {
    val request = (url(s"$endpoint$urlPrefix/list_users/by_permission").setContentType("application/json", Charset.forName("UTF-8")) << usersByPermissionReq.encodeJson()).POST
    GenericClient.communicate[UsersByPermissionFilterResponse]("/dashboard_user/list_users/by_permission", "POST", request)
  }

  override def getUsers(usersByPermissionReq: UsersByPermissionFilterRequest): Future[Either[ErrorResponse, UsersByPermissionFilterResponse]] = {
    val request = (url(s"$endpoint$urlPrefix/list_users/case_mgmt/by_permission").setContentType("application/json", Charset.forName("UTF-8")) << usersByPermissionReq.encodeJson()).POST
    GenericClient.communicate[UsersByPermissionFilterResponse]("/dashboard_user/list_users/case_mgmt/by_permission", "POST", request)
  }

  override def getUsersDetailsByIds(userIds: GetUserDetailsRequest): Future[Either[ErrorResponse, Seq[UserDetails]]] = {
    val request = (url(s"$endpoint$urlPrefix/get_user_details").setContentType("application/json", Charset.forName("UTF-8")) << userIds.encodeJson()).POST
    GenericClient.communicate[Seq[UserDetails]]("/dashboard_user/get_user_details", "POST", request)
  }

  override def getAccountsByName (accountNames: String, parentId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    val request = (url(s"$endpoint$account_apis_url_prefix/get_account_lists_by_name") <<? Map("account_names" -> accountNames, "parent_account_id" -> parentId.toString)).GET
    GenericClient.communicate[Seq[AccountIdName]]("/dashboard_account/get_account_lists_by_name", "GET", request)
  }

  override def getAssociatedAccounts (userId: Long): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    val request = (url(s"$endpoint$account_apis_url_prefix/get_associated_accounts_for_user") <<? Map("user_id" -> userId.toString)).GET
    GenericClient.communicate[Seq[AccountIdName]]("/dashboard_account/get_associated_accounts_for_user", "GET", request)
  }

  override def isUserRegisteredInQuicksight(userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/is_quicksight_user") <<? Map("user_id" -> userId.toString)).GET
    GenericClient.communicate[Boolean]("/dashboard_user/is_quicksight_user", "GET", request)
  }

  override def updateQuickSightUserStatus(updateQuicksightUserStatus: UpdateQuicksightUserStatus): Future[Either[ErrorResponse,Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/update_quicksight_user_status").setContentType("application/json", Charset.forName("UTF-8")) << updateQuicksightUserStatus.encodeJson()).PUT
    GenericClient.communicate[Boolean]("/dashboard_user/update_quicksight_user_status", "POST", request)
  }

  override def getSponsorBankUsers(sponsorBankAccountId:Long): Future[Either[ErrorResponse, Vector[DashboardUserV2]]] = {
    val request = url(s"$endpoint$urlPrefix/sponsor_bank_users/$sponsorBankAccountId")
    GenericClient.communicate[Vector[DashboardUserV2]]("/dashboard_user/sponsor_bank_users", "GET", request, accountId = Some(sponsorBankAccountId)) map {
      case Right(res) =>
        Right(res)
      case Left(errRes) =>
        logger.info(s"Could not get user list for accountId [$sponsorBankAccountId] with response [${errRes.message}]")
        Left(errRes)
    }
  }

  override def updateUserTos(userId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/update_user_tos/$userId")).PUT
    GenericClient.communicate[Boolean]("/dashboard_user/update_user_tos", "PUT", request)
  }

}


