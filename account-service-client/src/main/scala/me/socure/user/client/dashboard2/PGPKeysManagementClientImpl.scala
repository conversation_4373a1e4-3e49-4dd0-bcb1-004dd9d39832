package me.socure.user.client.dashboard2

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountDetails
import me.socure.model.pgp.{PgpDecAndSigKeys, PgpDecAndSigKeysDecoded}
import me.socure.model.superadmin.AccountPgpInfo
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 5/15/17.
  */
class PGPKeysManagementClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends PGPKeysManagementClient {
  val urlPrefix : String = "/pgp"

  val logger : Logger = LoggerFactory.getLogger(getClass)

  override def createPgpKeys(accountId: Long, expiryInSeconds : Option[Long]= None): Future[Either[ErrorResponse, Boolean]] = {
    val params = Map("account_id" ->  s"$accountId") ++ expiryInSeconds.map("expiryInSeconds" -> _.toString)
    val request = url(s"$endpoint$urlPrefix/create_pgp_keys") << params
    GenericClient.communicate[Boolean]("/pgp/create_pgp_keys", "POST", request, accountId = Some(accountId))
  }

  override def deactivatePgpKeys(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/deactivate_pgp_keys") << Map("account_id" -> s"$accountId")
    GenericClient.communicate[Boolean]("/pgp/deactivate_pgp_keys", "POST", request, accountId = Some(accountId))
  }

  override def getDecAndSigKeys(accountId: Long): Future[Either[ErrorResponse, PgpDecAndSigKeysDecoded]] = {
    val request = url(s"$endpoint$urlPrefix/get_dec_and_sig_keys/$accountId")
    GenericClient.communicate[PgpDecAndSigKeys]("/pgp/get_dec_and_sig_keys", "GET", request, accountId = Some(accountId)).map {
      case Right(b) => Right(PgpDecAndSigKeysDecoded.decode(b))
      case Left(a) => Left(a)
    }
  }

  override def getAllDecAndSigKeys(accountId : Long) : Future[Either[ErrorResponse, Seq[PgpDecAndSigKeysDecoded]]] = {
    val request = url(s"$endpoint$urlPrefix/get_all_dec_and_sig_keys/$accountId")
    GenericClient.communicate[Seq[PgpDecAndSigKeys]]("/pgp/get_all_dec_and_sig_keys", "GET", request, accountId = Some(accountId)).map {
      case Right(keys) => Right(keys.map(key => PgpDecAndSigKeysDecoded.decode(key)))
      case Left(a) => Left(a)
    }
  }

  override def getAccountPgpPublicKey(accountId: Long): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/get_public_key/$accountId")
    GenericClient.communicate[String]("/pgp/get_public_key", "GET", request, accountId = Some(accountId))
  }

  override def getActivePgpAccountList(): Future[Either[ErrorResponse, List[AccountPgpInfo]]] = {
    val request = url(s"$endpoint$urlPrefix/get_active_pgp_account_list")
    GenericClient.communicate[List[AccountPgpInfo]]("/pgp/get_active_pgp_account_list", "GET", request)
  }

  override def getActiveAccountWOPGPList(): Future[Either[ErrorResponse, List[AccountDetails]]] = {
    val request = url(s"$endpoint$urlPrefix/get_active_account_wo_pgp_list")
    GenericClient.communicate[List[AccountDetails]]("/pgp/get_active_pgp_account_list", "GET", request)
  }

  override def doesPgpKeyExists(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/does_pgp_key_exists/$accountId")
    GenericClient.communicate[Boolean]("/pgp/does_pgp_key_exists", "GET", request, accountId = Some(accountId))
  }
}
