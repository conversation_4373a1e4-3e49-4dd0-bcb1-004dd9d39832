package me.socure.user.client.dashboard2

import me.socure.model.ErrorResponse
import me.socure.model.account.AccountDashboardDomain
import me.socure.model.dashboardv2.AuditDetails

import scala.concurrent.Future

/**
  * Created by gopal on 11/04/2017.
  */
trait DashboardDomainClient {

  def getDashboardDomains(accountId : Long) : Future[Either[ErrorResponse, String]]

  def getDashboardDomainsByEmail(email : String) : Future[Either[ErrorResponse, String]]

  def getDashboardWhiteListPermissionAndDomainsByEmail(email : String) : Future[Either[ErrorResponse, AccountDashboardDomain]]

  def getDashboardWhiteListPermissionAndDomainsById(accountId: Long) : Future[Either[ErrorResponse, AccountDashboardDomain]]

  def getAssociatedAccountDashboardDomainListByEmailId(email: String) : Future[Either[ErrorResponse, Seq[AccountDashboardDomain]]]

  def updateDashboardDomain(accountId : Long, domains : List[String]) : Future[(AuditDetails, Either[ErrorResponse, Boolean])]

}
