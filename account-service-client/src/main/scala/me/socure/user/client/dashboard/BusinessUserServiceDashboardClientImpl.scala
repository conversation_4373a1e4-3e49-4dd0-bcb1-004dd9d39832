package me.socure.user.client.dashboard
import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.account.client.http.NonSecuredHttp
import me.socure.model.user.authorization.{CognitoStatus, UserAuth, UserAuthV2}
import me.socure.model.user.{PasswordChangeForm, PasswordlessLoginCredential, UserCredential, UserForm}
import me.socure.model.{ErrorResponse, Response}
import me.socure.util.JsonEnrichments.{JsonDecoder, JsonEncoder}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by gopal on 18/05/16.
  */
class BusinessUserServiceDashboardClientImpl(baseURL : String)(implicit executionContext: ExecutionContext) extends BusinessUserServiceDashboardClient {

  val prefix : String = "/user"

  override def validateUser(userCredential: UserCredential): Future[Either[ErrorResponse, Response[UserAuth]]] = {
    val request = url(baseURL + s"$prefix/validate_v2").POST.setBody(userCredential.encodeJson()).setHeader("Content-Type", "application/json")
    GenericClient.communicateR[Response[UserAuth]]("/user/validate_v2", "POST", request)
  }

  override def validateUserV2(userCredential: UserCredential): Future[Either[ErrorResponse, Response[UserAuthV2]]] = {
    val request = url(baseURL + s"$prefix/v3/validate").POST.setBody(userCredential.encodeJson()).setHeader("Content-Type", "application/json")
    GenericClient.communicateR[Response[UserAuthV2]]("/user/v3/validate", "POST", request)
  }

  override def getUserAuthDetails(userId: Long, userName: String): Future[Either[ErrorResponse, Response[UserAuth]]] = {
    val request = url(baseURL + s"$prefix/get_validated_user").GET.addQueryParameter("userId", userId.toString).addQueryParameter("userName",userName)
    GenericClient.communicateR[Response[UserAuth]]("/user/get_validated_user", "GET", request)
  }
  override def getCognitoStatus(userName:String): Future[Either[ErrorResponse, Response[CognitoStatus]]] = {
    val request = url(baseURL + s"$prefix/get_cognito_status").GET.addQueryParameter("userName", userName)
    GenericClient.communicateR[Response[CognitoStatus]]("/user/get_cognito_status", "GET", request)
  }

  override def getCognitoStatusById(userId: Long): Future[Either[ErrorResponse, Response[CognitoStatus]]] = {
    val request = url(baseURL + s"$prefix/get_cognito_status_by_id").GET.addQueryParameter("userId", userId.toString)
    GenericClient.communicateR[Response[CognitoStatus]]("/user/get_cognito_status_by_id", "GET", request)
  }

  override def updateCognitoStatus(userName: String, cognitoMigrationStatus: String): Future[Either[ErrorResponse, Response[Boolean]]] = {
    val request = url(baseURL + s"$prefix/update_cognito_status").POST.addQueryParameter("userName", userName).addQueryParameter("migrationStatus", cognitoMigrationStatus)
    GenericClient.communicateR[Response[Boolean]]("/user/update_cognito_status", "POST", request)
  }
  override def validateUserForPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, Response[UserAuth]]] = {
    val request = url(baseURL + s"$prefix/passwordless/validate_v2").POST.setBody(credential.encodeJson()).setHeader("Content-Type", "application/json")
    GenericClient.communicateR[Response[UserAuth]]("/user/passwordless/validate_v2", "POST", request)
  }

  override def validateUserForDocsPasswordlessLogin(credential: PasswordlessLoginCredential) : Future[Either[ErrorResponse, Response[UserAuth]]] = {
    val request = url(baseURL + s"$prefix/docs/passwordless/validate_v2").POST.setBody(credential.encodeJson()).setHeader("Content-Type", "application/json")
    GenericClient.communicateR[Response[UserAuth]]("/user/docs/passwordless/validate_v2", "POST", request)
  }

  override def lockUser(username: String): Future[Either[ErrorResponse, Response[String]]] = ???

  override def regenerateActivationCode(username: String): Future[Either[ErrorResponse, Response[String]]] = ???

  override def validateActivationCode(activationCode: String): Future[Either[ErrorResponse, Response[Boolean]]] = ???

  override def changePassword(passwordChangeForm: PasswordChangeForm): Future[Either[ErrorResponse, Response[String]]] = ???

  override def register(user: UserForm): Future[Either[ErrorResponse, Response[String]]] = ???

  override def unlockUser(username: String): Future[Either[ErrorResponse, Response[String]]] = ???


}



