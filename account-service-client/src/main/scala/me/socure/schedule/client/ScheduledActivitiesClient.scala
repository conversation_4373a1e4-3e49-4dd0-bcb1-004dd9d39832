package me.socure.schedule.client

import me.socure.model.ErrorResponse

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 03/04/2017.
  */
trait ScheduledActivitiesClient {

  def lockInactiveBusinssUser : Future[Either[ErrorResponse, String]]

  def lockNotLoggedInUserAfterSignup : Future[Either[ErrorResponse, Boolean]]

  def invalidateActivationToken : Future[Either[ErrorResponse, Boolean]]

  def invalidatePasswordResetToken: Future[Either[ErrorResponse, Boolean]]

  def passwordResetNotification : Future[Either[ErrorResponse, Boolean]]

  def deleteExpiredMagicTokens : Future[Either[ErrorResponse, Boolean]]

}
