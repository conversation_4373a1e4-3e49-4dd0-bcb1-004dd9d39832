package me.socure.schedule.client

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON> on 03/04/2017.
  */
class ScheduledActivitiesClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends ScheduledActivitiesClient {

  val logger : Logger = LoggerFactory.getLogger(this.getClass)

  val urlPrefix = "/schedule"

  override def lockInactiveBusinssUser: Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/lock_inactive_user").POST
    GenericClient.communicate[String]("/schedule/lock_inactive_user", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not finish lock business user task [${errRes.message}]")
        Left(errRes)
    }
  }

  override def lockNotLoggedInUserAfterSignup: Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/lock_user_not_loggedin_after_signup").POST
    GenericClient.communicate[Boolean]("/schedule/lock_user_not_loggedin_after_signup", "POST", request) map {
      case Right(res) => Right(res)
      case Left(errRes) =>
        logger.info(s"Could not finish lock business user task [${errRes.message}]")
        Left(errRes)
    }
  }

  override def invalidateActivationToken: Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/invalidate_activation_tokens").POST
    GenericClient.communicate[Boolean]("/schedule/invalidate_activation_tokens", "POST", request)
  }

  override def invalidatePasswordResetToken: Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/invalidate_reset_tokens").POST
    GenericClient.communicate[Boolean]("/schedule/invalidate_reset_tokens", "POST", request)
  }

  override def passwordResetNotification: Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/password_reset_notification").POST
    GenericClient.communicate[Boolean]("/schedule/password_reset_notification", "POST", request)
  }

  override def deleteExpiredMagicTokens : Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_expired_magic_tokens").DELETE
    GenericClient.communicate[Boolean]("/schedule/delete_expired_magic_tokens", "DELETE", request)
  }

}
