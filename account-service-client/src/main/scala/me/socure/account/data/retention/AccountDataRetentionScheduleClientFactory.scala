package me.socure.account.data.retention

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext

object AccountDataRetentionScheduleClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): AccountDataRetentionScheduleClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountDataRetentionScheduleClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): AccountDataRetentionScheduleClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountDataRetentionScheduleClientImpl(httpClient, endpoint)
  }
}
