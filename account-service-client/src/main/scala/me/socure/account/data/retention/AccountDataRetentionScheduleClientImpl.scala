package me.socure.account.data.retention

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, AccountDataRetentionScheduleWithHierarchy, DtoAccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._

class AccountDataRetentionScheduleClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountDataRetentionScheduleClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override def getAccountDataRetentionSchedule(): Future[Either[ErrorResponse, Seq[AccountDataRetentionSchedule]]] = {
    val request = (url(endpoint) / "data" / "retention" / "schedule"  ).GET
    GenericClient.callApi[Seq[AccountDataRetentionSchedule]](http, "/data/retention/schedule/", "GET", request)
  }

  override def getAccountDataRetentionSchedule(accountId: Long): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]] = {
    val request = (url(endpoint) / "data" / "retention" / "schedule" / "account" / accountId ).GET
    GenericClient.callApi[DtoAccountDataRetentionSchedule](http, "/data/retention/schedule/account/", "GET", request)
  }

  override def updateAccountDataRetentionSchedule(accountId: Long, updateAccountDataRetentionSchedule: UpdateAccountDataRetentionSchedule): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]] = {
    val request = (url(endpoint) / "data" / "retention" / "schedule" / "account" / accountId )
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << updateAccountDataRetentionSchedule.encodeJson()
    GenericClient.callApi[DtoAccountDataRetentionSchedule](http, "/data/retention/schedule/account/", "POST", request)
  }

  override def getAccountDataRetentionScheduleWithHierarchy(accountId: Long): Future[Either[ErrorResponse, AccountDataRetentionScheduleWithHierarchy]] = {
    val request = (url(endpoint) / "data" / "retention" / "schedule" / "account-hierarchy" / accountId ).GET
    GenericClient.callApi[AccountDataRetentionScheduleWithHierarchy](http, "/data/retention/schedule/account/", "GET", request)
  }
}
