package me.socure.account.data.retention

import me.socure.model.ErrorResponse
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, AccountDataRetentionScheduleWithHierarchy, DtoAccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}

import scala.concurrent.Future

trait AccountDataRetentionScheduleClient {
  def getAccountDataRetentionSchedule(): Future[Either[ErrorResponse, Seq[AccountDataRetentionSchedule]]]
  def getAccountDataRetentionScheduleWithHierarchy(accountId: Long): Future[Either[ErrorResponse, AccountDataRetentionScheduleWithHierarchy]]
  def getAccountDataRetentionSchedule(accountId: Long): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]]
  def updateAccountDataRetentionSchedule(accountId: Long, updateAccountDataRetentionSchedule: UpdateAccountDataRetentionSchedule): Future[Either[ErrorResponse, DtoAccountDataRetentionSchedule]]
}
