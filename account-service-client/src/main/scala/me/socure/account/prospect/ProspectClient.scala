package me.socure.account.prospect

import me.socure.model.ErrorResponse
import me.socure.model.prospect.{ProspectExclusionDetail, ProspectExclusionInput, ProspectInclusionDetail, ProspectInclusionInput}

import scala.concurrent.Future

trait ProspectClient {

  def getExclusionList(start: Option[Int] = None, size: Option[Int] = None, search: Option[String] = None): Future[Either[ErrorResponse, Seq[ProspectExclusionDetail]]]
  def getExclusionListTotalCount: Future[Either[ErrorResponse, Int]]
  def upsertExclusionDetail(exclusionInput: ProspectExclusionInput): Future[Either[ErrorResponse, Boolean]]
  def deleteExclusionDetail(id: Long):  Future[Either[ErrorResponse, Boolean]]

  def getInclusionList(start: Option[Int] = None, size: Option[Int] = None, search: Option[String] = None): Future[Either[ErrorResponse, Seq[ProspectInclusionDetail]]]
  def getInclusionListTotalCount: Future[Either[ErrorResponse, Int]]
  def upsertInclusionDetail(inclusionInput: ProspectInclusionInput): Future[Either[ErrorResponse, Boolean]]
  def deleteInclusionDetail(id: Long):  Future[Either[ErrorResponse, Boolean]]

}
