package me.socure.account.prospect

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.{HMACEncrypter, SHAHMACEncrypter}
import me.socure.common.hmac.filter.HMACHttpRequestFilter
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext

object ProspectClientFactory {
  def create(realm: String, version: String, endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): ProspectClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new ProspectClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): ProspectClient = {
    val hmacConfig = config.getConfig("hmac")
    val realm = hmacConfig.getString("realm")
    val version = hmacConfig.getString("version")
    val endpoint = config.getString("endpoint")
    val secretKey = hmacConfig.getString("secret.key")
    val strength = hmacConfig.getInt("strength")
    val hmacEncrypter = new SHAHMACEncrypter(
      secretKey = secretKey,
      strength = strength
    )
    create(realm = realm, version = version, endpoint = endpoint, encrypter = hmacEncrypter)
}}
