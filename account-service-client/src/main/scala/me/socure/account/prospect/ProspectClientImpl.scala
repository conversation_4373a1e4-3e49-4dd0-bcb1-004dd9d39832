package me.socure.account.prospect

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.prospect.{ProspectExclusionDetail, ProspectExclusionInput, ProspectInclusionDetail, ProspectInclusionInput}
import org.apache.http.entity.ContentType
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._

class ProspectClientImpl (http: Http, baseUrl: String)(implicit ec: ExecutionContext) extends ProspectClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = "prospect"
  private val inclusionPrefix = "inclusion"
  private val exclusionPrefix = "exclusion"

  override def getExclusionList(start: Option[Int], size: Option[Int], search: Option[String]): Future[Either[ErrorResponse, Seq[ProspectExclusionDetail]]] = {
    val params = Map() ++ start.map("start" -> _.toString) ++ size.map("size" -> _.toString) ++ search.map("search" -> _)
    val request = (url(baseUrl) / urlPrefix / exclusionPrefix).GET <<? params
    GenericClient.callApi[Seq[ProspectExclusionDetail]](http, "/prospect/exclusion", "GET", request)
  }

  override def getExclusionListTotalCount: Future[Either[ErrorResponse, Int]] = {
    val request = (url(baseUrl) / urlPrefix / exclusionPrefix / "total_count").GET
    GenericClient.callApi[Int](http, "/prospect/exclusion/total_count", "GET", request)
  }

  override def upsertExclusionDetail(exclusionInput: ProspectExclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    val jsonBody = Serialization.write(exclusionInput)
    val request =(url(baseUrl) / urlPrefix / exclusionPrefix)
      .PUT
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/prospect/exclusion", "PUT", request)

  }

  override def deleteExclusionDetail(id: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request =(url(baseUrl) / urlPrefix / exclusionPrefix/ id).DELETE
    GenericClient.callApi[Boolean](http, "/prospect/exclusion", "DELETE", request)
  }

  override def getInclusionList(start: Option[Int], size: Option[Int], search: Option[String]): Future[Either[ErrorResponse, Seq[ProspectInclusionDetail]]] = {
    val params = Map() ++ start.map("start" -> _.toString) ++ size.map("size" -> _.toString) ++ search.map("search" -> _)
    val request = (url(baseUrl) / urlPrefix / inclusionPrefix).GET <<? params
    GenericClient.callApi[Seq[ProspectInclusionDetail]](http, "/prospect/inclusion", "GET", request)
  }

  override def getInclusionListTotalCount: Future[Either[ErrorResponse, Int]] = {
    val request = (url(baseUrl) / urlPrefix / inclusionPrefix / "total_count").GET
    GenericClient.callApi[Int](http, "/prospect/inclusion/total_count", "GET", request)
  }

  override def upsertInclusionDetail(inclusionInput: ProspectInclusionInput): Future[Either[ErrorResponse, Boolean]] = {
    val jsonBody = Serialization.write(inclusionInput)
    val request = (url(baseUrl) / urlPrefix / inclusionPrefix)
      .PUT
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/prospect/inclusion", "PUT", request)
  }

  override def deleteInclusionDetail(id: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(baseUrl) / urlPrefix / inclusionPrefix / id).DELETE
    GenericClient.callApi[Boolean](http, "/prospect/inclusion", "DELETE", request)
  }
}
