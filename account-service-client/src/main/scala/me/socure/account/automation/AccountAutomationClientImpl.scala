package me.socure.account.automation

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountProvisioningDetails, UpdateAccountProvisioningDetails}
import me.socure.model.bundles.Bundle
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._

class AccountAutomationClientImpl (http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountAutomationClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = "automation"

  override def getAccountProvisioningDetails(accountId: Long): Future[Either[ErrorResponse, AccountProvisioningDetails]] = {
    val request = (url(endpoint) / urlPrefix / "account" / accountId ).GET
    GenericClient.callApi[AccountProvisioningDetails](http, "/automation/account", "GET", request)
  }

  override def updateAccountProvisioningDetails(accountId: Long, updateAccountProvisioningDetails: UpdateAccountProvisioningDetails): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / "account" / accountId )
          .setContentType("application/json", Charset.forName("UTF-8"))
          .POST << updateAccountProvisioningDetails.encodeJson()
        GenericClient.callApi[Boolean](http, "/automation/account", "POST", request)
  }

  override def getBundles(): Future[Either[ErrorResponse, Set[Bundle]]] = {
    val request = (url(endpoint) / urlPrefix / "bundles" ).GET
    GenericClient.callApi[Set[Bundle]](http, "/bundles", "GET", request)
  }
}
