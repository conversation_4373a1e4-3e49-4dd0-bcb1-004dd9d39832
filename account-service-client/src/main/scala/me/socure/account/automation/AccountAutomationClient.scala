package me.socure.account.automation

import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountProvisioningDetails, UpdateAccountProvisioningDetails}
import me.socure.model.bundles.Bundle

import scala.concurrent.Future

trait AccountAutomationClient {
  def getBundles(): Future[Either[ErrorResponse, Set[Bundle]]]
  def getAccountProvisioningDetails(accountId: Long): Future[Either[ErrorResponse, AccountProvisioningDetails]]
  def updateAccountProvisioningDetails(accountId: Long, updateAccountProvisioningDetails: UpdateAccountProvisioningDetails): Future[Either[ErrorResponse, Boolean]]
}