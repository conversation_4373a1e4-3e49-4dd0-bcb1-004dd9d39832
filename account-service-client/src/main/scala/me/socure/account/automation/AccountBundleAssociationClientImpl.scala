package me.socure.account.automation

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountBundleAssociation, DtoAccountBundleAssociation}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}

class AccountBundleAssociationClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountBundleAssociationClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = "bundles"

  override def getAccountBundleAssociation(accountId: Long): Future[Either[ErrorResponse, DtoAccountBundleAssociation]] = {
    val request = (url(endpoint) / urlPrefix / "account" / accountId ).GET
    GenericClient.callApi[DtoAccountBundleAssociation](http, "/bundles/account", "GET", request)
  }

  override def upsertAccountBundleAssociation(accountBundleAssociation: AccountBundleAssociation): Future[Either[ErrorResponse, DtoAccountBundleAssociation]] = {
    val request = (url(endpoint) / urlPrefix )
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << accountBundleAssociation.encodeJson()
    GenericClient.callApi[DtoAccountBundleAssociation](http, "/bundles", "POST", request)
  }

}
