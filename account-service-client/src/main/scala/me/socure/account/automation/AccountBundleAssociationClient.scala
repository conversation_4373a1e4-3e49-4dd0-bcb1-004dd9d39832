package me.socure.account.automation

import me.socure.model.ErrorResponse
import me.socure.model.account.automation.{AccountBundleAssociation, DtoAccountBundleAssociation}

import scala.concurrent.Future

trait AccountBundleAssociationClient {
  def getAccountBundleAssociation(accountId: Long): Future[Either[ErrorResponse, DtoAccountBundleAssociation]]
  def upsertAccountBundleAssociation(accountBundleAssociation: AccountBundleAssociation): Future[Either[ErrorResponse, DtoAccountBundleAssociation]]
}
