package me.socure.account.automation

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext

object AccountAutomationClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): AccountAutomationClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountAutomationClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): AccountAutomationClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountAutomationClientImpl(httpClient, endpoint)
  }
}