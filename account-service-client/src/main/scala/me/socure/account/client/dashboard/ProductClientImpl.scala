package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.{AccountProducts, ErrorResponse, UpdateProductsRequest}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class ProductClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends ProductClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = url(endpoint + "/products")

  override def getProductsForAccount(accountId: Long): Future[Either[ErrorResponse, Seq[AccountProducts]]] = {
    val request = urlPrefix / "account" / accountId
    GenericClient.callApi[Seq[AccountProducts]](http, "/products/account", "GET", request)
  }

  override def updateProductsForAccount(updateProductsRequest: UpdateProductsRequest): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((urlPrefix / "account").setContentType("application/json", Charset.forName("UTF-8"))
      << updateProductsRequest.encodeJson()).POST
    GenericClient.callApi[Boolean](http, "/products/account", "POST", request)
  }
}
