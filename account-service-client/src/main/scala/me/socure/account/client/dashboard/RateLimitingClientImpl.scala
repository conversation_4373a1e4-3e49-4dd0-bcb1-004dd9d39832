package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.RateLimiterPublicAPI.RateLimiterPublicAPI
import me.socure.model.account.{DeleteRateLimitingInput, SaveRateLimitingInput, UpdateRateLimitingInput}
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class RateLimitingClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends RateLimitingClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: Req = url(endpoint) / "api" / "1.0" / "rate-limiting"
  val v2UrlPrefix: Req = url(endpoint) / "api" / "2.0" / "rate-limiting"

  override def getRateLimits(accountId: Option[Long]): Future[Either[ErrorResponse, Seq[RateLimitingEntry]]] = {
    val request  = accountId match {
      case Some(accId) => urlPrefix <<? Map("accountId"  -> accId.toString)
      case None => urlPrefix
    }
    GenericClient.callApi[Seq[RateLimitingEntry]](http, "/api/1.0/rate-limiting", "GET", request, accountId = accountId)
  }

  override def getRateLimits(accountId: Long, environmentTypeId: Long, api: String): Future[Either[ErrorResponse, Seq[RateLimitingConfig]]] = {
    val request = (urlPrefix /  "policies") <<? Map("accountId"  -> accountId.toString, "environmentType" -> environmentTypeId.toString, "apiId" -> api)
    GenericClient.callApi[Seq[RateLimitingConfig]](http, "/api/1.0/rate-limiting/policies", "GET", request, accountId = Some(accountId))
  }

  override def getRateLimits(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]] = {
    val request = (urlPrefix /  "policies_v2") <<? Map("accountId"  -> accountId.toString, "environmentType" -> environmentTypeId.toString, "apiIds" -> apiIds.mkString(","))
    GenericClient.callApi[Map[String, Seq[RateLimitingConfig]]](http, "/api/1.0/rate-limiting/policies_v2", "GET", request, accountId = Some(accountId))
  }

  override def getRateLimitsV2(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]] = {
    val request = (v2UrlPrefix /  "policies_v2") <<? Map("accountId"  -> accountId.toString, "environmentType" -> environmentTypeId.toString, "apiIds" -> apiIds.mkString(","))
    GenericClient.callApi[Map[String, Seq[RateLimitingConfig]]](http, "/api/2.0/rate-limiting/policies_v2", "GET", request, accountId = Some(accountId))
  }

  override def getRateLimitingApis: Future[Either[ErrorResponse, Seq[RateLimiterPublicAPI]]] = {
    val request = urlPrefix / "apis"
    GenericClient.callApi[Seq[RateLimiterPublicAPI]](http, "/api/1.0/rate-limiting/apis", "GET", request)
  }

  override def saveRateLimits(saveRateLimitingInput: SaveRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = (urlPrefix.setContentType("application/json", Charset.forName("UTF-8")) << saveRateLimitingInput.encodeJson()).POST
    GenericClient.callApi[Boolean](http, "/api/1.0/rate-limiting", "POST", request)
  }

  override def updateRateLimits(updateRateLimitingInput: UpdateRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = (urlPrefix.setContentType("application/json", Charset.forName("UTF-8")) << updateRateLimitingInput.encodeJson()).PUT
    GenericClient.callApi[Boolean](http, "/api/1.0/rate-limiting", "PUT", request)

  }

  override def upsertRateLimits(updateRateLimitingInputs: Seq[UpdateRateLimitingInput]): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((urlPrefix / "upsert" ).setContentType("application/json", Charset.forName("UTF-8")) << updateRateLimitingInputs.encodeJson()).PUT
    GenericClient.callApi[Boolean](http, "/api/1.0/rate-limiting/upsert", "PUT", request)

  }

  override def deleteRateLimits(deleteRateLimitingInput: DeleteRateLimitingInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = (urlPrefix.setContentType("application/json", Charset.forName("UTF-8")) << deleteRateLimitingInput.encodeJson()).DELETE
    GenericClient.callApi[Boolean](http, "/api/1.0/rate-limiting", "DELETE", request)
  }

  override def deleteRateLimitsInBulk(deleteRateLimitingInputs: Seq[DeleteRateLimitingInput]): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((urlPrefix / "bulk").setContentType("application/json", Charset.forName("UTF-8")) << deleteRateLimitingInputs.encodeJson()).DELETE
    GenericClient.callApi[Boolean](http, "/api/1.0/rate-limiting/bulk", "DELETE", request)
  }

}
