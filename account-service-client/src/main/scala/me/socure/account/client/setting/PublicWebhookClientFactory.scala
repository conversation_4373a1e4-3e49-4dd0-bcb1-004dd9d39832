package me.socure.account.client.setting

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.{HMAC<PERSON><PERSON>rypter, SHAHMACEncrypter}
import me.socure.common.hmac.filter.HMACHttpRequestFilter
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext.Implicits.global

object PublicWebhookClientFactory {

  def create(endpoint: String): PublicWebhookClient = {
    val httpClient = new NonSecuredHttpFactory().getHttpClient()
    new PublicWebhookClientImpl(httpClient, endpoint)
  }

  def create(config: Config): PublicWebhookClient = {

    val endpoint = config.getString("endpoint")
    create(endpoint = endpoint)
  }

}
