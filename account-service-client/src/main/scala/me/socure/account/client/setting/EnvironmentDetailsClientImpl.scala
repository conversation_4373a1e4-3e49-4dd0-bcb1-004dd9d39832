package me.socure.account.client.setting

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.EnvironmentData
import me.socure.util.JsonEnrichments.formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class EnvironmentDetailsClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends EnvironmentDetailsClient {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "/environment"

  def getEnvironmentDataById(environmentId: Long): Future[Either[ErrorResponse, EnvironmentData]] = {
    val request = url(s"$endpoint$urlPrefix/" + environmentId.toString)
    GenericClient.callApi[EnvironmentData](http, "/environment", "GET", request)
  }

  override def getEnvironmentDataByIdV2(environmentId: Long): Future[Either[ErrorResponse, Option[EnvironmentData]]] = {
    val request = url(endpoint) / "environment" / "v2" / environmentId.toString
    GenericClient.callApi[Option[EnvironmentData]](http, "/environment/v2", "GET", request)
  }
}
