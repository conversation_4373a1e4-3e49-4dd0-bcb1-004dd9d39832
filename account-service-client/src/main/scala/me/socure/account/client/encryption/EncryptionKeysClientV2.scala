package me.socure.account.client.encryption

import me.socure.model.ErrorResponse
import me.socure.model.encryption.{CustomerKeyDetails, EncryptedKeyDetails, KmsArnDetails}

import scala.concurrent.Future

trait EncryptionKeysClientV2 {

  def getCustomerKeys(accountId: Long): Future[Either[ErrorResponse, Seq[KmsArnDetails]]]

  def getAllActiveKeys(accountId: Long): Future[Either[ErrorResponse, EncryptedKeyDetails]]

  def testCustomerKms(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]]

  def generateCustomerKeys(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]]
}
