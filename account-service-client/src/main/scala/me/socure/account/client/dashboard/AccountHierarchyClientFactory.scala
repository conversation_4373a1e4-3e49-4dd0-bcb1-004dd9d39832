package me.socure.account.client.dashboard

import com.typesafe.config.Config
import dispatch.Http
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

object AccountHierarchyClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): AccountHierarchyClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountHierarchyClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): AccountHierarchyClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountHierarchyClientImpl(httpClient, endpoint)
  }

  def createCached(httpClient: Http, endpoint: String, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): AccountHierarchyClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl
    implicit val codecForRootParentId: Codec[Long, Array[Byte]] = JavaSerializationCodec.codec[Long]

    new CachedAccountHierarchyClientImpl(httpClient, endpoint, new ScalaCacheStorage[Long, Array[Byte]](),
      memcachedTimeout, memcachedTtl)
  }

  def createCachedUsingConfig(config: Config, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                              memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): AccountHierarchyClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    createCached(httpClient, endpoint, memcachedClient, memcachedTtl, memcachedTimeout)
  }

}
