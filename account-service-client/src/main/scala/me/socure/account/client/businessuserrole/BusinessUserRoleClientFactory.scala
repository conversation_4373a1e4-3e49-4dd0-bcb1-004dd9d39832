package me.socure.account.client.businessuserrole

import java.net.InetSocketAddress
import java.util.concurrent.TimeUnit

import com.typesafe.config.Config
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.model.account.BusinessUserRolesWithPermissions
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.{Duration, FiniteDuration}

/**
 * <AUTHOR>
 */
object BusinessUserRoleClientFactory {

  def get(config: Config)(implicit ec: ExecutionContext): BusinessUserRoleClient = {
    val accountServiceEndpoint: String = if (config.hasPath("account.service.url")) {
      config.getString("account.service.url")
    } else {
      config.getString("account.service.endpoint")
    }
    get(accountServiceEndpoint, Some(config))
  }

  def get(accountServiceEndpoint: String, config: Option[Config] = None)(implicit ec: ExecutionContext): BusinessUserRoleClient = {
    new BusinessUserRoleClient(accountServiceEndpoint, config)
  }

  def getCached(config: Config)(implicit ec: ExecutionContext): BusinessUserRoleClientCached = {
    val memcachedHost: String = config.getString("memcached.endpoint")
    val memcachedPort: Int = config.getInt("memcached.port")
    val memcachedClient: MemcachedClient = new MemcachedClient(new InetSocketAddress(memcachedHost, memcachedPort))
    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    val ttl: Int = config.getInt("business.user.role.ttl")
    val cacheTtl: FiniteDuration = Duration.apply(ttl, TimeUnit.MINUTES)

    implicit val implicitTtl: Option[Duration] = Some(cacheTtl)
    implicit val codec: Codec[List[BusinessUserRolesWithPermissions], Array[Byte]] = JavaSerializationCodec.codec[List[BusinessUserRolesWithPermissions]]

    val client: BusinessUserRoleClient = get(config)
    new BusinessUserRoleClientCached(
      underlying = client,
      permissionsStorage = new ScalaCacheStorage[List[BusinessUserRolesWithPermissions], Array[Byte]](),
      memcachedTtl = Some(cacheTtl)
    )
  }

}
