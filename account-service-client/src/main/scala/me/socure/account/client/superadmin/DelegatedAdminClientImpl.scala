package me.socure.account.client.superadmin

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.account.client.http.NonSecuredHttp
import me.socure.model.superadmin.{AccountName, DelegatedAdmin}
import me.socure.model.{ErrorResponse, Response}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/9/16.
  */
class DelegatedAdminClientImpl(endpoint : String)(implicit val ec : ExecutionContext) extends DelegatedAdminClient {

  private val urlPrefix : String = "/delegated"

  override def getAccountName: Future[Either[ErrorResponse, Vector[AccountName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_all_admins")
    GenericClient.communicate[Vector[AccountName]]("/delegated/get_all_admins", "GET", request)
  }

  override def getDelegatedAdmin(email: String): Future[Either[ErrorResponse, List[DelegatedAdmin]]] = {
    val request = url(s"$endpoint$urlPrefix/delegated_admins/" + email)
    GenericClient.communicate[List[DelegatedAdmin]]("/delegated/delegated_admins", "GET", request)
  }

  override def createdDelegatedAdmin(adminemail: String, email : String, firstname : String, lastname : String, company : String, contact : String, password: String, roles : Option[Set[Int]]): Future[Either[ErrorResponse, Boolean]] = {
    val delegatedAdminUser = DelegatedAdmin(0, firstname, lastname, company, contact, email, roles)
    val request = url(s"$endpoint$urlPrefix/create_delegated_admin") << Map("user" -> delegatedAdminUser.encodeJson()) << Map("adminemail" -> adminemail) << Map("password" -> password)
    GenericClient.communicate[Boolean]("/delegated/create_delegated_admin", "GET", request)
  }

  override def deleteDelegatedAdmin(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_delegated_admin") << Map("email" -> email)
    GenericClient.communicate[Boolean]("/delegated/delete_delegated_admin", "GET", request)
  }

  override def updatePassword(email: String, password: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_password") << Map("email" -> email, "password" -> password)
    GenericClient.communicate[Boolean]("/delegated/update_password", "GET", request)
  }

  override def updateRoles(email: String, roles: Set[Int]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_roles") << Map("email" -> email, "roles" -> roles.mkString(","))
    GenericClient.communicate[Boolean]("/delegated/update_roles", "GET", request)
  }

  override def promoteDelegatedAdmin(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/promote") << Map("email" -> email)
    GenericClient.communicate[Boolean]("/delegated/promote", "GET", request)
  }

  override def isUserExist(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/is_user_exist/$email")
    GenericClient.communicate[Boolean]("/delegated/is_user_exist", "GET", request)
  }

  override def updateUserInformation(currentEmail: String, userInfo: DelegatedAdmin): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_user_information") << Map("email" -> currentEmail, "user" -> userInfo.encodeJson())
    GenericClient.communicate[Boolean]("/delegated/update_user_information", "GET", request)
  }
}
