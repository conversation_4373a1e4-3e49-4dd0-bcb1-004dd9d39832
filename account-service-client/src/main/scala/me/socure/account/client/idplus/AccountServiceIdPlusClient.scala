package me.socure.account.client.idplus

import me.socure.model.{AccountInformation, DocvAccountInformation, ErrorResponse}

import scala.concurrent.Future

/**
  * Created by alexa<PERSON><PERSON> on 5/9/16.
  */
trait AccountServiceIdPlusClient {

  def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]]
  def fetchByPublicApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]]
  def fetchByPublicAccountId(publicAccountId: String): Future[Either[ErrorResponse, DocvAccountInformation]]
}
