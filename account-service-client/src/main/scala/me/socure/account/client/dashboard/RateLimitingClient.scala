package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.RateLimiterPublicAPI.RateLimiterPublicAPI
import me.socure.model.account.{DeleteRateLimitingInput, SaveRateLimitingInput, UpdateRateLimitingInput}
import me.socure.model.ratelimiter.{RateLimitingConfig, RateLimitingEntry}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait RateLimitingClient {
  def getRateLimits(accountId: Option[Long]): Future[Either[ErrorResponse, Seq[RateLimitingEntry]]]
  def getRateLimits(accountId: Long, environmentTypeId: Long, api: String): Future[Either[ErrorResponse, Seq[RateLimitingConfig]]]
  def getRateLimitsV2(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]]
  def getRateLimits(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]]
  def getRateLimitingApis: Future[Either[ErrorResponse, Seq[RateLimiterPublicAPI]]]
  def saveRateLimits(saveRateLimitingInput: SaveRateLimitingInput): Future[Either[ErrorResponse, Boolean]]
  def updateRateLimits(updateRateLimitingInput: UpdateRateLimitingInput): Future[Either[ErrorResponse, Boolean]]
  def deleteRateLimits(deleteRateLimitingInput: DeleteRateLimitingInput) : Future[Either[ErrorResponse, Boolean]]
  def upsertRateLimits(updateRateLimitingInputs: Seq[UpdateRateLimitingInput]): Future[Either[ErrorResponse, Boolean]]
  def deleteRateLimitsInBulk(deleteRateLimitingInputs: Seq[DeleteRateLimitingInput]): Future[Either[ErrorResponse, Boolean]]
}
