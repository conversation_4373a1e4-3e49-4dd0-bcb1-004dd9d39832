package me.socure.account.client

import com.github.blemale.scaffeine.Cache
import com.typesafe.config.Config
import dispatch.{Http, Req}
import me.socure.account.client.http.NonSecuredHttp
import me.socure.account.client.idplus.CacheService
import me.socure.common.data.core.provider._
import me.socure.common.metrics.models.MetricTags
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.microservice.client.MicroServiceResponseParser
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.dynamic.control.center.v2.factory.EndpointRouterFactory
import me.socure.dynamic.control.center.v2.utils.EndpointRouter
import me.socure.model.dashboardv2.AuditDetails
import me.socure.model.{ErrorResponse, Response}
import me.socure.util.JsonEnrichments.JsonDecoder
import org.apache.http.HttpStatus
import org.json4s.{DefaultFormats, Formats}
import org.slf4j.{<PERSON><PERSON>, LoggerFactory}
import scalacache.ScalaCache

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}
import scala.util.control.NonFatal
/**
 * Created by gopal on 08/07/16.
 */
object GenericClient {

  private val prefixedMetrics: Metrics = JavaMetricsFactory.get(MetricTags.httpMetricPrefix)
  private val cacheMetrics: Metrics = JavaMetricsFactory.get(MetricTags.memcachedPrefix)
  private val cacheMetricsErrorAspect = "error.count"
  private implicit val formats: Formats = DefaultFormats
  val logger: Logger = LoggerFactory.getLogger(getClass)
  val ConfigPrefix="account.service"
  private val serviceName = "account-service"
  private var evaluator : EndpointRouter = _

  def getBaseTags(apiName: String,
                  isInternal: Boolean = true,
                  mSource: String = "client",
                  accountIdOpt: Option[Long] = None,
                  environmentNameOpt: Option[String] = None,
                  additionalTags: Set[String] = Set.empty[String],
                  httpMethod: Option[String] = None
                 ): MetricTags = {

    val metricTags = MetricTags(
      serviceName = Some(serviceName),
      apiName = Some(apiName),
      isInternal = Some(isInternal),
      mSource = Some(mSource),
      accountId = accountIdOpt,
      environmentId = environmentNameOpt.map(_.toLowerCase),
      tags = additionalTags
    )

    val extraTags = Set(
      if (httpMethod.isDefined) Some(s"http_method:${httpMethod.get}") else None
    ).flatten

    metricTags.copy(tags = additionalTags ++ extraTags)
  }

  def callApi[A](http: Http, apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String],attributeKeyValue:Option[(String,String)]=None,config:Option[Config]=None)(implicit manifest: Manifest[A], ec: ExecutionContext, formats: Formats): Future[Either[ErrorResponse, A]] = {
    getEvaluator(config).getRequest(request, attributeKeyValue) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = http(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map { r =>
        try {
          MicroServiceResponseParser.parseResponse[A](r.getStatusCode, r.getResponseBody)
        } catch {
          case NonFatal(ex) =>
            Left(ErrorResponse(HttpStatus.SC_INTERNAL_SERVER_ERROR, ex.getMessage))
        }
      }
    }
  }


  def callApiWithAuditDetails[A](http: Http, apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String], attributeKeyValue: Option[(String, String)] = None, config: Option[Config] = None)(implicit manifest: Manifest[A], ec: ExecutionContext, formats: Formats): Future[(AuditDetails, Either[ErrorResponse, A])] = {
    getEvaluator(config).getRequest(request, attributeKeyValue) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = http(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map {
        case response if response.getStatusCode == 200 =>
          val responseTuple: (AuditDetails, Either[ErrorResponse, A]) = response.getResponseBody().decodeJson[Response[(AuditDetails, Either[ErrorResponse, A])]].data
          logger.info(s"Generic client audit : ${responseTuple._1}")
          logger.info(s"Generic client resp : ${responseTuple._2}")
          (responseTuple._1, responseTuple._2)

        case response =>
          logger.info(s"Generic client response : ${response.getResponseBody()}")
          val responseTuple: (AuditDetails, Either[ErrorResponse, A]) = response.getResponseBody().decodeJson[Response[(AuditDetails, Either[ErrorResponse, A])]].data
          (responseTuple._1, responseTuple._2)
      }
    }
  }

  def callApiWithOptionalError[A](http: Http, apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String],config:Option[Config]=None)(implicit manifest: Manifest[A], ec: ExecutionContext, formats: Formats): Future[Either[Option[ErrorResponse], A]] = {
    callApi(http,apiName,httpMethod,request,accountId,additionalTags,config=config) map { res =>
      res.left.map(v => Option.apply(v))
    }
  }

  def communicate[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String], attributeKeyValue:Option[(String,String)]=None,config: Option[Config]=None)(implicit ec: ExecutionContext): Future[Either[ErrorResponse, A]] = {
    getEvaluator(config).getRequest(request,attributeKeyValue) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = NonSecuredHttp.client(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map {
        case response if response.getStatusCode == 200 =>
          Right(response.getResponseBody.decodeJson[Response[A]].data)
        case response =>
          Left(response.getResponseBody.decodeJson[Response[ErrorResponse]].data)
      }
    }
  }

  def communicateWithAuditDetails[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String], attributeKeyValue: Option[(String, String)] = None, config: Option[Config] = None)(implicit ec: ExecutionContext): Future[(AuditDetails,Either[ErrorResponse, A])] = {
    getEvaluator(config).getRequest(request, attributeKeyValue) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = NonSecuredHttp.client(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map {

        case response if response.getStatusCode == 200 =>
          val responseTuple: (AuditDetails, Either[ErrorResponse,A]) = response.getResponseBody().decodeJson[Response[(AuditDetails, Either[ErrorResponse,A])]].data
          logger.info(s"Generic client audit : ${responseTuple._1}")
          logger.info(s"Generic client resp : ${responseTuple._2}")
          (responseTuple._1,responseTuple._2)

        case response =>
          logger.info(s"Generic client response : ${response.getResponseBody()}")
          val responseTuple: (AuditDetails, Either[ErrorResponse,A]) =response.getResponseBody().decodeJson[Response[(AuditDetails,  Either[ErrorResponse,A])]].data
          (responseTuple._1,responseTuple._2)
      }
    }
  }

  def communicateR[A : Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext) : Future[Either[ErrorResponse,A]] = {
    getEvaluator().getRequest(request) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = NonSecuredHttp.client(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map {
        case response if response.getStatusCode == 200 =>
          Right(response.getResponseBody.decodeJson[A])
        case response =>
          Left(response.getResponseBody.decodeJson[Response[ErrorResponse]].data)
      }
    }
  }

  def communicateB(apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext) : Future[Either[ErrorResponse,Boolean]] = {
    getEvaluator().getRequest(request) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags)
      val futureResponse = NonSecuredHttp.client(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()

      futureResponse.map {
        case response if response.getStatusCode == 200 =>
          Right(true)
        case response =>
          Left(response.getResponseBody.decodeJson[Response[ErrorResponse]].data)
      }
    }
  }

  def communicateG(apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None, additionalTags: Set[String] = Set.empty[String],config:Option[Config]=None)(implicit ec: ExecutionContext): Future[org.asynchttpclient.Response] = {
    getEvaluator(config).getRequest(request) flatMap { res =>
      val tags = getBaseTags(apiName = apiName, accountIdOpt = accountId, httpMethod = Some(httpMethod), additionalTags = additionalTags ++ res.tags )
      val futureResponse = NonSecuredHttp.client(res.modifiedReq)
        .withMetricTags(
          metrics = prefixedMetrics,
          baseTags = tags,
          onSuccessTags = response => MetricTags(httpStatus = Some(response.getStatusCode))
        )()
      futureResponse
    }
  }

  def getCache[A](apiName: String, cache: Storage[A], cacheKey: String, timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext): Future[Option[A]] = {

    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:get"))

    val futureResponse = cache.get(cacheKey)
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = basetags,
        onSuccessTags = response => if(response isDefined) MetricTags(tags = Set(s"cache:hit")) else MetricTags(tags = Set(s"cache:miss"))
      )()

    val futureWithTimeout = if (timeoutOpt isDefined) timeoutOpt.get._1 timeout(futureResponse, timeoutOpt.get._2) else futureResponse
    futureWithTimeout recover {
      case ex: Exception =>
        val errorTags : MetricTags = MetricTags(exceptionClass = Some(ex.getClass.getSimpleName), tags = if(timeoutOpt.isDefined) Set(s"duration: ${timeoutOpt.get._2.toString}") else Set.empty)
        val tags = basetags.getTags ++ errorTags.getTags
        cacheMetrics.increment(cacheMetricsErrorAspect, tags.toSeq:_*)
        logger.error(s"Exception while fetching value from memcached for key $cacheKey", ex)
        None
    }
  }

  def storeCache[A](apiName: String, cache: Storage[A], cacheKey: String, result: A, timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext): Future[Unit] = {

    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:put"))

    val futureResponse = cache.store(cacheKey, result)
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = basetags
      )()

    val futureWithTimeout = if (timeoutOpt isDefined) timeoutOpt.get._1 timeout(futureResponse, timeoutOpt.get._2) else futureResponse
    futureWithTimeout recover {
      case ex: Exception =>
        val errorTags : MetricTags = MetricTags(exceptionClass = Some(ex.getClass.getSimpleName), tags = if(timeoutOpt.isDefined) Set(s"timeout: ${timeoutOpt.get._2.toString}") else Set.empty)
        val tags = basetags.getTags ++ errorTags.getTags
        cacheMetrics.increment(cacheMetricsErrorAspect, tags.toSeq:_*)
        logger.error(s"Exception while storing value in memcached for key $cacheKey", ex)
    }
  }

  def removeCache(apiName: String, scalaCache: ScalaCache[_], cacheKeys: Set[String], additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext): Future[Unit] = {

    val tags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:reomve"))
    val seqFuture = Future sequence(cacheKeys map scalaCache.cache.remove)

    val futureResponse = seqFuture.map(_ => ())
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = tags
      )()

    futureResponse
  }

  def getCacheWithCacheService[A <: AnyRef](apiName: String, cache: CacheService[String, A], cacheKey: String, timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext): Future[Option[A]] = {

    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:get"))

    val futureResponse = cache.get(cacheKey)
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = basetags,
        onSuccessTags = response => if(response isDefined) MetricTags(tags = Set(s"cache:hit")) else MetricTags(tags = Set(s"cache:miss"))
      )()

    val futureWithTimeout = if (timeoutOpt isDefined) timeoutOpt.get._1 timeout(futureResponse, timeoutOpt.get._2) else futureResponse
    futureWithTimeout recover {
      case ex: Exception =>
        val errorTags : MetricTags = MetricTags(exceptionClass = Some(ex.getClass.getSimpleName), tags = if(timeoutOpt.isDefined) Set(s"duration: ${timeoutOpt.get._2.toString}") else Set.empty)
        val tags = basetags.getTags ++ errorTags.getTags
        cacheMetrics.increment(cacheMetricsErrorAspect, tags.toSeq:_*)
        logger.error(s"Exception while fetching value from memcached for key $cacheKey", ex)
        None
    }
  }


  def getFromCache[A](apiName: String, cache: Cache[String, A], cacheKey: String, additionalTags: Set[String] = Set.empty[String]): Option[A] = {
    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:get"))
    try {
      val data = cache.getIfPresent(cacheKey)
      data match {
        case None => cacheMetrics.increment("op:get", (basetags.getTags ++ Set(s"cache.miss")).toSeq: _*)
        case Some(x) => cacheMetrics.increment("op:get", (basetags.getTags ++ Set(s"cache.hit")).toSeq: _*)
      }
      data
    } catch {
      case e: Exception =>
        cacheMetrics.increment(cacheMetricsErrorAspect, basetags.getTags.toSeq:_*)
        logger.error(s"Exception while storing value in local cache for key $cacheKey", e)
        None
    }
  }

  def storeInCache[A](apiName: String, cache: Cache[String, A], cacheKey: String, result: A, additionalTags: Set[String] = Set.empty[String]): Unit = {

    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:put"))
    try {
      cache.put(cacheKey, result)
    }
    catch {
      case e: Exception =>
        cacheMetrics.increment(cacheMetricsErrorAspect, basetags.getTags.toSeq:_*)
        logger.error(s"Exception while storing value in local cache for key $cacheKey", e)
    }
  }

  def removeFromCache(apiName: String, cache: Cache[String, _], cacheKeys: Set[String], additionalTags: Set[String] = Set.empty[String]): Unit = {
    val tags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:remove"))
    try {
      cacheKeys.foreach(key => cache.invalidate(key))
    }
    catch {
      case e: Exception =>
        cacheMetrics.increment(cacheMetricsErrorAspect, tags.getTags.toSeq:_*)
        logger.error(s"Exception while removing value in local cache for key", e)
    }
  }

  def storeCacheWithCacheService[A <: AnyRef](apiName: String, cache: CacheService[String, A], cacheKey: String, result: A, timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)] = None, additionalTags: Set[String] = Set.empty[String])(implicit ec: ExecutionContext): Future[Unit] = {

    val basetags = getBaseTags(apiName = apiName, additionalTags = additionalTags ++ Set("op:put"))

    val futureResponse = cache.put(cacheKey, result)
      .withMetricTags(
        metrics = cacheMetrics,
        baseTags = basetags
      )()

    val futureWithTimeout = if (timeoutOpt isDefined) timeoutOpt.get._1 timeout(futureResponse, timeoutOpt.get._2) else futureResponse
    futureWithTimeout recover {
      case ex: Exception =>
        val errorTags : MetricTags = MetricTags(exceptionClass = Some(ex.getClass.getSimpleName), tags = if(timeoutOpt.isDefined) Set(s"timeout: ${timeoutOpt.get._2.toString}") else Set.empty)
        val tags = basetags.getTags ++ errorTags.getTags
        cacheMetrics.increment(cacheMetricsErrorAspect, tags.toSeq:_*)
        logger.error(s"Exception while storing value in memcached for key $cacheKey", ex)
    }
  }

  def setTimeoutForFutures[A](futures: Set[Future[A]], timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)])(implicit ec: ExecutionContext): Future[Set[A]] = {
    val futureSeq = Future.sequence(futures)
    if (timeoutOpt isDefined) timeoutOpt.get._1 timeout(futureSeq, timeoutOpt.get._2) else futureSeq
  }

  def getEvaluator(config:Option[Config]=None)(implicit ec: ExecutionContext):EndpointRouter ={
    Option(evaluator) match {
      case Some(value) => value
      case _ => evaluator = EndpointRouterFactory.getRouter(ConfigPrefix,config)
        evaluator
    }
  }

}
