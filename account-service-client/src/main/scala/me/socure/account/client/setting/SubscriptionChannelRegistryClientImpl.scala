package me.socure.account.client.setting

import java.nio.charset.Charset

import com.typesafe.config.Config
import dispatch.{Http, Req, url}
import me.socure.account.client.GenericClient
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, Metadata, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails}
import org.json4s.jackson.Serialization
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

class SubscriptionChannelRegistryClientImpl(endpointRaw: String, http: Http,config:Option[Config]=None)(implicit ec: ExecutionContext) extends SubscriptionChannelRegistryClient {
  implicit val formats = JsonFormats.formats
  private val urlPrefix = "/settings/channel"
  val endpoint = getSafeEndpoint(endpointRaw)

  override def getSubscriptionChannelRegistry(id: Long,accountId: Long): Future[Either[ErrorResponse, DtoSubscriptionChannelRegistry]] = {
    val request = url(s"$endpoint$urlPrefix/$id?accountId=${accountId}")
    callGenericClient[DtoSubscriptionChannelRegistry]( "/settings/channel/id", "GET", request, accountId = Some(accountId))
  }

  override def createSubscriptionChannelRegistry(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(dtoSubscriptionChannelRegistry)
    val request = url(s"$endpoint$urlPrefix")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBodyEncoding(Charset.forName("UTF-8")) << requestBody
    callGenericClient[Boolean]("/settings/channel", "POST", request)
  }

  override def createSubscriptionChannelRegistryV2(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(dtoSubscriptionChannelRegistry)
    val request = url(s"$endpoint$urlPrefix/v2")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBodyEncoding(Charset.forName("UTF-8")) << requestBody
    callGenericClient[Boolean]("/settings/channel/v2", "POST", request)
  }

  override def updateSubscriptionChannelRegistry(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(dtoSubscriptionChannelRegistry)

    val request = (url(s"$endpoint$urlPrefix/${dtoSubscriptionChannelRegistry.id}")
                  .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PUT
    callGenericClient[Boolean]("/settings/channel", "PUT", request)
  }

  override def updateSubscriptionChannelRegistryV2(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(dtoSubscriptionChannelRegistry)

    val request = (url(s"$endpoint$urlPrefix/v2/${dtoSubscriptionChannelRegistry.id}")
      .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PUT
    callGenericClient[Boolean]("/settings/channel/v2", "PUT", request)
  }

  override def updateSubscriptionChannelState(id: Long,actionName : String,creator: Creator) : Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(Map("action" -> actionName , "creator" -> creator))

    val request = (url(s"$endpoint$urlPrefix/${id}")
      .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PATCH
    callGenericClient[Boolean]("/settings/channel", "PATCH", request)
  }

  override def updateSubscriptionChannelStateV2(id: Long,actionName : String, creator: Creator) : Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(Map("action" -> actionName, "creator" -> creator))

    val request = (url(s"$endpoint$urlPrefix/v2/${id}")
      .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PATCH
    callGenericClient[Boolean]("/settings/channel/v2", "PATCH", request)
  }

  override def deleteSubscriptionChannelRegistry(id: Long, creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    val requestBody = Serialization.write(Map("creator" -> creator))
    val request = (url(s"$endpoint$urlPrefix/$id").setContentType("application/json", Charset.forName("UTF-8")) << requestBody).DELETE
    callGenericClient[Boolean]("/settings/channel", "DELETE", request)
  }

  override def getSubscriptionChannelRegistries(environmentId: Long , creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {
    val request = (url(s"$endpoint$urlPrefix/environment/$environmentId") <<? Map("accountId" -> creator.accountId.toString)).GET
    callGenericClient[Seq[DtoSubscriptionChannelRegistry]]("/settings/channel/environment", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getSubscriptionChannelRegistries(environmentId: Long, subscriptionTypeId: Long,accountId:Long): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {
    val request = (url(s"$endpoint$urlPrefix/environment/$environmentId/subscription/$subscriptionTypeId")  <<? Map("accountId" -> accountId.toString)).GET
    callGenericClient[Seq[DtoSubscriptionChannelRegistry]]("/settings/channel/environment/subscription", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getSubscriptionChannelRegistriesV2(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]] = {
    val request = (url(s"$endpoint$urlPrefix/v2/environment/$environmentId") <<?
      Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)).GET
    callGenericClient[Seq[DtoSubscriptionChannelRegistry]]("/settings/channel/v2/environment", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def getSubscriptionChannelRegistryWithAccount(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int]): Future[Either[ErrorResponse, SubscriptionChannelRegistryWithAccount]] = {
    val request = url(s"$endpoint$urlPrefix/account/$accountId/environment/type/$environmentTypeId/subscription/$subscriptionTypeId")
    callGenericClient[SubscriptionChannelRegistryWithAccount]("/settings/channel/account/environment/type/subscription", "GET", getRequestAddedWithOptionalFeatureType(request, featureTypeId), accountId = Some(accountId))
  }

  override def getSubscriptionTypes(): Future[Either[ErrorResponse,List[Metadata]]] = {
    val request = url(s"$endpoint$urlPrefix/meta/subscriptiontypes")
    callGenericClient[List[Metadata]]("/settings/channel/meta/subscriptiontypes", "GET", request)
  }

  override def getChannelTypes(): Future[Either[ErrorResponse,List[Metadata]]] = {
    val request = url(s"$endpoint$urlPrefix/meta/types")
    callGenericClient[List[Metadata]]("/settings/channel/meta/types", "GET", request)
  }

  override def getCommunicationModes(): Future[Either[ErrorResponse,List[Metadata]]] = {
    val request = url(s"$endpoint$urlPrefix/meta/communicationmodes")
    callGenericClient[List[Metadata]]("/settings/channel/meta/communicationmodes", "GET", request)
  }

  override def getActions(): Future[Either[ErrorResponse,List[Metadata]]] = {
    val request = url(s"$endpoint$urlPrefix/meta/actions")
    callGenericClient[List[Metadata]]("/settings/channel/meta/actions", "GET", request)
  }

  override def hasActiveChannels(accountId: Long, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/environments/${environmentId}/channels/is_active") <<?
      Map("accountId" -> accountId.toString , "subscriptionTypeId" -> subscriptionTypeId.toString)).GET
    callGenericClient[Boolean]("/settings/channel/environments/channels/is_active", "GET", request, accountId = Some(accountId), additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def hasActiveChannelsV2(creator: Creator, environmentId: Long, subscriptionTypeId: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/v2/environments/${environmentId}/channels/is_active") <<?
      Map("userId" -> creator.userId.toString, "accountId" -> creator.accountId.toString , "subscriptionTypeId" -> subscriptionTypeId.toString)).GET
    callGenericClient[Boolean]("/settings/channel/v2/environments/channels/is_active", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def updateWatchlistWebhookSecretKey(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String, featureType: Option[Int] = None): Future[Either[ErrorResponse, Int]] = {
    val requestBody = Serialization.write(Map("secret_key"->secretKey))
    val request = ((url(endpoint) / "settings" / "channel" / "account" / accountId / "environment" / "type" / environmentTypeId / "subscription" / "type" / subscriptionTypeId)
      .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PUT
    callGenericClient[Int]("/settings/channel/account/environment/type/subscription/type", "PUT", getRequestAddedWithOptionalFeatureType(request, featureType), accountId = Some(accountId), additionalTags = Set(s"environmentTypeId:$environmentTypeId,subscriptionType:$subscriptionTypeId"))
  }

  override def getWebhookSecretKeyRotationDetails(): Future[Either[ErrorResponse, Seq[WebhookSecretKeyRotationDetails]]] = {
    val request = url(s"$endpoint$urlPrefix/secret_key_sources/expired").GET
    callGenericClient[Seq[WebhookSecretKeyRotationDetails]]("/settings/channel/secret_key_sources/expired", "GET", request)
  }

  override def updateWebhookSecretKeyRotationDetails(updateWebhookSecretKeyRotation: Seq[UpdateWebhookSecretKeyRotation]): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint$urlPrefix/secret_key_rotation")
      .setContentType("application/json", Charset.forName("UTF-8")) << updateWebhookSecretKeyRotation.encodeJson()).PUT
    callGenericClient[Boolean]("/settings/channel/secret_key_rotation", "PUT", request)
  }

  private def callGenericClient[A: Manifest](apiName:String ,httpMethod:String,request: Req, accountId: Option[Long] = None,additionalTags: Set[String] = Set.empty[String]):Future[Either[ErrorResponse, A]] ={
    GenericClient.callApi(http, apiName, httpMethod, request,accountId= accountId,config=config,additionalTags=additionalTags)
  }

  private def getSafeEndpoint(endpoint: String): String = {
    if (endpoint.endsWith("/")) endpoint.stripSuffix("/") else endpoint
  }

  private def getRequestAddedWithOptionalFeatureType(request: Req, featureType: Option[Int]): Req = {
    if(featureType.isDefined)
      request <<? Map("featureType" -> featureType.get.toString)
    else
      request
  }

}
