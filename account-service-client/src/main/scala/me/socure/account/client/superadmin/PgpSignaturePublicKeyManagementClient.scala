package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.pgp.{PgpPublic<PERSON>ey, PgpPublicKeyDecoded}
import me.socure.model.superadmin.AccountPgpInfo

import scala.concurrent.Future


trait PgpSignaturePublicKeyManagementClient {
  def getAccountsWithPgpSignaturePublicKeys : Future[Either[ErrorResponse, List[AccountPgpInfo]]]

  def getPgpSignaturePublicKey(accountId: Long) : Future[Either[ErrorResponse, PgpPublicKeyDecoded]]

  def insertPgpSignaturePublicKey(accountId: Long, publicKey: String) : Future[Either[ErrorResponse, Boolean]]

  def deletePgpSignaturePublicKey(accountId: Long) : Future[Either[ErrorResponse, Boolean]]
}
