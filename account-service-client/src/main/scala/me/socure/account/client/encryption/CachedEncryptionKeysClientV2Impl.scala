package me.socure.account.client.encryption

import dispatch.Http
import me.socure.account.client.GenericClient._
import me.socure.account.service.common.EncryptionKeysCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ErrorResponse
import me.socure.model.encryption.EncryptedKeyDetails

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedEncryptionKeysClientV2Impl(http: Http, endpoint: String, cache: Storage[EncryptedKeyDetails], timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)], memcachedTtl: Option[Duration] = None)
                                      (implicit ec: ExecutionContext) extends EncryptionKeysClientV2Impl(http, endpoint) {

  val getAllActiveKeysApiName = "/encryption_keys_v2/active_keys"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getAllActiveKeys(accountId: Long): Future[Either[ErrorResponse, EncryptedKeyDetails]] = {

    val cacheKey = EncryptionKeysCacheKeyProvider.provide(accountId)

    val future = getCache(getAllActiveKeysApiName, cache, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getAllActiveKeys(accountId)
          .flatMap {
            case r@Right(value) => storeCache(getAllActiveKeysApiName, cache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }

}
