package me.socure.account.client.setting

import me.socure.model.{ErrorResponse, ProductSettingAuditLog, ProductSettingAuditResponse, ProductSettingTraces, ProductSettingTracesFilter, ProductSettingTracesResponse}
import me.socure.model.account.WatchlistPreference
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.model.kyc.{KycPreferences, KycPreferencesRequest, KycPreferencesResponse}

import scala.concurrent.Future

trait AccountPreferenceClient {

  def getWatchlist(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, WatchlistPreference]]

  def setWatchlist(preference: WatchlistPreference): Future[Either[ErrorResponse, WatchlistPreference]]

  def getKyc(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, KycPreferences]]

  def saveKyc(environmentId: Long, preferences: KycPreferences): Future[(AuditDetails,Either[ErrorResponse, KycPreferences])]

  def getKycForAccounts(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, KycPreferencesResponse]]

  def saveKycForAccounts(environmentId: Long, preferences: KycPreferencesRequest): Future[(AuditDetails,Either[ErrorResponse, KycPreferencesResponse])]

  def deleteKyc(environmentId: Long): Future[Either[ErrorResponse, Boolean]]

  def saveProductSettingTraces(productSettingTraces: ProductSettingTraces): Future[Either[ErrorResponse, ProductSettingTraces]]

  def getProductSettingTracesById(id: Long): Future[Either[ErrorResponse, ProductSettingTraces]]

  def getProductSettingTracesFilter(productSettingTracesFilter: ProductSettingTracesFilter): Future[Either[ErrorResponse, ProductSettingTracesResponse]]

  def getProductSettingAuditLog(id: Long): Future[Either[ErrorResponse, ProductSettingAuditResponse]]
}
