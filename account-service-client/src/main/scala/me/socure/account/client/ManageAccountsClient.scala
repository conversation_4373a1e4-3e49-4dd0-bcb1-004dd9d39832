package me.socure.account.client

import me.socure.model.ErrorResponse
import me.socure.model.account.ParentAccount
import me.socure.model.superadmin.AccountCreationForm

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/25/17.
  */
trait ManageAccountsClient {
  def create(accountCreationForm: AccountCreationForm) : Future[Either[ErrorResponse, Boolean]]

  def getParentAccounts() : Future[Either[ErrorResponse, Seq[ParentAccount]]]

  def activateAccounts(accountIds: List[String]) : Future[Either[ErrorResponse, Boolean]]

  def updateAccountNameByPublicId(publicId: String, accountName: String): Future[Either[ErrorResponse, Int]]
}
