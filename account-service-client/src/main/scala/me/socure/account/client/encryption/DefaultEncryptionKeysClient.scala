package me.socure.account.client.encryption

import com.amazonaws.regions.Regions
import com.google.common.net.UrlEscapers
import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/18/17.
  */
class DefaultEncryptionKeysClient(endpoint: String)(implicit exe: ExecutionContext) extends EncryptionKeysClient {

  private val urlBase = s"$endpoint/encryption_keys"

  override def regenerate(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    GenericClient.communicate[Map[Regions, EncryptedKey]]("/encryption_keys/regenerate", "POST", url(s"$urlBase/regenerate/${accountId.value}").POST)
  }

  override def getKeys(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    GenericClient.communicate[Map[Regions, EncryptedKey]]("/encryption_keys/get", "GET",  url(s"$urlBase/get/${accountId.value}"))
  }

  override def hasKeys(accountId: AccountId): Future[Either[ErrorResponse, Boolean]] = {
    GenericClient.communicate[Boolean]("/encryption_keys/has", "GET",  url(s"$urlBase/has/${accountId.value}"))
  }

  override def getAccountIdByApiKey(apiKeyString: ApiKeyString): Future[Either[ErrorResponse, AccountId]] = {
    GenericClient.communicate[Option[AccountId]]("/encryption_keys/get_account_id_by_api_key", "GET",  url(s"$urlBase/get_account_id_by_api_key/${UrlEscapers.urlPathSegmentEscaper.escape(apiKeyString.value)}")) map {
      case Right(Some(accountId)) => Right(accountId)
      case Right(None) => Left(ErrorResponseFactory.get(ExceptionCodes.AccountNotFound))
      case Left(e) => Left(e)
    }
  }
}
