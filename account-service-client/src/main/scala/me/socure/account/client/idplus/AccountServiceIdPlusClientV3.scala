package me.socure.account.client.idplus

import me.socure.model.{AccountInformationV3, ErrorResponse}

import scala.concurrent.Future

/**
  * Created by alexa<PERSON><PERSON> on 5/9/16.
  */
trait AccountServiceIdPlusClientV3 {
  def fetchByApiKeyV3(apiKey: String): Future[Either[ErrorResponse, AccountInformationV3]]
  def fetchAccountInfoV3ByPublicApiKey(publicApiKey: String): Future[Either[ErrorResponse, AccountInformationV3]]
  def fetchAccountInfoV3ByAccountId(accountId: Long, envTypeId: Int = 1): Future[Either[ErrorResponse, AccountInformationV3]]

}
