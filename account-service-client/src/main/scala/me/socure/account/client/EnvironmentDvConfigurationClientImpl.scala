package me.socure.account.client

import java.nio.charset.Charset
import dispatch.{Http, url}
import me.socure.model.{DvConfigurationResponse, ErrorResponse}
import me.socure.model.account.{DvConfiguration, DvConfigurationRequest}
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class EnvironmentDvConfigurationClientImpl(http: Http, endpoint : String)(implicit ec : ExecutionContext) extends EnvironmentDvConfigurationClient {

  val logger : Logger = LoggerFactory.getLogger(classOf[ManageAccountsClientImpl])

  override def listDvConfiguration(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
    val request = creator match {
      case None =>  (url(endpoint) / "dv" / "configuration" / environmentId.toString).GET
      case Some(creator) => (url(endpoint) / "dv" / "configuration" / environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .GET
    }
    GenericClient.callApi[Map[String, DvConfiguration]](http, "/dv/configuration", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def saveDvConfiguration(dvConfigurationSeq: Seq[DvConfiguration], environmentId: Long, creator: Option[Creator] = None): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
  val request = creator match {
    case None =>  ((url(endpoint) / "dv" / "configuration" / environmentId.toString).setContentType("application/json", Charset.forName("UTF-8")) << dvConfigurationSeq.encodeJson()).PUT
    case Some(creator) => ((url(endpoint) / "dv" / "configuration" / environmentId.toString)
      .addQueryParameter("creator_user_id", creator.userId.toString)
      .addQueryParameter("creator_account_id", creator.accountId.toString)
      .setContentType("application/json", Charset.forName("UTF-8")) << dvConfigurationSeq.encodeJson()).PUT
  }
    GenericClient.callApiWithAuditDetails[Boolean](http,"/dv/configuration", "PUT", request,None,additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def listDvConfigurationForAccounts(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, DvConfigurationResponse]] = {
    val request = creator match {
      case None => (url(endpoint) / "dv" / "configuration" / "accounts" / environmentId.toString).GET
      case Some(creator) => (url(endpoint) / "dv" / "configuration" / "accounts" / environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .GET
    }
    GenericClient.callApi[DvConfigurationResponse](http, "/dv/configuration/accounts", "GET", request, additionalTags = Set(s"environmentId:$environmentId"))
  }

  override def saveDvConfigurationForAccounts(dvConfigurationRequest: DvConfigurationRequest, environmentId: Long, creator: Option[Creator] = None): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val request = creator match {
      case None => ((url(endpoint) / "dv" / "configuration" / "accounts" / environmentId.toString).setContentType("application/json", Charset.forName("UTF-8")) << dvConfigurationRequest.encodeJson()).PUT
      case Some(creator) => ((url(endpoint) / "dv" / "configuration" / "accounts" / environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .setContentType("application/json", Charset.forName("UTF-8")) << dvConfigurationRequest.encodeJson()).PUT
    }
    GenericClient.callApiWithAuditDetails[Boolean](http,"/dv/configuration/accounts", "PUT", request,None,additionalTags = Set(s"environmentId:$environmentId"))
  }
}
