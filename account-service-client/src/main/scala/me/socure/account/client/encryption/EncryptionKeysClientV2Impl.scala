package me.socure.account.client.encryption

import java.nio.charset.StandardCharsets

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{CustomerKeyDetails, EncryptedKeyDetails, KmsArnDetails}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class EncryptionKeysClientV2Impl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends EncryptionKeysClientV2 {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "encryption_keys_v2"

  override def getCustomerKeys(accountId: Long): Future[Either[ErrorResponse, Seq[KmsArnDetails]]] = {
    val request = url(s"$endpoint/$urlPrefix/customer_keys/$accountId")
    GenericClient.callApi[Seq[KmsArnDetails]](http, "/encryption_keys_v2/customer_keys", "GET", request, accountId = Some(accountId))
  }

  override def getAllActiveKeys(accountId: Long): Future[Either[ErrorResponse, EncryptedKeyDetails]] = {
    val request = url(s"$endpoint/$urlPrefix/active_keys/$accountId")
    GenericClient.callApi[EncryptedKeyDetails](http, "/encryption_keys_v2/active_keys", "GET", request, accountId = Some(accountId))
  }

  override def testCustomerKms(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]] = {
    val jsonBody = Serialization.write(customerKey)
    val request = url(s"$endpoint/$urlPrefix/customer_kms/test")
      .POST
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/encryption_keys_v2/customer_kms/test", "POST", request)
  }

  override def generateCustomerKeys(customerKey: CustomerKeyDetails): Future[Either[ErrorResponse, Boolean]] = {
    val jsonBody = Serialization.write(customerKey)
    val request = url(s"$endpoint/$urlPrefix/customer_key")
      .POST
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

    GenericClient.callApi[Boolean](http, "/encryption_keys_v2/customer_key", "POST", request)

  }
}
