package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountDetails, AccountIdName}

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON> on 6/17/16.
  */
trait LeaderboardClient {

  def getInternalAccountId : Future[Either[ErrorResponse, List[Long]]]

  def getAccountNameById : Future[Either[ErrorResponse, Map[java.lang.Long, AccountIdName]]]

  def getAccountByApiKey(apiKey : String) : Future[Either[ErrorResponse, AccountDetails]]

  def getAccountsByIndustry(sector : String) : Future[Either[ErrorResponse, Set[AccountDetails]]]

}
