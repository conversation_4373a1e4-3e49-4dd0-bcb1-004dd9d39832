package me.socure.account.client.superadmin

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.pgp.{PgpPublicKey, PgpPublicKeyDecoded}
import me.socure.model.superadmin.AccountPgpInfo
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class PgpSignaturePublicKeyManagementClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends PgpSignaturePublicKeyManagementClient {
  val urlPrefix : String = "/pgp"

  val logger : Logger = LoggerFactory.getLogger(getClass)

  override def getAccountsWithPgpSignaturePublicKeys: Future[Either[ErrorResponse, List[AccountPgpInfo]]] = {
    val request = url(s"$endpoint$urlPrefix/get_accounts_with_signature_public_key")
    GenericClient.communicate[List[AccountPgpInfo]]("/pgp/get_accounts_with_signature_public_key", "GET", request)
  }

  override def insertPgpSignaturePublicKey(accountId: Long, signature: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/insert_pgp_signature_public_key") << Map("account_id" -> accountId.toString, "publicKey" -> signature)
    GenericClient.communicate[Boolean]("/pgp/insert_pgp_signature_public_key", "POST", request, accountId = Some(accountId))
  }

  override def deletePgpSignaturePublicKey(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_pgp_signature_public_key") << Map("account_id" -> accountId.toString)
    GenericClient.communicate[Boolean]("/pgp/delete_pgp_signature_public_key", "POST", request, accountId = Some(accountId))

  }

  override def getPgpSignaturePublicKey(accountId: Long): Future[Either[ErrorResponse, PgpPublicKeyDecoded]] = {
    val request = url(s"$endpoint$urlPrefix/get_pgp_signature_public_key") <<? Map("account_id" -> accountId.toString)
    GenericClient.communicate[PgpPublicKey]("/pgp/get_pgp_signature_public_key", "GET", request, accountId = Some(accountId)).map {
      case Right(b) => Right(PgpPublicKeyDecoded.decode(b))
      case Left(a) => Left(a)
    }
  }
}
