package me.socure.account.client.payload

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountPayloadKey
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments.formats

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by ab<PERSON><PERSON><PERSON> on 11/16/2021.
 */
class AccountPayloadKeysClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountPayloadKeysClient {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "payload_keys"

  override def getPayloadKeys(accountId: Option[Long] = None): Future[Either[ErrorResponse, Seq[AccountPayloadKey]]] = {
    var request = url(s"$endpoint/$urlPrefix/get_payload_keys")
    for (f <- accountId) {
      request = request.addQueryParameter("account_id", f.toString)
    }
    GenericClient.callApi[Seq[AccountPayloadKey]](http, "/payload_keys/get_payload_keys", "GET", request)
  }
}
