package me.socure.account.client.businessuserrole

import me.socure.account.service.common.AccInfoCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse
import me.socure.model.account.BusinessUserRolesWithPermissions
import org.slf4j.LoggerFactory
import me.socure.account.client.GenericClient._

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by alexand<PERSON> on 5/30/16.
 */
class BusinessUserRoleClientCached(
                                    underlying: BusinessUserRoleClient,
                                    permissionsStorage: Storage[List[BusinessUserRolesWithPermissions]], memcachedTtl: Option[Duration] = None
                                  )(implicit ec: ExecutionContext) {

  import BusinessUserRoleClientCached._

  val getAccountPermissionsApiName = "/businessuserrole/account_permissions"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  def getAccountPermissions(accountId: Long): Future[Either[ErrorResponse, List[BusinessUserRolesWithPermissions]]] = {
    val key: String = AccInfoCacheKeyProvider.getAccountPermissionsKey(accountId)
    getCache(getAccountPermissionsApiName, permissionsStorage, key) flatMap {
      case None =>
        underlying.getAccountPermissions(accountId).map {
          case Left(e) =>
            logger.error(s"Failed to fetch account permissions from account id= $accountId")
            Left(e)
          case Right(b) =>
            storeCache(getAccountPermissionsApiName, permissionsStorage, key, b, None, additionalTags)
            permissionsStorage.store(key, b)
            Right(b)
        }
      case Some(r) =>
        Future.successful(Right(r))
    }
  }

}

object BusinessUserRoleClientCached {
  private val logger = LoggerFactory.getLogger(getClass)
}
