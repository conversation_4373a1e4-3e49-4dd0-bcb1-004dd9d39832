package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.client.GenericClient._
import me.socure.account.service.common.AccountHierarchyCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ErrorResponse

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedAccountHierarchyClientImpl(http: Http, endpoint: String, parentIdStorage: Storage[Long],
                                       timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)], memcachedTtl: Option[Duration] = None)(implicit ec: ExecutionContext) extends AccountHierarchyClientImpl(http, endpoint) {

  val getRootParent = "/hierarchy/get_root_parent"
  val additionalTags = if (memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getRootParent(accountId: Long): Future[Either[ErrorResponse, Long]] = {

    val cacheKey = AccountHierarchyCacheKeyProvider provideForGetRootParent accountId

    val future = getCache(getRootParent, parentIdStorage, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getRootParent(accountId)
          .flatMap {
            case r@Right(value) => storeCache(getRootParent, parentIdStorage, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }

}
