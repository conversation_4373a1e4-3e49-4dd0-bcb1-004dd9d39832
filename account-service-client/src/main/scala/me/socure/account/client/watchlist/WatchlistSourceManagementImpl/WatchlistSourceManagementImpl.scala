package me.socure.account.client.watchlist

import java.io.InputStream
import java.nio.charset.StandardCharsets
import me.socure.util.JsonEnrichments.formats
import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.{ErrorResponse, WatchlistSource}
import org.apache.commons.io.IOUtils
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by sumitkumar on 20/05/20
 **/

class WatchlistSourceManagementImpl(http: Http,endpoint: String)(implicit ec : ExecutionContext) extends WatchlistSourceManagementClient {

  val urlPrefix = "/settings/preferences/ca/watchlist/sources"
  val logger : Logger = LoggerFactory.getLogger(classOf[WatchlistSourceManagementImpl])

  override def importWatchlistSources(watchlistSources: InputStream): Future[Either[ErrorResponse, List[WatchlistSource]]] = {
    val request = (url(s"$endpoint$urlPrefix/import").POST.setBody(IOUtils.toByteArray(watchlistSources)))
      .setContentType("application/json",StandardCharsets.UTF_8)
    GenericClient.callApi[List[WatchlistSource]](http, "/settings/preferences/ca/watchlist/sources/import", "POST", request)
  }
}
