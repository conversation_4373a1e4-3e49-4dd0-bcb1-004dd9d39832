package me.socure.account.client

import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.model.ErrorResponse

import scala.concurrent.Future


trait ManageAccountsV2Client {
  def updateIsActive(publicId: String, activate: String): Future[Either[ErrorResponse, Int]]
  def updateIsDeleted(publicId: String, delete: String): Future[Either[ErrorResponse, Int]]
  def listAccounts(asr: AccountSearchRequest): Future[Either[ErrorResponse, Vector[AccountSearchResponse]]]
  def updateEmail(userId: Long, email: String): Future[Either[ErrorResponse, String]]
  def updateConsentId (accountId: Long, consentId: Int): Future[Either[ErrorResponse, Int]]
  def getConsentReasonId(accountId: Long): Future[Either[ErrorResponse, Int]]
  def resetPassword(email: String): Future[Either[ErrorResponse, Boolean]]
  def onboardEmail(email: String): Future[Either[ErrorResponse, <PERSON>olean]]
}
