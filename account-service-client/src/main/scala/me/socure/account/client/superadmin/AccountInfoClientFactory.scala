package me.socure.account.client.superadmin

import com.typesafe.config.Config
import dispatch.Http
import me.socure.account.client.account.{AccountInfoClientV2, AccountInfoClientV3}
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.user.authorization.AccountWithEnvironmentDetailsWithPublicId
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

/**
 * Created by jamesanto on 5/8/17.
 */
object AccountInfoClientFactory {
  def createCached(
                    accountServiceEndpoint: String,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration],
                    memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None,
                    config:Option[Config]=None
                  )(implicit exe: ExecutionContext): CachedAccountInfoClient = {
    createCached(
      underlying = createDefault(accountServiceEndpoint,config),
      memcachedClient = memcachedClient,
      ttl = ttl,
      memcachedTimeout
    )
  }

  def createDefault(accountServiceEndpoint: String,config:Option[Config]=None)(implicit exe: ExecutionContext): AccountInfoClientImpl = {
    new AccountInfoClientImpl(
      endpoint = accountServiceEndpoint,
      config = config
    )
  }

  private def createCached(
                    underlying: AccountInfoClient,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration],
                    memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)]
                  )(implicit exe: ExecutionContext): CachedAccountInfoClient = {

    implicit val scalaCache = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl = ttl
    implicit val codecHasRole = JavaSerializationCodec.codec[Boolean]
    implicit val codecAccountWithEnvironmentDetailsWithPublicId = JavaSerializationCodec.codec[AccountWithEnvironmentDetailsWithPublicId]

    new CachedAccountInfoClient(
      underlying = underlying,
      hasRoleStorage = new ScalaCacheStorage[Boolean, Array[Byte]](),
      accountWithEnvironmentDetailsWithPublicIdStorage = new ScalaCacheStorage[AccountWithEnvironmentDetailsWithPublicId, Array[Byte]](),
      memcachedTimeout,
      memcachedTtl= ttl
    )
  }

  def getV2Client(config: Config)(implicit ec: ExecutionContext): AccountInfoClientV2 = {
    val hmacConfig: Config = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient: Http = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountInfoClientV2(httpClient, endpoint, Some(config))
  }

  def createV2Client(realm: String, version: String, endpoint: String, encrypter: HMACEncrypter,config: Option[Config]=None): AccountInfoClientV2 = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient: Http = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountInfoClientV2(httpClient, endpoint,config)
  }

  def getV3Client(config: Config)(implicit ec: ExecutionContext): AccountInfoClientV3 = {
    val hmacConfig: Config = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient: Http = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountInfoClientV3(httpClient, endpoint,Some(config))
  }

  def getV2ClientAws(config: Config): AccountInfoClientV2 = {
    val endpoint = config.getString("endpoint")
    val http = HttpWithHmacFactory.createInsecure(config = config.getConfig("hmac"))
    new AccountInfoClientV2(http, endpoint,Some(config))
  }

}
