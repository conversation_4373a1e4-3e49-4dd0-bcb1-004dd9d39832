package me.socure.account.client.blacklist
import java.net.URLEncoder

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.{BlackListAccountInformation, ErrorResponse, Industry}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 6/4/16.
  */
class BlackListClientImpl(endpoint: String)(implicit ec: ExecutionContext) extends BlackListClient {

  override def fetchIndustryByAccount(accountId: Long): Future[Either[ErrorResponse, Industry]] = {
    val request = url(endpoint + "/blacklist" + "/fetch_industry/" + accountId)
    GenericClient.communicate[Industry]("/blacklist/fetch_industry", "GET", request, accountId = Some(accountId))

  }

  override def fetchAccountInformation(apiKey: String): Future[Either[ErrorResponse, BlackListAccountInformation]] = {
    val request = url(endpoint + "/blacklist" + "/fetch_account_information/" + URLEncoder.encode(apiKey, "utf-8"))
    GenericClient.communicate[BlackListAccountInformation]("/blacklist/fetch_account_information", "GET" ,request)
  }
}
