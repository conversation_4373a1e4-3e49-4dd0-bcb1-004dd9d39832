package me.socure.account.client.dashboard

import me.socure.dto.DtoSubscriptionType
import me.socure.model.ErrorResponse
import me.socure.model.account.{SecretKeyWithSubscription, SubscriptionsProvision}

import scala.concurrent.Future

trait SubscriptionsClient {
  def listSubscriptions(accountId: Long): Future[Either[ErrorResponse, Seq[Long]]]

  def update(accountId: Long, subscriptionTypeId: Long, operation: String): Future[Either[ErrorResponse, Boolean]]

  def listSubscriptionTypes(): Future[Either[ErrorResponse, Seq[DtoSubscriptionType]]]

  def listSubscriptionTypesWithProvisionDetails(accountId: Long): Future[Either[ErrorResponse, Seq[SubscriptionsProvision]]]

  def updateSubscriptionStatus(environmentId: Long, subscriptionTypes: Set[Long], action: String): Future[Either[ErrorResponse, String]]

  def updateWatchlistWebhookSecretKey(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String): Future[Either[ErrorResponse, Int]]
}

