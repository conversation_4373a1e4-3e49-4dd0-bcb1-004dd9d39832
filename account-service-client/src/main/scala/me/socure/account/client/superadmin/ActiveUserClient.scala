package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON> on 5/30/16.
  */
trait ActiveUserClient {

  def markAsInternal(ids : List[String]) : Future[Either[ErrorResponse, Int]]

  def unmarkAsInternal(ids : List[String]) : Future[Either[ErrorResponse, Int]]

  def deactivateUser(ids : List[String]) : Future[Either[ErrorResponse, Int]]

  def markAsInternal(accountId : Long) : Future[Either[ErrorResponse, Int]]

  def unmarkAsInternal(accountId : Long) : Future[Either[ErrorResponse, Int]]

  def deactivateAccount(accountId : Long) : Future[Either[ErrorResponse, Int]]

  def getDomainList(accountId : Long) : Future[Either[ErrorResponse, String]]

  def addDomainToAccount(accountId : Long, domain : String) : Future[Either[ErrorResponse, Boolean]]

  def deleteAccount(accountId : Long) : Future[Either[ErrorResponse, Boolean]]

}
