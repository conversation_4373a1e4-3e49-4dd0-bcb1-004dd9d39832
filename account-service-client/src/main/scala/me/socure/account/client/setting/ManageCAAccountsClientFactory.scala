package me.socure.account.client.setting

import com.typesafe.config.Config
import dispatch.Http
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.WatchlistSource
import me.socure.model.account.CAWatchlistPreference
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

object ManageCAAccountsClientFactory {

  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): ManageCAAccountsClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new ManageCAAccountsClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec : ExecutionContext): ManageCAAccountsClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new ManageCAAccountsClientImpl(httpClient, endpoint)
  }

  def createCached(httpClient: Http, endpoint: String, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): ManageCAAccountsClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl
    implicit val codecForWatchListPreferenceCache: Codec[CAWatchlistPreference, Array[Byte]] = JavaSerializationCodec.codec[CAWatchlistPreference]
    implicit val codecForWatchListSourceCache: Codec[Seq[WatchlistSource], Array[Byte]] = JavaSerializationCodec.codec[Seq[WatchlistSource]]

    new CachedManageCAAccountsClientImpl(httpClient, endpoint, new ScalaCacheStorage[CAWatchlistPreference, Array[Byte]](),
      new ScalaCacheStorage[Seq[WatchlistSource], Array[Byte]](), memcachedTimeout, memcachedTtl)
  }

  def createCachedUsingConfig(config: Config, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                              memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): ManageCAAccountsClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    createCached(httpClient, endpoint, memcachedClient, memcachedTtl, memcachedTimeout)
  }

}
