package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.{UserAccountAssociation, UserAccountAssociationInput, UserAssociationInput, UserAssociationResponse}
import me.socure.model.user.authorization.UserAuth

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait UserAccountAssociationClient {
  def getUserAccountAssociationsByUserId(userId: Long): Future[Either[ErrorResponse, Seq[UserAccountAssociation]]]
  def insertUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]]
  def updateUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]]
  def deleteUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]]
  def validateUserAccountAssociation(userId: Long, accountId: Long): Future[Either[ErrorResponse, UserAuth]]
  def insertUserAccountAssociations(accountId: Long, userAssociationInputSeq: Seq[UserAssociationInput], updatedBy: Long): Future[Either[ErrorResponse, Seq[UserAssociationResponse]]]
}
