package me.socure.account.client.superadmin

import java.net.URLEncoder
import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.account.client.http.NonSecuredHttp
import me.socure.model.account.{AccountDetails, AccountIdName}
import me.socure.model.{BlackListAccountInformation, ErrorResponse, Response}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 6/17/16.
  */
class LeaderboardClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends LeaderboardClient {
  val logger : Logger = LoggerFactory.getLogger(classOf[LeaderboardClientImpl])

  val urlPrefix : String = "/leaderboard"

  override def getInternalAccountId: Future[Either[ErrorResponse, List[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/get_internal_accounts")
    GenericClient.communicate[List[Long]]("/leaderboard/get_internal_accounts", "GET", request)
  }

  override def getAccountNameById : Future[Either[ErrorResponse, Map[java.lang.Long, AccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_name")
    GenericClient.communicate[Map[java.lang.Long, AccountIdName]]("/leaderboard/get_account_name", "GET", request)
  }

  override def getAccountByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountDetails]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_by_apikey/$apiKey")
    GenericClient.communicate[AccountDetails]("/leaderboard/get_account_by_apikey", "GET", request)

  }

  override def getAccountsByIndustry(sector: String): Future[Either[ErrorResponse, Set[AccountDetails]]] = {
    val request = url(s"$endpoint/industries/get_accounts_by_industry/$sector")
    GenericClient.communicate[Set[AccountDetails]]("/leaderboard/get_accounts_by_industry", "GET", request)
  }
}
