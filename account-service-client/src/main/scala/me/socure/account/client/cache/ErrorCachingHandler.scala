package me.socure.account.client.cache

import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse

import scala.concurrent.{ExecutionContext, Future}

trait ErrorCachingHandler {
  def cacheIfRequired[T](cacheKey: String)(response: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]]
}

class DefaultErrorCachingHandler(
                                  cacheErrors: Storage[ErrorResponse],
                                  canCacheThisError: ErrorResponse => Boolean
                                )(implicit val ec: ExecutionContext) extends ErrorCachingHandler {
  def cacheIfRequired[T](cacheKey: String)(response: => Future[Either[ErrorResponse, T]]): Future[Either[ErrorResponse, T]] = {
    cacheErrors.get(cacheKey).flatMap {
      case Some(res) => Future.successful(Left(res))
      case None => response.flatMap {
        case r@Right(_) => Future.successful(r)
        case Left(errorResponse) if canCacheThisError(errorResponse) => cacheErrors.store(cacheKey, errorResponse).map(_ => Left(errorResponse))
        case l@Left(_) => Future.successful(l)
      }
    }
  }
}

object ErrorCachingHandler {
  def apply(
             cacheErrors: Storage[ErrorResponse],
             canCacheThisError: ErrorResponse => Boolean
           )(implicit ec: ExecutionContext): ErrorCachingHandler = {
    new DefaultErrorCachingHandler(
      cacheErrors = cacheErrors,
      canCacheThisError = canCacheThisError
    )
  }

  def apply(
             cacheErrors: Storage[ErrorResponse],
             errorCodesToCache: Set[Int]
           )(implicit ec: ExecutionContext): ErrorCachingHandler = {
    apply(
      cacheErrors = cacheErrors,
      canCacheThisError = errorResponse => errorCodesToCache.contains(errorResponse.code)
    )
  }
}
