package me.socure.account.client

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object ManageAccountsV2ClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): ManageAccountsV2Client = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new ManageAccountsV2ClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): ManageAccountsV2Client = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new ManageAccountsV2ClientImpl(httpClient, endpoint)
  }
}
