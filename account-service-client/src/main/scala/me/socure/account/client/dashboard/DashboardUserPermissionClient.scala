package me.socure.account.client.dashboard

import me.socure.constants.DashboardUserPermissions.DashboardUserPermission
import me.socure.model.ErrorResponse
import me.socure.model.account.PermissionConversionResponse

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait DashboardUserPermissionClient {
  def getAllPermissions: Future[Either[ErrorResponse, Seq[DashboardUserPermission]]]
  def getCurrentAndNewPermissions: Future[Either[ErrorResponse, PermissionConversionResponse]]
}
