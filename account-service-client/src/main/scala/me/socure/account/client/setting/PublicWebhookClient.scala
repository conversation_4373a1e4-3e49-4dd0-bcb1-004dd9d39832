package me.socure.account.client.setting

import me.socure.model.ErrorResponse
import me.socure.model.account.{PublicWebhook, PublicWebhookWithPublicAccountId, PublicWebhooksWithPublicAccountId}

import scala.concurrent.Future

trait PublicWebhookClient {
  def listPublicWebhook: Future[Either[ErrorResponse, Vector[PublicWebhook]]]

  def getPublicWebhook(environmentId: Long): Future[Either[ErrorResponse, PublicWebhook]]

  def insertPublicWebhook(environmentId: Long, endpoint: String, certificate: String, subscriptionType: String): Future[Either[ErrorResponse, Boolean]]

  def updatePublicWebhook(environmentId: Long, endpoint: String, certificate: String, subscriptionType: String): Future[Either[ErrorResponse, Boolean]]

  def deletePublicWebhook(environmentId: Long, subscriptionType: Long): Future[Either[ErrorResponse, Boolean]]

  def getPublicWebhooks(envId: Long): Future[Either[ErrorResponse, PublicWebhooksWithPublicAccountId]]

  def getPublicWebhook(accountId: Long, environmentTypeId: Long, subscriptionType: Long): Future[Either[ErrorResponse, PublicWebhookWithPublicAccountId]]

}