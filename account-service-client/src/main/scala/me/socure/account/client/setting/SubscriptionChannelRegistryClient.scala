package me.socure.account.client.setting

import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, Metadata, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails}

import scala.concurrent.Future

trait SubscriptionChannelRegistryClient {
  def getSubscriptionChannelRegistry(id: Long , accountId:Long): Future[Either[ErrorResponse, DtoSubscriptionChannelRegistry]]
  def createSubscriptionChannelRegistry(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]
  def createSubscriptionChannelRegistryV2(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]
  def updateSubscriptionChannelRegistry(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]
  def updateSubscriptionChannelRegistryV2(dtoSubscriptionChannelRegistry: DtoSubscriptionChannelRegistry): Future[Either[ErrorResponse, Boolean]]
  def updateSubscriptionChannelState(id:Long,actionName: String,creator: Creator) : Future[Either[ErrorResponse, Boolean]]
  def updateSubscriptionChannelStateV2(id:Long,actionName: String, creator: Creator) : Future[Either[ErrorResponse, Boolean]]
  def deleteSubscriptionChannelRegistry(id: Long,creator: Creator): Future[Either[ErrorResponse, Boolean]]
  def getSubscriptionChannelRegistries(environmentId: Long,creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]
  def getSubscriptionChannelRegistries(environmentId: Long, subscriptionTypeId: Long,accountId:Long): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]
  def getSubscriptionChannelRegistriesV2(environmentId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[DtoSubscriptionChannelRegistry]]]
  def getSubscriptionChannelRegistryWithAccount(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int] = None): Future[Either[ErrorResponse, SubscriptionChannelRegistryWithAccount]]
  def getSubscriptionTypes(): Future[Either[ErrorResponse,List[Metadata]]]
  def getChannelTypes(): Future[Either[ErrorResponse,List[Metadata]]]
  def getCommunicationModes(): Future[Either[ErrorResponse,List[Metadata]]]
  def getActions(): Future[Either[ErrorResponse,List[Metadata]]]
  def hasActiveChannels(accountId:Long , environmentId: Long , subscriptionTypeId:Int) : Future[Either[ErrorResponse, Boolean]]
  def hasActiveChannelsV2(creator: Creator , environmentId: Long , subscriptionTypeId:Int) : Future[Either[ErrorResponse, Boolean]]
  def updateWatchlistWebhookSecretKey(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String, featureType: Option[Int]= None): Future[Either[ErrorResponse, Int]]
  def getWebhookSecretKeyRotationDetails(): Future[Either[ErrorResponse, Seq[WebhookSecretKeyRotationDetails]]]
  def updateWebhookSecretKeyRotationDetails(updateWebhookSecretKeyRotation: Seq[UpdateWebhookSecretKeyRotation]): Future[Either[ErrorResponse, Boolean]]
}
