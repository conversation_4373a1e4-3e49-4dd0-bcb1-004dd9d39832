package me.socure.account.client.setting

import com.typesafe.config.Config
import dispatch.Http
import me.socure.common.hmac.filter.HttpWithHmacFactory
import me.socure.common.storage.scalacache.ScalaCacheStorageErrorSwallowing
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.subscription.SubscriptionChannelRegistryWithAccount
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

object SubscriptionChannelRegistryClientFactory {

  def create(endpoint: String, http: Http, config: Option[Config] = None): SubscriptionChannelRegistryClient = {
    new SubscriptionChannelRegistryClientImpl(endpoint, http, config)
  }

  def create(config: Config): SubscriptionChannelRegistryClient = {
    val endpoint = config.getString("endpoint")

    val http = getHttp(config)
    create(endpoint = endpoint, http)
  }

  private def getHttp(config: Config): Http = {
    val hmacConfig = config.getConfig("hmac")
    HttpWithHmacFactory.createInsecure(hmacConfig)
  }

  def createCached(config: Config, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                              memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): SubscriptionChannelRegistryClient = {

    val endpoint = config.getString("endpoint")
    val httpClient = getHttp(config)

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl
    implicit val codec: Codec[SubscriptionChannelRegistryWithAccount, Array[Byte]] = JavaSerializationCodec.codec[SubscriptionChannelRegistryWithAccount]

    new CachedSubscriptionChannelRegistryClientImpl(httpClient, endpoint, new ScalaCacheStorageErrorSwallowing[SubscriptionChannelRegistryWithAccount, Array[Byte]](), memcachedTimeout, memcachedTtl,Some(config))
  }

}
