package me.socure.account.client.setting

import me.socure.dashboard.{AccountSettings, NewAccountSettings}
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountApiKeys, BusinessUserRolesLess, EnvironmentWithAccount}
import me.socure.model.dashboardv2.{Creator, EnvironmentNameAndId}
import org.joda.time.DateTime

import scala.concurrent.Future

/**
  * Created by sun<PERSON><PERSON> on 5/18/16.
  */

trait AccountSettingClient {

  def updateDomain(accountId : Long, domains : List[String]) : Future[Either[ErrorResponse, Boolean]]

  def getAccountSettings(accountId : Long) : Future[Either[ErrorResponse, AccountSettings]]

  def getNewAccountSettings(accountId : Long) : Future[Either[ErrorResponse, NewAccountSettings]]

  def deleteAccountCache(accountId : Long) : Future[Either[ErrorResponse, Boolean]]

  def upsertAccountCache(accountId : Long, cacheDate : DateTime) : Future[Either[ErrorResponse, Bo<PERSON>an]]

  def deleteSocialNetworkKey(keyId : Long) : Future[Either[ErrorResponse, Boolean]]

  def upsertSocialNetworkKey(accountId : Long, provider : String, appkey: String, appsecret: String, environment: Long) : Future[Either[ErrorResponse, Boolean]]

  def upsertIndividualCache(accountId : Long, identifier: String, cacheDate : DateTime): Future[Either[ErrorResponse, Boolean]]

  def deleteIndividualCache(accountId : Long, identifier: String) : Future[Either[ErrorResponse, Boolean]]

  def deleteSocialNetworkKey(accountId : Long, provider: String): Future[Either[ErrorResponse, Boolean]]

  def getAllEnvironmentWithAccount : Future[Either[ErrorResponse, List[EnvironmentWithAccount]]]

  def getEnvironmentsByEmail(email : String) : Future[Either[ErrorResponse, List[EnvironmentNameAndId]]]

  def getApiKeysForAccountAndSubAccounts(accountId: Long): Future[Either[ErrorResponse, List[AccountApiKeys]]]

  def getModulesByAccountId(accountId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[BusinessUserRolesLess]]]
}