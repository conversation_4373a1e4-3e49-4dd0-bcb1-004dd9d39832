package me.socure.account.client.idplus

import dispatch._
import me.socure.account.client.GenericClient
import me.socure.model.{AccountInformationV3, ErrorResponse}

import java.net.URLEncoder
import com.typesafe.config.Config
import me.socure.account.client.ClientConstant._

import java.nio.charset.StandardCharsets
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexa<PERSON><PERSON> on 5/6/16.
  */
class AccountServiceIdPlusClientImplV3(endpoint: String,config:Option[Config]=None)(implicit ec: ExecutionContext) extends AccountServiceIdPlusClientV3 {

  override def fetchByApiKeyV3(apiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val request = url(endpoint + "/idplus" +  "/fetch_account_information_v3/" + URLEncoder.encode(apiKey, "utf-8"))
    GenericClient.communicate[AccountInformationV3]("/idplus/fetch_account_information_v3", "GET", request,attributeKeyValue=Some(ApiKeyAttribute,apiKey),config=config)
  }

  override def fetchAccountInfoV3ByPublicApiKey(publicApiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val request = url(endpoint + "/idplus" +  "/account-information" + "/public-apikey/" + URLEncoder.encode(publicApiKey, "utf-8"))
    GenericClient.communicate[AccountInformationV3]("/idplus/account-information/public-apikey/", "GET", request,attributeKeyValue=Some(PublicApiKeyAttribute,publicApiKey),config=config)
  }

  override def fetchAccountInfoV3ByAccountId(accountId: Long, envTypeId: Int=1): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val request = url(endpoint + "/idplus" +  "/account-information" + "/account-id/" + URLEncoder.encode(accountId.toString, "utf-8"))
    GenericClient.communicate[AccountInformationV3]("/idplus/account-information/account-id/", "GET", request,attributeKeyValue=Some(AccountIdAttribute,accountId.toString),config=config)
  }
}
