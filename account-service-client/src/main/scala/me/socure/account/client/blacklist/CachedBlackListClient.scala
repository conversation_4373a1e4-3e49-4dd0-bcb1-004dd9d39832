package me.socure.account.client.blacklist

import me.socure.account.client.idplus.CacheService
import me.socure.model.{BlackListAccountInformation, ErrorResponse, Industry}

import scala.concurrent.Future

/**
  * Created by alexand<PERSON> on 6/4/16.
  */
class CachedBlackListClient(client: BlackListClientImpl, cacheService: CacheService[String, BlackListAccountInformation]) extends BlackListClient {

  override def fetchIndustryByAccount(accountId: Long): Future[Either[ErrorResponse, Industry]] = {
    client.fetchIndustryByAccount(accountId)
  }

  override def fetchAccountInformation(apiKey: String): Future[Either[ErrorResponse, BlackListAccountInformation]] = {
    client.fetchAccountInformation(apiKey)
  }
}
