package me.socure.account.client

import dispatch._
import me.socure.common.microservice.client.MicroServiceResponseParser._
import me.socure.constants.JsonFormats
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccessCredentials, AccountPermissions}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexand<PERSON> on 8/9/16.
  */
class BasicAccountClient(http: Http, endpoint: String)(implicit ec: ExecutionContext) {

  implicit val formats = JsonFormats.formats

  val servletEndpoint = endpoint + "/basic"

  def getProductionAccessCredentials(id: Long): Future[Either[ErrorResponse, AccessCredentials]] = {
    val request = url(servletEndpoint + "/production_credentials/" + id)
    GenericClient.callApi[AccessCredentials](http, "/production_credentials", "GET", request)
  }

  def accountPermissions(id: Long): Future[Either[ErrorResponse, AccountPermissions]] = {
    Future.successful(Right(AccountPermissions(true, true, true)))
  }
}
