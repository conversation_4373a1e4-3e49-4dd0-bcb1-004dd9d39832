package me.socure.account.client.setting

import me.socure.dashboard.{AccountSettings, NewAccountSettings}
import me.socure.model.{AccountInformation, AccountInformationV3, AccountStatus, Essential, Setting}
import me.socure.model.account.{AccessCredentials, ApiKeyStatus, Environment}

object AccountConvertors {

  //hotfix - settings/:accountID
  def toAccountSettings(newAccountSettings: NewAccountSettings): AccountSettings = {
    val environments = newAccountSettings.environments.map(a => {
      val accessCredentials =  new AccessCredentials(
        apiKey =
          a.accessCredentials.apiKeys.
            find(_.status.equals(ApiKeyStatus.ACTIVE)).
            map(_.apiKey).getOrElse(throw new Exception("Failed to retrieve API Key for account: " + newAccountSettings.accountId)),
        secretKey = a.accessCredentials.secretKey,
        accessToken = a.accessCredentials.accessToken,
        accessTokenSecret = a.accessCredentials.accessTokenSecret,
        certificate = a.accessCredentials.certificate
      )
      new Environment(id = a.id,
        name = a.name,
        domain = a.domain,
        accessCredentials = accessCredentials,
        socialAccounts = a.socialAccounts,
        invidiualCache = a.invidiualCache,
        overallCache = a.overallCache,
        publicApiKeys = Seq.empty
      )
    })
    new AccountSettings(
      newAccountSettings.accountId,
      environments)
  }

  //to be used only for ACTIVE accounts
  def toAccountInformation(accountInformationV3: AccountInformationV3): AccountInformation = {
    val essential:Essential = accountInformationV3.essential
    val isActive: Boolean = if(AccountStatus.ACTIVE.equals(essential.status)) true else false
    val setting:Setting = accountInformationV3.setting match {
      case Some(s) =>
        Setting(watchlistPreference = s.watchlistPreference,
          watchlistPreference_3_0 = s.watchlistPreference_3_0,
          watchlistPreferences_3_0 = s.watchlistPreferences_3_0,
          roles = s.roles,
          primaryFraudModel = s.primaryFraudModel,
          fraudModels = s.fraudModels,
          kycPreferences = s.kycPreferences,
          subscriptionTypeIds = s.subscriptionTypeIds,
          includedWatchlistSources = s.includedWatchlistSources,
          dvConfiguration = s.dvConfiguration,
          consentReason = s.consentReason,
          mlaField = s.mlaField,
          ein = s.ein,
          webhookNotificationPreferences = s.webhookNotificationPreferences,
          saiPreferences = s.saiPreferences)
      case None =>
        Setting(watchlistPreference = ???, watchlistPreference_3_0 = ???, watchlistPreferences_3_0 = ???, roles = ???, primaryFraudModel = ???, fraudModels = ???, kycPreferences = ???, subscriptionTypeIds = ???, includedWatchlistSources = ???, dvConfiguration = ???, consentReason = ???, mlaField = ???, ein = ???, webhookNotificationPreferences = ???)
    }
    AccountInformation(active = isActive, publicId = essential.publicId, accountId = essential.accountId,
      accountName = essential.accountName, environment = essential.environment,
      watchlistPreference = setting.watchlistPreference,
      watchlistPreference_3_0 = setting.watchlistPreference_3_0,
      watchlistPreferences_3_0 = setting.watchlistPreferences_3_0,
      industry = essential.industry,
      isInternal = essential.isInternal,
      roles = setting.roles,
      primaryFraudModel = setting.primaryFraudModel,
      fraudModels = setting.fraudModels,
      kycPreferences = setting.kycPreferences,
      subscriptionTypeIds = setting.subscriptionTypeIds,
      includedWatchlistSources = setting.includedWatchlistSources,
      dvConfiguration = setting.dvConfiguration,
      consentReason = setting.consentReason,
      externalId = essential.externalId,
      mlaField = setting.mlaField,
      ein = setting.ein,
      webhookNotificationPreferences = setting.webhookNotificationPreferences,
      rootParentIdOpt = Some(essential.rootParentId),
      hierarchyPath = essential.hierarchyPath,
      rootParentAccountType = essential.rootParentAccountType,
      saiPreferences = setting.saiPreferences
    )
  }
}
