package me.socure.account.client.superadmin

import java.lang
import me.socure.account.client.GenericClient._
import me.socure.account.client.superadmin.CachedAccountInfoClient._
import me.socure.account.service.common.{AccInfoCache<PERSON>eyProvider, Cache<PERSON>eyProvider}
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ErrorResponse
import me.socure.model.account._
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization.{AccountWithEnvironmentDetails, AccountWithEnvironmentDetailsWithPublicId}
import org.joda.time.DateTime

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesan<PERSON> on 5/8/17.
  */
class CachedAccountInfoClient(
                               underlying: AccountInfoClient,
                               hasRoleStorage: Storage[Boolean],
                               accountWithEnvironmentDetailsWithPublicIdStorage: Storage[AccountWithEnvironmentDetailsWithPublicId],
                               timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)],
                               memcachedTtl: Option[Duration] = None
                             )(implicit exe: ExecutionContext) extends AccountInfoClient {

  val hasRoleApiName = "/account/info/has_role"
  val accountDetailsByIdApiName = "/account/info/get_account_details_by_id_v2"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getAccountPublicId(accountId: Long): Future[Either[ErrorResponse, String]] = underlying.getAccountPublicId(accountId)


  override def getAllAccounts(): Future[Either[ErrorResponse, Seq[PublicAccount]]] = underlying.getAllAccounts()

  override def getAllActiveAccountNames: Future[Either[ErrorResponse, Map[lang.Long, AccountIdName]]] = {
    underlying.getAllActiveAccountNames
  }

  override def getAllNonInternalActiveAccounts: Future[Either[ErrorResponse, List[Long]]] = {
    underlying.getAllNonInternalActiveAccounts
  }

  override def hasRole(apiKeyString: ApiKeyString, role: Int): Future[Either[ErrorResponse, Boolean]] = {
    val cacheKey = key(apiKeyString, role)
    getCache(hasRoleApiName, hasRoleStorage, cacheKey, timeoutOpt) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => underlying.hasRole(apiKeyString = apiKeyString, role = role).flatMap {
        case r@Right(result) => storeCache(hasRoleApiName, hasRoleStorage, cacheKey, result, timeoutOpt, additionalTags) map(_ => r)
        case r => Future.successful(r)
      }
    }
  }

  def getParentAccountsWithoutPrimaryUsers(): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    underlying.getParentAccountsWithoutPrimaryUsers()
  }

  def getAccountDetailsByPublicId(publicId: String): Future[Either[ErrorResponse, AccountWithEnvironmentDetails]] = {
    underlying.getAccountDetailsByPublicId(publicId = publicId)
  }

  override def getAccountIdNamesByRoles(accountIdNamesRequest: AccountIdNamesByRolesRequest): Future[Either[ErrorResponse, Set[AccountIdName]]] = {
    underlying.getAccountIdNamesByRoles(
      accountIdNamesRequest = accountIdNamesRequest
    )
  }

  override def getAccountDetailsById(id: Long): Future[Either[ErrorResponse, AccountWithEnvironmentDetailsWithPublicId]] = {
    val cacheKey = AccInfoCacheKeyProvider keyForGetAccountDetailsById id
    getCache(accountDetailsByIdApiName, accountWithEnvironmentDetailsWithPublicIdStorage, cacheKey, timeoutOpt) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => underlying getAccountDetailsById id flatMap {
        case r@Right(result) => storeCache(accountDetailsByIdApiName, accountWithEnvironmentDetailsWithPublicIdStorage, cacheKey, result, timeoutOpt, additionalTags) map(_ => r)
        case r => Future.successful(r)
      }
    }
  }

  override def getAccountNameById(accountId: Long): Future[Either[ErrorResponse, String]] = {
    underlying.getAccountNameById(accountId = accountId)
  }

  override def getAccountNameAndPublicId (accountId: Long): Future[Either[ErrorResponse, PublicAccount]] = {
    underlying.getAccountNameAndPublicId(accountId = accountId)
  }

  override def getEncryptionEnabledParentAccounts: Future[Either[ErrorResponse, Seq[PublicAccount]]] = {
    underlying.getEncryptionEnabledParentAccounts
  }

  override def getAccountPreferences (accountId: Long): Future[Either[ErrorResponse, AccountPreferences]] = {
    underlying.getAccountPreferences(accountId)
  }

  override def getAllAccountNamesWithPublicId: Future[Either[ErrorResponse, Map[String, PublicAccountIdName]]] = underlying.getAllAccountNamesWithPublicId

  override def getAccountAnalyticsInfo(accountId: Long): Future[Either[ErrorResponse, Option[AccountAnalyticsInfoResponse]]] = underlying.getAccountAnalyticsInfo(accountId)

  override def addAccountAnalyticsInfo(accountAnalyticsInfoReq: AccountAnalyticsInfoRequest): Future[Either[ErrorResponse, Boolean]] = underlying.addAccountAnalyticsInfo(accountAnalyticsInfoReq)

  override def updateAccountAnalyticsLastImportedDate(accountId: Long, lastImportedDate: DateTime): Future[Either[ErrorResponse, Boolean]] = underlying.updateAccountAnalyticsLastImportedDate(accountId, lastImportedDate)

  override def getAccountIdsWithPermission(permissionId: Int): Future[Either[ErrorResponse, Set[Long]]] = underlying.getAccountIdsWithPermission(permissionId)

  def getAccountIdNamesWithAndWithoutPermission(withPermissionId:Int,withoutPermissionId:Int):Future[Either[ErrorResponse, List[AccountIdName]]] = underlying.getAccountIdNamesWithAndWithoutPermission(withPermissionId, withoutPermissionId)

  override def getAnalyticsGlobalInfo(): Future[Either[ErrorResponse, Option[AnalyticsGlobalInfoResponse]]] = underlying.getAnalyticsGlobalInfo()

  override def updateAnalyticsGlobalInfo(analyticsGlobalInfoReq: AnalyticsGlobalInfoRequest): Future[Either[ErrorResponse, Boolean]] = underlying.updateAnalyticsGlobalInfo(analyticsGlobalInfoReq)

  override def getAllAccountsIds(): Future[Either[ErrorResponse, Seq[Long]]] = underlying.getAllAccountsIds()
}

object CachedAccountInfoClient {
  def key(apiKeyString: ApiKeyString, role: Int): String = s"acc_ser_acc_inf_cl_has_role_${apiKeyString.value}_$role"
}
