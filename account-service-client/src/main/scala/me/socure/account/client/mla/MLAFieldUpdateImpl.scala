package me.socure.account.client.mla

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.mla.{MLAInputRequest, MLAResponse}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class MLAFieldUpdateImpl(http: Http,endpoint: String)(implicit ec : ExecutionContext) extends MLAFieldUpdateClient {

  val logger : Logger = LoggerFactory.getLogger(classOf[MLAFieldUpdateImpl])

  override def saveMLAFields(mlaRequest: MLAInputRequest): Future[Either[ErrorResponse, MLAResponse]] = {
    val request =  (url(endpoint) / "dashboard" / "mla" / "field")
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << mlaRequest.encodeJson()
    GenericClient.callApi[MLAResponse](http, "/dashboard/mla/field", "POST", request)
  }
}

