package me.socure.account.client.dashboard

import me.socure.account.client.GenericClient.{getCache, storeCache}
import me.socure.account.service.common.{AccountUIConfigurationCacheKeyProvider, IdmAccountInfoCacheKeyProvider}
import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse
import me.socure.model.account.{ApiKeyInfo, IdmAccountInfoResponse, IdmApiKey}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedIdmAccountInformationClient(
                                         underlying: IdmAccountInformationClient,
                                         cacheConfigurations: Storage[IdmAccountInfoResponse],
                                         memcachedTtl: Option[Duration] = None
                                       )(implicit ec: ExecutionContext) extends IdmAccountInformationClient {

  val IdmAccountInfoApiName = "/idm/account_information/v1"
  val AdditionalTags: Set[String] = if (memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def fetchIdmAccountInformation(apiKey: String): Future[Either[ErrorResponse, IdmAccountInfoResponse]] = {
    val cacheKey = IdmAccountInfoCacheKeyProvider.provide(apiKey)
    getCache(IdmAccountInfoApiName, cacheConfigurations, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => underlying.fetchIdmAccountInformation(apiKey).flatMap {
        case r@Right(result) => storeCache(IdmAccountInfoApiName, cacheConfigurations, cacheKey, result, None, AdditionalTags) map (_ => r)
        case r => Future.successful(r)
      }
    }
  }
}
