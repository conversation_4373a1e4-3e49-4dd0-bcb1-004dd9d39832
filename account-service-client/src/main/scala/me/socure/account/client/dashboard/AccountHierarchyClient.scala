package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountHierarchy, AccountInfoV2WithIndustry}

import scala.concurrent.Future

trait AccountHierarchyClient {
  def getAccountHierarchy(id: Long): Future[Either[ErrorResponse, AccountHierarchy]]
  def getAccountHierarchyByAccountId(accountId: Long): Future[Either[ErrorResponse, AccountHierarchy]]
  def insertOrUpdateAccountHierarchy(accountHierarchy: AccountHierarchy): Future[Either[ErrorResponse, Int]]
  def updatePrimaryAdminCount(accountId: Long, count: Int): Future[Either[ErrorResponse, Boolean]]
  def activate(accountId: Long): Future[Either[ErrorResponse, Boolean]]
  def deactivate(accountId: Long): Future[Either[ErrorResponse, Boolean]]
  def allowAdminister(accountId: Long): Future[Either[ErrorResponse, Boolean]]
  def denyAdminister(accountId: Long): Future[Either[ErrorResponse, Boolean]]
  def list(accountId: Long, userId: Long): Future[Either[ErrorResponse, Seq[AccountInfoV2WithIndustry]]]
  def validateAccountAccess(accountId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, Boolean]]
  def validateAccountAccess(accountId: Long, creatorAccountId: Long, accountPermission: Set[Int]): Future[Either[ErrorResponse, Boolean]]
  def getRootParent(accountId: Long ): Future[Either[ErrorResponse, Long]]
  def getSubAccounts(accountId: Long ): Future[Either[ErrorResponse, Set[Long]]]
  def getRootParentAccountType(accountId: Long): Future[Either[ErrorResponse, Int]]
}
