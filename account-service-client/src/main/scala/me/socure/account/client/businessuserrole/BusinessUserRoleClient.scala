package me.socure.account.client.businessuserrole

import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.constants.attributes.AccountAttributeName.AccountAttributeName
import me.socure.constants.attributes.AccountAttributeValue.AccountAttributeValue
import me.socure.model.ErrorResponse
import me.socure.model.account.{BusinessUserRolesLess, BusinessUserRolesWithPermissions}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by alexandre on 5/30/16.
  */
class BusinessUserRoleClient(endpoint: String,config:Option[Config]=None)(implicit ec: ExecutionContext) {
  val urlPrefix = "/businessuserrole"

  private def buildRequest(function: String, userIds: Seq[Long], role: Int) = {
    url(endpoint + urlPrefix + "/" + function).POST
      .addParameter("user_ids", userIds.headOption.map(_.toString).getOrElse(throw new Exception("Failed to construct request : Empty list of user ids")))
      .addParameter("role_id", role.toString)
  }

  private def buildRequest(function: String, accountId: Long, role: Int, issuedBy: String) = {
    url(endpoint + urlPrefix + "/" + function).POST
      .addParameter("account_id", accountId.toString)
      .addParameter("role_id", role.toString)
      .addParameter("issued_by", issuedBy)
  }

  def addRole(userIds: Seq[Long], role: Int): Future[Either[ErrorResponse, Int]] = {
    val request = buildRequest("add_role_to_users", userIds, role)
    callCommunicate[Int]("/businessuserrole/add_role_to_users", "POST", request)
  }

  def addRole(accountId: Long, role: Int, issuedBy: String): Future[Either[ErrorResponse, Int]] = {
    val request = buildRequest("add_role_to_account", accountId, role, issuedBy)
    callCommunicate[Int]("/businessuserrole/add_role_to_account", "POST", request)
  }

  def removeRole(userIds: Seq[Long], role: Int): Future[Either[ErrorResponse, Int]] = {
    val request = buildRequest("remove_role_from_users", userIds, role)
    callCommunicate[Int]("/businessuserrole/remove_role_from_users", "POST", request)
  }

  def addAttributes(userIds: Long, name : AccountAttributeName, value : AccountAttributeValue): Future[Either[ErrorResponse, Int]] = {
    val request = url(endpoint + urlPrefix +"/add_attribute").POST.addParameter("id", userIds.toString).addParameter("att_name", name.id.toString).addParameter("att_value", value.id.toString)
    callCommunicate[Int]("/businessuserrole/add_attribute", "POST", request)
  }

  def removeAttributes(userIds: Long, name : AccountAttributeName): Future[Either[ErrorResponse, Int]] = {
    val request = url(endpoint + urlPrefix + "/remove_attribute").POST.addParameter("id", userIds.toString).addParameter("att_name", name.id.toString)
    callCommunicate[Int]("/businessuserrole/remove_attribute", "POST", request)
  }

  def listRoles: Future[Either[ErrorResponse, List[BusinessUserRolesLess]]] = {
    val request = url(s"$endpoint$urlPrefix/list_roles")
    callCommunicate[List[BusinessUserRolesLess]]("/businessuserrole/list_roles", "GET", request)
  }

  def getAccountPermissions(accountId: Long): Future[Either[ErrorResponse, List[BusinessUserRolesWithPermissions]]] = {
    val request = url(s"$endpoint$urlPrefix/account_permissions/${accountId.toString}")
    callCommunicate[List[BusinessUserRolesWithPermissions]]("/businessuserrole/account_permissions", "GET", request, accountId = Some(accountId))
  }

  def isPermissionAvailable(accountId: Long, permission : Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/is_permission_available").addQueryParameter("account_id", accountId.toString).addQueryParameter("permission", permission.toString)
    callCommunicate[Boolean]("/businessuserrole/is_permission_available", "GET", request, accountId = Some(accountId))
  }

  def getParentAccountPermissions(accountId: Long): Future[Either[ErrorResponse, Set[Int]]] = {
    val request = url(s"$endpoint$urlPrefix/fetch_parent_account_permission/${accountId.toString}")
    callCommunicate[Set[Int]]("/businessuserrole/fetch_parent_account_permission", "GET", request, accountId = Some(accountId))
  }
  private def callCommunicate[A: Manifest](apiName:String ,httpMethod:String,request: Req,accountId: Option[Long] = None) : Future[Either[ErrorResponse, A]] = {
    GenericClient.communicate(apiName, httpMethod, request, accountId=accountId,config=config)
  }
}
