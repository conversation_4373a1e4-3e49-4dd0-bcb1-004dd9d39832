package me.socure.account.client.setting

import com.typesafe.config.Config
import dispatch.Http
import me.socure.account.client.GenericClient.{getCache, storeCache}
import me.socure.account.service.common.SubscriptionChannelRegistryCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ErrorResponse
import me.socure.model.subscription.SubscriptionChannelRegistryWithAccount

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedSubscriptionChannelRegistryClientImpl(http: Http, endpoint: String, cache: Storage[SubscriptionChannelRegistryWithAccount], timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)], memcachedTtl: Option[Duration] = None,config:Option[Config]=None)
                                                 (implicit ec: ExecutionContext) extends SubscriptionChannelRegistryClientImpl(endpoint, http,config) {

  val getSubscriptionChannelRegistryWithAccountApiName = "/settings/channel/account/environment/type/subscription"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getSubscriptionChannelRegistryWithAccount(accountId: Long, environmentTypeId: Long, subscriptionTypeId: Long, featureTypeId: Option[Int] = None): Future[Either[ErrorResponse, SubscriptionChannelRegistryWithAccount]] = {

    val cacheKey = SubscriptionChannelRegistryCacheKeyProvider provide(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)

    val future = getCache(getSubscriptionChannelRegistryWithAccountApiName, cache, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)
          .flatMap {
            case r@Right(value) => storeCache(getSubscriptionChannelRegistryWithAccountApiName, cache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }

}
