package me.socure.account.client.superadmin

import java.nio.charset.Charset

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.account.AccountDomain
import me.socure.model.ErrorResponse
import me.socure.util.JsonEnrichments._
import net.spy.memcached.compat.log.LoggerFactory

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sun<PERSON><PERSON> on 5/30/16.
  */
class ActiveUserClientImpl(baseUrl : String)(implicit executionContext: ExecutionContext) extends ActiveUserClient{
  val logger = LoggerFactory.getLogger(classOf[ActiveUserClientImpl])

  val urlPrefix : String = "/superadmin"

  override def markAsInternal(emails: List[String]): Future[Either[ErrorResponse, Int]] = {
    val params = Map("emails" -> emails.mkString(","))
    val request = url(baseUrl + s"$urlPrefix/mark_internal") << params
    GenericClient.communicate[Int]("/superadmin/mark_internal", "POST", request)
  }


  override def unmarkAsInternal(emails : List[String]): Future[Either[ErrorResponse, Int]] = {
    val params = Map("emails" -> emails.mkString(","))
    val request = url(baseUrl + s"$urlPrefix/unmark_internal") << params
    GenericClient.communicate[Int]("/superadmin/unmark_internal", "POST", request)
  }

  override def deactivateUser(emails : List[String]): Future[Either[ErrorResponse, Int]] = {
    val params = Map("emails" -> emails.mkString(","))
    val request = url(baseUrl + s"$urlPrefix/deactivate") << params
    GenericClient.communicate[Int]("/superadmin/deactivate", "POST", request)
  }

  override def markAsInternal(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val params = Map("account_id" -> accountId.toString)
    val request = url(baseUrl + s"$urlPrefix/mark/internal") << params
    GenericClient.communicate[Int]("/superadmin/mark/internal", "POST", request, accountId = Some(accountId))
  }


  override def unmarkAsInternal(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val params = Map("account_id" -> accountId.toString)
    val request = url(baseUrl + s"$urlPrefix/unmark/internal") << params
    GenericClient.communicate[Int]("/superadmin/unmark/internal", "POST", request, accountId = Some(accountId))
  }

  override def deactivateAccount(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val params = Map("account_id" -> accountId.toString)
    val request = url(baseUrl + s"$urlPrefix/deactivate_account") << params
    GenericClient.communicate[Int]("/superadmin/deactivate_account", "POST", request, accountId = Some(accountId))
  }

  override def getDomainList(accountId: Long): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$baseUrl$urlPrefix/get_domains/$accountId")
    GenericClient.communicate[String]("/superadmin/get_domains", "GET", request, accountId = Some(accountId))
  }

  override def addDomainToAccount(accountId: Long, domain: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$baseUrl$urlPrefix/add_domains").setContentType("application/json", Charset.forName("UTF-8")) << AccountDomain(accountId, domain.split(",", -1).toList).encodeJson()
    GenericClient.communicate[Boolean]("/superadmin/add_domains", "POST", request, accountId = Some(accountId))
  }

  override def deleteAccount(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$baseUrl$urlPrefix/delete_account") << Map("account_id" -> accountId.toString)
    GenericClient.communicate[Boolean]("/superadmin/delete_account", "POST", request, accountId = Some(accountId))
  }
}
