package me.socure.account.client.sftp

import dispatch.{Http, url}
import me.socure.account.client.GenericClient

import me.socure.model.ErrorResponse
import me.socure.model.account.AccountSftpUser
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import java.nio.charset.StandardCharsets

import scala.concurrent.{ExecutionContext, Future}

class AccountSftpUserClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountSftpUserClient {
  val logger: Logger = LoggerFactory.getLogger(getClass)

  override def listAccountSftpUsers(): Future[Either[ErrorResponse, Seq[AccountSftpUser]]] = {
    val request = (url(endpoint) / "sftp" / "user" ).GET
    GenericClient.callApi[Seq[AccountSftpUser]](http, "/sftp/user", "GET", request)
  }

  override def saveAccountSftpUser(accountId: Long, sftpUser: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / "sftp" / "user" ) <<? Map("account_id" -> accountId.toString, "sftp_user" -> sftpUser))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/sftp/user", "POST", request)
  }
}



