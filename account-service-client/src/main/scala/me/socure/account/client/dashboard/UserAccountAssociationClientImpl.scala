package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{UserAccountAssociation, UserAccountAssociationInput, UserAssociationInput, UserAssociationResponse}
import me.socure.model.user.authorization.UserAuth
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserAccountAssociationClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends UserAccountAssociationClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "user_account_associations"

  override def getUserAccountAssociationsByUserId(userId: Long): Future[Either[ErrorResponse, Seq[UserAccountAssociation]]] = {
    val request = (url(endpoint) / urlPrefix).addQueryParameter("user_id", userId.toString)
    GenericClient.callApi[Seq[UserAccountAssociation]](http, "/ui/configuration", "GET", request)
  }

  override def insertUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    val request = ((url(endpoint) / urlPrefix).setContentType("application/json", Charset.forName("UTF-8")) << userAccountAssociationInput.encodeJson()).POST
    GenericClient.callApi[Int](http, "/ui/configuration", "POST", request)
  }

  override def updateUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    val request = ((url(endpoint) / urlPrefix).setContentType("application/json", Charset.forName("UTF-8")) << userAccountAssociationInput.encodeJson()).PUT
    GenericClient.callApi[Int](http, "/ui/configuration", "PUT", request)
  }

  override def deleteUserAccountAssociation(userAccountAssociationInput: UserAccountAssociationInput): Future[Either[ErrorResponse, Int]] = {
    val request = ((url(endpoint) / urlPrefix).setContentType("application/json", Charset.forName("UTF-8")) << userAccountAssociationInput.encodeJson()).POST
    GenericClient.callApi[Int](http, "/ui/configuration", "POST", request)
  }

  override def validateUserAccountAssociation(userId: Long, accountId: Long): Future[Either[ErrorResponse, UserAuth]] = {
    val request = (((url(endpoint) / urlPrefix) / "validate").setContentType("application/json", Charset.forName("UTF-8")) <<? Map("account_id" -> accountId.toString, "user_id" -> userId.toString)).POST
    GenericClient.callApi[UserAuth](http, "/ui/configuration/validate", "POST", request)
  }

  override def insertUserAccountAssociations(accountId: Long, userAssociationInputSeq: Seq[UserAssociationInput], updatedBy: Long): Future[Either[ErrorResponse, Seq[UserAssociationResponse]]] = {
    val request = (((url(endpoint) / urlPrefix) / "bulk").setContentType("application/json", Charset.forName("UTF-8")).setBody(userAssociationInputSeq.encodeJson()) <<? Map("account_id" -> accountId.toString, "updated_by" -> updatedBy.toString)).POST
    GenericClient.callApi[Seq[UserAssociationResponse]](http, "/ui/configuration/bulk", "POST", request)
  }
}
