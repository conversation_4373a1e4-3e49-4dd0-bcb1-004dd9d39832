package me.socure.account.client.industry

import java.nio.charset.Charset

import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.{ErrorResponse, Industry}

import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._
/**
  * Created by sun<PERSON><PERSON> on 8/31/16.
  */
class IndustryManagementClientImpl(endpoint : String,config:Option[Config]=None)(implicit val ec : ExecutionContext) extends IndustryManagementClient {

  val urlPrefix : String = "/industries"

  override def getIndustryList: Future[Either[ErrorResponse, List[Industry]]] = {
    val request = url(s"$endpoint$urlPrefix/get_list")
    callCommunicate[List[Industry]]("/industries/get_list", "GET", request)
  }

  override def upsertIndustry(industry: Industry): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/upsert_industry").setContentType("application/json", Charset.forName("UTF-8")) << industry.encodeJson()
    callCommunicate[Boolean]("/industries/upsert_industry", "GET", request)
  }

  override def delete(sector: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_industry") << Map("sector" -> sector)
    callCommunicate[Boolean]("/industries/delete_industry", "GET", request)
  }

  override def getIndustryBySector(sector: String): Future[Either[ErrorResponse, Industry]] = {
    val request = url(s"$endpoint$urlPrefix/get_industry/$sector")
    callCommunicate[Industry]("/industries/get_industry", "GET", request)
  }


  private def callCommunicate[A: Manifest](apiName:String ,httpMethod:String,request: Req,accountId: Option[Long] = None,attributeKeyValue:Option[(String,String)]=None) : Future[Either[ErrorResponse, A]]= {
    GenericClient.communicate(apiName, httpMethod, request, accountId=accountId,config=config , attributeKeyValue=attributeKeyValue)
  }
}
