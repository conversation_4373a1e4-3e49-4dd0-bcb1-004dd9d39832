package me.socure.account.client.dashboard

import me.socure.model.dashboardv2.AuditDetails
import me.socure.model.{ErrorResponse, ModulesResponse}

import scala.concurrent.Future

trait ModuleManagementClient {
  def getModules(accountId: Long, creatorAccountId:  Long): Future[scala.Either[ErrorResponse, Set[String]]]

  def getDefaultModules(accountId: Long, creatorAccountId:  Long, creatorUserId: Long): Future[scala.Either[ErrorResponse, Set[Int]]]

  def getDefaultModulesForAccounts(accountId: Long, creatorAccountId:  Long, creatorUserId: Long): Future[scala.Either[ErrorResponse, ModulesResponse]]

  def saveDefaultModules(accountId: Long, modules: Set[Int], creatorAccountId:  Long, creatorUserId: Long): Future[(AuditDetails,scala.Either[ErrorResponse, Boolean])]

  def saveDefaultModulesForAccounts(accountIds: Seq[Long], modules: Set[Int], isForceInherit: Boolean = false, creatorAccountId:  Long, creatorUserId: Long): Future[(AuditDetails,scala.Either[ErrorResponse, Boolean])]

  def clearDefaultModules(accountId: Long, creatorAccountId:  Long, creatorUserId: Long): Future[scala.Either[ErrorResponse, Boolean]]
}
