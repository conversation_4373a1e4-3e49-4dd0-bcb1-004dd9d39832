package me.socure.account.client.dashboard

import com.typesafe.config.Config
import dispatch.Http
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ratelimiter.RateLimitingConfig
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec
import me.socure.account.client.http.NonSecuredHttp


import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

object RateLimitingClientFactory {

  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): RateLimitingClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new RateLimitingClientImpl(httpClient, endpoint)
  }

  def createCached(httpClient: Http, endpoint: String, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): RateLimitingClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl
    implicit val codec: Codec[Seq[RateLimitingConfig], Array[Byte]] = JavaSerializationCodec.codec[Seq[RateLimitingConfig]]

    new CachedRateLimitingClientImpl(httpClient, endpoint, new ScalaCacheStorage[Seq[RateLimitingConfig], Array[Byte]](), memcachedTimeout, memcachedTtl)
  }

  def createCachedUsingConfig(config: Config, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): RateLimitingClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    createCached(httpClient, endpoint, memcachedClient, memcachedTtl, memcachedTimeout)
  }

  def create(config: Config)(implicit exe: ExecutionContext): RateLimitingClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new RateLimitingClientImpl(httpClient, endpoint)
  }

  def create(endpoint: String)(implicit exe: ExecutionContext): RateLimitingClient = {
    val httpClient = NonSecuredHttp.client
    new RateLimitingClientImpl(httpClient, endpoint)
  }
}
