package me.socure.account.client.setting

import dispatch._
import me.socure.account.client.GenericClient
import me.socure.common.microservice.client.MicroServiceClientSupport
import me.socure.model.{ErrorResponse, ProductSettingAuditLog, ProductSettingAuditResponse, ProductSettingTraces, ProductSettingTracesFilter, ProductSettingTracesResponse}
import me.socure.model.account.WatchlistPreference
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.model.kyc.{KycPreferences, KycPreferencesRequest, KycPreferencesResponse}
import me.socure.util.JsonEnrichments._

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}

class AccountPreferenceClientImpl(http: Http, baseEndpoint: String)(implicit val ec: ExecutionContext) extends AccountPreferenceClient with MicroServiceClientSupport {

  private val endpoint = baseEndpoint + "/settings/preferences"
  private implicit val httpClient: Http = http

  override def getWatchlist(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, WatchlistPreference]] = {
    val request = creator match {
      case None => url(endpoint + "/watchlist").addQueryParameter("environment_id", environmentId.toString).GET
      case Some(creator) => url(endpoint + "/watchlist")
        .addQueryParameter("environment_id", environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[WatchlistPreference](http, "/settings/preferences/watchlist", "GET", request)
  }

  override def setWatchlist(preference: WatchlistPreference): Future[Either[ErrorResponse, WatchlistPreference]] = {
    val request = url(endpoint + "/watchlist").setContentType("application/json", Charset.forName("UTF-8")).POST << preference.encodeJson()
    GenericClient.callApi[WatchlistPreference](http, "/settings/preferences/watchlist", "POST", request)
  }

  override def getKyc(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, KycPreferences]] = {
    val request = creator match {
      case None => url(endpoint + s"/kyc/$environmentId").GET
      case Some(creator) => url(endpoint + s"/kyc/$environmentId")
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[KycPreferences](http, "/settings/preferences/kyc", "POST", request)
  }

  override def saveKyc(environmentId: Long, preferences: KycPreferences): Future[(AuditDetails, Either[ErrorResponse, KycPreferences])] = {
    val request = url(endpoint + s"/kyc/$environmentId")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(preferences.encodeJson())
    GenericClient.callApiWithAuditDetails[KycPreferences](http, "/settings/preferences/kyc", "POST", request)
  }

  override def getKycForAccounts(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, KycPreferencesResponse]] = {
    val request = creator match {
      case None => url(endpoint + s"/kyc/accounts/$environmentId").GET
      case Some(creator) => url(endpoint + s"/kyc/accounts/$environmentId")
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .GET
    }
    GenericClient.callApi[KycPreferencesResponse](http, "/settings/preferences/kyc/accounts", "POST", request)
  }

  override def saveKycForAccounts(environmentId: Long, preferences: KycPreferencesRequest): Future[(AuditDetails, Either[ErrorResponse, KycPreferencesResponse])] = {
    val request = url(endpoint + s"/kyc/accounts/$environmentId")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(preferences.encodeJson())
    GenericClient.callApiWithAuditDetails[KycPreferencesResponse](http,"/settings/preferences/kyc/accounts", "POST", request)
  }

  override def deleteKyc(environmentId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(endpoint + s"/kyc/$environmentId").DELETE
    GenericClient.callApi[Boolean](http, "/settings/preferences/kyc", "DELETE", request)
  }

  override def saveProductSettingTraces(productSettingTraces: ProductSettingTraces): Future[Either[ErrorResponse, ProductSettingTraces]] = {
    val request = url(endpoint + s"/product_setting/traces")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(productSettingTraces.encodeJson())
    GenericClient.callApi[ProductSettingTraces](http, "/settings/product_setting/traces", "POST", request)
  }

  override def getProductSettingTracesById(id: Long): Future[Either[ErrorResponse, ProductSettingTraces]] = {
    val request = url(endpoint + s"/product_setting/traces/$id")
      .GET
      .setContentType("application/json", Charset.forName("UTF-8"))
    GenericClient.callApi[ProductSettingTraces](http, "/settings/preferences/product_setting/traces", "GET", request)
  }

  override def getProductSettingTracesFilter(productSettingTracesFilter: ProductSettingTracesFilter): Future[Either[ErrorResponse, ProductSettingTracesResponse]] = {
    val request =  url(endpoint + s"/product_setting/traces")
      .GET
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(productSettingTracesFilter.encodeJson())
    GenericClient.callApi[ProductSettingTracesResponse](http, "/settings/preferences/product_setting/traces", "GET", request)
  }

  override def getProductSettingAuditLog(id: Long): Future[Either[ErrorResponse, ProductSettingAuditResponse]] = {
    val request = url(endpoint + s"/product_setting/audits/$id")
      .GET
      .setContentType("application/json", Charset.forName("UTF-8"))
    GenericClient.callApi[ProductSettingAuditResponse](http, "/settings/preferences/product_setting/audits", "GET", request)
  }

}
