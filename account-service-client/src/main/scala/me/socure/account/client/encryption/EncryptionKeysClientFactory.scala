package me.socure.account.client.encryption

import com.amazonaws.regions.Regions
import me.socure.account.client.cache.ErrorCachingHandler
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, EncryptedKey}
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

/**
  * Created by jamesanto on 4/26/17.
  */
object EncryptionKeysClientFactory {
  def createDefault(accountServiceEndpoint: String)(implicit exe: ExecutionContext): DefaultEncryptionKeysClient = {
    new DefaultEncryptionKeysClient(
      endpoint = accountServiceEndpoint
    )
  }

  def createCached(
                    underlying: EncryptionKeysClient,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): CachedEncryptionKeysClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = ttl
    implicit val codecKeys: Codec[Map[Regions, EncryptedKey], Array[Byte]] = JavaSerializationCodec.codec[Map[Regions, EncryptedKey]]
    implicit val codecAccountId: Codec[AccountId, Array[Byte]] = JavaSerializationCodec.codec[AccountId]
    implicit val codecHasKeys: Codec[Boolean, Array[Byte]] = JavaSerializationCodec.codec[Boolean]

    new CachedEncryptionKeysClient(
      underlying = underlying,
      cacheKeys = new ScalaCacheStorage[Map[Regions, EncryptedKey], Array[Byte]](),
      cacheEnvId = new ScalaCacheStorage[AccountId, Array[Byte]](),
      cacheHasKeys = new ScalaCacheStorage[Boolean, Array[Byte]](),
      memcachedTtl = ttl
    )
  }

  def createCached(
                    accountServiceEndpoint: String,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): CachedEncryptionKeysClient = {
    createCached(
      underlying = createDefault(accountServiceEndpoint),
      memcachedClient = memcachedClient,
      ttl = ttl
    )
  }

  def createCachedWithErrors(
                              underlying: EncryptionKeysClient,
                              memcachedClient: MemcachedClient,
                              ttl: Option[Duration],
                              errorCodesToCache: Set[Int]
                            )(implicit exe: ExecutionContext): CachedEncryptionKeysClientWithErrors = {
    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = ttl
    implicit val codecErrorResponse: Codec[ErrorResponse, Array[Byte]] = JavaSerializationCodec.codec[ErrorResponse]
    new CachedEncryptionKeysClientWithErrors(
      underlying = underlying,
      errorCachingHandler = ErrorCachingHandler(
        cacheErrors = new ScalaCacheStorage[ErrorResponse, Array[Byte]](),
        errorCodesToCache = errorCodesToCache
      )
    )
  }

  def createCachedWithErrors(
                              accountServiceEndpoint: String,
                              memcachedClient: MemcachedClient,
                              ttl: Option[Duration],
                              errorCodesToCache: Set[Int]
                            )(implicit exe: ExecutionContext): CachedEncryptionKeysClientWithErrors = {
    createCachedWithErrors(
      underlying = createCached(
        accountServiceEndpoint = accountServiceEndpoint,
        memcachedClient = memcachedClient,
        ttl = ttl
      ),
      memcachedClient = memcachedClient,
      ttl = ttl,
      errorCodesToCache = errorCodesToCache
    )
  }
}
