package me.socure.account.client.watchlist

import java.util.concurrent.TimeoutException

import me.socure.common.retry.decider.Decider

import scala.util.{Success, Try}

object TimeoutRetryDecider extends Decider[Any] {

  override def shouldRetrySuccess(success: Any): <PERSON>olean = false

  override def shouldRetryFailure(failure: Throwable): Boolean = {
    Try(failure.isInstanceOf[TimeoutException]) match {
      case Success(timeoutException) if timeoutException => true
      case _ => false
    }
  }

}
