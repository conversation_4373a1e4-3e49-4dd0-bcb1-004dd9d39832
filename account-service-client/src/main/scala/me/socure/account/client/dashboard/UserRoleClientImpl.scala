package me.socure.account.client.dashboard

import java.nio.charset.Charset
import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.{ErrorResponse, UsersAndRoles}
import me.socure.model.account.{DashboardUserPermissionResult, RolePermissionTemplateAssociation, UserRole, UserRoleInput, UserRoleResult}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class UserRoleClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends UserRoleClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "roles"

  override def getUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, UserRole]] = {
    val request = url(endpoint) / urlPrefix / id <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[UserRole](http, "/roles", "GET", request, accountId = Some(accountId))
  }

  override def getUserRolesByAccountId(accountId: Long, creatorUserId: Long, creatorAccountId: Long, filterSystemDefinedRoles: Boolean = false): Future[Either[ErrorResponse, Seq[UserRoleResult]]] = {
    val request = (url(endpoint) / urlPrefix) <<? Map("account_id" -> accountId.toString, "creator_user_id" -> creatorUserId.toString, "creator_account_id" -> creatorAccountId.toString, "filter_system_defined_roles" -> filterSystemDefinedRoles.toString)
    GenericClient.callApi[Seq[UserRoleResult]](http, "/roles", "GET", request, accountId = Some(accountId))
  }

  override def getRolesByPublicId(publicAccountId: String): Future[Either[ErrorResponse, Seq[UserRoleResult]]] = {
    val request = (url(endpoint) / urlPrefix/"roles_by_public_account_id") <<? Map("publicAccountId" -> publicAccountId)
    GenericClient.callApi[Seq[UserRoleResult]](http, "/roles/roles_by_public_account_id", "GET", request)
  }

  override def insertUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix).setContentType("application/json", Charset.forName("UTF-8")) << userRoleInput.encodeJson()).POST
    GenericClient.callApi[Boolean](http, "/roles", "POST", request)
  }

  override def updateUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix).setContentType("application/json", Charset.forName("UTF-8")) << userRoleInput.encodeJson()).PUT
    GenericClient.callApi[Boolean](http, "/roles", "PUT", request)
  }

  override def deleteUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = ((url(endpoint) / urlPrefix) / id <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)).DELETE
    GenericClient.callApi[Int](http, "/roles", "DELETE", request, accountId = Some(accountId))
  }

  override def getRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, RolePermissionTemplateAssociation]] = {
    val request = url(endpoint) / urlPrefix / "template" <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[RolePermissionTemplateAssociation](http, "/roles/template", "GET", request, accountId = Some(accountId))
  }

  override def insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation: RolePermissionTemplateAssociation): Future[Either[ErrorResponse, Int]] = {
    val request = ((url(endpoint) / urlPrefix / "template").setContentType("application/json", Charset.forName("UTF-8")) << rolePermissionTemplateAssociation.encodeJson()).PUT
    GenericClient.callApi[Int](http, "/roles/template", "PUT", request)
  }

  override def deleteRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = (url(endpoint) / urlPrefix / userRoleId <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)).DELETE
    GenericClient.callApi[Int](http, "/roles", "DELETE", request, accountId = Some(accountId))
  }

  override def getDashboardPermissionsByRoleId(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]] = {
    val request = url(endpoint) / urlPrefix / userRoleId / "dashboard_permissions" <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[List[DashboardUserPermissionResult]](http, "/roles/dashboard_permissions", "GET", request, accountId = Some(accountId))
  }

  override def getDashboardPermissionsByRoleTypeID(roleType: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]] = {
    val request = url(endpoint) / urlPrefix / "role_type" / roleType / "dashboard_permissions" <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[List[DashboardUserPermissionResult]](http, "/roles/role_type/dashboard_permissions", "GET", request, accountId = Some(accountId))
  }

  override def deleteUserRoleWithPermissionTemplate(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / userRoleId / "delete_with_permissions" <<? Map("user_id" -> userId.toString, "account_id" -> accountId.toString)).DELETE
    GenericClient.callApi[Boolean](http, "/roles/delete_with_permissions", "DELETE", request, accountId = Some(accountId))
  }

  override def getUsersAndRolesRecordCountForAccounts(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]]): Future[Either[ErrorResponse, Int]] = {
    val request = (url(endpoint) / urlPrefix / "get_users_and_roles_for_accounts" / "total_count" <<? Map("account_ids" -> accountIds.mkString(",")) ++ excludeUserStatuses.map("exclude_user_statuses" -> _.mkString(","))).GET
    GenericClient.callApi[Int](http, "/roles/get_users_and_roles_for_accounts/total_count", "GET", request)
  }

  override def getUsersAndRolesByAccountIds(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]], start: Option[Int], size: Option[Int]): Future[Either[ErrorResponse, Seq[UsersAndRoles]]] = {
    val params = Map("account_ids" -> accountIds.mkString(",")) ++ start.map("start" -> _.toString) ++ size.map("size" -> _.toString) ++ excludeUserStatuses.map("exclude_user_statuses" -> _.mkString(","))
    val request = (url(endpoint) / urlPrefix  / "get_users_and_roles_for_accounts" <<? params).GET
    GenericClient.callApi[Seq[UsersAndRoles]](http,"/roles/get_users_and_roles_for_accounts", "GET", request)
  }

}
