package me.socure.account.client.idplus

import java.nio.charset.StandardCharsets

import com.typesafe.config.Config
import dispatch.{Http, url}
import me.socure.account.client.{GenericClient, ManageAccountsClientImpl}
import me.socure.model.{AccountInformation, ErrorResponse}
import me.socure.util.JsonEnrichments.formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountServiceIdPlusV2ClientImpl(http: Http, endpoint : String,config:Option[Config]=None)(implicit ec : ExecutionContext)  extends AccountServiceIdPlusV2Client {
  val logger : Logger = LoggerFactory.getLogger(classOf[ManageAccountsClientImpl])

  override def fetchAccountInfoByPublicApiKey (publicApiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {
    val request = (url(s"$endpoint/idplus/v2/accounts/publicapikeys/$publicApiKey"))
      .GET
      .setContentType("application/json",StandardCharsets.UTF_8)
    GenericClient.callApi[AccountInformation](http, "/idplus/v2/accounts/publicapikeys/", "GET", request,config=config)
  }
}
