package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.dashboardv2.AuditDetails
import me.socure.model.{AccountUIConfiguration, AccountUIConfigurationRequest, AccountsUIConfigurationRequest, ErrorResponse}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}

class AccountUIConfigurationClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountUIConfigurationClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = url(endpoint + "/ui/configuration")

  override def getUIAccountConfiguration(accountId: Long, creatorUserId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, AccountUIConfiguration]] = {
    val request = urlPrefix / accountId <<? Map("accountId" -> accountId.toString, "creator_user_id" -> creatorUserId.toString, "creator_account_id" -> creatorAccountId.toString)
    GenericClient.callApi[AccountUIConfiguration](http, "/ui/configuration", "GET", request)

  }

  override def saveUIAccountConfiguration(uIAccountConfigurationRequest: AccountUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val request = (urlPrefix.setContentType("application/json", Charset.forName("UTF-8"))
      <<? Map("creator_user_id" -> creatorUserId.toString, "creator_account_id" -> creatorAccountId.toString)
      << uIAccountConfigurationRequest.encodeJson()).POST
    GenericClient.callApiWithAuditDetails[Boolean](http,"/ui/configuration", "POST", request)
  }

  override def saveUIAccountConfigurationForAccounts(uIAccountsConfigurationRequest: AccountsUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    val request = ((urlPrefix / "accounts").setContentType("application/json", Charset.forName("UTF-8"))
      <<? Map("creator_user_id" -> creatorUserId.toString, "creator_account_id" -> creatorAccountId.toString)
      << uIAccountsConfigurationRequest.encodeJson()).POST
    GenericClient.callApiWithAuditDetails[Boolean](http, "/ui/configuration/accounts", "POST", request)
  }
}
