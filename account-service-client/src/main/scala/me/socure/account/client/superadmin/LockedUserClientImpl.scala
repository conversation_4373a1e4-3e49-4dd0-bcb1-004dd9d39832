package me.socure.account.client.superadmin

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.superadmin.LockedUsers
import me.socure.model.ErrorResponse
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON> on 6/4/16.
  */
class LockedUserClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends LockedUserClient {

  val logger : Logger = LoggerFactory.getLogger(classOf[LockedUserClientImpl])

  val urlPrefix : String = "/locked"

  override def getLockedList: Future[Either[ErrorResponse, Vector[LockedUsers]]] = {
    val request = url(endpoint + s"$urlPrefix/list")
    GenericClient.communicate[Vector[LockedUsers]]("/locked/list", "GET", request)
  }

  override def unlockUser(emails: List[String]): Future[Either[ErrorResponse, Int]] = {
    val request = url(endpoint + s"$urlPrefix/unlock") << Map("emails" -> emails.mkString(","))
    GenericClient.communicate[Int]("/locked/unlock", "POST", request)
  }
}
