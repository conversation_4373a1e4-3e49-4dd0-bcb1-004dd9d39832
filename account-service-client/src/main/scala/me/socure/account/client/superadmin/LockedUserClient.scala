package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.superadmin.LockedUsers

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON> on 6/4/16.
  */
trait LockedUserClient {

  def getLockedList : Future[Either[Error<PERSON><PERSON>ponse, Vector[LockedUsers]]]

  def unlockUser(emails : List[String]) : Future[Either[ErrorResponse, Int]]

}
