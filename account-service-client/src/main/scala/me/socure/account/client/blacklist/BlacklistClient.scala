package me.socure.account.client.blacklist

import me.socure.model.{BlackListAccountInformation, ErrorResponse, Industry}

import scala.concurrent.Future

/**
  * Created by alexand<PERSON> on 6/4/16.
  */
trait BlackListClient {

  def fetchIndustryByAccount(accountId: Long): Future[Either[ErrorResponse, Industry]]

  def fetchAccountInformation(apiKey: String): Future[Either[ErrorResponse, BlackListAccountInformation]]
}
