package me.socure.account.client.setting

import com.typesafe.config.Config
import me.socure.common.http.NonSecuredHttpFactory
import scala.concurrent.ExecutionContext.Implicits.global

object EnvironmentDetailsClientFactory {

  def create(endpoint: String): EnvironmentDetailsClient = {
    val httpClient = new NonSecuredHttpFactory().getHttpClient()
    new EnvironmentDetailsClientImpl(httpClient, endpoint)
  }

  def create(config: Config): EnvironmentDetailsClient = {

    val endpoint = config.getString("endpoint")
    create(endpoint = endpoint)
  }

}
