package me.socure.account.client

import java.nio.charset.Charset

import dispatch.url
import me.socure.model.ErrorResponse
import me.socure.model.account.ParentAccount
import me.socure.model.superadmin.AccountCreationForm
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 10/25/17.
  */
class ManageAccountsClientImpl(endpoint : String)(implicit ec : ExecutionContext)  extends ManageAccountsClient{
  val logger : Logger = LoggerFactory.getLogger(classOf[ManageAccountsClientImpl])

  val urlPrefix : String = "/account/management"

  override def create(accountCreationForm: AccountCreationForm): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/create_account").setContentType("application/json", Charset.forName("UTF-8")) << accountCreationForm.encodeJson()
    GenericClient.communicate[Boolean]("/account/management/create_account", "GET", request)
  }

  override def getParentAccounts(): Future[Either[ErrorResponse, List[ParentAccount]]] = {
    val request = url(s"$endpoint$urlPrefix/parent_accounts").setContentType("application/json", Charset.forName("UTF-8"))
    GenericClient.communicate[List[ParentAccount]]("/account/management/parent_accounts", "GET", request)
  }

  override def activateAccounts(accountIds: List[String]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/activate_accounts").setContentType("application/json", Charset.forName("UTF-8")) << accountIds.encodeJson()
    GenericClient.communicate[Boolean]("/account/management/activate_accounts", "GET", request)

  }

  override def updateAccountNameByPublicId (publicId: String, accountName: String): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$urlPrefix/update/account-name/by/public-id").POST <<? Map("publicId" -> publicId, "accountName" -> accountName)
    GenericClient.communicate[Int]("/account/management/update/account-name/by/public-id", "GET", request)
  }

}
