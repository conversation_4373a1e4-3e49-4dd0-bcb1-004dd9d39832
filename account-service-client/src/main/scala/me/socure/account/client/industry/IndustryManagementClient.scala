package me.socure.account.client.industry

import me.socure.model.{Industry, ErrorResponse}

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON> on 8/31/16.
  */
trait IndustryManagementClient {

  def getIndustryList : Future[Either[ErrorResponse, List[Industry]]]

  def upsertIndustry(industry: Industry) : Future[Either[ErrorResponse, Boolean]]

  def delete(sector: String) : Future[Either[ErrorResponse, Boolean]]

  def getIndustryBySector(sector: String) : Future[Either[ErrorResponse, Industry]]

}
