package me.socure.account.client.mla

import com.typesafe.config.Config

import scala.concurrent.ExecutionContext.Implicits.global
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext

object MLAFieldUpdateClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): MLAFieldUpdateImpl = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new MLAFieldUpdateImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): MLAFieldUpdateImpl = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new MLAFieldUpdateImpl(httpClient, endpoint)
  }
}
