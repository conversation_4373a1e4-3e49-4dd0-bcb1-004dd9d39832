package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountHierarchy, AccountInfoV2WithIndustry}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

import me.socure.account.client.GenericClient

class AccountHierarchyClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountHierarchyClient{

  val logger: Logger = LoggerFactory.getLogger(classOf[SubscriptionsClientImpl])
  val urlPrefix: String = "hierarchy"

  override def getAccountHierarchy(id: Long): Future[Either[ErrorResponse, AccountHierarchy]] = {
    val request = url(s"$endpoint/$urlPrefix/$id")
    GenericClient.callApi[AccountHierarchy](http, "/hierarchy", "GET", request)
  }

  override def getAccountHierarchyByAccountId(accountId: Long): Future[Either[ErrorResponse, AccountHierarchy]] = {
    val request = url(s"$endpoint/$urlPrefix/").addQueryParameter("account_id", accountId.toString)
    GenericClient.callApi[AccountHierarchy](http, "/hierarchy", "GET", request)
  }

  override def insertOrUpdateAccountHierarchy(accountHierarchy: AccountHierarchy): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint/$urlPrefix/").setContentType("application/json", Charset.forName("UTF-8")) << accountHierarchy.encodeJson()).PUT
    GenericClient.callApi[Int](http, "/hierarchy", "PUT", request)
  }

  override def updatePrimaryAdminCount(accountId:  Long, count:  Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint/$urlPrefix/update/admin_count") <<? Map("account_id" -> accountId.toString, "count" -> count.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/hierarchy", "POST", request)
  }

  override def activate(accountId:  Long): Future[scala.Either[_root_.me.socure.model.ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint/$urlPrefix/activate") <<? Map("account_id" -> accountId.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/hierarchy/activate", "POST", request)
  }

  override def deactivate(accountId:  Long): Future[scala.Either[_root_.me.socure.model.ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint/$urlPrefix/deactivate") <<? Map("account_id" -> accountId.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/hierarchy/deactivate", "POST", request)
  }

  override def allowAdminister(accountId:  Long): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint/$urlPrefix/administer/allow") <<? Map("account_id" -> accountId.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/hierarchy/administer/allow", "POST", request)
  }

  override def denyAdminister(accountId:  Long): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = (url(s"$endpoint/$urlPrefix/administer/deny") <<? Map("account_id" -> accountId.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/hierarchy/administer/deny", "POST", request)
  }

  override def list(accountId: Long, userId: Long): Future[Either[ErrorResponse, Seq[AccountInfoV2WithIndustry]]] = {
    val request = url(s"$endpoint/$urlPrefix/list").addQueryParameter("account_id", accountId.toString).addQueryParameter("user_id", userId.toString)
    GenericClient.callApi[Seq[AccountInfoV2WithIndustry]](http, "/hierarchy/administer/list", "POST", request)
  }

  override def validateAccountAccess(accountId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / "validate")
      .addQueryParameter("account_id", accountId.toString)
      .addQueryParameter("creator_account_id", creatorAccountId.toString)
      .GET
    GenericClient.callApi[Boolean](http, "/hierarchy/validate", "GET", request)
  }

  override def validateAccountAccess(accountId: Long, creatorAccountId: Long, accountPermission: Set[Int]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint/$urlPrefix/validate/access/permissions")
      .addQueryParameter("account_id", accountId.toString)
      .addQueryParameter("creator_account_id", creatorAccountId.toString)
      .addQueryParameter("permissions", URLEncoder.encode(accountPermission.mkString(","), StandardCharsets.UTF_8.name()))
    GenericClient.callApi[Boolean](http, "/hierarchy/validate/access/permissions", "GET", request)
  }

  override def getRootParent(accountId: Long): Future[Either[ErrorResponse, Long]] = {
    val request = url(s"$endpoint/$urlPrefix/get_root_parent/$accountId")
    GenericClient.callApi[Long](http, "/hierarchy/get_root_parent", "GET", request)
  }

  override def getSubAccounts(accountId: Long): Future[Either[ErrorResponse, Set[Long]]] = {
    val request = url(s"$endpoint/$urlPrefix/subaccounts/$accountId")
    GenericClient.callApi[Set[Long]](http, "/hierarchy/subaccounts", "GET", request)
  }

  override def getRootParentAccountType(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint/$urlPrefix/get_root_parent_account_type/$accountId")
    GenericClient.callApi[Int](http, "/hierarchy/get_root_parent_account_type", "GET", request)
  }
}
