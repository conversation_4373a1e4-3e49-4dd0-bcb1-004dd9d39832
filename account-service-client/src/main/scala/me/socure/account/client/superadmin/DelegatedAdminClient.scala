package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.superadmin.{DelegatedAdmin, AccountName}

import scala.concurrent.Future

/**
  * Created by <PERSON><PERSON><PERSON> on 6/9/16.
  */
trait DelegatedAdminClient {

  def getAccountName : Future[Either[ErrorResponse, Vector[AccountName]]]

  def getDelegatedAdmin(email : String) : Future[Either[ErrorResponse, List[DelegatedAdmin]]]

  def createdDelegatedAdmin(adminemail: String, email : String, firstname : String, lastname : String, company : String, contact : String, password: String, roles : Option[Set[Int]]) : Future[Either[ErrorResponse, Bo<PERSON>an]]

  def deleteDelegatedAdmin(email : String) : Future[Either[ErrorResponse, Boolean]]

  def updatePassword(email : String, password : String) : Future[Either[ErrorResponse, Boolean]]

  def updateRoles(email : String, roles: Set[Int]) : Future[Either[ErrorResponse, Boolean]]

  def promoteDelegatedAdmin(email : String) : Future[Either[ErrorResponse, Boolean]]

  def isUserExist(email : String) : Future[Either[ErrorResponse, Boolean]]

  def updateUserInformation(currentEmail : String, userInfo : DelegatedAdmin) : Future[Either[ErrorResponse, Boolean]]

}
