package me.socure.account.client.dashboard

import com.typesafe.config.Config
import dispatch._
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountInfo
import org.json4s._
import org.json4s.jackson.JsonMethods._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}


/**
 * Created by joonkang on 7/15/16.
 */
object SubAccountService {
  implicit val defaultFormat = DefaultFormats
  trait SubAccountClient {
    def findSubAccountsForAccountId(id: Long): Future[List[AccountInfo]]
    def findProvisionedSubAccountsForAccountId(id: Long): Future[List[AccountInfo]]
    def getAccountInfoForEnvironment(environmentId: Long): Future[Either[ErrorResponse, AccountInfo]]
  }

  class SubAccountHttpClient(accountHost:String,config:Option[Config]=None)(implicit ec:ExecutionContext) extends SubAccountClient {
    val logger: Logger = LoggerFactory.getLogger(getClass)
    import me.socure.account.client.http.NonSecuredHttp._

    override def findSubAccountsForAccountId(id: Long): Future[List[AccountInfo]] = {
      val req = url(s"$accountHost/dashboard/subaccounts_for_id/$id").GET
      val futureRes = GenericClient.communicateG("/dashboard/subaccounts_for_id/", "GET", req,config = config).either
      futureRes.map {
        res ⇒ {
          res match {
            case Right(s) if s.getStatusCode == 200 ⇒ {
              val accts = (parse(s.getResponseBody) \\ "data").extractOpt[List[AccountInfo]]
              accts.getOrElse(throw new Exception(s"No accounts found for id ${9}"))
            }
            case error ⇒
              logger.info(s"Failed while fetching the sub accounts for account $id", error)
              throw new Exception(s"Query Failure due to: $error")
          }
        }
      }
    }

    override def findProvisionedSubAccountsForAccountId(id: Long): Future[List[AccountInfo]] = {
      val req = url(accountHost) / "dashboard" /"subaccounts_for_id" / id.toString
      val futureRes = client(req).either
      futureRes.map {
        res ⇒ {
          res match {
            case Right(s) if s.getStatusCode  == 200  ⇒ {
              val body = s.getResponseBody
              val accts  = (parse(s.getResponseBody) \\ "data").extractOpt[List[AccountInfo]]
              accts.getOrElse(throw new Exception(s"No accounts found for id ${9}"))
            }
            case _ ⇒ throw new Exception("Query Failure")
          }
        }
      }
    }

    override def getAccountInfoForEnvironment(environmentId: Long): Future[Either[ErrorResponse, AccountInfo]] = {
      val request = url(accountHost) / "dashboard_account" /"get_account_info_for_environment" / environmentId.toString
      GenericClient.communicate[AccountInfo]("/dashboard_account/get_account_info_for_environment", "GET", request, additionalTags = Set(s"environmentId:$environmentId"),config = config)
    }
  }

}
