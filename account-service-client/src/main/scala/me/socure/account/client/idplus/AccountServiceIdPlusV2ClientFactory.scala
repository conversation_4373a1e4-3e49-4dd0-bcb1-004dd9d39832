package me.socure.account.client.idplus

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.{HMACEncrypter, SHAHMACEncrypter}
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.AccountInformation
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.duration.Duration

object AccountServiceIdPlusV2ClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter,config: Option[Config]=None): AccountServiceIdPlusV2Client = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountServiceIdPlusV2ClientImpl(httpClient, endpoint,config)
  }

  def create(config: Config)(implicit ec : ExecutionContext): AccountServiceIdPlusV2Client = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountServiceIdPlusV2ClientImpl(httpClient, endpoint,Some(config))
  }

  def createCached(config: Config, memcachedClient: MemcachedClient, memcachedTtl: Option[Duration],
                   memcachedTimeout: Option[(NonBlockingFutureTimeout, Duration)] = None)(implicit exe: ExecutionContext): AccountServiceIdPlusV2Client = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = memcachedTtl
    implicit val codec: Codec[AccountInformation, Array[Byte]] = JavaSerializationCodec.codec[AccountInformation]

    new CachedAccountServiceIdPlusV2Client(httpClient, endpoint, new ScalaCacheStorage[AccountInformation, Array[Byte]](), memcachedTimeout, memcachedTtl,config=Some(config))
  }
}
