package me.socure.account.client.account

import java.nio.charset.Charset

import com.typesafe.config.Config
import dispatch.{Http, Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountInfoV2WithPermission, AccountInfoWithPermission, AccountPrivateValue, ActiveAccountApiKey, ApiKeyInfo}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization

import scala.concurrent.{ExecutionContext, Future}

class AccountInfoClientV2(http: Http, endpoint: String,config:Option[Config]=None)(implicit val ec: ExecutionContext) {

  def getAccountInfoWithPermission(accountId: Long): Future[Either[ErrorResponse, AccountInfoWithPermission]] = {
    val req = url(s"$endpoint/account/v2/info/accounts/$accountId")
    callGenericClient[AccountInfoWithPermission]( "/account/v2/info/accounts", "GET", req, accountId = Some(accountId))
  }

  def getAccountInfoByPublicKey(publicApiKey: String): Future[Either[Option[ErrorResponse], Option[AccountInfoV2WithPermission]]] = {
    val req = url(s"$endpoint/account/v2/info/accounts/key/public/$publicApiKey")
    callApiWithOptionalError[Option[AccountInfoV2WithPermission]]("/account/v2/info/accounts/key/public", "GET", req)
  }

  def getAccountInfoByPrivateKey(privateApiValue: AccountPrivateValue): Future[Either[Option[ErrorResponse], Option[AccountInfoWithPermission]]] = {
    val body = Serialization.write(privateApiValue)
    val req = url(s"$endpoint/account/v2/info/accounts/key/private")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(body)
    callApiWithOptionalError[Option[AccountInfoWithPermission]]( "/account/v2/info/accounts/key/private", "POST", req)
  }

  def getAccountInfoByPublicId(publicId: String): Future[Either[ErrorResponse, AccountInfoWithPermission]] = {
    val req = url(s"$endpoint/account/v2/info/accounts/publicid/$publicId")
    callGenericClient[AccountInfoWithPermission]( "/account/v2/info/accounts/publicid", "GET", req)
  }

  def getApiPrivateByPublicApiKey(publicApiKey: String) = {
    val req = (url(endpoint) / "account" / "v2" / "info" / "accounts" / "apikeys" / "public" / publicApiKey)
    callGenericClient[Option[ApiKeyInfo]]( "/account/v2/info/accounts/apikeys/public", "GET", req)
  }

  def getActiveApiKeyByAccountAndEnvTypeID(accountId: Long, envTypeId: Int): Future[Either[ErrorResponse, ActiveAccountApiKey]] = {
    val req = url(s"$endpoint/account/v2/info/apikeys/active")
      .addQueryParameter("accountId", accountId.toString)
      .addQueryParameter("envTypeId", envTypeId.toString)
    callGenericClient[ActiveAccountApiKey]("/account/v2/info/accounts/apikeys/active", "GET", req, accountId = Some(accountId))
  }

  private def callGenericClient[A: Manifest](apiName:String ,httpMethod:String,request: Req, accountId: Option[Long] = None):Future[Either[ErrorResponse, A]] ={
    GenericClient.callApi(http, apiName, httpMethod, request,accountId= accountId,config=config)
  }
  private def callApiWithOptionalError[A: Manifest](apiName:String ,httpMethod:String,request: Req) : Future[Either[Option[ErrorResponse], A]] = {
    GenericClient.callApiWithOptionalError(http, apiName, httpMethod, request,config=config)
  }
}
