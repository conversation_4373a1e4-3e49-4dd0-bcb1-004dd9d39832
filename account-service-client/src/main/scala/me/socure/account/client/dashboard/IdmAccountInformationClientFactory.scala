package me.socure.account.client.dashboard

import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.model.account.IdmAccountInfoResponse
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

object IdmAccountInformationClientFactory {
  def create(endpoint: String)(implicit exe: ExecutionContext): IdmAccountInformationClient = {
    new IdmAccountInformationClientImpl(endpoint)
  }

  def createCached(
                    endpoint: String,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): IdmAccountInformationClient = {
    createCached(
      underlying = create(endpoint),
      memcachedClient = memcachedClient,
      ttl = ttl
    )
  }

  def createCached(
                    underlying: IdmAccountInformationClient,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): IdmAccountInformationClient = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = ttl
    implicit val codec: Codec[IdmAccountInfoResponse, Array[Byte]] = JavaSerializationCodec.codec[IdmAccountInfoResponse]

    new CachedIdmAccountInformationClient(
      underlying = underlying,
      cacheConfigurations = new ScalaCacheStorage[IdmAccountInfoResponse, Array[Byte]](),
      memcachedTtl = ttl
    )
  }
}
