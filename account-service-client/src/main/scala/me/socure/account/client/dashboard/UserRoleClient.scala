package me.socure.account.client.dashboard

import me.socure.model.{ErrorResponse, UsersAndRoles}
import me.socure.model.account.{DashboardUserPermissionResult, RolePermissionTemplateAssociation, UserRole, UserRoleInput, UserRoleResult}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait UserRoleClient {
  def getUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, UserRole]]
  def getUserRolesByAccountId(accountId: Long, creatorUserId: Long, creatorAccountId: Long, filterSystemDefinedRoles: Boolean = false): Future[Either[ErrorResponse, Seq[UserRoleResult]]]
  def getRolesByPublicId(publicAccountId: String): Future[Either[ErrorResponse, Seq[UserRoleResult]]]
  def insertUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]]
  def updateUserRole(userRoleInput: UserRoleInput): Future[Either[ErrorResponse, Boolean]]
  def deleteUserRole(id: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]]
  def getRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, RolePermissionTemplateAssociation]]
  def insertOrUpdateRolePermissionTemplateAssociation(rolePermissionTemplateAssociation: RolePermissionTemplateAssociation): Future[Either[ErrorResponse, Int]]
  def deleteRolePermissionTemplateAssociation(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]]
  def getDashboardPermissionsByRoleId(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]]
  def getDashboardPermissionsByRoleTypeID(roleType: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[DashboardUserPermissionResult]]]
  def deleteUserRoleWithPermissionTemplate(userRoleId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Boolean]]
  def getUsersAndRolesRecordCountForAccounts(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]] = None): Future[Either[ErrorResponse, Int]]
  def getUsersAndRolesByAccountIds(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]] = None, start: Option[Int], size: Option[Int]): Future[Either[ErrorResponse, Seq[UsersAndRoles]]]
}
