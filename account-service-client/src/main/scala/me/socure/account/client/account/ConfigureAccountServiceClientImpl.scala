package me.socure.account.client.account

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.SubAccountCreationRequest
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

class ConfigureAccountServiceClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends ConfigureAccountServiceClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  override def createSubAccount(subAccountCreationRequest: SubAccountCreationRequest): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / "configure" / "account" / "create")
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << subAccountCreationRequest.encodeJson()

    GenericClient.callApi[Boolean](http, "/configure/account/create", "POST", request)
  }
}
