package me.socure.account.client.idplus

import java.net.URLEncoder

import com.typesafe.config.Config
import dispatch._
import me.socure.account.client.GenericClient
import me.socure.model.{AccountInformation, DocvAccountInformation, ErrorResponse}
import me.socure.account.client.ClientConstant._

import scala.concurrent.{ExecutionContext, Future}
/**
  * Created by alexand<PERSON> on 5/6/16.
  */
class AccountServiceIdPlusClientImpl(endpoint: String,config:Option[Config]=None)(implicit ec: ExecutionContext) extends AccountServiceIdPlusClient {

  override def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {
    val request = url(endpoint + "/idplus" +  "/fetch_account_information_v2/" + URLEncoder.encode(apiKey, "utf-8"))
    callCommunicate[AccountInformation]("/idplus/fetch_account_information_v2", "GET", request, attributeKeyValue=Some(ApiKeyAttribute,apiKey))
  }

  override def fetchByPublicApiKey (apiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {
    val request = url(endpoint + "/idplus" +  "/fetch_account_information_by_public_api_key/" + URLEncoder.encode(apiKey, "utf-8"))
    callCommunicate[AccountInformation]("/idplus/fetch_account_information_by_public_api_key", "GET", request)
  }

  override def fetchByPublicAccountId(publicAccountId: String): Future[Either[ErrorResponse, DocvAccountInformation]] = {
    val pubId = URLEncoder.encode(publicAccountId, "utf-8")
    val request = url(s"$endpoint/idplus/fetch_account_information_by_publicId/$pubId")
    callCommunicate[DocvAccountInformation]("/idplus/fetch_account_information_by_publicId", "GET", request)
  }

  private def callCommunicate[A: Manifest](apiName:String ,httpMethod:String,request: Req,accountId: Option[Long] = None,attributeKeyValue:Option[(String,String)]=None) : Future[Either[ErrorResponse, A]]= {
    GenericClient.communicate(apiName, httpMethod, request, accountId=accountId,config=config , attributeKeyValue=attributeKeyValue)
  }
}
