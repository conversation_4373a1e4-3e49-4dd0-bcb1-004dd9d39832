package me.socure.account.client.setting

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.common.microservice.client.MicroServiceClientSupport
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferenceRequest, CAWatchlistPreferences, CAWatchlistPreferencesResponse}
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.model.{ErrorResponse, WatchlistSource, WatchlistSourceForEnvironment, WatchlistSourceRequest}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}

class ManageCAAccountsClientImpl(http: Http, baseEndpoint : String)(implicit ec : ExecutionContext)  extends ManageCAAccountsClient with MicroServiceClientSupport{

  val logger : Logger = LoggerFactory.getLogger(classOf[ManageCAAccountsClientImpl])
  private val endpoint = s"$baseEndpoint/settings/preferences/ca"
  private implicit val httpClient: Http = http

  override def getCAWatchList(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreference]] = {
    val request = creator match {
      case None => url(endpoint + "/watchlist/3.0").addQueryParameter("environmentId", environmentId.toString).GET
      case Some(creator) => url(endpoint + "/watchlist/3.0")
        .addQueryParameter("environmentId", environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[CAWatchlistPreference](http, "/settings/preferences/ca/watchlist/3.0", "GET", request)
  }

  override def getCAWatchLists(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreferences]] = {
    val request = creator match {
      case None => url(endpoint + "/cawatchlist/3.0").addQueryParameter("environmentId", environmentId.toString).GET
      case Some(creator) => url(endpoint + "/cawatchlist/3.0")
        .addQueryParameter("environmentId", environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[CAWatchlistPreferences](http, "/settings/preferences/ca/cawatchlist/3.0", "GET", request)
  }

  override def getCAWatchListsForAccounts(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreferencesResponse]] = {
    val request = creator match {
      case None => url(endpoint + "/accounts/cawatchlist/3.0").addQueryParameter("environmentId", environmentId.toString).GET
      case Some(creator) => url(endpoint + "/accounts/cawatchlist/3.0")
        .addQueryParameter("environmentId", environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[CAWatchlistPreferencesResponse](http, "/settings/preferences/ca/accounts/cawatchlist/3.0", "GET", request)
  }

  override def setCAWatchlist(preference: CAWatchlistPreference): Future[(AuditDetails,Either[ErrorResponse, CAWatchlistPreference])] = {
    val request = url(endpoint + "/watchlist/3.0")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(preference.encodeJson())
    GenericClient.callApiWithAuditDetails[CAWatchlistPreference](http, "/settings/preferences/ca/watchlist/3.0", "POST", request)
  }

  override def setCAWatchlistForAccounts(preference: CAWatchlistPreferenceRequest): Future[(AuditDetails, Either[ErrorResponse, CAWatchlistPreference])] = {
    val request = url(endpoint + "/accounts/watchlist/3.0")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(preference.encodeJson())
    GenericClient.callApiWithAuditDetails[CAWatchlistPreference](http, "/settings/preferences/ca/accounts/watchlist/3.0", "POST", request)
  }

  override def getWatchlistSource(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[scala.Either[ErrorResponse, Seq[WatchlistSource]]] = {
    val request = creator match {
      case None => url(endpoint + "/watchlist/sources/included").addQueryParameter("environmentId", environmentId.toString).GET
      case Some(creator) => url(endpoint + "/watchlist/sources/included")
        .addQueryParameter("environmentId", environmentId.toString)
        .addQueryParameter("creator_user_id", creator.userId.toString)
        .addQueryParameter("creator_account_id", creator.accountId.toString)
        .addQueryParameter("forceValidation", forceValidation.getOrElse(false).toString)
        .GET
    }
    GenericClient.callApi[Seq[WatchlistSource]](http, "/settings/preferences/ca/watchlist/sources/included", "GET", request)
  }

  override def includeWatchlistSource(watchlistSourceForEnvironment: WatchlistSourceForEnvironment): Future[(AuditDetails,Either[ErrorResponse, Boolean])] = {
    val request = url(endpoint + "/watchlist/sources/include")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(watchlistSourceForEnvironment.encodeJson())
    metrics.timeFuture("included.watchlist.sources")(call[Boolean](req = request, "included.watchlist.sources"))
    GenericClient.callApiWithAuditDetails[Boolean](http, "/settings/preferences/ca/watchlist/sources/include", "POST", request)
  }

  override def includeWatchlistSourceForAccounts(watchlistSourceRequest: WatchlistSourceRequest): Future[(AuditDetails,Either[ErrorResponse, Boolean])] = {
    val request = url(endpoint + "/accounts/watchlist/sources/include")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(watchlistSourceRequest.encodeJson())
    metrics.timeFuture("included.watchlist.sources")(call[Boolean](req = request, "included.watchlist.sources"))
    GenericClient.callApiWithAuditDetails[Boolean](http, "/settings/preferences/ca/accounts/watchlist/sources/include","POST",request)
  }

  override def getWatchlistSource: Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    val request = url(endpoint + "/watchlist/sources").GET
    GenericClient.callApi[Seq[WatchlistSource]](http, "/settings/preferences/ca/watchlist/sources", "GET", request)
  }

  override def getWatchlistSourceByCategory(categoryId: Int): Future[Either[ErrorResponse, Seq[WatchlistSource]]] = {
    val request = url(endpoint + s"/watchlist/sources/category/$categoryId").GET
    GenericClient.callApi[Seq[WatchlistSource]](http, "/settings/preferences/ca/watchlist/sources/category", "GET", request)
  }

  override def getHistoricalRange(): Future[Either[ErrorResponse, Set[String]]] = {
    val request = url(endpoint + s"/historicalrange").GET
    GenericClient.callApi[Set[String]](http, "/settings/preferences/ca/historicalrange", "GET", request)
  }
}
