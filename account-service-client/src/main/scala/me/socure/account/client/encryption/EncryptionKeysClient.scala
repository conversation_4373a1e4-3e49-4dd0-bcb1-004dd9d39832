package me.socure.account.client.encryption

import com.amazonaws.regions.Regions
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}

import scala.concurrent.Future

/**
  * Created by jamesanto on 4/17/17.
  */
trait EncryptionKeysClient {
  def regenerate(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]]

  def getKeys(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]]

  def hasKeys(accountId: AccountId): Future[Either[ErrorResponse, Boolean]]

  def getAccountIdByApiKey(apiKeyString: ApiKeyString): Future[Either[ErrorResponse, AccountId]]
}
