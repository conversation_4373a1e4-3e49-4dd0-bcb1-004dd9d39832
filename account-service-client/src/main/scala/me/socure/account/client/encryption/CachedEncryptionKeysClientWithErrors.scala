package me.socure.account.client.encryption

import com.amazonaws.regions.Regions
import me.socure.account.client.cache.ErrorCachingHandler
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/18/17.
  */
class CachedEncryptionKeysClientWithErrors(
                                            val underlying: EncryptionKeysClient,
                                            errorCachingHandler: ErrorCachingHandler
                                          )(implicit exe: ExecutionContext) extends EncryptionKeysClient {

  import CachedEncryptionKeysClientWithErrors._

  override def regenerate(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    errorCachingHandler.cacheIfRequired(key(accountId))(underlying.regenerate(accountId))
  }

  override def getKeys(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    errorCachingHandler.cacheIfRequired(key(accountId))(underlying.getKeys(accountId))
  }

  override def hasKeys(accountId: AccountId): Future[Either[ErrorResponse, Boolean]] = {
    errorCachingHandler.cacheIfRequired(keyHas(accountId))(underlying.hasKeys(accountId))
  }

  override def getAccountIdByApiKey(apiKeyString: ApiKeyString): Future[Either[ErrorResponse, AccountId]] = {
    errorCachingHandler.cacheIfRequired(keyAccountId(apiKeyString))(underlying.getAccountIdByApiKey(apiKeyString))
  }
}

object CachedEncryptionKeysClientWithErrors {
  def key(accountId: AccountId): String = s"soc_acc_spec_enc_we_${accountId.value}"

  def keyHas(accountId: AccountId): String = s"soc_acc_spec_enc_has_we_${accountId.value}"

  def keyAccountId(apiKeyString: ApiKeyString): String = s"soc_acc_spec_enc_acc_id_we_${apiKeyString.value}"
}
