package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountAnalyticsInfoRequest, AccountAnalyticsInfoResponse, AccountIdName, AccountIdNamesByRolesRequest, AccountPreferences, PublicAccount, PublicAccountIdName}
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization.{AccountWithEnvironmentDetails, AccountWithEnvironmentDetailsWithPublicId}
import org.joda.time.DateTime

import scala.concurrent.Future

/**
  * Created by sun<PERSON><PERSON> on 6/17/16.
  */
trait AccountInfoClient {
  def getAccountPublicId(accountId: Long): Future[Either[ErrorResponse, String]]
  def getAllAccounts(): Future[Either[ErrorResponse, Seq[PublicAccount]]]
  def getAllAccountsIds(): Future[Either[ErrorResponse, Seq[Long]]]
  def getAllActiveAccountNames : Future[Either[ErrorResponse, Map[java.lang.Long, AccountIdName]]]
  def getAllNonInternalActiveAccounts: Future[Either[ErrorResponse, List[Long]]]
  def hasRole(apiKeyString: ApiKeyString, role: Int): Future[Either[ErrorResponse, Boolean]]
  def getParentAccountsWithoutPrimaryUsers(): Future[Either[ErrorResponse, Seq[AccountIdName]]]
  def getAccountDetailsByPublicId(publicId: String): Future[Either[ErrorResponse, AccountWithEnvironmentDetails]]
  def getAccountDetailsById(id: Long): Future[Either[ErrorResponse, AccountWithEnvironmentDetailsWithPublicId]]
  def getAccountIdNamesByRoles(accountIdNamesRequest: AccountIdNamesByRolesRequest): Future[Either[ErrorResponse, Set[AccountIdName]]]
  def getAccountNameById(accountId: Long): Future[Either[ErrorResponse, String]]
  def getAccountNameAndPublicId (accountId: Long): Future[Either[ErrorResponse, PublicAccount]]
  def getEncryptionEnabledParentAccounts: Future[Either[ErrorResponse, Seq[PublicAccount]]]
  def getAccountPreferences(accountId: Long): Future[Either[ErrorResponse, AccountPreferences]]
  def getAllAccountNamesWithPublicId : Future[Either[ErrorResponse, Map[String, PublicAccountIdName]]]
  def getAccountAnalyticsInfo(accountId: Long): Future[Either[ErrorResponse, Option[AccountAnalyticsInfoResponse]]]
  def addAccountAnalyticsInfo(accountAnalyticsInfoReq: AccountAnalyticsInfoRequest): Future[Either[ErrorResponse, Boolean]]
  def updateAccountAnalyticsLastImportedDate(accountId: Long, lastImportedDate: DateTime): Future[Either[ErrorResponse, Boolean]]
  def getAnalyticsGlobalInfo(): Future[Either[ErrorResponse, Option[AnalyticsGlobalInfoResponse]]]
  def updateAnalyticsGlobalInfo(analyticsGlobalInfoReq: AnalyticsGlobalInfoRequest): Future[Either[ErrorResponse, Boolean]]
  def getAccountIdsWithPermission(permissionId: Int): Future[Either[ErrorResponse, Set[Long]]]
  def getAccountIdNamesWithAndWithoutPermission(withPermissionId:Int,withoutPermissionId:Int):Future[Either[ErrorResponse, List[AccountIdName]]]
}
