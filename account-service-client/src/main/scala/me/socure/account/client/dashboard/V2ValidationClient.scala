package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait V2ValidationClient {
  def isPermissionAvailable(accountId: Long, permissions: Set[Int], creator: Creator): Future[Either[ErrorResponse, Boolean]]
  def isValidEnvironmentPermissionById(envId: Long, permissions: Set[Int], creator: Creator): Future[Either[ErrorResponse, Boolean]]
  def isValidEnvironmentPermissionByName(envName: String, accountId: Long, permissions: Set[String], creator: Creator ): Future[Either[ErrorResponse, Boolean]]
  def isValidActiveUserAccountAssociation(accountId: Long, userId: Long): Future[Either[ErrorResponse, Boolean]]
}
