package me.socure.account.client.setting

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccessCredentials, PublicWebhook, PublicWebhookWithPublicAccountId, PublicWebhooksWithPublicAccountId}
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments.formats
import scala.concurrent.{ExecutionContext, Future}


class PublicWebhookClientImpl(http: Http, endpoint : String)(implicit ec : ExecutionContext) extends PublicWebhookClient {
  val logger : Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix : String = "/settings/webhook"


  override def listPublicWebhook: Future[Either[ErrorResponse, Vector[PublicWebhook]]] = {
    val request = url(s"$endpoint$urlPrefix/list_public_webhooks")
    GenericClient.callApi[Vector[PublicWebhook]](http, "/settings/webhook/list_public_webhooks", "GET", request)
  }

  def getPublicWebhook(environmentId: Long): Future[Either[ErrorResponse, PublicWebhook]] = {
    val request = url(s"$endpoint$urlPrefix/get_public_webhook") <<? Map("environment_id" -> environmentId.toString)
    GenericClient.callApi[PublicWebhook](http, "/settings/webhook/get_public_webhook", "GET", request)
  }

  def insertPublicWebhook(environmentId: Long, certificateEndpoint: String, certificate: String, subscriptionType : String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/insert_public_webhook") << Map(
      "environment_id" -> environmentId.toString,
      "endpoint" -> certificateEndpoint,
      "public_key_certificate" -> certificate,
      "subscription_type" -> subscriptionType
    )
    GenericClient.callApi[Boolean](http, "/settings/webhook/insert_public_webhook", "GET", request)
  }

  def updatePublicWebhook(environmentId: Long, certificateEndpoint: String, certificate: String, subscriptionType : String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/update_public_webhook") << Map(
      "environment_id" -> environmentId.toString,
      "endpoint" -> certificateEndpoint,
      "public_key_certificate" -> certificate,
      "subscription_type" -> subscriptionType
    )
    GenericClient.callApi[Boolean](http, "/settings/webhook/update_public_webhook", "GET", request)

  }

  def deletePublicWebhook(environmentId: Long, subscription_type: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_public_webhook") << Map("environment_id" -> environmentId.toString, "subscription_type" -> subscription_type.toString)
    GenericClient.callApi[Boolean](http, "/settings/webhook/delete_public_webhook", "GET", request)

  }

  override def getPublicWebhooks(envId: Long): Future[Either[ErrorResponse, PublicWebhooksWithPublicAccountId]] = {
    val request = url(s"$endpoint$urlPrefix/environment/$envId")
    GenericClient.callApi[PublicWebhooksWithPublicAccountId](http, "/settings/webhook/environment", "GET", request)

  }

  override def getPublicWebhook(accountId: Long, environmentTypeId: Long, subscriptionType: Long): Future[Either[ErrorResponse, PublicWebhookWithPublicAccountId]] = {
    val request = url(s"$endpoint$urlPrefix/account/$accountId/environment/type/$environmentTypeId/subscription/$subscriptionType")
    GenericClient.callApi[PublicWebhookWithPublicAccountId](http, "/settings/webhook/account/environment/type", "GET", request)
  }

}
