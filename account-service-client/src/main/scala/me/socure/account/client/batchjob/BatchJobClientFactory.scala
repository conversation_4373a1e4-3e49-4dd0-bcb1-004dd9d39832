package me.socure.account.client.batchjob

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object BatchJobClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): BatchJobClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new BatchJobClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): BatchJobClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val http = HttpWithHmacFactory.createInsecure(hmacConfig)
    new BatchJobClientImpl(http, endpoint)
  }
}
