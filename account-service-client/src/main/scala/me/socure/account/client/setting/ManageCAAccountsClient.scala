package me.socure.account.client.setting

import me.socure.model.{ErrorResponse, WatchlistSource, WatchlistSourceForEnvironment, WatchlistSourceRequest}
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferenceRequest, CAWatchlistPreferences, CAWatchlistPreferencesResponse}
import me.socure.model.dashboardv2.{AuditDetails, Creator}

import scala.concurrent.Future

trait ManageCAAccountsClient {
   def getCAWatchList(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreference]]
   def getCAWatchLists(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreferences]]
   def getCAWatchListsForAccounts(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreferencesResponse]]
   def setCAWatchlist(preference: CAWatchlistPreference): Future[(AuditDetails, Either[ErrorResponse, CAWatchlistPreference])]
   def setCAWatchlistForAccounts(preference: CAWatchlistPreferenceRequest): Future[(AuditDetails, Either[ErrorResponse, CAWatchlistPreference])]
   def getWatchlistSource(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, Seq[WatchlistSource]]]
   def includeWatchlistSource(watchlistSourceForEnvironment: WatchlistSourceForEnvironment): Future[(AuditDetails,Either[ErrorResponse, Boolean])]
   def includeWatchlistSourceForAccounts(watchlistSourceRequest: WatchlistSourceRequest): Future[(AuditDetails,Either[ErrorResponse, Boolean])]
   def getWatchlistSource: Future[Either[ErrorResponse, Seq[WatchlistSource]]]
   def getWatchlistSourceByCategory(categoryId: Int): Future[Either[ErrorResponse, Seq[WatchlistSource]]]
   def getHistoricalRange(): Future[Either[ErrorResponse, Set[String]]]
}

