package me.socure.account.client.superadmin

import java.nio.charset.{Charse<PERSON>, StandardCharsets}
import com.typesafe.config.Config
import dispatch.{Req, url}
import me.socure.account.client.GenericClient
import me.socure.constants.JsonFormats
import me.socure.model.account.{AccountAnalyticsInfoRequest, AccountAnalyticsInfoResponse, AccountIdName, AccountIdNamesByRolesRequest, AccountPreferences, PublicAccount, PublicAccountIdName}
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization.{AccountWithEnvironmentDetails, AccountWithEnvironmentDetailsWithPublicId}
import me.socure.model.ErrorResponse
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import org.joda.time.DateTime
import org.json4s.Formats
import org.slf4j.{Lo<PERSON>, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by sunderraj on 6/17/16.
  */
class AccountInfoClientImpl(endpoint : String,config:Option[Config]=None)(implicit ec : ExecutionContext) extends AccountInfoClient {
  val logger : Logger = LoggerFactory.getLogger(classOf[AccountInfoClientImpl])

  private implicit def jsonFormats: Formats = JsonFormats.formats

  val urlPrefix : String = "/account/info"

  override def getAccountPublicId(accountId: Long): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/publicid/accounts/$accountId").GET
    callCommunicate[String]("/account/info/publicid/accounts", "GET", request, accountId = Some(accountId))
  }

  override def getAllAccounts(): Future[Either[ErrorResponse, Seq[PublicAccount]]] = {
    val request = url(s"$endpoint$urlPrefix/list").GET
    callCommunicate[Seq[PublicAccount]]("/account/info/list", "GET", request)
  }

  override def getAllAccountsIds(): Future[Either[ErrorResponse, Seq[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/list_account_ids").GET
    callCommunicate[Seq[Long]]("/account/info/list_account_ids", "GET", request)
  }

  override def getAllActiveAccountNames : Future[Either[ErrorResponse, Map[java.lang.Long, AccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_all_account_names")
    callCommunicate[Map[java.lang.Long, AccountIdName]]("/account/info/get_all_account_names", "GET", request)
  }

  override def getAllNonInternalActiveAccounts: Future[Either[ErrorResponse, List[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/get_non_internal_active_accounts")
    callCommunicate[List[Long]]("/account/info/get_non_internal_active_accounts", "GET", request)
  }

  override def hasRole(apiKeyString: ApiKeyString, role: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/has_role") <<? Map(
      "api_key" -> apiKeyString.value,
      "role" -> role.toString
    )
    callCommunicate[Boolean]("/account/info/has_role", "GET", request)
  }

  override def getParentAccountsWithoutPrimaryUsers(): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {

    val request = url(s"$endpoint$urlPrefix/get_parent_accounts_no_prim_users")
    callCommunicate[Seq[AccountIdName]]("/account/info/get_parent_accounts_no_prim_users", "GET", request)
  }

  override def getAccountDetailsByPublicId(publicId: String): Future[Either[ErrorResponse, AccountWithEnvironmentDetails]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_details_by_public_id_v2") <<? Map("publicId" -> publicId)
    callCommunicate[AccountWithEnvironmentDetails]("/account/info/get_account_details_by_public_id_v2", "GET", request)
  }

  override def getAccountDetailsById(id: Long): Future[Either[ErrorResponse, AccountWithEnvironmentDetailsWithPublicId]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_details_by_id_v2/$id")
    callCommunicate[AccountWithEnvironmentDetailsWithPublicId]("/account/info/get_account_details_by_id_v2", "GET", request)
  }

  override def getAccountIdNamesByRoles(accountIdNamesRequest: AccountIdNamesByRolesRequest): Future[Either[ErrorResponse, Set[AccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_id_names_by_roles_v2").setContentType("application/json", Charset.forName("UTF-8")) << accountIdNamesRequest.encodeJson()
    callCommunicate[Set[AccountIdName]]("/account/info/get_account_id_names_by_roles_v2", "GET", request)
  }

  override def getAccountNameById(accountId: Long): Future[Either[ErrorResponse, String]] = {
    val request = url(s"$endpoint$urlPrefix/get_account_name_by_id/$accountId")
    callCommunicate[String]("/account/info/get_account_name_by_id", "GET", request, accountId = Some(accountId))
  }

  override def getAccountNameAndPublicId (accountId: Long): Future[Either[ErrorResponse, PublicAccount]] = {
    val request = url(s"$endpoint$urlPrefix/public-account/$accountId")
    callCommunicate[PublicAccount]("/account/info/public-account", "GET", request, accountId = Some(accountId))
  }

  override def getEncryptionEnabledParentAccounts: Future[Either[ErrorResponse, Seq[PublicAccount]]] = {
    val request = url(s"$endpoint$urlPrefix/get_encrypted_parent_accounts")
    callCommunicate[Seq[PublicAccount]]("/account/info/get_encrypted_parent_accounts", "GET", request)
  }

  override def getAccountPreferences (accountId: Long): Future[Either[ErrorResponse, AccountPreferences]] = {
    val request = url(s"$endpoint$urlPrefix/preferences/$accountId")
    callCommunicate[AccountPreferences]("/account/info/preferences", "GET", request, accountId = Some(accountId))
  }

  override def getAllAccountNamesWithPublicId : Future[Either[ErrorResponse, Map[java.lang.String, PublicAccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_all_account_names_with_public_id")
    callCommunicate[Map[java.lang.String, PublicAccountIdName]]("/account/info/get_all_account_names_with_public_id", "GET", request)
  }

  @deprecated
  override def getAccountAnalyticsInfo(accountId: Long): Future[Either[ErrorResponse, Option[AccountAnalyticsInfoResponse]]] = {
    val request = url(s"$endpoint$urlPrefix/analytics/$accountId")
    callCommunicate[Option[AccountAnalyticsInfoResponse]]("/account/info/analytics", "GET", request)
  }

  @deprecated
  override def addAccountAnalyticsInfo(accountAnalyticsInfoReq: AccountAnalyticsInfoRequest): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/analytics")
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
      .POST << accountAnalyticsInfoReq.encodeJson()
    callCommunicate[Boolean]("/account/info/analytics", "POST", request)
  }

  @deprecated
  override def updateAccountAnalyticsLastImportedDate(accountId: Long, lastImportedDate: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/analytics/last_imported_date").PUT <<? Map("account_id" -> accountId.toString, "last_imported_date" -> lastImportedDate.toString)
    callCommunicate[Boolean]("/account/info/analytics/last_imported_date", "PUT", request)
  }

  override def getAnalyticsGlobalInfo(): Future[Either[ErrorResponse, Option[AnalyticsGlobalInfoResponse]]] = {
    val request = url(s"$endpoint$urlPrefix/analytics")
    callCommunicate[Option[AnalyticsGlobalInfoResponse]]("/account/info/analytics", "GET", request)
  }

  override def updateAnalyticsGlobalInfo(analyticsGlobalInfoReq: AnalyticsGlobalInfoRequest): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/analytics")
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
      .PUT << analyticsGlobalInfoReq.encodeJson()
    callCommunicate[Boolean]("/account/info/analytics", "PUT", request)
  }

  override def getAccountIdsWithPermission(permissionId: Int): Future[Either[ErrorResponse, Set[Long]]] = {
    val request = url(s"$endpoint$urlPrefix/get_accounts_with_permission") <<? Map("permission_id" -> permissionId.toString)
    callCommunicate[Set[Long]]("/account/info/get_accounts_with_permission", "GET", request)
  }

  override def getAccountIdNamesWithAndWithoutPermission(withPermissionId: Int, withoutPermissionId: Int): Future[Either[ErrorResponse, List[AccountIdName]]] = {
    val request = url(s"$endpoint$urlPrefix/get_accounts_with_and_without_permission") <<? Map("with_permission_id" -> withPermissionId.toString, "with_out_permission_id" -> withoutPermissionId.toString)
    callCommunicate[List[AccountIdName]]("/account/info/get_accounts_with_and_without_permission","GET",request)
  }

  private def callCommunicate[A: Manifest](apiName: String, httpMethod: String, request: Req, accountId: Option[Long] = None): Future[Either[ErrorResponse, A]] ={
    GenericClient.communicate[A](apiName, httpMethod, request, accountId = accountId,config = config)
  }
}
