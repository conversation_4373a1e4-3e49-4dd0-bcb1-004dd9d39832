package me.socure.account.client

import me.socure.model.{DvConfigurationResponse, ErrorResponse}
import me.socure.model.account.{DvConfiguration, DvConfigurationRequest}
import me.socure.model.dashboardv2.{AuditDetails, Creator}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait EnvironmentDvConfigurationClient {
  def listDvConfiguration(environmentId: Long, creator: Option[Creator] = None) : Future[Either[ErrorResponse, Map[String, DvConfiguration]]]
  def saveDvConfiguration(dvConfigurationSeq: Seq[DvConfiguration], environmentId: Long, creator: Option[Creator] = None):  Future[(AuditDetails, Either[ErrorResponse, Boolean])]
  def listDvConfigurationForAccounts(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, DvConfigurationResponse]]
  def saveDvConfigurationForAccounts(dvConfigurationRequest: DvConfigurationRequest, environmentId: Long, creator: Option[Creator] = None):  Future[(AuditDetails, Either[ErrorResponse, Boolean])]
}
