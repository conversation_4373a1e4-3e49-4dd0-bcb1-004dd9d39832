package me.socure.account.client.dashboard

import me.socure.model.{ErrorResponse, PermissionTemplate}
import me.socure.model.account.{PermissionTemplateMapping, PermissionTemplateMappingInput, UserAccountAssociation, UserAccountAssociationInput}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait PermissionTemplateClient {
  def getPermissionTemplateMappingsByTemplateId(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[PermissionTemplateMapping]]]
  def insertPermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]]
  def updatePermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]]
  def deletePermissionTemplateMapping(permissionTemplateId: Long, environmentTypeId: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]]
  def getPermissionTemplate(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, PermissionTemplate]]
  def insertPermissionTemplate(permissionTemplate: PermissionTemplate): Future[Either[ErrorResponse, Int]]
  def updatePermissionTemplate(permissionTemplate: PermissionTemplate): Future[Either[ErrorResponse, Int]]
  def deletePermissionTemplate(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]]
  def getPermissionTemplates(userAccountAssociationId: Long): Future[Either[ErrorResponse, Seq[PermissionTemplate]]]
}
