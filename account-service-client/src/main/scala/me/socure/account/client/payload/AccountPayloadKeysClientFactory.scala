package me.socure.account.client.payload

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.model.account.AccountPayloadKey
import me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import net.spy.memcached.MemcachedClient
import scalacache.ScalaCache
import scalacache.serialization.Codec

import java.net.InetSocketAddress
import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

/**
  * Created by abhishek on 11/16/2021.
  */
object AccountPayloadKeysClientFactory {
  def create(realm: String, version: String, endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): AccountPayloadKeysClientImpl = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountPayloadKeysClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): AccountPayloadKeysClientImpl = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountPayloadKeysClientImpl(httpClient, endpoint)
  }

  // TTL is implicitly passed to ScalaCacheStorage where it is used by scalacache to store a value with time to live
  // Cache expiration is done by scalacache
  def createCached(
                    underlyingClient: AccountPayloadKeysClientImpl,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): CachedAccountPayloadKeysClientImpl = {

    implicit val scalaCache: ScalaCache[Array[Byte]] = ScalaCache(MemcachedCache(memcachedClient))
    implicit val implicitTtl: Option[Duration] = ttl
    implicit val codec: Codec[Seq[AccountPayloadKey], Array[Byte]] = JavaSerializationCodec.codec[Seq[AccountPayloadKey]]

    new CachedAccountPayloadKeysClientImpl(
      underlyingClient = underlyingClient,
      cachePayloadKeys = new ScalaCacheStorage[Seq[AccountPayloadKey], Array[Byte]](),
      memcachedTtl = ttl
    )
  }

  def createCached(config: Config)(implicit exe: ExecutionContext): CachedAccountPayloadKeysClientImpl = {
    val memcachedHost = config.getConfig("memcached").getString("host")
    val memcachedPort = config.getConfig("memcached").getInt("port")
    val ttl = config.getConfig("account.payload.cache").getInt("ttl")
    val cacheTtl = Duration.apply(ttl, TimeUnit.MILLISECONDS)

    createCached(
      underlyingClient = create(config),
      memcachedClient = new MemcachedClient(new InetSocketAddress(memcachedHost, memcachedPort)),
      ttl = Some(cacheTtl)
    )
  }
}
