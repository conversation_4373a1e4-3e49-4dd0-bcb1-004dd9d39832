package me.socure.account.client.dashboard

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object UserAccountAssociationClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): UserAccountAssociationClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new UserAccountAssociationClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): UserAccountAssociationClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new UserAccountAssociationClientImpl(httpClient, endpoint)
  }
}
