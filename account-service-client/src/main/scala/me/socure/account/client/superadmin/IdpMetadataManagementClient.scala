package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.model.superadmin.AccountIdpMetadata

import scala.concurrent.Future


trait IdpMetadataManagementClient {

  def listIdpMetadata() : Future[Either[ErrorResponse, List[AccountIdpMetadata]]]

  def insertIdpMetadata(accountId: Long, metadata: String) : Future[Either[ErrorResponse, Boolean]]

  def deleteIdpMetadata(accountId: Long) : Future[Either[ErrorResponse, Boolean]]

  def getIdpMetadata(entityId: String) : Future[Either[ErrorResponse, IdpMetadata]]

  def getIdpMetadataByAccountId(accountId: Long) : Future[Either[ErrorResponse, IdpMetadata]]
}
