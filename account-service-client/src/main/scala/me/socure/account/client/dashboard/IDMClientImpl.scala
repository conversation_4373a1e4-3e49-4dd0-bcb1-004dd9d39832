package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{ApiKeyInfo, IdmApiKey}
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments._

import java.nio.charset.Charset
import scala.concurrent.{ExecutionContext, Future}


class IDMClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends IDMClient {
  val logger: Logger = LoggerFactory.getLogger(getClass)
  private implicit val httpClient: Http = http
  val urlPrefix: String = "idm"

  override def fetchIDMKey(accountId: Long): Future[Either[ErrorResponse, Option[ApiKeyInfo]]] = {
    val request = url(endpoint) / urlPrefix / "fetch_api_key" / accountId
    GenericClient.callApi[Option[ApiKeyInfo]](http, "/idm/fetch_api_key", "GET", request, accountId = Some(accountId))
  }

  override def generateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "generate_api_key").setContentType("application/json", Charset.forName("UTF-8")) << idmApiKey.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/idm/generate_api_key", "POST", request)
  }

  override def deprecateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "deprecate_api_key").setContentType("application/json", Charset.forName("UTF-8")) << idmApiKey.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/idm/deprecate_api_key", "POST", request)
  }

  override def updateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "update_api_key").setContentType("application/json", Charset.forName("UTF-8")) << idmApiKey.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/idm/update_api_key", "POST", request)
  }
}
