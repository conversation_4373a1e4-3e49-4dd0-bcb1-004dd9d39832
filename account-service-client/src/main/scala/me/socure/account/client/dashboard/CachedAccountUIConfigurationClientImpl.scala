package me.socure.account.client.dashboard

import me.socure.account.service.common.AccountUIConfigurationCacheKeyProvider
import com.github.blemale.scaffeine.Cache
import me.socure.model.{AccountUIConfiguration, AccountUIConfigurationRequest, AccountsUIConfigurationRequest, ErrorResponse}
import me.socure.account.client.GenericClient._
import me.socure.model.dashboardv2.AuditDetails

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedAccountUIConfigurationClientImpl(underlying: AccountUIConfigurationClient,
                                             cacheConfigurations: Cache[String,AccountUIConfiguration],
                                             memcachedTtl: Option[Duration] = None)(implicit ec: ExecutionContext) extends AccountUIConfigurationClient {

  val getUIAccountConfigurationApiName = "/ui/configuration"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getUIAccountConfiguration(accountId: Long, creatorUserId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, AccountUIConfiguration]] = {
    val cacheKey = AccountUIConfigurationCacheKeyProvider.provide(accountId)
    getFromCache(getUIAccountConfigurationApiName, cacheConfigurations, cacheKey) match {
      case Some(result) => Future.successful(Right(result))
      case None => underlying.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId).flatMap {
        case r@Right(result) if result.accountId.isDefined => storeInCache(getUIAccountConfigurationApiName, cacheConfigurations, cacheKey, result, additionalTags)
            Future.successful(r)
        case r => Future.successful(r)
      }
    }
  }

  override def saveUIAccountConfiguration(uIAccountConfigurationRequest: AccountUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    underlying.saveUIAccountConfiguration(uIAccountConfigurationRequest, creatorUserId, creatorAccountId)
  }

  override def saveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest: AccountsUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    underlying.saveUIAccountConfigurationForAccounts(uIAccountConfigurationRequest, creatorUserId, creatorAccountId)
  }
}