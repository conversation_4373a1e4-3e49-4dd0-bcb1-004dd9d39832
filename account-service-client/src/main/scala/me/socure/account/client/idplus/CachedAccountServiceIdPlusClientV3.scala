package me.socure.account.client.idplus

import me.socure.account.client.GenericClient._
import me.socure.account.service.common.AccInfoCacheKeyProvider
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformationV3, ErrorResponse}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

/**
  * Created by gurubala.d on 1/25/2022
  */
class CachedAccountServiceIdPlusClientV3(client: AccountServiceIdPlusClientImplV3,
                                         cachingServiceV3: CacheService[String,AccountInformationV3],
                                         futureTimeout: NonBlockingFutureTimeout,
                                         cacheFetchTimeout: Duration,
                                         accInfoCacheKeyProvider: AccInfoCacheKeyProvider = AccInfoCacheKeyProvider)(implicit ec: ExecutionContext)
  extends AccountServiceIdPlusClientV3 {

  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)
  val fetchByApiKeyV3ApiName = "/idplus/fetch_account_information_v3"
  val fetchByPublicApiKeyV3ApiName = "/idplus/account-information/public-api-key"
  val fetchByAccountEnvTypeIdKeyV3ApiName = "/idplus/account-information/account-id"
  val additionalTags = Try(Set(s"ttl:${cachingServiceV3.expiration.toString}")) match {
    case Success(value) =>  value
    case _ => Set.empty[String]
  }

  override def fetchByApiKeyV3(apiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {

    val cacheKey = accInfoCacheKeyProvider.provideV3(apiKey)

    val cached = metrics.timeFuture("fetch") {
      getCacheWithCacheService(fetchByApiKeyV3ApiName, cachingServiceV3, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit")
        val futureResponse = client.fetchByApiKeyV3(apiKey)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save") {
              storeCacheWithCacheService(fetchByApiKeyV3ApiName, cachingServiceV3, cacheKey, response, Some(futureTimeout, cacheFetchTimeout), additionalTags)
            }
        }
        futureResponse
    }
  }

  override def fetchAccountInfoV3ByPublicApiKey(publicApiKey: String): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val cacheKey = accInfoCacheKeyProvider.provideV3(publicApiKey)

    val cached = metrics.timeFuture("fetch") {
      getCacheWithCacheService(fetchByPublicApiKeyV3ApiName, cachingServiceV3, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit")
        val futureResponse = client.fetchAccountInfoV3ByPublicApiKey(publicApiKey)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save") {
              storeCacheWithCacheService(fetchByPublicApiKeyV3ApiName, cachingServiceV3, cacheKey, response, Some(futureTimeout, cacheFetchTimeout), additionalTags)
            }
        }
        futureResponse
    }
  }

  override def fetchAccountInfoV3ByAccountId(accountId: Long, envTypeId: Int=1): Future[Either[ErrorResponse, AccountInformationV3]] = {
    val cacheKey = accInfoCacheKeyProvider.provideV3(s"${accountId}_$envTypeId")

    val cached = metrics.timeFuture("fetch") {
      getCacheWithCacheService(fetchByAccountEnvTypeIdKeyV3ApiName, cachingServiceV3, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit")
        val futureResponse = client.fetchAccountInfoV3ByAccountId(accountId, envTypeId)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save") {
              storeCacheWithCacheService(fetchByAccountEnvTypeIdKeyV3ApiName, cachingServiceV3, cacheKey, response, Some(futureTimeout, cacheFetchTimeout), additionalTags)
            }
        }
        futureResponse
    }
  }
}
