package me.socure.account.client.batchjob

import me.socure.batchjob.{AccountPermissionUpdateRequest, AccountPermissionUpdateResponse}
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountHierarchy, AccountInfoV2WithIndustry}

import scala.concurrent.Future

trait BatchJobClient {
  def updateAccountPermissions(apur: List[AccountPermissionUpdateRequest]): Future[Either[ErrorResponse, List[AccountPermissionUpdateResponse]]]
}