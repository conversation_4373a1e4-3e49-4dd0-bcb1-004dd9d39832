package me.socure.account.client.idplus

import me.socure.account.client.GenericClient._
import me.socure.account.service.common.AccInfoCacheKeyProvider
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformation, DocvAccountInformation, ErrorResponse}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

/**
  * Created by alexand<PERSON> on 5/9/16.
  */
class CachedAccountServiceDVOrchestraClient(client: AccountServiceIdPlusClientImpl,
                                            cachingService: CacheService[String, DocvAccountInformation],
                                            futureTimeout: NonBlockingFutureTimeout,
                                            cacheFetchTimeout: Duration,
                                            accInfoCacheKeyProvider: AccInfoCacheKeyProvider = AccInfoCacheKeyProvider)(implicit ec: ExecutionContext)
  extends AccountServiceIdPlusClient {

  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)
  val fetchByPublicAccountIdApiName = "/idplus/fetch_account_information_by_publicId"
  val additionalTags = Try(Set(s"ttl:${cachingService.expiration.toString}")) match {
    case Success(value) =>  value
    case _ => Set.empty[String]
  }

  override def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]] = ???

  override def fetchByPublicApiKey (publicApiKey: String): Future[Either[ErrorResponse, AccountInformation]] = ???

  override def fetchByPublicAccountId(publicAccountId: String): Future[Either[ErrorResponse, DocvAccountInformation]] = {
    val cacheKey = accInfoCacheKeyProvider.provide(publicAccountId)

    val cached = metrics.timeFuture("fetch") {
      getCacheWithCacheService(fetchByPublicAccountIdApiName, cachingService, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit")
        val futureResponse = client.fetchByPublicAccountId(publicAccountId)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save") {
              storeCacheWithCacheService(fetchByPublicAccountIdApiName, cachingService, cacheKey, response, Some(futureTimeout, cacheFetchTimeout), additionalTags)
            }
        }
        futureResponse
    }
  }
}
