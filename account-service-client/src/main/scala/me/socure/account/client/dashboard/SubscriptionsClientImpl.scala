package me.socure.account.client.dashboard

import java.nio.charset.{Charset, StandardCharsets}
import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.dto.DtoSubscriptionType
import me.socure.model.ErrorResponse
import me.socure.model.account.{SecretKeyWithSubscription, SubscriptionUpdateInput, SubscriptionsProvision}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class SubscriptionsClientImpl (http: Http, endpoint : String)(implicit ec : ExecutionContext)  extends SubscriptionsClient{

  val logger : Logger = LoggerFactory.getLogger(classOf[SubscriptionsClientImpl])
  val urlPrefix : String = "subscription"
  val event: String = "event"
  val events : String = "events"
  val account: String = "account"

  override def listSubscriptions(accountId: Long): Future[Either[ErrorResponse, Seq[Long]]] = {
    val request = url(endpoint) / urlPrefix / account / accountId / "subscriptions"
    GenericClient.callApi[Seq[Long]](http, "/subscription/account/subscriptions", "GET", request)
  }

  override def update(accountId: Long, subscriptionTypeId: Long, operation: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / account / accountId / "subscriptions" / subscriptionTypeId / operation)
      .POST
      .setContentType("application/json",StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/subscription/account/subscriptions", "POST", request)
  }

  override def listSubscriptionTypes(): Future[Either[ErrorResponse, Seq[DtoSubscriptionType]]] = {
    val req = url(endpoint) / urlPrefix / event / "subscription_types"
    GenericClient.callApi[Seq[DtoSubscriptionType]](http, "/subscription/event/subscription_types", "GET", req)
  }

  override def listSubscriptionTypesWithProvisionDetails(accountId : Long): Future[Either[ErrorResponse, Seq[SubscriptionsProvision]]] = {
    val req = (url(endpoint) / urlPrefix / event / "subscription_types_with_provision") <<? Map("account_id"->accountId.toString)
    GenericClient.callApi[Seq[SubscriptionsProvision]](http, "/subscription/event/subscription_types_with_provision", "GET", req)
  }

  override def updateSubscriptionStatus(environmentId : Long, subscriptionTypes: Set[Long], action : String ) = {
    val subscriptionUpdateInput = SubscriptionUpdateInput(environmentId.toInt, subscriptionTypes, action)
    val req = (url(endpoint) / urlPrefix / events / "subscriptions").setContentType("application/json", Charset.forName("UTF-8")) << subscriptionUpdateInput.encodeJson() setMethod "PUT"
    GenericClient.callApi[String](http, "/subscription/events/subscriptions", "GET", req)

  }

  override def updateWatchlistWebhookSecretKey(accountId: Long, environmentTypeId: Int, subscriptionTypeId: Long, secretKey: String): Future[Either[ErrorResponse, Int]] = {
    val requestBody = Serialization.write(Map("secret_key"->secretKey))
    val request = ((url(endpoint) / "settings" / "channel" / "account" / accountId / "environment" / "type" / environmentTypeId / "subscription" / "type" / subscriptionTypeId)
      .setContentType("application/json", Charset.forName("UTF-8")) << requestBody).PUT
    GenericClient.callApi[Int](http, "/settings/channel/account/environment/type/subscription/type", "PUT", request, accountId = Some(accountId), additionalTags = Set(s"environmentTypeId:$environmentTypeId,subscriptionType:$subscriptionTypeId"))
  }

}
