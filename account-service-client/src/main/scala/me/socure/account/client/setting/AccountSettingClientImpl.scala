package me.socure.account.client.setting

import java.nio.charset.Charset

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.account.client.http.NonSecuredHttp
import me.socure.dashboard.{AccountSettings, NewAccountSettings}
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.dashboardv2.{Creator, EnvironmentNameAndId}
import me.socure.model.{ErrorResponse, Response}
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}


/**
  * Created by sun<PERSON><PERSON> on 5/18/16.
  */
class AccountSettingClientImpl(baseUrl : String)(implicit val ec: ExecutionContext) extends AccountSettingClient {
  val logger : Logger = LoggerFactory.getLogger(classOf[AccountSettingClientImpl])

  val prefix : String = "/settings"
  val dashBoardSettingsPrefix = "/dashboard_account"

  override def updateDomain(accountId: Long, domains: List[String]): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/update_domain").setContentType("application/json", Charset.forName("UTF-8")) << AccountDomain(accountId, domains).encodeJson()
    GenericClient.communicateB("/settings/update_domain", "GET", request)
  }

  override def getAccountSettings(accountId: Long): Future[Either[ErrorResponse, AccountSettings]] = {
    val request = url(baseUrl + prefix + "/new/" + accountId)
    GenericClient.communicateG("/settings/new", "GET", request) map {
      case res if res.getStatusCode == 200 =>
        val newAccountSettings = res.getResponseBody.decodeJson[Response[NewAccountSettings]].data
        Right(AccountConvertors.toAccountSettings(newAccountSettings))
      case errRes =>
        Left(errRes.getResponseBody.decodeJson[Response[ErrorResponse]].data)
    }

  }

  override def getNewAccountSettings(accountId: Long): Future[Either[ErrorResponse, NewAccountSettings]] = {
    val request = url(baseUrl + prefix + "/new/" + accountId)
    GenericClient.communicate[NewAccountSettings]("/settings/new", "GET", request)
  }

  override def deleteAccountCache(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/remove_account_cache" + "/" + accountId).POST
    GenericClient.communicateB("/settings/remove_account_cache", "GET", request)
  }

  override def upsertAccountCache(accountId: Long, cacheDate: DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/update_account_cache").setContentType("application/json", Charset.forName("UTF-8")) << AccountOverallCache(0, cacheDate, accountId).encodeJson() //Set Cache Id as '0', this will be taken care when its intserted
    GenericClient.communicateB("/settings/update_account_cache", "GET", request)
  }

  override def deleteSocialNetworkKey(keyId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/remove_appkey" + "/" + keyId).POST
    GenericClient.communicateB("/settings/remove_appkey", "GET", request)
  }

  override def upsertSocialNetworkKey(accountId: Long, provider: String, appkey: String, appsecret: String, environment: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/upsert_appkey").setContentType("application/json", Charset.forName("UTF-8")) << SocialNetworkAppKeys(0, provider, appkey, appsecret, environment, accountId).encodeJson()
    GenericClient.communicateB("/settings/upsert_appkey", "GET", request)
  }

  override def upsertIndividualCache(accountId : Long, identifier: String, cacheDate : DateTime): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/upsert_individual_cache").setContentType("application/json", Charset.forName("UTF-8")) << AccountIndividualCache(0, identifier, cacheDate, accountId).encodeJson() //Set Cache Id as '0', this will be taken care when its intserted
    GenericClient.communicateB("/settings/upsert_individual_cache", "GET", request)
  }


  //This is used only in current admin - can be deprecated once current dashboard is replaced with 2.0
  override def deleteIndividualCache(accountId: Long, identifier: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/remove_individual_cache_identifier").setContentType("application/json", Charset.forName("UTF-8")) << AccountIndividualCache(0, identifier, new DateTime(), accountId).encodeJson() //Set Cache Id as '0', this will be taken care when its intserted
    GenericClient.communicateB("/settings/remove_individual_cache_identifier", "GET", request)
  }


  //This is used only in current admin - can be deprecated once current dashboard is replaced with 2.0
  override def deleteSocialNetworkKey(accountId : Long, provider: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(baseUrl + s"$prefix/remove_social_appkey_config" + "/" + accountId + "/" + provider).POST
    GenericClient.communicateB("/settings/remove_social_appkey_config", "GET", request)
  }

  override def getAllEnvironmentWithAccount: Future[Either[ErrorResponse, List[EnvironmentWithAccount]]] = {
    val request = url(baseUrl + s"$prefix/get_all_env")
    GenericClient.communicate[List[EnvironmentWithAccount]]("/settings/get_all_env", "GET", request)
  }

  override def getEnvironmentsByEmail(email: String): Future[Either[ErrorResponse, List[EnvironmentNameAndId]]] = {
    val request = url(baseUrl + s"$prefix/get_environment_details/$email")
    GenericClient.communicate[List[EnvironmentNameAndId]]("/settings/get_environment_details", "GET", request)
  }

  override def getApiKeysForAccountAndSubAccounts(accountId: Long): Future[Either[ErrorResponse, List[AccountApiKeys]]] = {
    val request = url(s"$baseUrl$dashBoardSettingsPrefix/get_apikeys_for_account_and_subaccounts/$accountId").GET
    GenericClient.communicate[List[AccountApiKeys]]("/dashboard_account/get_apikeys_for_account_and_subaccounts", "GET", request)
  }

  override def getModulesByAccountId(accountId: Long, creator: Creator): Future[Either[ErrorResponse, Seq[BusinessUserRolesLess]]] = {
    val request = (url(baseUrl) / "settings" / "modules" / accountId).GET <<? Map("creator_user_id" -> creator.userId.toString, "creator_account_id" -> creator.accountId.toString)
    GenericClient.communicate[Seq[BusinessUserRolesLess]]("/settings/modules", "GET", request)
  }
}
