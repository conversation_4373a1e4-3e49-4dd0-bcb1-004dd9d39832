package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.{ApiKeyInfo, IdmApiKey}

import scala.concurrent.Future

trait IDMClient {
  def fetchIDMKey(accountId: Long): Future[Either[ErrorResponse, Option[ApiKeyInfo]]]
  def generateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]]
  def deprecateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]]
  def updateIDMKey(idmApiKey: IdmApiKey): Future[Either[ErrorResponse, Boolean]]

}
