package me.socure.account.client.dashboard

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object PartnerAndSubAccountClientFactory {

  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): PartnerAndSubAccountInfoClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new PartnerAndSubAccountInfoClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): PartnerAndSubAccountInfoClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new PartnerAndSubAccountInfoClientImpl(httpClient, endpoint)
  }
}
