package me.socure.account.client.payload

import me.socure.account.client.GenericClient._
import me.socure.account.service.common.AccountPayloadCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountPayloadKey

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by a<PERSON><PERSON><PERSON><PERSON> on 12/15/2021.
 *
 * Get payload keys from underlyingClient when
 * not present in cachePayloadKeys storage
 * Checks for the presence of payload keys for
 * particular cache key (accountId) in cache storage
 */
class CachedAccountPayloadKeysClientImpl(underlyingClient: AccountPayloadKeysClientImpl,
                                         cachePayloadKeys: Storage[Seq[AccountPayloadKey]],
                                         memcachedTtl: Option[Duration] = None
                                        )(implicit ec: ExecutionContext)
  extends AccountPayloadKeysClient {

  val payloadKeysApiName = "/payload_keys/get_payload_keys"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getPayloadKeys(accountId: Option[Long] = None): Future[Either[ErrorResponse, Seq[AccountPayloadKey]]] = {
    accountId match {
      case Some(accId) =>
        val cacheKey = AccountPayloadCacheKeyProvider.provide(accId)
        getCache(payloadKeysApiName, cachePayloadKeys, cacheKey) flatMap {
          case Some(result) =>
            Future.successful(Right(result))
          case None => underlyingClient.getPayloadKeys(accountId).flatMap {
            case r@Right(result) => storeCache(payloadKeysApiName, cachePayloadKeys, cacheKey, result, None, additionalTags) map(_ => r)
            case r => Future.successful(r)
          }
        }
      case None =>
        val getAllKeysFuture = underlyingClient.getPayloadKeys(accountId)
        getAllKeysFuture.onSuccess {
          case Right(result) =>
            val resultMap = result.groupBy(_.accountId)
            resultMap.map { case (key, value) =>
              val cacheKey = AccountPayloadCacheKeyProvider.provide(key)
              cachePayloadKeys.remove(cacheKey)
              cachePayloadKeys.store(cacheKey, value)
            }
        }
        getAllKeysFuture
    }
  }
}