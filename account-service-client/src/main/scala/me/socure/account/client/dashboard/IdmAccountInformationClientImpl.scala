package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.IdmAccountInfoResponse
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, Future}

class IdmAccountInformationClientImpl(endpoint: String)(implicit ec: ExecutionContext) extends IdmAccountInformationClient  {
  val urlPrefix: String = "idm"

  override def fetchIdmAccountInformation(apiKey: String): Future[Either[ErrorResponse, IdmAccountInfoResponse]] = {
    val request = (url(endpoint) / urlPrefix / "account_information" / "v1" / apiKey).GET
    GenericClient.communicate[IdmAccountInfoResponse]("/idm/account_information/v1", "GET", request)
  }
}
