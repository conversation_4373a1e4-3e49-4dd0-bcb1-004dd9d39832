package me.socure.account.client

import java.lang.{Long => JLong}
import java.util.concurrent.{Callable, TimeUnit}

import com.github.rholder.retry.{RetryerBuilder, StopStrategies, WaitStrategies}
import com.google.common.base.Predicate
import com.google.common.cache.{<PERSON>ache<PERSON><PERSON>er, CacheLoader, LoadingCache}
import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.model.account.AccountDetailsWithInternalFlag

import scala.collection.JavaConverters._
import scala.concurrent.Await
import scala.concurrent.duration.FiniteDuration
import scala.util.Try

/**
  * Created by jamesanto on 1/12/17.
  */
class CachedAccountDetailsClient(accountInfoClient: AccountInfoClient) extends AccountDetailsClient {

  private val retryer = RetryerBuilder
    .newBuilder[AccountDetailsWithInternalFlag]()
    .retryIfResult(new Predicate[AccountDetailsWithInternalFlag] {
      override def apply(input: AccountDetailsWithInternalFlag): Boolean = input == null
    })
    .retryIfException()
    .withWaitStrategy(WaitStrategies.exponentialWait(1000, 1, TimeUnit.MINUTES))
    .withStopStrategy(StopStrategies.stopAfterAttempt(3))
    .build()

  private val cache: LoadingCache[JLong, AccountDetailsWithInternalFlag] = {
    CacheBuilder
      .newBuilder()
      .build(new CacheLoader[JLong, AccountDetailsWithInternalFlag] {
        override def load(accountId: JLong): AccountDetailsWithInternalFlag = {
          val accountNames = getAccountNames
          cache.putAll(accountNames.map {
            case (accId, accDetails) => long2Long(accId) -> accDetails
          }.asJava)
          accountNames.get(accountId) match {
            case Some(accountDetails) => accountDetails
            case None => throw new AccountNotFoundException(accountId)
          }
        }
      })
  }


  private def getAccountNames: Map[Long, AccountDetailsWithInternalFlag] = {
    try {
      val allAccountNamesFuture = accountInfoClient.getAllActiveAccountNames
      val nonInternalAccountNamesFuture = accountInfoClient.getAllNonInternalActiveAccounts
      val allAccountNamesHolder = Await.result(allAccountNamesFuture, FiniteDuration(5, TimeUnit.SECONDS))
      val nonInternalAccountNamesHolder = Await.result(nonInternalAccountNamesFuture, FiniteDuration(5, TimeUnit.SECONDS))

      (allAccountNamesHolder, nonInternalAccountNamesHolder) match {
        case (Right(allAccountNames), Right(nonInternalAccountNames)) => allAccountNames.map {
          case (jAccId, value) =>
            val sAccId = Long2long(jAccId)
            sAccId -> AccountDetailsWithInternalFlag(
              accountDetails = value,
              isInternal = !nonInternalAccountNames.contains(sAccId)
            )
        }
        case (allAccountNamesErr, nonInternalAccountNamesErr) =>
          throw new AccountDetailsLoadingException(s"Unable to fetch account details. All[$allAccountNamesErr], Non Internal[$nonInternalAccountNamesErr]")
      }
    } catch {
      case e: Throwable =>
        throw new AccountDetailsLoadingException("Unable to fetch account details", e)
    }
  }

  override def getById(accountId: Long): Try[AccountDetailsWithInternalFlag] = {
    Try(retryer.call(new Callable[AccountDetailsWithInternalFlag] {
      override def call(): AccountDetailsWithInternalFlag = cache.get(long2Long(accountId))
    }))
  }

}

class AccountNotFoundException(val accountId: Long) extends Exception(s"Account not found for ID : $accountId")

class AccountDetailsLoadingException(message: String, cause: Throwable = null) extends Exception(message, cause)
