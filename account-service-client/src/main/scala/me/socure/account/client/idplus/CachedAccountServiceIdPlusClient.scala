package me.socure.account.client.idplus

import me.socure.account.client.GenericClient._
import me.socure.account.service.common.AccInfoCacheKeyProvider
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformation, DocvAccountInformation, ErrorResponse}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.util.{Success, Try}

/**
  * Created by alexand<PERSON> on 5/9/16.
  */
class CachedAccountServiceIdPlusClient(client: AccountServiceIdPlusClientImpl,
                                       cachingService: CacheService[String,AccountInformation],
                                       futureTimeout: NonBlockingFutureTimeout,
                                       cacheFetchTimeout: Duration,
                                       accInfoCacheKeyProvider: AccInfoCacheKeyProvider = AccInfoCacheKeyProvider)(implicit ec: ExecutionContext)
  extends AccountServiceIdPlusClient {

  private val metrics: Metrics = JavaMetricsFactory.get(this.getClass)

  val fetchByApiKeyApiName = "/idplus/fetch_account_information_v2"
  val fetchByPublicApiKeyApiName = "/idplus/fetch_account_information_by_public_api_key"
  val additionalTags = Try(Set(s"ttl:${cachingService.expiration.toString}")) match {
    case Success(value) =>  value
    case _ => Set.empty[String]
  }

  override def fetchByApiKey(apiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {

    val cacheKey = accInfoCacheKeyProvider.provide(apiKey)

    val cached = metrics.timeFuture("fetch") {
      getCacheWithCacheService(fetchByApiKeyApiName, cachingService, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit")
        val futureResponse = client.fetchByApiKey(apiKey)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save") {
              storeCacheWithCacheService(fetchByApiKeyApiName, cachingService, cacheKey, response, Some(futureTimeout, cacheFetchTimeout), additionalTags)
            }
        }
        futureResponse
    }
  }

  override def fetchByPublicApiKey (publicApiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {
    val cacheKey = accInfoCacheKeyProvider.provide(publicApiKey)

    val cached = metrics.timeFuture("fetchAccountInfoFPublicApiKey") {
      getCacheWithCacheService(fetchByPublicApiKeyApiName, cachingService, cacheKey, Some(futureTimeout, cacheFetchTimeout))
    }

    cached.flatMap {
      case Some(response) =>
        metrics.increment("hit_fetchAccountInfoFPublicApiKey")
        Future.successful(Right(response))
      case None =>
        metrics.increment("nohit_fetchAccountInfoFPublicApiKey")
        val futureResponse = client.fetchByPublicApiKey(publicApiKey)
        futureResponse.onSuccess {
          case Right(response) =>
            metrics.timeFuture("save_fetchAccountInfoFPublicApiKey") {
              storeCacheWithCacheService(fetchByPublicApiKeyApiName, cachingService, cacheKey, response)
            }
        }
        futureResponse
    }
  }

  override def fetchByPublicAccountId(publicAccountId: String): Future[Either[ErrorResponse, DocvAccountInformation]] = ???
}
