package me.socure.account.client

import me.socure.common.storage.Storage
import me.socure.model.{DvConfigurationResponse, ErrorResponse}
import me.socure.model.account.{DvConfiguration, DvConfigurationRequest}
import me.socure.model.dashboardv2.{AuditDetails, Creator}
import me.socure.account.client.GenericClient._

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class CachedEnvironmentDvConfigurationClient(underlying: EnvironmentDvConfigurationClient,
                                             dvConfigurationStorage: Storage[Map[String, DvConfiguration]],
                                             dvConfigurationStorageForAccounts: Storage[DvConfigurationResponse],
                                             memcachedTtl: Option[Duration] = None)
                                            (implicit exe: ExecutionContext) extends EnvironmentDvConfigurationClient {

  import CachedEnvironmentDvConfigurationClient.key

  val listDvConfigurationApiName = "/dv/configuration"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def listDvConfiguration(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, Map[String, DvConfiguration]]] = {
    val cacheKey = key(environmentId)
    getCache(listDvConfigurationApiName, dvConfigurationStorage, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => underlying.listDvConfiguration(environmentId, creator).flatMap {
        case r@Right(result) => storeCache(listDvConfigurationApiName, dvConfigurationStorage, cacheKey, result, None, additionalTags) map(_ => r)
        case r => Future.successful(r)
      }
    }
  }

  override def saveDvConfiguration(dvConfigurationSeq: Seq[DvConfiguration], environmentId: Long, creator: Option[Creator] = None):  Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    underlying.saveDvConfiguration(dvConfigurationSeq, environmentId, creator)
  }

  override def listDvConfigurationForAccounts(environmentId: Long, creator: Option[Creator] = None): Future[Either[ErrorResponse, DvConfigurationResponse]] = {
    val cacheKey = key(environmentId)
    getCache(listDvConfigurationApiName, dvConfigurationStorageForAccounts, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None => underlying.listDvConfigurationForAccounts(environmentId, creator).flatMap {
        case r@Right(result) => storeCache(listDvConfigurationApiName, dvConfigurationStorageForAccounts, cacheKey, result, None, additionalTags) map (_ => r)
        case r => Future.successful(r)
      }
    }
  }

  override def saveDvConfigurationForAccounts(dvConfigurationRequest: DvConfigurationRequest, environmentId: Long, creator: Option[Creator] = None):  Future[(AuditDetails, Either[ErrorResponse, Boolean])] = {
    underlying.saveDvConfigurationForAccounts(dvConfigurationRequest, environmentId, creator)
  }
}

object CachedEnvironmentDvConfigurationClient {
  def key(environmentId: Long): String = s"acc_list_dv_config_$environmentId"
}
