package me.socure.account.client

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

/**
 * <AUTHOR> Kumar
 */
object AccountDvConfigurationClientFactory {

  def create(realm: String, version: String, endpoint: String, encrypter: HMACEncrypter): EnvironmentDvConfigurationClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new EnvironmentDvConfigurationClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): EnvironmentDvConfigurationClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new EnvironmentDvConfigurationClientImpl(httpClient, endpoint)
  }
}
