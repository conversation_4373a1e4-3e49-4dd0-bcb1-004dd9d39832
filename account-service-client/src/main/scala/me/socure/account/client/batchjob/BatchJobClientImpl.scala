package me.socure.account.client.batchjob

import java.nio.charset.Charset
import dispatch.{Http, url}
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountHierarchy, AccountInfoV2WithIndustry}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import me.socure.account.client.GenericClient
import me.socure.account.client.dashboard.{AccountHierarchyClient, SubscriptionsClientImpl}
import me.socure.batchjob.{AccountPermissionUpdateRequest, AccountPermissionUpdateResponse}

class BatchJobClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends BatchJobClient {

  val logger: Logger = LoggerFactory.getLogger(classOf[SubscriptionsClientImpl])
  val urlPrefix: String = "batchjob"

  override def updateAccountPermissions(apur: List[AccountPermissionUpdateRequest]): Future[Either[ErrorResponse, List[AccountPermissionUpdateResponse]]] = {
    val request = (url(s"$endpoint/$urlPrefix/account/permission/update")
      .setContentType("application/json", Charset.forName("UTF-8")) << apur.encodeJson()).POST
    GenericClient.callApi[List[AccountPermissionUpdateResponse]](http, "/account/permission/update", "POST", request)
  }
}