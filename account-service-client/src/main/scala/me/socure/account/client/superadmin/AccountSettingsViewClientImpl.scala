package me.socure.account.client.superadmin

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountSettings, OverallEnvironmentSettings}
import me.socure.util.JsonEnrichments.formats
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class AccountSettingsViewClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends AccountSettingsViewClient {
  val logger: Logger = LoggerFactory.getLogger(classOf[AccountInfoClientImpl])
  val urlPrefix: String = "/view/settings/account/"

  override def getAccountSettings(accountId: Long): Future[Either[ErrorResponse, AccountSettings]] = {
    val request = url(s"$endpoint$urlPrefix$accountId").GET
    GenericClient.callApi[AccountSettings](http, "/view/settings/account/", "GET", request, accountId = Some(accountId))
  }

  override def getAccountSettingsWithOverallSettings(accountId: Long, environmentId: Long): Future[Either[ErrorResponse, OverallEnvironmentSettings]] = {
    val request = url(s"$endpoint$urlPrefix${"overall/"}$accountId")
      .addQueryParameter("environmentId", environmentId.toString)
      .addQueryParameter("account_id", accountId.toString).GET
    GenericClient.callApi[OverallEnvironmentSettings](http, "/view/settings/account/overall", "GET", request)
  }


}
