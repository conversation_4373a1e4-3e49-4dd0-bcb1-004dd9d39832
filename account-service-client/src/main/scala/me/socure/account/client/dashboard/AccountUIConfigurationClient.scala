package me.socure.account.client.dashboard

import me.socure.model.dashboardv2.AuditDetails
import me.socure.model.{AccountUIConfiguration, AccountUIConfigurationRequest, AccountsUIConfigurationRequest, ErrorResponse}

import scala.concurrent.Future

trait AccountUIConfigurationClient {
  def getUIAccountConfiguration(accountId: Long, creatorUserId: Long, creatorAccountId: Long): Future[Either[ErrorResponse, AccountUIConfiguration]]

  def saveUIAccountConfiguration(uIAccountConfigurationRequest: AccountUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])]

  def saveUIAccountConfigurationForAccounts(uIAccountsConfigurationRequest: AccountsUIConfigurationRequest, creatorUserId: Long, creatorAccountId: Long): Future[(AuditDetails, Either[ErrorResponse, Boolean])]
}
