package me.socure.account.client.dashboard

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory
import com.github.blemale.scaffeine.Cache
import com.github.blemale.scaffeine.Scaffeine

import me.socure.model.AccountUIConfiguration

import net.spy.memcached.MemcachedClient


import java.util.concurrent.TimeUnit
import scala.concurrent.ExecutionContext
import scala.concurrent.duration.Duration

object AccountUIConfigurationClientFactory {

  def create(realm: String, version: String, endpoint: String, encrypter: HMACEncrypter)(implicit exe: ExecutionContext): AccountUIConfigurationClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new AccountUIConfigurationClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): AccountUIConfigurationClient = {

    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new AccountUIConfigurationClientImpl(httpClient, endpoint)
  }

  def createCached(
                    config: Config,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): AccountUIConfigurationClient = {
    createCached(
      underlying = create(config),
      memcachedClient = memcachedClient,
      ttl = ttl
    )
  }

  def createCached(
                    underlying: AccountUIConfigurationClient,
                    memcachedClient: MemcachedClient,
                    ttl: Option[Duration]
                  )(implicit exe: ExecutionContext): AccountUIConfigurationClient = {


    val caffeine: Cache[String, AccountUIConfiguration] = ttl.get match {
      case v: Duration => Scaffeine().recordStats().expireAfterWrite(Duration(v.toMillis, TimeUnit.MILLISECONDS)).build[String, AccountUIConfiguration]()
      case _ =>  Scaffeine().recordStats().build[String, AccountUIConfiguration]()
    }

    new CachedAccountUIConfigurationClientImpl(
      underlying = underlying,
      cacheConfigurations = caffeine,
      memcachedTtl = ttl
    )
  }

}
