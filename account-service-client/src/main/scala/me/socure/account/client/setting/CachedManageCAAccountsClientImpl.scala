package me.socure.account.client.setting

import dispatch.Http
import me.socure.account.client.GenericClient._
import me.socure.account.service.common.watchlist.ManageCAAccountsCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.account.CAWatchlistPreference
import me.socure.model.dashboardv2.Creator
import me.socure.model.{ErrorResponse, WatchlistSource}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedManageCAAccountsClientImpl(http: Http, endpoint: String, watchListPreferenceCache: Storage[CAWatchlistPreference], watchListSourceCache: Storage[Seq[WatchlistSource]],
                                       timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)], memcachedTtl: Option[Duration] = None) (implicit ec: ExecutionContext) extends ManageCAAccountsClientImpl(http, endpoint) {

  val getCAWatchListApiName = "/settings/preferences/ca/watchlist/3.0"
  val getWatchlistSourceApiName = "/settings/preferences/ca/watchlist/sources/included"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getCAWatchList(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[Either[ErrorResponse, CAWatchlistPreference]] = {

//    cache key has only environment id bec comply advantage service calls this API only with environment id, we are going to use this cached client only in comply advantage service
//    admin dashboard calls this API with creator as well as forceValidation, but there we will use normal client not the cached client

    val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetCAWatchList environmentId

    val future = getCache(getCAWatchListApiName, watchListPreferenceCache, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getCAWatchList(environmentId, creator, forceValidation)
          .flatMap {
            case r@Right(value) => storeCache(getCAWatchListApiName, watchListPreferenceCache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }

  override def getWatchlistSource(environmentId: Long, creator: Option[Creator] = None, forceValidation: Option[Boolean] = Some(false)): Future[scala.Either[ErrorResponse, Seq[WatchlistSource]]] = {

    //    cache key has only environment id bec comply advantage service calls this API only with environment id, we are going to use this cached client only in comply advantage service
    //    admin dashboard calls this API with creator as well as forceValidation, but there we will use normal client not the cached client

    val cacheKey = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource environmentId

    val future = getCache(getWatchlistSourceApiName, watchListSourceCache, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getWatchlistSource(environmentId, creator, forceValidation)
          .flatMap {
            case r@Right(value) => storeCache(getWatchlistSourceApiName, watchListSourceCache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }

}
