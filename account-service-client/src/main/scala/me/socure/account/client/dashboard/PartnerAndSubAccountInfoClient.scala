package me.socure.account.client.dashboard

import me.socure.model.ErrorResponse
import me.socure.model.account.{MergeAccountDetailsInput, MigratedAccount, MigrationAccountDetails, MigrationAccountDetailsInput, MigrationSubAccountDetailsInput, PartnerAndSubAccountInfo, PartnerAndSubAccountUserInfo}

import scala.concurrent.Future

/**
 * <AUTHOR> Kumar
 */
trait PartnerAndSubAccountInfoClient {
  def getMigrationAccountDetails(accountId: Long): Future[Either[ErrorResponse, MigrationAccountDetails]]
  def migrateAccount(migrationAccountDetails: MigrationAccountDetailsInput): Future[Either[ErrorResponse, Boolean]]
  def mergeAccount(mergeAccountDetails: MergeAccountDetailsInput):  Future[Either[ErrorResponse, Boolean]]
  def migrateSubAccount(migrationAccountDetails: MigrationSubAccountDetailsInput): Future[Either[ErrorResponse, Boolean]]
  def promoteSubAccount(migrationAccountDetails: MigrationAccountDetailsInput): Future[Either[ErrorResponse, Boolean]]
  def fetchAccountDetails(accountId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountInfo]]
  def fetchUserDetails(userId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]]
  def fetchUserDetailsByEmail(email: String): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]]
  def getMigratedAccounts(): Future[Either[ErrorResponse, List[MigratedAccount]]]
  def associateUserAccountRole(userId: Long, accountId: Long, roleId: Long, isSystemRole: Option[Boolean], updatedBy: Option[Long]): Future[Either[ErrorResponse, Boolean]]
  def updateAccountType(accountId: Long,accountType: Int): Future[Either[ErrorResponse, Boolean]]
  def updateAdminister(accountId: Long, administer: Boolean): Future[Either[ErrorResponse, Boolean]]
  def updateIsSponsorBank(accountId: Long, isSponsorBank: Boolean, initiatedBy: String): Future[Either[ErrorResponse, Boolean]]
  def swapUserAccountRoles(accountId: Long, userId: Long, swappingUserId: Long): Future[Either[ErrorResponse, Boolean]]
}
