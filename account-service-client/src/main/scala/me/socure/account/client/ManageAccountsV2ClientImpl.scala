package me.socure.account.client

import java.nio.charset.StandardCharsets

import dispatch.{Http, url}
import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.model.ErrorResponse
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments._
import org.apache.http.entity.ContentType
import org.json4s.jackson.Serialization

import scala.concurrent.{ExecutionContext, Future}


class ManageAccountsV2ClientImpl (http: Http, endpoint : String)(implicit ec : ExecutionContext)  extends ManageAccountsV2Client{
  val logger : Logger = LoggerFactory.getLogger(classOf[ManageAccountsClientImpl])
  val urlPrefix : String = "/account/management/v2"

  override def updateConsentId (accountId: Long, consentId: Int): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/update_consent_reason") <<? Map("account_id" -> accountId.toString, "consent_id" -> consentId.toString))
      .POST
      .setContentType(ContentType.APPLICATION_JSON.getMimeType,StandardCharsets.UTF_8)
    GenericClient.callApi[Int](http, "/account/management/v2/update_consent_reason", "GET", request)
  }

  override def getConsentReasonId(accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = url(s"$endpoint$urlPrefix/get_consent_reason/$accountId")
      .GET
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Int](http, "/account/management/v2/get_consent_reason", "GET", request)
  }

  override def updateIsActive (publicId: String, activate: String): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/publicid/$publicId/activate") <<? Map("activate" -> activate))
      .PUT
      .setContentType("application/json",StandardCharsets.UTF_8)
    GenericClient.callApi[Int](http, "/account/management/v2/publicid/activate", "GET", request)
  }

  override def updateIsDeleted (publicId: String, delete: String): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/publicid/$publicId/delete") <<? Map("delete" -> delete))
      .PUT
      .setContentType("application/json",StandardCharsets.UTF_8)
    GenericClient.callApi[Int](http, "/account/management/v2/publicid/delete", "PUT", request)
  }

  override def listAccounts(asr: AccountSearchRequest): Future[Either[ErrorResponse, Vector[AccountSearchResponse]]] = {
    val jsonBody = Serialization.write(asr)
    val request = url(s"$endpoint$urlPrefix/accounts/list")
      .POST
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

    GenericClient.callApi[Vector[AccountSearchResponse]](http, "/account/management/v2/accounts/list", "GET", request)
  }

  override def updateEmail(userId: Long, email: String): Future[Either[ErrorResponse, String]] = {
    val payload: Map[String, String] = Map("email" -> email)
    val jsonBody: String = Serialization.write(payload)
    val request = url(s"$endpoint$urlPrefix/accounts/users/$userId/email")
      .PUT
      .setBody(jsonBody)
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)

    GenericClient.callApi[String](http, "/account/management/v2/accounts/users/email", "PUT", request)
  }

  override def resetPassword(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/reset_password/$email")
      .GET
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/account/management/v2/reset_password/email", "GET", request)
  }

  override def onboardEmail(email: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/onboard-email/$email")
      .GET
      .setContentType(ContentType.APPLICATION_JSON.getMimeType, StandardCharsets.UTF_8)
    GenericClient.callApi[Boolean](http, "/account/management/v2/onboard-email/email", "GET", request)
  }
}
