package me.socure.account.client.ein

import me.socure.model.ErrorResponse
import me.socure.model.ein.{EIN, EINRequest, EINResponse}

import scala.concurrent.Future

trait EINClient {
  def saveEIN(einRequest: EINRequest): Future[Either[ErrorResponse, EINResponse]]

  def updateEIN(einRequest: EINRequest): Future[Either[ErrorResponse, EINResponse]]

  def fetchEIN(accountId: Long): Future[Either[ErrorResponse, Option[EIN]]]
}
