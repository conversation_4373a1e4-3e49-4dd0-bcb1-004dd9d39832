package me.socure.account.client.idplus

import me.socure.account.service.common.CacheKeyProvider
import me.socure.scalacache.elasticache.cluster.client.MemcachedCache
import net.spy.memcached.MemcachedClient

import scala.concurrent.{ExecutionContext, Future}
import scala.concurrent.duration.Duration
import me.socure.util.JsonEnrichments.JsonDecoder
import me.socure.util.JsonEnrichments.JsonEncoder
import scalacache.{ScalaCache, get => cget, put => cput}
/**
  * Created by gopal on 19/05/16.
  */
class CacheService[K, V<: AnyRef](var expiration: Duration)(implicit ec: ExecutionContext, cache: ScalaCache[Array[Byte]], k: Manifest[K], v: Manifest[V]) {

  def this(memcachedClient: MemcachedClient, expiration: Duration)(implicit ec: ExecutionContext, k: Manifest[K], v: Manifest[V]) {
    this(expiration)(ec, ScalaCache(MemcachedCache(memcachedClient)), k, v)
  }

  private def buildCacheKey(key: K) : String = {
    CacheKeyProvider.provide(key.toString)  //TODO : Hash the key, It's mandate as maximum key length is only 250.
  }

  def get(key: K): Future[Option[V]] = {
    val newKey = buildCacheKey(key)
    cget[String, Array[Byte]](newKey).map(_.map(_.decodeJson[V]))
  }

  def put(key: K, value: V): Future[Unit] = {
    val newKey = buildCacheKey(key)
    cput(newKey)(value.encodeJson(), ttl = Some(expiration))
  }

}
