package me.socure.account.client.account

import java.nio.charset.Charset

import com.typesafe.config.Config
import dispatch.{Http, Req, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountInfoV3, AccountPrivateValue}
import me.socure.util.JsonEnrichments._
import org.json4s.jackson.Serialization

import scala.concurrent.{ExecutionContext, Future}

class AccountInfoClientV3(http: Http, endpoint: String,config:Option[Config]=None)(implicit val ec: ExecutionContext) {

  def getAccountInfoByPublicKey(publicApiKey: String): Future[Either[Option[ErrorResponse], Option[AccountInfoV3]]] = {
    val req = (url(endpoint) / "account" / "v3" / "info" / "accounts" / "key" / "public" / publicApiKey)
    callApiWithOptionalError[Option[AccountInfoV3]]( "/account/v3/info/accounts/keys/public", "GET", req)
  }

  def getAccountInfoByPrivateKey(privateApiValue: AccountPrivateValue): Future[Either[Option[ErrorResponse], Option[AccountInfoV3]]] = {
    val body = Serialization.write(privateApiValue)
    val req = (url(endpoint) / "account" / "v3" / "info" / "accounts" / "key" / "private")
      .POST
      .setContentType("application/json", Charset.forName("UTF-8"))
      .setBody(body)
    callApiWithOptionalError[Option[AccountInfoV3]]( "/account/v3/info/accounts/keys/private", "POST", req)
  }


  private def callApiWithOptionalError[A: Manifest](apiName:String ,httpMethod:String,request: Req) : Future[Either[Option[ErrorResponse], A]] = {
    GenericClient.callApiWithOptionalError(http, apiName, httpMethod, request,config=config)
  }
}
