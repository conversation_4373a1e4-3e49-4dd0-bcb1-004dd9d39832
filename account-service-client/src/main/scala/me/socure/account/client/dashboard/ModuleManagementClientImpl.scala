package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.{GenericClient, ManageAccountsClientImpl}
import me.socure.model.dashboardv2.AuditDetails
import me.socure.model.{ErrorResponse, ModulesResponse}
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments.formats

import scala.concurrent.{ExecutionContext, Future}

class ModuleManagementClientImpl(http: Http, endpoint : String)(implicit ec : ExecutionContext) extends ModuleManagementClient {
  val logger : Logger = LoggerFactory.getLogger(classOf[ManageAccountsClientImpl])
  val urlPrefix : String = "/modules"

  override def getModules(accountId: Long, creatorAccountId: Long): Future[scala.Either[ErrorResponse, Set[String]]] = {
    val request = url(s"$endpoint$urlPrefix/$accountId") <<? Map("creator_account_id" -> creatorAccountId.toString)
    GenericClient.callApi[Set[String]](http, "/modules", "GET", request, accountId = Some(accountId))
  }

  override def getDefaultModules(accountId: Long,
                                 creatorAccountId: Long,
                                 creatorUserId: Long): Future[scala.Either[ErrorResponse, Set[Int]]] = {
    val request = url(s"$endpoint$urlPrefix/default/$accountId") <<? Map("creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString)
    GenericClient.callApi[Set[Int]](http, "/modules/default", "GET", request)
  }
  override def getDefaultModulesForAccounts(accountId: Long,
                                 creatorAccountId:  Long,
                                 creatorUserId: Long): Future[scala.Either[ErrorResponse, ModulesResponse]] = {
    val request = url(s"$endpoint$urlPrefix/accounts/default/$accountId") <<? Map("creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString)
    GenericClient.callApi[ModulesResponse](http, "/modules/accounts/default", "GET", request)
  }

  override def saveDefaultModules(accountId: Long,
                                  modules: Set[Int],
                                  creatorAccountId:  Long,
                                  creatorUserId: Long): Future[(AuditDetails,scala.Either[ErrorResponse, Boolean])] = {
    val request = url(s"$endpoint$urlPrefix/default/$accountId")
      .POST <<? Map("modules" -> modules.mkString(","), "creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString)
    GenericClient.callApiWithAuditDetails[Boolean](http, "/modules/default", "POST", request)
  }

  override def saveDefaultModulesForAccounts(accountIds: Seq[Long],
                                  modules: Set[Int],
                                  isForceInherit: Boolean = false,
                                  creatorAccountId: Long,
                                  creatorUserId: Long): Future[(AuditDetails,scala.Either[ErrorResponse, Boolean])] = {
    val request = url(s"$endpoint$urlPrefix/accounts/default")
      .POST <<? Map("modules" -> modules.mkString(","), "accountIds" -> accountIds.mkString(","), "isForceInherit" -> isForceInherit.toString(),  "creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString)
    GenericClient.callApiWithAuditDetails[Boolean]( http,"/modules/accounts/default", "POST", request)
  }

  override def clearDefaultModules(accountId: Long,
                                   creatorAccountId:  Long,
                                   creatorUserId: Long): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/default/$accountId")
      .DELETE <<? Map("creator_account_id" -> creatorAccountId.toString, "creator_user_id" -> creatorUserId.toString)
    GenericClient.callApi[Boolean](http, "/modules/default", "DELETE", request)
  }
}
