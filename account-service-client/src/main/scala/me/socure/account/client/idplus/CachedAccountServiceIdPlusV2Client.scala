package me.socure.account.client.idplus

import java.util.concurrent.{CompletableF<PERSON>ure, Executor, Executors, TimeUnit, TimeoutException}

import com.github.benmanes.caffeine.cache.{AsyncCacheLoader, Caffeine, Scheduler}
import com.google.common.util.concurrent.Striped
import com.typesafe.config.Config
import dispatch.Http
import me.socure.account.client.GenericClient._
import me.socure.account.client.watchlist.TimeoutRetryDecider
import me.socure.account.service.common.{AccInfoCacheKeyProvider, CacheKeyProvider}
import me.socure.common.retry.Retry
import me.socure.common.retry.decider.Decider
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformation, ErrorResponse}

import scala.compat.java8.FutureConverters._
import scala.concurrent.duration.{Duration, FiniteDuration}
import scala.concurrent.{ExecutionContext, Future}

class CachedAccountServiceIdPlusV2Client(http: Http, endpoint : String,
                                          cache: Storage[AccountInformation],
                                          timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)],
                                          memcachedTtl: Option[Duration] = None , config:Option[Config]=None)(implicit ec: ExecutionContext)
                                          extends AccountServiceIdPlusV2ClientImpl(http, endpoint,config) {

  val fetchAccountInfoByPublicApiKeyApiName = "/idplus/v2/accounts/publicapikeys/"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]
  val locks = Striped.lock(15) // max 15 concurrent requests allowed

  implicit val strategy: RetryStrategy = RetryStrategy.constantBackoff(FiniteDuration(30, TimeUnit.MILLISECONDS), 3)
  implicit val retryDecider: Decider[Any] = TimeoutRetryDecider

  val localCache = Caffeine
    .newBuilder()
    .maximumSize(500)
    .scheduler(Scheduler.forScheduledExecutorService(Executors.newSingleThreadScheduledExecutor))
    .refreshAfterWrite(java.time.Duration.ofMinutes(10))
    .expireAfterWrite(java.time.Duration.ofMinutes(20))
    .buildAsync(new AsyncCacheLoader[String, Either[ErrorResponse, AccountInformation]] {
      override def asyncLoad(publicApiKey: String, executor: Executor): CompletableFuture[Either[ErrorResponse, AccountInformation]] = {

        val cacheKey = CacheKeyProvider provide(AccInfoCacheKeyProvider provide publicApiKey)

        val cached = getCache(fetchAccountInfoByPublicApiKeyApiName, cache, cacheKey, timeoutOpt) flatMap {
          case Some(response) =>
            Future successful Right(response)
          case None =>
            Retry.apply1(() => getAccountInformation(publicApiKey))
        }

        cached.toJava.toCompletableFuture
      }
    })

  private def getAccountInformation(publicApiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {

    val lock = locks.get(publicApiKey)
    val lockAcquired = lock.tryLock(50, TimeUnit.MILLISECONDS)

    try {
      if(lockAcquired) {
        val cacheKey = CacheKeyProvider provide(AccInfoCacheKeyProvider provide publicApiKey)

        getCache(fetchAccountInfoByPublicApiKeyApiName, cache, cacheKey, timeoutOpt, additionalTags = Set(s"retries:true")) flatMap {
          case Some(response) =>
            Future successful Right(response)
          case None =>
            super.fetchAccountInfoByPublicApiKey(publicApiKey) flatMap {
              case r@Right(value) => storeCache(fetchAccountInfoByPublicApiKeyApiName, cache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
              case r => Future successful r
            }
        }
      } else {
        Future.failed(new TimeoutException())
      }

    } finally {
      if(lockAcquired)
        lock.unlock()
    }
  }

  override def fetchAccountInfoByPublicApiKey (publicApiKey: String): Future[Either[ErrorResponse, AccountInformation]] = {

    localCache.get(publicApiKey).toScala

  }
}
