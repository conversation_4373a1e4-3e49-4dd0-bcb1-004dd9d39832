package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.{MergeAccountDetailsInput, MigratedAccount, MigrationAccountDetails, MigrationAccountDetailsInput, MigrationSubAccountDetailsInput, PartnerAndSubAccountInfo, PartnerAndSubAccountUserInfo}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class PartnerAndSubAccountInfoClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends PartnerAndSubAccountInfoClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  private implicit val httpClient: Http = http
  val urlPrefix: String = "partner_and_sub_account_info"

  override def getMigrationAccountDetails(accountId: Long): Future[Either[ErrorResponse, MigrationAccountDetails]] = {
    val request = url(endpoint) / urlPrefix / "account" / "v1" / accountId
    GenericClient.callApi[MigrationAccountDetails](http, "/partner_and_sub_account_info/account/v1", "GET", request, accountId = Some(accountId))
  }

  override def migrateAccount(migrationAccountDetails: MigrationAccountDetailsInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "migrate").setContentType("application/json", Charset.forName("UTF-8")) << migrationAccountDetails.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/partner_and_sub_account_info/migrate", "POST", request)

  }

  override def mergeAccount(mergeAccountDetails: MergeAccountDetailsInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "merge").setContentType("application/json", Charset.forName("UTF-8")) << mergeAccountDetails.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/partner_and_sub_account_info/merge", "POST", request)
  }

  override def migrateSubAccount(migrationAccountDetails: MigrationSubAccountDetailsInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "migrate" / "subaccount").setContentType("application/json", Charset.forName("UTF-8")) << migrationAccountDetails.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/partner_and_sub_account_info/migrate/subaccount", "POST", request)
  }

  override def promoteSubAccount(migrationAccountDetails: MigrationAccountDetailsInput): Future[Either[ErrorResponse, Boolean]] = {
    val request = ((url(endpoint) / urlPrefix / "promote").setContentType("application/json", Charset.forName("UTF-8")) << migrationAccountDetails.encodeJson()).POST
    GenericClient.callApi[Boolean](http,"/partner_and_sub_account_info/promote", "POST", request)
  }

  override def fetchAccountDetails(accountId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountInfo]] = {
    val request = url(endpoint) / urlPrefix / "account" / "v2"/ accountId
    GenericClient.callApi[PartnerAndSubAccountInfo](http,"/partner_and_sub_account_info/account/v2", "GET", request, accountId = Some(accountId))
  }

  override def fetchUserDetails(userId: Long): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]] = {
    val request = url(endpoint) / urlPrefix / "user" / userId
    GenericClient.callApi[PartnerAndSubAccountUserInfo](http,"/partner_and_sub_account_info/user", "GET", request)
  }

  override def fetchUserDetailsByEmail(email: String): Future[Either[ErrorResponse, PartnerAndSubAccountUserInfo]] = {
    val request = (url(endpoint) / urlPrefix  / "user").addQueryParameter("email", email)
    GenericClient.callApi[PartnerAndSubAccountUserInfo](http,"/partner_and_sub_account_info/user", "GET", request)
  }

  override def getMigratedAccounts(): Future[Either[ErrorResponse, List[MigratedAccount]]] = {
    val request = (url(endpoint) / urlPrefix  / "migrated" / "parents")
    GenericClient.callApi[List[MigratedAccount]](http,"/partner_and_sub_account_info/migrated/parents", "GET", request)
  }

  override def associateUserAccountRole(userId: Long, accountId: Long, roleId: Long, isSystemRole: Option[Boolean], updatedBy: Option[Long]): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix  / "associate" / "user" / userId / "account" / accountId / "role" / roleId)
      .addQueryParameter("isSystemRole", isSystemRole.getOrElse(false).toString)
      .POST
    GenericClient.callApi[Boolean](http,"/partner_and_sub_account_info/associate/user/account/role", "POST", request, accountId = Some(accountId))

  }

  override def updateAccountType(accountId: Long, accountType: Int): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix  / "update" / "account" / accountId / "accountType" / accountType)
      .POST
    GenericClient.callApi[Boolean](http, "/partner_and_sub_account_info/update/account/accountType", "POST", request, accountId = Some(accountId))

  }

  override def updateAdminister(accountId: Long, administer: Boolean): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix  / "update" / "account" / accountId / "administer" / administer)
      .POST
    GenericClient.callApi[Boolean](http, "/partner_and_sub_account_info/update/account/administer", "POST", request, accountId = Some(accountId))
  }

  override def updateIsSponsorBank(accountId: Long, isSponsorBank: Boolean, initiatedBy: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix  / "update" / "account" / accountId / "isSponsorBank" / isSponsorBank)
      .addQueryParameter("initiatedBy", initiatedBy)
      .POST
    GenericClient.callApi[Boolean](http, "/partner_and_sub_account_info/update/account/isSponsorBank", "POST", request, accountId = Some(accountId))
  }

  override def swapUserAccountRoles(accountId: Long, userId: Long, swappingUserId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / "account" / accountId / "swapUserRoles" / userId / swappingUserId)
      .POST
    GenericClient.callApi[Boolean](http, "/partner_and_sub_account_info/account/swapUserRoles", "POST", request, accountId = Some(accountId))
  }
}
