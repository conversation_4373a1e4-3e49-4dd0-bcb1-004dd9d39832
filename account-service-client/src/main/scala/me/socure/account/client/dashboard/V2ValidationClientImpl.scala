package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.common.metrics.{JavaMetricsFactory, Metrics}
import me.socure.common.microservice.client.MicroServiceResponseParser
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class V2ValidationClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends V2ValidationClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "validation"

  override def isPermissionAvailable(accountId: Long, permissions: Set[Int], creator: Creator): Future[Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix)  <<? Map(
      "account_id" -> accountId.toString,
      "permissions" -> permissions.mkString(","),
      "creator_user_id" -> creator.userId.toString,
      "creator_account_id" -> creator.accountId.toString
    )
    GenericClient.callApi[Boolean](http, "/validation", "GET", request)
  }

  override def isValidEnvironmentPermissionById(envId: Long, permissions: Set[Int], creator: Creator): Future[Either[ErrorResponse, Boolean]]= {
    val request = (url(endpoint) / urlPrefix / "validate" / "environment" / "accessById")  <<? Map(
      "env_id" -> envId.toString,
      "permissions" -> permissions.mkString(","),
      "creator_user_id" -> creator.userId.toString,
      "creator_account_id" -> creator.accountId.toString
    )
    GenericClient.callApi[Boolean](http, "/validation/validate/environmen/accessById", "GET", request)
  }

  override def isValidEnvironmentPermissionByName(envName: String, accountId: Long, permissions: Set[String], creator: Creator): Future[Either[ErrorResponse, Boolean]]= {
    val request = (url(endpoint) / urlPrefix / "validate" / "environment" / "accessByName")  <<? Map(
      "account_id" -> accountId.toString,
      "env_name" -> envName,
      "permissions" -> permissions.mkString(","),
      "creator_user_id" -> creator.userId.toString,
      "creator_account_id" -> creator.accountId.toString
    )
    GenericClient.callApi[Boolean](http, "/validation/validate/environmen/accessByName", "GET", request)
  }

  override def isValidActiveUserAccountAssociation(accountId: Long, userId: Long): Future[Either[ErrorResponse, Boolean]]= {
    val request = (url(endpoint) / urlPrefix / "validate" / "user_account_association" / "active")  <<? Map(
      "account_id" -> accountId.toString,
      "user_id" -> userId.toString
    )
    GenericClient.callApi[Boolean](http, "/validation/validate/user_account_association/active", "GET", request)
  }
}
