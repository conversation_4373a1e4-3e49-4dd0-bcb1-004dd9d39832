package me.socure.account.client.ein

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object EINClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): EINClientImpl = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new EINClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit exe: ExecutionContext): EINClientImpl = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new EINClientImpl(httpClient, endpoint)
  }
}

