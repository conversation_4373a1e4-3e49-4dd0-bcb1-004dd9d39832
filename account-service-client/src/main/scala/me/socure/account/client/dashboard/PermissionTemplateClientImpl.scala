package me.socure.account.client.dashboard

import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.{ErrorResponse, PermissionTemplate}
import me.socure.model.account.{PermissionTemplateMapping, PermissionTemplateMappingInput}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class PermissionTemplateClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends PermissionTemplateClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix: String = "/permission_templates"

  override def getPermissionTemplateMappingsByTemplateId(permissionTemplateId: Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Seq[PermissionTemplateMapping]]] = {
    val request = url(s"$endpoint$urlPrefix/mappings") <<? Map("template_id" -> permissionTemplateId.toString, "user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[Seq[PermissionTemplateMapping]](http, "/permission_templates/mappings", "GET", request)
  }

  override def insertPermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/mappings").setContentType("application/json", Charset.forName("UTF-8")) << permissionTemplateMappingInput.encodeJson()).POST
    GenericClient.callApi[Int](http, "/permission_templates/mappings", "POST", request)
  }

  override def updatePermissionTemplateMapping(permissionTemplateMappingInput: PermissionTemplateMappingInput): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/mappings").setContentType("application/json", Charset.forName("UTF-8")) << permissionTemplateMappingInput.encodeJson()).PUT
    GenericClient.callApi[Int](http, "/permission_templates/mappings", "PUT", request)
  }

  override def deletePermissionTemplateMapping(permissionTemplateId: Long, environmentTypeId: Int, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix/mappings") << Map("template_id" -> permissionTemplateId.toString, "environment_type" -> environmentTypeId.toString, "user_id" -> userId.toString, "account_id" -> accountId.toString)).DELETE
    GenericClient.callApi[Int](http, "/permission_templates/mappings", "DELETE", request)

  }

  override def getPermissionTemplate(permissionTemplateId:  Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, PermissionTemplate]] = {
    val request = url(s"$endpoint$urlPrefix") <<? Map("template_id" -> permissionTemplateId.toString, "user_id" -> userId.toString, "account_id" -> accountId.toString)
    GenericClient.callApi[PermissionTemplate](http, "/permission_templates", "GET", request)
  }

  override def insertPermissionTemplate(permissionTemplate: PermissionTemplate): Future[Either[ErrorResponse, Int]] = {
    val request = {
      (url(s"$endpoint$urlPrefix").setContentType("application/json", Charset.forName("UTF-8")) << permissionTemplate.encodeJson()).POST
    }
    GenericClient.callApi[Int](http, "/permission_templates", "POST", request)
  }

  override def updatePermissionTemplate(permissionTemplate:  PermissionTemplate): Future[Either[ErrorResponse, Int]] = {
    val request = {
      (url(s"$endpoint$urlPrefix").setContentType("application/json", Charset.forName("UTF-8")) << permissionTemplate.encodeJson()).PUT
    }
    GenericClient.callApi[Int](http, "/permission_templates", "PUT", request)
  }

  override def deletePermissionTemplate(permissionTemplateId:  Long, userId: Long, accountId: Long): Future[Either[ErrorResponse, Int]] = {
    val request = (url(s"$endpoint$urlPrefix") <<? Map("template_id" -> permissionTemplateId.toString, "user_id" -> userId.toString, "account_id" -> accountId.toString)).DELETE
    GenericClient.callApi[Int](http, "/permission_templates", "DELETE", request)
  }

  override def getPermissionTemplates(userAccountAssociationId:  Long): Future[Either[ErrorResponse, Seq[PermissionTemplate]]] = {
    val request = {
      url(s"$endpoint$urlPrefix").addQueryParameter("user_account_association_id", userAccountAssociationId.toString)
    }
    GenericClient.callApi[Seq[PermissionTemplate]](http, "/permission_templates", "GET", request)
  }
}
