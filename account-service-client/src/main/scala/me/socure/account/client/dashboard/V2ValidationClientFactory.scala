package me.socure.account.client.dashboard

import com.typesafe.config.Config
import me.socure.common.hmac.algorithm.HMACEncrypter
import me.socure.common.hmac.filter.{HMACHttpRequestFilter, HttpWithHmacFactory}
import me.socure.common.http.NonSecuredHttpFactory

import scala.concurrent.ExecutionContext
import scala.concurrent.ExecutionContext.Implicits.global

object V2ValidationClientFactory {
  def create(realm: String, version: String,  endpoint: String, encrypter: HMACEncrypter): V2ValidationClient = {
    val hmacHttpRequestFilter: HMACHttpRequestFilter = new HMACHttpRequestFilter(realm, version, encrypter)
    val httpClient = new NonSecuredHttpFactory().getHttpClientWithFilters(Seq(hmacHttpRequestFilter))
    new V2ValidationClientImpl(httpClient, endpoint)
  }

  def create(config: Config)(implicit ec: ExecutionContext): V2ValidationClient = {
    val hmacConfig = config.getConfig("hmac")
    val endpoint = config.getString("endpoint")
    val httpClient = HttpWithHmacFactory.createInsecure(hmacConfig)
    new V2ValidationClientImpl(httpClient, endpoint)
  }
}
