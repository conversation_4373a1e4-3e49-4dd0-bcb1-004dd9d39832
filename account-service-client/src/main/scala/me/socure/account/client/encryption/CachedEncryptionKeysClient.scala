package me.socure.account.client.encryption

import com.amazonaws.regions.Regions
import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}
import me.socure.account.client.GenericClient._

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/18/17.
  */
class CachedEncryptionKeysClient(
                                  val underlying: EncryptionKeysClient,
                                  cacheKeys: Storage[Map[Regions, EncryptedKey]],
                                  cacheEnvId: Storage[AccountId],
                                  cacheHasKeys: Storage[Boolean],
                                  memcachedTtl: Option[Duration] = None
                                )(implicit exe: ExecutionContext) extends EncryptionKeysClient {

  import CachedEncryptionKeysClient._

  val getKeysApiName = "/encryption_keys/get"
  val hasKeysApiName = "/encryption_keys/has"
  val getAccountIdByApiKeyApiName = "/encryption_keys/get_account_id_by_api_key"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def regenerate(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    underlying.regenerate(accountId).flatMap {
      case r@Right(result) =>
        storeCache(getKeysApiName, cacheKeys, key(accountId), result, None, additionalTags) map(_ => r)
      case r => Future.successful(r)
    }
  }

  override def getKeys(accountId: AccountId): Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]] = {
    val cacheKey = key(accountId)
    getCache(getKeysApiName, cacheKeys, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None =>
        underlying
          .getKeys(accountId)
          .flatMap {
            case r@Right(result) => storeCache(getKeysApiName, cacheKeys, cacheKey, result, None, additionalTags) map(_ => r)
            case r => Future.successful(r)
          }
    }
  }

  override def hasKeys(accountId: AccountId): Future[Either[ErrorResponse, Boolean]] = {
    val cacheKey = keyHas(accountId)
    getCache(hasKeysApiName, cacheHasKeys, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None =>
        underlying
          .hasKeys(accountId)
          .flatMap {
            case r@Right(result) => storeCache(hasKeysApiName, cacheHasKeys, cacheKey, result, None, additionalTags) map(_ => r)
            case r => Future.successful(r)
          }
    }
  }

  override def getAccountIdByApiKey(apiKeyString: ApiKeyString): Future[Either[ErrorResponse, AccountId]] = {
    val cacheKey = keyAccountId(apiKeyString)
    getCache(getAccountIdByApiKeyApiName, cacheEnvId, cacheKey) flatMap {
      case Some(result) => Future.successful(Right(result))
      case None =>
        underlying
          .getAccountIdByApiKey(apiKeyString)
          .flatMap {
            case r@Right(result) => storeCache(getAccountIdByApiKeyApiName, cacheEnvId, cacheKey, result, None, additionalTags) map(_ => r)
            case r => Future.successful(r)
          }
    }
  }
}

object CachedEncryptionKeysClient {
  def key(accountId: AccountId): String = s"soc_acc_spec_enc_${accountId.value}"

  def keyHas(accountId: AccountId): String = s"soc_acc_spec_enc_has_${accountId.value}"

  def keyAccountId(apiKeyString: ApiKeyString): String = s"soc_acc_spec_enc_acc_id_${apiKeyString.value}"
}
