package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.client.GenericClient._
import me.socure.account.service.common.RateLimitCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ErrorResponse
import me.socure.model.ratelimiter.RateLimitingConfig

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedRateLimitingClientImpl(http: Http, endpoint: String, cache: Storage[Seq[RateLimitingConfig]], timeoutOpt: Option[(NonBlockingFutureTimeout, Duration)], memcachedTtl: Option[Duration] = None)
                                  (implicit ec: ExecutionContext) extends RateLimitingClientImpl(http, endpoint) {

  val getRateLimitsApiName1V2 = "/api/2.0/rate-limiting/policies_v2"
  val getRateLimitsApiName1 = "/api/1.0/rate-limiting/policies_v2"
  val getRateLimitsApiName2 = "/api/1.0/rate-limiting/policies"
  val additionalTags = if(memcachedTtl.isDefined) Set(s"ttl:${memcachedTtl.get.toString}") else Set.empty[String]

  override def getRateLimits(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]] = {
    val cacheKeyWithReferences = apiIds map (apiId => (apiId, RateLimitCacheKeyProvider provide(accountId, environmentTypeId, apiId)))

    val futures = cacheKeyWithReferences map (cacheKeyWithReference => getCache(getRateLimitsApiName1, cache, cacheKeyWithReference._2, None)
      map (result => if (result.isDefined) Some(cacheKeyWithReference._1, result.get) else None))

    val future = setTimeoutForFutures(futures, timeoutOpt).map { respList =>
      if (respList.forall(_.isDefined))
        Some(respList.map(resp => resp.get._1 -> resp.get._2).toMap)
      else
        None
    } recover {
      case e: Exception =>
        logger.error(s"Exception while fetching value from memcached for account $accountId, environment $environmentTypeId, apiIds ${apiIds.mkString(",")}", e)
        None
    }

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getRateLimits(accountId, environmentTypeId, apiIds)
          .flatMap {
            case r@Right(resultMap) =>
              val futures = resultMap.map(result => storeCache(getRateLimitsApiName1, cache, RateLimitCacheKeyProvider.provide(accountId, environmentTypeId, result._1), result._2, None, additionalTags)).toSet
              setTimeoutForFutures(futures, timeoutOpt) recover {
                case e: Exception =>
                  logger.error(s"Exception while storing value in memcached for account $accountId, environment $environmentTypeId, apiIds ${apiIds.mkString(",")}", e)
              } map (_ => r)
            case r => Future successful r
          }
    }

  }

  override def getRateLimitsV2(accountId: Long, environmentTypeId: Long, apiIds: Set[String]): Future[Either[ErrorResponse, Map[String, Seq[RateLimitingConfig]]]] = {
    val cacheKeyWithReferences = apiIds map (apiId => (apiId, RateLimitCacheKeyProvider provide(accountId, environmentTypeId, apiId)))

    val futures = cacheKeyWithReferences map (cacheKeyWithReference => getCache(getRateLimitsApiName1V2, cache, cacheKeyWithReference._2, None)
      map (result => if (result.isDefined) Some(cacheKeyWithReference._1, result.get) else None))

    val future = setTimeoutForFutures(futures, timeoutOpt).map { respList =>
      if (respList.forall(_.isDefined))
        Some(respList.map(resp => resp.get._1 -> resp.get._2).toMap)
      else
        None
    } recover {
      case e: Exception =>
        logger.error(s"Exception while fetching value from memcached for account $accountId, environment $environmentTypeId, apiIds ${apiIds.mkString(",")}", e)
        None
    }

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getRateLimitsV2(accountId, environmentTypeId, apiIds)
          .flatMap {
            case r@Right(resultMap) =>
              val futures = resultMap.map(result => storeCache(getRateLimitsApiName1V2, cache, RateLimitCacheKeyProvider.provide(accountId, environmentTypeId, result._1), result._2, None, additionalTags)).toSet
              setTimeoutForFutures(futures, timeoutOpt) recover {
                case e: Exception =>
                  logger.error(s"Exception while storing value in memcached for account $accountId, environment $environmentTypeId, apiIds ${apiIds.mkString(",")}", e)
              } map (_ => r)
            case r => Future successful r
          }
    }  }

  override def getRateLimits(accountId: Long, environmentTypeId: Long, apiId: String): Future[Either[ErrorResponse, Seq[RateLimitingConfig]]] = {

    val cacheKey = RateLimitCacheKeyProvider provide(accountId, environmentTypeId, apiId)

    val future = getCache(getRateLimitsApiName2, cache, cacheKey, timeoutOpt)

    future flatMap {
      case Some(result) => Future successful Right(result)
      case None =>
        super.getRateLimits(accountId, environmentTypeId, apiId)
          .flatMap {
            case r@Right(value) => storeCache(getRateLimitsApiName2, cache, cacheKey, value, timeoutOpt, additionalTags) map (_ => r)
            case r => Future successful r
          }
    }
  }
}
