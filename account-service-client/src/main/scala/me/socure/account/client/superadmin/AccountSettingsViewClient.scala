package me.socure.account.client.superadmin

import me.socure.model.ErrorResponse
import me.socure.model.account.{AccountSettings, OverallEnvironmentSettings}

import scala.concurrent.Future

trait AccountSettingsViewClient {
  def getAccountSettings(accountId: Long): Future[Either[ErrorResponse, AccountSettings]]

  def getAccountSettingsWithOverallSettings(accountId: Long, environmentId: Long): Future[Either[ErrorResponse, OverallEnvironmentSettings]]
}
