package me.socure.account.client.ein


import java.nio.charset.Charset

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.ein.{EIN, EINRequest, EINResponse}
import me.socure.util.JsonEnrichments._
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}

class EINClientImpl (http: Http,endpoint: String)(implicit ec : ExecutionContext) extends EINClient {

  val urlPrefix = "ein"
  val logger : Logger = LoggerFactory.getLogger(classOf[EINClientImpl])


  override def saveEIN(einRequest: EINRequest): Future[Either[ErrorResponse, EINResponse]] = {
    val request = (url(s"$endpoint/$urlPrefix/").setContentType("application/json", Charset.forName("UTF-8")) << einRequest.encodeJson()).POST
    GenericClient.callApi[EINResponse](http, "/ein", "POST", request)
  }

  override def updateEIN(einRequest: EINRequest): Future[Either[ErrorResponse, EINResponse]] = {
    val request = (url(s"$endpoint/$urlPrefix/").setContentType("application/json", Charset.forName("UTF-8")) << einRequest.encodeJson()).PUT
    GenericClient.callApi[EINResponse](http, "/ein", "PUT", request)
  }

  override def fetchEIN(accountId: Long): Future[Either[ErrorResponse, Option[EIN]]] = {
    val request = url(s"$endpoint/$urlPrefix/account/$accountId").GET
    GenericClient.callApi[Option[EIN]](http, "/ein/account", "GET", request, accountId = Some(accountId))
  }
}
