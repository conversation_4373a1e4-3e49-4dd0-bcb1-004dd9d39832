package me.socure.account.client.dashboard

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.constants.DashboardUserPermissions.DashboardUserPermission
import me.socure.model.ErrorResponse
import me.socure.model.account.PermissionConversionResponse
import org.slf4j.{Logger, LoggerFactory}
import me.socure.util.JsonEnrichments.formats
import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class DashboardUserPermissionClientImpl(http: Http, endpoint: String)(implicit ec: ExecutionContext) extends DashboardUserPermissionClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  val urlPrefix: String = "permissions"

  override def getAllPermissions: Future[Either[ErrorResponse, Seq[DashboardUserPermission]]] = {
    val request = url(endpoint) / urlPrefix
    GenericClient.callApi[Seq[DashboardUserPermission]](http, "/permissions", "GET", request)

  }

  override def getCurrentAndNewPermissions: Future[Either[ErrorResponse, PermissionConversionResponse]] = {
    val request = url(endpoint) / urlPrefix / "conversion"
    GenericClient.callApi[PermissionConversionResponse](http, "/permissions/conversion", "GET", request)

  }
}
