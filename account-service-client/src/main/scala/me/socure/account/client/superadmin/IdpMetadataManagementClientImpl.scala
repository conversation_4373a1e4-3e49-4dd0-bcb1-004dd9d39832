package me.socure.account.client.superadmin

import dispatch.url
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.model.superadmin.AccountIdpMetadata
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}


class IdpMetadataManagementClientImpl(endpoint : String)(implicit ec : ExecutionContext) extends IdpMetadataManagementClient {
  val urlPrefix : String = "/idp/metadata"
  val logger : Logger = LoggerFactory.getLogger(getClass)

  override def listIdpMetadata(): Future[Either[ErrorResponse, List[AccountIdpMetadata]]] = {
    val request = url(s"$endpoint$urlPrefix/list_idp_metadata")
    GenericClient.communicate[List[AccountIdpMetadata]]("/idp/metadata/list_idp_metadata", "GET", request)
  }

  override def insertIdpMetadata(accountId: Long, metadata: String): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/insert_idp_metadata") << Map("account_id" -> accountId.toString, "metadata" -> metadata)
    GenericClient.communicate[Boolean]("/idp/metadata/insert_idp_metadata", "GET", request)
  }

  override def deleteIdpMetadata(accountId: Long): Future[Either[ErrorResponse, Boolean]] = {
    val request = url(s"$endpoint$urlPrefix/delete_idp_metadata") << Map("account_id" -> accountId.toString)
    GenericClient.communicate[Boolean]("/idp/metadata/delete_idp_metadata", "GET", request)
  }

  override def getIdpMetadata(entityId: String): Future[Either[ErrorResponse, IdpMetadata]] = {
    val request = url(s"$endpoint$urlPrefix/get_idp_metadata") <<? Map("entity_id" -> entityId.toString)
    GenericClient.communicate[IdpMetadata]("/idp/metadata/get_idp_metadata", "GET", request)
  }

  override def getIdpMetadataByAccountId(accountId: Long): Future[Either[ErrorResponse, IdpMetadata]] = {
    val request = url(s"$endpoint$urlPrefix/get_idp_metadata_by_account") <<? Map("account_id" -> accountId.toString)
    GenericClient.communicate[IdpMetadata]("/idp/metadata/get_idp_metadata_by_account", "GET", request)
  }
}
