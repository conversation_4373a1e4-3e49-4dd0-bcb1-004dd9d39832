package me.socure.account.bulk.service

import java.io.{BufferedWriter, InputStream}
import me.socure.account.bulk.model.{CreateEditUserResponse, CreateRolesResponse}
import me.socure.model.ErrorResponse
import me.socure.model.dashboardv2.Creator

import scala.concurrent.Future

trait AccountServiceBulkLibrary {

  def createRoles(accountIds: Set[Long], creator: Creator, csv: InputStream): Future[Either[ErrorResponse, List[CreateRolesResponse]]]

  def createOrEditUsers(creator: Creator, csv: InputStream, bw: Option[BufferedWriter]=None): Either[ErrorResponse, List[CreateEditUserResponse]]

}
