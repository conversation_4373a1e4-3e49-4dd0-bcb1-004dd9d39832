package me.socure.account.bulk.impl

import com.google.common.util.concurrent.RateLimiter
import me.socure.account.bulk.model.{CreateEditUserResponse, CreateRolesResponse}
import me.socure.account.bulk.service.AccountServiceBulkLibrary
import me.socure.account.client.businessuserrole.BusinessUserRoleClient
import me.socure.account.client.dashboard.{AccountHierarchyClient, UserRoleClient}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.retry.Retry
import me.socure.common.retry.strategy.RetryStrategy
import me.socure.constants.{DashboardUserPermissions, SystemDefinedRoles}
import me.socure.mail.service.MailNotificationService
import me.socure.model.account.{AccountIdName, AccountInfoV2WithIndustry, EnvironmentPermissionsWithGlobalScope, UserRoleInput, UserRoleResult}
import me.socure.model.dashboardv2._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import me.socure.user.client.dashboard2.BusinessUserManagementClient
import me.socure.validator.CommonValidations
import org.apache.commons.csv.{CSVFormat, CSVParser}
import org.apache.commons.validator.routines.EmailValidator
import org.joda.time.DateTime
import org.slf4j.{Logger, LoggerFactory}

import java.io.{BufferedWriter, InputStream}
import java.nio.charset.StandardCharsets
import java.util.concurrent.TimeUnit
import scala.collection.JavaConverters._
import scala.collection.mutable
import scala.concurrent.duration.{DurationInt, FiniteDuration}
import scala.concurrent.{Await, ExecutionContext, Future}
import scala.util.{Success, Try}
class AccountServiceBulkLibraryImpl(userRoleClient: UserRoleClient,  businessUserManagementClient: BusinessUserManagementClient, mailNotificationService: Option[MailNotificationService]=None, businessUserRoleClient: Option[BusinessUserRoleClient] = None, accountHierarchyClient : Option[AccountHierarchyClient]= None)(implicit ec: ExecutionContext) extends AccountServiceBulkLibrary {

  val logger: Logger = LoggerFactory.getLogger(getClass)

  val roleRateLimiter: RateLimiter = RateLimiter.create(3) // 3 role API per second

  val createUserEmailHeader = "Email Address"
  val createUserFirstNameHeader = "First Name"
  val createUserLastNameHeader = "Last Name"
  val createUserContactHeader = "Phone Number"
  val createUserAccountsHeader = "Accounts"
  val createUserRoleHeader = "Roles"
  val createUserPropagateAccessHeader = "Propagate Access to All Sub-Accounts"
  val createUsersMandatoryHeader = Set(createUserEmailHeader, createUserFirstNameHeader, createUserLastNameHeader, createUserContactHeader, createUserAccountsHeader, createUserRoleHeader, createUserPropagateAccessHeader)

  val createRolesMandatoryHeader = "Name"
  val accountIdsHeader = "AccountIds"
  val fieldsToBeExported = List("First Name", "Last Name", "Email Address", "Phone Number", "Accounts", "Roles", "Propagate Access to All Sub-Accounts", "Status", "Processing Timestamp", "Error")
  val errorFieldsToBeExported = List("Status", "Reason")
  val createRolesHeader = Set(("Accounts", 0, Map("Can View" -> Set(DashboardUserPermissions.ACCOUNTS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("ACCOUNTS_"))),
    ("Roles", 0, Map("Can View" -> Set(DashboardUserPermissions.USER_ROLES_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("USER_ROLES_"))),
    ("Users", 0, Map("Can View" -> Set(DashboardUserPermissions.USERS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("USERS_"))),
    ("Batch Jobs", 0, Map("Can View" -> Set(DashboardUserPermissions.BATCHJOB_VIEW.id))),
    ("DevHub", 0, Map("Can View" -> Set(DashboardUserPermissions.DOCUMENTATION_VIEW.id))),
    ("PII Access", 0, Map("Can View" -> Set(DashboardUserPermissions.PII_ACCESS_VIEW.id))),
    ("Transactions-Production", 1, Map("Can View" -> Set(DashboardUserPermissions.TRANSACTIONS_VIEW.id), "Create Only" -> Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id), "Can Edit" -> DashboardUserPermissions.startsWithName("TRANSACTIONS_"))),
    ("Transactions-Certification", 2, Map("Can View" -> Set(DashboardUserPermissions.TRANSACTIONS_VIEW.id), "Create Only" -> Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id), "Can Edit" -> DashboardUserPermissions.startsWithName("TRANSACTIONS_"))),
    ("Transactions-Sandbox", 3, Map("Can View" -> Set(DashboardUserPermissions.TRANSACTIONS_VIEW.id), "Create Only" -> Set(DashboardUserPermissions.TRANSACTIONS_CREATE.id), "Can Edit" -> DashboardUserPermissions.startsWithName("TRANSACTIONS_"))),
    ("Developers-Production", 1, Map("Can View" -> Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("EVENT_MANAGERS_"))),
    ("Developers-Certification", 2, Map("Can View" -> Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("EVENT_MANAGERS_"))),
    ("Developers-Sandbox", 3, Map("Can View" -> Set(DashboardUserPermissions.EVENT_MANAGERS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("EVENT_MANAGERS_"))),
    ("Settings-Production", 1, Map("Can View" -> Set(DashboardUserPermissions.SETTINGS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("SETTINGS_VIEW_"))),
    ("Settings-Certification", 2, Map("Can View" -> Set(DashboardUserPermissions.SETTINGS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("SETTINGS_VIEW_"))),
    ("Settings-Sandbox", 3, Map("Can View" -> Set(DashboardUserPermissions.SETTINGS_VIEW.id), "Can Edit" -> DashboardUserPermissions.startsWithName("SETTINGS_VIEW_"))))
  implicit val strategy = RetryStrategy.times(3).delayed(FiniteDuration.apply(1, TimeUnit.MINUTES))

  override def createRoles(accountIds: Set[Long], creator: Creator, csvInputStream: InputStream): Future[Either[ErrorResponse, List[CreateRolesResponse]]] = {
    val csvParser = CSVParser.parse(csvInputStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withHeader())

    val csvHeaders = csvParser.getHeaderNames.asScala.toSet

    if (csvHeaders.contains(createRolesMandatoryHeader)) {
      val createRolesFuture = csvParser.iterator().asScala.flatMap(csvRecord => {
        val fieldToValues = mutable.Map.empty[Int, EnvironmentPermissionsWithGlobalScope]

        createRolesHeader.foreach(header => {
          Try(csvRecord.get(header._1)) match {
            case Success(value) if header._3.contains(value) =>
              val environmentPermissionsWithGlobalScope = fieldToValues.find(_._1 == header._2).map(_._2).getOrElse(EnvironmentPermissionsWithGlobalScope(header._2, Set.empty[Int], None))
              fieldToValues += (header._2 -> environmentPermissionsWithGlobalScope.copy(permissions = environmentPermissionsWithGlobalScope.permissions ++ header._3(value)))
            case _ =>

          }
        })

        val roleName = csvRecord.get(createRolesMandatoryHeader)

        val parsedAccountIds = Try {
          val value = csvRecord.get(accountIdsHeader)
          Some(value.split(",").map(_.trim.toLong).toList)
        } match {
          case Success(Some(value)) => value
          case _ => accountIds
        }

        parsedAccountIds.map(accountId => {

          roleRateLimiter.acquire(1)

          val userRoleInput = UserRoleInput(
            id = None,
            name = roleName,
            description = None,
            accountId = accountId,
            permissions = fieldToValues.values.toSeq,
            creator = creator
          )

          userRoleClient.insertUserRole(userRoleInput) map {
            case Right(created) =>
              logger.info(s"Creating role success $userRoleInput")
              CreateRolesResponse(accountId, roleName, created)
            case Left(errorResponse: ErrorResponse) =>
              logger.info(s"Creating role failure $userRoleInput, error $errorResponse")
              CreateRolesResponse(accountId, roleName, created = false, Some(errorResponse))
          }
        })
      }).toList

      Future.sequence(createRolesFuture).map(Right(_))

    } else {
      Future.successful(Left(ErrorResponseFactory.get(ExceptionCodes.InvalidFile)))
    }
  }

  def combineRoles(baseRoles: Seq[RolesInputDetails], toBeAdded: Seq[RolesInputDetails]): Seq[RolesInputDetails] = {

    val baseRolesTuple = baseRoles.map(role2 => (role2.roleType, role2.roleId))

    baseRoles ++ toBeAdded.filterNot(role1 => {
      baseRolesTuple.contains((role1.roleType, role1.roleId))
    })

  }

  override def createOrEditUsers(creator: Creator, csvInputStream: InputStream, bw: Option[BufferedWriter]=None): Either[ErrorResponse, List[CreateEditUserResponse]] = {

      val accountToRoles = mutable.Map.empty[Long, Seq[UserRoleResult]]
      val roleNameList = mutable.Set.empty[String]
      val subAccountsOfParent = mutable.Map.empty[Long, Seq[SubAccountV2]]
      val userAccounts = mutable.Map.empty[Long, Seq[Long]]
      val csvParser = CSVParser.parse(csvInputStream, StandardCharsets.UTF_8, CSVFormat.DEFAULT.withHeader())
      val csvHeaders = csvParser.getHeaderNames.asScala.toSet

      if (csvHeaders.subsetOf(createUsersMandatoryHeader)) {
        bw match {
          case Some(bufferedWriter: BufferedWriter) =>
            try{
              bufferedWriter.write(fieldsToBeExported.mkString(",") + "\n")
            } catch {
              case e: Exception =>
                logger.error("Error occurred while writing to output CSV", e)
            }
          case None => None
        }
        val createUsersSequence = csvParser.iterator().asScala.map(csvRecord => {
          val email = csvRecord.get(createUserEmailHeader).trim
          val firstName = csvRecord.get(createUserFirstNameHeader).trim
          val lastName = csvRecord.get(createUserLastNameHeader).trim
          val phoneNumber = csvRecord.get(createUserContactHeader).trim
          val accounts = csvRecord.get(createUserAccountsHeader)
          val roles = csvRecord.get(createUserRoleHeader)
          val propagateUsers = csvRecord.get(createUserPropagateAccessHeader).trim
          val propagateUsersBoolean = propagateUsers.toLowerCase match {
            case "true" => true
            case _ => false
          }
          val parsedAccountNames = Try {
            Some(accounts.split(",").filter(_.nonEmpty).map(_.trim).toSet)
          } match {
            case Success(Some(value)) => value
            case _ =>
              logger.error(s"Account Names $accounts not proper for the record ${csvRecord.getRecordNumber}, email $email")
              Set.empty[String]
          }
          val creatorAccountId =  creator.accountId
          val parentAccountRoles: Seq[UserRoleResult] = Await.result(Retry(userRoleClient.getUserRolesByAccountId(creatorAccountId, creator.userId, creatorAccountId)), 10 minutes) match {
            case Right(rolesResult) => rolesResult
            case Left(e) =>
              logger.error(s"Error ${e.message} while fetching roles for account ${creatorAccountId}")
              Seq.empty[UserRoleResult]
          }
          val parsedRoleNames = Try {
            Some(roles.split(",").filter(_.nonEmpty).map(_.trim).toSeq)
          } match {
            case Success(Some(value)) => value
            case _ =>
              logger.error(s"Roles $roles not proper for the record ${csvRecord.getRecordNumber}, email $email")
              Seq.empty[String]
          }
          val parentCustomRoles: Seq[RolesInputDetails] = parsedRoleNames.flatMap(roleName => {
            parentAccountRoles.find(par => par.name.equals(roleName) && par.roleType == SystemDefinedRoles.CUSTOMROLE.roleType)
              .flatMap(par1 => {
                roleNameList += par1.name
                Some(RolesInputDetails(par1.roleType, par1.id))
              })
          })
          val defaultExtension = "+1-"
          val phoneArr = phoneNumber.split('-').filter(_.nonEmpty).map(_.trim)
          val phone = phoneArr match {
            case p if p.length > 1  => phoneNumber
            case p if p.length == 1 && p(0).length == 10 => defaultExtension + p(0)
            case _ => ""
          }
          if (!CommonValidations.isValidPhoneNumber(phone)) {
            val errorMsg = "User could not be created due to following invalid values: Phone Number"
            val res = CreateEditUserResponse(email, firstName, lastName, phoneNumber, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : $phoneNumber")
            writeToOutputCSVFile(res, bw)
            res
          } else if(!validateNames(firstName)){
            val errorMsg = "User could not be created due to following invalid values: First Name"
            val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : $firstName")
            writeToOutputCSVFile(res, bw)
            res
          } else if(!validateNames(lastName)){
            val errorMsg = "User could not be created due to following invalid values: Last Name"
            val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : $lastName")
            writeToOutputCSVFile(res, bw)
            res
          } else if (!EmailValidator.getInstance().isValid(email)){
            val errorMsg = "User could not be created due to following invalid values: Email Address"
            val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : $email")
            writeToOutputCSVFile(res, bw)
            res
          } else  if(parsedRoleNames.isEmpty){
            val errorMsg = ErrorResponseFactory.get(ExceptionCodes.InvalidRoleNames).message
            val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : ${parsedRoleNames.mkString(",")}")
            writeToOutputCSVFile(res, bw)
            res
          } else if (parsedAccountNames.isEmpty) {
            val errorMsg = ErrorResponseFactory.get(ExceptionCodes.InvalidAccountNames).message
            val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false,propagateUsersBoolean.toString, DateTime.now(), Some(errorMsg))
            logger.error(s"$errorMsg : ${parsedAccountNames.mkString(",")}")
            writeToOutputCSVFile(res, bw)
            res
          }
          else {
            val accountIdNames = getAccountsByName(parsedAccountNames.mkString(","), creatorAccountId)
            accountIdNames match {
              case Left(errorResponse: ErrorResponse) =>
                logger.error(s"Error while fetching account names API for accounts ${parsedAccountNames}: ${errorResponse.message}")
                val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false, propagateUsersBoolean.toString, DateTime.now(), Some(s"User could not be created due to internal error retrieving Account ID"))
                writeToOutputCSVFile(res, bw)
                res

              case Right(parsedAccountIdsMap) =>
                val parsedAccountIds = parsedAccountIdsMap.map(_.id)
                val accountNames = parsedAccountIdsMap.map(_.name)
                if (parsedAccountIds.isEmpty) {
                  val errorMessage = s"User could not be created due to invalid Account name ${parsedAccountNames.mkString(",")}"
                  logger.error(errorMessage)
                  val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false, propagateUsersBoolean.toString, DateTime.now(), Some(errorMessage))
                  writeToOutputCSVFile(res, bw)
                  res
                }
                else {
                  var partialError = ""
                  if (parsedAccountIds.size < parsedAccountNames.size) {
                    val accountsNotFound = parsedAccountNames.filter(accountName => !accountNames.contains(accountName))
                    partialError = partialError + s"User created, however, could not be given access to invalid account(s): ${accountsNotFound.mkString(", ")} | "
                    logger.error(s"Error $partialError")
                  }

                    parsedAccountIdsMap.filter(accountDetails => !accountToRoles.contains(accountDetails.id)).map(accountDetails => {
                      Await.result(Retry(userRoleClient.getUserRolesByAccountId(accountDetails.id, creator.userId, accountDetails.id)), 10 minutes) match {
                        case Right(rolesResult) =>
                          accountToRoles += (accountDetails.id -> rolesResult.filter(rr => isCurrentAccountRole(rr, accountDetails.id)))
                        case Left(errorResponse) =>
                          logger.error(s"Error ${errorResponse.message} while fetching roles for account ${accountDetails.id}")
                          partialError = partialError + s"Following roles not assigned: ${parsedRoleNames.mkString(", ")} for ${accountDetails.name} | "
                          accountToRoles += (accountDetails.id -> Seq.empty)
                      }
                    })
                  //create user and do account association or edit user
                  val newAccountWithRolesInput = parsedAccountIdsMap.flatMap(accountDetails => {
                    val rolesNotPresent = mutable.Set.empty[String]
                    val rolesList = parsedRoleNames.flatMap(roleName => {
                      val selectedRole = accountToRoles(accountDetails.id).find(_.name.equals(roleName))
                      selectedRole match {
                        case Some(roleDetails) =>
                          if(roleDetails.roleType != SystemDefinedRoles.ACCOUNTOWNER.roleType && roleDetails.roleType != SystemDefinedRoles.CASE_ANALYST.roleType && roleDetails.roleType != SystemDefinedRoles.CASE_SUPERVISOR.roleType){
                            roleNameList += roleDetails.name
                            Some(RolesInputDetails(roleDetails.roleType,roleDetails.id))
                          } else if(roleDetails.roleType == SystemDefinedRoles.CASE_ANALYST.roleType || roleDetails.roleType == SystemDefinedRoles.CASE_SUPERVISOR.roleType){
                            isPermissionAvailable(accountDetails.id, BusinessUserRoles.WATCHLIST_CASE_MANAGEMENT.id) match {
                              case Right(isCaseManagementProvisioned) =>
                                if(isCaseManagementProvisioned){
                                  roleNameList += roleDetails.name
                                  Some(RolesInputDetails(roleDetails.roleType,roleDetails.id))
                                } else {
                                  rolesNotPresent += roleName
                                  None
                                }
                              case Left(errorResponse: ErrorResponse) =>
                                logger.error(s"Not able to fetch WATCHLIST_CASE_MANAGEMENT permission for account ${accountDetails.id}: ${errorResponse.message}")
                                rolesNotPresent += roleName
                                None
                            }
                          }
                          else {
                            rolesNotPresent += roleName
                            None
                          }
                        case None =>
                          rolesNotPresent += roleName
                          None
                      }

                    })

                    val allRoleList = rolesList ++ parentCustomRoles
                    if (allRoleList.nonEmpty) {
                      if (!parsedRoleNames.forall(roleNameList.contains)) {
                        partialError = partialError + s"Following roles not assigned due to invalid role name: ${rolesNotPresent.mkString(", ")} for ${accountDetails.name} | "
                        logger.error(s"Error: Following roles ${rolesNotPresent} are not assigned to account ${accountDetails.name} for email ${email}")
                      }
                      Some(AccountWithRolesInput(accountDetails.id, allRoleList))
                    } else {
                      partialError = partialError + s"Following roles not assigned due to invalid role name: ${rolesNotPresent.mkString(", ")} for ${accountDetails.name} | "
                      logger.error(s"Error: Following roles ${rolesNotPresent} are not assigned to account ${accountDetails.name} for email ${email}")
                      None
                    }
                  })
                  if (newAccountWithRolesInput.isEmpty) {
                    if(partialError.isEmpty){
                      partialError = s"Unable to create user ${email} for account ${parsedAccountNames.mkString(",")} with roles ${parsedRoleNames.mkString(",")}"
                    }
                    logger.error(s"Error ${partialError}")
                    val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false, propagateUsersBoolean.toString, DateTime.now(), Some(s"${partialError}"))
                    writeToOutputCSVFile(res, bw)
                    res
                  } else {
                    if (propagateUsersBoolean) {
                      if (!userAccounts.contains(creator.userId)){
                        getAssociatedAccounts(creator.userId) match {
                          case Right(accountsProvisionedForUser) =>
                            userAccounts += (creator.userId -> accountsProvisionedForUser.map(_.id))
                          case Left(errorResponse: ErrorResponse) =>
                            logger.error(s"Error While Fetching Accounts Provisioned For User ${creator.userId}: ${errorResponse.message}")
                            userAccounts += (creator.userId -> Seq.empty[Long])
                        }
                      }

                      val subAccountsToBePropagated = parsedAccountIds.flatMap(accountId => {
                        if (!subAccountsOfParent.contains(accountId)) {
                          getSubaccounts(accountId, creator.userId) match {
                            case Right(subAccountsList) =>
                              if(subAccountsList.nonEmpty){
                              subAccountsOfParent += (accountId -> subAccountsList)
                              getSubAccountRoles(subAccountsList, parsedAccountIds, accountToRoles, creator, parsedRoleNames, accountId, userAccounts, parentCustomRoles) match {
                                case accountRoles =>
                                  accountRoles
                              }
                              } else {
                                Seq.empty
                              }
                            case Left(errorResponse: ErrorResponse) =>
                              logger.error(s"Error: while fetching sub accounts for account $accountId, ${errorResponse.message}")
                              Seq.empty
                          }
                        } else {
                          val subAccounts = subAccountsOfParent.get(accountId)
                          val constructSubAccountsWithRoles = subAccounts match {
                            case Some(subAccountsList) =>
                              getSubAccountRoles(subAccountsList, parsedAccountIds, accountToRoles, creator, parsedRoleNames, accountId, userAccounts, parentCustomRoles) match {
                                case accountRoles =>
                                  accountRoles
                              }
                            case None => Seq.empty
                          }
                          constructSubAccountsWithRoles
                        }
                      })
                        val totalAccountsToBePropagated = newAccountWithRolesInput ++ subAccountsToBePropagated
                        createOrUpdateUserAPICall(email, firstName, lastName, phone, accounts, propagateUsersBoolean.toString, roles, totalAccountsToBePropagated, partialError, creator, bw)
                    } else {
                      val totalAccountsToBePropagated = newAccountWithRolesInput
                      createOrUpdateUserAPICall(email, firstName, lastName, phone, accounts, propagateUsersBoolean.toString, roles, totalAccountsToBePropagated, partialError, creator, bw)
                    }
                  }

                }
            }
          }
        }).toList

        Right(createUsersSequence)

      } else {
        val errorMsg = s"Invalid CSV file provided. The CSV file must contain headers ${createUsersMandatoryHeader.mkString(",")}."
        logger.error(errorMsg)
        Left(ErrorResponse(400, errorMsg))
      }
    }

  private def getSubAccountRoles(accounts: Seq[SubAccountV2], parsedAccountIds: Seq[Long], accountToRoles : mutable.Map[Long, Seq[UserRoleResult]], creator: Creator, parsedRoleNames: Seq[String], parentAccountID : Long, userAccounts : mutable.Map[Long, Seq[Long]], parentCustomRoles : Seq[RolesInputDetails]): Seq[AccountWithRolesInput] = {
    val validSubAccounts = accounts.filter(account => !parsedAccountIds.contains(account.id)).map(_.id)
    val accountsProvisionedForUser = userAccounts.get(creator.userId) match {
      case Some(accountsForUser) => accountsForUser.filter(id => id != parentAccountID)
      case None => Seq.empty[Long]
    }
    val validSubAccountsForUser = validSubAccounts.filter(subAccID => accountsProvisionedForUser.contains(subAccID))
    val subAccountsRolesSeq = validSubAccountsForUser.map(subAccountId => {
      if(!accountToRoles.contains(subAccountId)) {
        val parentRoles = accountToRoles.get(parentAccountID)
        parentRoles match {
          case Some(roles) =>
            accountToRoles += (subAccountId -> roles)
            (subAccountId, roles)
          case None =>
            accountToRoles += (subAccountId -> Seq.empty)
            (subAccountId, Seq.empty)
        }
      } else {
        val rolesForSubAccount = accountToRoles.get(subAccountId)
        rolesForSubAccount match {
          case Some(roles) => (subAccountId, roles)
          case None => (subAccountId, Seq.empty)
        }
      }
    })
    subAccountsRolesSeq.flatMap( subAccountsWithRoles => {
        val subAccountRoles = parsedRoleNames.flatMap(roles => {
          val currentSubAccountRoles = subAccountsWithRoles._2
          val subAccountWithRolesInput = currentSubAccountRoles.find(_.name.equals(roles)).filter(role => role.roleType != SystemDefinedRoles.ACCOUNTOWNER.roleType).flatMap(role => {
            if(role.roleType == SystemDefinedRoles.CASE_ANALYST.roleType || role.roleType == SystemDefinedRoles.CASE_SUPERVISOR.roleType){
              isPermissionAvailable(subAccountsWithRoles._1, BusinessUserRoles.WATCHLIST_CASE_MANAGEMENT.id) match {
                case Right(isCaseManagementProvisioned) =>
                  if(isCaseManagementProvisioned){
                    Some(RolesInputDetails(role.roleType, role.id))
                  } else {
                    None
                  }
                case Left(errorResponse: ErrorResponse) =>
                  logger.error(s"Not able to fetch WATCHLIST_CASE_MANAGEMENT permission for account ${subAccountsWithRoles._1}: ${errorResponse.message}")
                  None
              }
            }else{
              Some(RolesInputDetails(role.roleType, role.id))
            }
          })
          subAccountWithRolesInput
        })
      if(subAccountRoles.nonEmpty) {
        Some(AccountWithRolesInput(subAccountsWithRoles._1, subAccountRoles++parentCustomRoles))
      } else {
        None
      }
    })

  }

  private def createOrUpdateUserAPICall(email: String, firstName: String, lastName: String, phone: String, accounts: String, propagateUsers: String, roles: String, totalAccountsToBePropagated: Seq[AccountWithRolesInput], partialError: String, creator: Creator, bw: Option[BufferedWriter]=None): CreateEditUserResponse = {
    var errorMessage = partialError
    val createBusinessUserInput = CreateBusinessUserInput(creator.accountId, email, firstName, lastName, phone, totalAccountsToBePropagated, creator)
    createBusinessUserWithAssociations(createBusinessUserInput) match {
      case Right(userActivationDetails: UserActivationDetails) =>
        val mailService = mailNotificationService match {
          case Some(mailNotificationService: MailNotificationService) =>
            if(userActivationDetails.activationCode.isDefined) {
              isNonSAMLAccountPresent(createBusinessUserInput.accountsWithRoles.map(_.accountId)) match {
                case true =>
                  isPermissionAvailable(creator.accountId, BusinessUserRoles.DashboardV3.id) match {
                    case Right(isV3) => Await.result(Retry(mailNotificationService.sendSetPasswordEmail(userActivationDetails.firstname, userActivationDetails.surname, userActivationDetails.email, userActivationDetails.activationCode.getOrElse(""), isV3, creator.accountId)), 10 minutes) match {
                      case Right(sent) =>
                        if (!sent) {
                          logger.error(s"Not able to send email to ${userActivationDetails.email}")
                          errorMessage = errorMessage + s"Not able to send email to ${userActivationDetails.email}. |"
                        }
                        sent
                      case Left(ex: Throwable) =>
                        logger.error(s"Not able to send email to ${userActivationDetails.email} due to ${ex.getMessage} |")
                        errorMessage = errorMessage + s"Not able to send email to ${userActivationDetails.email}. |"
                        false
                    }
                    case Left(errorResponse: ErrorResponse) =>
                      logger.error(s"Error while fetching dashboard v3 permission for user ${creator.accountId}: ${errorResponse.message}")
                      errorMessage = errorMessage + s"Error while fetching dashboard v3 permission for user ${creator.accountId}. |"
                      false
                  }
                case false =>
                  logger.error(s"No non SAML accounts found for ${userActivationDetails.email}")
                  errorMessage = errorMessage + s"Non non SAML accounts found for ${userActivationDetails.email} |"
                  false
              }
            } else {
              logger.error("User Activation Details Not Present")
              true
            }
          case None => true
        }
        mailService match {
              case _ =>
                if (errorMessage.isEmpty) {
                  val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = true, propagateUsers, DateTime.now())
                  writeToOutputCSVFile(res, bw)
                  res
                } else {
                  val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = true, propagateUsers, DateTime.now(), Some(errorMessage))
                  writeToOutputCSVFile(res, bw)
                  res
                }
            }
      case Left(errorResponse) =>
        logger.info(s"Creating user failure $createBusinessUserInput, error ${errorResponse.message}")
        errorMessage = errorMessage + "User Creation Failed - Internal Server Error |"
        val res = CreateEditUserResponse(email, firstName, lastName, phone, accounts, roles, added = false, propagateUsers, DateTime.now(), Some(errorMessage))
        writeToOutputCSVFile(res, bw)
        res
    }
  }

  def validateNames(string: String, stringArr: String*): Boolean = {
    (Seq(string) ++ stringArr).forall(s => s.nonEmpty && Option(s).forall(_.matches("[a-zA-Z0-9-_'\\(\\)\\+\\ ]*")))
  }

  private def isCurrentAccountRole(userRoleResult: UserRoleResult, accountId: Long): Boolean = {
    userRoleResult.byAccountId match {
      case Some(byAccountId) => byAccountId == accountId
      case None => true
    }
  }

  def isNonSAMLAccountPresent(accountIds: Seq[Long]): Boolean = {
    if (accountIds.isEmpty)
      false
    else {
      isPermissionAvailable(accountIds.head, BusinessUserRoles.SAML_2_0.id) match {
        case Right(available) if available =>
          isNonSAMLAccountPresent(accountIds.tail)
        case Left(_) => true
      }
    }
  }

  def writeToOutputCSVFile(res: CreateEditUserResponse, bw: Option[BufferedWriter]=None) = {
    bw match {
      case Some(bufferedWriter: BufferedWriter) =>
        val sb = new StringBuilder
        try {
          val csvRecord = fieldsToBeExported.map(field => {
            if (field == "First Name") res.firstName.mkString("\"", "", "\"")
            else if (field == "Last Name") res.lastName.mkString("\"", "", "\"")
            else if (field == "Email Address") res.email
            else if (field == "Phone Number") res.phone
            else if (field == "Accounts") {
              res.accounts
            }.mkString("\"", "", "\"")
            else if (field == "Roles") res.roleName.mkString("\"", "", "\"")
            else if (field == "Propagate Access to All Sub-Accounts")
              if (res.propagateAccess.equals("true")) "true"
              else "false"
            else if (field == "Status")
              if (res.added)
                if (res.errorResponse.isDefined)
                  "Partial Success"
                else
                  "Success"
              else "Failure"
            else if (field == "Processing Timestamp") res.timeStamp
            else if (field == "Error")
              if (res.errorResponse.isDefined) res.errorResponse.mkString("\"", "", "\"")
              else ""
            else ""
          })
          sb.append(csvRecord.mkString(",") + "\n")
          bufferedWriter.write(sb.mkString)
        } catch {
          case e: Exception =>
            logger.error("Error occurred while writing to output CSV", e)
        }
      case None => None
    }
  }

  private def getUserId(email: String): Either[ErrorResponse, Long] = {
    Await.result(Retry(businessUserManagementClient.getUserId(email)), 10 minutes)
  }

  private def createBusinessUserWithAssociations(createBusinessUserInput: CreateBusinessUserInput): Either[ErrorResponse, UserActivationDetails] = {
    Await.result(Retry(businessUserManagementClient.createBusinessUserWithAssociations(createBusinessUserInput)), 10 minutes)._2
  }

  private def getUserWithRolesAndAssociations(userId: Long, creator: Creator): Either[ErrorResponse, DashboardUserWithAssociations] = {
    Await.result(Retry(businessUserManagementClient.getUserWithRolesAndAssociations(userId, creator)), 10 minutes)
  }

  private def updateBusinessUserWithAssociations(updateBusinessUserInput: UpdateBusinessUserInput): Either[ErrorResponse, Boolean] = {
    Await.result(Retry(businessUserManagementClient.updateBusinessUserWithAssociations(updateBusinessUserInput)), 10 minutes)._2
  }

  private def getAccountsByName(accountNames: String, parentId: Long): Either[ErrorResponse, Seq[AccountIdName]] = {
    Await.result(Retry(businessUserManagementClient.getAccountsByName(accountNames, parentId)), 10 minutes)
  }

  private def getAssociatedAccounts(userId: Long): Either[ErrorResponse, Seq[AccountIdName]] = {
    Await.result(Retry(businessUserManagementClient.getAssociatedAccounts(userId)), 10 minutes)
  }

  private def getSubaccounts(accountId: Long, userId: Long): Either[ErrorResponse, Seq[SubAccountV2]] = {
    accountHierarchyClient match {
      case Some(accountHierarchyClient:  AccountHierarchyClient) =>
        Await.result(Retry(accountHierarchyClient.list(accountId, userId)), 10 minutes) match {
          case Right(sa) => Right(sa.find(_.accountId == accountId).map { sa0: AccountInfoV2WithIndustry =>
            val cnt = sa0.hierarchyPath.count(_ == '/')
            sa.flatMap(a =>
              if (a.hierarchyPath.split("/").length >= cnt + 1 && a.accountStatus)
                Some(SubAccountV2(a.accountId, a.accountName, a.industry, a.state && a.accountStatus, Some(a.hierarchyPath)))
              else None
            )
          }.getOrElse(Seq.empty[SubAccountV2]))
          case Left(errorResponse: ErrorResponse) =>
            logger.error(s"Error while fetching sub accounts for account $accountId: ${errorResponse.message}")
            Right(Seq.empty[SubAccountV2])
        }
      case None => Left(ErrorResponse(400, "Account Hierarchy Client Client Not Found"))
    }
  }

  private def isPermissionAvailable(accountId: Long, permission: Int): Either[ErrorResponse, Boolean] = {
    businessUserRoleClient match {
      case Some(roleClient: BusinessUserRoleClient) => Await.result(Retry(roleClient.isPermissionAvailable(accountId, permission)), 10 minutes)
      case None => Left(ErrorResponse(400, "Business Role Client Not Found"))
    }
  }
}
