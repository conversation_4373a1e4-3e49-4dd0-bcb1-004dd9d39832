package me.socure.account.sponsorbank

import dispatch.{Http, url}
import me.socure.account.client.GenericClient
import me.socure.model.ErrorResponse
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}
import org.slf4j.{Logger, LoggerFactory}

import scala.concurrent.{ExecutionContext, Future}
import me.socure.util.JsonEnrichments._

import java.nio.charset.Charset

class SponsorBankClientImpl (http: Http, endpoint: String)(implicit ec: ExecutionContext) extends SponsorBankClient {

  val logger: Logger = LoggerFactory.getLogger(getClass)
  val urlPrefix = "sponsorbank"

  override def getNonSponsorBankPrograms(): Future[Either[ErrorResponse, Seq[AccountIdName]]] = {
    val request = (url(endpoint) / urlPrefix / "non-linked"  ).GET
    GenericClient.callApi[Seq[AccountIdName]](http, "/sponsorbank/non-linked", "GET", request)
  }

  override def getSponsorBank(programId:  Long): Future[scala.Either[ErrorResponse, AccountIdName]] = {
    val request = (url(endpoint) / urlPrefix / "program" / programId ).GET
    GenericClient.callApi[AccountIdName](http, "/sponsorbank/program", "GET", request)
  }

  override def getLinkedPrograms(sponsorBankId:  Long): Future[scala.Either[ErrorResponse, Seq[SponsorBankProgram]]] = {
    val request = (url(endpoint) / urlPrefix / "linked" / "programs" / sponsorBankId ).GET
    GenericClient.callApi[Seq[SponsorBankProgram]](http, "/sponsorbank/linked/programs", "GET", request)
  }

  override def linkSponsorBankProgram(sponsorBankProgramLinkRequest:  SponsorBankProgramLinkRequest): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / "link"  )
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << sponsorBankProgramLinkRequest.encodeJson()
    GenericClient.callApi[Boolean](http, "/sponsorbank/link", "POST", request)
  }

  override def unlinkSponsorBankProgram(sponsorBankProgramLinkRequest:  SponsorBankProgramLinkRequest): Future[scala.Either[ErrorResponse, Boolean]] = {
    val request = (url(endpoint) / urlPrefix / "unlink"  )
      .setContentType("application/json", Charset.forName("UTF-8"))
      .POST << sponsorBankProgramLinkRequest.encodeJson()
    GenericClient.callApi[Boolean](http, "/sponsorbank/unlink", "POST", request)  }
}
