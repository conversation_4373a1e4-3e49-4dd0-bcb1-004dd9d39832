package me.socure.account.sponsorbank

import me.socure.model.ErrorResponse
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}

import scala.concurrent.Future

trait SponsorBankClient {
    def getNonSponsorBankPrograms(): Future[Either[ErrorResponse, Seq[AccountIdName]]]
    def getSponsorBank(programId: Long): Future[Either[ErrorResponse, AccountIdName]]
    def getLinkedPrograms(sponsorBankId: Long): Future[Either[ErrorResponse, Seq[SponsorBankProgram]]]
    def linkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Future[Either[ErrorResponse, Boolean]]
    def unlinkSponsorBankProgram(sponsorBankProgramLinkRequest: SponsorBankProgramLinkRequest): Future[Either[ErrorResponse, Boolean]]
}
