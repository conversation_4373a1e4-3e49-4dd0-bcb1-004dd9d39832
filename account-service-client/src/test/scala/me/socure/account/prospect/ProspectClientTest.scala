package me.socure.account.prospect

import org.scalatest.{EitherVal<PERSON>, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.clock.FakeClock
import me.socure.model.prospect.{ProspectExclusionDetail, ProspectExclusionInput, ProspectInclusionDetail, ProspectInclusionInput}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}

import me.socure.util.JsonEnrichments._
import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}

class ProspectClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new FakeClock(new DateTime("2024-04-23").withZone(DateTimeZone.UTC).getMillis)

  test("Get Exclusion list - success") {
    val expected = Seq(ProspectExclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getExclusionList()) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Exclusion list - fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getExclusionList()) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get Exclusion list total count - success") {
    val expected = 10
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion/total_count") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getExclusionListTotalCount) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Exclusion list total count- fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/exclusion/total_count") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getExclusionListTotalCount) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Search Exclusion list - success") {
    val expected = Seq(ProspectExclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getExclusionList(search = Some("test"))) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Insert Exclusion detail - success") {
    val input = ProspectExclusionInput(Option.empty, "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertExclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Insert Exclusion detail - fail") {
    val input = ProspectExclusionInput(Option.empty, "<EMAIL>", "test_user")
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertExclusionDetail(input)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Update Exclusion detail - success") {
    val input =  ProspectExclusionInput(Some(1), "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertExclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Update Exclusion detail - fail") {
    val input = ProspectExclusionInput(Some(2), "<EMAIL>", "test_user")
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertExclusionDetail(input)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Update Non Existing Exclusion detail should create new record") {
    val input = ProspectExclusionInput(Some(4), "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertExclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Delete Exclusion detail - success") {
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion/1") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteExclusionDetail(1)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Delete Exclusion detail - fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/exclusion/2") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteExclusionDetail(2)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Delete Non Existing Exclusion detail") {
    val expected = false
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/exclusion/2") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteExclusionDetail(2)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Inclusion list - success") {
    val expected = Seq(ProspectInclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getInclusionList()) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Inclusion list - fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getInclusionList()) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get Inclusion list total count- success") {
    val expected = 10
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion/total_count") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getInclusionListTotalCount) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Inclusion list total count - fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/inclusion/total_count") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getInclusionListTotalCount) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Search Inclusion list - success") {
    val expected = Seq(ProspectInclusionDetail(1, "<EMAIL>","created user",clock.now(),"updated user",clock.now()))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.getInclusionList(search = Some("test"))) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Insert Inclusion detail - success") {
    val input = ProspectInclusionInput(Option.empty, "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertInclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Insert Inclusion detail - fail") {
    val input = ProspectInclusionInput(Option.empty, "<EMAIL>", "test_user")
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertInclusionDetail(input)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Update Inclusion detail - success") {
    val input = ProspectInclusionInput(Some(1), "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertInclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Update Inclusion detail - fail") {
    val input = ProspectInclusionInput(Some(2), "<EMAIL>", "test_user")
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertInclusionDetail(input)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Update Non Existing Inclusion detail should create new record") {
    val input = ProspectInclusionInput(Some(1), "<EMAIL>", "test_user")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.upsertInclusionDetail(input)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Delete Inclusion detail - success") {
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion/1") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteInclusionDetail(1)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Delete Inclusion detail - fail") {
    val expected = ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/prospect/inclusion/1") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteInclusionDetail(1)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Delete No Available Inclusion detail") {
    val expected = false
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/prospect/inclusion/2") { port =>
      val client: ProspectClient = new ProspectClientImpl(http, s"$host:$port")
      whenReady(client.deleteInclusionDetail(2)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

}
