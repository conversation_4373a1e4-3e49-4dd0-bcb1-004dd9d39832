package me.socure.account.client.superadmin

import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{AccountDetails, AccountIdName}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sunder<PERSON> on 6/17/16.
  */
class LeaderboardClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host: String = "http://localhost"

  test("get internal account should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getInternalAccountId) { res =>
        res should be('left)
      }
    }
  }

  test("get internal account should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, List(1))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getInternalAccountId) { res =>
        res should be('right)
      }
    }
  }

  test("get account name should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountNameById) { res =>
        res should be('left)
      }
    }
  }

  test("get account name should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, Map(1l -> AccountIdName(1l, "account")))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountNameById) { res =>
        res should be('right)
      }
    }
  }

  test("get account by apikey should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountByApiKey("apikey")) { res =>
        res should be('left)
      }
    }
  }

  test("get account by apikey should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, AccountDetails(22, "account", "<EMAIL>"))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountByApiKey("apikey")) { res =>
        res should be('right)
      }
    }
  }

  test("get account by industry should throw AccountNotFound ") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountsByIndustry("invalidsector")) { res =>
        res should be('left)
      }
    }
  }

  test("get account by industry should return account") {
    val expectedRes = Response(ResponseStatus.Ok, Set(AccountDetails(22, "account", "<EMAIL>")))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/leaderboard/*") { port =>
      val client: LeaderboardClient = new LeaderboardClientImpl(s"http://localhost:$port/leaderboard")
      whenReady(client.getAccountsByIndustry("sector")) { res =>
        res should be('right)
        res.right.value shouldBe(expectedRes.data)
      }
    }
  }

}