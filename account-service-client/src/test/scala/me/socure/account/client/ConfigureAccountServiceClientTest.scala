package me.socure.account.client

import me.socure.account.client.account.{ConfigureAccountServiceClient, ConfigureAccountServiceClientImpl}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{SubAccountCreationRequest, SubAccountUserRequest}
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.ExecutionContext

class ConfigureAccountServiceClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {

  implicit val ec = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(120, Seconds), interval = Span(500, Millis))
  val http = new NonSecuredHttpFactory().getHttpClient()
  val subAccountCreationRequest = SubAccountCreationRequest("99-16ca6193-4149-456b-ae00-00fdad2437c6", "acnt_name", "101-205",
    user = Some(SubAccountUserRequest(Some("first_name"), Some("last_name"), "<EMAIL>", Some("contact"))),
    modules = Some(Set("rid-gnKpF0yue2")))

  test("create sub account should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/configure/account/create") { port =>
      val client: ConfigureAccountServiceClient = new ConfigureAccountServiceClientImpl(http, s"http://localhost:$port")
      whenReady(client.createSubAccount(subAccountCreationRequest)) { res =>
        res.fold(_.code shouldBe expectedRes.data.code, _ => fail)
      }
    }
  }

  test("create sub account success") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/configure/account/create") { port =>
      val client: ConfigureAccountServiceClient = new ConfigureAccountServiceClientImpl(http, s"http://localhost:$port")
      whenReady(client.createSubAccount(subAccountCreationRequest)) { res =>
        res.fold(_ => fail, _ shouldBe true)
      }
    }
  }
}
