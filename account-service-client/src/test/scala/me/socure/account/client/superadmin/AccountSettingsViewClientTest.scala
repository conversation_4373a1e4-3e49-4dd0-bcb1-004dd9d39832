package me.socure.account.client.superadmin

import me.socure.common.clock.RealClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountSettings
import me.socure.model.account.watchlist.{CAWatchlistPreferenceForAccount, CAWatchlistPreferenceView}
import me.socure.model.dv.{DVConfigurationDetails, DVConfigurationsForAccount}
import me.socure.model.kyc.{KycNationalIdMatchLogic, KycPreferencesForAccount, KycPreferencesView}
import me.socure.model.subscription.{SubscriptionChannelRegistryForAccount, SubscriptionChannelRegistryView}
import me.socure.model.{Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountSettingsViewClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new RealClock

  test("Should list Account Settings") {
    val expectedSCR = List(
      SubscriptionChannelRegistryForAccount(2,List(SubscriptionChannelRegistryView(4941,2L,"<EMAIL>",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1158,"Primary","Email",clock.now,clock.now))),
      SubscriptionChannelRegistryForAccount(1, List(
        SubscriptionChannelRegistryView(9,1L,"https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","DV",None,None,1157,"Primary","Webhook",clock.now, clock.now),
        SubscriptionChannelRegistryView(10,1,"https://webhook.site/3d2db530-7b4a-4ce0-b615-a2a13f6bec4b",Map("CERTIFICATE" -> "test-certificate1"),"ENABLE","WL",None,Some("Parent Account 1234567"),1157,"Primary","Webhook",clock.now,clock.now))))

    val expectedKYC = List(
      KycPreferencesForAccount(1,KycPreferencesView(Some(false),None,true, KycNationalIdMatchLogic.partial.toString, "default")),
      KycPreferencesForAccount(2,KycPreferencesView(Some(true),None,true, KycNationalIdMatchLogic.partial.toString, "default"))
    )

    val expectedWL = List(
      CAWatchlistPreferenceForAccount(1, CAWatchlistPreferenceView(Some(false),1,Some(true),Some("one_year_radius_yyyy_mm_dd"),false,false,0.5,10,Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,Some(false),Some(false))),
      CAWatchlistPreferenceForAccount(2,CAWatchlistPreferenceView(Some(false),2,Some(true),Some("one_year_radius_yyyy_mm_dd"),false,false,0.5,10,Set("warning", "sanction", "fitness-probity", "pep"),Some(Set("warning", "sanction", "fitness-probity", "pep")),Some(Set()),None,Some(false),Some(false))))

    val expectedDV = List(DVConfigurationsForAccount(2,List(DVConfigurationDetails("Minimum Age","test1","Review"),
        DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
        DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
        DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
        DVConfigurationDetails("FTB Matching - Dates","12","Accept"),
        DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
        DVConfigurationDetails("Strategy","lenient","NoAction"),
        DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"))),
        DVConfigurationsForAccount(1,List(DVConfigurationDetails("Document Expiration Grace - Past","0","NoAction"),
          DVConfigurationDetails("FTB Matching - Non Dates","75","Reject"),
          DVConfigurationDetails("FirstNameMatchWithNickNameDB","0","Accept"),
          DVConfigurationDetails("Document Expiration Grace - Future","0","NoAction"),
          DVConfigurationDetails("Strategy","lenient","NoAction"),
          DVConfigurationDetails("IPVsExtractedMatchingNonDates","70","Accept"),
          DVConfigurationDetails("IPVsExtractedMatchingDates","0","Accept"),
          DVConfigurationDetails("Minimum Age","18","Reject"),
          DVConfigurationDetails("FTB Matching - Dates","12","Accept"))))
    val expected = AccountSettings(kyc = expectedKYC, wl = expectedWL, webhook = expectedSCR, dv = expectedDV)
    val expectedRes = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/view/settings/account/1") { port =>
      val client: AccountSettingsViewClient = new AccountSettingsViewClientImpl(http, s"$host:$port")
      whenReady(client.getAccountSettings(1L)) { res =>
        res.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

}
