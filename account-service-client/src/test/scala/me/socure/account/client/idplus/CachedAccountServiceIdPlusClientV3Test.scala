package me.socure.account.client.idplus

import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformation, AccountInformationV3, AccountInformationValueGenerator}
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import java.util.concurrent.{Executors, ScheduledThreadPoolExecutor, TimeoutException}
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.language.postfixOps

/**
  * Created by gurubala.d on 1/27/2022
  */
class CachedAccountServiceIdPlusClientV3Test extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  private implicit val ec: ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
  private val realClient = mock[AccountServiceIdPlusClientImplV3]
  private val realClientV2 = mock[AccountServiceIdPlusV2ClientImpl]
  private val cache = mock[CacheService[String, AccountInformationV3]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)
  val cacheClient = new CachedAccountServiceIdPlusClientV3(realClient, cache, futureTimeout, cacheFetchTimeout = 200 milliseconds)

  before {
    Mockito.reset(realClient, realClientV2, cache)
  }

  test("should not interract with the real client when the cache returns something") {
    Mockito.when(cache.get("v3_key")).thenReturn(Future.successful(Some(AccountInformationValueGenerator.anAccountInformationV3())))

    whenReady(cacheClient.fetchByApiKeyV3("key")) { _ =>
      Mockito.verifyZeroInteractions(realClient)
    }
  }

  test("should interract with the real client whe the cache returns nothing then store to the cache when successful") {
    val value =AccountInformationValueGenerator.anAccountInformationV3()

    Mockito.when(cache.get("v3_key")).thenReturn(Future.successful(None))
    Mockito.when(realClient.fetchByApiKeyV3("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByApiKeyV3("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByApiKeyV3("key")
      Mockito.verify(cache, Mockito.times(1)).put("v3_key", value)
    }
  }

  test("should hit account service when memcached is failed or throws exception") {
    val value =AccountInformationValueGenerator.anAccountInformationV3()
    Mockito.when(cache.get("v3_key")).thenReturn(Future.failed(new TimeoutException("Timeout")))
    Mockito.when(cache.put("v3_key", value)).thenReturn(Future.successful({}))
    Mockito.when(realClient.fetchByApiKeyV3("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByApiKeyV3("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByApiKeyV3("key")
    }

  }

  test("should hit account service when memcached is taking more than 10 ms ") {
    val value =AccountInformationValueGenerator.anAccountInformationV3()

    Mockito.when(cache.get("v3_key")).thenAnswer(new Answer[Future[Option[AccountInformationV3]]] {
      override def answer(invocation: InvocationOnMock): Future[Option[AccountInformationV3]] = {
        Future {
          Thread.sleep(500) //usual elastic cache operation timeout is only 500 ms.
          Some(value)
        }
      }
    })

    Mockito.when(cache.put("v3_key", value)).thenReturn(Future.successful({}))
    Mockito.when(realClient.fetchByApiKeyV3("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByApiKeyV3("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByApiKeyV3("key")
    }

  }

}
