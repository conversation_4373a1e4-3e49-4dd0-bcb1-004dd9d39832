package me.socure.account.client


import java.lang.Long

import me.socure.account.client.superadmin.AccountInfoClient
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.model.account.{AccountDetails, AccountDetailsWithInternalFlag, AccountIdName}
import org.mockito.Mockito._
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}

import scala.concurrent.Future
import scala.util.{Failure, Success}

/**
  * Created by jamesanto on 1/12/17.
  */
class CachedAccountDetailsClientTest extends FreeSpec with Matchers with BeforeAndAfter {
  val accountInfoClient: AccountInfoClient = mock(classOf[AccountInfoClient])
  val initialAccountDetails = Map(
    long2Long(1) -> AccountIdName(1, "acc1"),
    long2Long(2) -> AccountIdName(2, "acc2")
  )
  val newAccountDetails: Map[Long, AccountIdName] = initialAccountDetails + (long2Long(3) -> AccountIdName(3, "acc3"))

  before {
    reset(accountInfoClient)
  }

  "CachedAccountDetailsClient" - {
    "should fetch account details by id" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Right(initialAccountDetails))
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Right(List(1L)))
      cachedAccountNameClient.getById(1) shouldBe Success(AccountDetailsWithInternalFlag(AccountIdName(1, "acc1"), isInternal = false))
      cachedAccountNameClient.getById(2) shouldBe Success(AccountDetailsWithInternalFlag(AccountIdName(2, "acc2"), isInternal = true))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountNotFoundException] shouldBe true
          ex.getCause.getCause.asInstanceOf[AccountNotFoundException].accountId shouldBe 3
        case Success(_) => fail("Expected it to fail")
      }

      verify(accountInfoClient, times(4)).getAllActiveAccountNames
      verify(accountInfoClient, times(4)).getAllNonInternalActiveAccounts

      reset(accountInfoClient)

      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Right(newAccountDetails))
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Right(newAccountDetails.keys.toList.map(Long2long)))
      cachedAccountNameClient.getById(3) shouldBe Success(AccountDetailsWithInternalFlag(AccountIdName(long2Long(3), "acc3"), isInternal = false))
      verify(accountInfoClient, times(1)).getAllActiveAccountNames
      verify(accountInfoClient, times(1)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for all account names with ErrorResponse" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Right(List(1L)))
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Left(ErrorResponseFactory.get(new Exception("test"))))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }
      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for non internal account names with ErrorResponse" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Right(initialAccountDetails))
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Left(ErrorResponseFactory.get(new Exception("test"))))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }

      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for all account names and non internal account names with ErrorResponse" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Left(ErrorResponseFactory.get(new Exception("test"))))
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Left(ErrorResponseFactory.get(new Exception("test"))))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }
      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for all account names" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.failed(new Exception("test"))
      when(accountInfoClient.getAllNonInternalActiveAccounts) thenReturn Future.successful(Right(List(1L)))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }
      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for non internal account names" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.successful(Right(initialAccountDetails))
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.failed(new Exception("test"))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }
      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }

    "should fail when the underlying client fails for both all account names and non internal account names" in {
      val cachedAccountNameClient = new CachedAccountDetailsClient(accountInfoClient)
      when(accountInfoClient.getAllActiveAccountNames) thenReturn Future.failed(new Exception("test"))

      cachedAccountNameClient.getById(3) match {
        case Failure(ex) =>
          ex.getCause.getCause.isInstanceOf[AccountDetailsLoadingException] shouldBe true
        case Success(_) => fail("Expected it to fail")
      }
      verify(accountInfoClient, times(3)).getAllActiveAccountNames
      verify(accountInfoClient, times(3)).getAllNonInternalActiveAccounts
    }
  }
}
