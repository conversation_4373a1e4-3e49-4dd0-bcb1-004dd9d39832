package me.socure.account.client.encryption

import java.net.InetSocketAddress
import _root_.me.socure.scalacache.elasticache.cluster.client.{JavaSerializationCodec, MemcachedCache}
import com.amazonaws.regions.Regions
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.common.storage.scalacache.ScalaCacheStorage
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, BeforeAndAfterAll, FreeSpec, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}
import scalacache.ScalaCache

/**
  * Created by jamesanto on 4/19/17.
  */
class CachedEncryptionKeysClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfterAll with BeforeAndAfter with MemcachedTestSupport {
  private implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  private implicit val ec = ExecutionContext.global
  override def memcachedPodLabel(): String = "cached-encryption-keys-memcached"
  private val memcachedClient = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))
  implicit val scalaCache = ScalaCache(MemcachedCache(memcachedClient))

  private val underlyingCacheClient = Mockito.mock(classOf[EncryptionKeysClient])
  private var client: CachedEncryptionKeysClient = _
  //private implicit var scalaCache: ScalaCache[Array[Byte]] = _
  private implicit val resultCodec = JavaSerializationCodec.codec[Map[Regions, EncryptedKey]]
  private implicit val resultCodecEnvId = JavaSerializationCodec.codec[AccountId]
  private implicit val ttl: Option[Duration] = None
  private val accountId = AccountId(5)
  private val apiKeyString = ApiKeyString("api_key")
  private val apiKeyStrings = Set(
    ApiKeyString("api_key"),
    ApiKeyString("api_key1")
  )
  private val cacheKey = CachedEncryptionKeysClient.key(accountId)
  private val cacheKeyEnvId = CachedEncryptionKeysClient.keyAccountId(apiKeyString)

  private val successResult = Map(
    Regions.US_EAST_1 -> EncryptedKey(Array[Byte](-105, -60, 52, 68, -62, -78, 98, 122, -33, -61)),
    Regions.US_WEST_1 -> EncryptedKey(Array[Byte](24, -115, 95, 117, 31, -91, -50, -37, -125, 80))
  )

  private val errorResult = ErrorResponse(
    code = 199,
    message = "Some unknown error"
  )

  override def beforeAll() : Unit = {

    val storage = new ScalaCacheStorage[Map[Regions, EncryptedKey], Array[Byte]]()
    val storageEnvId = new ScalaCacheStorage[AccountId, Array[Byte]]()
    val storageHasKeys = new ScalaCacheStorage[Boolean, Array[Byte]]()
    client = new CachedEncryptionKeysClient(
      underlying = underlyingCacheClient,
      cacheKeys = storage,
      cacheEnvId = storageEnvId,
      cacheHasKeys = storageHasKeys
    )
  }

  override def afterAll() {
    memcachedClient.shutdown()
    cleanupMemcached()
  }

  before {
    memcachedClient.delete(cacheKey)
    memcachedClient.delete(cacheKeyEnvId)
    Mockito.reset(underlyingCacheClient)
  }

  "CachedEncryptionKeysClient" - {
    "should regenerate keys properly" in {
      Mockito.when(underlyingCacheClient.regenerate(accountId)).thenReturn(Future.successful(Right(successResult)))

      //Put some values into the cache
      val future = for {
        _ <- scalaCache.cache.put[Map[Regions, EncryptedKey]](cacheKey, successResult, None)
        keys <- client.regenerate(accountId)
      } yield keys

      whenReady(future) { result =>
        result.fold(_ => fail, _ shouldBe successResult)
        Mockito.verify(underlyingCacheClient).regenerate(accountId)
        //Should have reset the cache values on regeneration
        whenReady(scalaCache.cache.get[Map[Regions, EncryptedKey]](cacheKey))(_ shouldBe Some(successResult))
      }
    }

    "should regenerate keys and should not modify cache if the result from underlying provider is an error" in {
      Mockito.when(underlyingCacheClient.regenerate(accountId)).thenReturn(Future.successful(Left(errorResult)))
      whenReady(client.regenerate(accountId)) { result =>
        result.fold(_ shouldBe errorResult, _ => fail)
        Mockito.verify(underlyingCacheClient).regenerate(accountId)
        //Should not have put the value in cache
        whenReady(scalaCache.cache.get[Map[Regions, EncryptedKey]](cacheKey))(_ shouldBe None)
      }
    }

    "should get keys properly" in {
      Mockito.when(underlyingCacheClient.getKeys(accountId)).thenReturn(Future.successful(Right(successResult)))
      whenReady(client.getKeys(accountId)) { result =>
        result.fold(_ => fail, _ shouldBe successResult)
        Mockito.verify(underlyingCacheClient).getKeys(accountId)
        //Should have put the value in cache. Now it should get from the cache
        Mockito.reset(underlyingCacheClient)
        whenReady(client.getKeys(accountId)) { cachedResult =>
          cachedResult.fold(_ => fail, _ shouldBe successResult)
          Mockito.verify(underlyingCacheClient, Mockito.never()).getKeys(accountId)
        }
      }
    }

    "should get keys and should not modify cache if the result from underlying provider is an error" in {
      Mockito.when(underlyingCacheClient.getKeys(accountId)).thenReturn(Future.successful(Left(errorResult)))
      whenReady(client.getKeys(accountId)) { result =>
        result.fold(_ shouldBe errorResult, _ => fail)
        Mockito.verify(underlyingCacheClient).getKeys(accountId)
        //Should not have put the value in cache
        whenReady(scalaCache.cache.get[Map[Regions, EncryptedKey]](cacheKey))(_ shouldBe None)
      }
    }

    "should get env id properly" in {
      Mockito.when(underlyingCacheClient.getAccountIdByApiKey(apiKeyString)).thenReturn(Future.successful(Right(accountId)))
      whenReady(client.getAccountIdByApiKey(apiKeyString)) { result =>
        result.fold(_ => fail, _ shouldBe accountId)
        Mockito.verify(underlyingCacheClient).getAccountIdByApiKey(apiKeyString)
        //Should have put the value in cache. Now it should get from the cache
        Mockito.reset(underlyingCacheClient)
        whenReady(client.getAccountIdByApiKey(apiKeyString)) { cachedResult =>
          cachedResult.fold(_ => fail, _ shouldBe accountId)
          Mockito.verify(underlyingCacheClient, Mockito.never()).getAccountIdByApiKey(apiKeyString)
        }
      }
    }

    "should get env id and should not modify cache if the result from underlying provider is an error" in {
      Mockito.when(underlyingCacheClient.getAccountIdByApiKey(apiKeyString)).thenReturn(Future.successful(Left(errorResult)))
      whenReady(client.getAccountIdByApiKey(apiKeyString)) { result =>
        result.fold(_ shouldBe errorResult, _ => fail)
        Mockito.verify(underlyingCacheClient).getAccountIdByApiKey(apiKeyString)
        //Should not have put the value in cache
        whenReady(scalaCache.cache.get[Map[Regions, EncryptedKey]](cacheKeyEnvId))(_ shouldBe None)
      }
    }
  }
}
