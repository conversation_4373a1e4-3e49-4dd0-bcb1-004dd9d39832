package me.socure.account.client.idplus

import java.util.concurrent.{Executors, ScheduledThreadPoolExecutor, TimeoutException}

import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{DocvAccountInformation, DocvAccountInformationValueGenerator}
import org.mockito.Mockito
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.language.postfixOps

class CachedAccountServiceDVOrchestraClientTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  private implicit val ec: ExecutionContext = ExecutionContext.fromExecutor(Executors.newFixedThreadPool(10))
  private val realClient = mock[AccountServiceIdPlusClientImpl]
  private val cache = mock[CacheService[String, DocvAccountInformation]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)
  val cacheClient = new CachedAccountServiceDVOrchestraClient(realClient, cache, futureTimeout, cacheFetchTimeout = 50 milliseconds)

  before {
    Mockito.reset(realClient, cache)
  }

  test("should not interact with the real client when the cache returns something") {
    Mockito.when(cache.get("v2_key")).thenReturn(Future.successful(Some(DocvAccountInformationValueGenerator.anDocvAccountInformation())))

    whenReady(cacheClient.fetchByPublicAccountId("key")) { _ =>
      Mockito.verifyZeroInteractions(realClient)
    }
  }

  test("should interact with the real client whe the cache returns nothing then store to the cache when successful") {
    val value =DocvAccountInformationValueGenerator.anDocvAccountInformation()

    Mockito.when(cache.get("v2_key")).thenReturn(Future.successful(None))
    Mockito.when(realClient.fetchByPublicAccountId("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByPublicAccountId("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByPublicAccountId("key")
      Mockito.verify(cache, Mockito.times(1)).put("v2_key", value)
    }
  }

  test("should hit account service when memcached is failed or throws exception") {
    val value =DocvAccountInformationValueGenerator.anDocvAccountInformation()
    Mockito.when(cache.get("v2_key")).thenReturn(Future.failed(new TimeoutException("Timeout")))
    Mockito.when(cache.put("v2_key", value)).thenReturn(Future.successful({}))
    Mockito.when(realClient.fetchByPublicAccountId("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByPublicAccountId("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByPublicAccountId("key")
    }

  }

  test("should hit account service when memcached is taking more than 10 ms ") {
    val value = DocvAccountInformationValueGenerator.anDocvAccountInformation()

    Mockito.when(cache.get("v2_key")).thenAnswer(new Answer[Future[Option[DocvAccountInformation]]] {
      override def answer(invocation: InvocationOnMock): Future[Option[DocvAccountInformation]] = {
        Future {
          Thread.sleep(500) //usual elastic cache operation timeout is only 500 ms.
          Some(value)
        }
      }
    })

    Mockito.when(cache.put("v2_key", value)).thenReturn(Future.successful({}))
    Mockito.when(realClient.fetchByPublicAccountId("key")).thenReturn(Future.successful(Right(value)))

    whenReady(cacheClient.fetchByPublicAccountId("key")) { _ =>
      Mockito.verify(realClient, Mockito.times(1)).fetchByPublicAccountId("key")
    }

  }

}
