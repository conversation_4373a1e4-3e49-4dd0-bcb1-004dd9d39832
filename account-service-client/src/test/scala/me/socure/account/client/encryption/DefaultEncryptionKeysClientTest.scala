package me.socure.account.client.encryption

import java.util.UUID

import com.amazonaws.regions.Regions
import me.socure.common.servlettester.ResourceServlet
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.model.ErrorResponse
import me.socure.model.encryption.{AccountId, ApiKeyString, EncryptedKey}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

/**
  * Created by jamesanto on 4/19/17.
  */
class DefaultEncryptionKeysClientTest extends FreeSpec with Matchers with ScalaFutures {
  private implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  private implicit val ec = ExecutionContext.global

  "DefaultEncryptionKeysClient" - {
    "should regenerate keys properly" in {
      testKeysSuccess("regenerate/2")(_.regenerate(AccountId(2)))
    }

    "should fail to regenerate keys when there is an error" in {
      testKeysError("regenerate/3")(_.regenerate(AccountId(3)))
    }

    "should get keys properly" in {
      testKeysSuccess("get/4")(_.getKeys(AccountId(4)))
    }

    "should fail to get keys when there is an error" in {
      testKeysError("get/5")(_.getKeys(AccountId(5)))
    }

    "should get account id by api key" in {
      val apiKey = UUID.randomUUID().toString
      testAccIdByApiKey(apiKey)
    }

    "should get account id by api key with spaces" in {
      val apiKey = "YOUR SOCURE API KEY"
      testAccIdByApiKey(apiKey)
    }

    "should check whether an account has keys" in {
      withServlet(new ResourceServlet(200, "/encryption/has-keys-200.json"), "/encryption_keys/has/3") { port =>
        val client = new DefaultEncryptionKeysClient(s"http://localhost:$port")

        whenReady(client.hasKeys(AccountId(3))) { response =>
          response shouldBe 'right
          response.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  private def testAccIdByApiKey(apiKey: String): Unit = {
    withServlet(new ResourceServlet(200, "/encryption/account-id-200.json"), s"/encryption_keys/get_account_id_by_api_key/$apiKey") { port =>
      val client = new DefaultEncryptionKeysClient(s"http://localhost:$port")

      whenReady(client.getAccountIdByApiKey(ApiKeyString(apiKey))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe AccountId(5))
      }
    }
  }

  private def testKeysSuccess(endpoint: String)(call: EncryptionKeysClient => Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]]): Unit = {
    withServlet(new ResourceServlet(200, "/encryption/keys-200.json"), s"/encryption_keys/$endpoint") { port =>
      val client = new DefaultEncryptionKeysClient(s"http://localhost:$port")

      whenReady(call(client)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe Map(
          Regions.US_EAST_1 -> EncryptedKey(Array[Byte](-105, -60, 52, 68, -62, -78, 98, 122, -33, -61)),
          Regions.US_WEST_1 -> EncryptedKey(Array[Byte](24, -115, 95, 117, 31, -91, -50, -37, -125, 80))
        ))
      }
    }
  }

  private def testKeysError(endpoint: String)(call: EncryptionKeysClient => Future[Either[ErrorResponse, Map[Regions, EncryptedKey]]]): Unit = {
    withServlet(new ResourceServlet(500, "/encryption/error-500.json"), s"/encryption_keys/$endpoint") { port =>
      val client = new DefaultEncryptionKeysClient(s"http://localhost:$port")

      whenReady(call(client)) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(
          code = 199,
          message = "Some unknown error"
        ), _ => fail)
      }
    }
  }
}
