package me.socure.account.client.idplus

import me.socure.model.account.EnvironmentValueGenerator._
import me.socure.model.account.WatchlistPreferenceValueGenerator._
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferencesValueGenerator
import me.socure.model.{AccountConsentReasons, AccountInformation, Industry}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{FunSuite, Matchers}
import me.socure.model.account.CAWatchlistPreferenceValueGenerator._
import me.socure.model.account.DvConfigurationValueGenerator
import me.socure.model.kyc.KycAddressMatchLogic.default
import me.socure.model.mla.MLAFields
/**
  * Created by alexandre on 5/9/16.
  */
class AccountInformationSerializerTest extends FunSuite with Matchers {


  test("should serialize and deserialize properly") {
    val input = AccountInformation(
      active = true,
      publicId = "1",
      accountId = "1",
      accountName = "",
      industry = Industry("sector", "some industry"),
      isInternal = true,
      environment = anEnvironment(),
      watchlistPreference = aWatchlistPreference(),
      watchlistPreference_3_0 = aCAWatchlistPreference(),
      watchlistPreferences_3_0 = aCAWatchlistPreferences(),

      roles = Set(1, 2, 3),
      primaryFraudModel = Some(FraudModel(
        id = 1,
        identifier = "identifier2",
        name = "name2",
        url = "url2",
        version = "version2",
        createdDate = new DateTime(DateTimeZone.UTC),
        lastUpdated = new DateTime(DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 1,
          identifier = "identifier1",
          name = "name1",
          url = "url1",
          version = "version1",
          createdDate = new DateTime(DateTimeZone.UTC),
          lastUpdated = new DateTime(DateTimeZone.UTC)
        )
      ),
      kycPreferences = KycPreferencesValueGenerator.aKycPreferences().copy(addressMatchLogic = Some(default)),
      subscriptionTypeIds = Seq(),
      includedWatchlistSources = Seq(),
      dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
      consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
      externalId = None,
      mlaField = MLAFields("", ""),
      ein = None,
      webhookNotificationPreferences = Seq()
    )

    val str = input.encodeJson()
    val actual = str.decodeJson[AccountInformation]
    actual shouldBe input
  }
}
