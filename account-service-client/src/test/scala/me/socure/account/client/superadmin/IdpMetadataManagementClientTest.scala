package me.socure.account.client.superadmin

import javax.servlet.http.{HttpServlet, HttpServletRequest, HttpServletResponse}

import me.socure.account.service.common.exceptions.ExceptionCodes
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.idp.metadata.IdpMetadata
import me.socure.model.superadmin.AccountIdpMetadata
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext


class IdpMetadataManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return accounts with idp metadata"){
    lazy val list = List(AccountIdpMetadata(1,"AccountName1",new DateTime("2017-12-06", DateTimeZone.UTC)), AccountIdpMetadata(4,"AccountName4",new DateTime("2017-12-06", DateTimeZone.UTC)))
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/idp/metadata/list_idp_metadata") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.listIdpMetadata, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }

  test("should insert idp metadata for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/idp/metadata/insert_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.insertIdpMetadata(1l, "some data"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when insert idp metadata for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.idpMetadataErrorResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/idp/metadata/insert_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.insertIdpMetadata(100l, "somedata"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
        response.left.value.message shouldBe ExceptionCodes.IdpMetadataAlreadyExists.description
        response.left.value.code shouldBe ExceptionCodes.IdpMetadataAlreadyExists.id
      }
    }
  }

  test("should delete idp metadata for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/idp/metadata/delete_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.deleteIdpMetadata(1l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when delete idpmetadata for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.errorResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/idp/metadata/delete_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.deleteIdpMetadata(100l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
        response.left.value.message shouldBe "Internal Error"
        response.left.value.code shouldBe 199
      }
    }
  }

  test("should get idp metadata for entity id"){
    val expected = IdpMetadata(1L, "metadata")
    val response = Response(ResponseStatus.Ok, expected)
    withServlet(new HttpServlet {
      override def doGet(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.setStatus(200)
        resp.getWriter.write(response.encodeJson())
      }

      override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.sendError(405)
      }
    },"/idp/metadata/get_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.getIdpMetadata("http://entityId"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should get idp metadata for account id"){
    val expected = IdpMetadata(1L, "metadata")
    val response = Response(ResponseStatus.Ok, expected)
    withServlet(new HttpServlet {
      override def doGet(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.setStatus(200)
        resp.getWriter.write(response.encodeJson())
      }

      override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.sendError(405)
      }
    },"/idp/metadata/get_idp_metadata_by_account/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.getIdpMetadataByAccountId(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should throw error when get idp metadata for entityid called") {
    val response = Response(ResponseStatus.Error, UserFixture.accountNotFoundResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/idp/metadata/get_idp_metadata/*") { port =>
      val client = new IdpMetadataManagementClientImpl(s"$host:$port")
      whenReady(client.getIdpMetadata("http://entityId"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
        response.left.value.message shouldBe "The account does not exist"
        response.left.value.code shouldBe 100
      }
    }
  }

}
