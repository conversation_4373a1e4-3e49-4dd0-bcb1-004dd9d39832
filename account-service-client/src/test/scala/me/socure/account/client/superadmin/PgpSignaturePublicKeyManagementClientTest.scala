package me.socure.account.client.superadmin

import java.util.Base64

import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.pgp.{PgpPublicKey, PgpPublicKeyDecoded}
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class PgpSignaturePublicKeyManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return accounts with pgp signature public key"){
    lazy val list = List(AccountPgpInfo(1, "acc-1234", "AccountName1",new DateTime("2017-12-06", DateTimeZone.UTC)), AccountPgpInfo(4, "acc-1235", "AccountName4",new DateTime("2017-12-06", DateTimeZone.UTC)))
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_accounts_with_signature_public_key") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountsWithPgpSignaturePublicKeys, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }

  test("should insert pgp signature public key for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/insert_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.insertPgpSignaturePublicKey(1l, "publicKey"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when create pgp signature public key for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.pgpSignaturePublicKeyErrorResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/insert_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.insertPgpSignaturePublicKey(100l, "publicKey"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should get pgp signature public key for account"){
    val response = Response(ResponseStatus.Ok, PgpPublicKey("cHVibGljS2V5"))
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.getPgpSignaturePublicKey(1l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value.value shouldEqual Base64.getDecoder.decode("cHVibGljS2V5")
      }
    }
  }

  test("should throw error when get pgp signature public key file for invalid account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.pgpSignaturePublicKeyErrorResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/get_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.getPgpSignaturePublicKey(100l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should delete pgp signature public key for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/delete_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.deletePgpSignaturePublicKey(1l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when delete pgp signature public key for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.errorResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/delete_pgp_signature_public_key/*") { port =>
      val client = new PgpSignaturePublicKeyManagementClientImpl(s"$host:$port")
      whenReady(client.deletePgpSignaturePublicKey(100l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }
}
