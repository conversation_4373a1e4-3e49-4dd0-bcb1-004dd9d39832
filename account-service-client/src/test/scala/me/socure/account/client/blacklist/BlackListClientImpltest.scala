package me.socure.account.client.idplus

import me.socure.account.client.blacklist.BlackListClientImpl
import me.socure.common.servlettester.ResourceServlet
import me.socure.common.servlettester.ServletTester._
import me.socure.model._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by alexand<PERSON> on 7/25/16.
  */
class BlackListClientImplTest extends FunSuite with Matchers with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global

  test("fetchAccountInformation should produce correct response when error is returned") {

    withServlet(new ResourceServlet(400, "/blacklist-error.json"), "/blacklist/fetch_account_information/*") { port =>
      val client = new BlackListClientImpl(s"http://localhost:$port")

      whenReady(client.fetchAccountInformation("key")) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(100, "the error"), _ => fail)
      }
    }
  }

  test("fetchAccountInformation should call backend correctly when api key contains spaces") {

    withServlet(new ResourceServlet(400, "/blacklist-error.json"), "/blacklist/fetch_account_information/*") { port =>
      val client = new BlackListClientImpl(s"http://localhost:$port")

      whenReady(client.fetchAccountInformation("key with spaces")) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(100, "the error"), _ => fail)
      }
    }
  }

  test("fetchAccountInformation should produce correct response when success is returned") {

    val expected = BlackListAccountInformation(100L)

    withServlet(new ResourceServlet(200, "/blacklist-account-success.json"), "/blacklist/fetch_account_information/key") { port =>
      val client = new BlackListClientImpl(s"http://localhost:$port")

      whenReady(client.fetchAccountInformation("key")) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("fetchIndustryByAccount should produce correct response when error is returned") {

    withServlet(new ResourceServlet(400, "/blacklist-error.json"), "/blacklist/fetch_industry/1") { port =>
      val client = new BlackListClientImpl(s"http://localhost:$port")

      whenReady(client.fetchIndustryByAccount(1L)) { response =>
        response shouldBe 'left
        response.fold(_.code shouldBe 100, _ => fail)
      }
    }
  }

  test("fetchIndustryByAccount should produce correct response when success is returned") {

    val expected = Industry("sector", "description")

    withServlet(new ResourceServlet(200, "/blacklist-industry-success.json"), "/blacklist/fetch_industry/1") { port =>
      val client = new BlackListClientImpl(s"http://localhost:$port")

      whenReady(client.fetchIndustryByAccount(1L)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }
}
