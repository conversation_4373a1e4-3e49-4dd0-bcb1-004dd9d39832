package me.socure.account.client.ein

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.ein.{EIN, EINRequest, EINResponse}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class EINClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global

  private val http = new NonSecuredHttpFactory().getHttpClient()

  test("Save EIN - success") {
    val einRequest = EINRequest(1L, "*********")
    val einResponse = EINResponse(1L, "*********")
    val expectedRes = Response(ResponseStatus.Ok, data = einResponse)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/ein/*") { port =>
      val client = new EINClientImpl(http = http, s"http://localhost:$port")
      whenReady(client.saveEIN(einRequest)) { response =>
        response.fold(_ => fail, _ => expectedRes)
      }
    }
  }

  test("Save EIN - fail") {
    val einRequest = EINRequest(1L, "*********")
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, data = expected)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/ein/*") { port =>
      val client = new EINClientImpl(http = http, s"http://localhost:$port")
      whenReady(client.saveEIN(einRequest)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Fetch EIN - success") {
    val accountId = 1L
    val expected = Some(EIN("*********"))
    val expectedRes = Response(ResponseStatus.Ok, data = expected)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), s"/ein/account/$accountId") { port =>
      val client = new EINClientImpl(http = http, s"http://localhost:$port")
      whenReady(client.fetchEIN(accountId)) { response =>
        response.fold(_ => fail, _ => expectedRes)
      }
    }
  }

  test("Fetch EIN - fail") {
    val accountId = 1L
    val expected = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    val expectedRes = Response(ResponseStatus.Ok, data = expected)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), s"/ein/account/$accountId") { port =>
      val client = new EINClientImpl(http = http, s"http://localhost:$port")
      whenReady(client.fetchEIN(accountId)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

}