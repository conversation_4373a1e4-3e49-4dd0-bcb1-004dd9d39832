package me.socure.account.client.industry

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.{ErrorResponse, Industry, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sunderraj on 8/31/16.
  */
class IndustryManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(500, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return industries list"){
    val response = Response(ResponseStatus.Ok, Vector(Industry("22", "Contruction")))
    withServlet(new StringServlet(200, response.encodeJson()),"/industries/get_list") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.getIndustryList) { response =>
        response shouldBe 'right
        response.right.value shouldBe Vector(Industry("22", "Contruction"))
      }
    }
  }

  test("should return empty industries list"){
    val response = Response(ResponseStatus.Ok, Vector.empty)
    withServlet(new StringServlet(200, response.encodeJson()),"/industries/get_list") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.getIndustryList) { response =>
        response shouldBe 'right
        response.right.value shouldBe Vector.empty
      }
    }
  }

  test("get industries list should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/industries/get_list") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.getIndustryList) { res =>
        res should be ('left)
      }
    }
  }

  test("upsert industries should fail") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/industries/upsert_industry") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.upsertIndustry(Industry("se", "de"))) { res =>
        res should be ('left)
      }
    }
  }

  test("upsert industry should be successful") {
    val expectedResponse = new Response[Boolean](ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, expectedResponse.encodeJson()), "/industries/upsert_industry") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.upsertIndustry(Industry("df","dfd"))) { res =>
        res should be ('right)
      }
    }
  }

  test("delete industry should be successful") {
    val expectedResponse = Response[Boolean](ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, expectedResponse.encodeJson()), "/industries/delete_industry") { port =>
      var client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.delete("120")) { res =>
        res should be ('right)
      }
    }
  }

  test("delete industry should fail") {
    val expectedResponse = Response[ErrorResponse](ResponseStatus.Error,ErrorResponseFactory.get(AccountsAssociatedWithIndustry))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/industries/delete_industry") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.delete("101")) { res =>
        res should be ('left)
      }
    }
  }

  test("get industry by sector should be successful") {
    val expectedResponse = Response[Industry](ResponseStatus.Ok, Industry("22", "Utilities"))
    withServlet(new StringServlet(200, expectedResponse.encodeJson()), "/industries/get_industry/*") { port =>
      var client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.getIndustryBySector("22")) { res =>
        res should be ('right)
      }
    }
  }

  test("get industry by sector should fail") {
    val expectedResponse = Response[ErrorResponse](ResponseStatus.Error,ErrorResponseFactory.get(IndustryNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/industries/get_industry/*") { port =>
      val client = new IndustryManagementClientImpl(s"$host:$port")
      whenReady(client.getIndustryBySector("501")) { res =>
        res should be ('left)
      }
    }
  }

}
