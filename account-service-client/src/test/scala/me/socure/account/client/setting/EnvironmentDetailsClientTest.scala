package me.socure.account.client.setting

import me.socure.account.client.http.NonSecuredHttp
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.EnvironmentData
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext


class EnvironmentDetailsClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(500, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return environment corresponding to the id"){
    val httpClient = NonSecuredHttp.client
    val response = Response(ResponseStatus.Ok, EnvironmentData.apply(1, "apiKey", "secretkey", "access_token", "token_secret", "domain", 1, 1))
    val id = 1
    withServlet(new StringServlet(200, response.encodeJson()),"/environment/"+id+"/*") { port =>
      val client = new EnvironmentDetailsClientImpl(httpClient,s"$host:$port")
      whenReady(client.getEnvironmentDataById(id)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe EnvironmentData.apply(1, "apiKey", "secretkey", "access_token", "token_secret", "domain", 1, 1))
      }
    }
  }

}
