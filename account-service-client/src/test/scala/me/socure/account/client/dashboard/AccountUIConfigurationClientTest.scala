package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccessForbidden
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, UserDetails}
import me.socure.model._
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class AccountUIConfigurationClientTest extends FunSuite with Matchers with ScalaFutures {

  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("should get UIAccountConfiguration") {
    val accountUIConfiguration = AccountUIConfiguration(Some(1), Some(10), Some(10))
    val result = Response(ResponseStatus.Ok, accountUIConfiguration)
    withServlet(new StringServlet(200, result.encodeJson()), "/ui/configuration/*") { port =>
      val client: AccountUIConfigurationClient = new AccountUIConfigurationClientImpl(http, s"$host:$port")
      whenReady(client.getUIAccountConfiguration(1, 1L, 1L)) { response =>
        response.fold(_ => fail, res => {
          res shouldBe result.data
        })
      }
    }
  }

  test("should return error response for get UIAccountConfiguration") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/ui/configuration/*") { port =>
      val client: AccountUIConfigurationClient = new AccountUIConfigurationClientImpl(http, s"$host:$port")
      whenReady(client.getUIAccountConfiguration(1, 1L, 1L)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should save UIAccountConfiguration for accounts") {
    val accountUIConfigurationRequest = AccountsUIConfigurationRequest(Seq(1), 10, 10)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val result = Response(ResponseStatus.Ok, (auditDetails, true))
    withServlet(new StringServlet(200, result.encodeJson()), "/ui/configuration/accounts") { port =>
      val client: AccountUIConfigurationClient = new AccountUIConfigurationClientImpl(http, s"$host:$port")
      whenReady(client.saveUIAccountConfigurationForAccounts(accountUIConfigurationRequest, 1, 1)) { response =>
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response for save UIAccountConfiguration for accounts") {
    val accountUIConfigurationRequest = AccountsUIConfigurationRequest(Seq(1), 10, 10)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(ErrorResponseFactory.get(AccessForbidden)), Seq.empty)
    val expectedRes = Response(ResponseStatus.Error, (auditDetails, ErrorResponseFactory.get(AccessForbidden)))

    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/ui/configuration/accounts") { port =>
      val client: AccountUIConfigurationClient = new AccountUIConfigurationClientImpl(http, s"$host:$port")
      whenReady(client.saveUIAccountConfigurationForAccounts(accountUIConfigurationRequest, 1, 1)) { response =>
        response._2.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should save UIAccountConfiguration") {
    val accountUIConfigurationRequest = AccountUIConfigurationRequest(1, 10, 10)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val result = Response(ResponseStatus.Ok, (auditDetails, true))
    withServlet(new StringServlet(200, result.encodeJson()), "/ui/configuration") { port =>
      val client: AccountUIConfigurationClient = new AccountUIConfigurationClientImpl(http, s"$host:$port")
      whenReady(client.saveUIAccountConfiguration(accountUIConfigurationRequest, 1, 1)) { response =>
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

}
