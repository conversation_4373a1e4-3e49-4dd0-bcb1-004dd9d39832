package me.socure.account.client.idplus

import me.socure.account.service.common.Constant
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model._
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferences
import me.socure.model.mla.MLAFields
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class AccountServiceIdPlusV2ClientImplTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http = new NonSecuredHttpFactory().getHttpClient()

  test("fetchAccountInfoByPublicApiKey should return Account information by public api key") {
    val publicApiKey = "09-16ca6193-4149-456b-ae00-00fdad2437c6"
    val expected = AccountInformation(
      active = true,
      publicId = "1",
      accountId = "1",
      accountName = "accountName",
      industry = Industry(
        sector = "some sector",
        description = "some industry"),
      isInternal = true,
      environment = Environment(
        publicApiKeys = Seq.empty,
        id = 456,
        name = "environment",
        domain = Set("domain1", "domain2"),
        accessCredentials = AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret key",
          accessToken = "access token",
          accessTokenSecret = "access token secret",
          certificate = "certificate"
        ),
        socialAccounts = Seq(
          SocialNetworkAppKeys(
            id = 145,
            provider= "provider",
            appkey = "app key",
            appsecret = "app secret",
            environment = 566,
            accountId = 999
          )
        ),
        invidiualCache = Seq(
          AccountIndividualCache(
            id = 666L,
            date = new DateTime(0L).withZone(DateTimeZone.UTC),
            identifier = "identifier1",
            accountId = 876L
          )
        ),
        overallCache = Some(AccountOverallCache(
          id = 234L,
          date = new DateTime(0L).withZone(DateTimeZone.UTC),
          accountId = 8765L))
      ),
      watchlistPreference = WatchlistPreference(
        environmentId = 100,
        exactDoB = true,
        dobAndName = false,
        matchScore = 90,
        categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
      ),
      watchlistPreference_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreference(),
      watchlistPreferences_3_0 = CAWatchlistPreferenceValueGenerator.aCAWatchlistPreferences(),
      roles = Set(
        BusinessUserRoles.WATCHLIST.id
      ),
      primaryFraudModel = Some(FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
        lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 123,
          identifier = "identifier",
          name = "name",
          url = "url",
          version = "123",
          createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
          lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
        )
      ),
      kycPreferences = KycPreferences(
        exactDob = Some(false),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        ssnExactMatch = true
      ),
      subscriptionTypeIds = Seq(),
      includedWatchlistSources = Seq(),
      dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
      consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
      externalId = None,
      mlaField = MLAFields("", ""),
      ein = None,
      webhookNotificationPreferences = Seq()
    )

    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/idplus/v2/accounts/publicapikeys/$publicApiKey") { port =>
      val client: AccountServiceIdPlusV2Client = new AccountServiceIdPlusV2ClientImpl(http, s"$host:$port")
      whenReady(client.fetchAccountInfoByPublicApiKey(publicApiKey = publicApiKey)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }
}
