package me.socure.account.client.dashboard

import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccessCredentials
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.UserInfoWithAccountId
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.user.client.dashboard.{DashboardUserClient, DashboardUserClientImpl}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 6/22/16.
  */
class DashboardUserClientTest extends FunSuite with Matchers with ScalaFutures with EitherValues {
  implicit val ec : ExecutionContext = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout =Span(5, Seconds), interval = Span(500, Millis))

  val host : String = "http://localhost"

  val accessCreds: AccessCredentials = AccessCredentials("apikey", "secretkey", "accesstoken", "accesssecret", "certificate")
  val accountNotFound: ErrorResponse = ErrorResponseFactory.get(AccountNotFound)
  val businessUserNotFound: ErrorResponse = ErrorResponseFactory.get(BusinessUserNotFound)

  test("get accountId should be error response") {
    val expectedRes = Response(ResponseStatus.Error, businessUserNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getAccountIdByUsername("<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("get userInfo with accountId should be error response") {
    val expectedRes = Response(ResponseStatus.Error, businessUserNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getUserInfoWithAccountIdByEmail("<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("get account id should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = 12)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getAccountIdByUsername("<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("get user info with account id should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = UserInfoWithAccountId(1, "firstname", "lastname", "<EMAIL>", 12))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getUserInfoWithAccountIdByEmail("<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("get access creds should be error response") {
    val expectedRes = Response(ResponseStatus.Error, accountNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getAccountCrdentials("socurekey")) { res =>
        res should be ('left)
      }
    }
  }

  test("get access creds should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = accessCreds)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.getAccountCrdentials("socurekey")) { res =>
        res should be ('right)
      }
    }
  }

  test("get business user should be error response") {
    val expectedRes = Response(ResponseStatus.Error, businessUserNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.findByEmail("<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("get business user should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = UserFixture.dashboardUser)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.findByEmail("<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("get user tos should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = false)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client: DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.checkToSAgreementByUser("<EMAIL>")) { res =>
        res should be('right)
      }
    }
  }


  test("change password should be error response") {
    val expectedRes = Response(ResponseStatus.Error, businessUserNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.changePassword("<EMAIL>", "current", "new")) { res =>
        res should be ('left)
      }
    }
  }

  test("change password should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/dashboard")
      whenReady(client.changePassword("<EMAIL>", "current", "new")) { res =>
        res should be ('right)
      }
    }
  }

  test("registration should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.register(UserFixture.userForm)) { res =>
        res should be ('left)
      }
    }
  }

  test("registration should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.register(UserFixture.userForm)) { res =>
        res should be ('right)
      }
    }
  }

  test("v2 registration should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.registerV2(UserFixture.userFormV2)) { res =>
        res should be ('left)
      }
    }
  }

  test("v2 registration should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.registerV2(UserFixture.userFormV2)) { res =>
        res should be ('right)
      }
    }
  }

  test("forgot password should be error response") {
    val expectedRes = Response(ResponseStatus.Error, businessUserNotFound)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/inactive/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/inactive")
      whenReady(client.forgotPassword("<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("forgot password should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = List(UserActivationDetails("firstname", "surname", "<EMAIL>", Option("activationcode"))))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/inactive/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/inactive")
      whenReady(client.forgotPassword("<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("reset password by reset code should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.resetPasswordByResetCode("code", "<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("reset password by reset code should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.resetPasswordByResetCode("code", "<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("set password by activation code should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.setPasswordByActivationCode("code", "<EMAIL>")) { res =>
        res should be ('left)
      }
    }
  }

  test("set password by activation code should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.setPasswordByActivationCode("code", "<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("get user by activation code should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidActivationCode))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.getUserByActivationCode("code")) { res =>
        res should be ('left)
      }
    }
  }

  test("get user by activation code should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = UserFixture.businessUser)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.getUserByActivationCode("code")) { res =>
        res shouldBe 'right
      }
    }
  }

  test("unlockAfterCoolingPeriod should work") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client : DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.unlockAfterCoolingPeriod("<EMAIL>")) { res =>
        res should be ('right)
      }
    }
  }

  test("get business user info should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = UserFixture.businessUserInfo)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client: DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.getBusinessUserInfo(UserFixture.businessUserInfo.businessUserId)) { res =>
        res should be('right)
      }
    }
  }

  test("validate inclusion list should give successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/user/*") { port =>
      val client: DashboardUserClient = new DashboardUserClientImpl(s"$host:$port/user")
      whenReady(client.validateInclusionListDomain(UserFixture.inclusionListEmail,UserFixture.prospectAccountType)) { res =>
        res should be('right)
      }
    }
  }
}
