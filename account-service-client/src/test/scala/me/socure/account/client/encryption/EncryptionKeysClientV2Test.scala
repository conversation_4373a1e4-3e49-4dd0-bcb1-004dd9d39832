package me.socure.account.client.encryption

import dispatch.Http
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.encryption.{AccountId, CustomerKeyDetails, EncryptedKeyDetails, KmsArnDetails}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class EncryptionKeysClientV2Test extends FunSuite with Matchers with EitherValues with ScalaFutures {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()

  test("Should return external KMS keys when available") {
    val accountId = 1L
    val expected = List(KmsArnDetails("arn", new DateTime("2017-03-14").withZone(DateTimeZone.UTC)))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/customer_keys/$accountId") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.getCustomerKeys(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should return empty external KMS keys when not available") {
    val accountId = 2L
    val expected = List()
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/customer_keys/$accountId") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.getCustomerKeys(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should return internal keys alone when external KMS not configured for the account") {
    val accountId = 2L
    val expected = EncryptedKeyDetails(Map.empty, Map("internal-arn-1" -> List("internal_data_key")))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/active_keys/$accountId") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should return both internal and customer keys when external KMS is configured for the account") {
    val accountId = 2L
    val expected = EncryptedKeyDetails(Map("external-arn-1" -> List("external_data_key")), Map("internal-arn-1" -> List("internal_data_key")))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/active_keys/$accountId") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should return success if encrypted and decrypted data are same using given KMS") {
    val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/customer_kms/test") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.testCustomerKms(customerKey)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should return error if provided arn/region/access is not proper") {
    val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
    val expected = ErrorResponse(199, "ISE")
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/encryption_keys_v2/customer_kms/test") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.testCustomerKms(customerKey)) { response =>
        response shouldBe 'left
        response.left.value shouldBe expected
      }
    }
  }

  test("should generate customer keys") {
    val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/encryption_keys_v2/customer_key") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.generateCustomerKeys(customerKey)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should fail to generate customer keys") {
    val customerKey = CustomerKeyDetails(AccountId(1), "KMS-ARN")
    val expected = ErrorResponseFactory.get(ExceptionCodes.GenerateCustomerKeysFailed)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/encryption_keys_v2/customer_key") { port =>
      val client: EncryptionKeysClientV2 = new EncryptionKeysClientV2Impl(http, s"$host:$port")
      whenReady(client.generateCustomerKeys(customerKey)) { response =>
        response shouldBe 'left
        response.left.value shouldBe expected
      }
    }
  }

}
