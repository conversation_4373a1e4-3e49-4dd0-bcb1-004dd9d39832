package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{IDMApiKeyCannotBeDeprecated, IDMApiKeyNotInserted, ID<PERSON>pi<PERSON>eyCannotBeUpdated, IDM<PERSON>eyFetchFailed, PermissionTemplateFetchFailed}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{ApiKeyInfo, ApiKeyStatus, IdmApiKey}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.ExecutionContext

class IDMClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {

  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("fetch IDM key") {
    val accountId = 1L
    val expected  = Option(ApiKeyInfo(1, ApiKeyStatus.ACTIVE, "test"))
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/idm/fetch_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.fetchIDMKey(accountId)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("fetch IDM key error") {
    val accountId = 1L
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(IDMKeyFetchFailed))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/idm/fetch_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.fetchIDMKey(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(IDMKeyFetchFailed.id, IDMKeyFetchFailed.description)
      }
    }
  }

  test("generate IDM key") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/idm/generate_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.generateIDMKey(idmApiKey)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("generate IDM key error") {
    val idmApiKey = IdmApiKey(1, "test")
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(IDMApiKeyNotInserted))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/idm/generate_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.generateIDMKey(idmApiKey)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(IDMApiKeyNotInserted.id, IDMApiKeyNotInserted.description)
      }
    }
  }

  test("deprecate IDM key") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/idm/deprecate_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.deprecateIDMKey(idmApiKey)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("deprecate IDM key error") {
    val idmApiKey = IdmApiKey(1, "test")
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(IDMApiKeyCannotBeDeprecated))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/idm/deprecate_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.deprecateIDMKey(idmApiKey)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(IDMApiKeyCannotBeDeprecated.id, IDMApiKeyCannotBeDeprecated.description)
      }
    }
  }

  test("update IDM key") {
    val idmApiKey = IdmApiKey(1, "test")
    val expected  = true
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/idm/update_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.updateIDMKey(idmApiKey)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("update IDM key error") {
    val idmApiKey = IdmApiKey(1, "test")
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(IDMApiKeyCannotBeUpdated))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/idm/update_api_key/*") { port =>
      val client : IDMClientImpl = new IDMClientImpl(http, s"$host:$port")
      whenReady(client.updateIDMKey(idmApiKey)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(IDMApiKeyCannotBeUpdated.id, IDMApiKeyCannotBeUpdated.description)
      }
    }
  }
}
