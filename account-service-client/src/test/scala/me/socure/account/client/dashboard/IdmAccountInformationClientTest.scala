package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class IdmAccountInformationClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(100, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()

  test("should be able to fetch account information") {
    val apiKey = "a16df0a5-9dda-4a58-ab72-83765492802e"
    withServlet(servlet = new ResourceServlet(status = 200, name = "/idm_account_information_success.json"), path = s"/idm/account_information/v1/$apiKey") { port =>
      val client: IdmAccountInformationClient = new IdmAccountInformationClientImpl(s"$host:$port")
      whenReady(client.fetchIdmAccountInformation(apiKey)) { response =>
        response shouldBe 'right
      }
    }
  }

  test("fetch account information client should return the error response from the server") {
    val apiKey = "a16df0a5-9dda-4a58-ab72-83765492802e"
    val response: ErrorResponse = ErrorResponseFactory.get(ExceptionCodes.IDMNotEnabled)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, response).encodeJson()), s"/idm/account_information/v1/$apiKey") { port =>
      val client: IdmAccountInformationClientImpl = new IdmAccountInformationClientImpl(s"http://localhost:$port")
      whenReady(client.fetchIdmAccountInformation(apiKey)) { res =>
        res shouldBe 'Left
      }
    }
  }
}
