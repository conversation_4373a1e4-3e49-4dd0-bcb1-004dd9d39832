package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.scalatest.{EitherVal<PERSON>, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError

import scala.concurrent.ExecutionContext

class SubscriptionsClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("Should list subscriptions for the account") {
    val accountId = 1L
    val expected = Seq(100, 101)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/subscription/account/${accountId}/subscriptions") { port =>
      val client: SubscriptionsClient = new SubscriptionsClientImpl(http, s"$host:$port")
      whenReady(client.listSubscriptions(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("list subscriptions Should throw error on error") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/subscription/account/${accountId}/subscriptions") { port =>
      val client: SubscriptionsClient = new SubscriptionsClientImpl(http, s"$host:$port")
      whenReady(client.listSubscriptions(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should update the subscription type for the account") {
    val accountId = 1L
    val subscribtionTypeId = 100L
    val operation = "subscribe"

    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/subscription/account/${accountId}/subscriptions/${subscribtionTypeId}/${operation}") { port =>
      val client: SubscriptionsClient = new SubscriptionsClientImpl(http, s"$host:$port")
      whenReady(client.update(accountId, subscribtionTypeId, operation)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should not update the subscription type for the account for invalid account") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    val subscribtionTypeId = 100L
    val operation = "subscribe"
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/subscription/account/${accountId}/subscriptions/${subscribtionTypeId}/${operation}") { port =>
      val client: SubscriptionsClient = new SubscriptionsClientImpl(http, s"$host:$port")
      whenReady(client.update(accountId, subscribtionTypeId, operation)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }
}
