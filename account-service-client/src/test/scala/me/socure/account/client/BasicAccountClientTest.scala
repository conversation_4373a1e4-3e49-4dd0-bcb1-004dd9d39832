package me.socure.account.client

import me.socure.account.client.http.NonSecuredHttp
import me.socure.common.servlettester.ResourceServlet
import me.socure.common.servlettester.ServletTester._
import me.socure.model.ErrorResponse
import me.socure.model.account.AccessCredentials
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by alexand<PERSON> on 8/22/16.
  */
class BasicAccountClientTest extends FunSuite with Matchers with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global

  test("should produce correct response when error is returned") {

    withServlet(new ResourceServlet(400, "/basic-error.json"), "/basic/production_credentials/1") { port =>
      val client = new BasicAccountClient(NonSecuredHttp.client, s"http://localhost:$port")

      whenReady(client.getProductionAccessCredentials(1)) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(100, "The account does not exist"), _ => fail)
      }
    }
  }

  test("should produce correct response when success is returned") {

    withServlet(new ResourceServlet(200, "/basic-success.json"), "/basic/production_credentials/1") { port =>
      val client = new BasicAccountClient(NonSecuredHttp.client, s"http://localhost:$port")

      whenReady(client.getProductionAccessCredentials(1)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret-key",
          accessToken = "access-token",
          accessTokenSecret = "access-token-secret",
          certificate = "certificate"
        ))
      }
    }
  }
}
