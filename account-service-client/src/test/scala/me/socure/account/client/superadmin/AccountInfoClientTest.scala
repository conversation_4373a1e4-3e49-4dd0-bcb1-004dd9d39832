package me.socure.account.client.superadmin

import javax.servlet.http.{HttpServlet, HttpServletRequest, HttpServletResponse}
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.publicid.PublicIdGenerator
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.{StringServlet, StringServletWithParamsValidation}
import me.socure.model._
import me.socure.model.account._
import me.socure.model.analytics.{AnalyticsGlobalInfoRequest, AnalyticsGlobalInfoResponse}
import me.socure.model.encryption.ApiKeyString
import me.socure.util.JsonEnrichments._
import org.apache.commons.io.IOUtils
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{EitherVal<PERSON>, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}
import scala.util.Random

/**
  * Created by sunderraj on 6/17/16.
  */
class AccountInfoClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  test("list accounts should process success properly") {
    val accounts = Seq(PublicAccount("acc-xxxxx", "Account name"))
    val expectedRes = Response(ResponseStatus.Ok, accounts)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllAccounts()) { res =>
        res should be('right)
        res.right.get shouldBe accounts
      }
    }
  }

  test("list accounts should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllAccounts()) { res =>
        res should be('left)
      }
    }
  }

  test("list_account_ids accounts should process success properly") {
    val accountIds = Seq(1L, 2L, 3L)
    val expectedRes = Response(ResponseStatus.Ok, accountIds)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllAccountsIds()) { res =>
        res.fold(_ => fail, _ shouldBe accountIds)
      }
    }
  }

  test("list_account_ids accounts should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllAccountsIds()) { res =>
        res.fold(_ shouldBe ErrorResponseFactory.get(UnknownError), _ => fail)
      }
    }
  }

  test("get account name should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllActiveAccountNames) { res =>
        res should be('left)
      }
    }
  }

  test("get account name should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, Map(1l -> AccountIdName(1l, "account")))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllActiveAccountNames) { res =>
        res should be('right)
      }
    }
  }

  test("get non internal and active account ids should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllNonInternalActiveAccounts) { res =>
        res should be('left)
      }
    }
  }

  test("get non internal and active account ids should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, List(1l))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/info/*") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAllNonInternalActiveAccounts) { res =>
        res should be('right)
      }
    }
  }

  test("should check whether account has role") {
    withServlet(new StringServletWithParamsValidation(200, Response(ResponseStatus.Ok, true).encodeJson(), Map("api_key" -> "api key", "role" -> "1")), "/account/info/has_role") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.hasRole(ApiKeyString("api key"), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Right(true)
      }
    }
  }

  test("should check whether account has role and should handle UnknownRole") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownRole))
    withServlet(new StringServletWithParamsValidation(400, expectedResponse.encodeJson(), Map("api_key" -> "api key", "role" -> "1")), "/account/info/has_role") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.hasRole(ApiKeyString("api key"), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(UnknownRole))
      }
    }
  }

  test("should check whether account has role and should handle AccountNotFound") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServletWithParamsValidation(400, expectedResponse.encodeJson(), Map("api_key" -> "api key", "role" -> "1")), "/account/info/has_role") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.hasRole(ApiKeyString("api key"), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("should get account details by public id") {
    val publicId = PublicIdGenerator.account().value
    withServlet(new StringServletWithParamsValidation(200, Response(ResponseStatus.Ok, AccountInfoClientTestObjects.accountWithEnvironmentDetails).encodeJson(), Map("publicId" -> publicId)), "/account/info/get_account_details_by_public_id_v2") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountDetailsByPublicId(publicId = publicId)) { res =>
        res shouldBe Right(AccountInfoClientTestObjects.accountWithEnvironmentDetails)
      }
    }
  }

  test("should return account not found error when account details not found for the given public id") {
    val publicId = PublicIdGenerator.account().value
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServletWithParamsValidation(400, expectedResponse.encodeJson(), Map("publicId" -> publicId)), "/account/info/get_account_details_by_public_id_v2") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountDetailsByPublicId(publicId = publicId)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("should get account details by id") {
    val id = Random.nextLong()
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, AccountInfoClientTestObjects.accountWithEnvironmentDetailsWithPublicId).encodeJson()), s"/account/info/get_account_details_by_id_v2/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountDetailsById(id = id)) { res =>
        res shouldBe Right(AccountInfoClientTestObjects.accountWithEnvironmentDetailsWithPublicId)
      }
    }
  }

  test("should return account not found error when account details not found for the given id") {
    val id = Random.nextLong()
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/account/info/get_account_details_by_id_v2/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountDetailsById(id = id)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("should get account id and names by given roles") {

    val request = AccountIdNamesByRolesRequest(
      roles = Random.shuffle(BusinessUserRoles.values.map(_.id)).take(3),
      onlyParent = Random.nextBoolean()
    )
    val accountIdNames = Set(
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString),
      AccountIdName(id = Random.nextLong(), name = Random.alphanumeric.take(10).mkString)
    )

    val expectedResponse = Response(
      status = ResponseStatus.Ok,
      data = accountIdNames
    )

    withServlet(new HttpServlet {
      override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        req.getContentType shouldBe "application/json; charset=UTF-8"
        IOUtils.toString(req.getInputStream).decodeJson[AccountIdNamesByRolesRequest] shouldBe request
        resp.setStatus(200)
        resp.getWriter.write(expectedResponse.encodeJson())
      }
    }, "/account/info/get_account_id_names_by_roles_v2") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountIdNamesByRoles(accountIdNamesRequest = request)) { res =>
        res shouldBe Right(accountIdNames)
      }
    }
  }

  test("should get account name by id") {
    val id = Random.nextLong()
    val publicAccount = PublicAccount("PublicId", "AccountName")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, publicAccount).encodeJson()), s"/account/info/public-account/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountNameAndPublicId(accountId = id)) { res =>
        res shouldBe Right(publicAccount)
      }
    }
  }

  test("should return account not found error when no account found for the given id") {
    val id = Random.nextLong()
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/account/info/public-account/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountNameAndPublicId(accountId = id)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("should return encrypted and non-deleted parent accounts"){
    val accounts = Seq(PublicAccount("acc-xxxxx", "Acc Name"))
    val expectedRes = Response(ResponseStatus.Ok, accounts)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/info/get_encrypted_parent_accounts") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getEncryptionEnabledParentAccounts) { res =>
        res should be('right)
        res.right.get shouldBe accounts
      }
    }
  }

  test("error response while fetching non-deleted parent accounts"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/info/get_encrypted_parent_accounts") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getEncryptionEnabledParentAccounts) { res =>
        res should be('left)
      }
    }
  }

  test("getAccountPreferences should get account preferences by id") {
    val id = Random.nextLong()
    val accountpreferences = AccountPreferences(1L, true, true, false)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, accountpreferences).encodeJson()), s"/account/info/preferences/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountPreferences(accountId = id)) { res =>
        res shouldBe Right(accountpreferences)
      }
    }
  }

  test("getAccountPreferences should return account not found error for an invalid account id") {
    val id = Random.nextLong()
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/account/info/preferences/$id") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountPreferences(accountId = id)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("get AccountIdNames should return accountIdNames for having a withPermissionId and having withoutPermissionId"){
    val withPermissionId = Random.nextInt()
    val withOutPermissionId = Random.nextInt()
    val accountIdNames = Seq(AccountIdName(1L, "account1"), AccountIdName(2L, "account2"))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, accountIdNames).encodeJson()), "/account/info/get_accounts_with_and_without_permission") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)) { res =>
          res shouldBe Right(accountIdNames)
      }
    }
  }

  test("error response while get AccountIdNames should return accountIdNames for having a withPermissionId and having withoutPermissionId"){
    val withPermissionId = Random.nextInt()
    val withOutPermissionId = Random.nextInt()
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(ExceptionCodes.UnknownError))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/account/info/get_accounts_with_and_without_permission") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      }
    }
  }

  test("getAnalyticsGlobalInfo should process success properly") {
    val expectedResponse = Some(AnalyticsGlobalInfoResponse(isHistoricDataImported = true, lastImportedDate = DateTime.now().withZone(DateTimeZone.UTC)))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expectedResponse).encodeJson()), "/account/info/analytics") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAnalyticsGlobalInfo()) { res =>
        res.fold(_ => fail, _ shouldBe expectedResponse)
      }
    }
  }

  test("getAnalyticsGlobalInfo should process failure properly") {
    val errorResponse = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, errorResponse).encodeJson()), "/account/info/analytics") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.getAnalyticsGlobalInfo()) { res =>
        res.fold(_ shouldBe errorResponse, _ => fail)
      }
    }
  }

  test("updateAnalyticsGlobalInfo should process success properly") {
    val request = AnalyticsGlobalInfoRequest(lastImportedDate = DateTime.now())
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, true).encodeJson()), "/account/info/analytics") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.updateAnalyticsGlobalInfo(request)) { res =>
        res.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("updateAnalyticsGlobalInfo should process failure properly") {
    val request = AnalyticsGlobalInfoRequest(lastImportedDate = DateTime.now())
    val errorResponse = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, errorResponse).encodeJson()), "/account/info/analytics") { port =>
      val client: AccountInfoClient = new AccountInfoClientImpl(s"http://localhost:$port")
      whenReady(client.updateAnalyticsGlobalInfo(request)) { res =>
        res.fold(_ shouldBe errorResponse, _ => fail)
      }
    }
  }
}