package me.socure.account.client.superadmin

import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 5/30/16.
  */
class ActiveUserClientTest extends FunSuite with ScalaFutures with Matchers with EitherValues {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout =Span(5, Seconds), interval = Span(500, Millis))

  val host : String = "http://localhost"

  test("mark internal should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.markAsInternal(List("<EMAIL>"))) { res =>
        res should be ('left)
      }
    }
  }

  test("mark internal should be successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.markAsInternal(List("<EMAIL>"))) { res =>
        res should be ('right)
      }
    }
  }

  test("mark account as internal should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.markAsInternal(100L)) { res =>
        res.fold(_.code shouldBe AccountNotFound.id, _ =>fail)
      }
    }
  }

  test("mark account as internal should return successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.markAsInternal(1L)) { res =>
        res should be ('right)
      }
    }
  }

  test("unmark internal should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.unmarkAsInternal(List("<EMAIL>"))) { res =>
        res should be ('left)
      }
    }
  }

  test("unmark internal should be successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.unmarkAsInternal(List("<EMAIL>"))) { res =>
        res should be ('right)
      }
    }
  }

  test("unmark account as internal should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.unmarkAsInternal(100L)) { res =>
        res.fold(_.code shouldBe AccountNotFound.id, _ =>fail)
      }
    }
  }

  test("unmark account as internal should return successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.unmarkAsInternal(1L)) { res =>
        res should be ('right)
      }
    }
  }

  test("deactivate user should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deactivateUser(List("<EMAIL>"))) { res =>
        res should be ('left)
      }
    }
  }

  test("deactivate user should be successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deactivateUser(List("<EMAIL>"))) { res =>
        res should be ('right)
      }
    }
  }

  test("deactivate account should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deactivateAccount(100L)) { res =>
        res.fold(_.code shouldBe AccountNotFound.id, _ =>fail)
      }
    }
  }

  test("deactivate account should return successful response") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deactivateAccount(1L)) { res =>
        res should be ('right)
      }
    }
  }

  test("get domain list should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.getDomainList(555)) { res =>
        res should be ('left)
      }
    }
  }

  test("get domain list should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = "domain")
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.getDomainList(22)) { res =>
        res should be ('right)
      }
    }
  }

  test("add domain to user should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.addDomainToAccount(555, "domian,domain2")) { res =>
        res should be ('left)
      }
    }
  }

  test("add domain to user should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.addDomainToAccount(22, "domain")) { res =>
        res should be ('right)
      }
    }
  }

  test("add domain to user should fail for invalid domains") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(DomainNotValid))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.addDomainToAccount(22, ",,")) { res =>
        res should be ('left)
      }
    }
  }

  test("delete account should be error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deleteAccount(555)) { res =>
        res should be ('left)
      }
    }
  }

  test("delete account should be successful response") {
    val expectedRes = Response(ResponseStatus.Ok, data = true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/superadmin/*") { port =>
      val client : ActiveUserClient = new ActiveUserClientImpl(s"http://localhost:$port/superadmin")
      whenReady(client.deleteAccount(22)) { res =>
        res should be ('right)
      }
    }
  }

}
