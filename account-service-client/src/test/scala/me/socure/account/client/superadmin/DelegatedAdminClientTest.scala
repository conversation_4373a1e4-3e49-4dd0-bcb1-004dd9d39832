package me.socure.account.client.superadmin

import me.socure.common.servlettester.{ServletTester, StringServlet}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.superadmin.{AccountName, DelegatedAdmin}
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sunderraj on 6/9/16.
  */
class DelegatedAdminClientTest extends FunSuite with ScalaFutures with Matchers {
  implicit val ec : ExecutionContext = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout =Span(5, Seconds), interval = Span(500, Millis))

  val host : String = "http://localhost"

  val accountName = AccountName("firstname", "surname", "email", "company")
  val delegatedAdmin = DelegatedAdmin(22, "firstname", "lastname", "company", "contact", "<EMAIL>", Some(Set(BusinessUserRoles.USER.id)))
  val updateDelegatedAdmin = DelegatedAdmin(0, "firstname", "lastname", "company", "contact", "<EMAIL>", None)
  val error = ErrorResponse(UnknownError.id, UnknownError.description)

  test("should return account name") {
    val expectedRes = Response(ResponseStatus.Ok, Vector(accountName))
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/get_all_admins") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.getAccountName) { res =>
          res should be('right)
        }
      }
    }
  }

  test("should return 400 on account name") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/get_all_admins") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.getAccountName) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should return delegated admin") {
    val expectedRes = Response(ResponseStatus.Ok, List(delegatedAdmin))
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/delegated_admins/*") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.getDelegatedAdmin("<EMAIL>")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe List(delegatedAdmin))
        }
      }
    }
  }

  test("should return 400 on delegated admin") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/delegated_admins/*") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.getDelegatedAdmin("<EMAIL>")) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should create delegated admin") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/create_delegated_admin") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.createdDelegatedAdmin("adminemai", "email", "first", "last", "company", "contact", "pass", Some(Set(BusinessUserRoles.ADMIN.id)))) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on create delegated admin") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/create_delegated_admin") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.createdDelegatedAdmin("adminemai", "email", "first", "last", "company", "contact", "pass", Some(Set(BusinessUserRoles.ADMIN.id)))) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should delete delegated admin") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/delete_delegated_admin") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.deleteDelegatedAdmin("<EMAIL>")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on delete delegated admin") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/delete_delegated_admin") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.deleteDelegatedAdmin("<EMAIL>")) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should update delegated admin password") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/update_password") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updatePassword("<EMAIL>", "password")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on update delegated admin password") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/update_password") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updatePassword("<EMAIL>", "password")) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should update delegated admin roles") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/update_roles") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updateRoles("<EMAIL>", Set(BusinessUserRoles.ADMIN.id, BusinessUserRoles.USER.id, BusinessUserRoles.REPORTS.id))) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on update delegated admin roles") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/update_roles") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updateRoles("<EMAIL>", Set(BusinessUserRoles.ADMIN.id, BusinessUserRoles.USER.id, BusinessUserRoles.REPORTS.id))) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("should promote delegated admin as admin") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/promote") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.promoteDelegatedAdmin("<EMAIL>")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on promote delegated admin as admin") {
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, error)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/promote") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.promoteDelegatedAdmin("<EMAIL>")) { res =>
          res should be('left)
          res.fold(_ shouldBe error, _ => fail)
        }
      }
    }
  }

  test("is user exist should return true") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/is_user_exist/*") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.isUserExist("<EMAIL>")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on is user exist") {
    val notFound = ErrorResponseFactory.get(BusinessUserNotFound)
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, notFound)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/is_user_exist/*") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.isUserExist("<EMAIL>")) { res =>
          res should be('left)
          res.fold(_ shouldBe notFound, _ => fail)
        }
      }
    }
  }

  test("update user information should return true") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, expectedRes.encodeJson()), "/delegated/update_user_information") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updateUserInformation("<EMAIL>", updateDelegatedAdmin)) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return 400 on update user information") {
    val notFound = ErrorResponseFactory.get(BusinessUserNotFound)
    val expectedError = Response[ErrorResponse](ResponseStatus.Error, notFound)
    ServletTester.withServlet(new StringServlet(400, expectedError.encodeJson()), "/delegated/update_user_information") {
      port => {
        val client = new DelegatedAdminClientImpl(s"$host:$port")
        whenReady(client.updateUserInformation("<EMAIL>", updateDelegatedAdmin)) { res =>
          res should be('left)
          res.fold(_ shouldBe notFound, _ => fail)
        }
      }
    }
  }
}
