package me.socure.account.client.dashboard

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.RateLimitCacheKeyProvider
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.ratelimiter.RateLimitingConfig
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.mockito.Mockito._
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedRateLimitingClientImplTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))
  implicit val ec = ExecutionContext.global

  val host = "http://localhost"
  private val http = Http.default

  val cacheMock = mock[Storage[Seq[RateLimitingConfig]]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)

  val accountId = 1
  val environmentTypeId = 1
  val apiId = "api"
  val cacheKey = RateLimitCacheKeyProvider provide(accountId, environmentTypeId, apiId)
  val cacheResult = Seq(RateLimitingConfig(10, 10))
  val result = Map(apiId -> cacheResult)
  val result1 = Map(apiId -> Seq.empty)

  val response = Response(ResponseStatus.Ok, result)
  val response1 = Response(ResponseStatus.Ok, result1)
  val response2 = Response(ResponseStatus.Ok, cacheResult)

  before {
    Mockito.reset(cacheMock)
  }

  test("getRateLimits - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedRateLimitingClientImpl(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(cacheResult)))

    whenReady(cachedClient.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
      response.fold(_ => fail, _ shouldBe result)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, cacheResult)
    }
  }

  test("getRateLimits - should not interact with the real client when the cache returns empty") {

    val cachedClient = new CachedRateLimitingClientImpl(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(Seq.empty)))

    whenReady(cachedClient.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
      response.fold(_ => fail, _ shouldBe result1)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, cacheResult)
    }
  }

  test("getRateLimits - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimits - should interact with the real client when the cache returns None and then put empty response from client in cache") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, Seq.empty)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response1.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result1)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, Seq.empty)
      }
    }
  }

  test("getRateLimits - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimits - should interact with the real client when memcached timedout") {
    Mockito.when(cacheMock.get(cacheKey))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimits - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimits - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response.encodeJson()), "/api/1.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getRateLimits(accountId, environmentTypeId, Set(apiId))) { response =>
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimitsV2 - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response.encodeJson()), "/api/2.0/rate-limiting/policies_v2") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getRateLimitsV2(accountId, environmentTypeId, Set(apiId))) { response =>
        println(response)
        response.fold(_ => fail, _ shouldBe result)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimit - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedRateLimitingClientImpl(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(cacheResult)))

    whenReady(cachedClient.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
      response.fold(_ => fail, _ shouldBe cacheResult)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, cacheResult)
    }
  }

  test("getRateLimit - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/api/1.0/rate-limiting/policies") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimit - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/api/1.0/rate-limiting/policies") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimit - should interact with the real client when memcached timedout") {
    Mockito.when(cacheMock.get(cacheKey))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/api/1.0/rate-limiting/policies") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimit - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response2.encodeJson()), "/api/1.0/rate-limiting/policies") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getRateLimit - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response2.encodeJson()), "/api/1.0/rate-limiting/policies") { port =>
      val client = new CachedRateLimitingClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getRateLimits(accountId, environmentTypeId, apiId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

}
