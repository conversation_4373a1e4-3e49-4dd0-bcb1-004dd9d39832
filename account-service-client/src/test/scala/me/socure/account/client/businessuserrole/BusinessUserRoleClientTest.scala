package me.socure.account.client.businessuserrole

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, UnknownError}
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.constants.attributes.{AccountAttributeName, AccountAttributeValue}
import me.socure.model.account.{BusinessUserRolesLess, BusinessUserRolesWithPermissions}
import me.socure.model.{BusinessUserRoles, ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}
/**
  * Created by alexand<PERSON> on 5/30/16.
  */
class BusinessUserRoleClientTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit val ec = scala.concurrent.ExecutionContext.Implicits.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host : String = "http://localhost"

  test("addRoles should parse error correctly") {
    withServlet(new ResourceServlet(400, "/business-user-role-add-roles-error.json"), "/businessuserrole/add_role_to_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.addRole(Seq(12L), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Left(ErrorResponse(123, "Something went wrong"))
      }
    }
  }

  test("addRoles should parse success correctly") {
    withServlet(new ResourceServlet(200, "/business-user-role-add-roles-success.json"), "/businessuserrole/add_role_to_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.addRole(Seq(12L), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("Adding new Role Watchlist Auto Monitoring should parse success correctly") {
    withServlet(new ResourceServlet(200, "/business-user-role-auto-monitoring-add-roles-success.json"), "/businessuserrole/add_role_to_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.addRole(Seq(12L), BusinessUserRoles.WatchlistAutoMonitoring.id)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("removeRoles should parse error correctly") {
    withServlet(new ResourceServlet(400, "/business-user-role-remove-roles-error.json"), "/businessuserrole/remove_role_from_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.removeRole(Seq(12L), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Left(ErrorResponse(124, "Something went wrong"))
      }
    }
  }

  test("removeRoles should parse success correctly") {
    withServlet(new ResourceServlet(200, "/business-user-role-remove-roles-success.json"), "/businessuserrole/remove_role_from_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.removeRole(Seq(12L), BusinessUserRoles.ADMIN.id)) { res =>
        res shouldBe Right(2)
      }
    }
  }

  test("remove auto Monitoring role should parse success response correctly") {
    withServlet(new ResourceServlet(200, "/business-user-role-auto-monitoring-remove-roles-success.json"), "/businessuserrole/remove_role_from_users") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.removeRole(Seq(12L), BusinessUserRoles.WatchlistAutoMonitoring.id)) { res =>
        res shouldBe Right(2)
      }
    }
  }

  test("add attribute should parse error correctly") {
    withServlet(new StringServlet(400, """{"status":"error","data":{"code":124,"message":"Something went wrong"}}"""), "/businessuserrole/add_attribute") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.addAttributes(12L, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) { res =>
        res shouldBe Left(ErrorResponse(124, "Something went wrong"))
      }
    }
  }

  test("add attribute should parse success correctly") {
    withServlet(new StringServlet(200, """{"status":"ok","data":1,"msg":""}"""), "/businessuserrole/add_attribute") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.addAttributes(12L, AccountAttributeName.SLA, AccountAttributeValue.DEFAULT_SLA)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("remove attribute should parse error correctly") {
    withServlet(new StringServlet(400, """{"status":"error","data":{"code":124,"message":"Something went wrong"}}"""), "/businessuserrole/remove_attribute") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.removeAttributes(12L, AccountAttributeName.SLA)) { res =>
        res shouldBe Left(ErrorResponse(124, "Something went wrong"))
      }
    }
  }

  test("remove attribute should parse success correctly") {
    withServlet(new StringServlet(200, """{"status":"ok","data":2,"msg":""}"""), "/businessuserrole/remove_attribute") { port =>
      val client = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.removeAttributes(12L, AccountAttributeName.SLA)) { res =>
        res shouldBe Right(2)
      }
    }
  }

  test("list roles should return list of roles") {
    val roles =  List(BusinessUserRolesLess(1, "Admin"))
    val expectedRes = Response(ResponseStatus.Ok, roles)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/businessuserrole/*") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.listRoles) { res =>
        res should be('right)
        res.right.value  shouldBe roles
      }
    }
  }

  test("list roles should return error") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/businessuserrole/*") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.listRoles) { res =>
        res should be('left)
      }
    }
  }

  test("account_permissions should return roles and permission provision for the account") {
    val roles = List(BusinessUserRolesWithPermissions(38,"KYC Test",false), BusinessUserRolesWithPermissions(22,"Watchlist",false), BusinessUserRolesWithPermissions(19,"SSN",false))
    val expectedRes = Response(ResponseStatus.Ok, roles)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/businessuserrole/account_permissions/1") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.getAccountPermissions(1L)) { res =>
        res should be('right)
        res.right.value shouldBe roles
      }
    }
  }

  test("account_permissions should return error") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/businessuserrole/account_permissions/100") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.getAccountPermissions(100L)) { res =>
        res should be('left)
      }
    }
  }

  test("fetch_parent_account_permission should return roles and permission provision for the account") {
    val roles = Set(1,2,3)
    val expectedRes = Response(ResponseStatus.Ok, roles)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/businessuserrole/fetch_parent_account_permission/1") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.getParentAccountPermissions(1L)) { res =>
        res.fold(_ => fail, _ shouldBe roles)
      }
    }
  }

  test("fetch_parent_account_permission should return error") {
    val error = ErrorResponseFactory.get(AccountNotFound)
    val expectedRes = Response(ResponseStatus.Error, error)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/businessuserrole/fetch_parent_account_permission/100") { port =>
      val client: BusinessUserRoleClient = new BusinessUserRoleClient(s"http://localhost:$port")
      whenReady(client.getParentAccountPermissions(100L)) { res =>
        res.fold(_ shouldBe error, _ => fail)
      }
    }
  }
}
