package me.socure.account.client.cache

import me.socure.common.random.Random
import me.socure.common.storage.Storage
import me.socure.model.ErrorResponse
import org.scalamock.scalatest.MockFactory
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

class DefaultErrorCachingHandlerTest extends FreeSpec with Matchers with ScalaFutures with MockFactory {

  private implicit val ec: ExecutionContext = ExecutionContext.Implicits.global

  private val cache = mock[Storage[ErrorResponse]]
  private val errorCodesToCache = Set(2, 3, 4)
  private val handler = ErrorCachingHandler(cache, errorCodesToCache)

  "should return from cache when it's in cache already" in {
    val cacheKey = Random.uuids()
    val expected = er()
    val underlyingResponse = Future.successful(Left(er()))
    (cache.get _).expects(cacheKey).returns(Future.successful(Some(expected)))
    whenReady(handler.cacheIfRequired(cacheKey)(underlyingResponse)) { res =>
      res shouldBe Left(expected)
    }
  }

  "should return from underlying cache and store it" in {
    val cacheKey = Random.uuids()
    val expected = er(code = 3)
    val underlyingResponse = Future.successful(Left(expected))
    (cache.get _).expects(cacheKey).returns(Future.successful(None))
    (cache.store _).expects(cacheKey, expected).returns(Future.successful())
    whenReady(handler.cacheIfRequired(cacheKey)(underlyingResponse)) { res =>
      res shouldBe Left(expected)
    }
  }

  "should return from underlying cache but not store it" in {
    val cacheKey = Random.uuids()
    val expected = er(code = 6)
    val underlyingResponse = Future.successful(Left(expected))
    (cache.get _).expects(cacheKey).returns(Future.successful(None))
    whenReady(handler.cacheIfRequired(cacheKey)(underlyingResponse)) { res =>
      res shouldBe Left(expected)
    }
  }

  private def er(code: Int = Random.nextInt()): ErrorResponse = {
    ErrorResponse(
      code = code,
      message = Random.alphaNumeric(10)
    )
  }
}
