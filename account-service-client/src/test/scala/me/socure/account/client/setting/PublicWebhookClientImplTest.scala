package me.socure.account.client.setting

import javax.servlet.http.{HttpServlet, HttpServletRequest, HttpServletResponse}
import me.socure.account.client.http.NonSecuredHttp
import me.socure.common.publicid.PublicId
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{PublicWebhook, PublicWebhookWithPublicAccountId, PublicWebhooksWithPublicAccountId}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{EitherVal<PERSON>, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, Future}

class PublicWebhookClientImplTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return list of public webhooks") {
    val httpClient = NonSecuredHttp.client
    val list = List(
      PublicWebhook(Some("Watchlist"), Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC)),
      PublicWebhook(Some("Watchlist"), Some("enable"),"test2.com", "certificate 2", 2 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    )
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/settings/webhook/list_public_webhooks") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"$host:$port")
      whenReady(client.listPublicWebhook, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }

  test("should get public webhook for environment id"){
    val httpClient = NonSecuredHttp.client
    val expected = PublicWebhook(Some("Watchlist"), Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val response = Response(ResponseStatus.Ok, expected)
    withServlet(new HttpServlet {
      override def doGet(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.setStatus(200)
        resp.getWriter.write(response.encodeJson())
      }

      override def doPost(req: HttpServletRequest, resp: HttpServletResponse): Unit = {
        resp.sendError(405)
      }
    },"/settings/webhook/get_public_webhook/*") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"$host:$port")
      whenReady(client.getPublicWebhook(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("should update public webhooks for account"){
    val httpClient = NonSecuredHttp.client
    val expected = true
    testSuccess[Boolean](s"/settings/webhook/update_public_webhook", expected) { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.updatePublicWebhook(1L, "updatedEndpoint1", "updated certificate 1", "100")
    }
  }

  test("should fail to update public webhooks for account"){
    val httpClient = NonSecuredHttp.client
    testError(s"/settings/webhook/update_public_webhook") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.updatePublicWebhook(1L, "updatedEndpoint1", "updated certificate 1", "100")
    }
  }

  test("should get public webhooks for environment"){
    val httpClient = NonSecuredHttp.client
    val envId: Long = 1L
    val webhook1 = PublicWebhook(Some("Watchlist"), Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val webhook2 = PublicWebhook(Some("DV"), Some("enable"),"test.com", "certificate 2", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val expected = PublicWebhooksWithPublicAccountId(
      webhooks = Seq(webhook1, webhook2),
      publicAccountId = PublicId(1.toString)
    )
    testSuccess[PublicWebhooksWithPublicAccountId](s"/settings/webhook/environment/$envId", expected) { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.getPublicWebhooks(envId)
    }
  }

  test("should fail to get public webhooks for environment"){
    val httpClient = NonSecuredHttp.client
    val envId: Long = 1L
    testError(s"/settings/webhook/environment/$envId") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.getPublicWebhooks(envId)
    }
  }

  test("should delete public webhooks for account"){
    val httpClient = NonSecuredHttp.client
    val expected = true
    testSuccess[Boolean](s"/settings/webhook/delete_public_webhook", expected) { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.deletePublicWebhook(1L, 1L)
    }
  }

  test("should fail to delete public webhooks for account"){
    val httpClient = NonSecuredHttp.client
    testError(s"/settings/webhook/delete_public_webhook") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.deletePublicWebhook(1L, 100L)
    }
  }

  test("should get public webhooks for account + environment type id + subscription type"){
    val httpClient = NonSecuredHttp.client
    val accountId: Long = 1L
    val envTypeId: Long = 1L
    val subscriptionType: Long = 1L
    val webhook1 = PublicWebhook(Some("Watchlist"), Some("enable"),"test.com", "certificate 1", 1 , new DateTime("2017-12-06", DateTimeZone.UTC), new DateTime("2017-12-06", DateTimeZone.UTC))
    val expected = PublicWebhookWithPublicAccountId(
      webhook = Some(webhook1),
      publicAccountId = PublicId(1.toString),
      provisioned = "Yes",
      None
    )
    testSuccess[PublicWebhookWithPublicAccountId](s"/settings/webhook/account/$accountId/environment/type/$envTypeId/subscription/$subscriptionType", expected) { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.getPublicWebhook(accountId, envTypeId, subscriptionType)
    }
  }

  test("should fail to get public webhooks for account + environment type id + subscription type"){
    val httpClient = NonSecuredHttp.client
    val accountId: Long = 10L
    val envTypeId: Long = 10L
    val subscriptionType: Long = 10L
    testError(s"/settings/webhook/account/$accountId/environment/type/$envTypeId/subscription/$subscriptionType") { port =>
      val client = new PublicWebhookClientImpl(httpClient, s"http://localhost:$port")
      client.getPublicWebhook(accountId, envTypeId, subscriptionType)
    }
  }

  private def testError(path: String)(invoke: Int => Future[Either[ErrorResponse, _]]): Unit = {
    val errorResponse = ErrorResponse(
      code = Random.nextInt(),
      message = Random.alphaNumeric(10)
    )
    withServlet(new StringServlet(400, Serialization.write(Response(ResponseStatus.Error, errorResponse))), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Left(errorResponse)
      }
    }
  }

  private def testSuccess[T](path: String, response: T)(invoke: Int => Future[Either[ErrorResponse, T]])(implicit m: Manifest[T]): Unit = {
    withServlet(new StringServlet(200, Serialization.write(Response(ResponseStatus.Ok, response))), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Right(response)
      }
    }
  }

}
