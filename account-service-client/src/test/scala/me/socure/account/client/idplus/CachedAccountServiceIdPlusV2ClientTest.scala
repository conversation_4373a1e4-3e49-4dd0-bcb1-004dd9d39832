package me.socure.account.client.idplus

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.{AccInfoCacheKeyProvider, CacheKeyProvider}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.{AccountInformation, AccountInformationValueGenerator, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.mockito.Mockito.{never, times}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.language.postfixOps

/**
  * Created by alexandre on 5/9/16.
  */
class CachedAccountServiceIdPlusV2ClientTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))
  implicit val ec = ExecutionContext.global

  val host = "http://localhost"
  private val http = Http.default

  val cacheMock = mock[Storage[AccountInformation]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)

  val apiKey = "api"
  val accountInformation = AccountInformationValueGenerator.anAccountInformation()
  val cacheKey = CacheKeyProvider.provide(AccInfoCacheKeyProvider.provide(apiKey))
  val response = Response(ResponseStatus.Ok, accountInformation)
  val urlPath = s"/idplus/v2/accounts/publicapikeys/$apiKey"

  before {
    Mockito.reset(cacheMock)
  }

  test("fetchAccountInfoByPublicApiKey - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedAccountServiceIdPlusV2Client(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(accountInformation)))

    whenReady(cachedClient.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
      response.fold(_ => fail, _ shouldBe accountInformation)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, accountInformation)
    }
  }

  test("fetchAccountInfoByPublicApiKey - shouldn't interact with the real client when the cache returns None during first time but returns data second time post lock acquire") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None), Future.successful(Some(accountInformation)))
    Mockito.when(cacheMock.store(cacheKey, accountInformation)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, None)
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, never()).store(cacheKey, accountInformation)
      }
    }
  }

  test("fetchAccountInfoByPublicApiKey - should interact with the real client and put response in cache when the cache returns None during first and second time post lock acquire") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, accountInformation)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, None)
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, accountInformation)
      }
    }
  }

  test("fetchAccountInfoByPublicApiKey - should interact with the real client when memcached is failed or throws exception during first and second time post lock acquire") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock.store(cacheKey, accountInformation)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, None)
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, accountInformation)
      }
    }
  }

  test("fetchAccountInfoByPublicApiKey - should interact with the real client when memcached timedout during first and second time post lock acquire") {
    Mockito.when(cacheMock.get(cacheKey))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock.store(cacheKey, accountInformation))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, accountInformation)
      }
    }
  }

  test("fetchAccountInfoByPublicApiKey - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, accountInformation)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, None)
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, accountInformation)
      }
    }
  }

  test("fetchAccountInfoByPublicApiKey - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, accountInformation)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedAccountServiceIdPlusV2Client(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.fetchAccountInfoByPublicApiKey(apiKey)) { response =>
        response.fold(_ => fail, _ shouldBe accountInformation)
        Mockito.verify(cacheMock, times(2)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, accountInformation)
      }
    }
  }
}
