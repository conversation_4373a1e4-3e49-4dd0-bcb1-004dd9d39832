package me.socure.account.client.superadmin

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.AccInfoCacheKeyProvider
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.constants.EnvironmentConstants
import me.socure.model.account.{AccountIdName, Subscription}
import me.socure.model.encryption.ApiKeyString
import me.socure.model.user.authorization.{AccountWithEnvironmentDetailsWithPublicId, AccountWithPublicId, EnvironmentSettings}
import me.socure.model.{BusinessUserRoles, ErrorResponse}
import org.mockito.Mockito
import org.mockito.Mockito.{never, times}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}
import scala.util.Random

/**
  * Created by jamesanto on 5/8/17.
  */
class CachedAccountInfoClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfter {
  private implicit val ec = ExecutionContext.Implicits.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))

  private val underlying = Mockito.mock(classOf[AccountInfoClient])
  private val hasRoleStorage = Mockito.mock(classOf[Storage[Boolean]])
  private val accountWithEnvironmentDetailsWithPublicIdStorageMock = Mockito.mock(classOf[Storage[AccountWithEnvironmentDetailsWithPublicId]])
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)
  private val client = new CachedAccountInfoClient(underlying, hasRoleStorage, accountWithEnvironmentDetailsWithPublicIdStorageMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

  private val apiKeyString = ApiKeyString("api key")
  private val role = BusinessUserRoles.ADMIN.id
  private val cacheKey = CachedAccountInfoClient.key(apiKeyString, role)
  private val accountId = 1
  private val cacheKeyAccountId = AccInfoCacheKeyProvider keyForGetAccountDetailsById accountId
  private val accountWithEnvironmentDetailsWithPublicId = AccountWithEnvironmentDetailsWithPublicId(
    account = AccountWithPublicId(
      id = 20001,
      publicId = "some-public-id-123",
      name = "Saml 2.0 Account",
      permission = Set(
        BusinessUserRoles.SAML_2_0.id,
        BusinessUserRoles.ADDRESS_RISK_SCORE.id
      ),
      isActive = true,
      isInternal = false,
      subscriptions = Set(Subscription(1, "Watchlist Monitoring")),
      false
    ),
    environment = Set(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
  )
 private val accountIdNames = Seq(AccountIdName(Random.nextInt(),"test")).toList
  val host = "http://localhost"
  private val http = Http.default

  before {
    Mockito.reset(underlying, hasRoleStorage, accountWithEnvironmentDetailsWithPublicIdStorageMock)
  }

  "CachedAccountInfoClient" - {
    "should check whether account has role" in {
      Mockito.when(hasRoleStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.hasRole(apiKeyString, role)).thenReturn(Future.successful(Right(true)))
      Mockito.when(hasRoleStorage.store(cacheKey, true)).thenReturn(Future.successful(()))
      whenReady(client.hasRole(apiKeyString, role)) { result =>
        result shouldBe Right(true)
        Mockito.verify(hasRoleStorage).get(cacheKey)
        Mockito.verify(underlying).hasRole(apiKeyString, role)
        Mockito.verify(hasRoleStorage).store(cacheKey, true)
      }
    }

    "should check whether account has role and should not store to cache" in {
      val response = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownRole))
      Mockito.when(hasRoleStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.hasRole(apiKeyString, role)).thenReturn(Future.successful(response))
      Mockito.when(hasRoleStorage.store(cacheKey, true)).thenReturn(Future.successful(()))
      whenReady(client.hasRole(apiKeyString, role)) { result =>
        result shouldBe response
        Mockito.verify(hasRoleStorage).get(cacheKey)
        Mockito.verify(underlying).hasRole(apiKeyString, role)
        Mockito.verify(hasRoleStorage, Mockito.never()).store(cacheKey, true)
      }
    }

    "should get from cache when available" in {
      Mockito.when(hasRoleStorage.get(cacheKey)).thenReturn(Future.successful(Some(true)))
      Mockito.when(underlying.hasRole(apiKeyString, role)).thenReturn(Future.successful(Right(true)))
      Mockito.when(hasRoleStorage.store(cacheKey, true)).thenReturn(Future.successful(()))
      whenReady(client.hasRole(apiKeyString, role)) { result =>
        result shouldBe Right(true)
        Mockito.verify(hasRoleStorage).get(cacheKey)
        Mockito.verify(underlying, Mockito.never()).hasRole(apiKeyString, role)
        Mockito.verify(hasRoleStorage, Mockito.never()).store(cacheKey, true)
      }
    }

    "getAccountDetailsById - should not interact with the real client when the cache returns" in {

      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.successful(Some(accountWithEnvironmentDetailsWithPublicId)))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, never()).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountDetailsById - should interact with the real client when the cache returns None and then put response in cache" in {

      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.successful(None))
      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Right(accountWithEnvironmentDetailsWithPublicId)))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenReturn(Future.successful(()))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
          response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
          Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
          Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
        }
    }

    "getAccountDetailsById - should interact with the real client when the cache returns None and then not put response in cache" in {

      val error = ErrorResponse(1, "error")

      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.successful(None))
      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Left(error)))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenReturn(Future.successful(()))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ shouldBe error, _ => fail)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, never()).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountDetailsById - should interact with the real client when memcached is failed or throws exception during get" in {

      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Right(accountWithEnvironmentDetailsWithPublicId)))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.failed(new Exception("Exception")))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenReturn(Future.successful(()))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountDetailsById - should interact with the real client when memcached timedout" in {
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId))
        .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId))
        .thenReturn(Future.successful(()))
      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Right(accountWithEnvironmentDetailsWithPublicId)))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenReturn(Future.successful(()))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountDetailsById - should interact with the real client and ignores exception during cache put" in {

      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.successful(None))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenReturn(Future.failed(new Exception("Exception")))
      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Right(accountWithEnvironmentDetailsWithPublicId)))

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountDetailsById - should interact with the real client and ignores timeout during cache put" in {

      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.get(cacheKeyAccountId)).thenReturn(Future.successful(None))
      Mockito.when(underlying.getAccountDetailsById(accountId)).thenReturn(Future.successful(Right(accountWithEnvironmentDetailsWithPublicId)))
      Mockito.when(accountWithEnvironmentDetailsWithPublicIdStorageMock.store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)).thenAnswer(new Answer[Future[Unit]] {
        override def answer(invocation: InvocationOnMock): Future[Unit] = {
          Future {
            Thread.sleep(100)
            ()
          }
        }
      })

      whenReady(client.getAccountDetailsById(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe accountWithEnvironmentDetailsWithPublicId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).get(cacheKeyAccountId)
        Mockito.verify(accountWithEnvironmentDetailsWithPublicIdStorageMock, times(1)).store(cacheKeyAccountId, accountWithEnvironmentDetailsWithPublicId)
      }
    }

    "getAccountIdNamesWithAndWithOutPermission - should interact with the real client and ignores exception during cache get" in {
      val withPermissionId =  Random.nextInt()
      val withOutPermissionId =  Random.nextInt()
      Mockito.when(underlying.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)).thenReturn(Future.successful(Right(accountIdNames)))

      whenReady(client.getAccountIdNamesWithAndWithoutPermission(withPermissionId,withOutPermissionId)) {response =>
        response.fold(_ => fail, _ shouldBe accountIdNames)
      }
    }
  }

}
