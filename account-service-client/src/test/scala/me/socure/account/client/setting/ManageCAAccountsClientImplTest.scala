package me.socure.account.client.setting

import dispatch.Http
import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.common.clock.FakeClock
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.constants.JsonFormats
import me.socure.model.account.{CAWatchlistPreference, CAWatchlistPreferenceRequest}
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model._
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.Formats
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.util.Random


class ManageCAAccountsClientImplTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit private val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val jsonFormats: Formats = JsonFormats.formats

  private val http = Http.default
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("GET CA Watchlist should be success response") {
    val environmentId = Random.nextLong()

    withServlet(new ResourceServlet(200, "/settings-ca-preference-watchlist-get-success.json"), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getCAWatchList(environmentId)) { res =>
        res should be('right)
      }
    }
  }

  test("GET CA Watchlist should be error response") {
    withServlet(new ResourceServlet(400, "/settings-preference-watchlist-get-error.json"), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getCAWatchList(12)) { res =>
        res should be('left)
      }
    }
  }

   test("POST CA Watchlist should be error response") {
     val preference = CAWatchlistPreference(
       environmentId = 1,
       exactDoB = Some(false),
       dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
       dobAndName = false,
       monitoring = false,
       matchingThresholds = 0.1,
       limit = 10,
       screeningCategories =  Set("PEP"),
       watchlistScreeningCategories = Option(Set("PEP")),
       country = Option(Set("US")),
       historicalRange = None,
       creator = Option(Creator(1, 1))
     )

     val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
     val errorResponse = ErrorResponseFactory.get(new Exception("The account does not exist"))
     val res =  Response(ResponseStatus.Ok, (auditDetails, errorResponse)).encodeJson()
     withServlet(new StringServlet(200, res), "/settings/preferences/ca/watchlist/3.0") { port =>
       val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
       whenReady(client.setCAWatchlist(preference)) { res =>
         res._2 should be('left)
       }
     }
   }

   test("POST CA Watchlist should be success response") {
     val preference = CAWatchlistPreference(
       environmentId = 1,
       exactDoB = Some(true),
       dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
       dobAndName = false,
       monitoring = false,
       matchingThresholds = 0.1,
       limit = 10,
       screeningCategories =  Set("PEP"),
       watchlistScreeningCategories = Option(Set("PEP")),
       country = Option(Set("US")),
       historicalRange = None
     )

     val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
     val res = Response(ResponseStatus.Ok, (auditDetails,preference)).encodeJson()
     withServlet(new StringServlet(200, res), "/settings/preferences/ca/watchlist/3.0") { port =>
       val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
       whenReady(client.setCAWatchlist(preference)) { res =>
         res._2 should be('right)
       }
     }
   }

  test("POST CA Watchlist should be success response with multiaccounts") {
    val preference = CAWatchlistPreference(
      environmentId = Random.nextLong(),
      exactDoB = Some(true),
      dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
      dobAndName = false,
      monitoring = false,
      matchingThresholds = 0.1,
      limit = 10,
      screeningCategories = Set("PEP"),
      watchlistScreeningCategories = Option(Set("PEP")),
      country = Option(Set("US")),
      historicalRange = None
    )
    val cAWatchlistPreferenceRequest = CAWatchlistPreferenceRequest(
      cAWatchlistPreference = preference,
      accountIds = Seq(1),
      isForceInherit = Some(false),
      environmentTypes = Seq(1)
    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val res = Response(ResponseStatus.Ok, (auditDetails,preference)).encodeJson()
    withServlet(new StringServlet(200, res), "/settings/preferences/ca/accounts/watchlist/3.0") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.setCAWatchlistForAccounts(cAWatchlistPreferenceRequest)) { res =>
        res._2 should be('right)
      }
    }
  }

  test("Should fetch watchlist included sources for an environment") {
    val environmentId = 1
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val watchlistSource2 = WatchlistSource(
      id = 101,
      name = "Source 2",
      category = "category 2",
      subCategory = "sub category 2",
      location = Some("location 2"),
      createAt = clock.now()
    )
    val expected = Seq(watchlistSource1, watchlistSource2)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlistSource(environmentId)) { res =>
        res shouldBe Right(expected)
      }
    }
  }

  test("Should include watchlist sources for an environment") {
    val environmentId = 1
    val sourceIds = Set(1L, 2L, 23L)
    val expected = true
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, (auditDetails, expected)).encodeJson()), s"/settings/preferences/ca/watchlist/sources/include") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.includeWatchlistSource(WatchlistSourceForEnvironment(environmentId, sourceIds))) { res =>
        res._2 shouldBe Right(expected)
      }
    }
  }

  test("Should include watchlist sources for an environment with multiaccounts") {
    val environmentId = 1
    val sourceIds = Set(1L, 2L, 23L)
    val watchlistSourceRequest = WatchlistSourceRequest(
      watchlistSourceForEnvironment = WatchlistSourceForEnvironment(environmentId, sourceIds),
      accountIds = Seq(1),
      isForceInherit = Some(false),
      environmentTypes = Seq(1)

    )
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, (auditDetails, expected)).encodeJson()), s"/settings/preferences/ca/accounts/watchlist/sources/include") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.includeWatchlistSourceForAccounts(watchlistSourceRequest)) { res =>
        res._2 shouldBe Right(expected)
      }
    }
  }

  test("Should fail - include watchlist sources for an environment") {
    val accountId = 100L
    val sourceIds = Set(1L, 2L, 23L)
    val expected = ErrorResponseFactory.get(new Exception("Unknown Error"))
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(expected), Seq.empty)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, (auditDetails, expected)).encodeJson()), s"/settings/preferences/ca/watchlist/sources/include") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.includeWatchlistSource(WatchlistSourceForEnvironment(accountId, sourceIds))) { res =>
        res._2 shouldBe Left(expected)
      }
    }
  }

  test("Should fetch watchlist sources") {
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val watchlistSource2 = WatchlistSource(
      id = 101,
      name = "Source 2",
      category = "category 2",
      subCategory = "sub category 2",
      location = Some("location 2"),
      createAt = clock.now()
    )
    val expected = Seq(watchlistSource1, watchlistSource2)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/settings/preferences/ca/watchlist/sources") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlistSource) { res =>
        res shouldBe Right(expected)
      }
    }
  }

  test("Should fetch watchlist sources by category") {
    val watchlistSource1 = WatchlistSource(
      id = 100,
      name = "Source 1",
      category = "category 1",
      subCategory = "sub category 1",
      location = Some("location 1"),
      createAt = clock.now()
    )
    val expected = Seq(watchlistSource1)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/settings/preferences/ca/watchlist/sources/category/1") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlistSourceByCategory(1)) { res =>
        res shouldBe Right(expected)
      }
    }
  }

  test("Should fail - get watchlist sources for category") {
    val expected = ErrorResponseFactory.get(new Exception("Invalid Category"))
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/settings/preferences/ca/watchlist/sources/category/100") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlistSourceByCategory(100)) { res =>
        res shouldBe Left(expected)
      }
    }
  }

  test("Should fetch historical range") {
    val expected = Set("1 year", "All Time")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/settings/preferences/ca/historicalrange") { port =>
      val client = new ManageCAAccountsClientImpl(http, s"http://localhost:$port")
      whenReady(client.getHistoricalRange()) { res =>
        res shouldBe Right(expected)
      }
    }
  }

}
