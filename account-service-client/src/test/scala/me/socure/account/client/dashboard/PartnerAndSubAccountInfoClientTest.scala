package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccessForbidden
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._
import scala.concurrent.ExecutionContext

class PartnerAndSubAccountInfoClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("should associate user,account and role") {
    val userId, accountId, userRoleId = 1L
    val expected  = true
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/partner_and_sub_account_info/associate/user/*") { port =>
      val client : PartnerAndSubAccountInfoClientImpl = new PartnerAndSubAccountInfoClientImpl(http, s"$host:$port")
      whenReady(client.associateUserAccountRole(userId, accountId, userRoleId, Some(false), None)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("should return error response for get default modules") {
    val userId, accountId, userRoleId = 1L
    val result = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, result.encodeJson()), "/partner_and_sub_account_info/associate/user/*") { port =>
      val client : PartnerAndSubAccountInfoClientImpl = new PartnerAndSubAccountInfoClientImpl(http, s"$host:$port")
      whenReady(client.associateUserAccountRole(userId, accountId, userRoleId, Some(false), None)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should update isSponsorBank Option") {
    val accountId = 1L
    val expected  = true
    val initiatedBy = "<EMAIL>"
    val result = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, result.encodeJson()), "/partner_and_sub_account_info/update/account/1/isSponsorBank/*") { port =>
      val client : PartnerAndSubAccountInfoClientImpl = new PartnerAndSubAccountInfoClientImpl(http, s"$host:$port")
      whenReady(client.updateIsSponsorBank(accountId, true, initiatedBy)) { response =>
        response.fold(_ => fail, _ => expected)
      }
    }
  }

  test("should fail to update isSponsorNBank Option") {
    val accountId = 1L
    val initiatedBy = "<EMAIL>"
    val result = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, result.encodeJson()), "/partner_and_sub_account_info/update/account/1/isSponsorBank/*") { port =>
      val client : PartnerAndSubAccountInfoClientImpl = new PartnerAndSubAccountInfoClientImpl(http, s"$host:$port")
      whenReady(client.updateIsSponsorBank(accountId, false, initiatedBy)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }
}
