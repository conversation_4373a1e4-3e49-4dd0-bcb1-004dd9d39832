package me.socure.account.client.sftp

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{CouldNotAddSFTPUserForAccount, UnableToListSFTPUsers}
import me.socure.common.clock.FakeClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountSftpUser
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class AccountSftpUserClientTest extends FunSuite with Matchers with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global

  private val host = "http://localhost"
  private val http = new NonSecuredHttpFactory().getHttpClient()
  private val clock = new FakeClock(new DateTime("2022-07-07").withZone(DateTimeZone.UTC).getMillis)

  test("Should list the Account Sftp Users") {
    val currentTime = clock.now
    val accountSftpUsers = Seq(AccountSftpUser(1L, 1L, "Account1", "SFTPUser", currentTime, currentTime))
    val expectedRes = Response(ResponseStatus.Ok, accountSftpUsers)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/sftp/user") { port =>
      val client = new AccountSftpUserClientImpl(http = http, s"$host:$port")
      whenReady(client.listAccountSftpUsers()) { response =>
        response.fold(_ => fail, _ shouldBe accountSftpUsers)
      }
    }
  }

  test("Should fail to list the Account Sftp Users") {
    val response0 = ErrorResponseFactory.get(UnableToListSFTPUsers)
    val expectedRes = Response(ResponseStatus.Error, response0)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/sftp/user") { port =>
      val client = new AccountSftpUserClientImpl(http = http, s"$host:$port")
      whenReady(client.listAccountSftpUsers()) { response =>
        response.fold(_ shouldBe response0, _ => fail)
      }
    }
  }

  test("Should Save Account Sftp User") {
    val response0 = true
    val expectedRes = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/sftp/user") { port =>
      val client = new AccountSftpUserClientImpl(http = http, s"$host:$port")
      whenReady(client.saveAccountSftpUser(1l, "sftpUser")) { response =>
        response.fold(_ => fail, _ shouldBe response0)
      }
    }
  }

  test("Should fail to save the Account Sftp Users") {
    val response0 = ErrorResponseFactory.get(CouldNotAddSFTPUserForAccount)
    val expectedRes = Response(ResponseStatus.Error, response0)
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/sftp/user") { port =>
      val client = new AccountSftpUserClientImpl(http = http, s"$host:$port")
      whenReady(client.saveAccountSftpUser(100L, "")) { response =>
        response.fold(_ shouldBe response0, _ => fail)
      }
    }
  }

}
