package me.socure.account.client.dashboard

import dispatch.Future
import me.socure.account.service.common.IdmAccountInfoCacheKeyProvider
import me.socure.common.storage.Storage
import me.socure.model.account.IdmAccountInfoResponse
import me.socure.util.JsonEnrichments.JsonDecoder
import org.mockito.Mockito
import org.mockito.Mockito.{mock, never, times, when}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}

import scala.concurrent.ExecutionContext

class CachedIdmAccountInfoClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfter {
  private implicit val ec = ExecutionContext.Implicits.global
  val storage: Storage[IdmAccountInfoResponse] = mock(classOf[Storage[IdmAccountInfoResponse]])
  val underlying: IdmAccountInformationClient = mock(classOf[IdmAccountInformationClient])
  private val client = new CachedIdmAccountInformationClient(underlying, storage)

  private val apiKey = "a16df0a5-9dda-4a58-ab72-83765492802e"
  private val cacheKey = IdmAccountInfoCacheKeyProvider.provide(apiKey)

  private val AccountInfoResponse: IdmAccountInfoResponse = "{\"active\":true,\"accountId\":\"acc-usbtscc\",\"accountName\":\"Socure\",\"apiKeys\":{\"idplusKeys\":[{\"id\":2056,\"environmentId\":321,\"environment\":\"Production\",\"apiKey\":\"203870b6-1316-4ae0-8872-6761c636f01e\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"},{\"id\":2054,\"environmentId\":322,\"environment\":\"Certification\",\"apiKey\":\"503870b6-1316-43e0-8872-6761c456f01e\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"},{\"id\":2057,\"environmentId\":325,\"environment\":\"Sandbox\",\"apiKey\":\"f0387ab6-1316-4ae0-8872-8761c636f01e\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"}],\"sdkKeys\":[{\"id\":1663,\"environmentId\":321,\"environment\":\"Production\",\"apiKey\":\"45bdf087-7561-633c-985b-4ecf4644470b\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"},{\"id\":2054,\"environmentId\":322,\"environment\":\"Certification\",\"apiKey\":\"503870b6-1316-43e0-8872-6761c456f01e\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"},{\"id\":2057,\"environmentId\":325,\"environment\":\"Sandbox\",\"apiKey\":\"f0387ab6-1316-4ae0-8872-8761c636f01e\",\"status\":\"active\",\"createdAt\":\"*************\",\"updatedAt\":\"*************\"}]}}".decodeJson[IdmAccountInfoResponse]

  "should be able to fetch account information by API key" - {
    Mockito.reset(underlying, storage)
    when(storage.get(cacheKey)).thenReturn(Future.successful(None))
    when(underlying.fetchIdmAccountInformation(apiKey)).thenReturn(Future.successful(Right(AccountInfoResponse)))
    when(storage.store(cacheKey, AccountInfoResponse)).thenReturn(Future.successful(()))
    whenReady(client.fetchIdmAccountInformation(apiKey)) { result =>
      result shouldBe Right(AccountInfoResponse)
      Mockito.verify(storage).get(cacheKey)
      Mockito.verify(underlying).fetchIdmAccountInformation(apiKey)
      Mockito.verify(storage).store(cacheKey, AccountInfoResponse)
    }
  }

  "should fetch account information by API key from cache" - {
    Mockito.reset(underlying, storage)
    when(storage.get(cacheKey)).thenReturn(Future.successful(Option(AccountInfoResponse)))
    whenReady(client.fetchIdmAccountInformation(apiKey)) { result =>
      result shouldBe Right(AccountInfoResponse)
      Mockito.verify(storage, times(1)).get(cacheKey)
      Mockito.verify(underlying, never).fetchIdmAccountInformation(apiKey)
      Mockito.verify(storage, never).store(cacheKey, AccountInfoResponse)
    }
  }
}
