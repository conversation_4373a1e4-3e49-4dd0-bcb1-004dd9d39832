package me.socure.account.client.setting

import dispatch.Http
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.constants.JsonFormats
import me.socure.model.account.WatchlistPreferenceValueGenerator
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, Creator, UserDetails}
import me.socure.model.kyc.{KycPreferences, KycPreferencesRequest, KycPreferencesResponse, KycPreferencesValueGenerator}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{EitherVal<PERSON>, <PERSON><PERSON>uit<PERSON>, Matchers}
import org.slf4j.{<PERSON><PERSON>, LoggerFactory}

import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.reflect.ClassTag

class AccountPreferenceClientImplTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit private val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val jsonFormats: Formats = JsonFormats.formats

  private val http = Http.default

  test("GET Watchlist should be error response") {
    withServlet(new ResourceServlet(400, "/settings-preference-watchlist-get-error.json"), "/settings/preferences/watchlist") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlist(12)) { res =>
        res should be('left)
      }
    }
  }

  test("GET Watchlist should be success response") {
    withServlet(new ResourceServlet(200, "/settings-preference-watchlist-get-success.json"), "/settings/preferences/watchlist") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      whenReady(client.getWatchlist(12)) { res =>
        res should be('right)
      }
    }
  }

  test("POST Watchlist should be error response") {
    val preference = WatchlistPreferenceValueGenerator.aWatchlistPreference()
    withServlet(new ResourceServlet(400, "/settings-preference-watchlist-post-error.json"), "/settings/preferences/watchlist") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      whenReady(client.setWatchlist(preference)) { res =>
        res should be('left)
      }
    }
  }



  def aSeqOf[T](maxSize: Int, generator: () => T)(implicit classTag: ClassTag[T]): Seq[T] = {
    Array.fill(Random.nextInt(maxSize)) { generator() }.toSeq
  }

  test("POST Watchlist should be success response") {
    val preference = WatchlistPreferenceValueGenerator.aWatchlistPreference()
    withServlet(new ResourceServlet(200, "/settings-preference-watchlist-post-success.json"), "/settings/preferences/watchlist") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      whenReady(client.setWatchlist(preference)) { res =>
        res should be('right)
      }
    }
  }

  test("kyc get success") {
    val kycPreferences = KycPreferencesValueGenerator.aKycPreferences()
    val environmentId = Random.nextLong()
    testSuccess[KycPreferences](s"/settings/preferences/kyc/$environmentId", kycPreferences) { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.getKyc(environmentId)
    }
  }

  test("kyc get failure") {
    val environmentId = Random.nextLong()
    testError(s"/settings/preferences/kyc/$environmentId") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.getKyc(environmentId)
    }
  }

  test("kyc save success") {
    val kycPreferences = KycPreferencesValueGenerator.aKycPreferences()
    val environmentId = Random.nextLong()
    testSuccessWithAudit[KycPreferences](s"/settings/preferences/kyc/$environmentId", kycPreferences) { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.saveKyc(environmentId, kycPreferences)
    }
  }

  test("kyc save failure") {
    val environmentId = Random.nextLong()
    testErrorWithAudit(s"/settings/preferences/kyc/$environmentId") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.saveKyc(environmentId, KycPreferencesValueGenerator.aKycPreferences())
    }
  }

  test("kyc save success for accounts") {
    val kycPreferences = KycPreferencesValueGenerator.aKycPreferences()
    val kycPreferencesRequest = KycPreferencesRequest(kycPreferences, Seq(1), Seq(1), Some(false))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails, KycPreferencesResponse(kycPreferences))).encodeJson()

    withServlet(new StringServlet(200, response), "/settings/preferences/kyc/accounts/1") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      whenReady(client.saveKycForAccounts(1L, kycPreferencesRequest)) { res =>
        res._2 shouldBe 'right
      }
    }
  }

  test("kyc save failure for accounts") {
    val environmentId = Random.nextLong()
    val kycPreferences = KycPreferencesValueGenerator.aKycPreferences()
    val kycPreferencesRequest = KycPreferencesRequest(kycPreferences.copy(creator = Some(Creator(1,1))), Seq(1), Seq(1), Some(false))
    testErrorWithAudit(s"/settings/preferences/kyc/accounts/$environmentId") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.saveKycForAccounts(environmentId, kycPreferencesRequest)
    }
  }

  test("kyc delete success") {
    val environmentId = Random.nextLong()
    testSuccess[Boolean](s"/settings/preferences/kyc/$environmentId", true) { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.deleteKyc(environmentId)
    }
  }

  test("kyc delete failure") {
    val environmentId = Random.nextLong()
    testError(s"/settings/preferences/kyc/$environmentId") { port =>
      val client = new AccountPreferenceClientImpl(http, s"http://localhost:$port")
      client.deleteKyc(environmentId)
    }
  }

  private def testError(path: String)(invoke: Int => Future[Either[ErrorResponse, _]]): Unit = {
    val errorResponse = ErrorResponse(
      code = Random.nextInt(),
      message = Random.alphaNumeric(10)
    )
    withServlet(new StringServlet(400, (Response(ResponseStatus.Error, errorResponse)).encodeJson()), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Left(errorResponse)
      }
    }
  }

  private def testSuccess[T](path: String, response: T)(invoke: Int => Future[Either[ErrorResponse, T]])(implicit m: Manifest[T]): Unit = {
    withServlet(new StringServlet(200, (Response(ResponseStatus.Ok, response)).encodeJson()), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Right(response)
      }
    }
  }

  private def testSuccessWithAudit[T](path: String, response: T)(invoke: Int => Future[(AuditDetails, Either[ErrorResponse, T])]): Unit = {
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val expectedResult = (auditDetails, response)
    val resp = (Response(ResponseStatus.Ok, expectedResult)).encodeJson()
    withServlet(new StringServlet(200, resp), path) { port =>
        whenReady(invoke(port)) { res =>
          res._2 shouldBe Right(response)
        }
      }
  }

  private def testErrorWithAudit(path: String)(invoke: Int => Future[(AuditDetails, Either[ErrorResponse, _])]): Unit = {
    val errorResponse = ErrorResponse(
      code = Random.nextInt(),
      message = Random.alphaNumeric(10)
    )
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(errorResponse), Seq.empty)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, (auditDetails, errorResponse)).encodeJson()), path) { port =>
      whenReady(invoke(port)) { res =>
        res._2 shouldBe Left(errorResponse)
      }
    }
  }
}
