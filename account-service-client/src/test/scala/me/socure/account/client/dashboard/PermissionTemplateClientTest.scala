package me.socure.account.client.dashboard

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{PermissionTemplateFetchFailed, PermissionTemplateCreateFailed, PermissionTemplateUpdateFailed, PermissionTemplateDeleteFailed}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{ErrorResponse, PermissionTemplate, Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

import scala.concurrent.ExecutionContext
import me.socure.util.JsonEnrichments._

class PermissionTemplateClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http = new NonSecuredHttpFactory().getHttpClient()

  test("Get Permission Template") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    val expected = PermissionTemplate(1, "Template1", 1, 1, 1, None)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.getPermissionTemplate(permissionTemplateId, userId, accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Get Permission Template error") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(PermissionTemplateFetchFailed))
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.getPermissionTemplate(permissionTemplateId, userId, accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(PermissionTemplateFetchFailed.id, PermissionTemplateFetchFailed.description)
      }
    }
  }

  test("Insert Permission Template") {
    val permissionTemplate = PermissionTemplate(0, "Template1", 1, 1, 1, None)
    val expected = 1
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.insertPermissionTemplate(permissionTemplate)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Insert Permission Template error") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(PermissionTemplateCreateFailed))
    val permissionTemplate = PermissionTemplate(0, "Template1", 1, 1, 1, None)
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.insertPermissionTemplate(permissionTemplate)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(PermissionTemplateCreateFailed.id, PermissionTemplateCreateFailed.description)
      }
    }
  }

  test("Update Permission Template") {
    val permissionTemplateId = 1
    val userId = 1
    val accountId = 1
    val expected = 1
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.deletePermissionTemplate(permissionTemplateId, userId, accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Update Permission Template error") {
    val userId = 1
    val accountId = 1
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(PermissionTemplateDeleteFailed))
    val permissionTemplateId = 1
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.deletePermissionTemplate(permissionTemplateId, userId, accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(PermissionTemplateDeleteFailed.id, PermissionTemplateDeleteFailed.description)
      }
    }
  }

  test("Delete Permission Template") {
    val permissionTemplate = PermissionTemplate(0, "Template1", 1, 1, 1, None)
    val expected = 1
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.updatePermissionTemplate(permissionTemplate)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Delete Permission Template error") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(PermissionTemplateUpdateFailed))
    val permissionTemplate = PermissionTemplate(0, "Template1", 1, 1, 1, None)
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.updatePermissionTemplate(permissionTemplate)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(PermissionTemplateUpdateFailed.id, PermissionTemplateUpdateFailed.description)
      }
    }
  }

  test("Get Permission Templates") {
    val userAccountAssociationId = 1
    val expected = Seq(PermissionTemplate(1, "Template1", 1, 1, 1, None))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.getPermissionTemplates(userAccountAssociationId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Get Permission Templates error") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(PermissionTemplateFetchFailed))
    val userAccountAssociationId = 1
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/permission_templates") { port =>
      val client: PermissionTemplateClient = new PermissionTemplateClientImpl(http, s"$host:$port")
      whenReady(client.getPermissionTemplates(userAccountAssociationId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(PermissionTemplateFetchFailed.id, PermissionTemplateFetchFailed.description)
      }
    }
  }


}
