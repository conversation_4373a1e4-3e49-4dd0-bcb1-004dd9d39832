package me.socure.account.client.superadmin

import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.client.dashboard2.PGPKeysManagementClientImpl
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class PGPKeysManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return accounts with pgp keys"){
    lazy val list = List(AccountPgpInfo(1, "acc-1234", "AccountName1",new DateTime("2017-12-06", DateTimeZone.UTC)), AccountPgpInfo(4, "acc-1235", "AccountName4",new DateTime("2017-12-06", DateTimeZone.UTC)))
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_active_pgp_account_list") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getActivePgpAccountList(), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }

  test("should generate pgp keys for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/create_pgp_keys/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.createPgpKeys(1l, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when create pgp key for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.accountNotFoundResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/create_pgp_keys/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.createPgpKeys(100l, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should get pgp public key for account"){
    val response = Response(ResponseStatus.Ok, "publicKey")
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_public_key/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountPgpPublicKey(1l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe "publicKey"
      }
    }
  }

  test("should throw error when get pgp public key file for invalid account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.accountNotFoundResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/get_public_key/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountPgpPublicKey(100l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should deactivate pgp keys for account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/deactivate_pgp_keys/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.deactivatePgpKeys(1l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should throw error when delete pgp signature public key for account is called") {
    val response = Response(ResponseStatus.Error, UserFixture.accountNotFoundResponse)
    withServlet(new StringServlet(400, response.encodeJson()),"/pgp/deactivate_pgp_keys/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.deactivatePgpKeys(100l), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should get_all_dec_and_sig_keys for account"){
    val response = Response(ResponseStatus.Ok, List.empty)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_all_dec_and_sig_keys/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getAllDecAndSigKeys(1L), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe List.empty
      }
    }
  }
}
