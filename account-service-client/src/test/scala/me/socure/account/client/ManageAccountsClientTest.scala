package me.socure.account.client

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound, InvalidInputFormat, UnknownError}
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by an<PERSON><PERSON><PERSON><PERSON> on 10/25/17.
  */
class ManageAccountsClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  test("create account should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/account/management/*") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.create(UserFixture.duplicateAccountCreationForm)) { res =>
        res should be('left)
      }
    }
  }

  test("create account should return successful response") {
    val expectedRes = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/management/*") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.create(UserFixture.accountCreationForm)) { res =>
        res should be('right)
      }
    }
  }

  test("get parent accounts") {
    val expectedRes = Response(ResponseStatus.Ok, List(UserFixture.parentAccount))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/account/management/*") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.getParentAccounts()) { res =>
        res should be('right)
      }
    }
  }

  test("updateAccountNameByPublicId should update account name by public id") {
    val publicId = Random.alphaNumeric(10)
    val accountName = Random.alphaNumeric(10)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 1).encodeJson()), "/account/management/update/account-name/by/public-id") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.updateAccountNameByPublicId(publicId = publicId, accountName = accountName)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("updateAccountNameByPublicId should return account not found error for an invalid public id") {
    val publicId = Random.alphaNumeric(10)
    val accountName = Random.alphaNumeric(10)
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/account/management/update/account-name/by/public-id") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.updateAccountNameByPublicId(publicId = publicId, accountName = accountName)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("updateAccountNameByPublicId should return invalid input format for an invalid account name") {
    val publicId = Random.alphaNumeric(10)
    val accountName = ""
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(InvalidInputFormat))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), "/account/management/update/account-name/by/public-id") { port =>
      val client: ManageAccountsClient = new ManageAccountsClientImpl(s"http://localhost:$port")
      whenReady(client.updateAccountNameByPublicId(publicId = publicId, accountName = accountName)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(InvalidInputFormat))
      }
    }
  }
}
