package me.socure.account.client.superadmin

import me.socure.constants.EnvironmentConstants
import me.socure.model.BusinessUserRoles
import me.socure.model.account.Subscription
import me.socure.model.user.authorization._

object AccountInfoClientTestObjects {
  val accountWithEnvironmentDetails = AccountWithEnvironmentDetails(
    account = Account(
      id = 20001,
      name = "Saml 2.0 Account",
      permission = Set(
        BusinessUserRoles.SAML_2_0.id,
        BusinessUserRoles.ADDRESS_RISK_SCORE.id
      ),
      isActive = true,
      isInternal = false,
      Set.empty
    ),
    environment = Set(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
  )

  val accountWithEnvironmentDetailsWithPublicId = AccountWithEnvironmentDetailsWithPublicId(
    account = AccountWithPublicId(
      id = 20001,
      publicId = "some-public-id-123",
      name = "Saml 2.0 Account",
      permission = Set(
        BusinessUserRoles.SAML_2_0.id,
        BusinessUserRoles.ADDRESS_RISK_SCORE.id
      ),
      isActive = true,
      isInternal = false,
      subscriptions = Set(Subscription(1, "Watchlist Monitoring")),
      false
    ),
    environment = Set(
      EnvironmentSettings(
        id = 200011,
        name = EnvironmentConstants.PRODUCTION_ENVIRONMENT.toString,
        socureKey = "200011_api_key_new"
      ),
      EnvironmentSettings(
        id = 200012,
        name = EnvironmentConstants.DEVELOPMENT_ENVIRONMENT.toString,
        socureKey = "200012_api_key_active"
      )
    )
  )
}
