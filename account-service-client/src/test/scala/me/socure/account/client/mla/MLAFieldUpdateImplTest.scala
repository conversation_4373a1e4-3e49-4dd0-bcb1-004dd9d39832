package me.socure.account.client.mla

import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ResourceServlet
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.model.mla.{MLAInputRequest, MLAResponse}
import org.scalatest.{FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

import scala.concurrent.ExecutionContext

class MLAFieldUpdateImplTest extends FunSuite with Matchers with ScalaFutures {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit val ec = ExecutionContext.global

  private val http = new NonSecuredHttpFactory().getHttpClient()

  test("should get a event record by ref id") {
    withServlet(new ResourceServlet(200, "/mla-input-req.json"), "/dashboard/mla/*") { port =>
      val client = new MLAFieldUpdateImpl(http = http, s"http://localhost:$port")
      whenReady(client.saveMLAFields(MLAInputRequest(
        "1234",
        "member-number",
        "security-code"
      ))) { response =>
        response.right.get shouldBe MLAResponse("763","999FZ05181","GG6")
      }
    }
  }

}
