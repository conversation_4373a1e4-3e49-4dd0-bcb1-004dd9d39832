package me.socure.account.client.setting

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.Constant
import me.socure.account.service.common.watchlist.ManageCAAccountsCacheKeyProvider
import me.socure.common.clock.FakeClock
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.account.CAWatchlistPreference
import me.socure.model.{Response, ResponseStatus, WatchlistSource}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.mockito.Mockito.{never, times}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{BeforeAndAfter, EitherValues, FunSuite, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedManageCAAccountsClientImplTest extends FunSuite with Matchers with ScalaFutures with EitherValues with MockitoSugar with BeforeAndAfter {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))
  implicit val ec = ExecutionContext.global
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  val host = "http://localhost"
  private val http = Http.default

  val cacheMock1 = mock[Storage[CAWatchlistPreference]]
  val cacheMock2 = mock[Storage[Seq[WatchlistSource]]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)

  val environmentId = 1

  val cacheKey1 = ManageCAAccountsCacheKeyProvider provideForGetCAWatchList environmentId
  val cacheKey2 = ManageCAAccountsCacheKeyProvider provideForGetWatchListSource environmentId

  val cacheResult1 = CAWatchlistPreference(
    environmentId = 123,
    exactDoB = Some(true),
    dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
    dobAndName = false,
    monitoring = false,
    matchingThresholds = 0.1,
    limit = 10,
    screeningCategories = Set("PEP"),
    watchlistScreeningCategories = None,
    country = Option(Set("US")),
    historicalRange = None
  )

  val cacheResult2 = Seq(WatchlistSource(
    id = 100,
    name = "Source 1",
    category = "category 1",
    subCategory = "sub category 1",
    location = Some("location 1"),
    createAt = clock.now()
  ))

  val response1 = Response(ResponseStatus.Ok, cacheResult1)
  val response2 = Response(ResponseStatus.Ok, cacheResult2)

  before {
    Mockito.reset(cacheMock1)
    Mockito.reset(cacheMock2)
  }

  test("getCAWatchList - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedManageCAAccountsClientImpl(http, host, cacheMock1, cacheMock2, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock1.get(cacheKey1)).thenReturn(Future.successful(Some(cacheResult1)))

    whenReady(cachedClient.getCAWatchList(environmentId)) { response =>
      response.fold(_ => fail, _ shouldBe cacheResult1)
      Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
      Mockito.verify(cacheMock1, never()).store(cacheKey1, cacheResult1)
      Mockito.verifyNoMoreInteractions(cacheMock2)
    }
  }

  test("getCAWatchList - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock1.get(cacheKey1)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock1.store(cacheKey1, cacheResult1)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response1.encodeJson()), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getCAWatchList(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult1)
        Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
        Mockito.verify(cacheMock1, times(1)).store(cacheKey1, cacheResult1)
        Mockito.verifyNoMoreInteractions(cacheMock2)
      }
    }
  }

  test("getCAWatchList - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock1.get(cacheKey1)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock1.store(cacheKey1, cacheResult1)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response1.encodeJson()), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getCAWatchList(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult1)
        Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
        Mockito.verify(cacheMock1, times(1)).store(cacheKey1, cacheResult1)
        Mockito.verifyNoMoreInteractions(cacheMock2)
      }
    }
  }

  test("getCAWatchList - should interact with the real client when memcached timedout") {
    Mockito.when(cacheMock1.get(cacheKey1))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock1.store(cacheKey1, cacheResult1))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response1.encodeJson()), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getCAWatchList(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult1)
        Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
        Mockito.verify(cacheMock1, times(1)).store(cacheKey1, cacheResult1)
        Mockito.verifyNoMoreInteractions(cacheMock2)
      }
    }
  }

  test("getCAWatchList - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock1.get(cacheKey1)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock1.store(cacheKey1, cacheResult1)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response1.encodeJson()), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getCAWatchList(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult1)
        Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
        Mockito.verify(cacheMock1, times(1)).store(cacheKey1, cacheResult1)
        Mockito.verifyNoMoreInteractions(cacheMock2)
      }
    }
  }

  test("getCAWatchList - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock1.get(cacheKey1)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock1.store(cacheKey1, cacheResult1)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response1.encodeJson()), "/settings/preferences/ca/watchlist/3.0") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getCAWatchList(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult1)
        Mockito.verify(cacheMock1, times(1)).get(cacheKey1)
        Mockito.verify(cacheMock1, times(1)).store(cacheKey1, cacheResult1)
        Mockito.verifyNoMoreInteractions(cacheMock2)
      }
    }
  }

  test("getWatchlistSource - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedManageCAAccountsClientImpl(http, host, cacheMock1, cacheMock2, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock2.get(cacheKey2)).thenReturn(Future.successful(Some(cacheResult2)))

    whenReady(cachedClient.getWatchlistSource(environmentId)) { response =>
      response.fold(_ => fail, _ shouldBe cacheResult2)
      Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
      Mockito.verify(cacheMock2, never()).store(cacheKey2, cacheResult2)
      Mockito.verifyNoMoreInteractions(cacheMock1)
    }
  }

  test("getWatchlistSource - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock2.get(cacheKey2)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock2.store(cacheKey2, cacheResult2)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getWatchlistSource(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult2)
        Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
        Mockito.verify(cacheMock2, times(1)).store(cacheKey2, cacheResult2)
        Mockito.verifyNoMoreInteractions(cacheMock1)
      }
    }
  }

  test("getWatchlistSource - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock2.get(cacheKey2)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock2.store(cacheKey2, cacheResult2)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getWatchlistSource(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult2)
        Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
        Mockito.verify(cacheMock2, times(1)).store(cacheKey2, cacheResult2)
        Mockito.verifyNoMoreInteractions(cacheMock1)
      }
    }
  }

  test("getWatchlistSource - should interact with the real client when memcached timed out") {
    Mockito.when(cacheMock2.get(cacheKey2))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock2.store(cacheKey2, cacheResult2))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response2.encodeJson()), "/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, Some(futureTimeout, Duration(50, TimeUnit.MILLISECONDS)))

      whenReady(client.getWatchlistSource(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult2)

        // Verify cache interactions
        Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
        Mockito.verify(cacheMock2, times(1)).store(cacheKey2, cacheResult2)
        Mockito.verifyNoMoreInteractions(cacheMock1)
      }
    }
  }


  test("getWatchlistSource - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock2.get(cacheKey2)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock2.store(cacheKey2, cacheResult2)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response2.encodeJson()), "/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, None)
      whenReady(client.getWatchlistSource(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult2)
        Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
        Mockito.verify(cacheMock2, times(1)).store(cacheKey2, cacheResult2)
        Mockito.verifyNoMoreInteractions(cacheMock1)
      }
    }
  }

  test("getWatchlistSource - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock2.get(cacheKey2)).thenReturn(Future.successful(None))

    Mockito.when(cacheMock2.store(cacheKey2, cacheResult2)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response2.encodeJson()), "/settings/preferences/ca/watchlist/sources/included") { port =>
      val client = new CachedManageCAAccountsClientImpl(http, s"$host:$port", cacheMock1, cacheMock2, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getWatchlistSource(environmentId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult2)
        Mockito.verify(cacheMock2, times(1)).get(cacheKey2)
        Mockito.verify(cacheMock2, times(1)).store(cacheKey2, cacheResult2)
        Mockito.verifyNoMoreInteractions(cacheMock1)
      }
    }
  }

}
