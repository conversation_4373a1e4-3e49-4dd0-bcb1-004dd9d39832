package me.socure.account.client.dashboard

import dispatch.Http
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccessForbidden, InvalidModules, UnknownError}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.dashboardv2.{ActionUserInfo, AuditDetails, UserDetails}
import me.socure.model.{ModulesResponse, Response, ResponseStatus}

import scala.concurrent.ExecutionContext
class ModuleManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("should get default modules for accounts") {
    val modules = ModulesResponse(Set(1,2,3),None, Some(false))
    val result = Response(ResponseStatus.Ok, modules)
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/accounts/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.getDefaultModulesForAccounts(1, 1L, 1L)) { response =>
        response.fold(_ => fail, m => {
          m.modules.nonEmpty shouldBe true
          m.modules.size shouldBe modules.modules.size
        })
      }
    }
  }

  test("should get default modules") {
    val modules = Set(1, 2, 3)
    val result = Response(ResponseStatus.Ok, modules)
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/default/*") { port =>
      val client: ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.getDefaultModules(1, 1L, 1L)) { response =>
        response.fold(_ => fail, m => {
          m.nonEmpty shouldBe true
          m.size shouldBe modules.size
        })
      }
    }
  }

  test("should return error response for get default modules") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/modules/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.getDefaultModules(100, 1L, 1L)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should get modules") {
    val modules = Set("rff-vk5ImDnyZ9")
    val result = Response(ResponseStatus.Ok, modules)
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.getModules(1, 1L)) { response =>
        response.fold(_ => fail, m => {
          m.nonEmpty shouldBe true
          m.size shouldBe modules.size
        })
      }
    }
  }

  test("should return error response for get modules") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/modules/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.getModules(100, 1L)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should save default modules") {
    val modules = Set(1,2,3)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val result = Response(ResponseStatus.Ok, (auditDetails, true))
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.saveDefaultModules(1, modules, 1L, 1L)) { response =>
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should save default modules for multiple accounts") {
    val modules = Set(1, 2, 3)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val result = Response(ResponseStatus.Ok, (auditDetails, true))
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/accounts/default") { port =>
      val client: ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.saveDefaultModulesForAccounts(Seq(1), modules, false, 1L, 1L )) { response =>
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response for save default modules") {
    val error = ErrorResponseFactory.get(InvalidModules)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    val expectedRes = Response(ResponseStatus.Error, (auditDetails,error))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/modules/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.saveDefaultModules(100, Set(1,2,3), 1L, 1L)) { response =>
        response._2.fold(_ shouldBe ErrorResponseFactory.get(InvalidModules), _ => fail)
      }
    }
  }

  test("should clear default modules") {
    val result = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, result.encodeJson()), "/modules/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.clearDefaultModules(1, 1L, 1L)) { response =>
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response for clear default modules") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/modules/default/*") { port =>
      val client : ModuleManagementClient = new ModuleManagementClientImpl(http, s"$host:$port")
      whenReady(client.clearDefaultModules(100, 1L, 1L)) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }
}
