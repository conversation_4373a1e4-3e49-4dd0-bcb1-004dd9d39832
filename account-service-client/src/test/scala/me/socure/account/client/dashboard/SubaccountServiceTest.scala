package me.socure.account.client.dashboard

import com.typesafe.config.ConfigFactory
import me.socure.account.client.dashboard.SubAccountService.SubAccountHttpClient
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.EnvironmentNotFound
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.model.account.AccountInfo
import me.socure.model.{Response, ResponseStatus}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}


/**
 * Created by joonkang on 7/15/16.
 */
class SubaccountServiceTest extends FunSuite with Matchers with ScalaFutures with BeforeAndAfter {
  implicit val ec = scala.concurrent.ExecutionContext.Implicits.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  import me.socure.util.JsonEnrichments._

  test("should call mock service") {
    withServlet(new ResourceServlet(200, "/example_subaccounts_success.json"),
      "/dashboard/subaccounts_for_id/2") { port =>

      val config = ConfigFactory.empty().withFallback(ConfigFactory.parseString(
        s"""{ accountService { endpoint = "http://localhost:$port"   }   }""".stripMargin))

      val subAccountService =new SubAccountHttpClient(config.getString("accountService.endpoint"))

      val res = subAccountService.findSubAccountsForAccountId(2)

      whenReady(res) {
        p ⇒ p.size shouldBe 3
      }
    }
  }

  test("should miss mock service and blow up") {
    withServlet(new ResourceServlet(200, "/example_subaccounts_success.json"),
      "/dashboard/subaccounts_for_id/2") { port =>

      val config = ConfigFactory.empty().withFallback(ConfigFactory.parseString(
        s"""{ accountService { endpoint = "http://localhost:$port"   }   }""".stripMargin))

      val subAccountService =new SubAccountHttpClient(config.getString("accountService.endpoint"))
      val retval = subAccountService.findSubAccountsForAccountId(7)
        whenReady(retval.failed) {
          p ⇒ {
            p shouldBe an [Exception]
            p.getMessage.startsWith("Query Failure") shouldBe true
          }
        }
      }
  }

  test("should return accountInfo when get_account_info_for_environment called") {
    val expectedRes = Response(ResponseStatus.Ok,  AccountInfo(1l, "account", Some(7l)))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard_account/get_account_info_for_environment/*") { port =>
      val client = new SubAccountHttpClient(s"http://localhost:$port")
      whenReady(client.getAccountInfoForEnvironment(9l)) { res =>
        res should be('right)
      }
    }
  }

  test("should return error when get_account_info_for_environment called"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/get_account_info_for_environment/*") { port =>
      val client = new SubAccountHttpClient(s"http://localhost:$port")
      whenReady(client.getAccountInfoForEnvironment(99l)) { res =>
        res should be ('left)
      }
    }
  }

}
