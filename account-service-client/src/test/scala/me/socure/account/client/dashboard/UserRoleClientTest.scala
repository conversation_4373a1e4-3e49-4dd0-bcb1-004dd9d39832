package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, PublicExceptionCodes}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{ Response, ResponseStatus, UsersAndRoles}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.ExecutionContext

class UserRoleClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("Should get count of users and roles for given account - success") {
    val accountIds = Set(1L, 2L)
    val expected = 10
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/roles/get_users_and_roles_for_accounts/total_count") { port =>
      val client: UserRoleClientImpl = new UserRoleClientImpl(http, s"$host:$port")
      whenReady(client.getUsersAndRolesRecordCountForAccounts(accountIds)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should get count of users and roles for given account - error") {
    val accountIds = Set(1L, 2L)
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/roles/get_users_and_roles_for_accounts/total_count") { port =>
      val client: UserRoleClientImpl = new UserRoleClientImpl(http, s"$host:$port")
      whenReady(client.getUsersAndRolesRecordCountForAccounts(accountIds)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Should get users and roles for given account - success") {
    val accountIds = Set(1L, 2L)
    val expected = Seq(
      UsersAndRoles(1L, 2L, 3L, "test", "Test", "User", "<EMAIL>", "+1-**********", "Developer, Admin 1", 1)
    )
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/roles/get_users_and_roles_for_accounts") { port =>
      val client: UserRoleClientImpl = new UserRoleClientImpl(http, s"$host:$port")
      whenReady(client.getUsersAndRolesByAccountIds(accountIds, None, None, None)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should get users and roles for given account - error") {
    val accountIds = Set(1L, 2L)
    val expected = ErrorResponseFactory.get(PublicExceptionCodes.AccountIdsEmpty)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/roles/get_users_and_roles_for_accounts") { port =>
      val client: UserRoleClientImpl = new UserRoleClientImpl(http, s"$host:$port")
      whenReady(client.getUsersAndRolesByAccountIds(accountIds, None, None, None)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

}
