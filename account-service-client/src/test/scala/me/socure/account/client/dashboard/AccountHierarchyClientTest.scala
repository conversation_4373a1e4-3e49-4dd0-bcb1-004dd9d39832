package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountInfoV2WithIndustry
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}
import me.socure.util.JsonEnrichments._

class AccountHierarchyClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()

  test("Should update primary admin count - success") {
    val accountId = 1L
    val count = 1
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/update/admin_count") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.updatePrimaryAdminCount(accountId, count)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should update primary admin count - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/update/admin_count") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.updatePrimaryAdminCount(accountId, 1)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should update status: activate - success") {
    val accountId = 1L
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/activate") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.activate(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should update status: activate - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/activate") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.activate(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should update status: deactivate - success") {
    val accountId = 1L
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/deactivate") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.deactivate(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should update status: deactivate - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/deactivate") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.deactivate(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should update administer: allow - success") {
    val accountId = 1L
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/administer/allow") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.allowAdminister(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should update administer: allow - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/administer/allow") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.allowAdminister(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should update administer: deny - success") {
    val accountId = 1L
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/administer/deny") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.denyAdminister(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should update administer: deny - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/administer/deny") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.denyAdminister(accountId)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should list account hierarchy - success") {
    val accountId = 1L
    val userId = 1L
    val expected = List(AccountInfoV2WithIndustry(5,14,"acc-db3y72Tg30","AccountName14","14/",2,administer = true,0, state = true, "industry", true), AccountInfoV2WithIndustry(6,15,"acc-Kf3habgWE6","AccountName15","14/15/",2,administer = true,0, state = true, "industry", true))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/list") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.list(accountId, userId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Should validate account access permission - failure") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val accountId = 11L
    val creatorAccountId = 11L
    val permissions = Set(13)
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/hierarchy/validate/access/permissions/*") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.validateAccountAccess(accountId, creatorAccountId, permissions)) { response =>
        response shouldBe 'left
        response.left.value shouldBe ErrorResponse(UnknownError.id, UnknownError.description)
      }
    }
  }

  test("Should validate account access permission - success") {
    val accountId = 1L
    val creatorAccountId = 11L
    val permissions = Set(13)
    val expected = true
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/validate/access/permissions") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.validateAccountAccess(accountId, creatorAccountId, permissions)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Get root parent - success") {
    val accountId = 1L
    val expected = 1L
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/get_root_parent/1") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.getRootParent(accountId)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }

  test("Get Sub Accounts - success") {
    val accountId = 1L
    val expected = Set(2L, 3L)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/hierarchy/subaccounts/1") { port =>
      val client: AccountHierarchyClient = new AccountHierarchyClientImpl(http, s"$host:$port")
      whenReady(client.getSubAccounts(accountId)) { response =>
        response.fold(_ => fail, r => {
          r.nonEmpty shouldBe true
        })
      }
    }
  }

}
