package me.socure.account.client.setting

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.SubscriptionChannelRegistryCacheKeyProvider
import me.socure.account.service.common.subscription.{ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.common.publicid.PublicId
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, SubscriptionChannelRegistryWithAccount}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.mockito.Mockito
import org.mockito.Mockito.{never, times}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedSubscriptionChannelRegistryClientImplTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))
  implicit val ec = ExecutionContext.global

  val host = "http://localhost"
  private val http = Http.default

  val cacheMock = mock[Storage[SubscriptionChannelRegistryWithAccount]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)

  val accountId = 1
  val environmentTypeId = 1
  val subscriptionTypeId = 1
  val featureTypeId = Some(1)

  val urlPath = s"/settings/channel/account/$accountId/environment/type/$environmentTypeId/subscription/$subscriptionTypeId"

  val cacheKey = SubscriptionChannelRegistryCacheKeyProvider provide(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)
  
  val firstRegistryMock = DtoSubscriptionChannelRegistry(
    communicationSource = "<EMAIL>",
    metadata = Map.empty,
    status = SubscriptionChannelRegistryStatus.ACTIVE.id,
    subscriptionTypeId = subscriptionTypeId,
    environmentId = 1L,
    channelType = ChannelType.PRIMARY.id,
    communicationMode = CommunicationMode.EMAIL.id,
    createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
    secretKey = Some("secret_key_12345"),
    featureTypes = Set(1)
  )
  
  val cacheResult = SubscriptionChannelRegistryWithAccount(
    subscriptions = Seq(firstRegistryMock),
    publicAccountId = PublicId(1.toString),
    isProvisioned = true,
    secretKey = Some("secret_key_12345")
  )

  val response = Response(ResponseStatus.Ok, cacheResult)

  before {
    Mockito.reset(cacheMock)
  }

  test("getSubscriptionChannelRegistryWithAccount - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedSubscriptionChannelRegistryClientImpl(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(cacheResult)))

    whenReady(cachedClient.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
      response.fold(_ => fail, _ shouldBe cacheResult)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, cacheResult)
    }
  }

  test("getSubscriptionChannelRegistryWithAccount - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedSubscriptionChannelRegistryClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getSubscriptionChannelRegistryWithAccount - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedSubscriptionChannelRegistryClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getSubscriptionChannelRegistryWithAccount - should interact with the real client when memcached timedout") {
    Mockito.when(cacheMock.get(cacheKey))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedSubscriptionChannelRegistryClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getSubscriptionChannelRegistryWithAccount - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedSubscriptionChannelRegistryClientImpl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getSubscriptionChannelRegistryWithAccount - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedSubscriptionChannelRegistryClientImpl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId, featureTypeId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

}
