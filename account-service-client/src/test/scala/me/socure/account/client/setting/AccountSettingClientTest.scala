package me.socure.account.client.setting

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.constants.JsonFormats
import me.socure.model.account.{AccountApiKeys, ApiKeyDetails, ApiKeyStatus, EnvironmentWithAccount}
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.json4s.Formats
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext.Implicits.global

/**
  * Created by sun<PERSON>raj on 5/18/16.
  */
class AccountSettingClientTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  implicit def jsonFormats: Formats = JsonFormats.formats

  val host : String = "http://localhost"

  test("should be error response") {
    withServlet(new ResourceServlet(400, "/update-domain-error.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"http://localhost:$port")
      whenReady(client.updateDomain(12, List("0.0.0.0/0"))) { res =>
        res should be ('left)
      }
    }
  }

  test("should return true response") {
    withServlet(new ResourceServlet(200, "/update-domain-success.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"http://localhost:$port")
      whenReady(client.updateDomain(12, List("**********/0"))) { res =>
        res shouldBe 'right
      }
    }
  }

  test("should get account setting") {
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys).encodeJson()), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getAccountSettings(1)) { res =>
        res.right.value.accountId shouldBe 1
      }
    }
  }

  test("should get error response") {
    withServlet(new ResourceServlet(400, "/account-setting-error.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getAccountSettings(22)) { res =>
        res shouldBe 'left
      }
    }
  }

  test("should get new account settings") {
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, UserFixture.accountSettingsWithApiKeys).encodeJson()), "/settings/new/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getNewAccountSettings(1)) { res =>
        res.right.value.accountId shouldBe 1
      }
    }
  }

  test("should get error response for new account settings") {
    withServlet(new ResourceServlet(400, "/account-setting-error.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getNewAccountSettings(22)) { res =>
        res shouldBe 'left
      }
    }
  }

  test("delete account cache should return error response") {
    withServlet(new ResourceServlet(400, "/delete-account-cache-error.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.deleteAccountCache(100)) { res =>
        res.left.value.code shouldBe 104
      }
    }
  }

  test("delete account cache should return success") {
    withServlet(new ResourceServlet(200, "/delete-account-cache-success.json"), "/setting/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port/setting")
      whenReady(client.deleteAccountCache(101)) { res =>
        res.right.value shouldBe true
      }
    }
  }

  test("update or insert account cache should return success") {
    withServlet(new ResourceServlet(200, "/upsert-account-cache-success.json"), "/setting/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port/setting")
      whenReady(client.upsertAccountCache(10, DateTime.now)) { res =>
        res.right.value shouldBe true
      }
    }
  }

  test("update or insert account cache error response") {
    withServlet(new ResourceServlet(400, "/upsert-account-cache-error.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.upsertAccountCache(11, DateTime.now)) { res =>
        res.left.value.code shouldBe 105
      }
    }
  }

  test("delete social key should return key not found") {
    withServlet(new ResourceServlet(400, "/delete-social-key-errora.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.deleteSocialNetworkKey(1)) { res =>
        res.left.value.code shouldBe 110
      }
    }
  }

  test("delete social key should fail") {
    withServlet(new ResourceServlet(400, "/delete-social-key-errorb.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.deleteSocialNetworkKey(2)) { res =>
        res.left.value.code shouldBe 107
      }
    }
  }

  test("delete social key should return success") {
    withServlet(new ResourceServlet(200, "/delete-social-key-success.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.deleteSocialNetworkKey(3)) { res =>
        res.right.value shouldBe true
      }
    }
  }

  test("upsert social key should be successful") {
    withServlet(new ResourceServlet(200, "/upsert-social-key-success.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.upsertSocialNetworkKey(22, "provider", "appkey", "appsecret", 2)) { res =>
        res.right.value shouldBe true
      }
    }
  }

  test("upsert social key should return account not found error reponse") {
    withServlet(new ResourceServlet(400, "/upsert-social-key-errora.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.upsertSocialNetworkKey(22, "provider", "appkey", "appsecret", 1)) { res =>
        res.left.value.code shouldBe 100
      }
    }
  }

  test("upsert social key should fail") {
    withServlet(new ResourceServlet(400, "/upsert-social-key-errorb.json"), "/settings/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.upsertSocialNetworkKey(22, "provider", "appkey", "appsecret", 3)) { res =>
        res.left.value.code shouldBe 108
      }
    }
  }

  test("should list environments with account") {
    val result = Response(ResponseStatus.Ok, List(EnvironmentWithAccount(1, "name", "email", 2, 1), EnvironmentWithAccount(1, "name", "email", 3, 2)))
    withServlet(new StringServlet(200, result.encodeJson()), "/settings/get_all_env") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getAllEnvironmentWithAccount) { res =>
        res.right.value shouldBe result.data
      }
    }
  }

  test("should return error response on getting environments list with account") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RecordsNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/settings/get_all_env") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getAllEnvironmentWithAccount) { res =>
        res.left.value.code shouldBe expectedRes.data.code
      }
    }
  }

  test("should list api keys for account and its sub account") {
    val accountWithApiKeys = List(
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, true, None)),17,"Production"),
      AccountApiKeys(14, "AccountName14",Vector(ApiKeyDetails("1916ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, true, None)),19,"Production"),
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, true,None)),18,"Development"))
    val result = Response(ResponseStatus.Ok, accountWithApiKeys)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/get_apikeys_for_account_and_subaccounts/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForAccountAndSubAccounts(1l)) { res =>
        res.right.value shouldBe result.data
      }
    }
  }

  test("should return error response on getting apikeys for account and its sub account") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/get_apikeys_for_account_and_subaccounts/*") { port =>
      val client : AccountSettingClient = new AccountSettingClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForAccountAndSubAccounts(999l)) { res =>
        res.left.value.code shouldBe expectedRes.data.code
      }
    }
  }

}
