package me.socure.account.client.dashboard

import dispatch.Http
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.ProductProvisioningTypes
import me.socure.model._
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class ProductClientTest extends FunSuite with Matchers with ScalaFutures {

  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  private val http = Http.default

  test("should get Account Products - success") {
    val response = Seq(AccountProducts(1, "Watchlist-3.0", 65, ProductProvisioningTypes.CASCADE, None, 1, false, false, false, None, None, None, None))
    val result = Response(ResponseStatus.Ok, response)
    withServlet(new StringServlet(200, result.encodeJson()), "/products/account/1") { port =>
      val client: ProductClient = new ProductClientImpl(http, s"$host:$port")
      whenReady(client.getProductsForAccount(1L)) { response =>
        response.fold(_ => fail, res => {
          res shouldBe result.data
        })
      }
    }
  }

  test("should get Account Products - failure") {
    val expResponse = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)
    val result = Response(ResponseStatus.Error, expResponse)
    withServlet(new StringServlet(400, result.encodeJson()), "/products/account/1") { port =>
      val client: ProductClient = new ProductClientImpl(http, s"$host:$port")
      whenReady(client.getProductsForAccount(1L)) { response =>
        response.fold(_ shouldBe expResponse, _ => fail)
      }
    }
  }

  test("should update Account Products - success") {

    val request = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true)))
    val response = true
    val result = Response(ResponseStatus.Ok, response)

    withServlet(new StringServlet(200, result.encodeJson()), "/products/account") { port =>
      val client: ProductClient = new ProductClientImpl(http, s"$host:$port")
      whenReady(client.updateProductsForAccount(request)) { response =>
        response.fold(_ => fail, res => {
          res shouldBe result.data
        })
      }
    }
  }

  test("should update Account Products - failure") {
    val request = UpdateProductsRequest("updatedBy", 1, Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true)))
    val exResponse = ErrorResponseFactory.get(ExceptionCodes.AccountNotFound)

    val result = Response(ResponseStatus.Error, exResponse)
    withServlet(new StringServlet(400, result.encodeJson()), "/products/account") { port =>
      val client: ProductClient = new ProductClientImpl(http, s"$host:$port")
      whenReady(client.updateProductsForAccount(request)) { response =>
        response.fold(_ shouldBe exResponse, _ => fail)
      }
    }
  }

}
