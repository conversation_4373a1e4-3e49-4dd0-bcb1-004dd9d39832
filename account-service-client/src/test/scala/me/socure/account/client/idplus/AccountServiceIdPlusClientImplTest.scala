package me.socure.account.client.idplus

import me.socure.account.service.common.Constant
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.{ResourceServlet, StringServlet}
import me.socure.model._
import me.socure.model.account._
import me.socure.model.cache.{AccountIndividualCache, AccountOverallCache}
import me.socure.model.ein.EIN
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.{KycAddressMatchLogic, KycPreferences, KycPreferencesValueGenerator}
import me.socure.model.mla.MLAFields
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

/**
  * Created by alexandre on 5/17/16.
  */
class AccountServiceIdPlusClientImplTest extends FunSuite with Matchers with ScalaFutures {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  private implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  test("should produce correct response when error is returned") {

    withServlet(new ResourceServlet(400, "/idplus-error.json"), "/idplus/fetch_account_information_v2/*") { port =>
      val client = new AccountServiceIdPlusClientImpl(s"http://localhost:$port")

      whenReady(client.fetchByApiKey("key")) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(100, "the error"), _ => fail)
      }
    }
  }

  test("should call correctly when api key contains spaces") {

    withServlet(new ResourceServlet(400, "/idplus-error.json"), "/idplus/fetch_account_information_v2/*") { port =>
      val client = new AccountServiceIdPlusClientImpl(s"http://localhost:$port")

      whenReady(client.fetchByApiKey("key with space")) { response =>
        response shouldBe 'left
        response.fold(_ shouldBe ErrorResponse(100, "the error"), _ => fail)
      }
    }
  }

  test("should produce correct response when success is returned") {

    val expected = AccountInformation(
      active = true,
      accountId = "1",
      publicId= "1",
      accountName = "accountName",
      industry = Industry(
        sector = "some sector",
        description = "some industry"),
      isInternal = true,
      environment = Environment(
        id = 456,
        name = "environment",
        domain = Set("domain1", "domain2"),
        publicApiKeys = Seq.empty,
        accessCredentials = AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret key",
          accessToken = "access token",
          accessTokenSecret = "access token secret",
          certificate = "certificate"
        ),
        socialAccounts = Seq(
          SocialNetworkAppKeys(
            id = 145,
            provider= "provider",
            appkey = "app key",
            appsecret = "app secret",
            environment = 566,
            accountId = 999
          )
        ),
        invidiualCache = Seq(
          AccountIndividualCache(
            id = 666L,
            date = new DateTime(0L).withZone(DateTimeZone.UTC),
            identifier = "identifier1",
            accountId = 876L
          )
        ),
        overallCache = Some(AccountOverallCache(
          id = 234L,
          date = new DateTime(0L).withZone(DateTimeZone.UTC),
          accountId = 8765L))
      ),
      watchlistPreference = WatchlistPreference(
        environmentId = 100,
        exactDoB = true,
        dobAndName = false,
        matchScore = 90,
        categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
      ),
      watchlistPreference_3_0 =  CAWatchlistPreference(
        environmentId = 100,
        exactDoB = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName = false,
        monitoring = false,
        matchingThresholds = 0.5,
        limit = 10,
        screeningCategories = Set("pep"),
        watchlistScreeningCategories = None,
        country = Option(Set("UK")),
        historicalRange = None
      ),
      watchlistPreferences_3_0 =  CAWatchlistPreferences(
         standard = CAWatchlistPreference(
           environmentId = 100,
           exactDoB = Some(true),
           dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
           dobAndName = false,
           monitoring = false,
           matchingThresholds = 0.5,
           limit = 10,
           screeningCategories =  Set("warning"),
           watchlistScreeningCategories = None,
           country = Option(Set("US")),
           historicalRange = None
         ),
        plus = CAWatchlistPreference(
            environmentId = 100,
            exactDoB = Some(true),
            dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
            dobAndName = false,
            monitoring = false,
            matchingThresholds = 0.5,
            limit = 10,
            screeningCategories =  Set("warning"),
            watchlistScreeningCategories = None,
            country = Option(Set("US")),
          historicalRange = None
          ),
        premier = CAWatchlistPreference(
            environmentId = 100,
            exactDoB = Some(true),
            dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
            dobAndName = false,
            monitoring = false,
            matchingThresholds = 0.5,
            limit = 10,
            screeningCategories =  Set("warning"),
            watchlistScreeningCategories = None,
            country = Option(Set("US")),
          historicalRange = None
          )
        ),
      roles = Set(
        BusinessUserRoles.WATCHLIST.id
      ),
      primaryFraudModel = Some(FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
        lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 123,
          identifier = "identifier",
          name = "name",
          url = "url",
          version = "123",
          createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
          lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
        )
      ),
      kycPreferences = KycPreferences(
        exactDob = Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        ssnExactMatch = true
      ),
      subscriptionTypeIds = Seq(),
      includedWatchlistSources = Seq(),
      dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
      consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
      externalId = None,
      mlaField = MLAFields("", ""),
      ein = None,
      webhookNotificationPreferences = Seq()
    )

    withServlet(new ResourceServlet(200, "/idplus-success.json"), "/idplus/fetch_account_information_v2/key") { port =>
      val client = new AccountServiceIdPlusClientImpl(s"http://localhost:$port")

      whenReady(client.fetchByApiKey("key")) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("should get account info by public apikey") {
    val expected = AccountInformation(
      publicId = "1",
      active = true,
      accountId = "1",
      accountName = "accountName",
      industry = Industry(
        sector = "some sector",
        description = "some industry"),
      isInternal = true,
      environment = Environment(
        id = 456,
        publicApiKeys = Seq.empty,
        name = "environment",
        domain = Set("domain1", "domain2"),
        accessCredentials = AccessCredentials(
          apiKey = "api-key",
          secretKey = "secret key",
          accessToken = "access token",
          accessTokenSecret = "access token secret",
          certificate = "certificate"
        ),
        socialAccounts = Seq(
          SocialNetworkAppKeys(
            id = 145,
            provider= "provider",
            appkey = "app key",
            appsecret = "app secret",
            environment = 566,
            accountId = 999
          )
        ),
        invidiualCache = Seq(
          AccountIndividualCache(
            id = 666L,
            date = new DateTime(0L).withZone(DateTimeZone.UTC),
            identifier = "identifier1",
            accountId = 876L
          )
        ),
        overallCache = Some(AccountOverallCache(
          id = 234L,
          date = new DateTime(0L).withZone(DateTimeZone.UTC),
          accountId = 8765L))
      ),
      watchlistPreference = WatchlistPreference(
        environmentId = 100,
        exactDoB = true,
        dobAndName = false,
        matchScore = 90,
        categories = Seq(WatchlistCategories.Sanctions, WatchlistCategories.Enforcement, WatchlistCategories.PEP)
      ),
      watchlistPreference_3_0 =  CAWatchlistPreference(
        environmentId = 100,
        exactDoB= Some(true),
        dobMatchLogic = Some(Constant.DEFAULT_DOB_MATCH_LOGIC_EXACT_YYYY),
        dobAndName= false,
        monitoring= false,
        matchingThresholds= 70,
        limit= 10,
        screeningCategories=  Set("PEP"),
        watchlistScreeningCategories = Option(Set("PEP")),
        country= Some(Set("US")),
        historicalRange = None
      ), watchlistPreferences_3_0 =  CAWatchlistPreferences(
        standard = null,
        plus = null,
        premier = null
      ),
      roles = Set(
        BusinessUserRoles.WATCHLIST.id
      ),
      primaryFraudModel = Some(FraudModel(
        id = 123,
        identifier = "identifier",
        name = "name",
        url = "url",
        version = "123",
        createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
        lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
      )),
      fraudModels = Set(
        FraudModel(
          id = 123,
          identifier = "identifier",
          name = "name",
          url = "url",
          version = "123",
          createdDate = new DateTime(0L).withZone(DateTimeZone.UTC),
          lastUpdated = new DateTime(0L).withZone(DateTimeZone.UTC)
        )
      ),
      kycPreferences = KycPreferencesValueGenerator.aKycPreferences().copy(addressMatchLogic = Some(KycAddressMatchLogic.default)),
      subscriptionTypeIds = Seq(),
      includedWatchlistSources = Seq(),
      dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
      consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
      externalId = None,
      mlaField = MLAFields("memberNumber", "securityCode"),
      ein = Some(EIN("*********")),
      webhookNotificationPreferences = Seq()
    )
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/idplus/fetch_account_information_by_public_api_key/key") { port =>
      val client = new AccountServiceIdPlusClientImpl(s"http://localhost:$port")
      whenReady(client.fetchByPublicApiKey("key")) { res =>
        res shouldBe Right(expected)
      }
    }
  }

  test("should return account not found error when no account found for the given account public api key") {
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/idplus/fetch_account_information_by_public_api_key/key") { port =>
      val client = new AccountServiceIdPlusClientImpl(s"http://localhost:$port")
      whenReady(client.fetchByPublicApiKey("key")) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }
}
