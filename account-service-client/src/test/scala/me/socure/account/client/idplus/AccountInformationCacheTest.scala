package me.socure.account.client.idplus

import java.net.InetSocketAddress
import java.util.concurrent.TimeUnit
import me.socure.common.k8s.memcached.MemcachedTestSupport
import me.socure.model.account.EnvironmentValueGenerator._
import me.socure.model.account.WatchlistPreferenceValueGenerator._
import me.socure.model.account.CAWatchlistPreferenceValueGenerator._
import me.socure.model.account.DvConfigurationValueGenerator
import me.socure.model.ein.EINResponse
import me.socure.model.fraudmodel.FraudModel
import me.socure.model.kyc.KycPreferencesValueGenerator
import me.socure.model.mla.MLAFields
import me.socure.model.{AccountConsentReasons, AccountInformation, BusinessUserRoles, Industry}
import net.spy.memcached.{AddrUtil, MemcachedClient}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.Eventually
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.{Await, ExecutionContextExecutor}
import scala.concurrent.duration.Duration

/**
  * Created by alexandre on 5/9/16.
  */
class AccountInformationCacheTest extends FunSuite with Matchers with Eventually with BeforeAndAfter with MemcachedTestSupport{

  private implicit val executionContext: ExecutionContextExecutor = scala.concurrent.ExecutionContext.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  override def memcachedPodLabel(): String = "cached-encryption-keys-client-memcached"
  private val client = new MemcachedClient(AddrUtil.getAddresses(memcachedEndpoint()))


  after {
    cleanupMemcached()
  }

  test("should put and get properly") {

    eventually {

      val idPlusResponseCache = new CacheService[String, AccountInformation](client, Duration(10, TimeUnit.SECONDS))

      val apiKey = "some-api-key"
      Await.result(idPlusResponseCache.get(apiKey), Duration(10, TimeUnit.SECONDS)) shouldBe empty

      val input = AccountInformation(
        active = true,
        accountId = "1",
        publicId = "1",
        accountName = "name",
        industry = Industry("some sector", "some industry"),
        isInternal = true,
        environment = anEnvironment(),
        watchlistPreference = aWatchlistPreference(),
        watchlistPreference_3_0 = aCAWatchlistPreference(),
        watchlistPreferences_3_0 = aCAWatchlistPreferences(),
        roles = Set(BusinessUserRoles.ADMIN.id, BusinessUserRoles.DEBUG_PERMISSION.id),
        primaryFraudModel = Some(FraudModel(
          id = 1,
          identifier = "identifier2",
          name = "name2",
          url = "url2",
          version = "version2",
          createdDate = new DateTime(DateTimeZone.UTC),
          lastUpdated = new DateTime(DateTimeZone.UTC)
        )),
        fraudModels = Set(
          FraudModel(
            id = 1,
            identifier = "identifier1",
            name = "name1",
            url = "url1",
            version = "version1",
            createdDate = new DateTime(DateTimeZone.UTC),
            lastUpdated = new DateTime(DateTimeZone.UTC)
          )
        ),
        kycPreferences = KycPreferencesValueGenerator.aKycPreferences(),
        subscriptionTypeIds = Seq(),
        includedWatchlistSources = Seq(),
        dvConfiguration = DvConfigurationValueGenerator.aDvConfigurationValue(),
        consentReason = AccountConsentReasons.OPEN_BANK_ACCOUNT.id,
        externalId = None,
        mlaField = MLAFields("", ""),
        ein = None,
        webhookNotificationPreferences = Seq()
      )

      Await.ready(idPlusResponseCache.put(apiKey, input), Duration(10, TimeUnit.SECONDS))
      Await.result(idPlusResponseCache.get(apiKey), Duration(10, TimeUnit.SECONDS)) shouldBe Some(input)
    }
  }
}
