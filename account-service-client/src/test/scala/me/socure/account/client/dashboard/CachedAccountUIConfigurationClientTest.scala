package me.socure.account.client.dashboard

import me.socure.account.service.common.AccountUIConfigurationCacheKeyProvider
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import com.github.blemale.scaffeine.Cache
import me.socure.model.AccountUIConfiguration
import org.mockito.Mockito
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

class CachedAccountUIConfigurationClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfter {
  private implicit val ec = ExecutionContext.Implicits.global

  private val underlying = Mockito.mock(classOf[AccountUIConfigurationClient])
  private val cacheConfigurations = Mockito.mock(classOf[Cache[String,AccountUIConfiguration]])
  private val client = new CachedAccountUIConfigurationClientImpl(underlying, cacheConfigurations)

  private val accountId = 100
  private val creatorUserId = 100
  private val creatorAccountId = 100
  private val cacheKey = AccountUIConfigurationCacheKeyProvider.provide(accountId)
  val accountUIConfiguration = AccountUIConfiguration(Some(1), Some(10), Some(10))

  before {
    Mockito.reset(underlying, cacheConfigurations)
  }

  "CachedAccountUIConfigurationClient" - {
    "should fetch the configuration and store in cache" in {
      Mockito.when(cacheConfigurations.getIfPresent(cacheKey)).thenReturn(None)
      Mockito.when(underlying.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)).thenReturn(Future.successful(Right(accountUIConfiguration)))

      whenReady(client.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)) { result =>
        result shouldBe Right(accountUIConfiguration)
        Mockito.verify(cacheConfigurations).getIfPresent(cacheKey)
        Mockito.verify(underlying).getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)
        Mockito.verify(cacheConfigurations).put(cacheKey, accountUIConfiguration)
      }
    }

    "should fetch the configuration and not store in cache if there's a error" in {
      val response = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      Mockito.when(cacheConfigurations.getIfPresent(cacheKey)).thenReturn(None)
      Mockito.when(underlying.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)).thenReturn(Future.successful(response))
      whenReady(client.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)) { result =>
        result shouldBe response
        Mockito.verify(cacheConfigurations).getIfPresent(cacheKey)
        Mockito.verify(underlying).getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)
        Mockito.verify(cacheConfigurations, Mockito.never()).put(cacheKey, accountUIConfiguration)
      }
    }

    "should get from cache when available" in {
      Mockito.when(cacheConfigurations.getIfPresent(cacheKey)).thenReturn(Some(accountUIConfiguration))
      whenReady(client.getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)) { result =>
        result shouldBe Right(accountUIConfiguration)
        Mockito.verify(cacheConfigurations).getIfPresent(cacheKey)
        Mockito.verify(underlying, Mockito.never()).getUIAccountConfiguration(accountId, creatorUserId, creatorAccountId)
        Mockito.verify(cacheConfigurations, Mockito.never()).put(cacheKey, accountUIConfiguration)
      }
    }
  }

}
