package me.socure.account.client.encryption

import java.util.concurrent.{ScheduledThreadPoolExecutor, TimeUnit, TimeoutException}
import dispatch.Http
import me.socure.account.service.common.EncryptionKeysCacheKeyProvider
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.common.storage.Storage
import me.socure.common.timeout.NonBlockingFutureTimeout
import me.socure.model.encryption.EncryptedKeyDetails
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.mockito.Mockito
import org.mockito.Mockito.{never, times}
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.mock.MockitoSugar
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{BeforeAndAfter, FunSuite, Matchers}

import scala.concurrent.duration.Duration
import scala.concurrent.{ExecutionContext, Future}

class CachedEncryptionKeysClientV2ImplTest extends FunSuite with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(50, Seconds), interval = Span(1000, Millis))
  implicit val ec = ExecutionContext.global

  val host = "http://localhost"
  private val http = Http.default

  val cacheMock = mock[Storage[EncryptedKeyDetails]]
  val threadExecutor = new ScheduledThreadPoolExecutor(10)
  val futureTimeout = new NonBlockingFutureTimeout(threadExecutor)

  val accountId = 1
  val urlPath = s"/encryption_keys_v2/active_keys/$accountId"
  val cacheKey = EncryptionKeysCacheKeyProvider provide accountId
  val cacheResult = EncryptedKeyDetails(Map.empty, Map("internal-arn-1" -> List("internal_data_key")))
  val response = Response(ResponseStatus.Ok, cacheResult)

  before {
    Mockito.reset(cacheMock)
  }

  test("getAllActiveKeys - should not interact with the real client when the cache returns") {

    val cachedClient = new CachedEncryptionKeysClientV2Impl(http, host, cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(Some(cacheResult)))

    whenReady(cachedClient.getAllActiveKeys(accountId)) { response =>
      response.fold(_ => fail, _ shouldBe cacheResult)
      Mockito.verify(cacheMock, times(1)).get(cacheKey)
      Mockito.verify(cacheMock, never()).store(cacheKey, cacheResult)
    }
  }

  test("getAllActiveKeys - should interact with the real client when the cache returns None and then put response in cache") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedEncryptionKeysClientV2Impl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getAllActiveKeys - should interact with the real client when memcached is failed or throws exception during get") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.failed(new Exception("Exception")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedEncryptionKeysClientV2Impl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getAllActiveKeys - should interact with the real client when memcached timedout") {
    Mockito.when(cacheMock.get(cacheKey))
      .thenReturn(Future.failed(new TimeoutException("Simulated timeout")))
    Mockito.when(cacheMock.store(cacheKey, cacheResult))
      .thenReturn(Future.successful(()))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedEncryptionKeysClientV2Impl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getAllActiveKeys - should interact with the real client and ignores exception during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenReturn(Future.failed(new Exception("Exception")))

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedEncryptionKeysClientV2Impl(http, s"$host:$port", cacheMock, None)
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

  test("getAllActiveKeys - should interact with the real client and ignores timeout during cache put ") {

    Mockito.when(cacheMock.get(cacheKey)).thenReturn(Future.successful(None))
    Mockito.when(cacheMock.store(cacheKey, cacheResult)).thenAnswer(new Answer[Future[Unit]] {
      override def answer(invocation: InvocationOnMock): Future[Unit] = {
        Future {
          Thread.sleep(100)
          ()
        }
      }
    })

    withServlet(new StringServlet(200, response.encodeJson()), urlPath) { port =>
      val client = new CachedEncryptionKeysClientV2Impl(http, s"$host:$port", cacheMock, Some(futureTimeout, Duration.apply(50, TimeUnit.MILLISECONDS)))
      whenReady(client.getAllActiveKeys(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe cacheResult)
        Mockito.verify(cacheMock, times(1)).get(cacheKey)
        Mockito.verify(cacheMock, times(1)).store(cacheKey, cacheResult)
      }
    }
  }

}
