package me.socure.account.client

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.storage.Storage
import me.socure.model.DvConfigurationResponse
import me.socure.model.account.{DvConfiguration, DvConfigurationValueGenerator}
import org.mockito.Mockito
import org.mockito.Mockito._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}

import scala.concurrent.{ExecutionContext, Future}

/**
 * <AUTHOR> Kumar
 */
class CachedEnvironmentDvConfigurationClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfter {

  private implicit val ec = ExecutionContext.Implicits.global
  val dvConfigurationStorage: Storage[Map[String, DvConfiguration]] = mock(classOf[Storage[Map[String, DvConfiguration]]])
  val dvConfigurationStorageForAccounts: Storage[DvConfigurationResponse] = mock(classOf[Storage[DvConfigurationResponse]])
  val underlying : EnvironmentDvConfigurationClient = mock(classOf[EnvironmentDvConfigurationClient])
  private val client = new CachedEnvironmentDvConfigurationClient(underlying, dvConfigurationStorage, dvConfigurationStorageForAccounts)

  private val environmentId = 1234
  private val cacheKey = CachedEnvironmentDvConfigurationClient.key(environmentId)

  before {
    Mockito.reset(underlying, dvConfigurationStorage)
  }

  "CachedAccountDvConfigurationClient" - {
    "should list dv configuration" in {
      Mockito.when(dvConfigurationStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.listDvConfiguration(environmentId, None)).thenReturn(Future.successful(Right(DvConfigurationValueGenerator.dvConfigurationDefaults())))
      Mockito.when(dvConfigurationStorage.store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())).thenReturn(Future.successful(()))
      whenReady(client.listDvConfiguration(environmentId, None)) { result =>
        result shouldBe Right(DvConfigurationValueGenerator.dvConfigurationDefaults())
        Mockito.verify(dvConfigurationStorage).get(cacheKey)
        Mockito.verify(underlying).listDvConfiguration(environmentId, None)
        Mockito.verify(dvConfigurationStorage).store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())
      }
    }

    "should list dv configuration for accounts" in {
      Mockito.when(dvConfigurationStorageForAccounts.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.listDvConfigurationForAccounts(environmentId, None)).thenReturn(Future.successful(Right(DvConfigurationValueGenerator.dvConfigurationResponseDefaults())))
      Mockito.when(dvConfigurationStorageForAccounts.store(cacheKey, DvConfigurationValueGenerator.dvConfigurationResponseDefaults())).thenReturn(Future.successful(()))
      whenReady(client.listDvConfigurationForAccounts(environmentId, None)) { result =>
        result shouldBe Right(DvConfigurationValueGenerator.dvConfigurationResponseDefaults())
        Mockito.verify(dvConfigurationStorageForAccounts).get(cacheKey)
        Mockito.verify(underlying).listDvConfigurationForAccounts(environmentId, None)
        Mockito.verify(dvConfigurationStorageForAccounts).store(cacheKey, DvConfigurationValueGenerator.dvConfigurationResponseDefaults())
      }
    }

    "should not store to cache if list configuration throws an error" in {
      val response = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      Mockito.when(dvConfigurationStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.listDvConfiguration(environmentId, None)).thenReturn(Future.successful(response))
      Mockito.when(dvConfigurationStorage.store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())).thenReturn(Future.successful(()))
      whenReady(client.listDvConfiguration(environmentId, None)) { result =>
        result shouldBe response
        Mockito.verify(dvConfigurationStorage).get(cacheKey)
        Mockito.verify(underlying).listDvConfiguration(environmentId, None)
        Mockito.verify(dvConfigurationStorage, Mockito.never()).store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())
      }
    }

    "should get from cache when available" in {
      Mockito.when(dvConfigurationStorage.get(cacheKey)).thenReturn(Future.successful(Some(DvConfigurationValueGenerator.dvConfigurationDefaults())))
      Mockito.when(underlying.listDvConfiguration(environmentId, None)).thenReturn(Future.successful(Right(DvConfigurationValueGenerator.dvConfigurationDefaults())))
      Mockito.when(dvConfigurationStorage.store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())).thenReturn(Future.successful(()))
      whenReady(client.listDvConfiguration(environmentId, None)) { result =>
        result shouldBe Right(DvConfigurationValueGenerator.dvConfigurationDefaults())
        Mockito.verify(dvConfigurationStorage).get(cacheKey)
        Mockito.verify(underlying, Mockito.never()).listDvConfiguration(environmentId, None)
        Mockito.verify(dvConfigurationStorage, Mockito.never()).store(cacheKey, DvConfigurationValueGenerator.dvConfigurationDefaults())
      }
    }
  }

}
