package me.socure.account.client.superadmin

import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.superadmin.LockedUsers
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.DateTime
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sunder<PERSON> on 6/4/16.
  */
class LockedUserClientTest extends FunSuite with Matchers with ScalaFutures with EitherValues{
  implicit val ec : ExecutionContext = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host : String = "http://localhost"

  test("should return no record found") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RecordsNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/locked/list") { port =>
      val client : LockedUserClient = new LockedUserClientImpl(s"http://localhost:$port")
      whenReady(client.getLockedList) { res =>
        res should be ('left)
      }
    }
  }

  test("should return records") {
    val lockedUsers = LockedUsers("firstname", "surname", "emails", "company", "contact", DateTime.now().toString("YYYY-mm-DD HH:MM"))
    val expectedRes = Response[Vector[LockedUsers]](ResponseStatus.Ok, data = Vector(lockedUsers))
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/locked/list") { port =>
      val client : LockedUserClient = new LockedUserClientImpl(s"http://localhost:$port")
      whenReady(client.getLockedList) { res =>
        res should be ('right)
      }
    }
  }

  test("should return business user not found") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(BusinessUserNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/locked/unlock") { port =>
      val client : LockedUserClient = new LockedUserClientImpl(s"http://localhost:$port")
      whenReady(client.unlockUser(List("<EMAIL>"))) { res =>
        res should be ('left)
      }
    }
  }

  test("should unlock returns no of row affected") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 1)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/locked/unlock") { port =>
      val client : LockedUserClient = new LockedUserClientImpl(s"http://localhost:$port")
      whenReady(client.unlockUser(List("<EMAIL>"))) { res =>
        res should be ('right)
      }
    }
  }

  test("should unlock for multiple users") {
    val expectedRes = Response[Int](ResponseStatus.Ok, data = 2)
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/locked/unlock") { port =>
      val client : LockedUserClient = new LockedUserClientImpl(s"http://localhost:$port")
      whenReady(client.unlockUser(List("<EMAIL>", "<EMAIL>"))) { res =>
        res should be ('right)
      }
    }
  }

}
