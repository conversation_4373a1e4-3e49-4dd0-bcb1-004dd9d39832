package me.socure.account.client

import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.ExecutionContext


class ManageAccountsV2ClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec = ExecutionContext.global

  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http = new NonSecuredHttpFactory().getHttpClient()

  test("update Consent Reason for the valid account") {
    val accountId = 10
    val consentId = 2
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 1).encodeJson()), "/account/management/v2/update_consent_reason") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.updateConsentId(accountId = accountId, consentId = consentId)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("get Consent Reason for the valid account") {
    val accountId = 10
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 2).encodeJson()), s"/account/management/v2/get_consent_reason/$accountId") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.getConsentReasonId(accountId = accountId)) { res =>
        res shouldBe Right(2)
      }
    }
  }

  test("updateIsActive should update account name by public id") {
    val publicId = Random.alphaNumeric(10)
    val activate = Random.nextBoolean().toString
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 1).encodeJson()), s"/account/management/v2/publicid/$publicId/activate") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.updateIsActive(publicId = publicId, activate = activate)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("updateIsActive should return account not found error for an invalid public id") {
    val publicId = Random.alphaNumeric(10)
    val activate = Random.nextBoolean().toString
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/account/management/v2/publicid/$publicId/activate") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.updateIsActive(publicId = publicId, activate = activate)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("updateIsDeleted should update account name by public id") {
    val publicId = Random.alphaNumeric(10)
    val delete = Random.nextBoolean().toString
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 1).encodeJson()), s"/account/management/v2/publicid/$publicId/delete") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.updateIsDeleted(publicId = publicId, delete = delete)) { res =>
        res shouldBe Right(1)
      }
    }
  }

  test("updateIsDeleted should return account not found error for an invalid public id") {
    val publicId = Random.alphaNumeric(10)
    val delete = Random.nextBoolean().toString
    val expectedResponse = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedResponse.encodeJson()), s"/account/management/v2/publicid/$publicId/delete") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.updateIsDeleted(publicId = publicId, delete = delete)) { res =>
        res shouldBe Left(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }

  test("search accounts") {
    val asr: AccountSearchRequest = AccountSearchRequest(
      id = None,
      publicId = None,
      name = None,
      apiKey = None,
      publicApiKey = None,
      email = None,
      deleted = None,
      active = None,
      internal = None,
      isParent = None,
      permissions = None,
      pagination = None)
    val asr1 = AccountSearchResponse(1,"publicId1", "AccountName1",true,false,true,None,Some("<EMAIL>"),false, false)
    val asr2 = AccountSearchResponse(2,"publicId2", "AccountName2",true,true,true,None,Some("<EMAIL>"),false, false)
    val expected = Vector(asr1, asr2)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/account/management/v2/accounts/list") { port =>
      val client: ManageAccountsV2Client = new ManageAccountsV2ClientImpl(http, s"$host:$port")
      whenReady(client.listAccounts(asr = asr)) { res =>
        res shouldBe Right(expected)
      }
    }
  }
}
