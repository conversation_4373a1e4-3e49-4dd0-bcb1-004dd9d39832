package me.socure.account.client.setting

import me.socure.account.service.common.subscription.{ChannelType, CommunicationMode, SubscriptionChannelRegistryStatus}
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.publicid.PublicId
import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.dashboardv2.Creator
import me.socure.model.subscription.{DtoSubscriptionChannelRegistry, SubscriptionChannelRegistryWithAccount, UpdateWebhookSecretKeyRotation, WebhookSecretKeyRotationDetails}
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.json4s.jackson.Serialization
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import java.util.Base64
import scala.concurrent.{ExecutionContext, Future}

class SubscriptionChannelRegistryClientImplTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"
  val testHttpClient = new NonSecuredHttpFactory().getHttpClient()
  val accountId =1L

  def serialize(metadata: Map[String, Any]): String = {
    Base64.getEncoder.encodeToString(Serialization.write(metadata).getBytes("UTF-8"))
  }

  test("getSubscriptionChannelRegistry by id should return a single result upon success") {
    val id = 791L
    val expected = DtoSubscriptionChannelRegistry(
      id = id,
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "abcdefg"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = 1L,
      environmentId = 177302L,
      channelType = ChannelType.PRIMARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      featureTypes = Set(1)
    )
    testSuccess[DtoSubscriptionChannelRegistry](s"/settings/channel/$id", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistry(id,accountId)
    }
  }

  test("getSubscriptionChannelRegistry by id should return an error response if no result found") {
    val id = 792L
    testError(s"/settings/channel/$id") {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistry(id,accountId)
    }
  }

  test("createSubscriptionChannelRegistry should return true upon successful creation") {
    val requestObject = DtoSubscriptionChannelRegistry(
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "test2"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = 1L,
      environmentId = 177301L,
      channelType = ChannelType.SECONDARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      featureTypes = Set(2)
    )
    val expected = true

    testSuccess(s"/settings/channel", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.createSubscriptionChannelRegistry(requestObject)
    }
  }

  test("createSubscriptionChannelRegistry should return an error if it's unable to create") {
    val requestObject = DtoSubscriptionChannelRegistry(
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "test2"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = 1L,
      environmentId = 177301L,
      channelType = ChannelType.SECONDARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
      featureTypes = Set(1, 2)
    )
    testError(s"/settings/channel") {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.createSubscriptionChannelRegistry(requestObject)
    }
  }

  test("updateSubscriptionChannelRegistry should return true upon successful update") {
    val requestObject = DtoSubscriptionChannelRegistry(
      id = 42,
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "test2"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = 1L,
      environmentId = 177301L,
      channelType = ChannelType.SECONDARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-20", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-20", DateTimeZone.UTC),
      featureTypes = Set(2)
    )
    val expected = true

    testSuccess(s"/settings/channel/${requestObject.id}", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.updateSubscriptionChannelRegistry(requestObject)
    }
  }

  test("updateSubscriptionChannelRegistry should return an error if it's unable to update") {
    val requestObject = DtoSubscriptionChannelRegistry(
      id = 42,
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "test2"),
      status = SubscriptionChannelRegistryStatus.DELETED.id,
      subscriptionTypeId = 1L,
      environmentId = 177301L,
      channelType = ChannelType.SECONDARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-20", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-20", DateTimeZone.UTC),
      featureTypes = Set(1)
    )
    testError(s"/settings/channel/${requestObject.id}") {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.updateSubscriptionChannelRegistry(requestObject)
    }
  }

  test("deleteSubscriptionChannelRegistry should return true if delete is successful") {
    val id = 791L
    val expected = true
    testSuccess(s"/settings/channel/$id", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.deleteSubscriptionChannelRegistry(id,Creator(0L,1L))
    }
  }

  test("getSubscriptionChannelRegistries by environmentId should return all subscriptions registered for environment id") {
    val environmentId = 177302L
    val subscriptionTypeId = 1L
    val expected = Seq(
      DtoSubscriptionChannelRegistry(
        id = 746L,
        communicationSource = "https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30",
        metadata = Map("CERTIFICATE" -> "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"),
        status = 2,
        subscriptionTypeId = 1L,
        environmentId = 177302L,
        channelType = ChannelType.PRIMARY.id,
        communicationMode = CommunicationMode.WEBHOOK.id,
        createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        featureTypes = Set(2)
      ),
      DtoSubscriptionChannelRegistry(
        id = 791L,
        communicationSource = "<EMAIL>",
        metadata = Map("CERTIFICATE" -> "test"),
        status = 1,
        subscriptionTypeId = 1L,
        environmentId = 177302L,
        channelType = ChannelType.SECONDARY.id,
        communicationMode = CommunicationMode.EMAIL.id,
        createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        featureTypes = Set(1)
      )
    )
    testSuccess(s"/settings/channel/environment/$environmentId", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistries(environmentId,Creator(0L,1L))
    }
  }

  test("getSubscriptionChannelRegistries by environmentId should return empty seq if no results are found") {
    val environmentId = 177301L
    val expected = Seq.empty[DtoSubscriptionChannelRegistry]
    testSuccess(s"/settings/channel/environment/$environmentId", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistries(environmentId,Creator(0L,1L))
    }
  }

  test("getSubscriptionChannelRegistries by environmentId and subscriptionTypeId should return all subscriptions registered for environment id and subscription type id") {
    val environmentId = 177302L
    val subscriptionTypeId = 1L
    val expected = Seq(
      DtoSubscriptionChannelRegistry(
        id = 746L,
        communicationSource = "https://5tzsq4zc13.execute-api.us-east-1.amazonaws.com/Stage-2019-12-30",
        metadata = Map("CERTIFICATE" -> "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"),
        status = 2,
        subscriptionTypeId = 1L,
        environmentId = 177302L,
        channelType = 1,
        communicationMode = 1,
        createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        featureTypes = Set(2)
      ),
      DtoSubscriptionChannelRegistry(
        id = 791L,
        communicationSource = "test",
        metadata = Map("CERTIFICATE" -> "test"),
        status = 1,
        subscriptionTypeId = 1L,
        environmentId = 177302L,
        channelType = 2,
        communicationMode = 2,
        createdAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        updatedAt = new DateTime("2020-08-13", DateTimeZone.UTC),
        featureTypes = Set(1)
      )
    )
    testSuccess(s"/settings/channel/environment/$environmentId/subscription/$subscriptionTypeId", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistries(environmentId, subscriptionTypeId,1L)
    }
  }

  test("getSubscriptionChannelRegistries by environmentId and subscriptionTypeId should return empty seq if no results are found") {
    val environmentId = 177301L
    val subscriptionTypeId = 1L
    val expected = Seq.empty[DtoSubscriptionChannelRegistry]
    testSuccess(s"/settings/channel/environment/$environmentId/subscription/$subscriptionTypeId", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.getSubscriptionChannelRegistries(environmentId, subscriptionTypeId,1L)
    }
  }

  test("getSubscriptionChannelRegistryWithAccount should return list of different subscription channels registered") {
    val accountId: Long = 1L
    val environmentTypeId: Long = 1L
    val subscriptionTypeId: Long = 1L
    val firstRegistryMock = DtoSubscriptionChannelRegistry(
      communicationSource = "<EMAIL>",
      metadata = Map("CERTIFICATE" -> "test"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = subscriptionTypeId,
      environmentId = 1L,
      channelType = ChannelType.PRIMARY.id,
      communicationMode = CommunicationMode.EMAIL.id,
      createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
      secretKey = Some("secret_key_12345"),
      featureTypes = Set(2)
    )
    val secondRegistryMock = DtoSubscriptionChannelRegistry(
      communicationSource = "test2.com",
      metadata = Map("CERTIFICATE" -> "test"),
      status = SubscriptionChannelRegistryStatus.ACTIVE.id,
      subscriptionTypeId = subscriptionTypeId,
      environmentId = 1L,
      channelType = ChannelType.SECONDARY.id,
      communicationMode = CommunicationMode.WEBHOOK.id,
      createdAt = new DateTime("2020-08-07", DateTimeZone.UTC),
      updatedAt = new DateTime("2020-08-07", DateTimeZone.UTC),
      featureTypes = Set(1)
    )
    val expected = SubscriptionChannelRegistryWithAccount(
      subscriptions = Seq(firstRegistryMock, secondRegistryMock),
      publicAccountId = PublicId(1.toString),
      isProvisioned = true,
      secretKey = Some("secret_key_12345")
    )
    testSuccess[SubscriptionChannelRegistryWithAccount](s"/settings/channel/account/$accountId/environment/type/$environmentTypeId/subscription/$subscriptionTypeId", expected) { port =>
      val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
      client.getSubscriptionChannelRegistryWithAccount(accountId, environmentTypeId, subscriptionTypeId)
    }
  }

  test("getSubscriptionChannelRegistryWithAccount should fail to get registered channels") {
    val accountId: Long = 10L
    val envTypeId: Long = 10L
    val subscriptionType: Long = 10L
    testError(s"/settings/channel/account/$accountId/environment/type/$envTypeId/subscription/$subscriptionType") { port =>
      val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
      client.getSubscriptionChannelRegistryWithAccount(accountId, envTypeId, subscriptionType)
    }
  }

  test("updateWatchlistWebhookSecretKey should return true upon successful updateSecretKey") {
    val expected = 1
    val accountId: Long = 10L
    val environmentTypeId: Int = 10
    val subscriptionTypeId: Long = 10L
    val secretKey: String = "secretKey"
    testSuccess(s"/settings/channel/account/$accountId/environment/type/$environmentTypeId/subscription/type/$subscriptionTypeId", expected) {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.updateWatchlistWebhookSecretKey(accountId, environmentTypeId, subscriptionTypeId, secretKey)
    }
  }

  test("getWebhookSecretKeyRotationDetails should return list of to be expired secret key rotation details") {
    val firstSecretKey = WebhookSecretKeyRotationDetails(
      subscriptionChannelId = 1L,
      secretKeyEndpoint = "http://test.com",
      clientId = "test_client_id",
      clientSecret = "test_client_secret",
      secretExpiresOn = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastSuccessfulSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastFailedSync = None,
      errorCode = None,
      failedSyncCount = None
    )
    val secondSecretKey = WebhookSecretKeyRotationDetails(
      subscriptionChannelId = 2L,
      secretKeyEndpoint = "http://test2.com",
      clientId = "test_client_id_2",
      clientSecret = "test_client_secret_2",
      secretExpiresOn = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastSuccessfulSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastFailedSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      errorCode = Some(400),
      failedSyncCount = Some(2)
    )
    val expected = Seq(firstSecretKey, secondSecretKey)
    testSuccess[Seq[WebhookSecretKeyRotationDetails]](s"/settings/channel/secret_key_sources/expired", expected) { port =>
      val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
      client.getWebhookSecretKeyRotationDetails()
    }
  }

  test("getWebhookSecretKeyRotationDetails should return Error") {
    testError(s"/settings/channel/secret_key_sources/expired") { port =>
      val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
      client.getWebhookSecretKeyRotationDetails()
    }
  }

  test("updateWebhookSecretKeyRotationDetails should update list of secret key rotation details") {
    val firstSecretKey = UpdateWebhookSecretKeyRotation(
      subscriptionChannelId = 1L,
      secretKey = Some("test_secret_key"),
      secretExpiresOn = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastSuccessfulSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastFailedSync = None,
      errorCode = None,
      failedSyncCount = None
    )
    val secondSecretKey = UpdateWebhookSecretKeyRotation(
      subscriptionChannelId = 1L,
      secretKey = Some("test_secret_key_2"),
      secretExpiresOn = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastSuccessfulSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastFailedSync = None,
      errorCode = None,
      failedSyncCount = None
    )
    val request = Seq(firstSecretKey, secondSecretKey)
    val expected = true
    testSuccess(s"/settings/channel/secret_key_rotation", expected) { port =>
      val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
      client.updateWebhookSecretKeyRotationDetails(request)
    }
  }

  test("updateWebhookSecretKeyRotationDetails should return error") {
    val firstSecretKey = UpdateWebhookSecretKeyRotation(
      subscriptionChannelId = 1L,
      secretKey = Some("test_secret_key"),
      secretExpiresOn = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastSuccessfulSync = Some(new DateTime("2020-08-07", DateTimeZone.UTC)),
      lastFailedSync = None,
      errorCode = None,
      failedSyncCount = None
    )
    val request = Seq(firstSecretKey)
    testError(s"/settings/channel/secret_key_rotation") {
      port =>
        val client = new SubscriptionChannelRegistryClientImpl(s"http://localhost:$port", testHttpClient)
        client.updateWebhookSecretKeyRotationDetails(request)
    }
  }

  private def testError(path: String)(invoke: Int => Future[Either[ErrorResponse, _]]): Unit = {
    val errorResponse = ErrorResponse(
      code = Random.nextInt(),
      message = Random.alphaNumeric(10)
    )
    withServlet(new StringServlet(400, Serialization.write(Response(ResponseStatus.Error, errorResponse))), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Left(errorResponse)
      }
    }
  }

  private def testSuccess[T](path: String, response: T)(invoke: Int => Future[Either[ErrorResponse, T]])(implicit m: Manifest[T]): Unit = {
    withServlet(new StringServlet(200, Serialization.write(Response(ResponseStatus.Ok, response))), path) { port =>
      whenReady(invoke(port)) { res =>
        res shouldBe Right(response)
      }
    }
  }
}
