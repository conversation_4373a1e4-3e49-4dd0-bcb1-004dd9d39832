package me.socure.account.automation

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountNotFound}
import me.socure.common.clock.FakeClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.automation.{AccountBundleAssociation, DtoAccountBundleAssociation}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountBundleAssociationClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("Get Account Bundle Association for account - success") {
    val accountId = 1L
    val expected = DtoAccountBundleAssociation(1, 1, "Bundle 1", "<EMAIL>", clock.now, None, None)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/bundles/*") { port =>
      val client: AccountBundleAssociationClient = new AccountBundleAssociationClientImpl(http, s"$host:$port")
      whenReady(client.getAccountBundleAssociation(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Bundle Association for account - fail") {
    val accountId = 1L
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/bundles/*") { port =>
      val client: AccountBundleAssociationClient = new AccountBundleAssociationClientImpl(http, s"$host:$port")
      whenReady(client.getAccountBundleAssociation(accountId)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Upsert Account Bundle Association for account - success") {
    val accountBundleAssociation = AccountBundleAssociation(1, "Bundle 1", "<EMAIL>")
    val expected = DtoAccountBundleAssociation(1, 1, "Bundle 1", "<EMAIL>", clock.now, None, None)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/bundles/*") { port =>
      val client: AccountBundleAssociationClient = new AccountBundleAssociationClientImpl(http, s"$host:$port")
      whenReady(client.upsertAccountBundleAssociation(accountBundleAssociation)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Upsert Account Bundle Association for account - fail") {
    val accountBundleAssociation = AccountBundleAssociation(1, "Bundle 1", "<EMAIL>")
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/bundles/*") { port =>
      val client: AccountBundleAssociationClient = new AccountBundleAssociationClientImpl(http, s"$host:$port")
      whenReady(client.upsertAccountBundleAssociation(accountBundleAssociation)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }
}