package me.socure.account.automation

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.common.clock.FakeClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.ProductProvisioningTypes
import me.socure.model.{AccountProducts, Response, ResponseStatus, UpdateProduct}
import me.socure.model.account.automation.{AccountProvisioningDetails, ProductConfiguration, UpdateAccountProvisioningDetails}
import me.socure.model.bundles.Bundle
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountAutomationClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("Get Bundle information - success") {
    val expected =  Set(Bundle("Bundle 4","Fraud Score, Alert List, Email RiskScore, Phone RiskScore, Address RiskScore, KYC, Device Risk, Watchlist, Synthetic",
      Set(37, 52, 29, 28, 33, 65, 188, 13, 71, 99, 30)))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/automation/bundles") { port =>
      val client: AccountAutomationClient = new AccountAutomationClientImpl(http, s"$host:$port")
      whenReady(client.getBundles()) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Automation Details for account - success") {
    val accountId = 1
    val expected = AccountProvisioningDetails(Some("Bundle One"),
      List(AccountProducts(1,"Watchlist-3.0",65, ProductProvisioningTypes.CASCADE,None,1,false,false,false,None,None,None,None)),
      ProductConfiguration(Some("*********"), None, None))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/automation/account/1") { port =>
      val client: AccountAutomationClient = new AccountAutomationClientImpl(http, s"$host:$port")
      whenReady(client.getAccountProvisioningDetails(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Automation Details for account - fail") {
    val accountId = 1L
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/automation/account/1") { port =>
      val client: AccountAutomationClient = new AccountAutomationClientImpl(http, s"$host:$port")
      whenReady(client.getAccountProvisioningDetails(accountId)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Upsert Account Automation Details for account - success") {
    val expected = true
    val accountId = 1L
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/automation/account/1") { port =>
      val client: AccountAutomationClient = new AccountAutomationClientImpl(http, s"$host:$port")
      whenReady(client.updateAccountProvisioningDetails(accountId, updateAccountProvisioningDetails)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Upsert Account Bundle Association for account - fail") {
    val expected = ErrorResponseFactory.get(AccountNotFound)
    val accountId = 1L
    val products = Seq(UpdateProduct(1, provisioned = true, enabled = true), UpdateProduct(2, provisioned = true, enabled = true))
    val productConfig = ProductConfiguration(ein = Some("*********"), None, None)
    val updateAccountProvisioningDetails = UpdateAccountProvisioningDetails(
      bundleReference = "Bundle One",
      products = products,
      productConfiguration = productConfig,
      initiatedBy = "<EMAIL>")
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/automation/account/1") { port =>
      val client: AccountAutomationClient = new AccountAutomationClientImpl(http, s"$host:$port")
      whenReady(client.updateAccountProvisioningDetails(accountId, updateAccountProvisioningDetails)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

}
