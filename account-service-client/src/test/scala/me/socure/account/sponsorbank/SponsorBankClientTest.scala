package me.socure.account.sponsorbank

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.clock.FakeClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountIdName
import me.socure.model.sponsor.bank.{SponsorBankProgram, SponsorBankProgramLinkRequest}
import me.socure.model.{Response, ResponseStatus}
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class SponsorBankClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("Get non linked programs - success") {
    val expected =  Seq(AccountIdName(1, "AccountName1"))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/sponsorbank/non-linked") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getNonSponsorBankPrograms()) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get non linked programs - fail") {
    val expected =  ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/sponsorbank/non-linked") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getNonSponsorBankPrograms()) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get linked programs - success") {
    val expected =  Seq(SponsorBankProgram(1, "programName", "<EMAIL>", clock.now))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/sponsorbank/linked/programs/2") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getLinkedPrograms(2)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get linked programs - fail") {
    val expected =  ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/sponsorbank/linked/programs/2") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getLinkedPrograms(2)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get sponsor bank for program - success") {
    val expected =  AccountIdName(1, "AccountName1")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/sponsorbank/program/2") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getSponsorBank(2)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get sponsor bank for program - fail") {
    val expected =  ErrorResponseFactory.get(UnknownError)
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/sponsorbank/program/2") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.getSponsorBank(2)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("link program to sponsor bank - success") {
    val expected =  true
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(1, 1, "<EMAIL>")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/sponsorbank/link") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.linkSponsorBankProgram(sponsorBankProgramLinkRequest)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("link program to sponsor bank - fail") {
    val expected =  ErrorResponseFactory.get(UnknownError)
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(1, 1, "<EMAIL>")
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/sponsorbank/link") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.linkSponsorBankProgram(sponsorBankProgramLinkRequest)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("unlink program to sponsor bank - success") {
    val expected =  true
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(1, 1, "<EMAIL>")
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/sponsorbank/unlink") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("unlink program to sponsor bank - fail") {
    val expected =  ErrorResponseFactory.get(UnknownError)
    val sponsorBankProgramLinkRequest = SponsorBankProgramLinkRequest(1, 1, "<EMAIL>")
    withServlet(new StringServlet(400, Response(ResponseStatus.Error, expected).encodeJson()), s"/sponsorbank/unlink") { port =>
      val client: SponsorBankClient = new SponsorBankClientImpl(http, s"$host:$port")
      whenReady(client.unlinkSponsorBankProgram(sponsorBankProgramLinkRequest)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

}
