package me.socure.account.data.retention

import dispatch.Http
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.{AccountDataRetentionScheduleNotFound, AccountNotFound}
import me.socure.common.clock.FakeClock
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.data.retention.{AccountDataRetentionSchedule, AccountDataRetentionScheduleWithHierarchy, DtoAccountDataRetentionSchedule, UpdateAccountDataRetentionSchedule}
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

class AccountDataRetentionScheduleClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()
  val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)

  test("Get Account Data Retention Schedule Details for account - success") {
    val accountId = 1
    val expected = DtoAccountDataRetentionSchedule(1, 1, 3, "Days", "<EMAIL>", clock.now)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionSchedule(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Data Retention Schedule Details for account - fail") {
    val accountId = 1L
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionSchedule(accountId)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get Account Data Retention Schedule Details - success") {
    val expected = Seq(AccountDataRetentionSchedule(1, 3, "Days"))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionSchedule()) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Data Retention Schedule Details - fail") {
    val expected = ErrorResponseFactory.get(AccountDataRetentionScheduleNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionSchedule()) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Get Account Data Retention Schedule Details with Hierarchy- success") {
    val accountId = 1
    val expected = AccountDataRetentionScheduleWithHierarchy(1, 3, "Days", Set(2,3))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account-hierarchy/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionScheduleWithHierarchy(accountId)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Get Account Data Retention Schedule Details with Hierarchy - fail") {
    val accountId = 1
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account-hierarchy/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.getAccountDataRetentionScheduleWithHierarchy(1)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }

  test("Update Account Data Retention Schedule Details for account - success") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(3, "Days", "<EMAIL>")
    val accountId = 1L
    val expected = DtoAccountDataRetentionSchedule(1, 1, 3, "Days", "<EMAIL>", clock.now)
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.updateAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule)) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

  test("Update Account Data Retention Schedule Details for account - fail") {
    val updateAccountDataRetentionSchedule = UpdateAccountDataRetentionSchedule(3, "Days", "<EMAIL>")
    val accountId = 1L
    val expected = ErrorResponseFactory.get(AccountNotFound)
    withServlet(new StringServlet(400, Response(ResponseStatus.Ok, expected).encodeJson()), s"/data/retention/schedule/account/1") { port =>
      val client: AccountDataRetentionScheduleClient = new AccountDataRetentionScheduleClientImpl(http, s"$host:$port")
      whenReady(client.updateAccountDataRetentionSchedule(accountId, updateAccountDataRetentionSchedule)) { response =>
        response.fold(_ shouldBe expected, _ => fail)
      }
    }
  }
}
