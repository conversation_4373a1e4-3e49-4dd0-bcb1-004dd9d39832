package me.socure.schedule.client

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.client.dashboard2.BusinessUserManagementClientImpl
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.time.{Millis, Seconds, Span}
import me.socure.util.JsonEnrichments._
import scala.concurrent.ExecutionContext

/**
  * Created by ma<PERSON><PERSON><PERSON> on 03/04/2017.
  */
class ScheduledActivitiesClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {

  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should completed task with error"){
    val response = Response(ResponseStatus.Ok, "Completed Successfully")
    withServlet(new StringServlet(200, response.encodeJson()),"/schedule/lock_inactive_user") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.lockInactiveBusinssUser, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe "Completed Successfully"
      }
    }
  }

  test("should be left if some error is happen"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/schedule/lock_inactive_user") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.lockInactiveBusinssUser, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("should completed task without error locking bu never loggedin"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/schedule/lock_user_not_loggedin_after_signup") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.lockNotLoggedInUserAfterSignup, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should not completed task locking bu never loggedin"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/schedule/lock_user_not_loggedin_after_signup") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.lockNotLoggedInUserAfterSignup, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("should completed task without error invalidating activation token"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/schedule/invalidate_activation_tokens") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.invalidateActivationToken, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should not completed task invalidating activation token"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/schedule/invalidate_activation_tokens") { port =>
      val client = new ScheduledActivitiesClientImpl(s"$host:$port")
      whenReady(client.invalidateActivationToken, Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

}
