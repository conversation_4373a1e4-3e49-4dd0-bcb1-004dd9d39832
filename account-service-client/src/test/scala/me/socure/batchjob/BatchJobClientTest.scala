package me.socure.batchjob

import dispatch.Http
import me.socure.account.client.batchjob.{BatchJobClient, BatchJobClientImpl}
import me.socure.account.client.dashboard.{AccountHierarchyClient, AccountHierarchyClientImpl}
import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.UnknownError
import me.socure.common.http.NonSecuredHttpFactory
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountInfoV2WithIndustry
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.scalatest.{EitherValues, FunSuite, Matchers}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}
import me.socure.util.JsonEnrichments._

class BatchJobClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))
  val host = "http://localhost"
  val http: Http = new NonSecuredHttpFactory().getHttpClient()

  test("Should update account permissions - success") {
    val upsRequest = List(
      AccountPermissionUpdateRequest(1000, Set(1, 2, 3), true),
      AccountPermissionUpdateRequest(10, Set(1001, 48, 3), true),
      AccountPermissionUpdateRequest(7, Set(1001, 2, 3), true),
      AccountPermissionUpdateRequest(7, Set(1001, 2, 3), false))
    val expected = List(
      AccountPermissionUpdateResponse(1000, Set(1, 2, 3), true, "Success", ""),
      AccountPermissionUpdateResponse(10, Set(1001, 48, 3), true, "Success", ""),
      AccountPermissionUpdateResponse(7, Set(1001, 2, 3), true, "Failure", "Account Not Found"),
      AccountPermissionUpdateResponse(7, Set(1001, 2, 3), false, "Failure", "Unknown role"))
    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, expected).encodeJson()), s"/batchjob/account/permission/update") { port =>
      val client: BatchJobClient = new BatchJobClientImpl(http, s"$host:$port")
      whenReady(client.updateAccountPermissions(upsRequest)) { response =>
        response shouldBe 'right
        response.right.value shouldBe expected
      }
    }
  }
}