package me.socure.user.client.dashboard2

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.clock.FakeClock
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{ApiKeyInfo, ApiKeyStatus, EnvironmentWithDomains}
import me.socure.model.dashboardv2._
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON><PERSON> on 8/26/16.
  */
class EnvironmentSettingsClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(500, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"
  test("get environment settings should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentSettingsWithApiKeys(1L)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return environment settings with api keys"){
    val response = Response(ResponseStatus.Ok, UserFixture.environmentSettingsWithApiKeys)
    withServlet(new StringServlet(200, response.encodeJson()),"/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentSettingsWithApiKeys(1L)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe UserFixture.environmentSettingsWithApiKeys)
      }
    }
  }

  test("get environment settings - V2 should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentSettingsWithApiKeys(1L, None, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return environment settings with api keys - V2"){
    val response = Response(ResponseStatus.Ok, UserFixture.environmentSettingsWithApiKeys)
    withServlet(new StringServlet(200, response.encodeJson()),"/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentSettingsWithApiKeys(1L, Some(1L), None)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe UserFixture.environmentSettingsWithApiKeys)
      }
    }
  }

  test("upsert social network key should return true"){
    val response = Response(ResponseStatus.Ok, 2)
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/upsert_appkey") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeys)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe 2)
      }
    }
  }

  test("upsert social network key should return EnvironmentNotFound"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/upsert_appkey") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.upsertSocialNetworkKeys(UserFixture.socialNetworkAppKeys)) { response =>
        response should be ('left)
      }
    }
  }

  test("remove social network config should return true"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/remove_appkey") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.deleteSocialNetworkKeys(1, Some(Creator(1,1)))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("remove social network config should return SocialKeyNotFound"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SocialKeyIdNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/remove_appkey") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.deleteSocialNetworkKeys(1, Some(Creator(1,1)))) { response =>
        response should be ('left)
      }
    }
  }

  test("update domain should return true"){
    val domain = EnvironmentDomain(1, List("domain.com"))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails, true)).encodeJson()
    withServlet(new StringServlet(200, response), "/environment_settings/update_domain") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.updateEnvironmentDomaon(domain.id, domain.domain, Some(Creator(1,1)))) { response =>
        response._2 shouldBe 'right
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("update domain should return EnvironmentNotFound"){
    val domain = EnvironmentDomain(1, List("domain.com"))
    val error = ErrorResponseFactory.get(EnvironmentNotFound)
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set.empty, Seq.empty), None, Some(error), Seq.empty)
    val expectedRes = Response(ResponseStatus.Ok, (auditDetails, error)).encodeJson()
    withServlet(new StringServlet(200, expectedRes), "/environment_settings/update_domain") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.updateEnvironmentDomaon(domain.id, domain.domain, Some(Creator(1,1)))) { response =>
        response._2 should be('left)
      }
    }
  }

  test("get environment list should return true"){
    val environmentList = Seq(EnvironmentNameAndId(1, "Production"), EnvironmentNameAndId(2, "Development"))
    val response = Response(ResponseStatus.Ok, environmentList)
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/get_environments/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnironmentList(1)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _.size shouldBe 2)
        response.fold(_ => fail, _.headOption.fold(fail)(_.id shouldBe 1))
      }
    }
  }

  test("get environment list should return EnvironmentNotFound"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/get_environments/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnironmentList(1)) { response =>
        response should be ('left)
      }
    }
  }

  test("generate api key should return api key"){
    val clock = new FakeClock(new DateTime("2017-03-14").withZone(DateTimeZone.UTC).getMillis)
    val response = Response(ResponseStatus.Ok, 1)
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/generate_api_key") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.generateApiKey(1, Some(Creator(1, 1)))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ should be (1))
      }
    }
  }

  test("generate api key should return ApiKeyCannotBeRenewed"){
   val response = Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyCannotBeRenewed))
    withServlet(new StringServlet(400, response.encodeJson()),"/environment_settings/generate_api_key") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.generateApiKey(1, Some(Creator(1, 1)))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("deprecate api key should return Api Key Status updated"){
    val list = List(1,1)
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/deprecate_api_key") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.deprecateApiKey(1, Some(Creator(1, 1)))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("deprecate api key should return ApiKeyStatusNotchanged"){
    val response = Response(ResponseStatus.Error, ErrorResponseFactory.get(ApiKeyStatusNotchanged))
    withServlet(new StringServlet(400, response.encodeJson()),"/environment_settings/deprecate_api_key") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.deprecateApiKey(1, Some(Creator(1, 1)))) { response =>
        response shouldBe 'left
      }
    }
  }

  test("should return social app key id"){
    val response = Response(ResponseStatus.Ok, Seq(1,2))
    withServlet(new StringServlet(200, response.encodeJson()),"/environment_settings/get_appkey_ids/1") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getSocialIdsByAccountId(1)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe List(1,2))
      }
    }
  }

  test("should return social key not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/get_appkey_ids/1") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getSocialIdsByAccountId(1)) { response =>
        response should be ('left)
      }
    }
  }

  test("get_environment_with_domains: should return success for an account"){
    val list = List(EnvironmentWithDomains(1, "Prod", "*********"), EnvironmentWithDomains(2, "Prod1", "*********"))
    val result = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, result.encodeJson()), "/environment_settings/get_environment_with_domains/1") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentWithDomains(1)) { response =>
        response.fold(_ => fail, _ shouldBe list)
      }
    }
  }

  test("get_environment_with_domains: should return error for invalid account id"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(EnvironmentNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/get_environment_with_domains/1") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironmentWithDomains(1)) { response =>
        response should be ('left)
      }
    }
  }

  test("should return environment name and id - V2"){
    val environmentList = Seq(EnvironmentNameAndId(1, "Production"), EnvironmentNameAndId(2, "Development"))
    val response = Response(ResponseStatus.Ok, environmentList)
    withServlet(new StringServlet(200, response.encodeJson()),"/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getEnvironments(1L, 1, Some(1L), Some(1L))) { response =>
        response.fold(_ => fail, _ shouldBe environmentList)
      }
    }
  }

  test("should return apikeys for given environmentId"){
    val expected = Seq(ApiKeyInfo(1, ApiKeyStatus.ACTIVE, "1234-1234-1234-1234"))
    val response = Response(ResponseStatus.Ok, expected)
    withServlet(new StringServlet(200, response.encodeJson()),"/*") { port =>
      val client = new EnvironmentSettingsClientImpl(s"$host:$port")
      whenReady(client.getApiKeys(1L, Some(1L), Some(1L))) { response =>
        response.fold(_ => fail, _ shouldBe expected)
      }
    }
  }

}
