package me.socure.user.client.dashboard

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

class DashboardUserClientImplTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit val ec = ExecutionContext.global

  test("should get account credentials for the api key") {

    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, UserFixture.accessCredsEnv).encodeJson()),"/dashboard/*") { port =>
      val client = new DashboardUserClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountCrdentials("88-16ca6193-4149-456b-ae00-00fdad2437c6"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe(UserFixture.accessCredsEnv)
      }
    }
  }

  test("should get account credentials return AccountNotFound") {

    withServlet(new StringServlet(400, Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound)).encodeJson()),"/dashboard/*") { port =>
      val client = new DashboardUserClientImpl(s"http://localhost:$port")
      whenReady(client.getAccountCrdentials("xxx"), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
        response.left.value shouldBe(ErrorResponseFactory.get(AccountNotFound))
      }
    }
  }
}
