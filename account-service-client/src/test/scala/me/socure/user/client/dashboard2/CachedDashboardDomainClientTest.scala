package me.socure.user.client.dashboard2

import me.socure.account.service.common.DashboardDomainCacheKeyProvider
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.storage.Storage
import me.socure.model.account.AccountDashboardDomain
import org.mockito.Mockito
import org.mockito.Mockito.mock
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}
import org.scalatest.concurrent.ScalaFutures

import scala.concurrent.{ExecutionContext, Future}

class CachedDashboardDomainClientTest extends FreeSpec with Matchers with ScalaFutures with BeforeAndAfter{
  private implicit val ec = ExecutionContext.Implicits.global
  val domainStorage: Storage[AccountDashboardDomain] = mock(classOf[Storage[AccountDashboardDomain]])
  val domainStorageSeq: Storage[Seq[AccountDashboardDomain]] = mock(classOf[Storage[Seq[AccountDashboardDomain]]])
  val underlying: DashboardDomainClient = mock(classOf[DashboardDomainClient])
  private val client = new CachedDashboardDomainClient(underlying, domainStorage, domainStorageSeq, None)

  private val accountId = 1234
  private val cacheKey = DashboardDomainCacheKeyProvider.provide(accountId)
  val email = "<EMAIL>"
  val cacheKeyEmail = DashboardDomainCacheKeyProvider.provide(email)
  val accountDashboardDomainList = Seq(AccountDashboardDomain(1, true, Some("test-domain.com")))

  before {
    Mockito.reset(underlying, domainStorage)
  }

  val accountDashboardDomain = AccountDashboardDomain(
    accountId=1234,
    domainWhiteEnabled=true,
    whiteListedDomain=Some("*******/1")
  )

  "CachedDashboardDomainClient" - {
    "should list Ip domain" in {
      Mockito.when(domainStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.getDashboardWhiteListPermissionAndDomainsById(accountId)).thenReturn(Future.successful(Right(accountDashboardDomain)))
      Mockito.when(domainStorage.store(cacheKey, accountDashboardDomain)).thenReturn(Future.successful(()))
      whenReady(client.getDashboardWhiteListPermissionAndDomainsById(accountId)) { result =>
        result shouldBe Right(accountDashboardDomain)
        Mockito.verify(domainStorage).get(cacheKey)
        Mockito.verify(underlying).getDashboardWhiteListPermissionAndDomainsById(accountId)
        Mockito.verify(domainStorage).store(cacheKey, accountDashboardDomain)
      }
    }

    "should not store to cache if get DashboardWhiteList Permission And Domains By Id throws an error" in {
      val response = Left(ErrorResponseFactory.get(ExceptionCodes.UnknownError))
      Mockito.when(domainStorage.get(cacheKey)).thenReturn(Future.successful(None))
      Mockito.when(underlying.getDashboardWhiteListPermissionAndDomainsById(accountId)).thenReturn(Future.successful(response))
      Mockito.when(domainStorage.store(cacheKey, accountDashboardDomain)).thenReturn(Future.successful(()))
      whenReady(client.getDashboardWhiteListPermissionAndDomainsById(accountId)) { result =>
        result shouldBe response
        Mockito.verify(domainStorage).get(cacheKey)
        Mockito.verify(underlying).getDashboardWhiteListPermissionAndDomainsById(accountId)
        Mockito.verify(domainStorage, Mockito.never()).store(cacheKey, accountDashboardDomain)
      }
    }

    "should get from cache when available" in {
      Mockito.when(domainStorage.get(cacheKey)).thenReturn(Future.successful(Some(accountDashboardDomain)))
      Mockito.when(underlying.getDashboardWhiteListPermissionAndDomainsById(accountId)).thenReturn(Future.successful(Right(accountDashboardDomain)))
      Mockito.when(domainStorage.store(cacheKey, accountDashboardDomain)).thenReturn(Future.successful(()))
      whenReady(client.getDashboardWhiteListPermissionAndDomainsById(accountId)) { result =>
        result shouldBe Right(accountDashboardDomain)
        Mockito.verify(domainStorage).get(cacheKey)
        Mockito.verify(underlying, Mockito.never()).getDashboardWhiteListPermissionAndDomainsById(accountId)
        Mockito.verify(domainStorage, Mockito.never()).store(cacheKey, accountDashboardDomain)
      }
    }

    "should get domain by email from cache when available" in {
      // Mock behavior
      Mockito.when(domainStorageSeq.get(cacheKeyEmail)).thenReturn(Future.successful(Some(accountDashboardDomainList)))
      Mockito.when(underlying.getAssociatedAccountDashboardDomainListByEmailId(email))
        .thenReturn(Future.successful(Right(accountDashboardDomainList)))
      Mockito.when(domainStorageSeq.store(cacheKeyEmail, accountDashboardDomainList))
        .thenReturn(Future.successful(()))

      whenReady(client.getAssociatedAccountDashboardDomainListByEmailId(email)) { result =>
        result shouldBe Right(accountDashboardDomainList)

        Mockito.verify(domainStorageSeq).get(cacheKeyEmail)

        // Ensure underlying service was never called since data was retrieved from cache
        Mockito.verify(underlying, Mockito.never()).getAssociatedAccountDashboardDomainListByEmailId(email)

        // Verify that the cache was not updated, as it was already available
        Mockito.verify(domainStorageSeq, Mockito.never()).store(cacheKeyEmail, accountDashboardDomainList)
      }
    }

  }
}
