package me.socure.user.client.dashboard2

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.{AccountApiKeys, AccountDetails, ApiKeyDetails, ApiKeyStatus, SubAccountApiKeys}
import me.socure.model.dashboardv2.AccountInfoWithEnvDetails
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.{IdAndName, Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, <PERSON>, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.{ExecutionContext, ExecutionContextExecutor}

/**
  * Created by sunderraj on 8/19/16.
  */
class SubAccountManagementClientTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit val ec: ExecutionContextExecutor = ExecutionContext.global

  val host = "http://localhost"

  test("should return account list"){
    val response = Response(ResponseStatus.Ok, Vector(UserFixture.accountDetailsV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/list_accounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccountList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe Vector(UserFixture.accountDetailsV2)
      }
    }
  }

  test("get account list should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/list_accounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccountList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("should return sub account list"){
    val response = Response(ResponseStatus.Ok, List(UserFixture.subAccountV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/list_sub_accounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccounts(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe List(UserFixture.subAccountV2)
      }
    }
  }

  test("should return sub account list with environment info"){
    val expectedResult = Set(
      AccountInfoWithEnvDetails(
        id = 2,
        name = Some("AccountName2"),
        environments = Set(
          IdAndName(id = 4, name = None)
        )
      ),
      AccountInfoWithEnvDetails(
        id = 3,
        name = Some("AccountName3"),
        environments = Set(
          IdAndName(id = 11, name = None),
          IdAndName(id = 12, name = None)
        )
      )
    )
    val response = Response(ResponseStatus.Ok, expectedResult)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/list_sub_accounts_with_env_details/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccountsWithEnvDetails(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe expectedResult
      }
    }
  }

  test("should return error response when sub account not found") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/list_sub_accounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccounts(1)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on create account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/create_sub_account") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccount(1, UserFixture.userForm), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should return error response on create account") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/create_sub_account") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccount(1, UserFixture.userForm)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on create account v2"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/create_sub_account_v2") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountV2(1, UserFixture.subAccountFormV2), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should return error response on create account v2") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/create_sub_account_v2") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountV2(1, UserFixture.subAccountFormV2)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on create account with minimum details"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/create_sub_account_with_min_details") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountWithMinDetails(1, UserFixture.subAccount, None, None, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should return success response on create account with minimum details and primary user"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/create_sub_account_with_min_details") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountWithMinDetails(1, UserFixture.subAccount, None, None, Some(1L)), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should return error response on create account with minimum details") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RegistrationFailed))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/create_sub_account_with_min_details") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountWithMinDetails(1, UserFixture.subAccount, None, None, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on create account with set password"){
    val uad = UserActivationDetails("firstname", "lastname", "email", Option("activationcode"))
    val response = Response(ResponseStatus.Ok, uad)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/create_sub_account_with_no_password") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.createSubAccountWithNoPassword(1, UserFixture.userFormWithNoPassword), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe uad
      }
    }
  }

  test("should return success response on update account"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/update_sub_account") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.updateSubAccount(UserFixture.userForm), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("should return success response on udpate account") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/update_sub_account") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.updateSubAccount(UserFixture.userForm)) { res =>
        res should be ('left)
      }
    }
  }

  test("activate account should return true"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/activate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.activate(1, None, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("activate account should return account not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/activate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.activate(1, None, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("activate account V2 should return true"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/activate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.activate(1, Some(1L), Some(1L)), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("activate account V2 should fail"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/activate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.activate(1, Some(1L), Some(1L))) { res =>
        res should be ('left)
      }
    }
  }

  test("deactivate account should return true"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/deactivate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.deactivate(1, None, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe true
      }
    }
  }

  test("deactivate account should return account not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/deactivate") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.deactivate(1, None, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("get account list should return list"){
    val list = Vector(AccountDetails(1, "<EMAIL>", "account_name"))
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/get_account_list/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }

  test("get account list should return account not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/get_account_list/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountList(1)) { res =>
        res should be ('left)
      }
    }
  }

  test("deactivate api keys should return Api key status updated"){
    val expectedRes = Response(ResponseStatus.Ok, "Api keys status updated")
    withServlet(new StringServlet(200, expectedRes.encodeJson()), "/dashboard_account/deprecate_api_keys") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.deprecateApiKeys()) { res =>
        res should be ('right)
      }
    }
  }

  test("deactivate api keys should return error response"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/deprecate_api_keys") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.deprecateApiKeys()) { res =>
        res should be ('left)
      }
    }
  }

  test("should list sub accounts with api keys") {
    val subAccountWithApiKeys = List(
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"),
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, canBeRenewed = true,None)),18,"Development"))
    val result = Response(ResponseStatus.Ok, subAccountWithApiKeys)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/get_apikeys_for_subaccounts/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForSubAccounts(1L)) { res =>
        res.right.value shouldBe result.data
      }
    }
  }

  test("should return error response on getting sub accounts with apikeys") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/get_apikeys_for_subaccounts/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForSubAccounts(999L)) { res =>
        res.left.value.code shouldBe expectedRes.data.code
      }
    }
  }

  test("should list sub accounts with api keys V2") {
    val subAccountWithApiKeys = List(
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"),
      SubAccountApiKeys("AccountName13",Vector(ApiKeyDetails("181926ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, Some(360897)),
        ApiKeyDetails("1826ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.NEW, canBeRenewed = true,None)),18,"Development"))
    val result = Response(ResponseStatus.Ok, subAccountWithApiKeys)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/get_apikeys_for_subaccounts/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForSubAccounts(1L, None, None)) { res =>
        res.right.value shouldBe result.data
      }
    }
  }

  test("should return error response on getting sub accounts with apikeys V2") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/get_apikeys_for_subaccounts/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getApiKeysForSubAccounts(999L, None, None)) { res =>
        res.left.value.code shouldBe expectedRes.data.code
      }
    }
  }

  test("should validate account access by apikey") {
    val result = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/validate/apikey/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccess("apiKey", 1L, 1L, "12")) { response =>
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response on validating the account access by apikey") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/validate/apikey/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccess("invalid apiKey", 1L, 1L, "12")) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should validate account access by environment type") {
    val result = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/validate/environmenttype/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccess(1, 1L, 1L, "12")) { response =>
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response on validating the account access by environment type") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/validate/environmenttype/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccess(100, 1L, 1L, "12")) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should validate account access by environment type with orpermissions") {
    val result = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/validate/environmenttype/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccessWithOrPermissions(1, 1L, 1L, "12", Some("1001"))) { response =>
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("should return error response on validating the account access by environment type with orpermissions") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccessForbidden))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/validate/environmenttype/access/*") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.validateAccountAccessWithOrPermissions(100, 1L, 1L, "12", Some("1001"))) { response =>
        response.fold(_ shouldBe ErrorResponseFactory.get(AccessForbidden), _ => fail)
      }
    }
  }

  test("should return sub account list with pagination"){
    val response = Response(ResponseStatus.Ok, List(UserFixture.subAccountV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/subaccounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccountsV2(1, 1, 1, Some(1), Some(1)), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe List(UserFixture.subAccountV2)
      }
    }
  }

  test("should return error response for get sub accounts v2") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(SubAccountNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_account/subaccounts/*") { port =>
      val client = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getSubAccountsV2(1, 1, 1, Some(1), Some(1))) { res =>
        res should be ('left)
      }
    }
  }

  test("get account API keys by env type ID") {
    val accountWithApiKeysList = List(
      AccountApiKeys(13, "AccountName13",Vector(ApiKeyDetails("1716ca6193-4149-456b-ae00-00fdad2437c6", ApiKeyStatus.ACTIVE, canBeRenewed = true, None)),17,"Production"))
    val result = Response(ResponseStatus.Ok, accountWithApiKeysList)
    withServlet(new StringServlet(200, result.encodeJson()), "/dashboard_account/get_apikeys_for_account_by_envType/13/1") { port =>
      val client : SubAccountManagementClient = new SubAccountManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountAPIKeysByEnvTypeId(13,1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res.right.value shouldBe result.data
      }
    }
  }
}
