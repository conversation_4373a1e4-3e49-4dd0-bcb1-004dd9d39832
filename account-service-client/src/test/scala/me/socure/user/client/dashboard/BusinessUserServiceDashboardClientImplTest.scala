package me.socure.user.client.dashboard

import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext
/**
  * Created by gopal on 19/05/16.
  */
class BusinessUserServiceDashboardClientImplTest extends FunSuite with Matchers with ScalaFutures with EitherValues {

  implicit val ec = ExecutionContext.global

  test("should return business user") {

    withServlet(new StringServlet(200, Response(ResponseStatus.Ok, UserFixture.userAuth).encodeJson()),"/user/*") { port =>
      val client = new BusinessUserServiceDashboardClientImpl(s"http://localhost:$port/user")

      whenReady(client.validateUser(UserFixture.userCredential), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response.right.value.data shouldBe(UserFixture.userAuth)
      }
    }
  }

  test("should return error response") {

    withServlet(new StringServlet(400, Response(ResponseStatus.Error, UserFixture.userpasswordMismatchError).encodeJson()),"/user/*") { port =>
      val client = new BusinessUserServiceDashboardClientImpl(s"http://localhost:$port/user")

      whenReady(client.validateUser(UserFixture.userCredential), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'left
        response.left.value shouldBe(UserFixture.userpasswordMismatchError)
      }
    }
  }

}
