package me.socure.user.client

import me.socure.account.service.common.exceptions.ExceptionCodes.AccountNotFound
import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.{ServletTester, StringServlet}
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.BusinessUserWithRoles
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by gopal on 31/05/16.
  */
class AdminUsersClientImplTest extends FunSuite with ScalaFutures with Matchers with EitherValues {

  implicit val ec:ExecutionContext = ExecutionContext.global

  implicit val defaultPatience:PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val list = List(UserActivationDetails("firstname", "surname", "email", Option("activationcode")))
  val activationCodeResponse = Response(ResponseStatus.Ok, list)
  val host = "http://localhost"

  test("should return active user") {
    ServletTester.withServlet(new StringServlet(200, UserFixture.activeAdminUserResponse.encodeJson()), "/user/active_users_v2") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activeUsers(None, None, Some(""))) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe UserFixture.primaryUsersL)
        }
      }
    }
  }

  test("should return error response") {
    ServletTester.withServlet(new StringServlet(400, UserFixture.errorResponseJson.encodeJson()), "/user/active_users_v2") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activeUsers(None, None, Some(""))) { res =>
          res should be('left)
          res.fold(_ shouldBe UserFixture.errorResponse, _ => fail)
        }
      }
    }
  }

  test("should return active user count") {
    ServletTester.withServlet(new StringServlet(200, Response(ResponseStatus.Ok, 10).encodeJson()), "/user/active_users_count") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activeUsersCount) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe 10)
        }
      }
    }
  }

  test("should return error response for active user count") {
    ServletTester.withServlet(new StringServlet(400, UserFixture.errorResponseJson.encodeJson()), "/user/active_users_count") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activeUsersCount) { res =>
          res should be('left)
          res.fold(_ shouldBe UserFixture.errorResponse, _ => fail)
        }
      }
    }
  }

  test("should return inactive user") {
    ServletTester.withServlet(new StringServlet(200, UserFixture.inactiveAdminUserResponse.encodeJson), "/inactive/inactive_users_v2") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.inactiveUsers) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe UserFixture.inActivePrimaryUsersL)
        }
      }
    }
  }

  test("should return error response for inactive") {
    ServletTester.withServlet(new StringServlet(400, UserFixture.errorResponseJson.encodeJson), "/inactive/inactive_users_v2") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.inactiveUsers) { res =>
          res should be('left)
          res.fold(_ shouldBe UserFixture.errorResponse, _ => fail)
        }
      }
    }
  }

  test("should return activiation details user") {
    ServletTester.withServlet(new StringServlet(200, activationCodeResponse.encodeJson), "/inactive/get_activation_code/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.getActivationCode("<EMAIL>")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe list)
        }
      }
    }
  }

  test("get activation code should throw error") {
    ServletTester.withServlet(new StringServlet(400, UserFixture.errorResponseJson.encodeJson), "/inactive/get_activation_code/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.getActivationCode("<EMAIL>")) { res =>
          res should be('left)
          res.fold(_ shouldBe(UserFixture.errorResponse), _ => fail)
        }
      }
    }
  }

  test("validate activation code should be ok") {
    val validation = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, validation.encodeJson), "/user/activate_user/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activateUserByActivationCode("validcode")) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("should return invalid activationcode") {
    val validationError  = ErrorResponse(ExceptionCodes.InvalidActivationCode.id, ExceptionCodes.InvalidActivationCode.description)
    ServletTester.withServlet(new StringServlet(400, Response(ResponseStatus.Error, validationError).encodeJson), "/user/activate_user/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        whenReady(client.activateUserByActivationCode("invalidcode")) { res =>
          res should be('left)
          res.fold(_ shouldBe validationError, _ => fail)
        }
      }
    }
  }

  test("activate users should be ok") {
    val validation = Response(ResponseStatus.Ok, true)
    ServletTester.withServlet(new StringServlet(200, validation.encodeJson), "/user/activate_users/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        val users = List("emmail1", "email2")
        whenReady(client.activateUsers(users)) { res =>
          res should be('right)
          res.fold(_ => fail, _ shouldBe true)
        }
      }
    }
  }

  test("activate users should fail") {
    val activationError  = ErrorResponse(ExceptionCodes.UserNotActivated.id, ExceptionCodes.UserNotActivated.description)
    ServletTester.withServlet(new StringServlet(400, Response(ResponseStatus.Error, activationError).encodeJson), "/user/activate_users/*") {
      port => {
        val client = new AdminUsersClientImpl(s"http://localhost:$port")
        val users = List("emmail1", "email2")
        whenReady(client.activateUsers(users)) { res =>
          res should be('left)
          res.fold(_ shouldBe activationError, _ => fail)
        }
      }
    }
  }

  test("get_users_with_roles should return active users for the account"){
    val users = List(
      BusinessUserWithRoles(4,"gopal","haris","<EMAIL>","**********",false,Vector("Users", "Accounts"),Vector(("Production",Set("Overview", "Settings", "Create Transcation")), ("Development",Set("List Transaction", "Overview")))))

    val response = Response(ResponseStatus.Ok, users)
    withServlet(new StringServlet(200, response.encodeJson()),"/user/get_users_with_roles/1") { port =>
      val client = new AdminUsersClientImpl(s"$host:$port")
      whenReady(client.getBusinessUsersWithRoles(1)) { response =>
        def onSuccess(users: List[BusinessUserWithRoles]) = {
          users.size shouldBe 1
          users shouldBe users
        }
        response.fold(_ => fail, onSuccess(_))
      }
    }
  }

  test("get_users_with_roles should return error response if the account does not exist"){
    val response = Response(ResponseStatus.Error, ErrorResponseFactory.get(AccountNotFound))
    withServlet(new StringServlet(400, response.encodeJson()),"/user/get_users_with_roles/100") { port =>
      val client = new AdminUsersClientImpl(s"$host:$port")
      whenReady(client.getBusinessUsersWithRoles(100L)) { response =>
        response.fold(_.code shouldBe AccountNotFound.id, _ => fail)
      }
    }
  }

  test("check if email exist - success"){
    val email = "<EMAIL>"
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/user/does_email_exist") { port =>
      val client = new AdminUsersClientImpl(s"$host:$port")
      whenReady(client.doesEmailExist(email)) { response =>
        response shouldBe Right(true)
      }
    }
  }

  test("check if email exist - failure"){
    val email = "<EMAIL>"
    val response = Response(ResponseStatus.Ok, false)
    withServlet(new StringServlet(200, response.encodeJson()),"/user/does_email_exist") { port =>
      val client = new AdminUsersClientImpl(s"$host:$port")
      whenReady(client.doesEmailExist(email)) { response =>
        response shouldBe Right(false)
      }
    }
  }

  test("check if email exist - error"){
    val email = "<EMAIL>"
    val expected: ErrorResponse = ErrorResponseFactory.get(ExceptionCodes.UnknownError)
    val response = Response(ResponseStatus.Error, expected)
    withServlet(new StringServlet(400, response.encodeJson()),"/user/does_email_exist") { port =>
      val client = new AdminUsersClientImpl(s"$host:$port")
      whenReady(client.doesEmailExist(email)) { response =>
        response shouldBe Left(expected)
      }
    }
  }
}
