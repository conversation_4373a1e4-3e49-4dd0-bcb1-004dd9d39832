package me.socure.user.client.dashboard2

import me.socure.common.random.Random
import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.account.AccountDashboardDomain
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{FreeSpec, Matchers}

import scala.concurrent.ExecutionContext

class DashboardDomainClientImplTest extends FreeSpec with Matchers with ScalaFutures {

  private implicit val ec: ExecutionContext = ExecutionContext.global
  private implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(500, Seconds), interval = Span(500, Millis))

  private val host = "http://localhost"
  private val urlPrefix = "/dashboard/domain"

  "DashboardDomainClientImpl" - {
    "should get dashboard whitelist permission and domains by id" in {
      val accountId = Random.nextLong()
      val accountDashboardDomain = AccountDashboardDomain(
        accountId = accountId,
        domainWhiteEnabled = Random.nextBoolean(),
        whiteListedDomain = Random.option(Random.alphaNumeric(n = 30))
      )
      val expectedRes = Response(ResponseStatus.Ok, accountDashboardDomain)
      withServlet(new StringServlet(200, expectedRes.encodeJson()), s"$urlPrefix/get_permission_domain_by_account_id/$accountId") { port =>
        val client = new DashboardDomainClientImpl(s"$host:$port")
        whenReady(client.getDashboardWhiteListPermissionAndDomainsById(accountId = accountId)) { res =>
          res shouldBe Right(accountDashboardDomain)
        }
      }
    }
  }
}
