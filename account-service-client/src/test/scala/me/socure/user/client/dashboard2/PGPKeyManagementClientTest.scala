package me.socure.user.client.dashboard2

import me.socure.common.servlettester.ServletTester.withServlet
import me.socure.common.servlettester.StringServlet
import me.socure.model.superadmin.AccountPgpInfo
import me.socure.model.{Response, ResponseStatus}
import me.socure.util.JsonEnrichments._
import org.joda.time.{DateTime, DateTimeZone}
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{Millis, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by an<PERSON><PERSON><PERSON><PERSON> on 5/15/17.
  */
class PGPKeyManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
    implicit val ec : ExecutionContext = ExecutionContext.global
    implicit val defaultPatience = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return pgp public key"){
    lazy val pgpKey1 = "public_key"
    val response = Response(ResponseStatus.Ok, pgpKey1)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_public_key/*") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getAccountPgpPublicKey(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe pgpKey1
      }
    }
  }

  test("should return list of account info"){
    lazy val list = List(AccountPgpInfo(1, "acc-1234" , "<EMAIL>", new DateTime("2017-12-06", DateTimeZone.UTC)))
    val response = Response(ResponseStatus.Ok, list)
    withServlet(new StringServlet(200, response.encodeJson()),"/pgp/get_active_pgp_account_list") { port =>
      val client = new PGPKeysManagementClientImpl(s"$host:$port")
      whenReady(client.getActivePgpAccountList(), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.right.value shouldBe list
      }
    }
  }
}
