package me.socure.user.client.dashboard2

import me.socure.account.service.common.exceptions.{ErrorResponseFactory, ExceptionCodes}
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.common.servlettester.ServletTester._
import me.socure.common.servlettester.StringServlet
import me.socure.constants.CaseManagementPermissions
import me.socure.model.account.{AccountIdName, UserRole}
import me.socure.model.dashboardv2.{CreateBusinessUserInput, _}
import me.socure.model.superadmin.UserActivationDetails
import me.socure.model.user.UpdateQuicksightUserStatus
import me.socure.model.{Response, ResponseStatus}
import me.socure.user.fixure.UserFixture
import me.socure.util.JsonEnrichments._
import org.scalatest.concurrent.PatienceConfiguration.{Interval, Timeout}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{<PERSON><PERSON>, Seconds, Span}
import org.scalatest.{EitherValues, FunSuite, Matchers}

import scala.concurrent.ExecutionContext

/**
  * Created by sun<PERSON>raj on 8/19/16.
  */
class BusinessUserManagementClientTest extends FunSuite with Matchers with EitherValues with ScalaFutures {
  implicit val ec : ExecutionContext = ExecutionContext.global
  implicit val defaultPatience: PatienceConfig = PatienceConfig(timeout = Span(5, Seconds), interval = Span(500, Millis))

  val host = "http://localhost"

  test("should return account list"){
    val response = Response(ResponseStatus.Ok, Vector(UserFixture.dashboardV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/list_users/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should return users list"){
    val response = Response(ResponseStatus.Ok, Vector(UserFixture.dashboardV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/list_users/*") { port =>  //TODO: It's better if we can check if the servlet receives the param as expected
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getAllUserList(1, None, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should return account and subaccount userids"){
    val response = Response(ResponseStatus.Ok, Vector[Long](10,5,1))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/list_userids/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getAllUserIdsList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe Vector[Long](10,5,1))
      }
    }
  }

  test("should return users list - V2"){
    val response = Response(ResponseStatus.Ok, Vector(UserFixture.dashboardV2))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/list_users/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getAllUserList(1, Some(1), Some(1)), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response.fold(_ => fail, _ shouldBe Vector[DashboardUserV2](UserFixture.dashboardV2))
      }
    }
  }

  test("get users list should return error response") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/list_users/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserList(1), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on create user"){
    val environmentList = Seq(EnvironmentNameAndId(1L, "Production"))
    val details = UserActivationDetails("first", "sur", "email", Option("activation"))

    val responseUser = Response(ResponseStatus.Ok, details)
    val responseEnvironments = Response(ResponseStatus.Ok, environmentList)
    val servlets = List((new StringServlet(200, responseUser.encodeJson()), "/dashboard_user/create_user"),
                        (new StringServlet(200, responseEnvironments.encodeJson()), "/environment_settings/get_environments/*"))
      withServlets(servlets) { port =>
        val client = new BusinessUserManagementClientImpl(s"$host:$port")
        whenReady(client.createBusinessUser(1L, UserFixture.businessUserFormRequest), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
          response shouldBe 'right
          response.fold(_ => fail, _ shouldBe details)
        }
      }
  }

  test("create user should return unknown error") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    val servlets = List((new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/create_user"),
                        (new StringServlet(400, expectedRes.encodeJson()), "/environment_settings/get_environments/*"))
    withServlets(servlets) { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.createBusinessUser(1, UserFixture.businessUserFormRequest), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("should return success response on update user"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/update_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateBusinessUser(1, UserFixture.businessUserForm), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("update user should return unknown error") {
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UnknownError))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/update_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateBusinessUser(1, UserFixture.businessUserForm), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("lock user status should be 200"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/lock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.lockBusinessUser(1, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("lock user status V2 should be 200"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/lock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.lockBusinessUser(1, Some(AccountWithCreator(1L, Creator(1L, 1L)))), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("lock user status should be 400"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UserNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/lock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.lockBusinessUser(1, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("unlock user status should be 200"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/unlock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.unlockBusinessUser(1, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("unlock user V2 status should be 200"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/unlock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.unlockBusinessUser(1, Some(AccountWithCreator(1L, Creator(1L, 1L)))), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("unlock user status should be 400"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UserNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/unlock_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.unlockBusinessUser(1, None), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { res =>
        res should be ('left)
      }
    }
  }

  test("delete user status should be 200"){
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), Some(AccountDelta("<EMAIL>", None, None, None, Some(Seq(AccountWithRoles(AccountIdName(22, "Account"), Seq.empty))), true)), None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails,true))

    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/delete_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.deleteBusinessUser(1, None)) { response =>
        response._2 shouldBe 'right
        response._2 fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("delete user V2 status should be 200"){
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), Some(AccountDelta("<EMAIL>", None, None, None, Some(Seq(AccountWithRoles(AccountIdName(22, "Account"), Seq.empty))), true)), None, Seq.empty)

    val response = Response(ResponseStatus.Ok, (auditDetails,true))
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/delete_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.deleteBusinessUser(1, Some(AccountWithCreator(1L, Creator(1L, 1L))))) { response =>
        response._2 shouldBe 'right
        response._2.fold(_ => fail, _ shouldBe true)
      }
    }
  }

  test("delete user status should be 400"){
    val auditDetails = AuditDetails(false, ActionUserInfo(UserDetails(0, "firstName", "lastName", "<EMAIL>", ""), Set(),
      Seq()), None, Some(ErrorResponseFactory.get(UserNotFound)), Seq.empty)
    val expectedRes = Response(ResponseStatus.Error,(auditDetails, ErrorResponseFactory.get(UserNotFound)))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/delete_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.deleteBusinessUser(1, None)) { res =>
        res._2 should be ('left)
      }
    }
  }

  test("get user with roles status should be 200"){
    val roles = DashboardUserWithRoles(UserFixture.dashboardV2, Set(13), Seq(EnvironmentRoles(1, Set(1,2))))
    val response = Response(ResponseStatus.Ok, roles)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/get_user_information/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getBusinessUserWithRoles(1, None)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe roles)
      }
    }
  }

  test("get user with roles status should be 400 with role not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(RolesNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/get_user_information/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getBusinessUserWithRoles(1, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("get user with roles status should be 400 with user not found"){
    val expectedRes = Response(ResponseStatus.Error, ErrorResponseFactory.get(UserNotFound))
    withServlet(new StringServlet(400, expectedRes.encodeJson()), "/dashboard_user/get_user_information/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getBusinessUserWithRoles(1, None)) { res =>
        res should be ('left)
      }
    }
  }

  test("force_reset_password should return 200 when it works"){
    val details = UserActivationDetails("first", "sur", "email", Option("activation"))
    val response = Response(ResponseStatus.Ok, details)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/force_reset_password/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.forceResetPassword(1, None)) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe details)
      }
    }
  }

  test("force_reset_password V2 should return 200 when it works"){
    val details = UserActivationDetails("first", "sur", "email", Option("activation"))
    val accountWithCreator = AccountWithCreator(1L, Creator(1L, 1L))
    val response = Response(ResponseStatus.Ok, details)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/force_reset_password/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.forceResetPassword(1, Some(accountWithCreator))) { response =>
        response shouldBe 'right
        response.fold(_ => fail, _ shouldBe details)
      }
    }
  }

  test("force_reset_password should return 400 when it does not work"){
    val response = Response(ResponseStatus.Error, ErrorResponseFactory.get(UserNotFound))
    withServlet(new StringServlet(400, response.encodeJson()),"/dashboard_user/force_reset_password/*") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.forceResetPassword(1, None)) { response =>
        response shouldBe 'left
      }
    }
  }

  test("get_user_id should return id properly"){
    val response = Response(ResponseStatus.Ok, 1)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/get_user_id/test") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserId("test")) { response =>
        response.fold(_ => fail, _ shouldBe 1)
      }
    }
  }

  test("get_user_id should return error response if the user does not exist"){
    val response = Response(ResponseStatus.Error, ErrorResponseFactory.get(UserNotFound))
    withServlet(new StringServlet(400, response.encodeJson()),"/dashboard_user/get_user_id/test") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserId("test")) { response =>
        response.fold(_.code shouldBe UserNotFound.id, _ => fail)
      }
    }
  }

  test("should list business user with associations") {
    val userId = 8
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val data = DashboardUserWithAssociations(UserDetails(11,"guest11","user","<EMAIL>","**********"),List(AccountWithRoles(AccountIdName(6,"sub-account2"),Vector(UserRole(Some(1),"Role-9",None)))))
    val response = Response(ResponseStatus.Ok, data)
    withServlet(new StringServlet(
      200,
      response.encodeJson()),
      s"/dashboard_user/get_user_information_v2/$userId") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserWithRolesAndAssociations(userId, creator)) { response =>
        response.fold(_ => fail, _ shouldBe data)
      }
    }
  }

  test("should not list business user with associations for v1 account") {
    val userId = 8
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val error = ErrorResponseFactory.get(ExceptionCodes.InvalidUserAccountAssociation)
    val response = Response(ResponseStatus.Ok, error)
    withServlet(new StringServlet(
      400,
      response.encodeJson()),
      s"/dashboard_user/get_user_information_v2/$userId") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUserWithRolesAndAssociations(userId, creator)) { response =>
        response shouldBe Left(error)
      }
    }
  }

  test("should update business user with associations") {
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val data = true
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "email", "**********"), Set.empty[Int], Seq.empty[String]), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails,data))
    withServlet(new StringServlet(
      200,
      response.encodeJson()),
      s"/dashboard_user/update_business_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateBusinessUserWithAssociations(updateBusinessUserInput)) { response =>
        response._2.fold(_ => fail, _ shouldBe data)
      }
    }
  }

  test("should not update business user with associations") {
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val updateBusinessUserInput = UpdateBusinessUserInput(id = 11,firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val error = ErrorResponseFactory.get(ExceptionCodes.InvalidUserAccountAssociation)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "email", "**********"), Set.empty[Int], Seq.empty[String]), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails,error))
    withServlet(new StringServlet(
      400,
      response.encodeJson()),
      s"/dashboard_user/update_business_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateBusinessUserWithAssociations(updateBusinessUserInput)) { response =>
        response._2 shouldBe Left(error)
      }
    }
  }

  test("should create business user with associations") {
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val createBusinessUserInput = CreateBusinessUserInput(accountId = 8, email = "email", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val data = UserActivationDetails("first", "last", "email", Option("activation"))
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "email", "**********"), Set.empty[Int], Seq.empty[String]), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails,data))
    withServlet(new StringServlet(
      200,
      response.encodeJson()),
      s"/dashboard_user/create_business_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.createBusinessUserWithAssociations(createBusinessUserInput)) { response =>
        response._2.fold(_ => fail, _ shouldBe data)
      }
    }
  }

  test("should not create business user with associations") {
    val creatorUserId = 13
    val creatorAccountId = 8
    val creator = Creator(creatorUserId,creatorAccountId)
    val createBusinessUserInput = CreateBusinessUserInput(accountId = 8, email = "email", firstName = "test",lastName = "test",contactNumber = "**********",accountsWithRoles = Seq(AccountWithRolesInput(accountId = 6, roles= Seq(RolesInputDetails(roleType = 0, roleId = Some(2))))),creator = creator)
    val error = ErrorResponseFactory.get(ExceptionCodes.InvalidUserAccountAssociation)
    val auditDetails = AuditDetails(true, ActionUserInfo(UserDetails(0, "firstName", "lastName", "email", "**********"), Set.empty[Int], Seq.empty[String]), None, None, Seq.empty)
    val response = Response(ResponseStatus.Ok, (auditDetails,error))
    withServlet(new StringServlet(
      400,
      response.encodeJson()),
      s"/dashboard_user/create_business_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.createBusinessUserWithAssociations(createBusinessUserInput)) { response =>
        response._2 shouldBe Left(error)
      }
    }
  }

  test("should return users list filtered by permission"){
    val requestData = UsersByPermissionFilterRequest(Seq(1), Some(Seq(1,2)), Seq(CaseManagementPermissions.CaseManagementReview.name), 1, None, None)
    val expectedResp = UsersByPermissionFilterResponse(Seq(UserDetailsByPermissionFilter(1,"test", "last", "<EMAIL>", None, 1, 1)), None, None, 1)
    val response = Response(ResponseStatus.Ok, expectedResp)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/list_users/*") { port =>  //TODO: It's better if we can check if the servlet receives the param as expected
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getUsersFilteredByPermission(requestData), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should return associated accounts of an user"){
    val response = Response(ResponseStatus.Ok, Seq.empty[AccountIdName])
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_account/get_associated_accounts_for_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.getAssociatedAccounts(13), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should return if user is registered in quicksight"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/is_quicksight_user") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.isUserRegisteredInQuicksight(13), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should update quicksight registered user"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/update_quicksight_user_status") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateQuickSightUserStatus(UpdateQuicksightUserStatus(userId = 13, status = true)), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }

  test("should update terms of service"){
    val response = Response(ResponseStatus.Ok, true)
    withServlet(new StringServlet(200, response.encodeJson()),"/dashboard_user/update_user_tos/13") { port =>
      val client = new BusinessUserManagementClientImpl(s"$host:$port")
      whenReady(client.updateUserTos(13), Timeout(Span(10, Seconds)), Interval(Span(500, Millis))) { response =>
        response shouldBe 'right
      }
    }
  }
}
