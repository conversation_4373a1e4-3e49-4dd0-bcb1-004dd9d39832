{"status": "ok", "data": {"essential": {"status": "ACTIVE", "publicId": "1", "accountId": "1", "accountName": "accountName", "industry": {"sector": "some sector", "description": "some industry"}, "isInternal": true, "environment": {"id": 456, "publicApiKeys": [], "name": "environment", "domain": ["domain1", "domain2"], "accessCredentials": {"apiKey": "api-key", "secretKey": "secret key", "accessToken": "access token", "accessTokenSecret": "access token secret", "certificate": "certificate"}, "socialAccounts": [{"id": 145, "provider": "provider", "appkey": "app key", "appsecret": "app secret", "environment": 566, "accountId": 999}], "invidiualCache": [{"id": 666, "identifier": "identifier1", "date": "0", "accountId": 876}], "overallCache": {"id": 234, "date": "0", "accountId": 8765}}, "rootParentId": 1}, "setting": {"watchlistPreference": {"environmentId": 100, "exactDoB": true, "dobAndName": false, "matchScore": 90, "categories": ["sanctions", "enforcement", "pep"]}, "watchlistPreference_3_0": {"environmentId": 100, "exactDoB": true, "dobMatchLogic": "exact_yyyy", "dobAndName": false, "monitoring": false, "matchingThresholds": 0.5, "limit": 10, "screeningCategories": ["pep"], "country": ["UK"]}, "watchlistPreferences_3_0": {"standard": {"environmentId": 100, "exactDoB": true, "dobMatchLogic": "exact_yyyy", "dobAndName": false, "monitoring": false, "matchingThresholds": 0.5, "limit": 10, "screeningCategories": ["warning"], "country": ["US"]}, "plus": {"environmentId": 100, "exactDoB": true, "dobMatchLogic": "exact_yyyy", "dobAndName": false, "monitoring": false, "matchingThresholds": 0.5, "limit": 10, "screeningCategories": ["warning"], "country": ["US"]}, "premier": {"environmentId": 100, "exactDoB": true, "dobMatchLogic": "exact_yyyy", "dobAndName": false, "monitoring": false, "matchingThresholds": 0.5, "limit": 10, "screeningCategories": ["warning"], "country": ["US"]}}, "roles": [22], "primaryFraudModel": {"id": 123, "identifier": "identifier", "name": "name", "url": "url", "version": "123", "createdDate": "0", "lastUpdated": "0"}, "fraudModels": [{"id": 123, "identifier": "identifier", "name": "name", "url": "url", "version": "123", "createdDate": "0", "lastUpdated": "0"}], "kycPreferences": {"exactDob": true, "dobMatchLogic": "exact_yyyy", "ssnExactMatch": true}, "dvConfiguration": {"Document Expiration Grace - Past": {"configId": 2, "configValue": "0", "decision": 0}, "FTB Matching - Non Dates": {"configId": 4, "configValue": "75", "decision": 3}, "Document Expiration Grace - Future": {"configId": 3, "configValue": "0", "decision": 0}, "Minimum Age": {"configId": 1, "configValue": "18", "decision": 3}, "FTB Matching - Dates": {"configId": 5, "configValue": "50", "decision": 4}}, "consentReason": 1, "mlaField": {"memberNumber": "", "securityCode": ""}}}, "msg": "a message"}