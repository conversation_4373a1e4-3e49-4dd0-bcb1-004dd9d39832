package me.socure.storage.slick.tables.account

import org.joda.time.DateTime

/**
 * DTO for the tbl_account_metadata table.
 * This table is used by the Socure Account Intelligence team to store account ID and child ID mappings.
 */
case class DtoAccountMetadata(
  id: Long,
  accountId: Long,
  childId: String,
  createdAt: DateTime,
  updatedAt: DateTime,
  createdBy: String,
  updatedBy: String
)
