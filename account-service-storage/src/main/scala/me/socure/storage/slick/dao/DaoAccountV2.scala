package me.socure.storage.slick.dao

import com.github.tototoshi.slick.MySQLJodaSupport._
import me.socure.account.utils.UUIDUtilities
import me.socure.common.clock.Clock
import me.socure.constants._
import me.socure.model.account.ApiKeyStatus.ApiKeyStatus
import me.socure.model.account._
import me.socure.model.account.platform.resource.mapping.{AccountPlatformResourceMappingStatuses, ResourceTypes}
import me.socure.model.dashboardv2.{AccountWithRolesInput, CreateBusinessUserInput, Creator, DelegatedUserForm, UpdateBusinessUserInput}
import me.socure.model.{BusinessUserRoles, DefaultAccountsModules, UsersAndRoles}
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.account.platform.resource.mapping.DaoTblAccountPlatformResourceMapping
import me.socure.storage.slick.tables.account.sponsorbank.DaoTblSponsorBankProgram
import me.socure.storage.slick.tables.accountsocialkeys.DaoTblEnvironmentSocialKeys
import me.socure.storage.slick.tables.industry.{DaoTblIndustry, DtoIndustry}
import me.socure.storage.slick.tables.user.role.{DaoTblBusinessUserEnvironmentRole, DtoBusinessUserEnvironmentRole}
import me.socure.storage.slick.tables.user._
import me.socure.utils.DBTables.DBTable
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import org.joda.time.DateTime
import org.json4s.Formats
import org.json4s.jackson.Serialization
import org.slf4j.{Logger, LoggerFactory}
import slick.driver.JdbcProfile
import slick.jdbc.GetResult

import scala.collection.mutable
import scala.concurrent.{ExecutionContext, Future}

class DaoAccountV2(val dbProxy: DBProxyWithMetrics, val profile: JdbcProfile)(implicit val ec: ExecutionContext)
  extends DaoTblAccountHierarchy
    with DaoTblAccountAssociationHistory
    with DaoTblUserRole
    with DaoTblBusinessUserEnvironmentRole
    with DaoTblUserAccountAssociation
    with DaoTblPermissionTemplateMapping
    with DaoTblPermissionTemplate
    with DaoTblAccount
    with DaoTblEnvironment
    with DaoTblEnvironmentType
    with DaoTblEnvironmentSocialKeys
    with DaoTblIndustry
    with DaoTblAccountPermission
    with DaoTblUserAccountRoleAssociation
    with DaoTblRolePermissionTemplateAssociation
    with DaoTblBusinessUser
    with DaoTblActivationToken
    with DaoTblAccountMigrationAudit
    with DaoTblAccountAttribute
    with DaoTblApiKey
    with DaoTblDefaultModules
    with DaoTblApiAudit
    with DaoTblAccountUIConfiguration
    with DaoTblSystemRolesMaxCount
    with DaoTblSponsorBankProgram
    with DaoTblAccountMetadata
    with DaoTblAccountPlatformResourceMapping{

  import profile.api._

  protected implicit def jsonFormats: Formats = JsonFormats.formats

  val logger: Logger = LoggerFactory.getLogger(this.getClass)
  val defaultAutoTimeoutInMinutes = 30
  val defaultIdleTimeoutInMinutes = 30

  def getAccountHierarchy(id: Long): Future[Option[DtoAccountHierarchy]] = {
    val query = TblAccountHierarchy.filter(_.id === id).result.headOption
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getAccountHierarchy")
  }

  def getAccountHierarchyByAccountId(accountId: Long): Future[Option[DtoAccountHierarchy]] = {
    val query = TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getAccountHierarchyByAccountId")
  }

  def getAccountWithHierarchy(accountId: Long): Future[Option[(DtoAccount, DtoAccountHierarchy)]] = {
    val query = TblAccounts.filter(_.id === accountId).join(TblAccountHierarchy).on(_.id === _.accountId).result
    dbProxy.run(query.headOption, Set(DBTables.TblAccount, DBTables.TblAccountHierarchy), DBActions.Select, "getAccountWithHierarchy")
  }

  def getAccountInfoV2(hierarchyPath: String): Future[Seq[(DtoAccountHierarchy, DtoAccount)]] = {
    val query =
      for {
        ah <- TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"${hierarchyPath.trim}%"))
        acc <- TblAccounts.filter(a => a.isDeleted === false && a.id === ah.accountId)
        _ <- TblAccountPermission.filter(a => a.accountId === ah.accountId && a.permission === BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id && a.enabled === true)
      } yield (ah, acc)
    dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblAccountPermission), DBActions.Select, "getAccountInfoV2")
  }

  /** returns accounts with all hierarchy status */
  def getAllAccountInfoV2WithIndustry(hierarchyPath: String): Future[Seq[(DtoAccountHierarchy, DtoAccount, DtoIndustry)]] = {
    val query =
      for {
        ah <- TblAccountHierarchy.filter(_.hierarchyPath.like(s"${hierarchyPath.trim}%"))
        acc <- TblAccounts.filter(a => a.isDeleted === false  && a.id === ah.accountId)
        sector <- TblIndustries.filter(_.sector === acc.industrySector)
        _ <- TblAccountPermission.filter(a => a.accountId === ah.accountId && a.permission === BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id && a.enabled === true)
      } yield (ah, acc, sector)
    dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblIndustry), DBActions.Select, "getAllAccountInfoV2WithIndustry")
  }

  /** returns accounts with active hierarchy status */
  def getAccountInfoV2WithIndustry(hierarchyPath: String): Future[Seq[(DtoAccountHierarchy, DtoAccount, DtoIndustry)]] = {
    val query =
      for {
        ah <- TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"${hierarchyPath.trim}%"))
        acc <- TblAccounts.filter(a => a.isDeleted === false  && a.id === ah.accountId)
        sector <- TblIndustries.filter(_.sector === acc.industrySector)
        _ <- TblAccountPermission.filter(a => a.accountId === ah.accountId && a.permission === BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id && a.enabled === true)
      } yield (ah, acc, sector)
    dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblIndustry), DBActions.Select, "getAccountInfoV2WithIndustry")
  }

  def getAccountInfoV2WithIndustry(hierarchyPath: String, businessUserId: Long): Future[Seq[(DtoAccountHierarchy, DtoAccount, DtoIndustry)]] = {
    val query =
      for {
        ah <- TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"${hierarchyPath.trim}%"))
        acc <- TblAccounts.filter(a => a.isDeleted === false  && a.id === ah.accountId)
        _ <- TblUserAccountAssociation.filter(u => u.businessUserId === businessUserId && u.accountId === ah.accountId && u.status === UserAccountAssociationStatuses.ACTIVE.id && u.associationType=== UserAccountAssociationType.DIRECT.id)
        sector <- TblIndustries.filter(_.sector === acc.industrySector)
        _ <- TblAccountPermission.filter(a => a.accountId === ah.accountId && a.permission === BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id && a.enabled === true)
      } yield (ah, acc, sector)
    dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblIndustry, DBTables.TblUserAccountAssociation), DBActions.Select, "getAccountInfoV2WithIndustry")
  }

  // must not be used in any API other than Account creation
  def insertAccountHierarchy(dtoAccountHierarchy: DtoAccountHierarchy): Future[DtoAccountHierarchy] = {
    val query = (TableQuery[TblAccountHierarchy] returning TableQuery[TblAccountHierarchy].map(_.id)) += dtoAccountHierarchy
    val dbFuture = dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Insert, "getAccountInfoV2WithIndustry")
    dbFuture.map(id => dtoAccountHierarchy.copy(id = id))
  }

  def updateAccountHierarchy(dtoAccountHierarchy: DtoAccountHierarchy): Future[Int] = {
    val query = TblAccountHierarchy.filter(_.id === dtoAccountHierarchy.id).update(dtoAccountHierarchy)
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Update, "updateAccountHierarchy")
  }

  def updateIsSponsorBank(accountId: Long, isSponsorBank: Boolean): Future[Int] = {
    val query = TblAccounts.filter(_.id === accountId).map(_.isSponsorBank).update(isSponsorBank)
    dbProxy.run(query, DBTables.TblAccount, DBActions.Update, "updateIsSponsorBank")
  }

  def updateAdministerFlag(accountId: Long, canAdminister: Boolean): Future[Int] = {
    val query = TblAccountHierarchy.filter(_.accountId === accountId).map(_.administer).update(canAdminister)
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Update, "updateAdministerFlag")
  }

  def updatePrimaryAdminCount(accountId: Long, primaryAdminCount: Int): Future[Int] = {
    val query = TblAccountHierarchy.filter(_.accountId === accountId).map(_.numberOfPrimaryAdmins).update(primaryAdminCount)
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Update, "updatePrimaryAdminCount")
  }

  def updateAccountHierarchyStatus(accountId: Long, hierarchyStatus: Int, clock: Clock): Future[Int] = {
    val query0 = TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
    val dbFuture0 = dbProxy.run(query0, DBTables.TblAccountHierarchy, DBActions.Update, "updateAccountHierarchyStatus_0")
    dbFuture0.flatMap {
      case Some(ah) =>
        val query1 = TblAccountAssociationHistory.filter(_.accountHierarchyId === ah.id).result.headOption
        val dbFuture1 = dbProxy.run(query1, DBTables.TblAccountAssociationHistory, DBActions.Update, "updateAccountHierarchyStatus_1")
        dbFuture1.flatMap {
          case Some(aah) =>
            val query = for{
              _ <- TblAccountHierarchy.filter(_.id === ah.id).map(_.hierarchyStatus).update(hierarchyStatus)
              res <- {
                if(hierarchyStatus == Status.ACTIVE.id) {
                  TblAccountAssociationHistory.filter(_.id === aah.id).map(_.activatedAt).update(clock.now())
                }else TblAccountAssociationHistory.filter(_.id === aah.id).map(_.deactivatedAt).update(Some(clock.now()))
              }
            } yield res
            dbProxy.run(query.transactionally, Set(DBTables.TblAccountHierarchy, DBTables.TblAccountAssociationHistory), DBActions.Update, "updateAccountHierarchyStatus")
          case _ => Future.successful(0)
        }
      case _ => Future.successful(0)
    }
  }

  def saveAccountAssociationHistory(dtoAccountAssociationHistory: DtoAccountAssociationHistory): Future[DtoAccountAssociationHistory] = {
    val query = (TableQuery[TblAccountAssociationHistory] returning TableQuery[TblAccountAssociationHistory].map(_.id)) += dtoAccountAssociationHistory
    val dbFuture = dbProxy.run(query, DBTables.TblAccountAssociationHistory, DBActions.Insert, "saveAccountAssociationHistory")
    dbFuture.map(id => dtoAccountAssociationHistory.copy(id = id))
  }

  def activateAccountAssociationHistory(accountHierarchyId: Long): Future[Int] = {
    val query = TblAccountAssociationHistory
      .filter(c => c.accountHierarchyId === accountHierarchyId)
      .map(a => a.deactivatedAt)
      .update(None)
    dbProxy.run(query, DBTables.TblAccountAssociationHistory, DBActions.Update, "activateAccountAssociationHistory")
  }

  def deActivateAccountAssociationHistory(accountHierarchyId: Long, clock: Clock): Future[Int] = {
    val query = TblAccountAssociationHistory
      .filter(c => c.accountHierarchyId === accountHierarchyId)
      .map(a => a.deactivatedAt)
      .update(Some(clock.now()))
    dbProxy.run(query, DBTables.TblAccountAssociationHistory, DBActions.Update, "deActivateAccountAssociationHistory")
  }

  def saveUserRole(dtoUserRole: DtoUserRole): Future[Int] = {
    val query = TblUserRole += dtoUserRole
    dbProxy.run(query, DBTables.TblUserRole, DBActions.Insert, "saveUserRole")
  }

  def getUserRole(id: Long): Future[Option[DtoUserRole]] = {
    val query = TblUserRole.filter(_.id === id).result.headOption
    dbProxy.run(query, DBTables.TblUserRole, DBActions.Select, "getUserRole")
  }

  def getUserRoles(ids: Seq[Long]): Future[Seq[DtoUserRole]] = {
    val query = TblUserRole.filter(_.id inSet ids).result
    dbProxy.run(query, DBTables.TblUserRole, DBActions.Select, "getUserRoles")
  }

  def insertUserRole(userRoleInput: UserRoleInput): Future[Boolean] = {
    val dtoUserRole = DtoUserRole(id = userRoleInput.id.getOrElse(0), name = userRoleInput.name, description = userRoleInput.description, byBusinessUserId = userRoleInput.creator.userId, byAccountId = userRoleInput.accountId, updatedAt = DateTime.now)
    val creator = userRoleInput.creator
    val dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping] = userRoleInput.permissions.map(environmentPermissions => DtoPermissionTemplateMapping(id= 0L, permissionTemplateId= 0L, environmentTypeId= environmentPermissions.environmentTypeId, permissions= environmentPermissions.permissions.mkString(","), globalScope= environmentPermissions.globalScope.map(_.mkString(","))))
    val query = for {
      userRoleId <- (TableQuery[TblUserRole] returning TableQuery[TblUserRole].map(_.id)) += dtoUserRole
      uaaIdOpt <- TblUserAccountAssociation.filter(uaa => (uaa.accountId === creator.accountId) && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.businessUserId === creator.userId)).map(_.id).result.headOption
      permissionTemplateId <- (TableQuery[TblPermissionTemplate] returning TableQuery[TblPermissionTemplate].map(_.id)) += DtoPermissionTemplate(id = 0L, name= s"Template_For_Role_$userRoleId", `type`= TemplateTypes.CUSTOM.id, accountId= userRoleInput.accountId, updatedBy= uaaIdOpt.getOrElse(0), updateAt= DateTime.now)
      _ <- TblPermissionTemplateMapping ++= dtoPermissionTemplateMappingSeq.map(_.copy(permissionTemplateId = permissionTemplateId))
      _ <- TblRolePermissionTemplateAssociation += DtoRolePermissionTemplateAssociation(0, permissionTemplateId, userRoleId)
    } yield true
    dbProxy.run(query.transactionally, Set(DBTables.TblUserRole, DBTables.TblPermissionTemplate, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Insert, "insertUserRole")
  }

  def updateUserRole(userRoleInput: UserRoleInput): Future[Boolean] = {
    val dtoPermissionTemplateMappingSeq: Seq[DtoPermissionTemplateMapping] = userRoleInput.permissions.map(environmentPermissions => DtoPermissionTemplateMapping(id= 0L, permissionTemplateId= 0L, environmentTypeId= environmentPermissions.environmentTypeId, permissions= environmentPermissions.permissions.mkString(","), globalScope= environmentPermissions.globalScope.map(_.mkString(","))))
    val query = for {
      _ <- TblUserRole.filter(_.id === userRoleInput.id).map(ur => (ur.name, ur.description)).update(userRoleInput.name, userRoleInput.description)
      permissionTemplateIdOpt <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === userRoleInput.id).map(_.permissionTemplateId).result.headOption
      _ <- TblPermissionTemplateMapping.filter(_.permissionTemplateId === permissionTemplateIdOpt.getOrElse(-1L)).delete
      _ <- TblPermissionTemplateMapping ++= dtoPermissionTemplateMappingSeq.map(_.copy(permissionTemplateId = permissionTemplateIdOpt.getOrElse(-1L)))
    } yield true

    dbProxy.run(query.transactionally, Set(DBTables.TblUserRole, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Update, "updateUserRole")
  }

  def deleteUserRole(id: Long): Future[Int] = {
    val query = TblUserRole.filter(_.id === id).delete
    dbProxy.run(query, DBTables.TblUserRole, DBActions.Delete, "deleteUserRole")
  }

  def getUserRolesByAccountId(accountId: Long): Future[(Seq[((DtoUserRole, DtoRolePermissionTemplateAssociation), DtoPermissionTemplate)], Map[Long, Int])] = {
    val query = for {
      userAccountAssociatedRoleIds <- (TblUserAccountAssociation.filter(ur => ur.accountId === accountId && ur.associationType === UserAccountAssociationType.DIRECT.id ) join TblUserAccountRoleAssociation on (_.id === _.userAccountAssociationId)).filter(ux=> ux._2.userRoleId.nonEmpty).map(_._2.userRoleId.get).result
      userRoleSeq <- (TblUserRole.filter(ur => ur.byAccountId === accountId || (ur.id inSet userAccountAssociatedRoleIds)) join TblRolePermissionTemplateAssociation on(_.id === _.userRoleId) join TblPermissionTemplate on (_._2.permissionTemplateId === _.id)).result
      userAccountAssociationSeq <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId  && uaa.associationType === UserAccountAssociationType.DIRECT.id).result
      roleIdVsCount <- TblUserAccountRoleAssociation.filter(uara => (uara.roleType === SystemDefinedRoles.CUSTOMROLE.roleType) && (uara.userRoleId inSet userRoleSeq.map(_._1._1.id)) && (uara.userAccountAssociationId inSet  userAccountAssociationSeq.map(_.id))).groupBy(_.userRoleId).map ({
        case (userRoleId, results) => (userRoleId.get -> results.length)
      }).result
    } yield (userRoleSeq, roleIdVsCount.toMap)
    dbProxy.run(query, Set(DBTables.TblUserRole, DBTables.TblUserAccountAssociation, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "getUserRolesByAccountId")
  }

  def getSystemDefinedRolesDetailsByAccountId(accountId: Long): Future[Map[Int, Int]] = {
    val query = for {
      userAccountAssociiationSeq <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId  && uaa.associationType === UserAccountAssociationType.DIRECT.id).result
      roleTypeVsCount <- TblUserAccountRoleAssociation.filter(uara => (uara.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType) && (uara.userAccountAssociationId inSet  userAccountAssociiationSeq.map(_.id))).groupBy(_.roleType).map ({
        case (roleType, results) => (roleType -> results.length)
      }).result
    } yield roleTypeVsCount.toMap
    dbProxy.run(query, Set(DBTables.TblAccount, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getSystemDefinedRolesDetailsByAccountId")
  }


  def isValidUserRole(accountId: Long, roleType: Int, roleId: Long): Future[Boolean] = {
    val query = for {
      accountWithSubAccountIds <- TblAccountHierarchy.filter(a => {
        a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"$accountId/%")
      }).map(_.accountId).result
      isValid <- TblUserRole.filter(ur => (ur.byAccountId inSet accountWithSubAccountIds) && ur.id === roleId).exists.result
    } yield isValid
    dbProxy.run(query, Set(DBTables.TblAccountHierarchy, DBTables.TblUserRole), DBActions.Select, "isValidUserRole")
  }

  def isAccountAssociatedToRoleId(accountId: Long, roleType: Int, roleId: Long): Future[Boolean] = {
    val query = for {
      userRole <- TblUserRole.filter(ur => ur.id === roleId && ur.byAccountId === accountId ).exists.result
      userAccountAssociatedRole <- (TblUserAccountAssociation.filter(ur => ur.accountId === accountId && ur.associationType === UserAccountAssociationType.DIRECT.id ) join TblUserAccountRoleAssociation on (_.id === _.userAccountAssociationId)).filter(uara => uara._2.roleType === roleType && (uara._2.userRoleId.nonEmpty && uara._2.userRoleId === roleId)).exists.result
    } yield userAccountAssociatedRole || userRole
    dbProxy.run(query, Set(DBTables.TblUserRole, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "isAccountAssociatedToRoleId")
  }

  //Not in use
  def getUserIdVsRoles(accountId: Long, userIds: Vector[Long]): Future[Map[Long, Seq[Long]]] = {
    val query = for {
      res <- TblUserAccountAssociation.filter(uaa => (uaa.accountId === accountId) && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.businessUserId inSet userIds)) join TblUserAccountRoleAssociation.filter(_.roleType === SystemDefinedRoles.CUSTOMROLE.roleType) on(_.id === _.userAccountAssociationId) map(res => (res._1.businessUserId, res._2.userRoleId))
    } yield res
    val dbFuture = dbProxy.run(query.result, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserIdVsRoles")
    dbFuture.map(res => res.groupBy(_._1).map(res => res._1 -> res._2.map(_._2.get)))
  }

  def getRoleIdVsRoleName(ids: Set[Long]): Future[Map[Long, String]] = {
    val query = TblUserRole.filter(_.id inSet ids).map(ur => (ur.id, ur.name))
    val dbFuture = dbProxy.run(query.result, DBTables.TblUserRole, DBActions.Select, "getRoleIdVsRoleName")
    dbFuture.map(res => res.toMap)
  }

  def getRoleIdVsRoleNameAndByAccountId(ids: Set[Long]): Future[Map[Long, (String, Long)]] = {
    val query = TblUserRole.filter(_.id inSet ids).map(ur => (ur.id, (ur.name, ur.byAccountId)))
    val dbFuture = dbProxy.run(query.result, DBTables.TblUserRole, DBActions.Select, "getRoleIdVsRoleName")
    dbFuture.map(res => res.toMap)
  }

  def getUserAccountAssociationWithAccountId(accountId: Long): Future[Option[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id)).result.headOption
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociationWithAccountId")
  }

  def getUserAccountAssociation(userId: Long, accountId: Long): Future[Option[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.accountId === accountId && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id)).result.headOption
    dbProxy.runWithWriter(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociation")
  }

  def getUserAccountAssociation(userId: Long, accountIds: Seq[Long]): Future[Option[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId.inSet(accountIds) && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id)).result.headOption
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociation")
  }

  def getActiveUserAccountAssociation(userId: Long, accountId: Long): Future[Option[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountId && uaa.status === UserAccountAssociationStatuses.ACTIVE.id).result.headOption
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getActiveUserAccountAssociation")
  }

  def getAllUserAccountAssociation(userId: Long, accountId: Long): Future[Option[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountId).result.headOption
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getAllUserAccountAssociation")
  }

  //SA Associate User with Role

  def getUserAccountRoleAssociation(userId: Long, accountId: Long, roleType: Int, roleId: Option[Long]): Future[Seq[DtoUserAccountRoleAssociation]] = {
    val query = for {
      userAccountAssociation <- TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId  && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountId && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id))
      userAccountRoleAssociation <- TblUserAccountRoleAssociation.filter(uara => uara.userAccountAssociationId === userAccountAssociation.id && uara.roleType === roleType && ((uara.userRoleId.isEmpty && roleId.isEmpty) || uara.userRoleId === roleId))
    } yield(userAccountRoleAssociation)
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getUserAccountRoleAssociation")
  }

  def getUserAccountAssociationById(userAccountAssociationId: Long): Future[DtoUserAccountAssociation] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.id === userAccountAssociationId && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id)).result.head
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociationById")
  }

  def getUserAccountAssociations(accountId: Long): Future[Seq[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id)).result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociations")
  }

  def getAssociationsByUserId(userId: Long): Future[Seq[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id)).result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getAssociationsByUserId")
  }

  def getAssociationsByUserIdFilteredByAccountIds(userId: Long, accountIds: Set[Long]): Future[Seq[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.accountId.inSet(accountIds)).result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getAssociationsByUserId")
  }

  def getAllUserAccountAssociationsByUserId(userId: Long): Future[Seq[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id).result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getAssociationsByUserId")
  }

  def getPrimaryAdminsCount(accountId: Long): Future[Int] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.isPrimaryUser).length.result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "getPrimaryAdminsCount")
  }

  def canAddPrimaryAdmin(accountId: Long): Future[Boolean] = {
    val query = for {
      ah <- TblAccountHierarchy.filter(_.accountId === accountId).map(_.numberOfPrimaryAdmins).result.headOption
      uaa <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.isPrimaryUser).length.result
    } yield (ah, uaa)
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccountHierarchy), DBActions.Select, "canAddPrimaryAdmin")
      .map { r =>
        r._1.exists(pa => pa > r._2)
      }
  }

  //Used in Test classes
  def getUserAccountAssociationWithRoles(userId: Long, accountId: Long): Future[Map[DtoUserAccountAssociation, Seq[DtoUserAccountRoleAssociation]]] = {
    val query = for {
      uaa <- TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountId)
      uaur <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === uaa.id && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType)
    } yield (uaa, uaur)
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getUserAccountAssociationWithRoles")
      .map(_.groupBy(_._1).mapValues(_.map(_._2)))
  }

  def getUserAccountAssociationsByUserId(userId: Long): Future[Seq[DtoUserAccountAssociation]] = {
    val query = TblUserAccountAssociation
      .filter(uaa =>
        uaa.businessUserId === userId &&
          uaa.associationType === UserAccountAssociationType.DIRECT.id &&
          uaa.status === UserAccountAssociationStatuses.ACTIVE.id
      )
    dbProxy.run(query.result, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociationsByUserId")
  }

  def getAssociatedV2Accounts(userId: Long): Future[Seq[(DtoAccountHierarchy, Int)]] = {
    val query = for {
      accountIds <- TblUserAccountAssociation
        .filter(uaa =>
          uaa.businessUserId === userId &&
            uaa.associationType === UserAccountAssociationType.DIRECT.id &&
            uaa.status === UserAccountAssociationStatuses.ACTIVE.id
        )
        .map(_.accountId)
        .result
      accountsWithParentType <- TblAccountHierarchy
        .filter(account =>
          (account.accountId inSet accountIds.toSet) &&
            account.hierarchyStatus === Status.ACTIVE.id
        )
        .joinLeft(TblAccountHierarchy)
        .on { case (child, parent) =>
          child.hierarchyPath
            .like(parent.accountId.asColumnOf[String] ++ "/%") &&
            parent.hierarchyStatus === Status.ACTIVE.id
        }
        .map { case (child, parent) =>
          (child, parent.map(_.accountType).getOrElse(child.accountType))
        }
        .result
    } yield accountsWithParentType

    dbProxy.run(
      query,
      Set(DBTables.TblUserAccountAssociation, DBTables.TblAccountHierarchy),
      DBActions.Select,
      "getAssociatedV2Accounts"
    )
  }

  def checkPermissionsForAccounts(accountIds: Set[Long], permissions: Set[Int]): Future[Seq[DtoAccountPermission]] = {
    val query = TblAccountPermission
      .filter(ap => (ap.accountId inSetBind accountIds) && ap.enabled === true && (ap.permission inSetBind permissions))

    dbProxy.run(
      query.result,
      Set(DBTables.TblAccountPermission),
      DBActions.Select,
      "checkPermissionsForAccounts"
    )
  }


  def getAssociatedAccounts(userId: Long): Future[Seq[AccountIdName]] = {
    val query = for {
      accountIds <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && uaa.associationType === UserAccountAssociationType.DIRECT.id  && uaa.status === UserAccountAssociationStatuses.ACTIVE.id).map(_.accountId).result
      accounts <- TblAccounts.filter(account => (account.id inSet accountIds) && account.isActive).result
    } yield accounts
    dbProxy.runWithWriter(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount), DBActions.Select, "getAssociatedAccounts")
      .map { accounts =>
        accounts map { account =>
          AccountIdName (
            account.accountId,
            account.name
          )
        }
      }
  }

  def getAssociatedAccountsAndWorkflowIds(accountIds: Set[Long]): Future[Map[Int, Map[Long, Seq[String]]]] = {
    val query = TblAccountPlatformResourceMapping.filter(apm => apm.accountId.inSet(accountIds) && apm.resourceType === ResourceTypes.WorkFlow && apm.status === AccountPlatformResourceMappingStatuses.Active).map(apm => (apm.envTypeId, apm.accountId, apm.resourceId)).result
    dbProxy.runWithWriter(query, DBTables.TblAccountPlatformResourceMapping, DBActions.Select, "getAssociatedAccountsAndWorkflowIds")
      .map { envAccWorkflows =>
        envAccWorkflows
          .groupBy(_._1)  // group by envTypeId
          .map { case (envTypeId, envAccWfs) =>
            envTypeId -> envAccWfs
              .groupBy(_._2)  // group by accountId
              .map { case (accountId, accountWfs) =>
                accountId -> accountWfs.map(_._3)
              }
          }
      }
  }

  def getAllAssociatedAccounts(userId: Long): Future[Seq[AccountIdName]] = {
    val query = for {
      accountIds <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && uaa.associationType === UserAccountAssociationType.DIRECT.id  && uaa.status === UserAccountAssociationStatuses.ACTIVE.id).map(_.accountId).result
      accounts <- TblAccounts.filter(account => (account.id inSet accountIds)).result
    } yield accounts
    dbProxy.runWithWriter(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount), DBActions.Select, "getAllAssociatedAccounts")
      .map { accounts =>
        accounts map { account =>
          AccountIdName (
            account.accountId,
            account.name
          )
        }
      }
  }

  def getAssociatedPrograms(userId: Long): Future[Seq[AccountIdName]] = {
    val query = for {
      accountIds <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && uaa.associationType === UserAccountAssociationType.PROGRAM.id && uaa.status === UserAccountAssociationStatuses.ACTIVE.id).map(_.accountId).result
      accounts <- TblAccounts.filter(account => (account.id inSet accountIds) && account.isActive).result
    } yield accounts
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount), DBActions.Select, "getAssociatedAccounts")
      .map { accounts =>
        accounts map { account =>
          AccountIdName(
            account.accountId,
            account.name
          )
        }
      }
  }

  def getAssociatedAccounts(creatorUserId: Long, accountIds: Seq[Long], userId: Option[Long]): Future[Seq[AccountIdName]] = {
    val query = for {
      accountIds <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId || uaa.businessUserId === creatorUserId) && (uaa.accountId inSet accountIds) && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(_.accountId).result
      accounts <- TblAccounts.filter(account => (account.id inSet accountIds) && account.isActive).map(res => (res.id, res.name)) result
    } yield accounts
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount), DBActions.Select, "getAssociatedAccounts")
      .map { accounts =>
        accounts map { account =>
          AccountIdName (
            account._1,
            account._2
          )
        }
      }
  }

  def hasAccountOwnerInParentAccountHierarchy(userId: Long, accountId: Long, hierarchyPath: String): Future[Seq[DtoUserAccountAssociation]] = {
    val query = for {
      accountOwnerAccounts <- (TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && (uaa.associationType === UserAccountAssociationType.DIRECT.id) && (uaa.accountId inSet hierarchyPath.split("/").map(_.trim.toLong).filter(_ != accountId).toSet) && uaa.status === UserAccountAssociationStatuses.ACTIVE.id)
                                .join(TblUserAccountRoleAssociation.filter(_.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType)) on (_.id === _.userAccountAssociationId)) map(res => res._1) result
    } yield accountOwnerAccounts
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccountHierarchy, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "hasAccountOwnerInParentAccountHierarchy")
  }

  def isAccountOwnerInParentAccountHierarchy(userId: Long, accountId: Long, hierarchyPath: String): Future[Boolean] = {
    val query = for {
      accountOwnerAccounts <- (TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && (uaa.accountId inSet hierarchyPath.split("/").map(_.trim.toLong).filter(_ != accountId).toSet) && uaa.status === UserAccountAssociationStatuses.ACTIVE.id)
        .join(TblUserAccountRoleAssociation.filter(_.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType)) on (_.id === _.userAccountAssociationId)) map(res => res._1) result
    } yield accountOwnerAccounts
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccountHierarchy, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "isAccountOwnerInParentAccountHierarchy")
      .map { acc =>
        if(acc.length > 0)
          true
        else
          false
      }
  }

  def getUserAccountOwnerAssociations(userId: Long):Future[Set[Long]] = {
    val query = for {
      accountIds <- (TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && (uaa.associationType === UserAccountAssociationType.DIRECT.id) && uaa.status === UserAccountAssociationStatuses.ACTIVE.id).join(TblUserAccountRoleAssociation.filter(_.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType)) on (_.id === _.userAccountAssociationId)).map(_._1.accountId).result
      accounts <- TblAccounts.filter(account => (account.id inSet accountIds) && account.isActive).map(_.id).result
    } yield accounts
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation, DBTables.TblAccount), DBActions.Select, "getUserAccountOwnerAssociations").map(
      _.toSet
    )
  }

  def getAccountOwnerAssociatedAccounts(accountIds: Set[Long], ignoreAccountIds: Seq[AccountIdName], hierarchyPaths: Set[String]): Future[Seq[AccountIdName]] = {
      if(accountIds.size > 0) {
        val query = for {
          subAccountIds <- TblAccountHierarchy.filter(a => (a.hierarchyPath regexLike hierarchyPaths.map(h => s"^${h.trim}.*").mkString("|"))
            && (a.hierarchyStatus === Status.ACTIVE.id) && !(a.accountId inSet accountIds)).map(_.accountId).result
          accounts <- TblAccounts.filter(account => (account.id inSet (subAccountIds.toSet diff ignoreAccountIds.map(_.id).toSet)) && account.isActive).result
        } yield accounts
        dbProxy.run(query, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount), DBActions.Select, "getAccountOwnerAssociatedAccounts")
          .map { accounts =>
            accounts map { account =>
              AccountIdName(
                account.accountId,
                account.name
              )
            }
          }
      } else {
        Future.successful(Seq.empty)
      }
  }

  implicit class RegexLikeOps(s: Rep[String]) {
    def regexLike(p: Rep[String]): Rep[Boolean] = {
      val expr = SimpleExpression.binary[String,String,Boolean] { (s, p, qb) =>
        qb.expr(s)
        qb.sqlBuilder += " REGEXP "
        qb.expr(p)
      }
      expr.apply(s,p)
    }
  }

  def getUserAccountAssociationsByEmail(email: String): Future[Seq[DtoUserAccountAssociation]] = {
    val query = for {
      (a, b) <- TblUserAccountAssociation.filter(_.associationType === UserAccountAssociationType.DIRECT.id).join(TblBusinessUser) on(_.businessUserId === _.id)
      if a.status === UserAccountAssociationStatuses.ACTIVE.id && b.email === email
    } yield a
    dbProxy.run(query.result, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociationsByEmail")
  }

  def accountHasNonFunctionEmailsV2(accountId: Long): Future[Seq[DtoBusinessUser]] = {
    val query = for {
      (_, bu) <- TblUserAccountAssociation.filter(uaa => uaa.accountId===accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).join(TblBusinessUser) on(_.accountId === _.accountId)
      if bu.email.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail)
    } yield bu
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation,DBTables.TblBusinessUser), DBActions.Select, "accountHasNonFunctionEmailsV2")
  }

  def accountHasNonFunctionEmailsV1(accountId: Long): Future[Seq[DtoBusinessUser]] = {
    val query = for {
      (_, bu) <- TblAccounts.filter(_.id === accountId).join(TblBusinessUser) on(_.id === _.accountId)
      if bu.email.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail)
    } yield bu
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation,DBTables.TblBusinessUser), DBActions.Select, "accountHasNonFunctionEmailsV1")
  }

  def getAllUserAccountAssociationsByEmail(email: String): Future[Seq[DtoUserAccountAssociation]] = {
    val query = for {
      (a, b) <- TblUserAccountAssociation.filter(_.associationType === UserAccountAssociationType.DIRECT.id).join(TblBusinessUser) on(_.businessUserId === _.id)
      if b.email === email
    } yield a
    dbProxy.run(query.result, DBTables.TblUserAccountAssociation, DBActions.Select, "getUserAccountAssociationsByEmail")
  }

  def insertUserAccountAssociation(dtoUserAccountAssociation: DtoUserAccountAssociation): Future[DtoUserAccountAssociation] = {
    val query = (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += dtoUserAccountAssociation
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Insert, "insertUserAccountAssociation")
      .map(id => dtoUserAccountAssociation.copy(id = id))
  }

  def insertUserAccountAssociationWithRoles(dtoUserAccountAssociation: DtoUserAccountAssociation, roles: Set[Long]): Future[Long] = {
    val query = for {
      uaa <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += dtoUserAccountAssociation
      _ <- TblUserAccountRoleAssociation ++= roles.map(role => DtoUserAccountRoleAssociation(0, uaa, Some(role)))
    } yield uaa
    dbProxy.run(query.transactionally, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Insert, "insertUserAccountAssociationWithRoles")
  }

  //SA
  def insertUserAccountAssociationWithRole(dtoUserAccountAssociation: DtoUserAccountAssociation, roleType: Int, role: Option[Long]): Future[Long] = {
    val query = for {
      uaa <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += dtoUserAccountAssociation
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, uaa, role, roleType)
    } yield uaa
    dbProxy.run(query.transactionally, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Insert, "insertUserAccountAssociationWithRole")
  }

  //SA
  def updateUserAccountAssociationWithRole(dtoUserAccountAssociation: DtoUserAccountAssociation, roleType: Int, role: Option[Long]): Future[Boolean] = {
    val query = for {
      _ <- TblUserAccountAssociation.filter(uaa => uaa.id === dtoUserAccountAssociation.id && uaa.associationType === UserAccountAssociationType.DIRECT.id).update(dtoUserAccountAssociation)
      _ <- TblUserAccountRoleAssociation.insertOrUpdate(DtoUserAccountRoleAssociation(0, dtoUserAccountAssociation.id, role, roleType))
    } yield true
    dbProxy.run(query.transactionally, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Update, "updateUserAccountAssociationWithRole")
  }

  def updateUserAccountAssociation(dtoUserAccountAssociation: DtoUserAccountAssociation): Future[Int] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.id === dtoUserAccountAssociation.id && uaa.id === dtoUserAccountAssociation.id && uaa.associationType === UserAccountAssociationType.DIRECT.id).update(dtoUserAccountAssociation)
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "updateUserAccountAssociation")
  }

  def deleteUserAccountAssociation(userId: Long, accountId: Long): Future[Int] = {
    val query = TblUserAccountAssociation
      .filter(uaa => uaa.businessUserId === userId
        && uaa.associationType === UserAccountAssociationType.DIRECT.id
        && uaa.accountId === accountId
        && uaa.status === UserAccountAssociationStatuses.ACTIVE.id
        && uaa.isPrimaryUser === false)
      .map(_.status)
      .update(UserAccountAssociationStatuses.DELETED.id)
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "deleteUserAccountAssociation")
  }

  def deleteUserAccountAssociation(userId: Long, accountId: Long, updatedBy: Long): Future[Int] = {
    isAccountOwner(userId, accountId).flatMap {
      case false =>
        val query = TblUserAccountAssociation
          .filter(uaa => uaa.businessUserId === userId
            && uaa.associationType === UserAccountAssociationType.DIRECT.id
            && uaa.accountId === accountId
            && (uaa.status === UserAccountAssociationStatuses.ACTIVE.id || uaa.status === UserAccountAssociationStatuses.LOCKED.id))
          .map(f => (f.status, f.updatedBy))
          .update(UserAccountAssociationStatuses.DELETED.id, Some(updatedBy))
        dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "deleteUserAccountAssociation_updatedBy")
      case true => logger.info(s"Account Owner user cannot be deleted. User Id: ${userId}, Account Id: ${accountId}")
        Future.successful(0)
    }
  }

  def deleteUserAccountRoleAssociation(userAccountAssociationId: Long): Future[Int] = {
    isAccountOwner(userAccountAssociationId).flatMap {
      case false =>
        val query = TblUserAccountRoleAssociation
          .filter(_.userAccountAssociationId === userAccountAssociationId).delete
        dbProxy.run(query, DBTables.TblUserAccountRoleAssociation, DBActions.Delete, "deleteUserAccountRoleAssociation")
      case true => Future.successful(0)
    }
  }

  def lockUserAccountAssociation(userId: Long, accountId: Long, updatedBy: Long): Future[Int] = {
    val query = TblUserAccountAssociation
      .filter(uaa => uaa.businessUserId === userId
        && uaa.associationType === UserAccountAssociationType.DIRECT.id
        && uaa.accountId === accountId
        && uaa.status === UserAccountAssociationStatuses.ACTIVE.id)
      .map(f => (f.status, f.updatedBy))
      .update(UserAccountAssociationStatuses.LOCKED.id, Some(updatedBy))
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "lockUserAccountAssociation")
  }

  def activateUserAccountAssociation(userId: Long, accountId: Long, updatedBy: Long): Future[Int] = {
    val query = TblUserAccountAssociation
      .filter(uaa => uaa.businessUserId === userId
        && uaa.associationType === UserAccountAssociationType.DIRECT.id
        && uaa.accountId === accountId
        && uaa.status === UserAccountAssociationStatuses.LOCKED.id)
      .map(f => (f.status, f.updatedBy))
      .update(UserAccountAssociationStatuses.ACTIVE.id, Some(updatedBy))
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "activateUserAccountAssociation")
  }

  def deactivateUserAccountAssociation(userId: Long, accountId: Long): Future[Int] = {
    val query = TblUserAccountAssociation
      .filter(uaa => uaa.businessUserId === userId && uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.status === UserAccountAssociationStatuses.ACTIVE.id)
      .map(_.status)
      .update(UserAccountAssociationStatuses.INACTIVE.id)
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Update, "deactivateUserAccountAssociation")
  }

  def getPermissionTemplateMappingsByTemplateId(permissionTemplateId: Long): Future[Seq[DtoPermissionTemplateMapping]] = {
    val query = TblPermissionTemplateMapping.filter(_.permissionTemplateId === permissionTemplateId)
    dbProxy.run(query.result, DBTables.TblPermissionTemplateMapping, DBActions.Select, "getPermissionTemplateMappingsByTemplateId")
  }

  def insertPermissionTemplateMapping(dtoPermissionTemplateMapping: DtoPermissionTemplateMapping): Future[Int] = {
    val query = TblPermissionTemplateMapping += dtoPermissionTemplateMapping
    dbProxy.run(query, DBTables.TblPermissionTemplateMapping, DBActions.Insert, "insertPermissionTemplateMapping")
  }

  def updatePermissionTemplateMapping(dtoPermissionTemplateMapping: DtoPermissionTemplateMapping): Future[Int] = {
    val query = TblPermissionTemplateMapping.filter(_.id === dtoPermissionTemplateMapping.id).update(dtoPermissionTemplateMapping)
    dbProxy.run(query, DBTables.TblPermissionTemplateMapping, DBActions.Update, "updatePermissionTemplateMapping")
  }

  def deletePermissionTemplateMapping(permissionTemplateId: Long, environmentTypeId: Int): Future[Int] = {
    val query = TblPermissionTemplateMapping.filter(ptm => ptm.permissionTemplateId === permissionTemplateId && ptm.environmentTypeId === environmentTypeId).delete
    dbProxy.run(query, DBTables.TblPermissionTemplateMapping, DBActions.Delete, "deletePermissionTemplateMapping")
  }

  def insertPermissionTemplate(dtoPermissionTemplate: DtoPermissionTemplate): Future[Int] = {
    val query = TblPermissionTemplate += dtoPermissionTemplate
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Insert, "insertPermissionTemplate")
  }

  def getPermissionTemplate(id: Long): Future[Option[DtoPermissionTemplate]] = {
    val query = TblPermissionTemplate.filter(_.id === id).result.headOption
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Select, "getPermissionTemplate")
  }

  def updatePermissionTemplate(dtoPermissionTemplate: DtoPermissionTemplate): Future[Int] = {
    val query = TblPermissionTemplate.filter(_.id === dtoPermissionTemplate.id).update(dtoPermissionTemplate)
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Update, "updatePermissionTemplate")
  }

  def deletePermissionTemplate(id: Long): Future[Int] = {
    val query = TblPermissionTemplate.filter(_.id === id).delete
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Delete, "deletePermissionTemplate")
  }

  def getPermissionTemplates(userAccountAssociationId: Long): Future[Seq[DtoPermissionTemplate]] = {
    val query = TblPermissionTemplate.filter(_.updatedBy === userAccountAssociationId).result
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Delete, "getPermissionTemplates")
  }

  def insertOrUpdateUserAccountRoleAssociation(userAccountAssociationId: Long, userRoles: Set[Long]): Future[Option[Int]] = {
    val deleteQuery = TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === userAccountAssociationId).delete
    val insertQuery = TblUserAccountRoleAssociation.forceInsertAll(userRoles.map(role => DtoUserAccountRoleAssociation(0, userAccountAssociationId, Some(role))))
    dbProxy.run((deleteQuery andThen insertQuery).transactionally, DBTables.TblUserAccountRoleAssociation, DBActions.Upsert, "insertOrUpdateUserAccountRoleAssociation")
  }

  def fetchUserAccountRoles(userId: Long, accountId: Long): Future[Seq[Long]] = {
    val query = for {
      uaa <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === userId) && uaa.associationType === UserAccountAssociationType.DIRECT.id && (uaa.accountId === accountId))
      uar <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === uaa.id && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType)
    } yield uar
    dbProxy.run(query.result, Set(DBTables.TblUserAccountRoleAssociation, DBTables.TblUserAccountAssociation), DBActions.Select, "fetchUserAccountRoles")
      .map (_.map(_.userRoleId.get))
  }

  def fetchUserAccountRoles(userAccountAssociationIds: Seq[Long]): Future[Map[Long, Set[Long]]] = {
    val query = for {
      userAccountRoleAssociation <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(userAccountAssociationIds) && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType)
    } yield userAccountRoleAssociation
    dbProxy.run(query.result, DBTables.TblUserAccountRoleAssociation, DBActions.Select, "fetchUserAccountRoles-useraccountassociationId")
      .map { res =>
        val resMap: mutable.Map[Long, Set[Long]] = mutable.Map()
        res.foreach(dtoUserAccountRoleAssociation => {
          if (resMap.contains(dtoUserAccountRoleAssociation.userAccountAssociationId)) {
            var roles = resMap.getOrElse(dtoUserAccountRoleAssociation.userAccountAssociationId, Set.empty)
            roles += dtoUserAccountRoleAssociation.userRoleId.get
            resMap.put(dtoUserAccountRoleAssociation.userAccountAssociationId, roles)
          } else {
            resMap.put(dtoUserAccountRoleAssociation.userAccountAssociationId, Set(dtoUserAccountRoleAssociation.userRoleId.get))
          }
        })
        resMap.toMap
      }
  }

  def getRolePermissionTemplateAssociationByRoleId(userRoleId: Long): Future[Option[DtoRolePermissionTemplateAssociation]] = {
    val query = TblRolePermissionTemplateAssociation.filter(uaa => uaa.userRoleId === userRoleId).result.headOption
    dbProxy.run(query, DBTables.TblRolePermissionTemplateAssociation, DBActions.Select, "getRolePermissionTemplateAssociationByRoleId")
  }

  def getRolePermissionTemplateAssociationByTemplateId(permissionTemplateId: Long): Future[Seq[DtoRolePermissionTemplateAssociation]] = {
    val query = TblRolePermissionTemplateAssociation.filter(uaa => uaa.permissionTemplateId === permissionTemplateId).result
    dbProxy.run(query, DBTables.TblRolePermissionTemplateAssociation, DBActions.Select, "getRolePermissionTemplateAssociationByTemplateId")
  }

  def insertOrUpdateRolePermissionTemplateAssociation(dtoRolePermissionTemplateAssociation: DtoRolePermissionTemplateAssociation): Future[Int] = {
    val query = TblRolePermissionTemplateAssociation.insertOrUpdate(dtoRolePermissionTemplateAssociation)
    dbProxy.run(query, DBTables.TblRolePermissionTemplateAssociation, DBActions.Upsert, "insertOrUpdateRolePermissionTemplateAssociation")
  }

  def deleteRolePermissionTemplateAssociation(userRoleId: Long): Future[Int] = {
    val query = TblRolePermissionTemplateAssociation.filter(_.userRoleId === userRoleId).delete
    dbProxy.run(query, DBTables.TblRolePermissionTemplateAssociation, DBActions.Delete, "deleteRolePermissionTemplateAssociation")
  }

  def getAccountAssociationHistory(accountHierarchyId: Long): Future[Option[DtoAccountAssociationHistory]] = {
    val query = TblAccountAssociationHistory.filter(_.accountHierarchyId === accountHierarchyId).result.map(_.headOption)
    dbProxy.run(query, DBTables.TblAccountAssociationHistory, DBActions.Select, "getAccountAssociationHistory")
  }

  def getAssociationHistoryForAccount(accountId: Long): Future[Seq[DtoAccountAssociationHistory]] = {
    val query = for{
      ah <- TblAccountHierarchy.filter(_.accountId === accountId).map(_.id).result
      aah <- TblAccountAssociationHistory.filter(_.accountHierarchyId.inSet(ah.toSet)).result
    } yield aah
    dbProxy.run(query, Set(DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy), DBActions.Select, "getAssociationHistoryForAccount")
  }

  def addAccountAdditionalDetails(dtoAccount: DtoAccount,
                                  businessUserId: Long,
                                  dtoAccountHierarchy: DtoAccountHierarchy,
                                  defaultUserPermissions: Set[(Int, String)],
                                  isDashboardV3: Boolean,
                                  roleType: Int,
                                  clock: Clock): Future[Long] = {
    val dtoUserAccountAssociation = DtoUserAccountAssociation(0, businessUserId, dtoAccount.accountId, Status.ACTIVE.id, isPrimaryUser = true, None, None, clock.now)
    val accountPermissions = if(isDashboardV3)  Seq(DtoAccountPermission(dtoAccount.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id), DtoAccountPermission(dtoAccount.accountId, BusinessUserRoles.DashboardV3.id), DtoAccountPermission(dtoAccount.accountId, BusinessUserRoles.Dashboard.id), DtoAccountPermission(dtoAccount.accountId, BusinessUserRoles.WHITELIST_DASHBOARD.id)) else Seq(DtoAccountPermission(dtoAccount.accountId, BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id))
    val query = for {
      _ <- TblAccountPermission ++= accountPermissions
      accountHierarchyId <- (TblAccountHierarchy returning TblAccountHierarchy.map(_.id)) += dtoAccountHierarchy
      _ <- TblAccountAssociationHistory += DtoAccountAssociationHistory(0, accountHierarchyId, clock.now, None)
      userAccountAssociationId <- (TblUserAccountAssociation returning TblUserAccountAssociation.map(_.id)) += dtoUserAccountAssociation
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, userAccountAssociationId, None, roleType)
    } yield accountHierarchyId
    dbProxy.run(query.transactionally, Set(DBTables.TblAccountPermission, DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblUserAccountAssociation,
      DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblRolePermissionTemplateAssociation,
      DBTables.TblPermissionTemplateMapping), DBActions.Insert, "addAccountAdditionalDetails")
  }

  def addAccountPermissions(accountId: Long, accountPermissions: Set[Int]) = {
    val dtoAccountPermissions = accountPermissions.map(DtoAccountPermission(accountId, _))
    val query = TblAccountPermission ++= dtoAccountPermissions
    dbProxy.run(query, DBTables.TblAccountPermission, DBActions.Insert, "addAccountPermissions")
  }

  def addSubAccountAdditionalDetails(accountId: Long,
                                     accountPermissions: Set[Int],
                                     dtoAccountHierarchy: DtoAccountHierarchy,
                                     primaryUserId: Long,
                                     userPermissions: Map[Int, Set[Int]],
                                     creator: Creator,
                                     clock: Clock): Future[Long] = {
    val dtoAccountPermissions = accountPermissions.map(DtoAccountPermission(accountId, _))
    val query = for {
      accountHierarchyId <- (TblAccountHierarchy returning TblAccountHierarchy.map(_.id)) += dtoAccountHierarchy
      _ <- TblAccountAssociationHistory += DtoAccountAssociationHistory(0, accountHierarchyId, clock.now, None)
      _ <- TblAccountPermission ++= dtoAccountPermissions
      userAccountAssociationId <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += DtoUserAccountAssociation(id = 0, primaryUserId, accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = true , None, None, DateTime.now)
      primaryAdminAssociation <- TblUserAccountAssociation.filter(uaa => uaa.accountId === creator.accountId && uaa.businessUserId === primaryUserId && uaa.associationType === UserAccountAssociationType.DIRECT.id).result.head
      userRoleId <- TblUserAccountRoleAssociation.filter(uara => uara.userAccountAssociationId === primaryAdminAssociation.id).map(_.userRoleId).result.head
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, userAccountAssociationId, userRoleId)
    } yield accountHierarchyId

    dbProxy.run(query.transactionally, Set(DBTables.TblAccountPermission, DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblUserAccountAssociation,
      DBTables.TblUserAccountRoleAssociation), DBActions.Insert, "addSubAccountAdditionalDetails")
  }

  def addSubAccountAdditionalDetailsWithNewRole(accountId: Long,
                                                accountPermissions: Set[Int],
                                                dtoAccountHierarchy: DtoAccountHierarchy,
                                                primaryUserId: Long,
                                                userPermissions: Map[Int, Set[Int]],
                                                creator: Creator,
                                                clock: Clock): Future[Long] = {
    val dtoAccountPermissions = accountPermissions.map(DtoAccountPermission(accountId, _))
    val creatorUserId = if(creator.userId == 0L) primaryUserId else creator.userId
    val query = for {
      accountHierarchyId <- (TblAccountHierarchy returning TblAccountHierarchy.map(_.id)) += dtoAccountHierarchy
      _ <- TblAccountAssociationHistory += DtoAccountAssociationHistory(0, accountHierarchyId, clock.now, None)
      _ <- TblAccountPermission ++= dtoAccountPermissions
      updatedBy <- TblUserAccountAssociation.filter(uaa => uaa.businessUserId === creatorUserId && uaa.accountId === creator.accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(_.id).result.headOption
      userAccountAssociationId <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += DtoUserAccountAssociation(id = 0, primaryUserId, accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = true , None, None, DateTime.now)
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, userAccountAssociationId, None, SystemDefinedRoles.ACCOUNTOWNER.roleType)
    } yield accountHierarchyId
    dbProxy.run(query.transactionally, Set(DBTables.TblAccountPermission, DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblUserAccountAssociation,
      DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblRolePermissionTemplateAssociation,
      DBTables.TblPermissionTemplateMapping), DBActions.Insert, "addSubAccountAdditionalDetailsWithNewRole")
  }

  def createSubAccountUsersOnCreation(accountId: Long,
                                      userList: Vector[UserWithAccountRoles]
                                     ) = {

    val query = for {
      _ <- TblUserAccountAssociation.forceInsertAll((userList).map(user => DtoUserAccountAssociation(id = 0, user.userId, accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = false , None, None, DateTime.now)))
      _ <- DBIO.seq(
        userList.flatMap(users => {
          if(users.roles.isEmpty){
            Seq(DBIO.successful(0))
          } else {
            users.roles.distinct.map(roles => {
              if(roles.roleType == SystemDefinedRoles.CUSTOMROLE.roleType) {
                profile.api.queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.userRoleId, uara.roleType)))
                  .forceInsertQuery(TblUserAccountAssociation.filter(uaa => uaa.businessUserId === users.userId && uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(res => (res.id, roles.roleId, roles.roleType)))
              } else {
                profile.api.queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.roleType)))
                  .forceInsertQuery(TblUserAccountAssociation.filter(uaa => uaa.businessUserId === users.userId && uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(res => (res.id, roles.roleType)))
              }
            })
          }
        }) : _*
      )

    } yield true
    dbProxy.run(query.transactionally, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Insert, "createSubAccountUsersOnCreation")
  }

  //Not in use
  def checkPermissions(accountId: Long, businessUserId: Long, permissions: Set[Int], envType: Int): Future[Boolean] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId && a.associationType === UserAccountAssociationType.DIRECT.id)
      userAccountRoles <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === userAccountAssociation.id)
      roles <- TblUserRole.filter(_.id === userAccountRoles.userRoleId)
      permissionTemplates <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === roles.id)
      permissionTemplate <- TblPermissionTemplate.filter(_.id === permissionTemplates.permissionTemplateId)
      mappings <- TblPermissionTemplateMapping.filter{p => p.permissionTemplateId === permissionTemplate.id && p.environmentTypeId === envType}
    } yield mappings.permissions
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate,
      DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "checkPermissions")
      .map { permissions0 =>
        permissions0.map { p =>
          p.split(",").toSet.map{a:String => a.toInt}
        }.toSet.flatten
      }.map(permissions.subsetOf(_))
  }

  def fetchPermissions(accountId: Long, businessUserId: Long, envType: Int): Future[Seq[String]] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId && a.associationType === UserAccountAssociationType.DIRECT.id)
      userAccountCustomRoles <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociation.id && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType)
      customRoles <- TblUserRole.filter(_.id === userAccountCustomRoles.userRoleId)
      permissionTemplates <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === customRoles.id)
      permissionTemplate <- TblPermissionTemplate.filter(_.id === permissionTemplates.permissionTemplateId)
      mappings <- TblPermissionTemplateMapping.filter{p => p.permissionTemplateId === permissionTemplate.id && p.environmentTypeId === envType}
    } yield mappings.permissions
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate,
      DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "fetchPermissions_envType")
  }

  def fetchSystemRolesPermissions(accountId: Long, businessUserId: Long, envType: Int, rootAccountType: Option[Int]): Future[Set[Int]] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId )
      userAccountSysDefinedRoles <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociation.id && uar.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType)
    } yield userAccountSysDefinedRoles
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "fetchSystemRolesPermissions_envType")
      .map(sysRoles => sysRoles.groupBy(_.roleType).flatMap(roles => SystemDefiendRolesPermissions.sysDefinedRoles(rootAccountType, roles._1).getOrElse(envType, Seq.empty)).toSet)
  }

  def fetchPermissions(accountId: Long, businessUserId: Long): Future[Seq[(Int, String)]] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId && a.associationType === UserAccountAssociationType.DIRECT.id)
      userAccountRoles <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === userAccountAssociation.id)
      roles <- TblUserRole.filter(_.id === userAccountRoles.userRoleId)
      permissionTemplates <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === roles.id)
      permissionTemplate <- TblPermissionTemplate.filter(_.id === permissionTemplates.permissionTemplateId)
      mappings <- TblPermissionTemplateMapping.filter{p => p.permissionTemplateId === permissionTemplate.id}
    } yield (mappings.environmentTypeId, mappings.permissions)
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate,
      DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "fetchPermissions")
  }

  def fetchSystemRolesPermissions(accountId: Long, businessUserId: Long, rootAccountType: Option[Int]): Future[Seq[(Int, String)]] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId && a.associationType === UserAccountAssociationType.DIRECT.id)
      userAccountSysDefinedRoles <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociation.id && uar.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType)
    } yield userAccountSysDefinedRoles
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "fetchSystemRolesPermissions")
      .map(sysRoles => sysRoles.flatMap(roles => SystemDefiendRolesPermissions.sysDefinedRolesAsSeq(rootAccountType, roles.roleType).map(permission => (permission._1, permission._2.mkString(",")))))
  }


  def checkGlobalPermissions(accountId: Long, businessUserId: Long, permissions: Set[Int], envType: Int): Future[Boolean] = {
    val domains = permissions.map(p =>
      DashboardUserPermissions.byId(p) match {
        case Some(dashboardUserPermission) =>
          dashboardUserPermission.domain match {
            case domain: Domains.Domain => domain.id
            case _ => -1
          }
        case _ => -1
      }
    ).filter(p => p >= 0)
    getGlobalPermissions(accountId, businessUserId, envType).flatMap{
      case Some(dtoPermissionTemplateMapping) =>
        dtoPermissionTemplateMapping.globalScope match {
          case None => Future.successful(false)
          case Some(globalScope) =>
            if(domains.subsetOf(globalScope.split(",").toSet.map{a:String => a.toInt}))
              checkPermissions(accountId, businessUserId, permissions, dtoPermissionTemplateMapping.environmentTypeId)
            else
              Future.successful(false)
        }
      case _ => Future.successful(false)
    }
  }
  //Not in use
  def getGlobalPermissions(accountId: Long, businessUserId: Long, envType: Int): Future[Option[DtoPermissionTemplateMapping]] = {
    val query = for{
      userAccountAssociation <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.businessUserId === businessUserId && a.associationType === UserAccountAssociationType.DIRECT.id)
      userAccountRoles <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociation.id && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType)
      roles <- TblUserRole.filter(_.id === userAccountRoles.userRoleId)
      permissionTemplates <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === roles.id)
      permissionTemplate <- TblPermissionTemplate.filter(_.id === permissionTemplates.permissionTemplateId)
      mappings <- TblPermissionTemplateMapping.filter{p => p.permissionTemplateId === permissionTemplate.id && p.environmentTypeId === envType}
    } yield mappings

    dbProxy.run(query.result.headOption, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate,
      DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "getGlobalPermissions")
  }

  def createUserWithAdditionalDetails(dtoBusinessUser: DtoBusinessUser, esV2Permissions: Map[Int, String], globalV2Permissions: String, creator: Creator): Future[String] = {
    val uuid = UUIDUtilities.getRandomUUID
    val envIds = esV2Permissions.keys.map(_.toLong).toSet
    val query = for {
      userId <- (TblBusinessUser returning TblBusinessUser.map(_.id)) += dtoBusinessUser.copy(email = dtoBusinessUser.email.toLowerCase())
      _ <- TblActivationToken  += DtoActivationToken(0, userId, token = Some(uuid), Some(DateTime.now()))
      updatedBy <- TblUserAccountAssociation.filter(uaa => uaa.businessUserId === creator.userId && uaa.accountId === creator.accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(_.id).result.headOption
      userAccountAssociationId <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += DtoUserAccountAssociation(id = 0, businessUserId = userId, accountId = dtoBusinessUser.accountId, status = UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = false , revoked = None, updatedBy = updatedBy, updatedAt = DateTime.now)
      userRoleId <- (TblUserRole returning TblUserRole.map(_.id)) += DtoUserRole(id = 0, name = s"Role_$userId", description = None, byBusinessUserId = creator.userId, byAccountId = creator.accountId, updatedAt = DateTime.now)
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, userAccountAssociationId, Some(userRoleId))
      permissionTemplateId <- {
        val dtoPermissionTemplate = DtoPermissionTemplate(0, s"Template_$userId", TemplateTypes.DEFAULT.id, dtoBusinessUser.accountId, updatedBy.getOrElse(userAccountAssociationId), DateTime.now)
        (TblPermissionTemplate returning TblPermissionTemplate.map(_.id)) += dtoPermissionTemplate
      }
      envs <- TblEnvironment.filter(e => e.accountId === dtoBusinessUser.accountId && e.id.inSet(envIds)).result
      _ <- {
        val dtoPermissionTemplateMappings = if(dtoBusinessUser.isPrimaryUser){
          esV2Permissions.map { dp =>
            DtoPermissionTemplateMapping(0, permissionTemplateId, dp._1, dp._2, None)
          }
        }else {
          esV2Permissions.map { dp =>
            val envType = envs.filter(_.id == dp._1).map(_.environmentType.toInt).headOption.getOrElse(throw new Exception("Environment is not right"))
            DtoPermissionTemplateMapping(0, permissionTemplateId, envType, dp._2, None)
          }
        }
        if(globalV2Permissions.nonEmpty)
          TblPermissionTemplateMapping ++= List(DtoPermissionTemplateMapping(0, permissionTemplateId, EnvironmentTypes.GLOBAL_ENVIRONMENT.id, globalV2Permissions, None)) ++ dtoPermissionTemplateMappings
        else
          TblPermissionTemplateMapping ++= dtoPermissionTemplateMappings
      }
      _ <- TblRolePermissionTemplateAssociation += DtoRolePermissionTemplateAssociation(id = 0, permissionTemplateId = permissionTemplateId, userRoleId = userRoleId)
    } yield uuid
    dbProxy.run(query.transactionally, Set(DBTables.TblBusinessUser, DBTables.TblActivationToken, DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation,
      DBTables.TblPermissionTemplate, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Insert, "createUserWithAdditionalDetails")
  }

  def fetchAccountDetails(accountId: Long): Future[(Seq[(DtoUserAccountAssociation, DtoBusinessUser)], DtoAccountHierarchy, Seq[DtoPermissionTemplate], Seq[DtoPermissionTemplateMapping], Seq[DtoUserRole], Seq[DtoRolePermissionTemplateAssociation], Seq[DtoUserAccountRoleAssociation], DtoAccount, Seq[((DtoAccountHierarchy, DtoAccount), DtoIndustry)], Seq[(DtoAccount, DtoIndustry)], Seq[DtoBusinessUser])] = {
    val query = for {
      acc <- TblAccounts.filter(_.id === accountId).result.head
      uaa <- (TblUserAccountAssociation.filter(a => a.accountId === accountId && a.associationType === UserAccountAssociationType.DIRECT.id) join TblBusinessUser on(_.businessUserId === _.id)).result
      ah <- TblAccountHierarchy.filter(_.accountId === accountId).result.head
      pt <- TblPermissionTemplate.filter(_.accountId === accountId).result
      ptm <- TblPermissionTemplateMapping.filter(_.permissionTemplateId inSet pt.map(_.id)).result
      roles <- TblUserRole.filter(_.byAccountId === accountId).result
      rpta <- TblRolePermissionTemplateAssociation.filter(_.permissionTemplateId inSet pt.map(_.id)).result
      uaro <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId inSet uaa.map(_._1.id)).result
      subaccounts <- (TblAccountHierarchy.filter(a => {
        a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"${ah.hierarchyPath}%") && a.accountId =!= accountId
      }) join TblAccounts.filter(a => a.isDeleted === false) on (_.accountId === _.id) join TblIndustries on (_._2.industrySector === _.sector)).result
      subaccountsNotMigrated <- (TblAccounts.filter(_.parentId === accountId).filterNot(_.id inSetBind subaccounts.map(_._1._1.accountId)) join TblIndustries on (_.industrySector === _.sector)).result
      users <- TblBusinessUser.filter(_.accountId inSetBind subaccountsNotMigrated.map(_._1.accountId) ).result
    } yield (uaa, ah, pt, ptm, roles, rpta, uaro, acc, subaccounts, subaccountsNotMigrated, users)

    dbProxy.run(query, Set(DBTables.TblAccount, DBTables.TblAccountHierarchy, DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation,
      DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblRolePermissionTemplateAssociation,
      DBTables.TblPermissionTemplateMapping), DBActions.Select, "fetchAccountDetails")
  }

  def fetchAccountDetailsLite(accountId: Long): Future[(Seq[(DtoUserAccountAssociation, DtoBusinessUser)], DtoAccountHierarchy, DtoAccount, Seq[((DtoAccountHierarchy, DtoAccount), DtoIndustry)])] = {
    val query = for {
      acc <- TblAccounts.filter(_.id === accountId).result.head
      uaa <- (TblUserAccountAssociation.filter(a => a.accountId === accountId && a.associationType === UserAccountAssociationType.DIRECT.id) join TblBusinessUser on(_.businessUserId === _.id)).result
      ah <- TblAccountHierarchy.filter(_.accountId === accountId).result.head
      subaccounts <- (TblAccountHierarchy.filter(a => {
        a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"${ah.hierarchyPath}%") && a.accountId =!= accountId
      }) join TblAccounts.filter(a => a.isDeleted === false) on (_.accountId === _.id) join TblIndustries on (_._2.industrySector === _.sector)).result

    } yield (uaa, ah, acc, subaccounts)

    dbProxy.run(query, Set(DBTables.TblAccount, DBTables.TblAccountHierarchy, DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation), DBActions.Select, "fetchAccountDetails")
  }

  //SA
  def fetchUserDetails(userId: Long): Future[(Seq[DtoUserAccountAssociation], Seq[DtoUserAccountRoleAssociation], Seq[DtoUserRole], Seq[DtoRolePermissionTemplateAssociation], Seq[DtoPermissionTemplateMapping], DtoBusinessUser)] = {
    val query = for {
      dtoBusinessUser <- TblBusinessUser.filter(_.id === userId).result.head
      dtoUserAccountAssociationSeq <- TblUserAccountAssociation.filter(a => a.businessUserId === userId && a.associationType === UserAccountAssociationType.DIRECT.id).result
      dtoUserAccountRoleAssociationSeq <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(dtoUserAccountAssociationSeq.map(_.id)) && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType).result
      dtoUserRoles <- TblUserRole.filter(_.id inSet dtoUserAccountRoleAssociationSeq.map(_.userRoleId.get)).result
      dtoRolePermissionTemplateAssociations <- TblRolePermissionTemplateAssociation.filter(_.userRoleId inSet dtoUserRoles.map(_.id)).result
      dtoPermissionTemplateMappingSeq <- TblPermissionTemplateMapping.filter(_.permissionTemplateId inSet dtoRolePermissionTemplateAssociations.map(_.permissionTemplateId)).result
    } yield (dtoUserAccountAssociationSeq, dtoUserAccountRoleAssociationSeq, dtoUserRoles, dtoRolePermissionTemplateAssociations, dtoPermissionTemplateMappingSeq, dtoBusinessUser)
    dbProxy.run(query, Set(DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation,
      DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "fetchUserDetails")
  }

  def doesUserAccountAssociationExist(userId: Long) : Future[Boolean] = {
    val query = TblUserAccountAssociation.filter(a => a.businessUserId === userId && a.associationType === UserAccountAssociationType.DIRECT.id).map(_.id).exists
    dbProxy.run(query.result, DBTables.TblUserAccountAssociation, DBActions.Select, "doesUserAccountAssociationExist")
  }

  def getUserIdByEmail(email: String) : Future[Option[Long]] = {
    val query = TblBusinessUser.filter(_.email === email).map(_.id).result.headOption
    dbProxy.run(query, DBTables.TblBusinessUser, DBActions.Select, "getUserIdByEmail")
  }

  def updateBusinessUserV2(dtoBusinessUser: DtoBusinessUser, userInfo: DelegatedUserForm) : Future[Boolean] = {
    val query = TblBusinessUser.filter(_.id === dtoBusinessUser.id).map(bu => (bu.firstName, bu.lastName, bu.contactNumber)).update((userInfo.firstname, userInfo.lastname, userInfo.contactnumber))
    dbProxy.run(query, DBTables.TblBusinessUser, DBActions.Update, "updateBusinessUserV2")
      .map(_ > 0)
  }

  def updateUserV2(userAccountAssociationId: Long, dtoBusinessUser: DtoBusinessUser, environmentTypeVsPermissionsMap: Map[Int, String], creator: Creator, userInfo: DelegatedUserForm): Future[Boolean] = {
    val query = for {
      _ <- TblBusinessUser.filter(_.id === dtoBusinessUser.id).map(bu => (bu.firstName, bu.lastName, bu.contactNumber)).update((userInfo.firstname, userInfo.lastname, userInfo.contactnumber))
      userRoleIds <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === userAccountAssociationId).map(_.userRoleId.get).result
      userRoleId <- TblUserRole.filter(ur=> (ur.id inSet userRoleIds) && ur.name.like("Role%")).map(_.id).result.head
      permissionTemplateId <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === userRoleId).map(_.permissionTemplateId).result.head
      _ <- TblPermissionTemplateMapping.filter(_.permissionTemplateId === permissionTemplateId).delete
      _ <- {
        val dtoPermissionTemplateMappings = environmentTypeVsPermissionsMap.map{ dp =>
          DtoPermissionTemplateMapping(0, permissionTemplateId, dp._1, dp._2, None)
        }
        TblPermissionTemplateMapping ++= dtoPermissionTemplateMappings
      }
    } yield true
    dbProxy.run(query.transactionally, Set(DBTables.TblBusinessUser, DBTables.TblPermissionTemplateMapping), DBActions.Update, "updateUserV2")
  }

  def updateSponsorBankRolesForPrograms(userId: Long, accountsWithRoles: Seq[AccountWithRolesInput]): Future[Int] = {
    val businessUserId = userId
    val accountIds = accountsWithRoles.map(_.accountId)
    val query = for {
      programIds <- TblSponsorBankProgram.filter(_.sponsorBankId.inSet(accountIds)).map(_.programId).result
      existAccountIds <- TblUserAccountAssociation.filter(uaa => uaa.accountId.inSet(programIds) && (uaa.businessUserId === businessUserId)).map(_.accountId).result
      _ <- TblUserAccountAssociation.filter(_.accountId.inSet(existAccountIds)).map(_.status).update(UserAccountAssociationStatuses.ACTIVE.id)
      _ <- TblUserAccountAssociation returning TblUserAccountAssociation.map(_.id) ++= programIds.diff(existAccountIds).map(programId => DtoUserAccountAssociation(id = 0,
        businessUserId, programId,  UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = false, None, None, DateTime.now, Some(UserAccountAssociationType.PROGRAM.id))
      )
      uaaIdsAndSpIds <-TblSponsorBankProgram.filter(_.sponsorBankId.inSet(accountIds)).joinLeft(TblUserAccountAssociation.filter(uaa => uaa.businessUserId === businessUserId)).on(_.programId === _.accountId).map(res=> (res._1.sponsorBankId, res._2.map(_.id))).result
      roleDelete <- TblUserAccountRoleAssociation.filter(uara => uara.userAccountAssociationId.inSet(uaaIdsAndSpIds.map(_._2.get)) &&
        uara.roleType.inSet(Seq(SystemDefinedRoles.BSA_OFFICER.roleType, SystemDefinedRoles.COMPLIANCE_ANALYST_ROLE.roleType))).delete
      _ <- DBIO.seq(
        accountsWithRoles.flatMap(accountWithRoles => accountWithRoles.roles.filter(res => res.roleType == SystemDefinedRoles.BSA_OFFICER.roleType || res.roleType == SystemDefinedRoles.COMPLIANCE_ANALYST_ROLE.roleType).map(roles =>
            profile
              .api
              .queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.roleType)))
              .forceInsertQuery(
                TblUserAccountAssociation.filter( _.id.inSet(uaaIdsAndSpIds.filter(_._1 == accountWithRoles.accountId).map(_._2.get))).map(res => (res.id, roles.roleType))
              )
        )): _*
      )
    } yield roleDelete
    dbProxy.run(query.transactionally, Set(DBTables.TblSponsorBankProgram, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Update, "updateSponsorBankRolesForPrograms")
  }

  def updateBusinessUserWithAssociations(updateBusinessUserInput: UpdateBusinessUserInput, availableAccountIdVsAssociationIdMap: Map[Long, Long]): Future[Boolean] = {
    val businessUserId = updateBusinessUserInput.id
    val accountsWithRoles = updateBusinessUserInput.accountsWithRoles
    val accountIdsInput = updateBusinessUserInput.accountsWithRoles.map(_.accountId)
    val existingAccountIds = availableAccountIdVsAssociationIdMap.keySet.toSeq
    val existingAssociationIds: Set[Long] = availableAccountIdVsAssociationIdMap.values.toSet
    val query = for {
      _ <- TblBusinessUser.filter(_.id === updateBusinessUserInput.id).map(bu => (bu.firstName, bu.lastName, bu.contactNumber)).update((updateBusinessUserInput.firstName, updateBusinessUserInput.lastName, updateBusinessUserInput.contactNumber))
      deletedAccountIds <- TblUserAccountAssociation.filter(uaa => (uaa.businessUserId === businessUserId) && (uaa.status =!= UserAccountAssociationStatuses.ACTIVE.id)).map(_.accountId).result
      _ <- TblUserAccountAssociation.filter(uaa => (uaa.accountId inSet (existingAccountIds diff accountIdsInput)) && (uaa.businessUserId === updateBusinessUserInput.id)).map(_.status).update(UserAccountAssociationStatuses.DELETED.id)
      _ <- TblUserAccountAssociation.forceInsertAll(((accountIdsInput diff existingAccountIds) diff deletedAccountIds).map(accountId => DtoUserAccountAssociation(id= 0,businessUserId = updateBusinessUserInput.id,accountId = accountId,status = Status.ACTIVE.id,isPrimaryUser = false,revoked = None,updatedBy = None,updatedAt = DateTime.now)))
      _ <- DBIO.seq((deletedAccountIds intersect  accountIdsInput).map(accountId => TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId  && uaa.businessUserId === businessUserId).map(_.status).update(UserAccountAssociationStatuses.ACTIVE.id)): _*)
      _ <- TblUserAccountAssociation.filter(uaa => uaa.accountId.inSet(accountIdsInput) && uaa.businessUserId === businessUserId).map(_.associationType).update(Some(UserAccountAssociationType.DIRECT.id))
      _ <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId inSet existingAssociationIds).delete
      _ <- DBIO.seq(
        accountsWithRoles.flatMap(accountWithRoles => accountWithRoles.roles.distinct.map( roles =>
          if(roles.roleType == SystemDefinedRoles.CUSTOMROLE.roleType) {
            profile
              .api
              .queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.userRoleId, uara.roleType)))
              .forceInsertQuery(
                TblUserAccountAssociation.filter(uaa => uaa.businessUserId === businessUserId && uaa.associationType === UserAccountAssociationType.DIRECT.id &&  uaa.accountId === accountWithRoles.accountId).map(res => (res.id, roles.roleId, roles.roleType))
              )
          } else {
            profile
              .api
              .queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.roleType)))
              .forceInsertQuery(
                TblUserAccountAssociation.filter(uaa => uaa.businessUserId === businessUserId && uaa.accountId === accountWithRoles.accountId).map(res => (res.id, roles.roleType))
              )
          }
        )): _*
      )
    } yield true
    dbProxy.run(query.transactionally, Set(DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation, DBTables.TblRolePermissionTemplateAssociation), DBActions.Update, "updateUserV2")
  }

  def createBusinessUserWithAssociations(createBusinessUserInput: CreateBusinessUserInput): Future[(String, Long)] = {
    val accountsWithRoles = createBusinessUserInput.accountsWithRoles
    val accountIdsInput = createBusinessUserInput.accountsWithRoles.map(_.accountId)
    val uuid = UUIDUtilities.getRandomUUID
    val query = for {
      userId <- (TblBusinessUser returning TblBusinessUser.map(_.id)) += DtoBusinessUser(
        id = 0,
        email = createBusinessUserInput.email,
        firstName = createBusinessUserInput.firstName,
        lastName = createBusinessUserInput.lastName,
        contactNumber = createBusinessUserInput.contactNumber,
        registeredOn = DateTime.now(),
        accountNonLocked = true,
        accountId = createBusinessUserInput.accountId,
        isPrimaryUser = false)
      _ <- TblActivationToken  += DtoActivationToken(0, userId, token = Some(uuid), Some(DateTime.now()))
      _ <- TblUserAccountAssociation.forceInsertAll((accountIdsInput).map(accountId => DtoUserAccountAssociation(id= 0,businessUserId = userId,accountId = accountId,status = Status.ACTIVE.id,isPrimaryUser = false,revoked = None,updatedBy = None,updatedAt = DateTime.now)))
      _ <- DBIO.seq(
        accountsWithRoles.flatMap(accountWithRoles => accountWithRoles.roles.distinct.map( roles =>
          if(roles.roleType == SystemDefinedRoles.CUSTOMROLE.roleType) {
            profile
              .api
              .queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.userRoleId, uara.roleType)))
              .forceInsertQuery(
                TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountWithRoles.accountId).map(res => (res.id, roles.roleId, roles.roleType))
              )
          } else {
            profile
              .api
              .queryInsertActionExtensionMethods(TblUserAccountRoleAssociation.map(uara => (uara.userAccountAssociationId, uara.roleType)))
              .forceInsertQuery(
                TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id && uaa.accountId === accountWithRoles.accountId).map(res => (res.id, roles.roleType))
              )
          }
        )): _*
      )
    } yield (uuid, userId)
    dbProxy.run(query.transactionally, Set(DBTables.TblBusinessUser, DBTables.TblActivationToken, DBTables.TblUserAccountAssociation), DBActions.Insert, "createBusinessUserWithAssociations")
  }

  def getAccountEnvironments(accountId: Long): Future[(DtoAccount, Seq[DtoEnvironment])] = {
    val query = for {
      account <- TblAccounts.filter(_.id === accountId).result.head
      envs <- TblEnvironment.filter(_.accountId === account.accountId).result
    } yield (account, envs)

    dbProxy.run(query, Set(DBTables.TblAccount, DBTables.TblEnvironment), DBActions.Select, "getAccountEnvironments")
  }

  def getDashboardUserPermissions(userAccountAssociationId: Long): Future[Map[Int, Set[Int]]] = {
    val query = for {
      userRoleIds <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociationId && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType).map(_.userRoleId.get).result
      userRoles <- TblUserRole.filter(ur=> ur.id inSet userRoleIds).map(_.id).result
      permissionTemplateIds <- TblRolePermissionTemplateAssociation.filter(_.userRoleId inSet userRoles).map(_.permissionTemplateId).result
      ptms <- TblPermissionTemplateMapping.filter(_.permissionTemplateId inSet permissionTemplateIds).result
    } yield ptms
    dbProxy.runWithWriter(query, Set(DBTables.TblUserAccountRoleAssociation, DBTables.TblUserRole, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "getDashboardUserPermissions")
      .map { res =>
        val resMap: mutable.Map[Int, Set[Int]] = mutable.Map()
        res.foreach(dtoPermissionTemplateMapping => {
          if (resMap.contains(dtoPermissionTemplateMapping.environmentTypeId)) {
            var permissions = resMap.getOrElse(dtoPermissionTemplateMapping.environmentTypeId, Set.empty)
            if(!dtoPermissionTemplateMapping.permissions.isEmpty) {
              permissions ++= dtoPermissionTemplateMapping.permissions.split(",").map(_.toInt).toSet
            }
            resMap.put(dtoPermissionTemplateMapping.environmentTypeId, permissions)
          } else {
            resMap.put(dtoPermissionTemplateMapping.environmentTypeId,
              if(dtoPermissionTemplateMapping.permissions.isEmpty) Set.empty[Int]
              else dtoPermissionTemplateMapping.permissions.split(",").map(_.toInt).toSet
            )
          }
        })
        resMap.toMap
      }
  }

  def getDashboardUserPermissions(userAccountAssociationId: Long, accountId: Long, userId: Long): Future[Map[Int, Set[Int]]] = {
    val query = for {
      uaaId <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.businessUserId === userId && uaa.id =!= userAccountAssociationId).map(_.id).result
      userRoleIds <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(uaaId :+ userAccountAssociationId) && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType).map(_.userRoleId.get).result
      userRoles <- TblUserRole.filter(ur => ur.id inSet userRoleIds).map(_.id).result
      permissionTemplateIds <- TblRolePermissionTemplateAssociation.filter(_.userRoleId inSet userRoles).map(_.permissionTemplateId).result
      ptms <- TblPermissionTemplateMapping.filter(_.permissionTemplateId inSet permissionTemplateIds).result
    } yield ptms
    dbProxy.runWithWriter(query, Set(DBTables.TblUserAccountRoleAssociation, DBTables.TblUserRole, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "getDashboardUserPermissions")
      .map { res =>
        val resMap: mutable.Map[Int, Set[Int]] = mutable.Map()
        res.foreach(dtoPermissionTemplateMapping => {
          if (resMap.contains(dtoPermissionTemplateMapping.environmentTypeId)) {
            var permissions = resMap.getOrElse(dtoPermissionTemplateMapping.environmentTypeId, Set.empty)
            if (!dtoPermissionTemplateMapping.permissions.isEmpty) {
              permissions ++= dtoPermissionTemplateMapping.permissions.split(",").map(_.toInt).toSet
            }
            resMap.put(dtoPermissionTemplateMapping.environmentTypeId, permissions)
          } else {
            resMap.put(dtoPermissionTemplateMapping.environmentTypeId,
              if (dtoPermissionTemplateMapping.permissions.isEmpty) Set.empty[Int]
              else dtoPermissionTemplateMapping.permissions.split(",").map(_.toInt).toSet
            )
          }
        })
        resMap.toMap
      }
  }
  def getDashboardUserSystemPermissions(userAccountAssociationId: Long, rootAccountType: Option[Int]): Future[Map[Int, Set[Int]]] = {
    val query = for {
      userRoleTypes <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccountAssociationId && uar.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType).map(_.roleType).result
    } yield userRoleTypes.toSet
    dbProxy.run(query, DBTables.TblUserAccountRoleAssociation, DBActions.Select, "getDashboardUserSystemPermissions")
      .map { roles =>
        val resMap: mutable.Map[Int, Set[Int]] = mutable.Map()
        roles.foreach { roleType =>
          SystemDefiendRolesPermissions.sysDefinedRoles(rootAccountType, roleType).map(permissions => {
            if (resMap.contains(permissions._1)) {
              var permissionsSet = resMap.getOrElse(permissions._1, Set.empty)
              permissionsSet ++= permissions._2
              resMap.put(permissions._1, permissionsSet)
            } else {
              resMap.put(permissions._1, permissions._2)
            }
          })
        }
        resMap.toMap
      }
  }

  def getPlatformUserSystemPermissions(userId: Long, rootAccountType: Option[Int]): Future[Map[Long, Map[Int, Set[Int]]]] = {
    val query = for {
      accountAssociationIds <- TblUserAccountAssociation.filter(_.businessUserId === userId).map(uaa => (uaa.accountId, uaa.id)).result
      uaaRoleTypes <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(accountAssociationIds.map(_._2)) && uar.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType).map(uar => (uar.userAccountAssociationId, uar.roleType)).result
    } yield (accountAssociationIds, uaaRoleTypes)
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getPlatformUserSystemPermissions")
      .map {
        case (accountAssociationIds, uaaRoleTypes) =>
          val roleTypesByAssociationId = uaaRoleTypes.groupBy(_._1).mapValues(_.map(_._2).toSet)
          accountAssociationIds.map { case (accountId, associationId) =>
            val roleTypesForAccount = roleTypesByAssociationId.getOrElse(associationId, Set.empty)
            val resMap: mutable.Map[Int, Set[Int]] = mutable.Map()
            roleTypesForAccount.foreach { roleType =>
              SystemDefiendRolesPermissions.sysDefinedRoles(rootAccountType, roleType).map(permissions => {
                if (resMap.contains(permissions._1)) {
                  var permissionsSet = resMap.getOrElse(permissions._1, Set.empty)
                  permissionsSet ++= permissions._2
                  resMap.put(permissions._1, permissionsSet)
                } else {
                  resMap.put(permissions._1, permissions._2)
                }
              })
            }
            accountId -> resMap.toMap
          }.toMap
      }
  }

  def getDashboardUserSystemPermissions(userAccountAssociationId: Long, accountId: Long, userId: Long, rootAccountType: Option[Int]): Future[Map[Int, Set[Int]]] = {
    val query = for {
      uaaId <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.businessUserId === userId && uaa.id =!=userAccountAssociationId).map(_.id).result
      userRoleTypes <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(uaaId :+ userAccountAssociationId) && uar.roleType =!= SystemDefinedRoles.CUSTOMROLE.roleType).map(_.roleType).result  // combining both account owner permission(if exist) and current login sub-account permission
    } yield userRoleTypes.toSet
    dbProxy.run(query, DBTables.TblUserAccountRoleAssociation, DBActions.Select, "getDashboardUserSystemPermissions")
      .map { roles =>
        val resMap: mutable.Map[Int, Set[Int]] = mutable.Map()
        roles.foreach { roleType =>
          SystemDefiendRolesPermissions.sysDefinedRoles(rootAccountType, roleType).map(permissions => {
            if (resMap.contains(permissions._1)) {
              var permissionsSet = resMap.getOrElse(permissions._1, Set.empty)
              permissionsSet ++= permissions._2
              resMap.put(permissions._1, permissionsSet)
            } else {
              resMap.put(permissions._1, permissions._2)
            }
          })
        }
        resMap.toMap
      }
  }

  def isSubAccount(parentAccountId: Long, subAccountId: Long): Future[Boolean] = {
    val query = TblAccountHierarchy.filter(a => {a.accountId === subAccountId})
    dbProxy.run(query.result.headOption, DBTables.TblAccountHierarchy, DBActions.Select, "isSubAccount")
      .map {
        res => {
          if(res.isEmpty) {
            false
          }
          else {
            res.head.hierarchyPath.startsWith(s"$parentAccountId/") || res.head.hierarchyPath.contains(s"/$parentAccountId/")
          }
        }
      }
  }

  def isImmediateSubAccount(parentAccountId: Long, subAccountId: Long): Future[Boolean] = {
    val query = TblAccountHierarchy.filter(a => {a.accountId === subAccountId})
    dbProxy.run(query.result.headOption, DBTables.TblAccountHierarchy, DBActions.Select, "isImmediateSubAccount")
      .map {
        res => {
          if(res.isEmpty) {
            false
          }
          else {
            res.head.hierarchyPath.endsWith(s"$parentAccountId/$subAccountId/")
          }
        }
      }
  }

  def getEnvironmentByEnvironmentId(environmentId: Long): Future[Option[DtoEnvironment]]= {
    val query = for{
      env <- TblEnvironment.filter(_.id === environmentId)
    } yield env
    dbProxy.run(query.result.headOption, DBTables.TblEnvironment, DBActions.Select, "getEnvironmentByEnvironmentId")
  }

  def getEnvironment(environmentType: Long, accountId: Long): Future[Option[DtoEnvironment]] = {
    val query = TblEnvironment.filter(e => e.environmentType === environmentType && e.accountId === accountId).result.headOption
    dbProxy.run(query, DBTables.TblEnvironment, DBActions.Select, "getEnvironment")
  }

  def getEnvironmentIds(environmentTypes: Seq[Long], accountId: Seq[Long]): Future[Seq[Long]] = {
    val query = TblEnvironment.filter(e => (e.environmentType inSet environmentTypes) && (e.accountId inSet accountId)).map(_.id).result
    dbProxy.run(query, DBTables.TblEnvironment, DBActions.Select, "getEnvironmentIds")
  }

  def getEnvironmentBySocialId(socialId: Long): Future[Option[DtoEnvironment]]= {
    val query = for{
      social <- TblEnvironmentSocialKeys.filter(_.id === socialId)
      env <- TblEnvironment.filter(_.id === social.environmentId)
    } yield env
    dbProxy.run(query.result.headOption, Set(DBTables.TblEnvironment, DBTables.TblEnvironmentSocialKeys), DBActions.Select, "getEnvironmentBySocialId")
  }

  //Not in use
  def getUserAccountPermissions(accountId: Long, businessUserId: Long): Future[Seq[DtoPermissionTemplateMapping]] = {
    val query = for {
      ua <- TblUserAccountAssociation.filter(ua => ua.accountId === accountId && ua.businessUserId === businessUserId && ua.associationType === UserAccountAssociationType.DIRECT.id).result
      uar <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId.inSet(ua.map(_.id)) && uar.roleType === SystemDefinedRoles.CUSTOMROLE.roleType).result
      pt <- TblRolePermissionTemplateAssociation.filter(_.userRoleId inSet uar.map(_.userRoleId.get)).result
      ptm <- TblPermissionTemplateMapping.filter(_.permissionTemplateId inSet pt.map(_.permissionTemplateId)).result
    } yield ptm
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping), DBActions.Select, "getUserAccountPermissions")
  }

  def fetchPermissionsByRoleId(userRoleId: Long): Future[Seq[(Int, String)]] = {
    val query = for {
      permissionTemplates <- TblRolePermissionTemplateAssociation.filter(_.userRoleId === userRoleId)
      mappings <- TblPermissionTemplateMapping.filter{p => p.permissionTemplateId === permissionTemplates.permissionTemplateId}
    } yield (mappings.environmentTypeId, mappings.permissions)
    dbProxy.run(query.result, Set(DBTables.TblPermissionTemplateMapping, DBTables.TblRolePermissionTemplateAssociation), DBActions.Select, "fetchPermissionsByRoleId")
  }

  def isAccountV2Provisioned(accountIds: Set[Long]): Future[Boolean] = {
    val query = for{
      ap <- TblAccountPermission
      if ap.accountId inSetBind accountIds
      if ap.permission === BusinessUserRoles.ACCOUNT_MANAGEMENT_V2.id
      if ap.enabled === true
    } yield ap
    dbProxy.run(query.result, DBTables.TblAccountPermission, DBActions.Select, "isAccountV2Provisioned") map (ap => ap.size == accountIds.size)
  }

  def isValidSubAccount(accountId: Long, subAccountIds: Set[Long]): Future[Boolean] = {
    val query = for{
      account <- TblAccounts.filter(_.parentId === accountId)
      if account.id inSetBind subAccountIds
    } yield account
    dbProxy.run(query.result, DBTables.TblAccount, DBActions.Select, "isValidSubAccount")
      .map (account => account.size == subAccountIds.size)
  }

  def isValidUser(accountIds: Set[Long], userIds: Set[Long]): Future[Boolean] = {
    val query = for{
      users <- TblBusinessUser
      if users.id inSetBind userIds
      if users.accountId inSetBind accountIds
    } yield users
    dbProxy.run(query.result, DBTables.TblBusinessUser, DBActions.Select, "isValidUser")
      .map (users => users.size == userIds.size)
  }

  def isValidUser(accountId: Long, userId: Long): Future[Boolean] = {
    val query = TblUserAccountAssociation.filter(f => f.accountId === accountId && f.businessUserId === userId).exists.result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "isValidUser")
  }

  def isValidActiveUser(accountId: Long, userId: Long): Future[Boolean] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.businessUserId === userId && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.associationType === UserAccountAssociationType.DIRECT.id).exists.result
    dbProxy.run(query, DBTables.TblUserAccountAssociation, DBActions.Select, "isValidActiveUser")
  }

  def isValidBusinessUser(accountId: Long, userId: Long): Future[Boolean] = {
    val query = TblBusinessUser.filter(u => u.accountId === accountId && u.id === userId).exists.result
    dbProxy.run(query, DBTables.TblBusinessUser, DBActions.Select, "isValidBusinessUser")
  }

  def isAccountPermissionProvisioned(accountId: Long, permission: Int): Future[Boolean] = {
    val query = TblAccountPermission.filter(ap => ap.accountId === accountId && ap.permission === permission && ap.enabled === true).exists
    dbProxy.run(query.result, DBTables.TblAccountPermission, DBActions.Select, "isAccountPermissionProvisioned")
  }

  def isAdministerProvisioned(accountId: Long): Future[Boolean] = {
    val query = TblAccountHierarchy.filter(sa => sa.accountId === accountId && sa.administer).exists.result
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "isAdministerProvisioned")
  }

  def userExists(email : String): Future[Boolean] = {
    val query = TblBusinessUser.filter(_.email === email).exists
    dbProxy.run(query.result, DBTables.TblBusinessUser, DBActions.Select, "userExists")
  }

  def getUser(id: Long): Future[Seq[DtoBusinessUser]] = {
    dbProxy.run(TblBusinessUser.filter(_.id === id).result, DBTables.TblBusinessUser, DBActions.Select, "getUser")
  }

  def isParentAccount(accountId : Long): Future[Boolean] = {
    val query = TblAccounts.filter(a => a.id === accountId && a.parentId.isEmpty).exists
    dbProxy.run(query.result, DBTables.TblAccount, DBActions.Select, "isParentAccount")
  }

  def insertAccountMigrationAudit(dtoAccountMigrationAudit: DtoAccountMigrationAudit): Future[DtoAccountMigrationAudit] = {
    val query = (TableQuery[TblAccountMigrationAudit] returning TableQuery[TblAccountMigrationAudit].map(_.id)) += dtoAccountMigrationAudit
    dbProxy.run(query, DBTables.TblAccountMigrationAudit, DBActions.Insert, "insertAccountMigrationAudit")
      .map(id => dtoAccountMigrationAudit.copy(id = id))
  }

  def getAccountIdByPermissionTemplateId(permissionTemplateId: Long): Future[Option[Long]] = {
    val query = TblPermissionTemplate.filter(_.id === permissionTemplateId).map(_.accountId).result.headOption
    dbProxy.run(query, DBTables.TblPermissionTemplate, DBActions.Select, "getAccountIdByPermissionTemplateId")
  }


  def getAccountAttributes(accountId: Long): Future[Seq[DtoAccountAttribute]] = {
    val query = TblAccountAttribute.filter(_.accountId === accountId).result
    dbProxy.run(query, DBTables.TblAccountAttribute, DBActions.Select, "getAccountAttributes")
  }

  def fetchUserEnvironmentRoles(accountIds: Set[Long]): Future[(Seq[DtoBusinessUser], Seq[DtoEnvironment], Seq[DtoBusinessUserEnvironmentRole], Seq[DtoAccountPermission])] = {
    val query = for {
      users <- TblBusinessUser.filter(_.accountId inSetBind accountIds).result
      env <- TblEnvironment.filter(_.accountId inSetBind accountIds).result
      role <- TblBusinessUserEnvironmentRole.filter(_.businessUserId inSetBind users.map(_.id)).result
      modules <- TblAccountPermission.filter(ap => ap.accountId.inSetBind(accountIds) && ap.enabled === true).result
    } yield (users, env, role, modules)
    dbProxy.run(query, Set(DBTables.TblBusinessUser, DBTables.TblEnvironment, DBTables.TblBusinessUserEnvironmentRole, DBTables.TblAccountPermission), DBActions.Select, "fetchUserEnvironmentRoles")
  }

  def fetchPrimaryUser(accountId: Long): Future[Option[DtoBusinessUser]] = {
    val query = TblBusinessUser.filter(a => {a.accountId === accountId && a.isPrimaryUser === true})
    dbProxy.run(query.result.headOption, DBTables.TblBusinessUser, DBActions.Select, "fetchPrimaryUser")
  }

  def fetchPrimaryUserOfAnAccount(accountId: Long): Future[Option[DtoBusinessUser]] = {
    val query = for {
      uaa <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.isPrimaryUser === true && a.status === UserAccountAssociationStatuses.ACTIVE.id && a.associationType === UserAccountAssociationType.DIRECT.id)
      bu <- TblBusinessUser.filter(_.id === uaa.businessUserId)
    } yield bu
    dbProxy.run(query.result.headOption,
      Set(DBTables.TblUserAccountAssociation, DBTables.TblBusinessUser),
      DBActions.Select,
      "fetchPrimaryUserOfAnAccount")
  }


  def fetchAccountAndSubAccounts(accountId: Long): Future[(Option[DtoAccount], Seq[DtoAccount])] = {
    val query = for {
      account <- TblAccounts.filter(_.id === accountId).result.headOption
      subAccounts <- TblAccounts.filter(a => a.parentId === accountId && a.isDeleted === false).result
    } yield (account, subAccounts)
    dbProxy.run(query, DBTables.TblAccount, DBActions.Select, "fetchAccountAndSubAccounts")
  }

  def migrateAccount(accountId: Long ,accountType: Int, startTime: DateTime, dtoAccountAttributeSeq : Seq[DtoAccountAttribute]  , dtoAccountPermissions : Seq[DtoAccountPermission],dtoAccountHierarchySeq: Seq[DtoAccountHierarchy], dtoPrimaryUserAccountAssociations : Seq[DtoUserAccountAssociation], dtoUserAccountAssociations : Seq[DtoUserAccountAssociation], dtoUserRoles : Seq[DtoUserRole], permissions : Seq[Map[Int, String]], subAccounts: Seq[SubAccountMigrationDetails], initiatedBy: String, isSubAccountMigration: Boolean, isSubAccountPromote: Boolean, mergeAccountId: Option[Long], parentAccountId: Option[Long], adminster: Option[Boolean], clock: Clock): Future[Boolean] = {
    val query = for {
      _ <- TblAccountAttribute ++= dtoAccountAttributeSeq
      _ <- TblAccountPermission ++= dtoAccountPermissions
      accountHierarchyIds <- (TblAccountHierarchy returning TblAccountHierarchy.map(_.id)) ++= dtoAccountHierarchySeq
      _ <- TblAccountAssociationHistory ++= accountHierarchyIds.map(accountHierarchyId => DtoAccountAssociationHistory(0, accountHierarchyId, clock.now, None))
      primaryUserAccountAssociationsIds <- (TblUserAccountAssociation returning TblUserAccountAssociation.map(_.id)) ++= dtoPrimaryUserAccountAssociations
      _ <- {
        val dtoPrimaryUserAccountRoleAssociations = primaryUserAccountAssociationsIds.map { primaryUaaId =>
          DtoUserAccountRoleAssociation(0, primaryUaaId, None, SystemDefinedRoles.ACCOUNTOWNER.roleType)
        }
        TblUserAccountRoleAssociation ++= dtoPrimaryUserAccountRoleAssociations
      }
      userAccountAssociationsIds <- (TblUserAccountAssociation returning TblUserAccountAssociation.map(_.id)) ++= dtoUserAccountAssociations
      userRoleIds <- (TblUserRole returning TblUserRole.map(_.id)) ++= dtoUserRoles
      permissionTemplateIds <- {
        val dtoPermissionTemplates = userAccountAssociationsIds.zipWithIndex.map { case (userAccountAssociationId, index) =>
          if (dtoUserAccountAssociations(index).isPrimaryUser) {
            DtoPermissionTemplate(0, s"Template_${dtoUserAccountAssociations(index).businessUserId}", TemplateTypes.DEFAULT.id, dtoUserAccountAssociations(index).accountId, userAccountAssociationId, clock.now)
          } else {
            DtoPermissionTemplate(0, s"Template_${dtoUserAccountAssociations(index).businessUserId}", TemplateTypes.CUSTOM.id, dtoUserAccountAssociations(index).accountId, userAccountAssociationId, clock.now)
          }
        }
        (TblPermissionTemplate returning TblPermissionTemplate.map(_.id)) ++= dtoPermissionTemplates
      }
      _ <- {
        val dtoUserAccountRoleAssociations = userRoleIds.zipWithIndex.map { case (userRoleId, index) =>
          DtoUserAccountRoleAssociation(0, userAccountAssociationsIds(index), Option(userRoleId))
        }
        TblUserAccountRoleAssociation ++= dtoUserAccountRoleAssociations
      }
      _ <- {
        val dtoPermissionTemplateMappingsList = for ((permissionTemplateId, index) <- permissionTemplateIds.zipWithIndex; permission <- permissions(index) ) yield {
          DtoPermissionTemplateMapping(0, permissionTemplateId, permission._1, permission._2, None)
        }
        TblPermissionTemplateMapping returning TblPermissionTemplateMapping.map(_.id) ++= dtoPermissionTemplateMappingsList
      }
      _ <- {
        val dtoRolePermissionTemplateAssociation = userRoleIds.zipWithIndex.map { case (userRole, index) =>
          DtoRolePermissionTemplateAssociation(0, permissionTemplateIds(index), userRole)
        }
        TblRolePermissionTemplateAssociation ++= dtoRolePermissionTemplateAssociation
      }
      _ <- {
        val processingTime = DateTime.now().getMillis - startTime.getMillis
        if(isSubAccountMigration) {
          val dtoAccountMigrationAudit = subAccounts.map { subAccount =>
            val additionalInfo = Serialization.write(subAccount)
            DtoAccountMigrationAudit(0, subAccount.accountId, accountType, Some(additionalInfo), MigrationStatus.SUCCEEDED.id, initiatedBy, startTime, processingTime)
          }
          TblAccountMigrationAudit ++= dtoAccountMigrationAudit
        } else {
          val additionalInfo = Serialization.write(MigrationAccountDetailsAudit(accountId = accountId, accountType = accountType, subAccounts = subAccounts, parentAccountId, mergeAccountId, adminster))
          val dtoAccountMigrationAudit = DtoAccountMigrationAudit(0, accountId, accountType, Some(additionalInfo), MigrationStatus.SUCCEEDED.id, initiatedBy, startTime, processingTime)
          TblAccountMigrationAudit += dtoAccountMigrationAudit
        }

      }
      _ <- {
        val accounts = if(isSubAccountMigration || isSubAccountPromote || mergeAccountId.isDefined) subAccounts.map(_.accountId) else accountId +: subAccounts.map(_.accountId)
        val dtoAccountUIConfigurations = accounts.map(DtoAccountUIConfiguration(0, _, defaultAutoTimeoutInMinutes.toShort, defaultIdleTimeoutInMinutes.toShort, DateTime.now(), DateTime.now()))
        TblAccountUIConfiguration ++= dtoAccountUIConfigurations
      }
    } yield true

    if(isSubAccountPromote) {
      val subAccountIds = subAccounts.map{sub => sub.accountId}
      val updateQuery = TblAccounts.filter(_.id inSetBind subAccountIds).map(_.parentId).update(None)
      dbProxy.run((query.transactionally andThen updateQuery).transactionally, Set(DBTables.TblAccount, DBTables.TblAccountMigrationAudit, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping,
        DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblUserRole, DBTables.TblUserAccountAssociation,
        DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblAccountPermission, DBTables.TblAccountAttribute), DBActions.Upsert, "migrateAccount")
        .map(_ > 0)
    } else if(mergeAccountId.isDefined) {
      val userUpdateQuery = TblBusinessUser.filter(_.accountId === accountId).map(_.accountId).update(mergeAccountId.get)
      val deleteQuery = TblAccounts.filter(_.id === accountId).map(_.isDeleted).update(true)
      dbProxy.run((query.transactionally andThen userUpdateQuery andThen deleteQuery).transactionally, Set(DBTables.TblAccount, DBTables.TblBusinessUser, DBTables.TblAccountMigrationAudit, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping,
        DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblUserRole, DBTables.TblUserAccountAssociation,
        DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblAccountPermission, DBTables.TblAccountAttribute), DBActions.Upsert, "migrateAccount")
        .map(_ > 0)
    } else {
      dbProxy.run(query.transactionally, Set(DBTables.TblAccountMigrationAudit, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplateMapping,
        DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate, DBTables.TblUserRole, DBTables.TblUserAccountAssociation,
        DBTables.TblAccountAssociationHistory, DBTables.TblAccountHierarchy, DBTables.TblAccountPermission, DBTables.TblAccountAttribute), DBActions.Upsert, "migrateAccount")
    }
  }

  def getActiveSubAccountIds(accountId: Long): Future[Set[Long]] = {
    val query = TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(s"$accountId/%") && a.accountId =!= accountId).map(_.accountId)
    dbProxy.run(query.result, DBTables.TblAccountHierarchy, DBActions.Select, "getSubAccountIds")
      .map(_.toSet)
  }

  def getSubAccountIdsWithParent(accountId: Long): Future[Set[DtoAccountHierarchy]] = {
    val query = TblAccountHierarchy.filter(a => a.hierarchyPath.like(s"$accountId/%"))
    dbProxy.run(query.result, DBTables.TblAccountHierarchy, DBActions.Select, "getSubAccountIds")
      .map(_.toSet)
  }

  def getAllSubAccountIds(accountId: Long): Future[Set[Long]] = {
    val query = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse("")) && a.accountId =!= accountId).result
    } yield th.map(_.accountId)
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getAllSubAccountIds").map(_.toSet)
  }

  def getActiveSubAccountIdsWithParent(accountId: Long): Future[Set[Long]] = {
    val query = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyStatus === Status.ACTIVE.id && a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse(""))).result
    } yield th.map(_.accountId)
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getSubAccountIdsWithParent").map(_.toSet)
  }

  def removePermissionsForSubAccounts(subAccountIds: Set[Long], permissions: Set[Int]): Future[Int] = {
    val deleteQuery = TblAccountPermission.filter(row => (row.accountId inSet subAccountIds) && (row.permission inSet permissions)).delete
    dbProxy.run(deleteQuery, DBTables.TblAccountPermission, DBActions.Delete, "removePermissionsForSubAccounts")
  }

  def getUsers(accountId: Long): Future[Seq[(DtoUserAccountAssociation, DtoBusinessUser, DtoAccount)]] = {
    val query = for{
      uaa <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.associationType === UserAccountAssociationType.DIRECT.id && (a.status === UserAccountAssociationStatuses.ACTIVE.id || a.status === UserAccountAssociationStatuses.LOCKED.id))
      a <-  TblAccounts.filter(_.id === uaa.accountId)
      bu <- TblBusinessUser.filter(_.id === uaa.businessUserId)
    } yield (uaa, bu, a)
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount, DBTables.TblBusinessUser), DBActions.Select, "getUsers")
  }

  def getAccountHierarchyWithEnvironment(apiKey: String, statusSet: Set[ApiKeyStatus]): Future[Option[(DtoAccountHierarchy, DtoEnvironment)]] = {
    val query = for{
      ak <- TblApiKey.filter(k => k.apiKey === apiKey && k.status.inSet(statusSet))
      env <- TblEnvironment.filter(_.id === ak.environmentId)
      acc <- TblAccountHierarchy.filter(_.accountId === env.accountId)
    } yield (acc, env)
    dbProxy.run(query.result.headOption, Set(DBTables.TblApiKey, DBTables.TblEnvironment, DBTables.TblAccountHierarchy), DBActions.Select, "getAccountHierarchyWithEnvironment")
  }

  def getSubAccounts(accountId: Long): Future[Seq[DtoAccountHierarchy]] = {
    val query = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse(""))  && a.accountId =!= accountId).result
    } yield th
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getSubAccounts")
  }

  def getActiveSubAccounts(accountId: Long): Future[Seq[DtoAccountHierarchy]] = {
    val query = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse(""))   && (a.hierarchyStatus === Status.ACTIVE.id) && a.accountId =!= accountId).result
    } yield th
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getActiveSubAccounts")
  }

  def getApiKeysForSubAccount(accountId: Long): Future[Seq[(DtoAccount, DtoEnvironment, DtoApiKey, DtoAccountHierarchy)]] = {
    val query0 = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse("")) && a.hierarchyStatus === Status.ACTIVE.id).result
    } yield th
    dbProxy.run(query0, DBTables.TblAccountHierarchy, DBActions.Select, "getApiKeysForSubAccount_0") flatMap { ah: Seq[DtoAccountHierarchy] =>
      val accountIds = ah.map(_.accountId)
      val query = for {
        account <- TblAccounts.filter(_.id inSet accountIds)
        environments <- TblEnvironment.filter(_.accountId === account.id)
        apikeys <- TblApiKey.filter(ak => ak.environmentId === environments.id && ak.status =!= ApiKeyStatus.DEPRECATED)
        ah <- TblAccountHierarchy.filter(_.accountId === account.id)
      } yield (account, environments, apikeys, ah)
      dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblAccountHierarchy), DBActions.Select, "getApiKeysForSubAccount")
    }
  }

  def getApiKeysForSubAccount(accountId: Long, userId: Long): Future[Seq[(DtoAccount, DtoEnvironment, DtoApiKey, DtoAccountHierarchy)]] = {
    val query0 = for {
      th0 <- TblAccountHierarchy.filter(_.accountId === accountId).result.headOption
      th <- TblAccountHierarchy.filter(a => a.hierarchyPath.like(th0.map(_.hierarchyPath.trim + "%").getOrElse("")) && a.hierarchyStatus === Status.ACTIVE.id).result
    } yield th
    dbProxy.run(query0, DBTables.TblAccountHierarchy, DBActions.Select, "getApiKeysForSubAccount") flatMap { ah: Seq[DtoAccountHierarchy] =>
      val accountIds = ah.map(_.accountId)
      val query = for {
        account <- TblAccounts.filter(_.id inSet accountIds)
        environments <- TblEnvironment.filter(_.accountId === account.id)
        apikeys <- TblApiKey.filter(ak => ak.environmentId === environments.id && ak.status =!= ApiKeyStatus.DEPRECATED)
        ah <- TblAccountHierarchy.filter(_.accountId === account.id)
        _ <- TblUserAccountAssociation.filter(u => u.accountId === ah.accountId && u.businessUserId === userId && u.associationType === UserAccountAssociationType.DIRECT.id)
      } yield (account, environments, apikeys, ah)
      dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblAccountHierarchy, DBTables.TblUserAccountAssociation), DBActions.Select, "getApiKeysForSubAccount_0")
    }
  }

  def getMigratedAccounts(): Future[Seq[(DtoAccountHierarchy, DtoAccount)]] = {
    val query = for {
      ah <- TblAccountHierarchy.filter(f => f.hierarchyStatus === Status.ACTIVE.id && f.accountType =!= AccountTypes.SUB_ACCOUNT.id)
      acc <- TblAccounts.filter(_.id === ah.accountId)
    } yield (ah, acc)
    dbProxy.run(query.result, Set(DBTables.TblAccountHierarchy, DBTables.TblAccount), DBActions.Select, "getMigratedAccounts")
  }

  def getEnvironments(accountId: Long, envType: Seq[Int]): Future[Seq[DtoEnvironment]] = {
    dbProxy.run(TblEnvironment.filter(e => e.accountId === accountId && e.environmentType.inSet(envType.map(_.toLong))).result,
      DBTables.TblEnvironment,
      DBActions.Select,
      "getEnvironments")
  }

  def getEnvironmentRolesV1(userId: Long, environmentId: Long): Future[Seq[Int]] = {
    val query = TblBusinessUserEnvironmentRole.filter(buer => buer.businessUserId === userId && buer.environmentId === environmentId).map(_.role)
    dbProxy.run(query.result,
      DBTables.TblBusinessUserEnvironmentRole,
      DBActions.Select,
      "getEnvironmentRolesV1")
  }

  def isV1PrimaryUser(userId: Long): Future[Option[Boolean]] = {
    val query = TblBusinessUser.filter(_.id === userId).map(_.isPrimaryUser).result.headOption
    dbProxy.run(query,
      DBTables.TblBusinessUser,
      DBActions.Select,
      "isV1PrimaryUser")
  }

  def isParentAccountEnvironment(userId: Long, environmentId: Long): Future[Boolean] = {
    val query = for {
      accountId <- TblBusinessUser.filter(_.id === userId).map(_.accountId).result.headOption
      environmentIds <- TblEnvironment.filter(_.accountId inSet accountId).map(_.id).result
    } yield environmentIds.contains(environmentId)
    dbProxy.run(query,
      Set(DBTables.TblBusinessUser, DBTables.TblEnvironment),
      DBActions.Select,
      "isParentAccountEnvironment")
  }

  def getSubAccountEnvironments(userId: Long, environmentId: Long): Future[Seq[DtoEnvironment]] = {
    val query = for {
      accountId <- TblBusinessUser.filter(_.id === userId).map(_.accountId).result.headOption
      subAccountIds <- TblAccounts.filter(account => account.parentId === accountId).map(_.id).result
      subAccountEnvironments <- TblEnvironment.filter(_.accountId inSet subAccountIds).result
    } yield subAccountEnvironments
    dbProxy.run(query,
      Set(DBTables.TblBusinessUser, DBTables.TblAccount, DBTables.TblEnvironment),
      DBActions.Select,
      "getSubAccountEnvironments")
  }

  def getEnvironmentType(environmentId: Long): Future[Option[Long]] = {
    val query = TblEnvironment.filter(_.id === environmentId).map(_.environmentType).result.headOption
    dbProxy.run(query,
      DBTables.TblEnvironment,
      DBActions.Select,
      "getEnvironmentType")
  }

  def getEnvironmentByUserId(userId: Long, environmentType: Long): Future[Option[DtoEnvironment]] = {
    val query = for {
      accountIdOpt <- TblBusinessUser.filter(_.id === userId).map(_.accountId).result.headOption
      environmentOpt <- TblEnvironment.filter(dtoEnvironment => dtoEnvironment.environmentType === environmentType && dtoEnvironment.accountId === accountIdOpt.getOrElse(-1L)).result.headOption
    } yield environmentOpt
    dbProxy.run(query,
      Set(DBTables.TblBusinessUser, DBTables.TblEnvironment),
      DBActions.Select,
      "getEnvironmentByUserId")
  }

  def getAccountPermissions(accountId: Long): Future[Seq[DtoAccountPermission]] = {
    dbProxy.run(TblAccountPermission.filter(ap => ap.accountId === accountId && ap.enabled === true).result,
      DBTables.TblAccountPermission,
      DBActions.Select,
      "getAccountPermissions")
  }

  def getAccountPermissions(accountId: Long, permissions: Set[Int]): Future[Seq[DtoAccountPermission]] = {
    dbProxy.run(TblAccountPermission.filter(ap => ap.accountId === accountId && ap.permission.inSetBind(permissions) && ap.enabled === true).result,
      DBTables.TblAccountPermission,
      DBActions.Select,
      "getAccountPermissions_0")
  }

  def getEnvironmentId(accountId: Long, environmentName: String) : Future[Option[Long]] = {
    val query = for {
      envType <- TblEnvironmentType.filter(_.name === environmentName).map(_.id).result.headOption
      envId <- TblEnvironment.filter(dtoEnvironment => dtoEnvironment.environmentType === envType && dtoEnvironment.accountId === accountId).map(_.id).result.headOption
    } yield envId

    dbProxy.run(query,
      Set(DBTables.TblEnvironmentType, DBTables.TblEnvironment),
      DBActions.Select,
      "getEnvironmentId")
  }

  def fetchEnvironmentsByAccounts(accountIds: Set[Long]) = {
    val query = TblEnvironment.filter { env => env.accountId.inSet(accountIds)}.map(_.id)
    dbProxy.run(query.result, DBTables.TblEnvironment, DBActions.Select, "fetchEnvironmentsByAccounts")
  }

  def getUserDetailsV2(userId: Long, creatorAccountId: Long): Future[Option[(String, String, String, String)]] = {
    val query = TblBusinessUser.filter(_.id === userId).map(dtoBusinessUser => (dtoBusinessUser.firstName, dtoBusinessUser.lastName, dtoBusinessUser.email, dtoBusinessUser.contactNumber)).result.headOption
    dbProxy.run(query,
      DBTables.TblBusinessUser,
      DBActions.Select,
      "getUserDetailsV2")
  }

  def getAccountIdVsAssociationId(userId: Long, creatorAccountId: Long): Future[Map[Long, Long]] = {
    val query = TblUserAccountAssociation.filter(uaa => uaa.businessUserId === userId && uaa.status === UserAccountAssociationStatuses.ACTIVE.id && uaa.associationType === UserAccountAssociationType.DIRECT.id) join TblAccountHierarchy on (_.accountId === _.accountId) filter(res => (res._2.hierarchyPath.like(s"$creatorAccountId/%") || res._2.hierarchyPath.like(s"%/$creatorAccountId/%")) && res._2.hierarchyStatus === Status.ACTIVE.id) join TblAccounts on (_._2.accountId === _.id) filter (res => res._2.isActive && !res._2.isDeleted) map(res => (res._1._2.accountId, res._1._1.id))
    dbProxy.run(query.result,
      Set(DBTables.TblUserAccountAssociation, DBTables.TblAccountHierarchy),
      DBActions.Select,
      "getAccountIdVsAssociationId")
      .map(_.toMap)
  }

  def getAssociationIdVsRoles(userAccountAssociationIds: Set[Long]): Future[Map[Long, Seq[(Int, Option[Long])]]]= {
    val query = TblUserAccountRoleAssociation.filter(_.userAccountAssociationId inSet userAccountAssociationIds).map(res => (res.userAccountAssociationId, res.userRoleId, res.roleType))
    dbProxy.run(query.result,
      DBTables.TblUserAccountAssociation,
      DBActions.Select,
      "getAssociationIdVsRoles")
      .map(res => res.groupBy(_._1).map(associationIdRoles => associationIdRoles._1 -> associationIdRoles._2.map(roles => (roles._3, roles._2))))
  }

  def getAccountIdVsName(accountIds: Set[Long]): Future[Map[Long, String]] = {
    val query = TblAccounts.filter(_.id inSet accountIds).map(res => (res.id, res.name))
    dbProxy.run(query.result,
      DBTables.TblAccount,
      DBActions.Select,
      "getAccountIdVsName")
      .map(_.toMap)
  }

  def getDefaultModules(accountId: Long): Future[Option[DtoDefaultModules]] = {
    dbProxy.run(TblDefaultModules.filter(_.accountId === accountId).result.headOption,
      DBTables.TblDefaultModules,
      DBActions.Select,
      "getDefaultModules")
  }

  def getDefaultModulesForAccounts(accountIds: Seq[Long]): Future[Seq[DtoDefaultModules]] = {
    dbProxy.run(TblDefaultModules.filter(_.accountId inSet accountIds).result,
      DBTables.TblDefaultModules,
      DBActions.Select,
      "getDefaultModulesForAccounts")
  }

  def getDefaultModulesWithCount(accountId: Long): Future[(Option[DtoDefaultModules], Int)] = {
    val query = for {
      response <- TblDefaultModules.filter(_.accountId === accountId).result.headOption
      inheritedToSubAccountsCount <- TblDefaultModules.filter(_.byAccountId === accountId).length.result
    } yield (response, inheritedToSubAccountsCount)
    dbProxy.run(query,
      DBTables.TblDefaultModules,
      DBActions.Select,
      "getDefaultModules")
  }

  def clearDefaultModules(accountId: Seq[Long]): Future[Int] = {
    val query = for {
      filteredByAccountId <- TblDefaultModules.filter(_.accountId inSet(accountId)).map(_.id).result
      response <- TblDefaultModules.filter(_.id.inSet(filteredByAccountId)).delete
    } yield (response)
    dbProxy.run(query,
      DBTables.TblDefaultModules,
      DBActions.Delete,
      "clearDefaultModules")
  }

  def insertDefaultModules(dtoDefaultModules: DtoDefaultModules): Future[Int] = {
    dbProxy.run(TblDefaultModules += dtoDefaultModules,
      DBTables.TblDefaultModules,
      DBActions.Insert,
      "insertDefaultModules")
  }

  def updateDefaultModules(accountId: Long, modules: String, clock: Clock): Future[Int] = {
    dbProxy.run(TblDefaultModules.filter(_.accountId === accountId).map(dm => (dm.modules, dm.lastUpdatedAt)).update(modules, clock.now),
      DBTables.TblDefaultModules,
      DBActions.Update,
      "updateDefaultModules")
  }

  def insertApiAudit(dtoApiAudit: DtoApiAudit): Future[Int] = {
    dbProxy.run(TblApiAudit += dtoApiAudit,
      DBTables.TblApiAudit,
      DBActions.Insert,
      "insertApiAudit")
  }

  def insertDefaultModulesForAccounts(defaultAccountsModules: DefaultAccountsModules): Future[Option[Int]] = {
    val query = for {
      updateAccountIds <- TblDefaultModules.filter(st => st.accountId inSet defaultAccountsModules.accountId).map(r => r.accountId).result
      res <- TblDefaultModules ++= defaultAccountsModules.accountId.filter(accountId => !updateAccountIds.contains(accountId)).map(accountId =>
        DtoDefaultModules(0,
          accountId,
          defaultAccountsModules.modules,
          DateTime.now(),
          DateTime.now(),
          defaultAccountsModules.byAccountId,
          defaultAccountsModules.isForceInherit))
      _ <- TblDefaultModules.filter(st => st.accountId inSet updateAccountIds)
        .map(r => (r.modules, r.lastUpdatedAt, r.byAccountId, r.isForceInherit)).update(defaultAccountsModules.modules,
        DateTime.now(), defaultAccountsModules.byAccountId, defaultAccountsModules.isForceInherit)
    } yield res
    dbProxy.run(query.transactionally, DBTables.TblAccountPermission, DBActions.Update, "saveUIAccountsConfiguration")
  }

  def getParentAccountWithPermissionsByApiKey(apiKey: String): Future[Seq[DtoAccountPermission]] = {
    val query = for{
      ak <- TblApiKey.filter(k => k.apiKey === apiKey && ((k.status === ApiKeyStatus.ACTIVE) || (k.status === ApiKeyStatus.NEW)))
      env <- TblEnvironment.filter(_.id === ak.environmentId)
      account <- TblAccounts.filter(a => a.id === env.accountId && a.parentId.isEmpty && a.isActive === true && a.isDeleted === false)
      permissions <- TblAccountPermission.filter(ap => ap.accountId === account.id && ap.enabled === true)
    } yield permissions

    dbProxy.run(query.result,
      Set(DBTables.TblApiKey, DBTables.TblEnvironment, DBTables.TblAccount, DBTables.TblAccountPermission),
      DBActions.Select,
      "getParentAccountWithPermissionsByApiKey")
  }

  def createBusinessUserWithActivationToken(user: SubAccountUserRequest, accountId: Long): Future[(Long, String)] = {
    val uuid = UUIDUtilities.getRandomUUID
    val query = for {
      userId <- (TblBusinessUser returning TblBusinessUser.map(_.id)) += DtoBusinessUser(
        id = 0,
        email = user.email,
        firstName = user.firstName.get,
        lastName = user.lastName.get,
        contactNumber = user.contactNumber.get,
        registeredOn = DateTime.now(),
        accountNonLocked = true,
        accountId = accountId,
        isPrimaryUser = false)
      _ <- TblActivationToken  += DtoActivationToken(0, userId, token = Some(uuid), Some(DateTime.now()))
    } yield (userId,uuid)

    dbProxy.run(query.transactionally,
      Set(DBTables.TblActivationToken, DBTables.TblBusinessUser),
      DBActions.Insert,
      "createBusinessUserWithActivationToken")
  }

  def getPrimaryUsersOfAnAccount(accountId: Long): Future[Seq[Long]] = {
    val query = for{
      uaa <- TblUserAccountAssociation.filter(a => a.accountId === accountId && a.isPrimaryUser === true && a.status === UserAccountAssociationStatuses.ACTIVE.id && a.associationType === UserAccountAssociationType.DIRECT.id)
      bu <- TblBusinessUser.filter(_.id === uaa.businessUserId).map(_.id)
    } yield bu
    dbProxy.run(query.result,
      Set(DBTables.TblUserAccountAssociation, DBTables.TblBusinessUser),
      DBActions.Select,
      "getPrimaryUsersOfAnAccount")
  }

  def addPrimaryUserAccountAssociationsWithNewRole(accountId: Long,
                                                   primaryUserId: Long,
                                                   userPermissions: Map[Int, Set[Int]],
                                                   creator: Creator,
                                                   clock: Clock): Future[Boolean] = {

    val query = for {
      userAccountAssociationId <- (TableQuery[TblUserAccountAssociation] returning TableQuery[TblUserAccountAssociation].map(_.id)) += DtoUserAccountAssociation(id = 0, primaryUserId, accountId, UserAccountAssociationStatuses.ACTIVE.id, isPrimaryUser = true, None, None, DateTime.now)
      userRoleId <- (TblUserRole returning TblUserRole.map(_.id)) += DtoUserRole(id = 0, name = s"Role_$userAccountAssociationId", description = None, byBusinessUserId = creator.userId, byAccountId = creator.accountId, updatedAt = DateTime.now)
      _ <- TblUserAccountRoleAssociation += DtoUserAccountRoleAssociation(0, userAccountAssociationId, Option(userRoleId))
      permissionTemplateId <- {
        val dtoPermissionTemplate = DtoPermissionTemplate(0, s"Template_$primaryUserId", TemplateTypes.DEFAULT.id, accountId, userAccountAssociationId, DateTime.now)
        (TblPermissionTemplate returning TblPermissionTemplate.map(_.id)) += dtoPermissionTemplate
      }
      _ <- {
        val dtoPermissionTemplateMappings = userPermissions.map(p => DtoPermissionTemplateMapping(0, permissionTemplateId, p._1, p._2.mkString(","), None))
        TblPermissionTemplateMapping ++= dtoPermissionTemplateMappings
      }
      _ <- {
        TblRolePermissionTemplateAssociation += DtoRolePermissionTemplateAssociation(0, permissionTemplateId, userRoleId)
      }
    } yield true

    dbProxy.run(query.transactionally,
      Set(DBTables.TblUserAccountAssociation, DBTables.TblUserRole, DBTables.TblUserAccountRoleAssociation, DBTables.TblPermissionTemplate,
        DBTables.TblPermissionTemplateMapping, DBTables.TblRolePermissionTemplateAssociation),
      DBActions.Insert,
      "addPrimaryUserAccountAssociationsWithNewRole")
  }

  def doesAccountNameExist (name: String): Future[Boolean] = {
    val query = TblAccounts.filter(a => a.name === name && a.isDeleted === false).exists
    dbProxy.run(query.result,
      DBTables.TblAccount,
      DBActions.Select,
      "doesAccountNameExist")
  }

  def getBySector(sector : String): Future[Option[DtoIndustry]] = {
    val query = TblIndustries.filter(_.sector === sector).result
    dbProxy.run(query.headOption,
      DBTables.TblIndustry,
      DBActions.Select,
      "getBySector")
  }

  def getAccountByEnvironmentId(environmentId: Long): Future[Option[DtoAccount]] = {
    val query = for {
      accountId <- TblEnvironment.filter(_.id === environmentId).map(_.accountId).result.headOption
      account <- TblAccounts.filter(_.id === accountId).result.headOption
    } yield account
    dbProxy.run(query,
      Set(DBTables.TblEnvironment, DBTables.TblAccount),
      DBActions.Select,
      "getAccountByEnvironmentId")
  }

  def updateEmailAndUserAccountAssociationStatus(email: String, newEmail: String, businessUserId: Long, associatedAccounts: Set[Long]): Future[Int] = {
    val value = for {
      bu <- TblBusinessUser.filter(_.email === email).map(_.email).update(newEmail)
      _ <- TblUserAccountAssociation.filter(f => f.businessUserId === businessUserId && f.accountId.inSet(associatedAccounts) && f.associationType === UserAccountAssociationType.DIRECT.id).map(_.status).update(UserAccountAssociationStatuses.DELETED.id)
    } yield bu
    dbProxy.run(value.transactionally, Set(DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation), DBActions.Update, "updateEmailAndUserAccountAssociationStatus")
  }

  def swapUserRoles(userAccountAssociationId: Long, swappingUserAccountAssociationId: Long): Future[(Int, Int, Int)] = {
    val query = for {
      uara <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === userAccountAssociationId).result
      swapUara <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === swappingUserAccountAssociationId).result
      deletedEntries <- TblUserAccountRoleAssociation.filter(_.id inSetBind uara.map(_.id) ++ swapUara.map(_.id)).delete
      updatedRolesforUser <- (TblUserAccountRoleAssociation returning TblUserAccountRoleAssociation.map(_.id)) ++= uara.map(u => DtoUserAccountRoleAssociation(0, swappingUserAccountAssociationId, u.userRoleId, u.roleType))
      updatedRolesforSwapUser <- TblUserAccountRoleAssociation returning TblUserAccountRoleAssociation.map(_.id) ++= swapUara.map(u => DtoUserAccountRoleAssociation(0, userAccountAssociationId, u.userRoleId, u.roleType))
    } yield (deletedEntries, updatedRolesforUser.length, updatedRolesforSwapUser.length)
    dbProxy.run(query.transactionally, DBTables.TblUserAccountRoleAssociation, DBActions.Update, "swapUserRoles")
  }

  def getUsersFilteredByPermission(accountIds: Seq[Long], envIds: Option[Seq[Int]], permissions: Seq[Int], permissionCrit: Int): Unit = {
    val usersQuery = for {
      userIds <- TblUserAccountAssociation.filter(a => (a.accountId inSet accountIds) && a.associationType === UserAccountAssociationType.DIRECT.id && (a.status === UserAccountAssociationStatuses.ACTIVE.id || a.status === UserAccountAssociationStatuses.LOCKED.id))
        .joinLeft(TblUserAccountRoleAssociation).on(_.id === _.userAccountAssociationId)
    } yield (userIds)
    dbProxy.run(usersQuery.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount, DBTables.TblBusinessUser), DBActions.Select, "getUsersFilteredByPermission")
  }

  def fetchUsersAndCustomRolePermissions(accountIds: Seq[Long], envIds: Seq[Int]): Future[Seq[DtoUserDetailsByPermission]] = {
    implicit val getAllUserCustomDetails = GetResult(u => DtoUserDetailsByPermission(
      id = u.nextLong(),
      firstName = u.nextString(),
      lastName = u.nextString(),
      email = u.nextString(),
      accountId = u.nextLong(),
      envType = u.nextInt(),
      permissions = Some(u.nextString())
    ))
    val query =
      sql"""SELECT bu.id, bu.first_name, bu.last_name, bu.email, uaa.account_id, ptm.environment_type_id, ptm.permissions
            FROM user_account_association AS uaa
            LEFT JOIN tbl_business_user AS bu ON uaa.business_user_id = bu.id
            LEFT JOIN user_account_role_association AS uara ON uaa.id=uara.user_account_association_id
            LEFT JOIN role_permission_template_association AS rpta ON uara.user_role_id=rpta.user_role_id
            LEFT JOIN permission_template AS pt ON rpta.permission_template_id=pt.id
            LEFT JOIN permission_template_mapping AS ptm ON ptm.permission_template_id = pt.id
            WHERE uaa.account_id IN (#${accountIds.mkString(",")}) AND ptm.environment_type_id IN (#${envIds.mkString(",")}) AND uaa.status = ${UserAccountAssociationStatuses.ACTIVE.id} AND uara.role_type = ${SystemDefinedRoles.CUSTOMROLE.roleType}
        """.as[DtoUserDetailsByPermission]

    dbProxy.run(query,
      Set(DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation, DBTables.TblRolePermissionTemplateAssociation, DBTables.TblPermissionTemplate, DBTables.TblPermissionTemplateMapping),
      DBActions.Select, "fetchUsersAndPermissions").map(_.toSeq)

  }

  def fetchUsersAndSystemRolesPermissions(accountIds: Seq[Long]): Future[Seq[DtoUserDetailsSystemPermission]] = {
    implicit val getAllUserSystemRoleDetails = GetResult{u =>
      DtoUserDetailsSystemPermission(
        id = u.nextLong(),
        firstName = u.nextString(),
        lastName = u.nextString(),
        email = u.nextString(),
        accountId = u.nextLong(),
        roleType = u.nextInt()
      )
    }
    val query =
      sql"""SELECT bu.id, bu.first_name, bu.last_name, bu.email, uaa.account_id, uara.role_type
            FROM user_account_association AS uaa
            LEFT JOIN tbl_business_user AS bu ON uaa.business_user_id = bu.id
            LEFT JOIN user_account_role_association AS uara ON uaa.id=uara.user_account_association_id
            WHERE uaa.account_id IN (#${accountIds.mkString(",")}) AND uaa.status = ${UserAccountAssociationStatuses.ACTIVE.id} AND uara.role_type != ${SystemDefinedRoles.CUSTOMROLE.roleType}
         """.as[DtoUserDetailsSystemPermission]

    dbProxy.run(query, Set(DBTables.TblBusinessUser, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "fetchUsersAndSystemRolesPermissions")
      .map(_.toSeq)
  }

  def getSystemRoleMaxCount(roleType: Int): Future[Option[DtoSystemRolesMaxCount]] = {
    val query = TblSystemRolesMaxCount.filter(_.roleType === roleType).result.headOption
    dbProxy.run(query, DBTables.TblSystemRolesMaxCount, DBActions.Select, "getSystemRolesMaxCount")
  }

  def getAccountUsersWithSystemRole(accountId: Long, roleType: Int): Future[Int] = {
    val query = for {
      userAccountAssociations <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.associationType === UserAccountAssociationType.DIRECT.id).map(_.id).result
      usersWithRole <- TblUserAccountRoleAssociation.filter(uara => uara.roleType === roleType && uara.userAccountAssociationId.inSet(userAccountAssociations)).result
    } yield usersWithRole.length
    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Update, "getAccountUsersWithSystemRole")
  }

  def isAccountOwner(userId: Long, accountId: Long): Future[Boolean] = {
    val query = for {
      uaa <- TblUserAccountAssociation.filter(uaa => uaa.accountId === accountId && uaa.businessUserId === userId && uaa.associationType === UserAccountAssociationType.DIRECT.id)
      uar <- TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === uaa.id && uar.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType).map(_.id)
    } yield uar
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "isAccountOwner")
      .map(_.nonEmpty)
  }

  def isAccountOwner(userAccAssociationId: Long): Future[Boolean] = {
    val query = TblUserAccountRoleAssociation.filter(uar => uar.userAccountAssociationId === userAccAssociationId && uar.roleType === SystemDefinedRoles.ACCOUNTOWNER.roleType).map(_.id)
    dbProxy.run(query.result, Set(DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "isAccountOwner")
      .map(_.nonEmpty)
  }

  def deleteUserRoleWithPermissionTemplate(roleId: Long, permissionTemplateId: Long, isPermissionTemplateUnusedInOtherRoles: Boolean): Future[Int] = {
    val query = for{
      roleResp <- TblUserRole.filter(_.id === roleId).delete
      permTemplateResp <- {
        if(isPermissionTemplateUnusedInOtherRoles && permissionTemplateId > 0){
          TblPermissionTemplate.filter(_.id === permissionTemplateId).delete
        } else {
          DBIO.successful(0)
        }
      }
    } yield roleResp+permTemplateResp
    dbProxy.run(query.transactionally, Set(DBTables.TblUserRole, DBTables.TblPermissionTemplate), DBActions.Delete, "deleteUserRoleWithPermissionTemplate")
  }

  def isUserRoleUnassociated(roleId: Long): Future[Boolean] = {
    val query = TblUserAccountRoleAssociation.filter(_.userRoleId === roleId).exists.result
    dbProxy.run(query, DBTables.TblUserAccountRoleAssociation, DBActions.Select, "isUserRoleUnassociated")
  }

  def getRoleMappedPermissionTemplateIds(roleId: Long): Future[Seq[Long]] = {
    val query = TblRolePermissionTemplateAssociation.filter(_.userRoleId === roleId).map(_.permissionTemplateId).result
    dbProxy.run(query, DBTables.TblRolePermissionTemplateAssociation, DBActions.Select, "getRoleMappedPermissionTemplateIds")
  }

  def getAccountHierarchyPaths(accountIds: Set[Long]): Future[Seq[DtoAccountHierarchy]] = {
    val query = TblAccountHierarchy.filter(_.accountId inSet accountIds).result
    dbProxy.run(query, DBTables.TblAccountHierarchy, DBActions.Select, "getAccountHierarchyPaths")
  }

  def getAccountsByName(accountNames: Seq[String], parentAccountId : Long): Future[Seq[AccountIdName]] = {
    val query = for {
      ah <- TblAccountHierarchy.filter(_.accountId === parentAccountId).result.head
      subAccountsInHierarchy <- TblAccountHierarchy.filter(_.hierarchyPath.like(s"${ah.hierarchyPath}%")).result
      subAccounts <- (TblAccounts.filter(a => a.name.inSet(accountNames)  && !a.isDeleted  && a.id.inSet(subAccountsInHierarchy.map(_.accountId)))).result
    } yield (subAccounts.map(acc => AccountIdName(acc.accountId, acc.name)))
    dbProxy.run(query.transactionally,Set(DBTables.TblAccount, DBTables.TblAccountHierarchy), DBActions.Select, "getAccountsByName")
  }

  def getUsersAndRolesTotalRecordCount(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]]): Future[Int] = {
    val query =
      TblAccounts.filter(f => f.id.inSet(accountIds) && !f.isDeleted)
        .join(TblUserAccountAssociation.filter(f => f.associationType === UserAccountAssociationType.DIRECT.id && !f.status.inSet(excludeUserStatuses.getOrElse(Set.empty[Int])))).on(_.id === _.accountId)
        .joinLeft(TblUserAccountRoleAssociation).on(_._2.id === _.userAccountAssociationId)
        .map { case ((_, uaa), _) => uaa.id }
        .distinct
        .length
    dbProxy.run(query.result, Set(DBTables.TblAccount, DBTables.TblUserAccountAssociation, DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getUsersAndRolesTotalRecordCount")
  }

  private def getSystemDefinedRolesCases: String = {
    val roleCaseBuilder = new StringBuilder()
    SystemDefinedRoles.values.filterNot(role => role.roleType == 0).map(role => roleCaseBuilder.append(s" WHEN ${role.roleType} THEN '${role.name}' "))
    roleCaseBuilder.toString()
  }

  def getUsersAndRoles(accountIds: Set[Long], excludeUserStatuses: Option[Set[Int]], start: Option[Int], size: Option[Int]): Future[Seq[UsersAndRoles]] = {
    implicit val getAllUsersAndRoles = GetResult { u =>
      UsersAndRoles(
        accountId = u.nextLong(),
        userId = u.nextLong(),
        userAssociationId = u.nextLong(),
        accountName = u.nextString(),
        firstName = u.nextString(),
        lastName = u.nextString(),
        userEmail = u.nextString(),
        contactNumber = u.nextString(),
        userRoles = u.nextString(),
        userAssociationStatus = u.nextInt()
      )
    }
    val query = sql"""
     SELECT uaa.account_id, uaa.business_user_id, uara.user_account_association_id, ta.name,
      tbu.first_name, tbu.last_name, tbu.email, tbu.contact_number,
      GROUP_CONCAT(
        CASE
              WHEN uara.role_type = 0 THEN ur.name
              ELSE
                CASE uara.role_type
                  #${getSystemDefinedRolesCases}
                  ELSE uara.role_type
                END
          END
          SEPARATOR ', '
      ) as role_name,
      uaa.status
     FROM tbl_account ta
     JOIN user_account_association uaa ON uaa.account_id = ta.id
     LEFT JOIN user_account_role_association uara ON uara.user_account_association_id = uaa.id
     JOIN tbl_business_user tbu ON tbu.id = uaa.business_user_id
     LEFT JOIN user_role ur ON ur.id = uara.user_role_id
     WHERE ta.id IN (#${accountIds.mkString(",")}) AND uaa.association_type = ${UserAccountAssociationType.DIRECT.id} #${if(excludeUserStatuses.isDefined) s"AND uaa.status NOT IN (${excludeUserStatuses.get.mkString(",")})" else ""} AND ta.is_deleted = false
     GROUP BY uaa.account_id, uaa.business_user_id, uara.user_account_association_id, ta.name, tbu.email
     ORDER BY uaa.account_id, uaa.business_user_id
     #${if(start.isDefined && size.isDefined) s" LIMIT ${size.get} OFFSET ${start.get} " else ""}
     """.as[UsersAndRoles]

    dbProxy.run(query, Set(DBTables.TblUserAccountAssociation, DBTables.TblAccount, DBTables.TblBusinessUser, DBTables.TblUserAccountRoleAssociation, DBTables.TblUserRole), DBActions.Select, "getUsersAndRoles")
  }


  def getProspectDetails(accountId: Long):Future[(Set[Long], Set[Long], Set[Int])] = {
    val query = for{
      subAccountIds <- TblAccountHierarchy.filter(a => a.hierarchyPath.like(s"$accountId/%")).map(_.accountId).result.map(_.toSet)
      associationIds <- TblUserAccountAssociation.filter(_.accountId === accountId).map(_.id).result.map(_.toSet)
      associatedRoles <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === associationIds.head).map(_.roleType).result.map(_.toSet)
    } yield (subAccountIds,associationIds,associatedRoles)
    dbProxy.run(query.transactionally, Set(DBTables.TblAccountHierarchy, DBTables.TblUserAccountAssociation,DBTables.TblUserAccountRoleAssociation), DBActions.Select, "getProspectAccountDetails")
  }

  def upgradeProspectToDirectCustomer(accountId: Long,roleType: Int, role: Option[Long], associationId:Long,dtoAccountHierarchy: DtoAccountHierarchy): Future[Boolean] = {
    val result = for {
      ah <- TblAccountHierarchy.insertOrUpdate(dtoAccountHierarchy)
      tuara1 <- TblUserAccountRoleAssociation.filter(_.userAccountAssociationId === associationId).delete
      tuara2 <- TblUserAccountRoleAssociation.insertOrUpdate(DtoUserAccountRoleAssociation(0, associationId, role, roleType))
    } yield ah > 0 && tuara1 > 0 && tuara2 > 0
    dbProxy.run(result.transactionally, DBTables.TblUserAccountRoleAssociation, DBActions.Update, "upgradeProspectToDirectCustomer")
  }
}
