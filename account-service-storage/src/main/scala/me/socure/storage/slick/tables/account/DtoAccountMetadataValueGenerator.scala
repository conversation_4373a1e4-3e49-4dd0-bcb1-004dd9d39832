package me.socure.storage.slick.tables.account

import me.socure.common.clock.Clock
import org.joda.time.DateTime

/**
 * Value generator for DtoAccountMetadata
 */
object DtoAccountMetadataValueGenerator {
  
  /**
   * Create a new DtoAccountMetadata instance
   *
   * @param id The ID (0 for new records)
   * @param accountId The account ID
   * @param childId The child ID
   * @param createdBy The user who created the entry
   * @param updatedBy The user who updated the entry (defaults to createdBy)
   * @param createdAt The creation timestamp (defaults to current time)
   * @param updatedAt The update timestamp (defaults to current time)
   * @return A new DtoAccountMetadata instance
   */
  def create(
    id: Long = 0,
    accountId: Long,
    childId: String,
    createdBy: String,
    updatedBy: String = null,
    createdAt: DateTime = null,
    updatedAt: DateTime = null
  )(implicit clock: Clock): DtoAccountMetadata = {
    val now = clock.now()
    DtoAccountMetadata(
      id = id,
      accountId = accountId,
      childId = childId,
      createdAt = if (createdAt == null) now else createdAt,
      updatedAt = if (updatedAt == null) now else updatedAt,
      createdBy = createdBy,
      updatedBy = if (updatedBy == null) createdBy else updatedBy
    )
  }
}
