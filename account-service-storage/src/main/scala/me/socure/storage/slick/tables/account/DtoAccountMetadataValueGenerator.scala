package me.socure.storage.slick.tables.account

import me.socure.common.clock.Clock
import org.joda.time.DateTime

/**
 * Value generator for DtoAccountMetadata
 */
object DtoAccountMetadataValueGenerator {

  /**
   * Create a new DtoAccountMetadata instance
   *
   * @param accountId The account ID
   * @param childId The child ID
   * @param createdBy The user who created the entry
   * @return A new DtoAccountMetadata instance
   */
  def create(
    accountId: Long,
    childId: String,
    createdBy: String
  )(implicit clock: Clock): DtoAccountMetadata = {
    val now = clock.now()
    DtoAccountMetadata(
      id = 0, // Will be auto-incremented
      accountId = accountId,
      childId = childId,
      createdAt = now,
      updatedAt = now,
      createdBy = createdBy,
      updatedBy = createdBy // Same as createdBy for new records
    )
  }
}
