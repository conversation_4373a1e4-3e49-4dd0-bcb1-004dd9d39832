package me.socure.storage.slick.tables.account

import org.joda.time.DateTime
import slick.driver.JdbcProfile
import com.github.tototoshi.slick.MySQLJodaSupport._

/**
 * DAO trait for the tbl_account_metadata table.
 * This table is used to store account ID and child ID mappings.
 */
trait DaoTblAccountMetadata {

  val profile: JdbcProfile
  import profile.api._

  class TblAccountMetadata(tag: Tag) extends Table[DtoAccountMetadata](tag, "tbl_account_metadata") {

    val id = column[Long]("id", O<PERSON>ey, O.AutoInc)
    val accountId = column[Long]("account_id")
    val childId = column[String]("child_id")
    val createdAt = column[DateTime]("created_at")
    val updatedAt = column[DateTime]("updated_at")
    val createdBy = column[String]("created_by")
    val updatedBy = column[String]("updated_by")

    def * = (id, accountId, childId, createdAt, updatedAt, createdBy, updatedBy) <> (DtoAccountMetadata.tupled, DtoAccountMetadata.unapply)
  }

  lazy val TblAccountMetadata = new TableQuery(tag => new TblAccountMetadata(tag))
}
