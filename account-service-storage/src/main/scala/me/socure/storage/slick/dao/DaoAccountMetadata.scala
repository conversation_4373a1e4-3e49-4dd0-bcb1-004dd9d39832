package me.socure.storage.slick.dao

import me.socure.common.clock.Clock
import me.socure.common.metrics.DBProxyWithMetrics
import me.socure.constants.{DBActions, DBTables}
import me.socure.storage.slick.tables.account.{DaoTblAccountMetadata, DtoAccountMetadata}
import slick.driver.JdbcProfile

import scala.concurrent.{ExecutionContext, Future}

/**
 * DAO implementation for the tbl_account_metadata table.
 * This class provides methods for CRUD operations on the account metadata table.
 */
class DaoAccountMetadata(val dbProxy: DBProxyWithMetrics, val profile: JdbcProfile, clock: Clock)
                        (implicit val ec: ExecutionContext) extends DaoTblAccountMetadata {

  import profile.api._

  /**
   * Create a new account metadata entry
   *
   * @param accountId The account ID
   * @param childId The child ID
   * @param createdBy The user who created the entry
   * @return The created DtoAccountMetadata
   */
  def create(accountId: Long, childId: String, createdBy: String): Future[DtoAccountMetadata] = {
    val now = clock.now()
    val metadata = DtoAccountMetadata(
      id = 0, // Will be auto-incremented
      accountId = accountId,
      childId = childId,
      createdAt = now,
      updatedAt = now,
      createdBy = createdBy,
      updatedBy = createdBy
    )
    
    val query = (TblAccountMetadata returning TblAccountMetadata.map(_.id)) += metadata
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Insert, "createAccountMetadata")
      .map(id => metadata.copy(id = id))
  }

  /**
   * Get account metadata by ID
   *
   * @param id The metadata ID
   * @return Option of DtoAccountMetadata
   */
  def getById(id: Long): Future[Option[DtoAccountMetadata]] = {
    val query = TblAccountMetadata.filter(_.id === id).result.headOption
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Select, "getAccountMetadataById")
  }

  /**
   * Get all metadata entries for an account
   *
   * @param accountId The account ID
   * @return Sequence of DtoAccountMetadata
   */
  def getByAccountId(accountId: Long): Future[Seq[DtoAccountMetadata]] = {
    val query = TblAccountMetadata.filter(_.accountId === accountId).result
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Select, "getAccountMetadataByAccountId")
  }

  /**
   * Get metadata entry by account ID and child ID
   *
   * @param accountId The account ID
   * @param childId The child ID
   * @return Option of DtoAccountMetadata
   */
  def getByAccountIdAndChildId(accountId: Long, childId: String): Future[Option[DtoAccountMetadata]] = {
    val query = TblAccountMetadata.filter(m => m.accountId === accountId && m.childId === childId).result.headOption
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Select, "getAccountMetadataByAccountIdAndChildId")
  }

  /**
   * Update an existing account metadata entry
   *
   * @param id The metadata ID
   * @param childId The new child ID
   * @param updatedBy The user who updated the entry
   * @return The updated DtoAccountMetadata
   */
  def update(id: Long, childId: String, updatedBy: String): Future[Option[DtoAccountMetadata]] = {
    val now = clock.now()
    
    val updateQuery = for {
      metadata <- TblAccountMetadata if metadata.id === id
    } yield (metadata.childId, metadata.updatedAt, metadata.updatedBy)
    
    val updateAction = updateQuery.update((childId, now, updatedBy))
    
    dbProxy.run(updateAction, DBTables.TblAccountMetadata, DBActions.Update, "updateAccountMetadata").flatMap { _ =>
      getById(id)
    }
  }

  /**
   * Delete an account metadata entry by ID
   *
   * @param id The metadata ID
   * @return Number of rows deleted (0 or 1)
   */
  def delete(id: Long): Future[Int] = {
    val query = TblAccountMetadata.filter(_.id === id).delete
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Delete, "deleteAccountMetadata")
  }

  /**
   * Delete all metadata entries for an account
   *
   * @param accountId The account ID
   * @return Number of rows deleted
   */
  def deleteByAccountId(accountId: Long): Future[Int] = {
    val query = TblAccountMetadata.filter(_.accountId === accountId).delete
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Delete, "deleteAccountMetadataByAccountId")
  }
}
