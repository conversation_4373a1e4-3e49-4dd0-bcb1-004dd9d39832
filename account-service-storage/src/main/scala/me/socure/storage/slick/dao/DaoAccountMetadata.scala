package me.socure.storage.slick.dao

import me.socure.common.clock.Clock
import me.socure.storage.slick.tables.account.{DaoTblAccountMetadata, DtoAccountMetadata, DtoAccountMetadataValueGenerator}
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import slick.driver.JdbcProfile

import scala.concurrent.{ExecutionContext, Future}

/**
 * DAO implementation for the tbl_account_metadata table.
 * This class provides methods for CRUD operations on the account metadata table.
 */
class DaoAccountMetadata(val dbProxy: DBProxyWithMetrics, val profile: JdbcProfile, val clock: Clock)
                        (implicit val ec: ExecutionContext) extends DaoTblAccountMetadata {

  import profile.api._

  /**
   * Create a new account metadata entry
   *
   * @param accountId The account ID
   * @param childId The child ID
   * @param createdBy The user who created the entry
   * @return The created DtoAccountMetadata
   */
  def create(accountId: Long, childId: String, createdBy: String): Future[DtoAccountMetadata] = {
    val metadata = DtoAccountMetadataValueGenerator.create(
      accountId = accountId,
      childId = childId,
      createdBy = createdBy
    )(clock)

    val query = (TblAccountMetadata returning TblAccountMetadata.map(_.id)) += metadata
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Insert, "createAccountMetadata")
      .map(id => metadata.copy(id = id))
  }

  /**
   * Get metadata entry for an account
   *
   * @param accountId The account ID
   * @return Option of DtoAccountMetadata
   */
  def getByAccountId(accountId: Long): Future[Option[DtoAccountMetadata]] = {
    val query = TblAccountMetadata.filter(_.accountId === accountId).result.headOption
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Select, "getAccountMetadataByAccountId")
  }

  /**
   * Update an existing account metadata entry
   *
   * @param accountId The account ID
   * @param childId The new child ID
   * @param updatedBy The user who updated the entry
   * @return The updated DtoAccountMetadata
   */
  def update(accountId: Long, childId: String, updatedBy: String): Future[Option[DtoAccountMetadata]] = {
    val now = clock.now()

    val updateAction = for {
      metadata <- TblAccountMetadata if metadata.accountId === accountId
    } yield metadata

    val updateQuery = updateAction.map(m => (m.childId, m.updatedAt, m.updatedBy))
    val updateResult = updateQuery.update((childId, now, updatedBy))

    dbProxy.run(updateResult, DBTables.TblAccountMetadata, DBActions.Update, "updateAccountMetadata").flatMap { _ =>
      getByAccountId(accountId)
    }
  }

  /**
   * Delete metadata entry for an account
   *
   * @param accountId The account ID
   * @return Number of rows deleted (0 or 1)
   */
  def delete(accountId: Long): Future[Int] = {
    val query = TblAccountMetadata.filter(_.accountId === accountId).delete
    dbProxy.run(query, DBTables.TblAccountMetadata, DBActions.Delete, "deleteAccountMetadata")
  }
}
