package me.socure

import com.github.tototoshi.slick.MySQLJodaSupport._
import me.socure.account.service.common.subscription.SubscriptionChannelRegistryStatus
import me.socure.account.service.common.{AccountSearchRequest, AccountSearchResponse}
import me.socure.account.utils.UUIDUtilities
import me.socure.common.clock.Clock
import me.socure.common.slick.Extensions._
import me.socure.configuration.AnalyticsConfig
import me.socure.constants.EnvironmentConstants._
import me.socure.constants.attributes.AccountAttributeValue.AccountAttributeValue
import me.socure.constants.{AccountManagementDefaults, EnvironmentConstants}
import me.socure.model.BusinessUserRoles
import me.socure.model.account._
import me.socure.model.analytics.AnalyticsGlobalInfoResponse
import me.socure.model.dashboardv2.{AccountV2, EnvironmentNameAndId}
import me.socure.storage.slick.tables.DaoTblAnalyticsGlobal
import me.socure.storage.slick.tables.account._
import me.socure.storage.slick.tables.account.sponsorbank.DaoTblSponsorBankProgram
import me.socure.storage.slick.tables.accountcache._
import me.socure.storage.slick.tables.accountsocialkeys.{DaoTblEnvironmentSocialKeys, DtoEnvironmentSocialKeys}
import me.socure.storage.slick.tables.industry.{DaoTblIndustry, DtoIndustry}
import me.socure.storage.slick.tables.subscription.DaoTblSubscriptionChannelRegistry
import me.socure.storage.slick.tables.user.{DaoTblBusinessUser, DtoBusinessUser}
import me.socure.utils.DBMetricsUtility.withMetrics
import me.socure.utils.{DBActions, DBProxyWithMetrics, DBTables}
import org.joda.time.DateTime
import slick.driver.JdbcProfile
import slick.jdbc.GetResult

import scala.concurrent.{ExecutionContext, Future}

/**
 * Created by gopal on 02/05/16.
 */
class DaoAccount(val dbProxyWithMetrics: DBProxyWithMetrics, val profile: JdbcProfile, analyticsConfig: Option[AnalyticsConfig] = None)(implicit val ec: ExecutionContext)
  extends DaoTblAccount
    with DaoEnvironmentCache
    with DaoEnvironmentCacheIndividual
    with DaoTblEnvironmentSocialKeys
    with DaoTblEnvironment
    with DaoTblBusinessUser
    with DaoTblAccountPermission
    with DaoTblAccountAttribute
    with DaoTblApiKey
    with DaoTblIndustry
    with DaoTblPublicApiKey
    with DaoTblAccountConsentReason
    with DaoTblSubscriptionStatus
    with DaoTblSubscriptionChannelRegistry
    with DaoTblSubscriptionType
    with DaoTblSponsorBankProgram
    with DaoTblAccountAnalytics
    with DaoTblAccountHierarchy
    with DaoTblTermsOfService
    with DaoTblProduct
    with DaoTblAccountMetadata
    with DaoTblAnalyticsGlobal {

  import DaoAccount._
  import profile.api._

  private val ActiveApiKeyStatuses = Set(ApiKeyStatus.ACTIVE, ApiKeyStatus.NEW)

  def getNonFunctionalBusinessUsers(publicAccountId: String): Future[Seq[DtoBusinessUser]] = {
    val query = for {
      acc <- TblAccounts.filter(_.publicId === publicAccountId)
      bu <- TblBusinessUser.filter(u => u.accountId === acc.id && u.email.startsWith(AccountManagementDefaults.PrefixForNonFunctionalEmail))
    } yield bu
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblBusinessUser), DBActions.Select, "getNonFunctionalBusinessUsers")
  }

  def fetchPublicIdByAccountId(accountId: Long): Future[Option[String]] = {
    val query = TblAccounts.filter(_.id === accountId).map(_.publicId)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblAccount, DBActions.Select, "fetchPublicIdByAccountId")
  }

  // Account Consent reason
  def getConsentId(accountId: Long): Future[Option[DtoAccountConsentReason]] = {
    val query = TblAccountConsentReasons.filter(a => a.accountId === accountId)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblAccountConsentReasons, DBActions.Select, "getConsentId")
  }

  def updateConsentId(consent: DtoAccountConsentReason): Future[Int] = {
    val query = TblAccountConsentReasons.filter(_.accountId === consent.accountId).map(_.consentId).update(consent.consentId)
    dbProxyWithMetrics.run(query, DBTables.TblAccountConsentReasons, DBActions.Update, "updateConsentId")
  }


  //Delete Account
  def deleteAccount(accountId: Long) = {
    val query = TblAccounts.filter(_.id === accountId).map(_.isDeleted).update(true)
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Update, "deleteAccount")
  }

  def getAllDeletedAccounts = {
    implicit val convertResult = GetResult(r => DeletedAccountDetails(
      accountId = r.nextLong(),
      name = r.nextString(),
      isInternal = r.nextBoolean(),
      apiKey = r.nextString(),
      parentName = r.nextStringOption()
    ))

    val query =
      sql"""
           SELECT a.id, a.name, a.is_internal, ak.api_key, (SELECT name FROM tbl_account WHERE id = a.parent_id)
           FROM tbl_account a
           JOIN tbl_environment e ON a.id = e.account_id
           JOIN tbl_api_key ak ON e.id = ak.environment_id
           WHERE a.is_deleted = TRUE
           AND e.environment_type_id = ${EnvironmentConstants.PRODUCTION_ENVIRONMENT.id}
           AND ak.status = 'active'
         """.as[DeletedAccountDetails]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getAllDeletedAccounts")
  }

  //Mark Internal
  def markInternal(accountIds: List[Long], status: Boolean): Future[Int] = {
    val query = TblAccounts.filter(_.id inSet accountIds).map(_.isInternal).update(status)
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Update, "markInternal")
  }

  def deactivateAccounts(accountIds: List[Long]): Future[Int] = {
    val query = TblAccounts.filter(_.id inSet accountIds).map(_.isActive).update(false)
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Update, "deactivateAccounts")
  }

  def doesAccountNameExist(name: String) = {
    val query = TblAccounts.filter(a => a.name === name && a.isDeleted === false).exists
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "doesAccountNameExist")
  }

  // Get all account info for provider account Id.
  def getAccount(accountId: Long): Future[Option[DtoAccount]] = {
    val query = TblAccounts.filter(_.id === accountId).result
    dbProxyWithMetrics.runWithWriter(query.headOption, DBTables.TblAccount, DBActions.Select, "getAccount")
  }

  def getAccounts(accountIds: Set[Long]): Future[Seq[DtoAccount]] = {
    val query = TblAccounts.filter(f => f.id.inSetBind(accountIds)).result
    dbProxyWithMetrics.runWithWriter(query, DBTables.TblAccount, DBActions.Select, "getAccounts")
  }

  def getActiveAccountInfo(accountId: Long): Future[Option[DtoAccount]] = {
    val query = TblAccounts.filter { a => a.id === accountId && a.isActive && !a.isDeleted }.result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblAccount, DBActions.Select, "getActiveAccountInfo")
  }

  def fetchActiveApikeyByPublicKey(publicKey: String): Future[Option[DtoApiKey]] = {
    val query = for {
      (pub, priv) <- TblPublicApiKey.join(TblApiKey).on(_.environmentId === _.environmentId)
      if pub.apiKey === publicKey && pub.status.inSet(ActiveApiKeyStatuses) && priv.status.inSet(ActiveApiKeyStatuses)
    } yield priv
    dbProxyWithMetrics.run(query.sortBy(_.status.desc).result.headOption, Set(DBTables.TblPublicApiKey, DBTables.TblApiKey), DBActions.Select, "fetchActiveApikeyByPublicKey")
  }

  def fetchAccountInfoAndPermission(publicId: String): Future[Map[DtoAccount, Seq[DtoAccountPermission]]] = {
    val query = TblAccounts.filter(_.publicId === publicId) joinLeft TblAccountPermission on ((ta, tap) => ta.id === tap.accountId && tap.enabled === true)
    val eventualTuples: Future[Seq[(DtoAccount, Option[DtoAccountPermission])]] = dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblAccountPermission),
      DBActions.Select, "fetchAccountInfoAndPermission(for PublicId)")
    eventualTuples.map(_.groupBy(_._1).mapValues(_.flatMap(_._2)))
  }

  def fetchAccountInfoAndPermission(accountId: Long): Future[Map[DtoAccount, Seq[DtoAccountPermission]]] = {
    val query = TblAccounts.filter(_.id === accountId) joinLeft TblAccountPermission on ((ta, tap) => ta.id === tap.accountId && tap.enabled === true)
    val eventualTuples: Future[Seq[(DtoAccount, Option[DtoAccountPermission])]] = dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblAccountPermission),
      DBActions.Select, "fetchAccountInfoAndPermission(for AccountId)")
    eventualTuples.map(_.groupBy(_._1).mapValues(_.flatMap(_._2)))
  }

  def saveAccount(dtoAccount: DtoAccount)  = {
    val insert = (TableQuery[TblAccounts] returning TableQuery[TblAccounts].map(_.id)) += dtoAccount
    val dbFuture = dbProxyWithMetrics.run(insert, DBTables.TblAccount, DBActions.Insert, "saveAccount")
    dbFuture.map(id => dtoAccount.copy(accountId = id))
  }

  def createAccount(dtoAccount: DtoAccount, dtoEnvironmentsTemplate: Seq[DtoEnvironment], dtoApiKeyTemplate: DtoApiKey, dtoPublicApiKeyTemplate: DtoPublicApiKey): Future[(Long, Seq[Long], Seq[Long], Seq[Long])] = {
    val query = for {
      accountId <- (TableQuery[TblAccounts] returning TableQuery[TblAccounts].map(_.id)) += dtoAccount
      savedEnvironmentIds <- {
        val environments = dtoEnvironmentsTemplate.map(env => env.copy(accountId = accountId));
        TblEnvironment returning TblEnvironment.map(_.id) ++= environments
      }
      apiKeysId <- {
        val apiKeys = savedEnvironmentIds.map(environmentId => dtoApiKeyTemplate.copy(environmentId = environmentId, apiKey = UUIDUtilities.getRandomUUID))
        TblApiKey returning TblApiKey.map(_.id) ++= apiKeys
      }
      publicApiKeysId <- {
        val publicApiKeys = savedEnvironmentIds.map(environmentId => dtoPublicApiKeyTemplate.copy(environmentId = environmentId, apiKey = UUIDUtilities.getRandomUUID))
        TblPublicApiKey returning TblPublicApiKey.map(_.id) ++= publicApiKeys
      }
    } yield (accountId, savedEnvironmentIds, apiKeysId, publicApiKeysId)
    dbProxyWithMetrics.run(
      query.transactionally,
      Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblPublicApiKey),
      DBActions.Insert,
      "saveAccount")
  }

  def revertCreatedAccount(accountId: Long, savedEnvironmentIds: Seq[Long], apiKeysId: Seq[Long], publicApiKeysId: Seq[Long]): Future[Boolean] = {
    val query = for {
      _ <- TblPublicApiKey.filter(_.id inSetBind publicApiKeysId).delete
      _ <- TblApiKey.filter(_.id inSetBind apiKeysId).delete
      _ <- TblEnvironment.filter(_.id inSetBind savedEnvironmentIds).delete
      _ <- TblAccounts.filter(_.id === accountId).delete
    } yield true
    dbProxyWithMetrics.run(
      query.transactionally,
      Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblPublicApiKey),
      DBActions.Delete,
      "revertCreatedAccount")
  }

  def getAccountByPublicApiKey(publicApiKey: String): Future[Option[DtoAccount]] = {
    val query = TblAccounts.filter(a => a.publicApiKey === publicApiKey && a.isDeleted === false).result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblAccount, DBActions.Select, "getAccountByPublicApiKey")
  }

  def getAccountDetailsByApiKey(apiKey: String) = {
    val query = TblApiKey.filter(a => {
      a.apiKey === apiKey && a.status != ApiKeyStatus.DEPRECATED
    }).flatMap(e =>
      TblEnvironment.filter(_.id === e.environmentId).flatMap(a =>
        TblAccounts.filter(_.id === a.accountId).flatMap(b =>
          TblBusinessUser.filter(_.accountId === b.id).filter(_.isPrimaryUser === true).map(c =>
            (a.id, c.email, b.name)))))

    val dbFuture = dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblApiKey, DBTables.TblEnvironment, DBTables.TblBusinessUser),
      DBActions.Select, "getAccountDetailsByApiKey")
    dbFuture.map(_.headOption)
  }

  def getAccountCredsByApiKey(apiKey: String) = {
    val query = TblApiKey.filter(a => {
      a.apiKey === apiKey && a.status != ApiKeyStatus.DEPRECATED
    }) join TblEnvironment on (_.environmentId === _.id)
    val dbFuture = dbProxyWithMetrics.run(query.result, Set(DBTables.TblApiKey, DBTables.TblEnvironment), DBActions.Select, "getAccountCredsByApiKey")
    dbFuture.map(_.headOption)
  }


  //TODO: Discuss Clearing ACCOUNT & INDIVIDUAL cache input
  def getAccountCache(envId: Long): Future[Option[DtoEnvironmentCache]] = {
    val query = TblEnvironmentCache.filter(_.environmentId === envId)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblEnvironmentCache, DBActions.Select, "getAccountCache")
  }

  def getEnvironmentCacheIndividual(envId: Long): Future[Seq[DtoEnvironmentCacheInvidiual]] = {
    val query = TblAccountCacheIndividual.filter(_.environmentId === envId)
    dbProxyWithMetrics.run(query.result, DBTables.TblEnvironmentIndividualCache, DBActions.Select, "getEnvironmentCacheIndividual")
  }

  def getSocialAppKeys(envId: Long): Future[Seq[DtoEnvironmentSocialKeys]] = {
    val query = TblEnvironmentSocialKeys.filter(_.environmentId === envId)
    dbProxyWithMetrics.run(query.result, DBTables.TblEnvironmentSocialKeys, DBActions.Select, "getSocialAppKeys")
  }


  /**
   * updates white listed domain of account
   *
   * @param accountId
   * @param domains
   * @return
   */
  def updateDomain(accountId: Long, domains: List[String]) = { //TODO: Domain should be updated based on the environment. At present doing it on default environemnt
    val domain = domains mkString ","
    isActiveParentAccount(accountId) flatMap {
      case true =>
        val query = TblEnvironment.filter(e => e.accountId === accountId && e.environmentType === PRODUCTION_ENVIRONMENT.id.toLong).map(_.domain).update(Some(domain))
        dbProxyWithMetrics.run(query, DBTables.TblEnvironment, DBActions.Update, "updateDomain")
      case _ => Future.successful(0)
    }
  }

  //TODO: Important.... Removing any caches is either based on accountId or enviromentId or both
  def removeEnvironmentCache(envId: Long) = {
    val query = TblEnvironmentCache.filter(_.environmentId === envId).delete
    dbProxyWithMetrics.run(query, DBTables.TblEnvironmentCache, DBActions.Delete, "removeEnvironmentCache")
  }

  def removeAccountInvidiualCache(accountInvidiualCacheId: Long) = {
    val query = TblAccountCacheIndividual.filter(_.id === accountInvidiualCacheId).delete
    dbProxyWithMetrics.run(query, DBTables.TblEnvironmentIndividualCache, DBActions.Delete, "removeAccountInvidiualCache(accountInvidiualCacheId)")
  }

  def removeAccountInvidiualCache(envId: Long, identifier: String) = {
    val query = TblAccountCacheIndividual.filter(ic => ic.environmentId === envId && ic.identifier === identifier)
    dbProxyWithMetrics.run(query.delete, DBTables.TblEnvironmentIndividualCache, DBActions.Delete, "removeAccountInvidiualCache(envId)")
  }

  def removeAppNetworkKeys(socialaccountId: Long) = {
    val query = TblEnvironmentSocialKeys.filter(_.id === socialaccountId).delete
    dbProxyWithMetrics.run(query, DBTables.TblEnvironmentSocialKeys, DBActions.Delete, "removeAppNetworkKeys")
  }

  def removeEnvironmentAppNetworkKeys(envId: Long, network: Int) = {
    val query = TblEnvironmentSocialKeys.filter(sk => sk.network === network && sk.environmentId === envId)
    dbProxyWithMetrics.run(query.delete, DBTables.TblEnvironmentSocialKeys, DBActions.Delete, "removeEnvironmentAppNetworkKeys")
  }

  def upsert(dtoAccountKeys: DtoEnvironmentSocialKeys, accountId: Long): Future[Int] = {
    getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        val query = TblEnvironmentSocialKeys.filter(se => se.environmentId === env.id && se.network === dtoAccountKeys.network).map(_.id).result
        val dbFuture = dbProxyWithMetrics.run(query, DBTables.TblEnvironmentSocialKeys, DBActions.Select, "searchEnvironmentAppNetworkKeys")
        dbFuture flatMap {
          case envId :: _ =>
            val updateQuery = TblEnvironmentSocialKeys.filter(_.id === envId).map(nk => (nk.applicationKey, nk.applicationSecret)).update(dtoAccountKeys.applicationKey, dtoAccountKeys.applicationSecret)
            dbProxyWithMetrics.run(updateQuery, DBTables.TblEnvironmentSocialKeys, DBActions.Update, "updateEnvironmentAppNetworkKey")
          case _ =>
            dbProxyWithMetrics.run(TblEnvironmentSocialKeys.insertOrUpdate(dtoAccountKeys.copy(environmentId = env.id)), DBTables.TblEnvironmentSocialKeys,
              DBActions.Insert, "insertEnvironmentAppNetworkKey")
        }
      case _ => Future.successful(0)
    }
  }

  def upsert(accountCache: DtoEnvironmentCache, accountId: Long): Future[Int] = {
    getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        val query = TblEnvironmentCache.filter(_.environmentId === env.id).map(_.environmentId).result
        val dbFuture = dbProxyWithMetrics.run(query, DBTables.TblEnvironmentCache, DBActions.Select, "searchEnvironmentCache")
        dbFuture flatMap {
          case envId :: _ =>
            val updateQuery = TblEnvironmentCache.filter(_.environmentId === envId).map(_.cacheSkipDate).update(accountCache.cacheskipdate)
            dbProxyWithMetrics.run(updateQuery, DBTables.TblEnvironmentCache, DBActions.Update, "updateEnvironmentCache")
          case _ =>
            dbProxyWithMetrics.run(TblEnvironmentCache.insertOrUpdate(accountCache.copy(environmentId = env.id)), DBTables.TblEnvironmentCache,
              DBActions.Insert, "insertEnvironmentCache")
        }
      case _ => Future.successful(0)
    }
  }

  def upsert(accountCacheIndividual: DtoEnvironmentCacheInvidiual, accountId: Long): Future[Int] = {
    getProductionEnvironmentForAccount(accountId) flatMap {
      case Some(env) =>
        val query = TblAccountCacheIndividual.filter(aci => aci.environmentId === env.id && aci.identifier === accountCacheIndividual.identifier).map(_.environmentId).result
        val dbFuture = dbProxyWithMetrics.run(query, DBTables.TblEnvironmentIndividualCache, DBActions.Select, "searchEnvironmentIndividualCache")
        dbFuture flatMap {
          case envId +: _ =>
            val updateQuery = TblAccountCacheIndividual.filter(_.environmentId === envId).map(_.date).update(accountCacheIndividual.date)
            dbProxyWithMetrics.run(updateQuery, DBTables.TblEnvironmentIndividualCache, DBActions.Update, "updateEnvironmentIndividualCache")
          case _ =>
            dbProxyWithMetrics.run(TblAccountCacheIndividual.insertOrUpdate(accountCacheIndividual.copy(environmentId = env.id)), DBTables.TblEnvironmentIndividualCache,
              DBActions.Insert, "insertEnvironmentIndividualCache")
        }
      case _ => Future.successful(0)
    }
  }

  def getInternalAccountsId = {
    val query = TblAccounts.filter(_.isInternal === true).map(_.id).result
    dbProxyWithMetrics.run(query, DBTables.TblAccount,
      DBActions.Select, "getInternalAccountsId")
  }

  def getParentAccounts = {
    val query = TblAccounts.filter(a => a.parentId.isEmpty && a.isActive === true && a.isDeleted === false).result
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Select, "getParentAccounts")
  }

  def getSubAccountsWithIndustry(parentId: Long) = {
    val query = TblAccounts.filter(a => a.parentId === parentId && a.isDeleted === false) join TblIndustries on (_.industrySector === _.sector)
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblIndustry), DBActions.Select, "getSubAccountsWithIndustry")
  }

  def getSubAccountsByPublicId(publicId: String) = {
    val query = for {
      (t1, t2) <- TblAccounts join TblAccounts on { (t1, t2) => t1.id === t2.id || t1.parentId === t2.id }
      if t2.publicId === publicId
    } yield t1
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getSubAccountsByPublicId")
  }

  def getSubAccountsWithEnvDetails(parentId: Long): Future[Seq[(Long, String, Long)]] = {
    val query = for {
      (acc, env) <- TblAccounts join TblEnvironment on (_.id === _.accountId)
      if acc.parentId === parentId && acc.isDeleted === false
    } yield (acc.id, acc.name, env.id)

    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblEnvironment), DBActions.Select, "getSubAccountsWithEnvDetails")
  }

  def getWithSubAccounts(parentId: Long): Future[Seq[DtoAccount]] = {
    val query = TblAccounts.filter(a => (a.parentId === parentId || a.id === parentId) && a.isActive === true && a.isDeleted === false)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getWithSubAccounts")
  }

  def getAllAccounts: Future[Seq[DtoAccount]] = {
    val query = TblAccounts.filter(_.isDeleted === false)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getAllAccounts")
  }

  def getAllAccountsIds: Future[Seq[Long]] = {
    val query = TblAccounts.filter(_.isDeleted === false).map(_.id)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getAllAccountsIds")
  }

  def getAllAccountNames: Future[Seq[(Long, String)]] = {
    val query = for {
      (a) <- TblAccounts
      if a.isActive === true && a.isDeleted === false
    } yield (a.id, a.name)

    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getAllAccountNames")
  }

  def isActiveParentAccount(accountId: Long): Future[Boolean] = {
    val query = TblAccounts.filter(a => a.id === accountId && a.parentId.isEmpty && a.isActive === true && a.isDeleted === false).exists
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "isActiveParentAccount")
  }

  def isParentAccount(accountId: Long): Future[Boolean] = {
    val query = TblAccounts.filter(a => a.id === accountId && a.parentId.isEmpty && a.isDeleted === false).exists
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "isParentAccount")
  }

  def getParentAccountInfo(accountId: Long): Future[Option[DtoAccount]] = {
    val query = TblAccounts.filter(a => a.id === accountId && a.parentId.isEmpty && a.isActive === true && a.isDeleted === false)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblAccount, DBActions.Select, "getParentAccountInfo")
  }

  def getAllNonInternalActiveAccounts: Future[Seq[Long]] = {
    val query = TblAccounts.filter(a => a.isActive === true && a.isDeleted === false && a.isInternal === false).map(_.id)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getAllNonInternalActiveAccounts")
  }

  def getDomainsListByAccountId(accountId: Long) = {
    val query = TblEnvironment.filter(e => e.accountId === accountId && e.environmentType === PRODUCTION_ENVIRONMENT.id.toLong).map(_.domain).result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblEnvironment, DBActions.Select, "getDomainsListByAccountId")
  }

  def fetchDomainsByAccountId(accountId: Long): Future[Seq[DtoEnvironment]] = {
    val query = TblEnvironment.filter(e => e.accountId === accountId).result
    dbProxyWithMetrics.run(query, DBTables.TblEnvironment, DBActions.Select, "fetchDomainsByAccountId")
  }

  def getProductionEnvironmentForAccount(accountId: Long) = {
    val query = TblEnvironment.filter(e => e.accountId === accountId && e.environmentType === PRODUCTION_ENVIRONMENT.id.toLong).result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblEnvironment, DBActions.Select, "getProductionEnvironmentForAccount")
  }

  def getProductionEnvironmentWithApiKeysForAccount(accountId: Long): Future[Map[DtoEnvironment, Seq[DtoApiKey]]] = {
    val query = TblEnvironment.filter(e => e.accountId === accountId && e.environmentType === PRODUCTION_ENVIRONMENT.id.toLong)
      .joinLeft(TblApiKey.filter(_.status =!= ApiKeyStatus.DEPRECATED)).on(_.id === _.environmentId)

    val dbFuture = dbProxyWithMetrics.run(query.result, Set(DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getProductionEnvironmentWithApiKeysForAccount")
    dbFuture.map(_.groupBy(_._1).mapValues(_.flatMap(_._2)))
  }

  def getAccountPermissions(accountId: Long): Future[Seq[DtoAccountPermission]] = {
    val query = TblAccountPermission.filter(ap => ap.accountId === accountId && ap.enabled === true).result
    dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Select, "getAccountPermissions")
  }

  def getAccountPermissionsWithParentFeatureFlag(accountId: Long): Future[Seq[DtoAccountPermission]] = {
    val query = for {
      permissions <- TblAccountPermission.filter(ap => ap.accountId === accountId && ap.enabled === true).result
      parentAccountId <- TblAccounts.filter(_.id === accountId).map(_.parentId).result
      featureFlags <- TblProduct.filter(permission => permission.businessUserRoleId === BusinessUserRoles.EnforceSegmentation.id).map(_.businessUserRoleId).result
      parentPermission <- TblAccountPermission.filter(ap => ap.accountId === parentAccountId.head.getOrElse(accountId) && ap.enabled === true && ap.permission.inSet(featureFlags)).result
    } yield permissions ++ parentPermission
    dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Select, "getAccountPermissionsWithParentFeatureFlag")
  }

  def getProgramIdNameByUserId(userId: Long): Future[Seq[AccountIdName]] = {
    val query = for {
      accountId <- TblBusinessUser.filter(_.id === userId).map(_.accountId).result.headOption
      programsIds <- TblSponsorBankProgram.filter(sbp => sbp.sponsorBankId === accountId).map(_.programId).result
      accounts <- TblAccounts.filter(account => (account.id inSet programsIds) && account.isActive && !account.isDeleted).result
    } yield accounts
    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblSponsorBankProgram), DBActions.Select, "getProgramIdNameByUserId")
      .map { accounts =>
        accounts map { account =>
          AccountIdName(
            account.accountId,
            account.name
          )
        }
      }
  }

  def getProgramAccountIdName(accountId: Long): Future[Seq[AccountIdName]] = {
    val query = for {
      spbIds <- TblSponsorBankProgram.filter(sbp => sbp.sponsorBankId === accountId || sbp.programId === accountId).map(_.programId).result
      accounts <- TblAccounts.filter(account => (account.id inSet spbIds)).result
    } yield accounts
    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblSponsorBankProgram), DBActions.Select, "getProgramAccountIdName")
      .map { accounts =>
        accounts map { account =>
          AccountIdName(
            account.accountId,
            account.name
          )
        }
      }
  }

  def getSponsorBankIdsByProgramId(programId: Long): Future[Seq[Long]] = {
     val query = TblSponsorBankProgram.filter(sbp => sbp.programId === programId).map(_.sponsorBankId).result
    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblSponsorBankProgram), DBActions.Select, "getSponsorBankIdsByProgramId")
  }

  def getTOSTimeStamp(userId: Long): Future[Option[DateTime]] = {
    val query = TblTermsOfService.filter(tos => tos.businessUserId === userId).map(_.timeStamp).result.headOption
    dbProxyWithMetrics.run(query, Set(DBTables.TblTermsOfService), DBActions.Select, "getTOSTimeStamp")
  }

  def userExistsInTOSTable(userId: Long): Future[Boolean] = {
    val query = TblTermsOfService.filter(_.businessUserId === userId).exists.result
    dbProxyWithMetrics.run(query, DBTables.TblBusinessUser, DBActions.Update, "userExistsInTOsTable")
  }

  def updateTOSAgreement(userId: Long): Future[Int] = {
    val query = for {
      _ <- TblBusinessUser.filter(_.id === userId).map(_.isAgreedTermsOfService).update(true)
      tos <- TblTermsOfService += DtoTermsOfService(None, userId, None, DateTime.now())
    } yield tos
    dbProxyWithMetrics.run(query.transactionally, DBTables.TblBusinessUser, DBActions.Insert, "updateTOSAgreement")
  }

  def toggleAccountActiveStatus(accountId: Long, status: Boolean) = {
    val updateQuery = TblAccounts.filter(_.id === accountId).map(_.isActive).update(status)
    dbProxyWithMetrics.run(updateQuery, DBTables.TblAccount, DBActions.Update, "toggleAccountActiveStatus")
  }

  //Account Attributes Related Quries

  def getAccountAttributes(accountId: Long) = {
    val query = TblAccountAttribute.filter(_.accountId === accountId).result
    dbProxyWithMetrics.run(query, DBTables.TblAccountAttribute, DBActions.Select, "getAccountAttributes")
  }


  //Sub Account Related Quries

  def getSubAccounts(accountId: Long) = {
    implicit val convertResult = GetResult(r => AccountIdName(
      id = r.nextLong(),
      name = r.nextString()
    ))

    val query =
      sql"""
           SELECT a.id, a.name
           FROM tbl_account a
           WHERE a.parent_id = ${accountId} OR a.id=${accountId}
        """.as[AccountIdName]

    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Select, "getSubAccounts")
  }

  //Note : This function return sub-account list of an account and its own account
  def getSubAccountsWithUserCount(accountId: Long) = {
    implicit val convertResult = GetResult(r => AccountV2(
      id = r.nextLong(),
      name = r.nextString(),
      email = r.nextString(),
      parentAccount = r.nextStringOption(),
      industry = r.nextString(),
      status = r.nextBoolean(),
      userCount = r.nextInt()
    ))

    val query =
      sql"""
           SELECT a.id, a.name, u.email,
           (SELECT name FROM tbl_account WHERE id = a.parent_id),
           (SELECT description FROM tbl_industry WHERE sector = a.industry_sector), a.is_active,
           (SELECT COUNT(*) FROM tbl_business_user WHERE account_id = a.id)
           FROM tbl_account a
           JOIN tbl_business_user u ON a.id = u.account_id
           WHERE u.is_primary_user = TRUE
           AND (a.parent_id = ${accountId} OR a.id = ${accountId})
         """.as[AccountV2]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblIndustry, DBTables.TblBusinessUser), DBActions.Select, "getSubAccountsWithUserCount")
  }

  def getAllAccountsDetails(accountId: Long): Future[Vector[AccountIdName]] = {
    implicit val convertResult = GetResult(r => AccountIdName(
      id = r.nextLong(),
      name = r.nextString()
    ))

    val query =
      sql"""SELECT a.id, a.name
            FROM tbl_account a
            WHERE (a.parent_id = ${accountId} OR a.id = ${accountId})
         """.as[AccountIdName]

    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Select, "getAllAccountsDetails")
  }

  def getParentAccountsWithoutPrimaryUsers(): Future[Seq[AccountIdName]] = {
    implicit val convertResult = GetResult(r => AccountIdName(
      id = r.nextLong(),
      name = r.nextString()
    ))

    val query =
      sql"""
           SELECT acc.id, acc.name,
                      GROUP_CONCAT(DISTINCT ap.permission SEPARATOR ',') AS permissions
                      FROM tbl_account acc
                      JOIN tbl_environment env ON acc.id = env.account_id
                      JOIN tbl_environment_type env_type ON env.environment_type_id = env_type.id
                      LEFT JOIN tbl_account_permission ap ON ap.account_id = acc.id AND ap.enabled = true
                      LEFT JOIN
                      (SELECT DISTINCT account_id AS a_id FROM tbl_business_user WHERE is_primary_user = TRUE) prim_acc
                      ON prim_acc.a_id = acc.id
                      WHERE acc.parent_id IS NULL AND
                      env_type.id = ${PRODUCTION_ENVIRONMENT.id} AND
                      acc.is_deleted = FALSE AND
                      prim_acc.a_id IS NULL
                      GROUP BY acc.id, acc.name, env.updated_at
                      HAVING permissions IS NULL OR find_in_set(${BusinessUserRoles.SAML_2_0.id}, permissions) = 0
                      ORDER BY env.updated_at DESC;
        """.as[AccountIdName]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblEnvironmentType, DBTables.TblAccountPermission, DBTables.TblBusinessUser),
      DBActions.Select, "getParentAccountsWithoutPrimaryUsers")
  }

  def getAllEnvironmentWithAccount = {
    implicit val convertResult = GetResult(r => EnvironmentWithAccount(
      accountId = r.nextLong(),
      accountName = r.nextString(),
      email = r.nextString(),
      environmentId = r.nextLong(),
      environmentType = r.nextLong()
    ))

    val query =
      sql"""SELECT a.id, a.name, b.email, e.id, e.environment_type_id
           FROM tbl_account a
           LEFT JOIN tbl_business_user b ON a.id = b.account_id
           LEFT JOIN tbl_environment e ON a.id = e.account_id
           WHERE b.id IS NULL OR b.is_primary_user = TRUE
         """.as[EnvironmentWithAccount]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblBusinessUser), DBActions.Select, "getAllEnvironmentWithAccount")
  }

  def getEnvironmentInfo(email: String) = {
    implicit val convertResult = GetResult(r => EnvironmentNameAndId(
      id = r.nextLong(),
      name = EnvironmentConstants(r.nextInt()).toString
    ))

    val query =
      sql"""SELECT e.id, e.environment_type_id
           FROM tbl_business_user b
           LEFT JOIN tbl_environment e ON b.account_id = e.account_id
           WHERE b.email = ${email}
         """.as[EnvironmentNameAndId]

    dbProxyWithMetrics.run(query, Set(DBTables.TblEnvironment, DBTables.TblBusinessUser), DBActions.Select, "getEnvironmentInfo")
  }

  def getAccountIdByApiKey(apiKey: String): Future[Option[Long]] = {
    dbProxyWithMetrics.run(
      (for {
        (key, env) <- TblApiKey join TblEnvironment on (_.environmentId === _.id)
        if key.apiKey === apiKey
      } yield env.accountId).result.headOption,
      Set(DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getAccountIdByApiKey"
    )
  }

  def getApiKeysForSubAccounts(parentId: Long): Future[Seq[(DtoApiKey, String, DtoEnvironment)]] = {
    dbProxyWithMetrics.run(
      (for {
        ((key, env), a) <- TblApiKey.join(TblEnvironment).on(_.environmentId === _.id).join(TblAccounts).on(_._2.accountId === _.id)
        if a.parentId === parentId && a.isActive === true && a.isDeleted === false && key.status =!= ApiKeyStatus.DEPRECATED
      } yield (key, a.name, env)).result,
      Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getApiKeysForSubAccounts"
    )
  }

  def getApiKeysForAccountAndSubAccounts(accountId: Long): Future[Seq[(DtoApiKey, String, DtoEnvironment)]] = {
    dbProxyWithMetrics.run(
      (for {
        ((key, env), a) <- TblApiKey.join(TblEnvironment).on(_.environmentId === _.id).join(TblAccounts).on(_._2.accountId === _.id)
        if (a.id === accountId || a.parentId === accountId) && a.isActive === true && a.isDeleted === false && key.status =!= ApiKeyStatus.DEPRECATED
      } yield (key, a.name, env)).result,
      Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getApiKeysForAccountAndSubAccounts"
    )
  }

  def getApiKeysForAccountByEnvironmentType(accountId: Long, envTypeId: Long): Future[Seq[(DtoApiKey, String, DtoEnvironment)]] = {
    dbProxyWithMetrics.run(
      (for {
        ((key, env), a) <- TblApiKey.join(TblEnvironment).on(_.environmentId === _.id).join(TblAccounts).on(_._2.accountId === _.id)
        if a.id === accountId && a.isActive === true && a.isDeleted === false && key.status =!= ApiKeyStatus.DEPRECATED && env.environmentType === envTypeId
      } yield (key, a.name, env)).result,
      Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "getApiKeysForAccountAndSubAccounts"
    )
  }

  def getAccountInfoForEnvironment(environmentId: Long) = {
    dbProxyWithMetrics.run(
      (for {
        (env, a) <- TblEnvironment.join(TblAccounts).on(_.accountId === _.id)
        if env.id === environmentId && a.isActive === true && a.isDeleted === false
      } yield (a)).result.headOption,
      Set(DBTables.TblAccount, DBTables.TblEnvironment), DBActions.Select, "getAccountInfoForEnvironment"
    )
  }

  def anyHasRole(accountIds: Set[Long], businessUserRole: Int, accountAttributeValueOpt: Option[AccountAttributeValue]): Future[Boolean] = {
    val hasPermissionFuture = dbProxyWithMetrics.run(
      (for {
        tblAccPerm <- TblAccountPermission
        if tblAccPerm.accountId.inSet(accountIds) &&
          tblAccPerm.permission === businessUserRole &&
         tblAccPerm.enabled === true
      } yield tblAccPerm).exists.result,
      DBTables.TblAccountPermission,
      DBActions.Select,
      "hasPermission"
    )

    def hasAttributeFuture = accountAttributeValueOpt match {
      case Some(accountAttributeValue) =>
        dbProxyWithMetrics.run(
          (for {
            tblAccAttr <- TblAccountAttribute
            if tblAccAttr.accountId.inSet(accountIds) &&
              tblAccAttr.value === accountAttributeValue.id.toString
          } yield tblAccAttr).exists.result,
          DBTables.TblAccountAttribute, DBActions.Select, "hasAccountAttribute"
        )
      case None => Future.successful(false)
    }

    hasPermissionFuture.flatMap {
      case true => Future.successful(true)
      case false => hasAttributeFuture
    }
  }

  private def getAccountWithEnvironmentDetails(accountOpt: Option[DtoAccount]): Future[Option[(DtoAccount, Set[DtoEnvironment], Set[DtoAccountPermission], Set[DtoApiKey], Map[Long, String])]] = {
    accountOpt match {
      case Some(account) =>
        val resFuture = for {
          environments <- {
            dbProxyWithMetrics.run(TblEnvironment.filter(_.accountId === account.accountId).result, DBTables.TblEnvironment,
              DBActions.Select, "getAccountWithEnvironmentDetails_getEnvironment")
          }
          permissions <- {
            dbProxyWithMetrics.run(TblAccountPermission.filter(ap => ap.accountId === account.accountId && ap.enabled === true).result, DBTables.TblAccountPermission,
              DBActions.Select, "getAccountWithEnvironmentDetails_getAccountPermission")
          }
          apiKeys <- {
            dbProxyWithMetrics.run(TblApiKey.filter { key =>
              key.environmentId.inSet(environments.map(_.id)) &&
                key.status.inSet(validApiKeyStatuses)
            }.result, DBTables.TblApiKey,
              DBActions.Select, "getAccountWithEnvironmentDetails_getApiKeys")
          }
          subscriptionTypes <- {
            dbProxyWithMetrics.run((TblAccountPermission.filter(ap => ap.accountId === account.accountId && ap.enabled) join
              TblSubscriptionType on (_.permission === _.businessUserRoleId) map (subscriptions => (subscriptions._2.id, subscriptions._2.name))).result,
              Set(DBTables.TblSubscriptionType, DBTables.TblSubscriptionType), DBActions.Select, "getAccountWithEnvironmentDetails_getSubscriptionTypes")
          }
        } yield (environments, permissions, apiKeys, subscriptionTypes)
        resFuture.map { case (environments, permissions, apiKeys, subscriptionTypes) =>
          Some((
            account,
            environments.toSet,
            permissions.toSet,
            apiKeys.toSet,
            subscriptionTypes.toMap
          ))
        }
      case None => Future.successful(None)
    }
  }

  def getAccountWithEnvironmentDetailsByPublicId(publicId: String): Future[Option[(DtoAccount, Set[DtoEnvironment], Set[DtoAccountPermission], Set[DtoApiKey], Map[Long, String])]] = {
    val dbFuture = dbProxyWithMetrics.run(TblAccounts.filter(_.publicId === publicId).result.headOption, DBTables.TblAccount,
      DBActions.Select, "getAccountWithEnvironmentDetailsByPublicId")
    dbFuture.flatMap(getAccountWithEnvironmentDetails)
  }

  def getAccountWithEnvironmentDetailsById(id: Long): Future[Option[(DtoAccount, Set[DtoEnvironment], Set[DtoAccountPermission], Set[DtoApiKey], Map[Long, String])]] = {
    val dbFuture = dbProxyWithMetrics.runWithWriter(TblAccounts.filter(_.id === id).result.headOption, DBTables.TblAccount,
      DBActions.Select, "getAccountWithEnvironmentDetailsById")
    dbFuture.flatMap(getAccountWithEnvironmentDetails)
  }

  def getAccountIdNamesWithRoles(
                                  roles: Set[Int],
                                  onlyParents: Boolean
                                ): Future[Set[AccountIdName]] = {
    val accountIdsWithGivenRoles = TblAccountPermission.filter(ap => ap.permission.inSet(roles) && ap.enabled === true).map(_.accountId)
    val accountsWithGivenRoles0 = for {
      acc <- TblAccounts
      if acc.isActive === true &&
        acc.isDeleted === false &&
        acc.id.in(accountIdsWithGivenRoles)
    } yield acc

    val accountsWithGivenRoles = if (onlyParents) accountsWithGivenRoles0.filter(_.parentId.isEmpty === true) else accountsWithGivenRoles0
    val dbFuture = dbProxyWithMetrics.run(accountsWithGivenRoles.map(acc => (acc.id, acc.name)).result,
      Set(DBTables.TblAccount, DBTables.TblAccountPermission), DBActions.Select, "getAccountIdNamesWithRoles"
    )
    dbFuture.map(_.toSet[(Long, String)].map {
      case (accountId, accountName) => AccountIdName(
        id = accountId,
        name = accountName
      )
    })
  }

  def fetchAccountCacheIndividuals(envId: Long) = {
    val query = TblAccountCacheIndividual.filter(_.environmentId === envId)
    dbProxyWithMetrics.run(query.result, DBTables.TblEnvironmentIndividualCache, DBActions.Select, "fetchAccountCacheIndividuals")
  }

  def fetchAccountCache(envId: Long): Future[Option[DtoEnvironmentCache]] = {
    val query = TblEnvironmentCache.filter(_.environmentId === envId)
    val dbFuture = dbProxyWithMetrics.run(query.result, DBTables.TblEnvironmentCache, DBActions.Select, "fetchAccountCache")
    dbFuture.map(_.headOption)
  }

  def fetchAccountkeys(envId: Long) = {
    val query = TblEnvironmentSocialKeys.filter(_.environmentId === envId)
    dbProxyWithMetrics.run(query.result, DBTables.TblEnvironmentSocialKeys, DBActions.Select, "fetchAccountkeys")
  }

  def fetchEnvironmentByType(parentId: Long, environmentType: Long) = {
    val query = TblEnvironment.filter { env =>
      env.environmentType === environmentType && env.accountId === parentId
    }.map(_.domain)

    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblEnvironment, DBActions.Select, "fetchEnvironmentByType")
  }

  def fetchAccountPermission(accountId: Long) = {
    val query = TblAccountPermission.filter(ap => ap.accountId === accountId && ap.enabled === true)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccountPermission, DBActions.Select, "fetchAccountPermission")
  }

  def fetchAccountByPublicApiKey(publicApiKey: String): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey)]] = {
    val query = for {
      account <- TblAccounts.filter(a => a.publicApiKey === publicApiKey && a.isDeleted === false)
      environment <- TblEnvironment.filter(_.accountId === account.id)
      apiKey <- TblApiKey.filter(a => a.environmentId === environment.id && a.status =!= ApiKeyStatus.DEPRECATED)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKey)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccountByPublicApiKey")
  }

  def fetchAccount(apiKey: String): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey)]] = {
    val query = for {
      apiKey <- TblApiKey.filter(a => a.apiKey === apiKey && a.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === apiKey.environmentId)
      account <- TblAccounts.filter(a => a.id === environment.accountId && a.isDeleted === false)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKey)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccount")
  }
  def fetchAccountV3(apiKey: String): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey)]] = {
    val query = for {
      apiKey <- TblApiKey.filter(a => a.apiKey === apiKey && a.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === apiKey.environmentId)
      account <- TblAccounts.filter(a => a.id === environment.accountId)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKey)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccount")
  }

  def fetchAccountByPublicKeyV3(publicApiKey: String): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey)]] = {
    val query = for {
      publiApiKey <- TblPublicApiKey.filter(a => a.apiKey === publicApiKey && a.status =!= ApiKeyStatus.DEPRECATED)
      apiKey <- TblApiKey.filter(a => a.environmentId === publiApiKey.environmentId && a.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === apiKey.environmentId)
      account <- TblAccounts.filter(a => a.id === environment.accountId)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKey)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblPublicApiKey, DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccountByPublicKeyV3")
  }

  def fetchAccountByAccountIdV3(accountId: Long, environmentTypeId: Long): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey)]] = {
    val query = for {
      environment <- TblEnvironment.filter(a => a.accountId === accountId && a.environmentType === environmentTypeId)
      apiKey <- TblApiKey.filter(a => a.environmentId === environment.id && a.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === apiKey.environmentId)
      account <- TblAccounts.filter(a => a.id === environment.accountId)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKey)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblPublicApiKey, DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccountByAccountIdV3")
  }

  def fetchAccountByPublicAccountId(publicAccountId: String): Future[Option[DtoAccount]] = {
    val query = for {
      account <- TblAccounts.filter(a => a.publicId === publicAccountId && a.isDeleted === false)
    } yield account

    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblAccount, DBActions.Select, "fetchAccountByPublicAccountId")
  }

  def fetchAccountByPublicApiKeyV2(apiKey: String): Future[Option[(DtoAccount, DtoEnvironment, DtoIndustry, DtoApiKey, DtoPublicApiKey)]] = {
    val query = for {
      publicApiKeys <- TblPublicApiKey.filter(a => a.apiKey === apiKey && a.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === publicApiKeys.environmentId)
      account <- TblAccounts.filter(a => a.id === environment.accountId && a.isDeleted === false)
      apiKeys <- TblApiKey.filter(a => a.environmentId === environment.id && a.status =!= ApiKeyStatus.DEPRECATED)
      industry <- TblIndustries.filter(_.sector === account.industrySector)
    } yield (account, environment, industry, apiKeys, publicApiKeys)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblPublicApiKey, DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey, DBTables.TblIndustry),
      DBActions.Select, "fetchAccountByPublicApiKeyV2")
  }

  def fetchApiKeys(environmentId: Long): Future[Seq[DtoApiKey]] = {
    val query = for {
      apikey <- TblApiKey.filter(f => f.environmentId === environmentId && f.status =!= ApiKeyStatus.DEPRECATED)
      environment <- TblEnvironment.filter(_.id === apikey.environmentId)
      _ <- TblAccounts.filter(f => f.id === environment.accountId && f.isActive)
    } yield apikey
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "fetchApiKeys")
  }

  def isAccountDeleted(apiKey: String) = {
    val query = for {
      (a, b) <- TblApiKey.filter(_.apiKey === apiKey) join TblEnvironment on (_.environmentId === _.id) join TblAccounts.filter(_.isDeleted === true) on (_._2.accountId === _.id)
    } yield (a, b)

    dbProxyWithMetrics.run(query.result.headOption, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "isAccountDeleted")
  }

  def getEncryptedParentAccounts: Future[Seq[PublicAccount]] = {
    implicit val result = GetResult(res => PublicAccount(
      id = res.nextString(),
      name = res.nextString()
    ))

    val query =
      sql"""
           SELECT DISTINCT a.public_id, a.name
           FROM tbl_account a
           JOIN tbl_encryption_keys ek ON a.id = ek.account_id
           WHERE a.is_deleted = FALSE
           AND ek.encryption_key IS NOT NULL
           AND a.parent_id IS NULL
           AND a.is_active = TRUE
           ORDER BY a.name
         """.as[PublicAccount]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccount, DBTables.TblEncryptionKeys), DBActions.Select, "getEncryptedParentAccounts")
  }

  def updateAccountName(publicId: String, accountName: String): Future[Int] = {
    val query = TblAccounts.filter(_.publicId === publicId).map(_.name).update(accountName)
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Update, "updateAccountName")
  }

  def updateIsDeleted(publicId: String, isDeleted: Boolean): Future[Int] = {
    val query = TblAccounts.filter(_.publicId === publicId).map(_.isDeleted).update(isDeleted)
    dbProxyWithMetrics.run(query, DBTables.TblAccount, DBActions.Update, "updateIsDeleted")
  }

  def updateIsActive(publicId: String, isActive: Boolean): Future[Int] = {
    val status = if (isActive) SubscriptionChannelRegistryStatus.SUSPENDED.id else SubscriptionChannelRegistryStatus.DISABLED.id
    val query = for {
      accountId <- TblAccounts.filter(_.publicId === publicId).map(_.id).result.head
      environmentIds <- TblEnvironment.filter(_.accountId === accountId).map(_.id).result
      updateIsActive <- TblAccounts.filter(_.id === accountId).map(_.isActive).update(isActive)
      _ <- TblSponsorBankProgram.filter(_.sponsorBankId===accountId).delete
      _ <- TblSubscriptionChannelRegistry.filter(channel => (channel.environmentId inSet environmentIds) && !(channel.status === SubscriptionChannelRegistryStatus.DELETED.id))
        .map(_.status).update(status)

    } yield updateIsActive
    dbProxyWithMetrics.run(query.transactionally, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblSubscriptionChannelRegistry),
      DBActions.Update, "updateIsActive")
  }

  def updateIsActive(accountIds: List[Long], clock: Clock): Future[Int] = {
    val timestamp = clock.now
    val query = for {
      accountInfo <- TblAccounts.filter(f => (f.id inSetBind accountIds) && !f.isActive).map(r => (r.id, r.firstActivatedAt)).result
      environmentIds <- TblEnvironment.filter(_.accountId inSetBind accountInfo.map(_._1)).map(_.id).result
      updateIsActive <- TblAccounts.filter(f => (f.id inSetBind accountInfo.map(_._1)) && f.firstActivatedAt.isDefined).map(_.isActive).update(true)
      updateIsActiveWithFirstActivatedAt <- TblAccounts.filter(f => (f.id inSetBind accountInfo.map(_._1)) && !f.firstActivatedAt.isDefined).map(m => (m.isActive, m.firstActivatedAt)).update(true, Some(timestamp))
      _ <- TblSponsorBankProgram.filter(_.sponsorBankId inSetBind accountIds).delete
      _ <- TblSubscriptionChannelRegistry.filter(channel => (channel.environmentId inSet environmentIds) && !(channel.status === SubscriptionChannelRegistryStatus.DELETED.id))
        .map(_.status).update(SubscriptionChannelRegistryStatus.SUSPENDED.id )

    } yield (updateIsActive + updateIsActiveWithFirstActivatedAt)
    dbProxyWithMetrics.run(query.transactionally, Set(DBTables.TblAccount, DBTables.TblEnvironment, DBTables.TblSubscriptionChannelRegistry),
      DBActions.Update, "updateIsActive_Activate")
  }

  private def buildAccountSearchResponse(account: DtoAccount,
                                         primaryUsers: Option[DtoBusinessUser],
                                         piiMaskEnabled: Boolean,
                                         parentId: Option[String]) = {
    AccountSearchResponse(accountId = account.accountId,
      publicId = account.publicId,
      name = account.name,
      active = account.isActive,
      internal = account.isInternal,
      deleted = account.isDeleted,
      parentId = parentId,
      primaryUser = primaryUsers.headOption.map(_.email),
      encryptionEnabled = account.firstActivatedAt match {
        case Some(v) => true
        case _ => false
      },
      piiMaskEligible = piiMaskEnabled)
  }


  def accountBasicFilter(asr: AccountSearchRequest): Future[Seq[(DtoAccount, Option[DtoBusinessUser], Option[DtoAccount])]] = {
    val query = for {
      ((a, u), a0) <- TblAccounts
        .maybeFilter(asr.name)((r, name) => r.name === name)
        .maybeFilter(asr.id)(_.id === _)
        .maybeFilter(asr.publicId)(_.publicId === _)
        .maybeFilter(asr.active)(_.isActive === _)
        .maybeFilter(asr.deleted)(_.isDeleted === _)
        .maybeFilter(asr.internal)(_.isInternal === _)
        .maybeFilter(asr.isParent)((r, isParent) => if (isParent) {
          !r.parentId.isDefined
        } else {
          r.parentId.isDefined
        })
        .joinLeft(TblBusinessUser
          .filter(_.isPrimaryUser))
        .on(_.id === _.accountId)
        .joinLeft(TblAccounts)
        .on(_._1.parentId === _.id)
    } yield (a, u, a0)
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblAccount, DBTables.TblBusinessUser), DBActions.Select, "accountBasicFilter")
  }

  private def apiKeyFilter(asr: AccountSearchRequest): Future[Seq[(DtoEnvironment, DtoApiKey)]] = {
    val query = for {
      e <- TblEnvironment
      ak <- TblApiKey.maybeFilter(asr.apiKey)(_.apiKey === _).filter(k => (k.environmentId === e.id && (k.status === ApiKeyStatus.ACTIVE || k.status === ApiKeyStatus.NEW)))
    } yield (e, ak)
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "apiKeyFilter")
  }

  private def publicApiKeyFilter(asr: AccountSearchRequest): Future[Seq[(DtoEnvironment, DtoPublicApiKey)]] = {
    val query = for {
      e <- TblEnvironment
      ak <- TblPublicApiKey.maybeFilter(asr.publicApiKey)(_.apiKey === _).filter(k => (k.environmentId === e.id && (k.status === ApiKeyStatus.ACTIVE || k.status === ApiKeyStatus.NEW)))
    } yield (e, ak)
    dbProxyWithMetrics.run(query.result, Set(DBTables.TblEnvironment, DBTables.TblPublicApiKey), DBActions.Select, "publicApiKeyFilter")
  }

  def searchAccounts(asr: AccountSearchRequest): Future[Seq[Option[AccountSearchResponse]]] = {
    val apiKeys = apiKeyFilter(asr)
    val publicApiKeys = publicApiKeyFilter(asr)
    val bu = dbProxyWithMetrics.run(TblBusinessUser.maybeFilter(asr.email)(_.email === _).result, DBTables.TblBusinessUser, DBActions.Select, "searchAccounts")
    val pr = dbProxyWithMetrics.run(TblAccountPermission.maybeFilter(asr.permissions)((tap, req) => tap.permission.inSetBind(req) && tap.enabled === true).result, DBTables.TblAccountPermission, DBActions.Select, "searchAccounts")
    val dbFuture = accountBasicFilter(asr) flatMap {
      accountBasicFilterResponse =>
        Future.sequence(accountBasicFilterResponse.map { v =>
          val accountId = v._1.accountId
          val parentId = v._3.map(_.publicId)
          for {
            pm <- pr.map(_.exists(p => p.accountId == accountId && p.permission == BusinessUserRoles.MASK_PII.id))
            cp <- asr.permissions match {
              case Some(x) =>
                for {
                  p <- pr.map(_.filter(p => p.accountId == accountId)).map(_.map(_.permission).toSet)
                } yield (x.subsetOf(p))
              case _ => Future.successful(true)
            }
            cu <- asr.email match {
              case Some(x) =>
                bu.map(_.exists(_.accountId == accountId))
              case _ => Future.successful(true)
            }
            ak <- asr.apiKey match {
              case Some(x) =>
                apiKeys.map(_.exists(_._1.accountId == accountId))
              case _ => Future.successful(true)
            }
            pak <- asr.publicApiKey match {
              case Some(x) =>
                publicApiKeys.map(_.exists(_._1.accountId == accountId))
              case _ => Future.successful(true)
            }
          } yield {
            if (cp && cu && ak && pak)
              Some(buildAccountSearchResponse(v._1, v._2, pm, parentId))
            else None
          }
        })
    }
    withMetrics(
      dbFuture,
      Set(DBTables.TblAccount, DBTables.TblBusinessUser, DBTables.TblAccountPermission, DBTables.TblEnvironment, DBTables.TblPublicApiKey, DBTables.TblApiKey),
      DBActions.Select,
      "searchAccounts"
    )
  }

  def updateEmail(userId: Long, email: String) = {
    val value = TblBusinessUser.filter(_.id === userId).map(_.email).update(email)
    dbProxyWithMetrics.run(value, DBTables.TblBusinessUser, DBActions.Update, "updateEmail")
  }

  def updateEmail(email: String, newEmail: String) = {
    val value = TblBusinessUser.filter(_.email === email).map(_.email).update(newEmail)
    dbProxyWithMetrics.run(value, DBTables.TblBusinessUser, DBActions.Update, "updateEmail")
  }

  def fetchAccountByPublicKey(publicApiKey: String): Future[Map[DtoAccount, Seq[DtoAccountPermission]]] = {
    val accountIds = TblEnvironment.filter(_.id.in(
      TblPublicApiKey.filter(p => p.apiKey === publicApiKey && p.status.inSet(validApiKeyStatuses)).map(_.environmentId)
    )).map(_.accountId)

    val query = for {
      (account, permissionOpt) <- TblAccounts.joinLeft(TblAccountPermission).on((ta, tap) => ta.id === tap.accountId && tap.enabled === true).filter(_._1.id.in(accountIds))
    } yield (account, permissionOpt)

    val eventualTuples: Future[Seq[(DtoAccount, Option[DtoAccountPermission])]] = dbProxyWithMetrics.run(query.result,
      Set(DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblEnvironment, DBTables.TblApiKey), DBActions.Select, "fetchAccountByPublicKey")
    eventualTuples.map(_.groupBy(_._1).mapValues(_.map(_._2).flatten))
  }

  def fetchAccountByPrivateKey(privateApiKey: String): Future[Map[DtoAccount, Seq[DtoAccountPermission]]] = {
    val accountIds = TblEnvironment.filter(_.id.in(
      TblApiKey.filter(p => p.apiKey === privateApiKey && p.status.inSet(validApiKeyStatuses)).map(_.environmentId)
    )).map(_.accountId)

    val query = for {
      (account, permissionOpt) <- TblAccounts.joinLeft(TblAccountPermission).on((ta, tap) => ta.id === tap.accountId && tap.enabled === true).filter(_._1.id.in(accountIds))
    } yield (account, permissionOpt)

    val eventualTuples: Future[Seq[(DtoAccount, Option[DtoAccountPermission])]] = dbProxyWithMetrics.run(query.result,
      Set(DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblEnvironment, DBTables.TblPublicApiKey), DBActions.Select, "fetchAccountByPublicKey")
    withMetrics(
      eventualTuples,
      Set(DBTables.TblAccount, DBTables.TblAccountPermission, DBTables.TblEnvironment, DBTables.TblPublicApiKey),
      DBActions.Select,
      "fetchAccountByPublicKey"
    )
    eventualTuples.map(_.groupBy(_._1).mapValues(_.flatMap(_._2)))
  }

  def doesAccountExists(accountId: Long): Future[Boolean] = {
    val query = TblAccounts.filter(_.id === accountId).exists.result
    dbProxyWithMetrics.run(query, DBTables.TblAccount,
      DBActions.Select, "doesAccountExists")
  }

  def getAllAccountNamesWithPublicId: Future[Seq[(Long, String, String)]] = {
    val query = for {
      a <- TblAccounts
      if a.isActive && !a.isDeleted
    } yield (a.id, a.publicId, a.name)

    dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getAllAccountNamesWithPublicId")
  }

  def getSubAccountIds(parentId: Long): Future[Set[Long]] = {
    val query = TblAccounts.filter(a => a.parentId === parentId && a.isDeleted === false).map(_.id)
    val dbFuture = dbProxyWithMetrics.run(query.result, DBTables.TblAccount, DBActions.Select, "getSubAccountIds")
    dbFuture.map(_.toSet)
  }

  def addPermission(accountId: Long, permission: Int): Future[Boolean] = {
    val query = TblAccountPermission += DtoAccountPermission(accountId, permission)
    val dbFuture = dbProxyWithMetrics.run(query, DBTables.TblAccountPermission, DBActions.Insert, "addPermission")
    dbFuture.map(_.equals(1))
  }

  def getParentAccountId(accountId: Long): Future[Option[Option[Long]]] = {
    val query = TblAccounts.filter(_.id === accountId).map(_.parentId).result
    dbProxyWithMetrics.run(query.headOption, DBTables.TblAccount, DBActions.Select, "getParentAccountId")
  }

  def isInternalAccount(accountId: Long): Future[Option[Boolean]] = {
    val query = TblAccounts.filter(_.id === accountId).map(_.isInternal)
    dbProxyWithMetrics.run(query.result.headOption, DBTables.TblAccount, DBActions.Select, "isInternalAccount")
  }

  @deprecated
  def getAccountAnalyticsinfo(accountId: Long): Future[Option[AccountAnalyticsInfoResponse]] = {
    val query = TblAccountAnalytics.filter(_.accountId === accountId).map(r => (r.isHistoricalDataImported, r.lastImportedAt)).result.headOption
    dbProxyWithMetrics.run(query, DBTables.TblAccountAnalytics, DBActions.Select, "getAccountAnalyticsinfo").map {
      case Some(result) => Some(AccountAnalyticsInfoResponse(result._1, result._2))
      case None => None
    }
  }

  def getAnalyticsGlobalinfo(): Future[Option[AnalyticsGlobalInfoResponse]] = {
    val query = TblAnalyticsGlobal.sortBy(_.id.desc).take(1).result.headOption
    dbProxyWithMetrics.run(query, DBTables.TblAnalyticsGlobal, DBActions.Select, "getAnalyticsGlobalinfo").map {
      case Some(result) =>
          val isHistoricDataImported = if(analyticsConfig.isDefined) analyticsConfig.get.isHistoricDataImported else false
          Some(AnalyticsGlobalInfoResponse(isHistoricDataImported = isHistoricDataImported, lastImportedDate = result.lastImportedAt))
      case None => None
    }
  }

  @deprecated
  def addAccountAnalyticsInfo(dtoAccountAnalytics: DtoAccountAnalytics): Future[Boolean] = {
    val query = TblAccountAnalytics += dtoAccountAnalytics
    dbProxyWithMetrics.run(query, DBTables.TblAccountAnalytics, DBActions.Insert, "addAccountAnalyticsInfo").map(_ > 0)
  }

  @deprecated
  def updateAccountAnalyticsLastImportedDate(accountId: Long, lastImportedDate: DateTime): Future[Boolean] = {
    val query = TblAccountAnalytics.filter(_.accountId === accountId).map(_.lastImportedAt).update(lastImportedDate)
    dbProxyWithMetrics.run(query, DBTables.TblAccountAnalytics, DBActions.Update, "updateAccountAnalyticsLastImportedDate").map(_ > 0)
  }

  def addAnalyticsGlobalInfo(dtoAnalyticsGlobal: DtoAnalyticsGlobal): Future[Boolean] = {
    val query = TblAnalyticsGlobal += dtoAnalyticsGlobal
    dbProxyWithMetrics.run(query, DBTables.TblAnalyticsGlobal, DBActions.Insert, "addAnalyticsGlobalInfo").map(_ > 0)
  }

  def getAccountsWithPermission(permissionId: Int): Future[Seq[Long]] = {
    val query = TblAccountPermission.filter(ap => ap.permission === permissionId && ap.enabled === true).map(_.accountId)
    dbProxyWithMetrics.run(query.result, DBTables.TblAccountPermission, DBActions.Select, "getAccountsWithPermission")
  }

  def getAccountIdNamesWithAndWithoutPermission(withPermission:Int,withOutPermission:Int):Future[Seq[AccountIdName]] = {
    implicit val convertResult = GetResult(r => AccountIdName(
      id = r.nextLong(),
      name = r.nextString()
    ))

   val query =
     sql"""
          SELECT tap.account_id,ta.name  FROM tbl_account_permission tap join tbl_account ta on ta.id = tap.account_id  WHERE tap.permission = ${withPermission} AND tap.enabled = TRUE AND tap.account_id NOT IN (SELECT account_id FROM tbl_account_permission tap2 WHERE tap2.permission = ${withOutPermission} AND tap2.enabled = TRUE)
        """.as[AccountIdName]

    dbProxyWithMetrics.run(query, Set(DBTables.TblAccountPermission,DBTables.TblAccount), DBActions.Select, "getAccountsWithAndWithOutPermission")
}

  def getActiveApiKeyForAccountByEnvironmentType(accountId: Long, envTypeId: Long): Future[Option[ActiveAccountApiKey]] = {
    val query = TblEnvironment
      .filter(e => e.environmentType === envTypeId && e.accountId === accountId)
      .join(TblApiKey.filter(k => k.status === ApiKeyStatus.ACTIVE))
      .on(_.id === _.environmentId)
    dbProxyWithMetrics.run(
      query.result.headOption,
      Set(DBTables.TblEnvironment, DBTables.TblApiKey),
      DBActions.Select,
      "getActiveApiKeyForAccountByEnvironmentType"
    ).map{
      case Some(ativeApiKey) => Some(ActiveAccountApiKey(
        id = ativeApiKey._2.id,
        environmentId = ativeApiKey._2.environmentId,
        apiKey = ativeApiKey._2.apiKey
      ))
      case None => None
    }
  }

}

object DaoAccount {
  private val validApiKeyStatuses = Set(ApiKeyStatus.NEW, ApiKeyStatus.ACTIVE)
}
