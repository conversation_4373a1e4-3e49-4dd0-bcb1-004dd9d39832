package me.socure.utils
import me.socure.types.scala.{ByMember, Enum}

object DBTables extends Enum with ByM<PERSON>ber {
type DBTable = EnumVal

sealed case class EnumVal(id: Int, reference: String, name: String) extends Value

  val TblAccountAssociationHistory = new DBTable(1, "TblAccountAssociationHistory", "account_association_history")
  val TblAccountConsentReasons = new DBTable(2, "TblAccountConsentReasons","account_consent_reason")
  val TblAccountHierarchy = new DBTable(3, "TblAccountHierarchy", "account_hierarchy")
  val TblAccountMigrationAudit = new DBTable(4, "TblAccountMigrationAudit", "account_migration_audit")
  val TblAccountUiConfiguration = new DBTable(5, "TblAccountUiConfiguration", "account_ui_configuration")
  val TblApiAudit = new DBTable(6, "TblApiAudit", "api_audit")
  val TblBusinessUserInfo = new DBTable(7, "TblBusinessUserInfo", "business_user_info")
  val TblDefaultModules = new DBTable(8, "TblDefaultModules", "default_modules")
  val TblDocumentType = new DBTable(9, "TblDocumentType", "document_type")
  val TblEmployeeIdentificationNumber = new DBTable(10, "TblEmployeeIdentificationNumber",  "employee_identification_number")
  val TblEnvironmentDVConfiguration = new DBTable(11,  "TblEnvironmentDVConfiguration", "environment_dv_configuration")
  val TblPermissionTemplate = new DBTable(12, "TblPermissionTemplate", "permission_template")
  val TblRateLimits = new DBTable(13, "TblRateLimits", "rate_limits")
  val TblRolePermissionTemplateAssociation = new DBTable(14, "TblRolePermissionTemplateAssociation", "role_permission_template_association")
  val TblSubscriptionChannelRegistry = new DBTable(15, "TblSubscriptionChannelRegistry", "subscription_channel_registry")
  val TblSubscriptionStatus = new DBTable(16, "TblSubscriptionStatus", "subscription_status")
  val TblSubscriptionType = new DBTable(17, "TblSubscriptionType", "subscription_type")
  val TblSubscriptions = new DBTable(18, "TblSubscriptions", "subscriptions")
  val TblAccount = new DBTable(19, "TblAccount", "tbl_account")
  val TblAccountAttribute = new DBTable(20, "TblAccountAttribute", "tbl_account_attribute")
  val TblAccountPayloadKeys = new DBTable(21, "TblAccountPayloadKeys", "tbl_account_payload_keys")
  val TblAccountPermission = new DBTable(22, "TblAccountPermission", "tbl_account_permission")
  val TblAccountPGPKeys = new DBTable(23, "TblAccountPGPKeys", "tbl_account_pgp_keys")
  val TblActivationToken = new DBTable(24, "TblActivationToken", "tbl_activation_token")
  val TblApiKey = new DBTable(25, "TblApiKey", "tbl_api_key")
  val TblBusinessUser = new DBTable(26, "TblBusinessUser", "tbl_business_user")
  val TblBusinessUserBadLoginCount = new DBTable(27, "TblBusinessUserBadLoginCount", "tbl_business_user_bad_login_count")
  val TblBusinessUserBadLoginTry = new DBTable(28, "TblBusinessUserBadLoginTry", "tbl_business_user_bad_login_try")
  val TblBusinessUserEnvironmentRole = new DBTable(29, "TblBusinessUserEnvironmentRole", "tbl_business_user_environment_role")
  val TblCAWatchlistPreferences = new DBTable(30, "TblCAWatchlistPreferences", "tbl_ca_watchlist_preferences")
  val TblDashboardDomain = new DBTable(31, "TblDashboardDomain", "tbl_dashboard_domain")
  val TblEncryptionKeys = new DBTable(32, "TblEncryptionKeys", "tbl_encryption_keys")
  val TblEnvironment = new DBTable(33, "TblEnvironment", "tbl_environment")
  val TblEnvironmentCache = new DBTable(34, "TblEnvironmentCache", "tbl_environment_cache")
  val TblEnvironmentIndividualCache = new DBTable(35, "TblEnvironmentIndividualCache", "tbl_environment_individual_cache")
  val TblEnvironmentSocialKeys = new DBTable(36, "TblEnvironmentSocialKeys", "tbl_environment_social_key")
  val TblEnvironmentType = new DBTable(37, "TblEnvironmentType", "tbl_environment_type")
  val TblFilteredWatchlistSource = new DBTable(38, "TblFilteredWatchlistSource", "tbl_filtered_watchlist_source")
  val TblIdpMetadata = new DBTable(39, "TblIdpMetadata", "tbl_idp_metadata")
  val TblIndustry = new DBTable(40, "TblIndustry", "tbl_industry")
  val TblKycPreferences = new DBTable(41, "TblKycPreferences", "tbl_kyc_preferences")
  val TblMLAFields = new DBTable(42, "TblMLAFields", "tbl_mla_fields")
  val TblPassword = new DBTable(43, "TblPassword", "tbl_password")
  val TblPgpSignaturePublicKey = new DBTable(44, "TblPgpSignaturePublicKey", "tbl_pgp_signature_public_key")
  val TblPublicApiKey = new DBTable(45, "TblPublicApiKey", "tbl_public_api_key")
  val TblPublicWebhook = new DBTable(46, "TblPublicWebhook", "tbl_public_webhook")
  val TblResetToken = new DBTable(47, "TblResetToken", "tbl_reset_token")
  val TblSuppressedWatchlistSource = new DBTable(48, "TblSuppressedWatchlistSource", "tbl_suppressed_watchlist_source")
  val TblWatchlistPreferences = new DBTable(49, "TblWatchlistPreferences","tbl_watchlist_preferences")
  val TblWatchlistSource = new DBTable(50, "TblWatchlistSource", "tbl_watchlist_source")
  val TblUserAccountAssociation = new DBTable(51, "TblUserAccountAssociation", "user_account_association")
  val TblUserAccountRoleAssociation = new DBTable(52, "TblUserAccountRoleAssociation", "user_account_role_association")
  val TblUserRole = new DBTable(53, "TblUserRole", "user_role")
  val TblPermissionTemplateMapping = new DBTable(54, "TblPermissionTemplateMapping", "permission_template_mapping")
  val TblServiceEncryptionKeys = new DBTable(55, "TblServiceEncryptionKey", "tbl_service_encryption_keys")
  val TblAccountSftpUser = new DBTable(56, "TblAccountSftpUsers", "tbl_account_sftp_user")
  val TblSystemRolesMaxCount = new DBTable(57, "TblSystemRolesMaxCount", "tbl_system_roles_max_count")
  val TblIdmApiKey = new DBTable(58, "TblIdmApiKey", "idm_api_key")
  val TblProduct = new DBTable(59, "TblProduct", "tbl_product")
  val TblProductOverride = new DBTable(60, "TblProductOverride", "tbl_product_override")
  val TblAccountBundleAssociation = new DBTable(61, "TblAccountBundleAssociation", "tbl_account_bundle_association")
  val TblAccountBundleAssociationAudit = new DBTable(62, "TblAccountBundleAssociationAudit", "tbl_account_bundle_association_audit")
  val TblMagicToken = new DBTable(63, "TblMagicToken", "tbl_magic_token")
  val TblMagicTokenAudit = new DBTable(64, "TblMagicTokenAudit", "tbl_magic_token_audit")
  val TblAccountAudit = new DBTable(65, "TblAccountAudit", "tbl_account_audit")
  val TblAccountDataRetentionSchedule = new DBTable(66, "TblAccountDataRetentionSchedule", "tbl_account_data_retention_schedule")
  val TblWebhookSecretSource = new DBTable(67, "TblWebhookSecretSource", "webhook_secret_source")
  val TblWebhookSecretCredentials = new DBTable(68, "TblWebhookSecretCredentials", "webhook_secret_credentials")
  val TblSponsorBankProgram = new DBTable(69, "TblSponsorBankProgram", "tbl_sponsor_bank_program")
  val TblAccountAnalytics = new DBTable(70, "TblAccountAnalytics", "tbl_account_analytics")
  val TblTermsOfService = new DBTable(71, "TblTermsOfService", "terms_of_service")
  val TblProspectBlackListedDomains = new DBTable(72, "TblProspectBlackListedDomains", "tbl_prospect_blacklisted_domains")
  var TblProductSettingTraces = new DBTable(73, "TblProductSettingTraces", "tbl_product_setting_traces")
  var TblProductSettingChangeRequest = new DBTable(74, "TblProductSettingChangeRequest", "tbl_product_setting_change_request")
  var TblProductSettingAuditLog = new DBTable(75, "TblProductSettingAuditLog", "tbl_product_setting_audit_log")
  val TblProspectInclusionDomains = new DBTable(76, "TblProspectInclusionDomains", "tbl_prospect_inclusion_domains")
  val TblAnalyticsGlobal = new DBTable(77, "TblAnalyticsGlobal", "tbl_analytics_global")
  val TblSaiPreferences = new DBTable(78, "TblSaiPreferences", "tbl_sai_preferences")
  val TblAccountPlatformResourceMapping = new DBTable(79, "TblAccountPlatformResourceMapping", "tbl_account_platform_resource_mapping")
  val TblDocumentLinkToken = new DBTable(80, "TblDocumentLinkToken", "tbl_document_link_tokens")
  val TblDocumentTokenAudit = new DBTable(81, "TblDocumentTokenAudit", "tbl_document_token_audit")
  val TblAccountMetadata = new DBTable(82, "TblAccountMetadata", "tbl_account_metadata")
}
