package me.socure.storage.slick.dao

import me.socure.common.clock.FakeClock
import me.socure.storage.slick.tables.account.{DtoAccountMetadata, DtoAccountMetadataValueGenerator}
import org.joda.time.DateTime
import org.scalatest.{FreeSpec, Matchers}

class DaoAccountMetadataTest extends FreeSpec with Matchers {

  val now = new DateTime(2023, 1, 1, 0, 0)
  val clock = new FakeClock(now.getMillis)

  "DtoAccountMetadataValueGenerator" - {
    "should create metadata with correct values" in {
      val accountId = 1L
      val childId = "child123"
      val createdBy = "<EMAIL>"

      val metadata = DtoAccountMetadataValueGenerator.create(
        accountId = accountId,
        childId = childId,
        createdBy = createdBy
      )(clock)

      metadata.accountId shouldBe accountId
      metadata.childId shouldBe childId
      metadata.createdBy shouldBe createdBy
      metadata.updatedBy shouldBe createdBy
      metadata.createdAt shouldBe now
      metadata.updatedAt shouldBe now
    }

    "should create metadata with different child IDs" in {
      val accountId = 1L
      val childId1 = "child123"
      val childId2 = "child456"
      val createdBy = "<EMAIL>"

      val metadata1 = DtoAccountMetadataValueGenerator.create(
        accountId = accountId,
        childId = childId1,
        createdBy = createdBy
      )(clock)

      val metadata2 = DtoAccountMetadataValueGenerator.create(
        accountId = accountId,
        childId = childId2,
        createdBy = createdBy
      )(clock)

      metadata1.childId shouldBe childId1
      metadata2.childId shouldBe childId2
      metadata1.accountId shouldBe metadata2.accountId
    }

    "should handle maximum child ID length" in {
      val accountId = 1L
      val childId = "a" * 20 // Maximum length as per schema
      val createdBy = "<EMAIL>"

      val metadata = DtoAccountMetadataValueGenerator.create(
        accountId = accountId,
        childId = childId,
        createdBy = createdBy
      )(clock)

      metadata.childId shouldBe childId
      metadata.childId.length shouldBe 20
    }
  }

  "DtoAccountMetadata case class" - {
    "should create instances with correct values" in {
      val metadata = DtoAccountMetadata(
        id = 1L,
        accountId = 123L,
        childId = "child456",
        createdAt = now,
        updatedAt = now,
        createdBy = "<EMAIL>",
        updatedBy = "<EMAIL>"
      )

      metadata.id shouldBe 1L
      metadata.accountId shouldBe 123L
      metadata.childId shouldBe "child456"
      metadata.createdAt shouldBe now
      metadata.updatedAt shouldBe now
      metadata.createdBy shouldBe "<EMAIL>"
      metadata.updatedBy shouldBe "<EMAIL>"
    }

    "should support copy operations" in {
      val original = DtoAccountMetadata(
        id = 1L,
        accountId = 123L,
        childId = "child456",
        createdAt = now,
        updatedAt = now,
        createdBy = "<EMAIL>",
        updatedBy = "<EMAIL>"
      )

      val updated = original.copy(
        childId = "newChild789",
        updatedBy = "<EMAIL>"
      )

      updated.id shouldBe original.id
      updated.accountId shouldBe original.accountId
      updated.childId shouldBe "newChild789"
      updated.updatedBy shouldBe "<EMAIL>"
      updated.createdBy shouldBe original.createdBy
    }
  }
}
