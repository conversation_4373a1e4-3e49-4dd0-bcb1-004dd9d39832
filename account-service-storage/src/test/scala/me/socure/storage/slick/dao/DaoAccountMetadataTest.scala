package me.socure.storage.slick.dao

import me.socure.common.clock.Clock
import me.socure.utils.DBProxyWithMetrics
import me.socure.storage.slick.tables.account.{DtoAccountMetadata, DtoAccountMetadataValueGenerator}
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{any, anyString}
import org.mockito.Mockito.{verify, when}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.{BeforeAndAfter, FreeSpec, Matchers}
import org.scalatestplus.mockito.MockitoSugar
import slick.driver.MySQLDriver

import scala.concurrent.{ExecutionContext, Future}

class DaoAccountMetadataTest extends FreeSpec with Matchers with MockitoSugar with BeforeAndAfter with ScalaFutures {

  implicit val ec: ExecutionContext = ExecutionContext.global
  val clock: Clock = mock[Clock]

  val dbProxy: DBProxyWithMetrics = mock[DBProxyWithMetrics]
  val profile = MySQLDriver
  val now = new DateTime(2023, 1, 1, 0, 0)

  val daoAccountMetadata = new DaoAccountMetadata(dbProxy, profile, clock)

  before {
    when(clock.now()).thenReturn(now)
    when(dbProxy.run(any(), any(), any(), anyString())(any())).thenReturn(Future.successful(1L))
  }

  "DaoAccountMetadata" - {
    "create" - {
      "should create a new account metadata entry" in {
        val accountId = 1L
        val childId = "child123"
        val createdBy = "<EMAIL>"

        val expectedMetadata = DtoAccountMetadataValueGenerator.create(
          accountId = accountId,
          childId = childId,
          createdBy = createdBy
        )(clock)

        val result = daoAccountMetadata.create(accountId, childId, createdBy)

        whenReady(result) { metadata =>
          metadata.accountId shouldBe accountId
          metadata.childId shouldBe childId
          metadata.createdBy shouldBe createdBy
          metadata.updatedBy shouldBe createdBy
          metadata.createdAt shouldBe now
          metadata.updatedAt shouldBe now
          metadata.id shouldBe 1L
        }

        verify(dbProxy).run(any(), any(), any(), anyString())(any())
      }
    }

    "getByAccountId" - {
      "should get metadata for an account" in {
        val accountId = 1L
        val expectedMetadata = DtoAccountMetadata(
          id = 1L,
          accountId = accountId,
          childId = "child123",
          createdAt = now,
          updatedAt = now,
          createdBy = "<EMAIL>",
          updatedBy = "<EMAIL>"
        )

        when(dbProxy.run(any(), any(), any(), anyString())(any())).thenReturn(Future.successful(Some(expectedMetadata)))

        val result = daoAccountMetadata.getByAccountId(accountId)

        whenReady(result) { metadata =>
          metadata shouldBe Some(expectedMetadata)
        }

        verify(dbProxy).run(any(), any(), any(), anyString())(any())
      }
    }

    "update" - {
      "should update an existing account metadata entry" in {
        val accountId = 1L
        val childId = "newChild123"
        val updatedBy = "<EMAIL>"
        val expectedMetadata = DtoAccountMetadata(
          id = 1L,
          accountId = accountId,
          childId = childId,
          createdAt = now,
          updatedAt = now,
          createdBy = "<EMAIL>",
          updatedBy = updatedBy
        )

        when(dbProxy.run(any(), any(), any(), anyString())(any()))
          .thenReturn(Future.successful(1), Future.successful(Some(expectedMetadata)))

        val result = daoAccountMetadata.update(accountId, childId, updatedBy)

        whenReady(result) { metadata =>
          metadata shouldBe Some(expectedMetadata)
        }

        verify(dbProxy).run(any(), any(), any(), anyString())(any())
      }
    }

    "delete" - {
      "should delete metadata for an account" in {
        val accountId = 1L

        when(dbProxy.run(any(), any(), any(), anyString())(any())).thenReturn(Future.successful(1))

        val result = daoAccountMetadata.delete(accountId)

        whenReady(result) { count =>
          count shouldBe 1
        }

        verify(dbProxy).run(any(), any(), any(), anyString())(any())
      }
    }
  }
}
