package me.socure.account.service.common.servlet.error.handler

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.account.service.common.servlet.BaseScalatraServlet
import me.socure.model.{ErrorResponse, Response, ResponseStatus}
import org.json4s._
import org.json4s.ext.EnumNameSerializer
import org.json4s.jackson._
import org.scalatest.mock.MockitoSugar
import org.scalatra.test.scalatest.ScalatraFunSuite

import scala.concurrent.ExecutionContext

/**
  * Created by jamesanto on 8/3/16.
  */
class JsonErrorHandlingTest extends ScalatraFunSuite with MockitoSugar {
  implicit val ec = ExecutionContext.global
  implicit val formats = DefaultFormats ++ Set(new EnumNameSerializer(ResponseStatus))

  val ex = new Exception("naughty!!!")

  class MyServlet extends BaseScalatraServlet {
    get("/test") {
      "hi"
    }

    get("/ex") {
      throw ex
    }
  }

  val servlet = new MyServlet

  addServlet(servlet, "/*")

  test("should handle 200") {
    val uri = "/test"
    get(uri) {
      status should equal(200)
      body shouldBe "hi"
    }
  }

  test("should handle 404") {
    val uri = "/some_uri_that_does_not_exist/really"
    get(uri) {
      status should equal(404)
      parseJson(body).extract[Response[ErrorResponse]] shouldBe Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(NoHandlerFound)
      )
    }
  }

  test("should handle 500") {
    get("/ex") {
      status should equal(500)
      parseJson(body).extract[Response[ErrorResponse]] shouldBe Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(InternalError)
      )
    }
  }
}
