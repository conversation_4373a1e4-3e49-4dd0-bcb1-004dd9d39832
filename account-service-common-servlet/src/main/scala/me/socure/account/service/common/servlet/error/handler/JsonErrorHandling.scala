package me.socure.account.service.common.servlet.error.handler

import me.socure.account.service.common.exceptions.ErrorResponseFactory
import me.socure.account.service.common.exceptions.ExceptionCodes._
import me.socure.model.{Response, ResponseStatus}
import org.json4s.ext.EnumNameSerializer
import org.json4s.{DefaultFormats, Formats, MappingException}
import org.scalatra.ScalatraServlet
import org.scalatra.json.JacksonJsonSupport
import org.slf4j.LoggerFactory

/**
  * Created by jamesanto on 8/2/16.
  */
trait JsonErrorHandling {
  this: ScalatraServlet with JacksonJsonSupport =>

  private val logger = LoggerFactory.getLogger(getClass)

  override protected implicit def jsonFormats: Formats = DefaultFormats ++ Seq(new EnumNameSerializer(ResponseStatus))

  before() {
    contentType = formats("json")

  }

  notFound {
    status = 404
    logger.info(s"No handler found for ${request.getRequestURI}")
    Response(
      status = ResponseStatus.Error,
      data = ErrorResponseFactory.get(NoHandlerFound)
    )
  }

  error {
    case e: MappingException =>
      status = 400
      logger.info(s"Invalid input for URI ${request.getRequestURI}", e)
      Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(InvalidInputFormat)
      )
    case e : NoSuchElementException =>
      status = 400
      logger.info(s"Missing parameters for URI ${request.getRequestURI}", e)
      Response(ResponseStatus.Error, ErrorResponseFactory.get(MissingRequiredParameters))
    case e: Throwable =>
      status = 500
      logger.info(s"Unhandled exception while handling ${request.getRequestURI}", e)
      Response(
        status = ResponseStatus.Error,
        data = ErrorResponseFactory.get(InternalError)
      )
  }
}
